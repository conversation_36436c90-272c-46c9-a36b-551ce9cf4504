/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: namespace_example.c
 * Description: Example
 * Author:
 * Create: 2022-2-25
 */

#include <stdio.h>
#include <pthread.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <sys/epoll.h>
#include "gmc.h"
#include "gmc_graph.h"
#include "gmc_types.h"
static int responseEpollFd;
static pthread_t responseEpollThreadId;
static pthread_t timeoutEpollThreadId;
static int timeoutEpollFd;
const char *labelName = "access_list";
const char *configJson = R"({"max_record_count":1000})";
const char *indexKey = "test1";
char *labelJson = NULL;
const uint32_t sleepTime = 1000;
const int receive_value = 8;
const uint32_t timeoutTimes = 1000;

#define DEMO_INFO(...)       \
    do {                     \
        printf("[INFO] ");   \
        printf(__VA_ARGS__); \
        putchar('\n');       \
    } while (0)

#define DEMO_ERROR(ret, ...)        \
    do {                            \
        printf("[ERROR %d] ", ret); \
        printf(__VA_ARGS__);        \
        putchar('\n');              \
    } while (0)

#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
char g_connServer[64] = "usocket:/run/verona/unix_emserver";
#else
char g_connServer[64] = "channel:";
#endif
inline static bool IsEulerEnv(void)
{
#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
    return true;
#else
    return false;
#endif
}

long ReadJanssonFile(const char *path, char **buf, bool convertArray)
{
    FILE *fp;
    fp = fopen(path, "rb");
    if (fp == NULL) {
        DEMO_INFO("[ReadJanssonFile] open file:%s fail.", path);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        DEMO_INFO("[ReadJanssonFile] fseek file:%s to end failed.", path);
        fclose(fp);
        return -1;
    }

    long size = ftell(fp);
    if (size < 0) {
        DEMO_INFO("[ReadJanssonFile] read file size:%ld failed.", size);
        fclose(fp);
        return -1;
    }
    char *pBuffer = (char *)malloc(size + 1);
    if (pBuffer == NULL) {
        DEMO_INFO("[ReadJanssonFile] malloc memory:%ld for file:%s failed.", size + 1, path);
        fclose(fp);
        return -1;
    }
    rewind(fp);
    long readSize = fread(pBuffer, 1, size, fp);
    if (readSize != size) {
        DEMO_INFO("[ReadJanssonFile] read file:%s failed, expectSize:%ld, actualSize:%ld.", path, size, readSize);
        free(pBuffer);
        fclose(fp);
        return -1;
    }
    pBuffer[size] = 0;
    rc = fclose(fp);
    if (rc != 0) {
        DEMO_INFO("file close error.");
        free(pBuffer);
        return -1;
    }
    *buf = pBuffer;
    return size;
}

void CommonAsyncDDLCb(void *userData, int32_t status, const char *errMsg)
{
    uint32_t *received = (uint32_t *)userData;
    (*received)++;
}

// 客户端初始化
int32_t ClientInit()
{
    int32_t ret = GmcInit();
    if (ret != 0) {
        DEMO_ERROR(ret, "client init failed.");
        GmcUnInit();
    }
    return ret;
}

// 客户端去初始化
void ClientUninit()
{
    (void)GmcUnInit();
}

/*
 * 建立同步连接
 */
static int32_t ConnectWrapper(
    GmcConnTypeE type, const char *serverLactor, const char *username, const char *connName, GmcConnT **conn)
{
    GmcConnOptionsT *connOptions = NULL;
    int32_t ret = GmcConnOptionsCreate(&connOptions);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLactor);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    if (type == GMC_CONN_TYPE_SUB && connName != NULL) {
        ret = GmcConnOptionsSetConnName(connOptions, connName);
        if (ret != GMERR_OK) {
            goto CLEAR;
        }
    }
    ret = GmcConnect(type, connOptions, conn);
CLEAR:
    GmcConnOptionsDestroy(connOptions);
    return ret;
}

int32_t CreateSyncConnectionAndStmt(GmcConnT **connSync, GmcStmtT **stmtSync)
{
    int32_t ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *serverLocator = g_connServer;
    char *userName = "XXuser";
    ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcAllocStmt(conn, &stmt);
    if (ret != GMERR_OK) {
        (void)GmcDisconnect(conn);
        return ret;
    }
    *connSync = conn;
    *stmtSync = stmt;
    return ret;
}

static int ResponseEpollReg(int fd, GmcEpollCtlTypeE type)
{
    switch (type) {
        case GMC_EPOLL_ADD: {
            struct epoll_event event;
            event.data.fd = fd;
            event.events = EPOLLIN;
            return epoll_ctl(responseEpollFd, EPOLL_CTL_ADD, fd, &event);
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(responseEpollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}

static int EpollRegWithUserData(int fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    struct epoll_event event;
    event.data.fd = fd;
    event.events = events;
    int *epollFd = (int *)(userData);
    switch (type) {
        case GMC_EPOLL_ADD: {
            return epoll_ctl(*epollFd, EPOLL_CTL_ADD, fd, &event);
        }
        case GMC_EPOLL_MOD: {
            return epoll_ctl(*epollFd, EPOLL_CTL_MOD, fd, &event);
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(*epollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}

/*
 * 建立异步连接
 */
int32_t CreateAsyncConnAndStmt(GmcConnT **connAsync, GmcStmtT **stmtAsync)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *serverLocator = g_connServer;
    static const char *userName = "XXuser";
    GmcConnOptionsT *connOptions = NULL;
    int32_t ret = GmcConnOptionsCreate(&connOptions);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Connect option create failed.");
        return ret;
    }
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Connect option set server locator failed.");
        GmcConnOptionsDestroy(connOptions);
        return ret;
    }
    if (IsEulerEnv()) {
        GmcConnOptionsSetEpollRegFuncWithUserData(connOptions, EpollRegWithUserData, &responseEpollFd);
    } else {
        GmcConnOptionsSetEpollRegFunc(connOptions, ResponseEpollReg);
    }

    ret = GmcConnect(GMC_CONN_TYPE_ASYNC, connOptions, &conn);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Connect failed.");
        GmcConnOptionsDestroy(connOptions);
        return ret;
    }

    GmcConnOptionsDestroy(connOptions);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcAllocStmt(conn, &stmt);
    if (ret != GMERR_OK) {
        GmcDisconnect(conn);
        return ret;
    }
    *connAsync = conn;
    *stmtAsync = stmt;
    return ret;
}

/*
 * 断连
 */
void DestroyConnAndStmt(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    (void)GmcDisconnect(conn);
}

/*
 * 场景描述：同步场景下命名空间的创建、设置以及删除
 */

static int32_t UseNamespace(GmcStmtT *stmt, const char *namespace)
{
    int32_t ret = GmcUseNamespace(stmt, namespace);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Use namespace failed.");
        goto CLEAR;
    }
    DEMO_INFO("Use namespace success.");

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Create vertex label  failed.");
        goto CLEAR;
    }
    DEMO_INFO("Create vertex label success.");

    ret = GmcDropVertexLabel(stmt, labelName);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Drop vertex label 1 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Drop vertex label success.");
CLEAR:
    return ret;
}

int32_t NamespaceSyncDemo(GmcConnT *conn, GmcStmtT *stmt)
{
    const char *namespace = "test_namespace_1";
    const char *namespace2 = "test_namespace_2";
    int32_t ret = GmcCreateNamespace(stmt, namespace, "user");
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Create namespace  failed.");
        goto CLEAR;
    }
    DEMO_INFO("Create namespace success.");
    ret = GmcCreateNamespace(stmt, namespace2, "user2");
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Create namespace 2 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Create namespace 2 success.");

    DEMO_INFO("Namespace demo 1");
    ret = UseNamespace(stmt, namespace);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Namespace demo 1 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Namespace demo 2");
    ret = UseNamespace(stmt, namespace2);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Namespace demo 2 failed.");
        goto CLEAR;
    }

    ret = GmcDropNamespace(stmt, namespace);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Drop namespace 1 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Drop namespace 1 success.");

    ret = GmcDropNamespace(stmt, namespace2);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Drop namespace 2 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Drop namespace 2 success.");

CLEAR:
    return ret;
}

typedef struct TagAsyncUserDataT {
    int threadId;        // 用户自定义的线程ID，用于区分线程
    int recvNum;         // 此次共接收的消息数
    int historyRecvNum;  // 历史接收的消息数
    int status;          // 实际返回值
    int affectRows;      // 实际返回值
    int totalNum;        // 实际返回值
    int succNum;         // 实际返回值
    int expint32_t;      // 预期返回值
    int expAffectRows;   // 预期返回值
    int expTotalNum;     // 预期返回值
    int expSuccNum;      // 预期返回值
} AsyncUserDataT;

void create_vertex_label_callback(void *userData, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        user_data->recvNum++;
    }
}
void create_namespace_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

/*
 * 场景描述：异步场景下命名空间的创建、设置以及删除
 */
int32_t NamespaceAsyncDemo(GmcConnT *conn, GmcStmtT *stmt)
{
    const char *namespace = "test_namespaceAsync_1";
    const char *namespace2 = "test_namespaceAsync_2";
    uint32_t received = 0;
    uint32_t calc = 0;
    AsyncUserDataT data = {0};
    int32_t ret = GmcCreateNamespaceAsync(stmt, namespace, "userName1", create_namespace_callback, &data);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Create namespace async 1 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Create namespace async 1 success.");

    ret = GmcCreateNamespaceAsync(stmt, namespace2, "userName2", CommonAsyncDDLCb, &received);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Create namespace async 2 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Create namespace async 2 success.");

    ret = GmcUseNamespaceAsync(stmt, namespace, CommonAsyncDDLCb, &received);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Use namespace async 1 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Use namespace async 1 success.");

    ret = GmcCreateVertexLabelAsync(stmt, labelJson, configJson, CommonAsyncDDLCb, &received);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Create vertex label async 1 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Create vertex label async 1 success.");

    ret = GmcDropVertexLabelAsync(stmt, labelName, CommonAsyncDDLCb, &received);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Drop vertex label async 1 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Drop vertex label async 1 success.");

    ret = GmcUseNamespaceAsync(stmt, namespace2, CommonAsyncDDLCb, &received);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Use namespace async 2 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Use namespace async 2 success.");

    ret = GmcCreateVertexLabelAsync(stmt, labelJson, configJson, CommonAsyncDDLCb, &received);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Create vertex label async 2 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Create vertex label async 2 success.");

    ret = GmcDropNamespaceAsync(stmt, namespace, CommonAsyncDDLCb, &received);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Drop namespace async 1 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Drop namespace async 1 success.");

    ret = GmcDropNamespaceAsync(stmt, namespace2, CommonAsyncDDLCb, &received);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Drop namespace async 2 failed.");
        goto CLEAR;
    }
    DEMO_INFO("Drop namespace async 2 success.");

    while (received != receive_value) {
        usleep(sleepTime);
        calc++;
        if (calc > timeoutTimes) {
            ret = GMERR_REQUEST_TIME_OUT;
            DEMO_ERROR(ret, "async receive timeout");
            return ret;
        }
    }
CLEAR:
    return ret;
}

int TimeoutEpollReg(int fd, GmcEpollCtlTypeE type)
{
    switch (type) {
        case GMC_EPOLL_ADD: {
            struct epoll_event event;
            event.data.fd = fd;
            event.events = EPOLLIN;
            return epoll_ctl(timeoutEpollFd, EPOLL_CTL_ADD, fd, &event);
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(timeoutEpollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}

static void *EpollThreadFunc(void *args)
{
    const int epollTimeoutMs = 1000;
    static struct epoll_event events[2048];
    int *epollFd = (int *)args;
    *epollFd = epoll_create(sizeof(events));

    if (*epollFd < 0) {
        return NULL;
    }

    while (*epollFd >= 0) {
        int32_t fdCount = epoll_wait(*epollFd, events, sizeof(events), epollTimeoutMs);

        while (fdCount > 0) {
            --fdCount;
            if (IsEulerEnv()) {
                GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
            } else {
                GmcHandleEvent(events[fdCount].data.fd);
            }
        }
    }
    return NULL;
}

void StartEpollThread()
{
    pthread_create(&responseEpollThreadId, NULL, EpollThreadFunc, &responseEpollFd);
    pthread_create(&timeoutEpollThreadId, NULL, EpollThreadFunc, &timeoutEpollFd);
    sleep(1);
    (void)GmcRegTimeoutEpollFunc(TimeoutEpollReg);
    DEMO_INFO("start response epoll and timeout epoll thread. ");
    DEMO_INFO("response epoll fd: %d, timeout epoll fd: %d", responseEpollFd, timeoutEpollFd);
}

int32_t showNamespaceSyncDemo()
{
    // 建连 同步
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t ret = CreateSyncConnectionAndStmt(&conn, &stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Connect failed");
        goto CLEAR;
    }
    DEMO_INFO("Connect succeed.");

    // 执行demo
    ret = NamespaceSyncDemo(conn, stmt);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    DEMO_INFO("NamespaceDemo succeed.");

CLEAR:
    // 断连 同步
    DestroyConnAndStmt(conn, stmt);
    return ret;
}

int32_t showNamespaceAsyncDemo()
{
    // 建连 异步
    StartEpollThread();
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    int32_t ret = CreateAsyncConnAndStmt(&connAsync, &stmtAsync);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Connect Async failed");
        goto CLEAR;
    }
    DEMO_INFO("Connect Async succeed.");

    // 执行demo
    ret = NamespaceAsyncDemo(connAsync, stmtAsync);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    DEMO_INFO("NamespaceAsyncDemo succeed.");

CLEAR:
    // 断连 异步
    DestroyConnAndStmt(connAsync, stmtAsync);
}

int main()
{
    // 客户端初始化
    DEMO_INFO("========== init environment ==========");
    int32_t ret = ClientInit();
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "client init failed.");
        return ret;
    }
    DEMO_INFO("client init success.");
    // 读取表文件
    ret = ReadJanssonFile("../schema_field/complex_example.gmjson", &labelJson, false);
    if (ret == -1) {
        DEMO_ERROR(ret, "ReadJanssonFile failed.");
        goto UNINIT;
    }
    // ==============================同步=================================
    DEMO_INFO("========== sync namespace demo ==========");
    ret = showNamespaceSyncDemo();
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "showNamespaceSyncDemo failed.");
        goto UNINIT;
    }
    DEMO_INFO("showNamespaceSyncDemo succeed.");

    // ==============================异步=================================
    DEMO_INFO("========== async namespace demo ==========");
    ret = showNamespaceAsyncDemo();
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "showNamespaceAsyncDemo failed.");
        goto UNINIT;
    }
    DEMO_INFO("========== all cases passed ==========");
UNINIT:
    DEMO_INFO("========== uninit environment ==========");
    // 客户端去初始化
    ClientUninit();
    free(labelJson);
    labelJson = NULL;
    return ret;
}
