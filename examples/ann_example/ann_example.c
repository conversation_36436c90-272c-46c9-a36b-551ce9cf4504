/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: ann_example.c
 * Description: Example
 * Author:
 * Create: 2024-10-22
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "gmc.h"

#define DEMO_INFO(...)       \
    do {                     \
        printf("[INFO] ");   \
        printf(__VA_ARGS__); \
        putchar('\n');       \
    } while (0)

#define DEMO_ERROR(ret, ...)        \
    do {                            \
        printf("[ERROR %d] ", ret); \
        printf(__VA_ARGS__);        \
        putchar('\n');              \
    } while (0)

#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
char g_connServer[64] = "usocket:/run/verona/unix_emserver";
#else
char g_connServer[64] = "channel:";
#endif
char *g_annLabelJson = NULL;
char *g_vlivfLabelJson = NULL;

const char *g_annLabelName = "annlabel";
const char *g_vlivfLabelName = "vlivflabel";

static char *g_annJsonFile = "../schema_field/ann_example.gmjson";
static char *g_vlivfJsonFile = "../schema_field/vlivf_example.gmjson";

static float *g_trainDataSet = NULL;
static float *g_queryDataSet = NULL;
static float *g_vlivfCentroids = NULL;

#define BATCH_BUFFER_SIZE 2048
#define BATCH_OPER_NUM 20

#define DIMS 128

#define TRAIN_VEC_NUMS 128

#define QUERY_VEC_NUMS 10
#define TOPK 3

#define CENTROIDS_NUMS 10

void RandomGenerateOriginalVectors(float *originVectors, uint32_t nums)
{
    srand(time(NULL));

    for (uint32_t i = 0; i < nums; i++) {
        originVectors[i] = (float)(rand() / (RAND_MAX / 2.0));
    }
}

long ReadJanssonFile(const char *path, char **buf)
{
    FILE *fp;
    fp = fopen(path, "rb");
    if (NULL == fp) {
        DEMO_INFO("[ReadJanssonFile] open file:%s fail.", path);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        DEMO_INFO("[ReadJanssonFile] fseek file:%s to end failed.", path);
        fclose(fp);
        return -1;
    }

    long size = ftell(fp);
    if (size < 0) {
        DEMO_INFO("[ReadJanssonFile] read file size:%ld failed.", size);
        fclose(fp);
        return -1;
    }
    char *pBuffer = (char *)malloc(size + 1);
    if (pBuffer == NULL) {
        DEMO_INFO("[ReadJanssonFile] malloc memory:%ld for file:%s failed.", size + 1, path);
        fclose(fp);
        return -1;
    }
    rewind(fp);
    long readSize = fread(pBuffer, 1, size, fp);
    if (readSize != size) {
        DEMO_INFO("[ReadJanssonFile] read file:%s failed, expectSize:%ld, actualSize:%ld.", path, size, readSize);
        free(pBuffer);
        fclose(fp);
        return -1;
    }
    pBuffer[size] = 0;
    rc = fclose(fp);
    if (rc != 0) {
        DEMO_INFO("file close error.");
        free(pBuffer);
        return -1;
    }
    *buf = pBuffer;
    return size;
}

// 客户端初始化
int32_t ClientInit()
{
    int32_t ret = GmcInit();
    if (ret != 0) {
        DEMO_ERROR(ret, "client init failed.");
        GmcUnInit();
    }
    return ret;
}

// 客户端去初始化
void ClientUninit()
{
    (void)GmcUnInit();
}

static void DestroyConnAndStmt(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    (void)GmcDisconnect(conn);
}

/*
 * 建立同步连接
 */
static int32_t ConnectWrapper(
    GmcConnTypeE type, const char *serverLactor, const char *username, const char *connName, GmcConnT **conn)
{
    GmcConnOptionsT *connOptions = NULL;
    int32_t ret = GmcConnOptionsCreate(&connOptions);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLactor);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    if (type == GMC_CONN_TYPE_SUB && connName != NULL) {
        ret = GmcConnOptionsSetConnName(connOptions, connName);
        if (ret != GMERR_OK) {
            goto CLEAR;
        }
    }
    ret = GmcConnect(type, connOptions, conn);
CLEAR:
    GmcConnOptionsDestroy(connOptions);
    return ret;
}

static int32_t CreateSyncConnectionAndStmt(GmcConnT **connSync, GmcStmtT **stmtSync)
{
    int32_t ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *serverLocator = g_connServer;
    char *userName = "XXuser";
    ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcAllocStmt(conn, &stmt);
    if (ret != GMERR_OK) {
        (void)GmcDisconnect(conn);
        return ret;
    }
    *connSync = conn;
    *stmtSync = stmt;
    return ret;
}

static int32_t BatchInsertDataDemo(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, uint32_t dataNum)
{
    int ret = GMERR_OK;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[batch_insert] batch option init failed.");
        return ret;
    }

    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[batch_insert] batch option set failed.");
        return ret;
    }

    ret = GmcBatchOptionSetBufLimitSize(&batchOption, BATCH_BUFFER_SIZE);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[batch_insert] batch option set buffers size failed.");
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[batch_insert] batch prepare with option failed.");
        return ret;
    }

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        DEMO_ERROR(ret, "[batch_insert] batch prepare stmt failed.");
        return ret;
    }

    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    if (dataNum > TRAIN_VEC_NUMS) {
        dataNum = TRAIN_VEC_NUMS;
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            DEMO_ERROR(ret, "[batch_insert] batch set F0 failed.");
            return ret;
        }

        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, g_trainDataSet + i * DIMS, DIMS * sizeof(float));
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            DEMO_ERROR(ret, "[batch_insert] batch set F1 failed.");
            return ret;
        }

        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            DEMO_ERROR(ret, "[batch_insert] batch add dml failed.");
            return ret;
        }

        // 批量buffer阈值2M，匹配操作数需要业务根据实际字段设置合适值
        if (i % BATCH_OPER_NUM == 0) {
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                GmcBatchDestroy(batch);
                DEMO_ERROR(ret, "[batch_insert] batch execute failed.");
                return ret;
            }

            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            if (ret != GMERR_OK) {
                GmcBatchDestroy(batch);
                DEMO_ERROR(ret, "[batch_insert] batch parse ret failed.");
                return ret;
            }

            ret = GmcBatchReset(batch);
            if (ret != GMERR_OK) {
                GmcBatchDestroy(batch);
                DEMO_ERROR(ret, "[batch_insert] batch parse reset failed.");
                return ret;
            }
        }
    }

    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        DEMO_ERROR(ret, "[batch_insert] batch execute reset failed.");
        return ret;
    }

    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        DEMO_ERROR(ret, "[batch_insert] batch parse ret failed.");
        return ret;
    }

    GmcBatchDestroy(batch);
    return ret;
}

static int32_t AnnQueryDataDemo(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, uint32_t dataNum)
{
    uint32_t prefetchNum = TOPK * 2;
    int ret = GMERR_OK;
    for (uint32_t i = 0; i < dataNum; i++) {
        // 重置stmt
        GmcResetStmt(stmt);
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[ann_query] ann query prepare stmt failed.");
            return ret;
        }

        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prefetchNum, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[ann_query] set attr failed.");
            return ret;
        }

        // 使用原向量字段id查询
        GmcFilterStructT filter = {.fieldId = 1,
            .nodeName = NULL,
            .compOp = GMC_OP_IP_VECTOR_SIMILARITY,
            .value = g_queryDataSet + i * DIMS,
            .valueLen = sizeof(float) * DIMS};

        ret = GmcSetFilterStructure(stmt, &filter);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[ann_query] set filter failed.");
            return ret;
        }

        ret = GmcSetScanLimit(stmt, TOPK);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[ann_query] set scan limit failed.");
            return ret;
        }

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[ann_query] execute failed.");
            return ret;
        }

        bool eof;
        uint32_t cnt = 0;
        while (true) {
            ret = GmcFetch(stmt, &eof);
            if (ret != GMERR_OK) {
                DEMO_ERROR(ret, "[ann_query] fetch failed.");
                return ret;
            }
            if (eof) {
                break;
            }
            bool isNull;
            uint32_t f0Value;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &f0Value, sizeof(uint32_t), &isNull);
            if (ret != GMERR_OK) {
                DEMO_ERROR(ret, "[ann_query] get Vertex failed.");
                return ret;
            }
            cnt++;
        }
        if (cnt != TOPK) {
            DEMO_ERROR(ret, "[ann_query] fetch data abnormal.");
            return ret;
        }
    }

    return ret;
}

static int AnnExample()
{
    DEMO_INFO("========== init environment ==========");
    // 客户端初始化
    int32_t ret = ClientInit();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ReadJanssonFile(g_annJsonFile, &g_annLabelJson);
    if (ret == -1) {
        return ret;
    }

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = CreateSyncConnectionAndStmt(&conn, &stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "connect failed.");
        goto UNINIT;
    }
    DEMO_INFO("connect success.");

    // 建表
    const char *errMsg = NULL;
    ret = GmcCreateVertexLabel(stmt, g_annLabelJson, NULL);
    if (ret != GMERR_OK) {
        errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "create vertex label error, errormsg is %s", errMsg);
        goto CLEAR1;
    }
    DEMO_INFO("Create vertex label success.");

    // 初始化向量，仅示范功能使用，用户不需要该步骤，跳过即可
    g_trainDataSet = (float *)malloc(sizeof(float) * DIMS * TRAIN_VEC_NUMS);
    RandomGenerateOriginalVectors(g_trainDataSet, DIMS * TRAIN_VEC_NUMS);
    g_queryDataSet = (float *)malloc(sizeof(float) * DIMS * QUERY_VEC_NUMS);
    RandomGenerateOriginalVectors(g_queryDataSet, DIMS * QUERY_VEC_NUMS);

    DEMO_INFO("========== ann vertor batch insert demo ==========");
    ret = BatchInsertDataDemo(conn, stmt, g_annLabelName, TRAIN_VEC_NUMS);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "ann vertor batch insert demo failed.");
        goto CLEAR2;
    }
    DEMO_INFO("ann vertor batch insert demo success.");

    DEMO_INFO("========== ann vector search demo ==========");

    ret = AnnQueryDataDemo(conn, stmt, g_annLabelName, QUERY_VEC_NUMS);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "ann vertor search demo failed.");
        goto CLEAR2;
    }

    DEMO_INFO("ann vertor search demo success.");

    DEMO_INFO("========== all cases passed ==========");
CLEAR2:
    DEMO_INFO("========== uninit environment ==========");
    GmcDropVertexLabel(stmt, g_annLabelName);
    free(g_trainDataSet);
    g_trainDataSet = NULL;
    free(g_queryDataSet);
    g_queryDataSet = NULL;
CLEAR1:
    DestroyConnAndStmt(conn, stmt);
UNINIT:
    free(g_annLabelJson);
    g_annLabelJson = NULL;
    // 客户端去初始化
    ClientUninit();
    return ret;
}

static int BatchLoadIndexCentroidsDemo(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    int32_t ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[vlivf_load_centroids] batch options init failed.");
        return ret;
    }

    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[vlivf_load_centroids] set order failed.");
        return ret;
    }

    ret = GmcBatchOptionSetBufLimitSize(&batchOption, BATCH_BUFFER_SIZE);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[vlivf_load_centroids] set buffers size failed.");
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[vlivf_load_centroids] batch prepare failed.");
        return ret;
    }

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_LOAD_INDEX);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        DEMO_ERROR(ret, "[vlivf_load_centroids] prepare stmt failed.");
        return ret;
    }

    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;

    for (uint32_t i = 0; i < CENTROIDS_NUMS; i++) {
        // load index时不允许设置除索引外的其他字段
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        if (ret != GMERR_INVALID_OBJECT) {
            GmcBatchDestroy(batch);
            DEMO_ERROR(ret, "[vlivf_load_centroids] set F0 error.");
            return ret;
        }

        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, g_vlivfCentroids + i * DIMS, DIMS * sizeof(float));
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            DEMO_ERROR(ret, "[vlivf_load_centroids] set F1 failed.");
            return ret;
        }

        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            DEMO_ERROR(ret, "[vlivf_load_centroids] add dml failed.");
            return ret;
        }

        if (i % BATCH_OPER_NUM == 0) {
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                GmcBatchDestroy(batch);
                DEMO_ERROR(ret, "[vlivf_load_centroids] execute failed.");
                return ret;
            }

            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            if (ret != GMERR_OK) {
                GmcBatchDestroy(batch);
                DEMO_ERROR(ret, "[vlivf_load_centroids] deparse ret failed.");
                return ret;
            }

            ret = GmcBatchReset(batch);
            if (ret != GMERR_OK) {
                GmcBatchDestroy(batch);
                DEMO_ERROR(ret, "[vlivf_load_centroids] batch reset failed.");
                return ret;
            }
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        DEMO_ERROR(ret, "[vlivf_load_centroids] batch execute failed.");
        return ret;
    }

    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        DEMO_ERROR(ret, "[vlivf_load_centroids] deparse ret failed.");
        return ret;
    }

    GmcBatchDestroy(batch);
    return ret;
}

static int VlIvfIndexQueryDemo(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, uint32_t dataNum)
{
    uint32_t prefetchNum = TOPK * 2;
    int ret = GMERR_OK;
    for (uint32_t i = 0; i < dataNum; i++) {
        // reset stmt
        GmcResetStmt(stmt);
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[vlivf_query] prepare stmt failed.");
            return ret;
        }

        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prefetchNum, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[vlivf_query] set stmt attr failed.");
            return ret;
        }

        ret = GmcSetScanLimit(stmt, TOPK);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[vlivf_query] set scan limit failed.");
            return ret;
        }

        // 指定索引id
        ret = GmcSetIndexKeyId(stmt, 1);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[vlivf_query] set index key id failed.");
            return ret;
        }

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, g_queryDataSet + i * DIMS, sizeof(float) * DIMS);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[vlivf_query] set index key value failed.");
            return ret;
        }

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "[vlivf_query] execute failed.");
            return ret;
        }

        bool eof;
        uint32_t cnt = 0;
        while (true) {
            ret = GmcFetch(stmt, &eof);
            if (ret != GMERR_OK) {
                DEMO_ERROR(ret, "[vlivf_query] fetch failed.");
                return ret;
            }
            if (eof) {
                break;
            }
            bool isNull;
            uint32_t f0Value;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &f0Value, sizeof(uint32_t), &isNull);
            if (ret != GMERR_OK) {
                DEMO_ERROR(ret, "[vlivf_query] get vertex property failed.");
                return ret;
            }
            cnt++;
        }
        if (cnt != TOPK) {
            DEMO_ERROR(ret, "[vlivf_query] fetch data is abnormal.");
            return ret;
        }
    }

    return ret;
}

static int VlivfExample()
{
    DEMO_INFO("========== init environment ==========");
    // 客户端初始化
    int32_t ret = ClientInit();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ReadJanssonFile(g_vlivfJsonFile, &g_vlivfLabelJson);
    if (ret == -1) {
        return ret;
    }

    // 建连
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = CreateSyncConnectionAndStmt(&conn, &stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[vlivf_example] connect failed.");
        goto UNINIT;
    }
    DEMO_INFO("[vlivf_example] connect success.");

    // 建表
    const char *errMsg = NULL;
    ret = GmcCreateVertexLabel(stmt, g_vlivfLabelJson, NULL);
    if (ret != GMERR_OK) {
        errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "[vlivf_example] create vertex label error, errormsg is %s", errMsg);
        goto CLEAR1;
    }
    DEMO_INFO("[vlivf_example] Create vertex label success.");

    // 初始化向量，仅示范功能使用，用户使用实际向量即可。
    g_trainDataSet = (float *)malloc(sizeof(float) * DIMS * TRAIN_VEC_NUMS);
    RandomGenerateOriginalVectors(g_trainDataSet, DIMS * TRAIN_VEC_NUMS);
    g_queryDataSet = (float *)malloc(sizeof(float) * DIMS * QUERY_VEC_NUMS);
    RandomGenerateOriginalVectors(g_queryDataSet, DIMS * QUERY_VEC_NUMS);
    g_vlivfCentroids = (float *)malloc(sizeof(float) * DIMS * CENTROIDS_NUMS);
    RandomGenerateOriginalVectors(g_vlivfCentroids, DIMS * CENTROIDS_NUMS);

    DEMO_INFO("========== vlivf index batch load centroids demo ==========");
    ret = BatchLoadIndexCentroidsDemo(conn, stmt, g_vlivfLabelName);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[vlivf_example] vlivf index batch load centroids demo failed.");
        goto CLEAR2;
    }

    DEMO_INFO("========== vlivf index batch insert demo ==========");
    ret = BatchInsertDataDemo(conn, stmt, g_vlivfLabelName, TRAIN_VEC_NUMS);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[vlivf_example] vlivf index batch insert demo failed.");
        goto CLEAR2;
    }
    DEMO_INFO("[vlivf_example] vlivf index batch insert demo success.");

    DEMO_INFO("========== vlivf index search demo ==========");
    ret = VlIvfIndexQueryDemo(conn, stmt, g_vlivfLabelName, QUERY_VEC_NUMS);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "[vlivf_example] vlivf index search demo failed.");
        goto CLEAR2;
    }
    DEMO_INFO("[vlivf_example] vlivf index search demo success.");

    DEMO_INFO("========== all cases passed ==========");
CLEAR2:
    DEMO_INFO("========== uninit environment ==========");
    GmcDropVertexLabel(stmt, g_annLabelName);
    free(g_trainDataSet);
    g_trainDataSet = NULL;
    free(g_queryDataSet);
    g_queryDataSet = NULL;
    free(g_vlivfCentroids);
    g_vlivfCentroids = NULL;
CLEAR1:
    DestroyConnAndStmt(conn, stmt);
UNINIT:
    free(g_vlivfLabelJson);
    g_vlivfLabelJson = NULL;
    // 客户端去初始化
    ClientUninit();
    return ret;
}

int main()
{
    DEMO_INFO("========== start ann example demo ==========");
    int32_t ret = AnnExample();
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "ann example demo failed.");
        return ret;
    }

    DEMO_INFO("========== start vlivf example demo ==========");
    ret = VlivfExample();
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "vlivf example demo failed.");
        return ret;
    }

    return ret;
}
