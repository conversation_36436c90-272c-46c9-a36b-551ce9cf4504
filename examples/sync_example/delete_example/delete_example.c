/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: delete_example.c
 * Description: Example
 * Author:
 * Create: 2022-2-25
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "gmc.h"

#define DEMO_INFO(...)       \
    do {                     \
        printf("[INFO] ");   \
        printf(__VA_ARGS__); \
        putchar('\n');       \
    } while (0)

#define DEMO_ERROR(ret, ...)        \
    do {                            \
        printf("[ERROR %d] ", ret); \
        printf(__VA_ARGS__);        \
        putchar('\n');              \
    } while (0)

const char *indexKeyString = "test1";
const char *labelName = "access_list";
const char *configJson = R"({"max_record_count":1000})";
const char *indexKey = "test1";
char *labelJson = NULL;
#ifdef EXPERIMENTAL_GUANGQI
// 光启持久化模式下不支持memberkey
static char *json_file = "../../schema_field/complex_example_guangqi.gmjson";
#else
static char *json_file = "../../schema_field/complex_example.gmjson";
#endif
#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
char g_connServer[64] = "usocket:/run/verona/unix_emserver";
#else
char g_connServer[64] = "channel:";
#endif
long ReadJanssonFile(const char *path, char **buf, bool convertArray)
{
    FILE *fp;
    fp = fopen(path, "rb");
    if (NULL == fp) {
        DEMO_INFO("[ReadJanssonFile] open file:%s fail.", path);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        DEMO_INFO("[ReadJanssonFile] fseek file:%s to end failed.", path);
        fclose(fp);
        return -1;
    }

    long size = ftell(fp);
    if (size < 0) {
        DEMO_INFO("[ReadJanssonFile] read file size:%ld failed.", size);
        fclose(fp);
        return -1;
    }
    char *pBuffer = (char *)malloc(size + 1);
    if (pBuffer == NULL) {
        DEMO_INFO("[ReadJanssonFile] malloc memory:%ld for file:%s failed.", size + 1, path);
        fclose(fp);
        return -1;
    }
    rewind(fp);
    long readSize = fread(pBuffer, 1, size, fp);
    if (readSize != size) {
        DEMO_INFO("[ReadJanssonFile] read file:%s failed, expectSize:%ld, actualSize:%ld.", path, size, readSize);
        free(pBuffer);
        fclose(fp);
        return -1;
    }
    pBuffer[size] = 0;
    rc = fclose(fp);
    if (rc != 0) {
        DEMO_INFO("file close error.");
        free(pBuffer);
        return -1;
    }
    *buf = pBuffer;
    return size;
}

// 客户端初始化
int32_t ClientInit()
{
    int32_t ret = GmcInit();
    if (ret != 0) {
        DEMO_ERROR(ret, "client init failed.");
        GmcUnInit();
    }

    return ret;
}

// 客户端去初始化
void ClientUninit()
{
    (void)GmcUnInit();
}

/*
 * 建立同步连接
 */
static int32_t ConnectWrapper(
    GmcConnTypeE type, const char *serverLactor, const char *username, const char *connName, GmcConnT **conn)
{
    GmcConnOptionsT *connOptions = NULL;
    int32_t ret = GmcConnOptionsCreate(&connOptions);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLactor);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    if (type == GMC_CONN_TYPE_SUB && connName != NULL) {
        ret = GmcConnOptionsSetConnName(connOptions, connName);
        if (ret != GMERR_OK) {
            goto CLEAR;
        }
    }
    ret = GmcConnect(type, connOptions, conn);
CLEAR:
    GmcConnOptionsDestroy(connOptions);
    return ret;
}

static int32_t CreateSyncConnectionAndStmt(GmcConnT **connSync, GmcStmtT **stmtSync)
{
    int32_t ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *serverLocator = g_connServer;
    char *userName = "XXuser";
    ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcAllocStmt(conn, &stmt);
    if (ret != GMERR_OK) {
        (void)GmcDisconnect(conn);
        return ret;
    }
    *connSync = conn;
    *stmtSync = stmt;
    return ret;
}

/*
 * 断连
 */
void DestroyConnAndStmt(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    (void)GmcDisconnect(conn);
}

static int32_t CreateVertexLabelDemo(GmcStmtT *stmt)
{
    // 建表
    const char *errMsg = NULL;
    int32_t ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    if (ret != GMERR_OK) {
        errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "GmcCreateVertexLabel errormsg is %s", errMsg);
        return ret;
    }
    DEMO_INFO("Create vertex label success.");
    return ret;
}

static int32_t DropVertexLabelDemo(GmcStmtT *stmt)
{
    // 删表
    DEMO_INFO("========== clear ==========");
    int32_t ret = GmcDropVertexLabel(stmt, labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

// 辅助函数
static int32_t AddVector(GmcNodeT *chainHandle)
{
    GmcNodeT *itemhandle = NULL;
    GmcNodeT *childHandler2 = NULL;
    int32_t ret = GMERR_OK;
    const uint32_t nftable_rule_num = 3;
    for (uint32_t i = 0; i < 1; i++) {
        ret = GmcNodeAppendElement(chainHandle, &itemhandle);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret =
            GmcNodeSetPropertyByName(itemhandle, "nftable_chain_name", GMC_DATATYPE_STRING, indexKey, strlen(indexKey));
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcNodeSetPropertyByName(itemhandle, "nftable_chain_handle", GMC_DATATYPE_UINT32, &i, sizeof(i));
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcNodeGetChild(itemhandle, "nftable_rule", &childHandler2);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 每个nftable_chain里嵌套一个nftable_rule，nftable_rule有三个元素

        for (uint32_t j = 0; j < nftable_rule_num; j++) {
            GmcNodeT *itemhandle2 = NULL;
            ret = GmcNodeAppendElement(childHandler2, &itemhandle2);
            if (ret != GMERR_OK) {
                return ret;
            }
            char buff[2];
            buff[0] = j + '0';
            buff[1] = 0;
            ret = GmcNodeSetPropertyByName(itemhandle2, "nftable_rule_chain", GMC_DATATYPE_STRING, buff, strlen(buff));
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = GmcNodeSetPropertyByName(itemhandle2, "nftable_rule_handle", GMC_DATATYPE_UINT32, &j, sizeof(j));
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return ret;
}
/*
 * 树模型插入操作
 */
static int32_t InsertDemo(GmcStmtT *stmt)
{
    int32_t ret;

    // GMC_OPERATION_INSERT, GMC_OPERATION_DELETE, GMC_OPERATION_UPDATE, GMC_OPERATION_REPLACE, GMC_OPERATION_MERGE ...
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Prepare statement by label name failed.");
        goto CLEAR;
    }
    DEMO_INFO("Prepare statement by label name success.");

    GmcNodeT *rootHandler = NULL;
    GmcNodeT *childHandler = NULL;
    const char *errMsg = NULL;
    ret = GmcGetRootNode(stmt, &rootHandler);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Get root node failed.");
        errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "GmcGetNode errormsg is %s", errMsg);
        goto CLEAR;
    }
    DEMO_INFO("Get root node success.");

    ret = GmcSetVertexProperty(stmt, "nftable_table_name", GMC_DATATYPE_STRING, indexKey, strlen(indexKey));
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Set vertex property failed.");
        goto CLEAR;
    }
    DEMO_INFO("Set vertex property success.");

    ret = GmcNodeGetChild(rootHandler, "nftable_chain", &childHandler);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Node get child failed.");
        goto CLEAR;
    }
    DEMO_INFO("Node get child success.");

    ret = AddVector(childHandler);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Add vertor failed.");
        goto CLEAR;
    }
    DEMO_INFO("Add vector success.");

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Execute failed.");
        errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "GmcExecute errormsg is %s", errMsg);
        goto CLEAR;
    }
    DEMO_INFO("Execute success.");

CLEAR:
    return ret;
}

/*
 * 场景描述：树模型按主键删除操作
 */
static int32_t DeleteByPrimaryKeyDemo(GmcStmtT *stmt)
{
    int32_t ret = CreateVertexLabelDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "create table failed.");
        return ret;
    }
    DEMO_INFO("create table success");
    ret = InsertDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Insert vertex failed.");
        goto RETURN;
    }
    DEMO_INFO("Insert vertex by label name success.");

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Prepare statement by label name failed.");
        goto RETURN;
    }
    DEMO_INFO("Prepare statement by label name success.");

    const char *keyName = "access_control_list_key";
    ret = GmcSetIndexKeyName(stmt, keyName);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Set index key name failed.");
        goto RETURN;
    }
    DEMO_INFO("Set index key name success.");

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, indexKeyString, strlen(indexKeyString));
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Set index key value failed.");
        goto RETURN;
    }
    DEMO_INFO("Set index key value success.");

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Execute failed.");
        const char *errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "GmcExecute errormsg is %s", errMsg);
        goto RETURN;
    }
    DEMO_INFO("Execute success.");

    // 如果删除不存在的记录，返回值为GMERR_OK，但是affectedRows为0
    uint32_t affectedRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectedRows, sizeof(affectedRows));
    if (ret == GMERR_OK && affectedRows != 0) {
        DEMO_INFO("Delete by primary key success.affectRows: %u", affectedRows);
    } else {
        DEMO_ERROR(ret, "Delete by primary key failed. affectedRows: %u", affectedRows);
        goto RETURN;
    }
RETURN:
    DropVertexLabelDemo(stmt);
    return ret;
}

/*
 * 场景描述：树模型按节点删除
 */
static int32_t DeleteByIndexDemo(GmcStmtT *stmt)
{
    int32_t ret = CreateVertexLabelDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "create table failed.");
        return ret;
    }
    // insert data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Prepare statement by label name failed.");
        goto RETURN;
    }
    DEMO_INFO("Prepare statement by label name success.");

    GmcNodeT *rootHandler = NULL;
    GmcNodeT *childHandler = NULL;
    const char *errMsg = NULL;
    ret = GmcGetRootNode(stmt, &rootHandler);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Get root node failed.");
        errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "GmcGetNode errormsg is %s", errMsg);
        goto RETURN;
    }
    DEMO_INFO("Get root node success.");

    ret = GmcSetVertexProperty(stmt, "nftable_table_name", GMC_DATATYPE_STRING, indexKey, strlen(indexKey));
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Set vertex property failed.");
        goto RETURN;
    }
    DEMO_INFO("Set vertex property success.");

    ret = GmcNodeGetChild(rootHandler, "nftable_chain", &childHandler);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Node get child failed.");
        goto RETURN;
    }
    DEMO_INFO("Node get child success.");

    ret = AddVector(childHandler);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Add vertor failed.");
        goto RETURN;
    }
    DEMO_INFO("Add vector success.");

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Execute failed.");
        errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "GmcExecute errormsg is %s", errMsg);
        goto RETURN;
    }
    DEMO_INFO("Execute success.");

    // delete data
    char *json;
    ret = GmcDumpVertexToJson(stmt, 0, &json);
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Prepare statement by label name failed.");
        goto RETURN;
    }
    DEMO_INFO("Prepare statement by label name success.");

    const char *keyName = "nftable_rule_key";
    ret = GmcSetVertexByJson(stmt, 0, json);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Set index key name failed.");
        goto RETURN;
    }
    DEMO_INFO("Set index key name success.");

    ret = GmcNodeRemoveElementByIndex(childHandler, 0);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Set index key value failed.");
        goto RETURN;
    }
    DEMO_INFO("Set index key value success.");

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Execute failed.");
        const char *errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "GmcExecute errormsg is %s", errMsg);
        goto RETURN;
    }
    DEMO_INFO("Execute success.");

    // 如果删除不存在的记录，返回值为GMERR_OK，但是affectedRows为0
    uint32_t affectedRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectedRows, sizeof(affectedRows));
    if (ret == GMERR_OK && affectedRows != 0) {
        DEMO_INFO("Delete by primary key success. affectRows: %u", affectedRows);
    } else {
        DEMO_ERROR(ret, "Delete by primary key failed. affectedRows: %u", affectedRows);
        goto RETURN;
    }
RETURN:
    // 与GmcDumpVertexToJson配套使用，dump完需要释放json内存
    GmcFreeJsonStr(stmt, json);
    DropVertexLabelDemo(stmt);
    return ret;
}

/*
 * 场景描述：树模型按过滤条件删除
 */
static int32_t DeleteByCondDemo(GmcStmtT *stmt)
{
    int32_t ret = CreateVertexLabelDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "create table failed.");
        return ret;
    }
    ret = InsertDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Insert vertex failed.");
        goto RETURN;
    }
    DEMO_INFO("Insert vertex by label name success.");

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Prepare statement by label name failed.");
        goto RETURN;
    }
    DEMO_INFO("Prepare statement by label name success.");

    ret = GmcSetFilter(stmt, "access_list.nftable_chain/nftable_chain_handle=0");
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Set filter failed. ret: %d", ret);
        goto RETURN;
    }
    DEMO_INFO("Set filter success.");

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Execute failed.");
        const char *errMsg = GmcGetLastError();
        DEMO_ERROR(ret, "GmcExecute errormsg is %s", errMsg);
        goto RETURN;
    }
    DEMO_INFO("GmcExecute success.");

    // 如果删除不存在的记录，返回值为GMERR_OK，但是affectedRows为0
    uint32_t affectedRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectedRows, sizeof(affectedRows));
    if (ret == GMERR_OK && affectedRows != 0) {
        DEMO_INFO("Delete by condition success. affectRows: %u", affectedRows);
    } else {
        DEMO_ERROR(ret, "Delete by primary key failed.affectedRows: %u", affectedRows);
        goto RETURN;
    }
RETURN:
    DropVertexLabelDemo(stmt);
    return ret;
}

/*
 * 场景描述：树模型后台删除
 */
static int32_t DeleteByAllFastDemo(GmcStmtT *stmt)
{
    int32_t ret = CreateVertexLabelDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "create table failed.");
        return ret;
    }
    ret = InsertDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Insert vertex failed.");
        goto RETURN;
    }
    DEMO_INFO("Insert vertex by label name success.");

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Prepare statement by label name failed.");
        goto RETURN;
    }
    DEMO_INFO("Prepare statement by label name success.");

    // 后台删除所有vertex数据
    ret = GmcDeleteAllFast(stmt, labelName);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Failed to delete all vertex data");
        goto RETURN;
    }

RETURN:
    DropVertexLabelDemo(stmt);
    return ret;
}

int main()
{
    // 客户端初始化
    DEMO_INFO("========== init environment ==========");
    int32_t ret = ClientInit();
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ReadJanssonFile(json_file, &labelJson, false);
    if (ret == -1) {
        goto UNINIT;
    }
    // 建连
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = CreateSyncConnectionAndStmt(&conn, &stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "connect failed.");
        goto UNINIT;
    }
    DEMO_INFO("connect success.");
    // 执行demo
    DEMO_INFO("========== primary key delete demo ==========");
    ret = DeleteByPrimaryKeyDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "primary key delete demo failed.");
        goto CLEAR1;
    }
    DEMO_INFO("========== index key delete demo ==========");
    ret = DeleteByIndexDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "index key delete demo failed.");
        goto CLEAR1;
    }
    DEMO_INFO("========== cond delete demo ==========");
    ret = DeleteByCondDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "cond delete demo failed.");
        goto CLEAR1;
    }
    DEMO_INFO("========== delete by all fast demo ==========");
    ret = DeleteByAllFastDemo(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "cond delete demo failed.");
        goto CLEAR1;
    }
    DEMO_INFO("========== all cases passed ==========");

CLEAR1:
    // 断连
    DEMO_INFO("========== uninit environment ==========");
    DestroyConnAndStmt(conn, stmt);
UNINIT:
    free(labelJson);
    labelJson = NULL;
    // 客户端去初始化
    ClientUninit();
    return ret;
}
