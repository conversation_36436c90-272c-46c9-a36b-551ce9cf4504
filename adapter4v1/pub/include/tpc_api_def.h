/*******************************************************************************
  Copyright (c) Huawei Technologies Co., Ltd. 2012-2024. All rights reserved.
  FileName     : v_dbtpc.h
  Author       : DCDM V3R1C00 Team
  Version      : DCDM V3R1C00
  Date         : 2012-11-03
  Description  : External interfaces and structures for DB TPC module
  History:
      <author>             <time>      <version >       <desc>
    DCDM V3R1C00 Team   2008-11-03   1.2             VRP Requirements
******************************************************************************/
#ifndef TPC_API_DEF_H
#define TPC_API_DEF_H

#include "tpc_types.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

#if defined(__GNUC__)
#define TPC_EXPORT __attribute__((visibility("default")))
#else
#define TPC_EXPORT
#endif

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 初始化V1适配层以及简单关系表服务。
 * @attention @li 配置文件路径需有效，最大长度为256（包含结束符'\0'）。
 * @param[in] cfgParameter DB 配置文件路径。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_Init(const char *cfgParameter);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 初始化V1适配层以及简单关系表服务。
 * @attention @li 内部使用默认配置文件路径/usr/local/file/compatV1_gmserver.ini初始化, 需要将配置文件放到指定位置。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE cfg_DBTPCCompInit(VOS_VOID);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * V1适配层以及简单关系表服务去初始化清理。
 * @attention @li 调用此接口前，需要初始化服务。\n
 */
TPC_EXPORT void TPC_UnInit(void);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 为数据库创建新会话。输出结果为新的会话ID。根据DB ID开启cdb。如果对应的数据库不存在，则此接口返回错误。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 开放DB接口返回的有效数据库ID。
 * @param[out] pulCdbId 函数返回的CDB ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_BeginCDB(VOS_UINT32 ulDbId, VOS_UINT32 *pulCdbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 为指定CDB ID的数据库。根据DB ID开启CDB。如果对应的数据库不存在或者指定CDB ID非法，则此接口返回错误。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 开放DB接口返回的有效数据库ID。
 * @param[in] ulCdbId 需要创建的CDB的ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_BeginCDBByID(VOS_UINT32 ulDbId, VOS_UINT32 ulCdbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 回滚指定CDB所做的更改。如果指定CDB ID非法，则此接口返回错误。
 * @attention @li 调用此接口前，需要先开启cdb。
 * @param[in] ulCdbId 待回滚的CDB ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_RollbackCDB(VOS_UINT32 ulCdbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 提交指定CDB所做的更改。如果指定CDB ID非法，则此接口返回错误。
 * @attention @li 调用此接口前，需要先开启cdb。
 * @param[in] ulCdbId 待提交的CDB ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CommitCDB(VOS_UINT32 ulCdbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 使用给定的数据库名称创建数据库。
 * @attention @li DB Name最大长度为16(包含结束符号 '\0')。\n
 *  @li pucDbDir 在 pstCfg 设置DB不为持久化配置下可以为NULL，否则需要传入有效文件路径。\n
 *  @li DB不路径规范化，用户需要确保路径正确。建议不要使用相对路径，避免漏洞。
 * @param[in] pucDbName 待创建的数据库名称。
 * @param[in] pucDbDir 待创建的数据库应持久化路径，只有在此数据库需要持久化时才考虑此参数。
 * @param[in] pstCfg 待创建数据库的实例参数。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CreateDB(VOS_UINT8 *pucDbName, VOS_UINT8 *pucDbDir, const DB_INST_CONFIG_STRU *pstCfg);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 使用给定的数据库名称创建数据库。
 * @attention @li DB Name最大长度为16(包含结束符号 '\0')。\n
 *  @li pucDbDir 在 pstCfg 设置DB不为持久化配置下可以为NULL，否则需要传入有效文件路径。\n
 *  @li DB不路径规范化，用户需要确保路径正确。建议不要使用相对路径，避免漏洞。
 * @param[in] pucDbName 待创建的数据库名称。
 * @param[in] pucDbDir 待创建的数据库应持久化路径，只有在此数据库需要持久化时才考虑此参数。
 * @param[in] pstCfg 待创建数据库实例的配置参数。
 * @param[in] enStorage 待创建数据库的存储位置(RAM/RSM)，目前只支持RAM。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CreateDBEx(
    VOS_UINT8 *pucDbName, VOS_UINT8 *pucDbDir, const DB_INST_CONFIG_STRU *pstCfg, DB_DATA_STORAGE_ENUM enStorage);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 打开数据库，并返回数据库ID。
 * @attention @li 参数均应为有效的指针。\n
 *  @li 在根据 pucDbName 查找不到对应数据库时，参数 pucDbDir 才有效，从持久化路径加载数据库。\n
 *  @li 所有使用该数据库ID的数据库操作，必须打开数据库才能执行。\n
 *  @li 如果多次打开同一个数据库，则返回相同的数据库ID。
 * @param[in] pucDbDir 数据库的持久化文件目录。
 * @param[in] pucDbName 打开的数据库名称。
 * @param[out] pulDbId 打开的数据库ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_OpenDB(VOS_UINT8 *pucDbDir, VOS_UINT8 *pucDbName, VOS_UINT32 *pulDbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 关闭指定数据库。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待关闭的数据库ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CloseDB(VOS_UINT32 ulDbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 删除数据库。如果该名称的数据库不存在，则此接口返回错误。如果给定的数据库是配置持久化的，并且该数据库处于关闭状态,
 * 那么当删除数据库时，对应于给定数据库的DB文件将被删除。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待关闭的数据库ID。
 * @param[in] ulBehavior 删除数据库行为(TPC_WAIT 和 TPC_NOWAIT)
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_DropDB(VOS_UINT8 *pucDbName, VOS_UINT32 ulBehavior);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 物理备份数据库。调用此接口时，数据库内部启动一个读事务。如果启动事务失败，该接口返回错误，在接口返回之前，已启动事务结束。
 * @attention @li 调用此接口前，需要先打开数据库。数据库ID从TPC_OpenDB接口中获取。
 * @param[in] ulDbId 有效数据库ID。
 * @param[in] pucFilepattern 要创建的备份文件路径，需要用户保证路径正确。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_BkpPhy(VOS_UINT32 ulDbId, VOS_UINT8 *pucFilepattern);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 物理备份数据库为指定大小端的文件。调用此接口时，数据库内部启动一个读事务。如果启动事务失败，该接口返回错误，在接口返回之前，已启动事务结束。
 * @attention @li 调用此接口前，需要先打开数据库。数据库ID从TPC_OpenDB接口中获取。
 * @param[in] ulDbId 有效数据库ID。
 * @param[in] pucFilepattern 要创建的备份文件路径，需要用户保证路径正确。
 * @param[in] pstFileSaveOpt 结构体指针，指示所需大小端。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_BkpPhyEx(VOS_UINT32 ulDbId, VOS_UINT8 *pucFilepattern, DB_SAVE_OPTIONS *pstFileSaveOpt);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 将数据库物理备份到指定大小端的文件。用户可以注册回调函数，将用户定义的数据类型和DBT_BLOCK数据类型转换为所需的大小端。
 * @attention @li 调用此接口前，需要先打开数据库。数据库ID从TPC_OpenDB接口中获取。
 * @param[in] ulDbId 有效数据库ID。
 * @param[in] pucFilepattern 要创建的备份文件路径，需要用户保证路径正确。
 * @param[in] pstFileSaveOpt 结构体指针，指示所需大小端。
 * @param[in] pfnGetTblConvHook 转换函数指针。将用户定义的数据类型和DBT_BLOCK数据类型转换为所需的大小端。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_BkpPhyWithDataConvHook(
    VOS_UINT32 ulDbId, VOS_UINT8 *pucFilepattern, DB_SAVE_OPTIONS *pstFileSaveOpt, DB_GetTblConvHook pfnGetTblConvHook);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 此接口将数据库物理备份到所需大小端格式的文件中。调用该接口时，DB内部启动对数据库的读事务。如果启动事务失败，则返回错误。
 * 启动的事务在接口返回前结束。用户可以注册回调函数，将用户定义的数据类型和DBT_BLOCK数据类型转换为所需的大小端。
 * @attention @li 调用此接口前，需要先打开数据库。数据库ID从TPC_OpenDB接口中获取。
 * @param[in] ulDbId 有效数据库ID。
 * @param[in] pucFileName 要创建的备份文件路径，需要用户保证路径正确。
 * @param[in] pucFilepattern
 * 物理备份选项，pstFileSaveOpt表示大小端格式。pfnGetTblConvHook为表字段（DBT_BLOCK或自定义字段）转换函数指针。bAddChecksum指定是否在DB文件中包括校验和。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_PhyBkp2(VOS_UINT32 ulDbId, VOS_UINT8 *pucFileName, DB_BAKPHY_OPTIONS_STRU *pstBakPhyOpts);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 此函数从文件验证数据库。在对TPC
 * DB执行恢复操作之前，检查DB文件的完整性。如果数据文件包含校验和信息，则校验和信息将用于数据文件的验证；否则接口将返回错误。根据从“快速”到“详细”所需的检查细节，提供了各种检查级别。
 * @param[in] pucDBDataFile 待检查的DB文件。
 * @param[in] enCheckLevel 需要进行的检查级别。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CheckDataFile(VOS_UINT8 *pucDBDataFile, DB_CHECK_LEVEL enCheckLevel);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 此函数用于获取数据库中指定表的checkSum值或所有记录的checkSum列表。
 * @param[in] ulDbId 有效数据库ID。
 * @param[in] rTblId create Table接口返回的有效表ID。
 * @param[in] enCRCFlag 操作标记,决定对表或记录执行checkSum计算。
 * @param[in] iSeqIndexId 索引下标,确定checkSum列表排序。指定为0xFF时,将在内部选择表的第一个唯一索引。
 * @param[in][out] pstCkSum 表和记录的checkSum值列表。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetTableCRC(VOS_UINT32 ulDbId, T_RELATION rTblId, DB_TBL_CRC_TYPE_ENUM enCRCFlag,
    T_INDEX iSeqIndexId, DB_TBL_CRC_DATA_STRU *pstCkSum);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 将指定路径下的数据库备份文件加载到内存中。
 * @param[in] pucSrcFilePattern 需要恢复数据库的备份文件路径。
 * @param[in] pucDestDBName 待恢复的数据库名称。
 * @param[in] pucDestDir 恢复数据库的路径。路径还必须包括文件名（长度限制为DB_PATH_LEN，包括'\0'）。
 * @param[in] enRestore 如果已存在同名数据库，则选择该选项决定接口的行为。
 * @param[in] pstDbConfig 静态配置参数。
 * @param[in] ulBehavior 恢复行为。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_Restore(VOS_UINT8 *pucSrcFilePattern, VOS_UINT8 *pucDestDBName, VOS_UINT8 *pucDestDir,
    DB_RESTORETYPE_ENUM enRestore, const DB_RESTORE_CONFIG_STRU *pstDbConfig, VOS_UINT32 ulBehavior);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 将指定路径下的数据库备份文件加载到内存中。用户可以注册回调函数，将用户定义的数据类型和DBT_BLOCK数据类型转换为所需的大小端。
 * @param[in] pucSrcFilePath 需要恢复数据库的备份文件路径。
 * @param[in] pucDestDBName 待恢复的数据库名称。
 * @param[in] pucDestDir 恢复数据库的路径。路径还必须包括文件名（长度限制为DB_PATH_LEN，包括'\0'）。
 * @param[in] enRestore 如果已存在同名数据库，则选择该选项决定接口的行为。
 * @param[in] pstDbConfig 静态配置参数。
 * @param[in] ulBehavior 恢复行为。
 * @param[in] pfnGetTblConvHook 转换函数指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_RestoreWithDataConvHook(VOS_UINT8 *pucSrcFilePath, VOS_UINT8 *pucDestDBName,
    VOS_UINT8 *pucDestDir, DB_RESTORETYPE_ENUM enRestore, const DB_RESTORE_CONFIG_STRU *pstDbConfig,
    VOS_UINT32 ulBehavior, DB_GetTblConvHook pfnGetTblConvHook);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 为指定的数据库ID创建边。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待建边的数据库ID。
 * @param[in] edgeName 边名字。
 * @param[in] edgeDef 边定义结构，有效的指针。详见结构体DB_EDGE_DEF_STRU。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CreateEdge(VOS_UINT32 ulDbId, VOS_UINT8 *edgeName, DB_EDGE_DEF_STRU *edgeDef);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 为指定的数据库ID删除边。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待删边的数据库ID。
 * @param[in] edgeName 边名字。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_DropEdge(VOS_UINT32 ulDbId, VOS_UINT8 *edgeName);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 为指定的数据库ID获取边信息。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待获取边的数据库ID。
 * @param[in] edgeName 边名字。
 * @param[out] edgeInfo 输出的边信息，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetEdgeInfo(VOS_UINT32 ulDbId, VOS_UINT8 *edgeName, DB_EDGE_INFO_STRU *edgeInfo);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 为指定的数据库ID创建表。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待建表的数据库ID。
 * @param[in] pstRelDef 表定义结构，有效的指针。详见结构体DB_REL_DEF_STRU。
 * @param[out] pusRelId 建表完成输出的表ID，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CreateTbl(VOS_UINT32 ulDbId, DB_REL_DEF_STRU *pstRelDef, VOS_UINT16 *pusRelId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 为指定的数据库ID创建表。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待建表的数据库ID。
 * @param[in] pstRelDef 表定义结构，有效的指针。详见结构体DB_REL_DEF_STRU。
 * @param[in] pstFeature 表的特征结构，此参数暂不兼容。
 * @param[out] pusRelId 建表完成输出的表ID，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CreateTblEx(
    VOS_UINT32 ulDbId, DB_REL_DEF_STRU *pstRelDef, VOS_UINT16 *pusRelId, DB_REG_FEATURE_STRU *pstFeature);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 指定表ID为数据库创建表。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待建表的数据库ID。
 * @param[in] pstRelDef 表定义结构，有效的指针。详见结构体DB_REL_DEF_STRU。
 * @param[in] usRelId 建表指定的表ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CreateTblByID(VOS_UINT32 ulDbId, VOS_UINT16 usRelId, DB_REL_DEF_STRU *pstRelDef);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 指定表ID为数据库创建表。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待建表的数据库ID。
 * @param[in] pstRelDef 表定义结构，详见结构体DB_REL_DEF_STRU。
 * @param[in] usRelId 建表指定的表ID。
 * @param[in] pstFeature 表的特征结构，此参数暂不兼容。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CreateTblByIDEx(
    VOS_UINT32 ulDbId, VOS_UINT16 usRelId, DB_REL_DEF_STRU *pstRelDef, DB_REG_FEATURE_STRU *pstFeature);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 删除数据库表，释放与表关联的资源。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 待删表所在的数据库ID。
 * @param[in] usRelId 待删表的表ID。
 * @param[in] ulBehavior 删除表行为(TPC_WAIT 和 TPC_NOWAIT)，该参数暂不兼容。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_DropTbl(VOS_UINT32 ulDbId, VOS_UINT16 usRelId, VOS_UINT32 ulBehavior);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 修改数据库中指定表的表ID与表名。
 * @attention @li 调用此接口前，需要先打开数据库。\n
 * @li 若传入的 pucNewTblName 为 NULL，则只修改表的ID。
 * @param[in] ulDbId 待操作表所在的数据库ID。
 * @param[in] rOrigTblId 表的原始表ID。
 * @param[in] pucNewTblName 新的表名，可为NULL，为NULL则只修改表ID。
 * @param[in] rNewTblId 新的表ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_ModifyTblNameAndID(
    VOS_UINT32 ulDBId, T_RELATION rOrigTblId, VOS_UINT8 *pucNewTblName, T_RELATION rNewTblId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 修改表的扩展步长。
 * @attention @li 该接口不兼容，内部直接返回OK。
 * @param[in] ulDbId 待操作表所在的数据库ID。
 * @param[in] usTblId 待修改的表ID。
 * @param[in] ulExtendRecNum 表的扩展步长。
 * @return @li GMERR_OK。
 */
TPC_EXPORT DB_ERR_CODE TPC_SetExtendRecNum(VOS_UINT32 ulDbId, VOS_UINT16 usTblId, VOS_UINT32 ulExtendRecNum);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 设置表的最大记录数量。
 * @attention @li 调用此接口前，需要先打开数据库 /n
 * @li 新设置的最大记录数需要大于等于原始最大记录数。
 * @param[in] ulDbId 待修改表所在的数据库ID。
 * @param[in] rTblId 待修改表的ID。
 * @param[in] usNewGrowthRate 扩展率，参数不兼容，内部不处理。
 * @param[in] ulNewMaxStepSize 扩展最大步长，参数不兼容，内部不处理。
 * @param[in] ulNewMaxRecNum 新的最大记录数。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_SetMaxRecNumOfTable(VOS_UINT32 ulDBId, T_RELATION rTblId, VOS_UINT16 usNewGrowthRate,
    VOS_UINT32 ulNewMaxStepSize, VOS_UINT32 ulNewMaxRecNum);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 设置数据库私有描述信息，指定描述信息数据和长度。
 * @attention @li 调用此接口前，需要先打开数据库 /n
 * @li 只有在删除数据库时，才会释放分配给存储私有描述的内存 /n
 * @li 如果描述信息长度超过最大长度，则返回错误。
 * @param[in] pucDbName 数据库名称，有效的指针。
 * @param[in] pDBDescrInfo 要设置的描述buffer，有效的指针。
 * @param[in] ulLen 描述buffer的长度。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_SetDBDesc(VOS_UINT8 *pucDbName, VOS_VOID *pDBDescrInfo, VOS_UINT32 ulLen);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDBID、数据库ID、关联号码插入一条记录。
 * @attention
 * 注意事项请参考DCDM数据库开发者指南
 * @param[in] ulCdbId 	会话ID。0~（数据库最大会话数-1）。
 * @param[in] ulDbId 	开放DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo 	create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstDsBuf 	待插入记录的缓冲区。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_InsertRec(
    VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, const DB_DSBUF_STRU *pstDsBuf);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDBID、数据库ID、关联号码删除符合条件的记录。
 * @attention
 * 注意事项请参考DCDM数据库开发者指南
 * @param[in] ulCdbId 	会话ID。0~（数据库最大会话数-1）。
 * @param[in] ulDbId 	开放DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo 	create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstCond 	结构体变量保持条件，从表中删除记录。有效的指针。
 * @param[out] pulRecNum 无符号32位整数，包含从关系中删除的记录数。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_DeleteRec(
    VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond, VOS_UINT32 *pulRecNum);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 此接口更新特定数据库的表中满足给定条件的记录。
 * @attention
 * 注意事项请参考DCDM数据库开发者指南
 * @param[in] ulCdbId	会话ID。0~（数据库最大会话数-1）。
 * @param[in] ulDbId	开放DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo	create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstCond	结构体变量保持条件，从表中删除记录。有效的指针。
 * @param[in] pstFldFilter	结构变量，用于保存满足给定条件的记录中要修改的字段。有效的指针。
 * @param[in] pstDsBuf 		指示保存字段筛选输入参数中指定的字段的新值的结构变量。有效的指针。
 * @param[out]	pulRecNum 	指示无符号32位整数，它保存了被修改的记录数。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_UpdateRec(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_DSBUF_STRU *pstDsBuf, VOS_UINT32 *pulRecNum);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取数据库私有描述信息，指定存储描述信息的buffer和长度。
 * @attention @li pDBDescrInfo 用户应分配缓冲区，长度应在参数 pullLen 中指定。/n
 * @li pDBDescrInfo传入NULL或输入长度小于实际描述长度，则只获取当前数据库描述信息实际长度。/n
 * @li 成功获取描述信息后，pulLen 会被更新为时间描述信息长度。
 * @param[in] pucDbName 数据库名称，有效的指针。
 * @param[in] pucDbDir 数据库持久化路径，可为NULL。
 * @param[out] pDBDescrInfo 存储描述信息buffer，有效的指针。
 * @param[in, out] pulLen 描述buffer的长度，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetDBDesc(
    VOS_UINT8 *pucDbName, VOS_UINT8 *pucDbDir, VOS_VOID *pDBDescrInfo, VOS_UINT32 *pulLen);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取给定名称的数据库ID。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] pucDatabaseName 数据库名称，有效的指针。
 * @param[out] pulDatabaseId 获取到的数据库ID，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetDBId(VOS_UINT8 *pucDatabaseName, VOS_UINT32 *pulDatabaseId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取给定ID的数据库名。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 数据库ID。
 * @param[out] pucDbName 数据库名，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetDBName(VOS_UINT32 ulDbId, VOS_UINT8 *pucDbName);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取给定数据库中与表ID对应的表中所有字段的详细信息。
 * @attention @li 调用此接口前，需要先打开数据库。/n
 * @li 用户必须分配足够的内存(字段数 * sizeof(DB_FIELD_INFO))，以保存所有字段的字段信息，并指定变量中分配的内存大小。/n
 * @li 如果分配的内存不够，则返回错误。/n
 * @li 字段个数可以通过 TPC_GetTblInfo 接口获取。
 * @param[in] ulDBID 数据库ID。
 * @param[in] usRelID 表ID。
 * @param[out] pstAllFieldInfo 表中的字段信息，详见结构体DB_FIELD_INFO，有效的指针。
 * @param[in] ulBufSize 用户分配的字节数(pstAllFieldInfo大小)。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetTblColInfo(
    VOS_UINT32 ulDBID, VOS_UINT16 usRelID, DB_FIELD_INFO *pstAllFieldInfo, VOS_UINT32 ulBufSize);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取给定数据库中与表ID对应的表中所有索引的详细信息。
 * @attention @li 调用此接口前，需要先打开数据库。/n
 * @li 用户必须分配足够的内存(索引数 * sizeof(DB_INDEX_INFO))，以保存所有索引信息，并指定变量中分配的内存大小。/n
 * @li 如果分配的内存不够，则返回错误。/n
 * @li 索引个数可以通过 TPC_GetTblInfo 接口获取。
 * @param[in] ulDbId 数据库ID。
 * @param[in] usRelID 表ID。
 * @param[out] pstAllIndexInfo 表中的索引信息，详见结构体DB_INDEX_INFO，有效的指针。
 * @param[in] ulBufSize 用户分配的字节数(pstAllIndexInfo 大小)。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetTblIdxInfo(
    VOS_UINT32 ulDbId, VOS_UINT16 usRelID, DB_INDEX_INFO *pstAllIndexInfo, VOS_UINT32 ulBufSize);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取给定数据库中表的数量。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 数据库ID。
 * @param[out] pulRelNum 数据库中表的数量，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetTblCount(VOS_UINT32 ulDbId, VOS_UINT32 *pulRelNum);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取给定数据库中给定表名的表ID。
 * @attention @li 调用此接口前，需要先打开数据库。
 * @param[in] ulDbId 数据库名称。
 * @param[in] pucRelName 表名，有效的指针。
 * @param[out] pusRelId 表ID，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetTblId(VOS_UINT32 ulDbId, VOS_UINT8 *pucRelName, VOS_UINT16 *pusRelId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取给定数据库中表数量以及所有的表名。
 * @attention @li 调用此接口前，需要先打开数据库。/n
 * @li 如果pulTblCount大于数据库中的表数量，则ppucRelation用于获取表名，而pulTblCount会更新为实际表数量。/n
 * @li 如果用户缓冲区不足（数据库中的表数大于用户传递的表数），则接口返回VOS_ERRNO_DB_BUFNOTENOUGH，/n
 * @li 并且关系数将使用数据库中的实际关系数进行更新，并且关系名没有输出。/n
 * @li 如果ppucRelation为NULL，那么pulTblCount将用于获取数据库中表数量，不输出表名。
 * @param[in] ulDbId 数据库名称。
 * @param[in, out] pulTblCount 传入ppucRelation数组大小，如果ppucRelation为NULL，则只用于获取数据库中表的数量。
 * @param[out] ppucRelation 表示数据库的所有表名数组。输出参数ppucRelation的内存应该正确。
                            应将内存分配为长度为DB_REL_NAME_LEN的字符串数组。数组大小应等于pulTblCount的输入值。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetTblNamesAndCount(VOS_UINT32 ulDbId, VOS_UINT32 *pulTblCount, VOS_UINT8 **ppucRelation);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取数据库指定表的记录数量。
 * @attention @li 调用此接口前，需要先打开数据库。/n
 * @param[in] ulDbId 数据库ID。
 * @param[in] usRelID 表ID。
 * @param[out] pulActRec 表的记录数量，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetRelActRec(VOS_UINT32 ulDbId, VOS_UINT16 usRelID, VOS_UINT32 *pulActRec);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 同接口 TPC_GetRelActRec
 * @param[in] ulDbId 数据库ID。
 * @param[in] usRelID 表ID。
 * @param[out] pulActRec 表的记录数量，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE DBS_GetRelActRec(VOS_UINT32 ulDbId, VOS_UINT16 usRelID, VOS_UINT32 *pulActRec);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取数据库中指定表ID对应表的信息。
 * @attention @li 调用此接口前，需要先打开数据库。/n
 * @param[in] ulDbId 数据库ID。
 * @param[in] usRelID 表ID。
 * @param[out] pstRelationInfo 表信息，详见结构体DB_RELATION_INFO，有效的指针。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetTblInfo(VOS_UINT32 ulDbId, VOS_UINT16 usRelID, DB_RELATION_INFO *pstRelationInfo);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取活跃的CDB数量。
 * @attention @li 调用此接口前，需要先打开数据库。/n
 * @li 若DB ID传入有效，获取指定DB ID活跃的CDB数量。/n
 * @li 若DB ID传入uint32的最大值，获取所有活跃的CDB数量。
 * @param[in] ulDbId 数据库ID。
 * @param[out] pulActiveCDBNum 当前DB下活跃的CDB数量。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetActiveCDBNum(VOS_UINT32 ulDbId, VOS_UINT32 *pulActiveCDBNum);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取指定DB ID已启用的查询Handle信息。
 * @attention @li 调用此接口前，需要先打开数据库。/n
 * @param[in] ulDbId 数据库ID。
 * @param[out] pstHandleCtrlInfo 活跃的查询handle信息。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetOpenHandleInfo(VOS_UINT32 ulDbId, DB_HANDLECTRL_INFO_STRU *pstHandleCtrlInfo);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 获取指定表ID的一条记录的长度。
 * @attention @li 调用此接口前，需要先打开数据库。/n
 * @param[in] ulDbId 数据库ID。
 * @param[in] usRelID 表ID。
 * @param[out] pulRecLen 记录长度。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_GetRelRecLen(VOS_UINT32 ulDbId, VOS_UINT16 usRelID, VOS_UINT32 *pulRecLen);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取满足给定条件的记录。
 * @attention
 * DB_DSBUF_STRU结构体中的成员usRecNum可作为最大限制查询数量。 \n
 * 其他注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId open DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstCond 查询条件。有效的指针。
 * @param[in] pstFldFilter 用于保存满足给定条件的记录中要获取的字段。有效的指针。
 * @param[in,out] pstDsBuf 查询结果。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_SelectAllRec(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo,
    DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_DSBUF_STRU *pstDsBuf);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取满足给定条件的记录。
 * @attention
 * DB_BUF_STRU结构体中的成员ulRecNum可作为最大限制查询数量。 \n
 * 若usRecNum设置为DB_SELECT_ALL，则在查询结果buf不足够的场景会继续进行查询，并更新usRecNum为满足条件的记录数量。 \n
 * 若usRecNum设置为非DB_SELECT_ALL，则在查询结果buf不足够的场景将直接结束查询，不修改usRecNum。
 * 其他注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId open DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstCond 查询条件。有效的指针。
 * @param[in] pstFldFilter 用于保存满足给定条件的记录中要获取的字段。有效的指针。
 * @param[in,out] pstBufData 查询返回结果。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_SelectAllRecEx(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo,
    DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_BUF_STRU *pstBufData);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取满足给定路径上所有的记录。
 * @attention
 * 其他注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId open DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstCond 查询条件。有效的指针。
 * @param[in] pstFldFilter 用于保存满足给定条件的记录中要获取的字段。有效的指针。
 * @param[in] pstPath 用于保存满足给定边组成的路径。
 * @param[out] pstBuff 查询结果。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_SelectAllRecByPath(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo,
    DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_PATH_STRU *pstPath, DB_MUTIL_BUF_STRU **pstBuff);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取满足给定条件的记录，并进行排序。
 * @attention
 * DB_DSBUF_STRU结构体中的成员usRecNum可作为最大限制查询数量 \n
 * 其他注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId open DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstSort 用于保存查询结果中需要排序的字段已经排序方式（升序或降序）。有效的指针。
 * @param[in] pstCond 查询条件。有效的指针。
 * @param[in] pstFldFilter 用于保存满足给定条件的记录中要获取的字段。有效的指针。
 * @param[in,out] pstDsBuf 查询结果。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_SelectAllRecByOrder(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo,
    DB_SORT_STRU *pstSort, DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_DSBUF_STRU *pstDsBuf);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取满足给定条件的记录，并根据排序结构进行排序处理。
 * @attention
 * DB_BUF_STRU结构体中的成员ulRecNum可作为最大限制查询数量。 \n
 * 若usRecNum设置为DB_SELECT_ALL，则在查询结果buf不足够的场景会继续进行查询，并更新usRecNum为满足条件的记录数量。 \n
 * 若usRecNum设置为非DB_SELECT_ALL，则在查询结果buf不足够的场景将直接结束查询，不修改usRecNum。
 * 其他注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId open DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstSort 用于保存查询结果中需要排序的字段已经排序方式（升序或降序）。有效的指针。
 * @param[in] pstCond 查询条件。有效的指针。
 * @param[in] pstFldFilter 用于保存满足给定条件的记录中要获取的字段。有效的指针。
 * @param[in,out] pstBufData 查询返回结果。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_SelectAllRecByOrderEx(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo,
    DB_SORT_STRU *pstSort, DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_BUF_STRU *pstBufData);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取第一条满足给定条件的记录。
 * @attention
 * 注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId open DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstCond 查询条件。有效的指针。
 * @param[in] pstFldFilter 用于保存满足给定条件的记录中要获取的字段。有效的指针。
 * @param[out] pstDsBuf 查询返回结果。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_SelectFirstRec(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo,
    DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_DSBUF_STRU *pstDsBuf);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取满足给定条件的记录数量。
 * @attention
 * 注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId open DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstCond 查询条件。有效的指针。
 * @param[out] pulRecNum 查询返回结果。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_CountMatchingRecs(
    VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond, VOS_UINT32 *pulRecNum);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID判断给定条件的记录是否存在。
 * @attention
 * 注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId open DB接口返回的有效数据库ID。数据类型限制。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[in] pstCond 查询条件。有效的指针。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_RecordExist(
    VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、表ID获取此接口获取CDB中插入、删除和更新的所有记录。
 * @attention
 * 注意事项请参考DCDM数据库开发者指南。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] usRelNo create Table接口返回的有效表ID。数据类型限制。
 * @param[out] pulInsRecNum CDB中插入的记录数。
 * @param[out] ppInsRecList CDB插入的记录, 按顺序依次排列。
 * @param[out] pulDelRecNum CDB中删除的记录数。
 * @param[out] ppDelRecList CDB中删除的RDB记录。
 * @param[out] pulUpdRecNum CDB中更新的记录数（包括新的数据和老的记录数）。
 * @param[out] ppUpdRecList CDB中更新的记录，有一条新更新的CDB记录和一条已更新的旧RDB记录。
 * @return @li GMERR_OK @li VOS_ERRNO_DB_NULLPTR @li 其他返回值，ERROR CODE AS PER DOPRA STANDARD。
 */
TPC_EXPORT DB_ERR_CODE TPC_SelectCdbDiffData(VOS_UINT32 ulCdbId, VOS_UINT16 usRelNo, VOS_UINT32 *pulInsRecNum,
    VOS_VOID **ppInsRecList, VOS_UINT32 *pulDelRecNum, VOS_VOID **ppDelRecList, VOS_UINT32 *pulUpdRecNum,
    VOS_VOID **ppUpdRecList);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 设置查询句柄
 * @param[in] ulCdbId cdbId
 * @param[in] ulDbId 数据库id
 * @param[in] usTblId 表id
 * @param[in] pstCond 查询条件
 * @param[in] pstFldFilter 过滤字段
 * @param[out] phSelect 句柄ID
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_BeginSelect(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usTblId, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 设置带排序的查询句柄
 * @param[in] ulCdbId cdbId
 * @param[in] ulDbId 数据库id
 * @param[in] usTblId 表id
 * @param[in] pstSort 排序字段
 * @param[in] pstCond 查询条件
 * @param[in] pstFldFilter 过滤字段
 * @param[out] phSelect 句柄ID
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_BeginIdxSelectByOrder(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usTblId,
    DB_SORT_STRU *pstSort, DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 释放接口TPC_SelectCdbDiffData中获取CDB中插入、删除和更新的所有记录内存。
 * @attention
 * 注意事项请参考DCDM数据库开发者指南。
 * @param[in] ppInsRecList CDB插入的记录, 按顺序依次排列。
 * @param[in] ppDelRecList CDB中删除的RDB记录。
 * @param[in] ppUpdRecList CDB中更新的记录，有一条新更新的CDB记录和一条已更新的旧RDB记录。
 * @return @li VOS_VOID
 */
TPC_EXPORT VOS_VOID TPC_FreeDiffData(VOS_VOID *ppInsRecList, VOS_VOID *ppDelRecList, VOS_VOID *ppUpdRecList);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 读取查询句柄数据。
 * @param[in] ulCdbId cdbId
 * @param[in] hSelect 句柄id
 * @param[in, out] pstBuff 查询结果，内置需要查询的数量
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_FetchSelectRec(VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect, DB_DSBUF_STRU *pstBuff);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 释放指定的handle。
 * @param[in] ulCdbId cdbId
 * @param[in] hSelect 句柄id
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_EndSelect(VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect);

/**
 * @ingroup tpc_api_def
 * @par 描述：单跳获取图数据设置起点条件
 * @attention @li 不支持复杂运算符且句柄不能初始化为0
 * @param[in] ulCdbId cdbId
 * @param[in] ulDbId 数据库id
 * @param[in] usTblId 表id
 * @param[in] pstCond 查询条件
 * @param[in] pstFldFilter 过滤字段
 * @param[out] phSelect 句柄ID
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_BeginSelectByTopo(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usTblId,
    DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect);

/**
 * @ingroup tpc_api_def
 * @par 描述：单跳获取数据
 * @param[in] ulCdbId cdbId
 * @param[in] hSelect 句柄id
 * @param[in] fetchCount 最大数据量
 * @param[out] pstBuff 查询结果
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_FetchSelectTopoRec(
    VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect, VOS_UINT16 fetchCount, DB_MUTIL_BUF_STRU **pstBuff);

/**
 * @ingroup tpc_api_def
 * @par 描述：跳到下一条边
 * @param[in] hSelect 句柄id
 * @param[in] edge 边
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_MoveNextByEdge(DB_SELHANDLE hSelect, DB_EDGE_CON_STRU edge);

/**
 * @ingroup tpc_api_def
 * @par 描述：回到上一条边
 * @param[in] hSelect 句柄id
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_MoveBack(DB_SELHANDLE hSelect);

/**
 * @ingroup tpc_api_def
 * @par 描述：结束图查询句柄
 * @attention @li 不调用该接口关闭查询句柄，则无法关闭数据库
 * @param[in] ulCdbId cdbId
 * @param[in] hSelect 句柄id
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_EndTopoSelect(VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect);

/**
 * @ingroup tpc_api_def
 * @par 描述：释放图查询结果内存
 * @param[in] memId 查询结果id
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT VOS_VOID TPC_FreeBufMemById(VOS_UINT32 memId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 释放指定CDB的所有handle。
 * @param[in] ulCdbId cdbId。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_ReleaseAllHandles(VOS_UINT32 ulCdbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 强制提交CDB所做的更改。此接口即使已经提交冲突，也允许重演操作并提交。如果指定CDB ID非法，则此接口返回错误。
 * @attention @li 调用此接口前，需要先开启cdb。
 * @param[in] ulCdbId 待提交的CDB ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_ForceCommitCDB(VOS_UINT32 ulCdbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 释放指定DB的所有handle。
 * @param[in] ulDbId 数据库ID。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CloseAllHandles(VOS_UINT32 ulDbId);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * DFX视图
 * @param[in] sysviewType DFX查询类型。
 * @param[in] sysviewArgs DFX查询参数。
 * @param[out] pucResult DFX查询结果。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_Sysview(
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType, DB_TPC_SYSVIEW_ARGS_STRU *sysviewArgs, VOS_UINT8 **pucResult);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 释放DFX视图的查询结果
 * @param[in] pucResult 待释放的DFX查询结果。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT VOS_VOID TPC_FreeSysviewResult(VOS_UINT8 **pucResult);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据给定条件选择对应表的最佳的索引，若没有匹配到索引，对应返回的索引ID为0xFF。
 * 输出的查询计划可以配合接口TPC_SimpleSelectAllRec或TPC_SelectAllRec2使用。
 * @param[in] ulCdbId 会话ID。0~（数据库最大会话数-1）或 RDB。
 * @param[in] ulDbId Open DB接口返回有效的数据库ID。
 * @param[in] usRelNo Create Table接口返回的有效表ID。
 * @param[in] pstCond 查询条件。
 * @param[out] pstQueryPlan 根据条件生成的计划。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_PrepareQueryPlan(
    VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond, DB_QUERY_PLAN_STRU *pstQueryPlan);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取满足给定条件的记录。
 * 仅支持[DB_OP_EQUAL, DB_OP_LARGEREQUAL]查询条件
 * @param[in, out] pstSelect 查询参数，具体见DB_SELECT_STRU结构体。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_SimpleSelectAllRec(DB_SELECT_STRU *pstSelect);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 根据CDB ID、数据库ID和表ID获取满足给定条件的记录。
 * TPC_PrepareQueryPlan接口输出的已准备好的计划，可以使用此接口查询数据。
 * @param[in, out] pstSelect 查询参数，具体见DB_SELECT_STRU结构体。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_SelectAllRec2(DB_SELECT_STRU *pstSelect);

/**
 * @ingroup tpc_api_def
 * @par 描述：
 * 此接口通过用n条记录填充中间空闲插槽来压缩表，并释放数据块（如果为空）。
 * @param[in] ulDbId 开放DB接口返回的有效数据库ID。
 * @param[in] usTblId create Table接口返回的有效表ID。
 * @param[in] ulCompRecNum 压缩的最大记录数。
 * @param[in] pfnCompNotify 通知物理记录ID变化的函数钩子。
 * @return @li GMERR_OK @li 其他返回值。
 */
TPC_EXPORT DB_ERR_CODE TPC_CompressTable(
    VOS_UINT32 ulDbId, VOS_UINT16 usTblId, VOS_UINT32 ulCompRecNum, DB_COMPRESSTBL_NOTIFY_FUNC pfnCompNotify);

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif /* TPC_API_DEF_H */
