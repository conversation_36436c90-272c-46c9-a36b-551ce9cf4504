/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: v1_wrapper_dml.c
 * Description: realization of interface of v1 wrapper dql APIs.
 * Author:
 * Create:
 */

#include "v1_wrapper_dql.h"

inline static Status AdapterCheckBufData(DB_BUF_STRU *bufData)
{
    if (bufData == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NULLPTR, "pstBufData is null pointer.");
        return VOS_ERRNO_DB_NULLPTR;
    }

    if (bufData->pBuf == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NULLPTR, "Buf in pstBufData is null pointer.");
        return VOS_ERRNO_DB_NULLPTR;
    }
    return GMERR_OK;
}

Status AdapterCheckSelectArgs(DB_SORT_STRU *pstSort, DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter,
    DB_BUF_STRU *pstBufData, bool isTpc)
{
    ADPT4V1_CHECK_OBJECT_NULL(pstSort, "pstSort is null pointer.");
    ADPT4V1_CHECK_OBJECT_NULL(pstCond, "pstCond is null pointer.");
    ADPT4V1_CHECK_OBJECT_NULL(pstFldFilter, "pstFldFilter is null pointer.");

    const uint32_t maxCondCnt = isTpc ? TPC_COND_MAX : DB_COND_MAX;
    if (pstCond->usCondNum > maxCondCnt) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDCONDITION,
            "Number of input conditions(%" PRIu16 ") is greater than the maximum limit(%" PRIu32 ").",
            pstCond->usCondNum, maxCondCnt);
        return VOS_ERRNO_DB_INVALIDCONDITION;
    }
    return AdapterCheckBufData(pstBufData);
}

static Status AdapterSelectAllRecByOrderEx(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo,
    DB_SORT_STRU *pstSort, DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_BUF_STRU *pstBufData,
    bool isTpcDb)
{
    Status ret = AdapterCheckSelectArgs(pstSort, pstCond, pstFldFilter, pstBufData, isTpcDb);
    if (ret != GMERR_OK) {
        return ret;
    }

    AdptV1ItemRefT conn = {0};
    uint32_t actDbId;
    ret = AdapterV1GetCdbConn(ulCdbId, ulDbId, DB_INVALID_UINT16, &conn, &actDbId);
    if (ret != GMERR_OK) {
        return ret;
    }

    SimpRelScanCtxT scanCtx = {.dbId = actDbId,
        .relId = usRelNo,
        .cond = pstCond,
        .fldFilter = pstFldFilter,
        .sort = pstSort,
        .dataStru = pstBufData,
        .matchedCnt = NULL};
    ret = GmeSimpleRelSelectAllRec(conn.item, &scanCtx, isTpcDb);
    AdapterV1QuickFreeCdbConn(ulCdbId, &conn);
    return ret;
}

/* This API gets all the records which match the required condition in a table
   for a particular database. No. of records to fetched is an input. */
DB_ERR_CODE TPC_SelectAllRec(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_DSBUF_STRU *pstDsBuf)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstDsBuf, "pstDsBuf is null pointer for select all record.");

    DB_BUF_STRU pstBufData;
    AdapterInitDbBuff(&pstBufData, pstDsBuf);
    DB_SORT_STRU pstSort = {.ucSortNum = 0};
    ret = AdapterSelectAllRecByOrderEx(ulCdbId, ulDbId, usRelNo, &pstSort, pstCond, pstFldFilter, &pstBufData, true);
    if (ret != GMERR_OK) {
        if (ret == VOS_ERRNO_DB_RECNOTEXIST) {
            pstDsBuf->usRecNum = 0;
        }
        return ConvertErrorCode(ret);
    }

    AdapterSetDsBufByDbBuf(&pstBufData, pstDsBuf);
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (GetSysviewPerfStatState()) {
            RecordPerfStatInfo(TPC_API_SELECT_ALL_RECORD, startTime, endTime);
        }
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_SELECT)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Select all perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usRelNo,
                .pstCond = pstCond,
                .pstFldFilter = pstFldFilter,
                .pstSort = NULL,
                .recNum = pstDsBuf->usRecNum,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_SELECT, &detail);
        }
    }
    return GMERR_OK;
}

/* This API gets all the records which match the required condition in a table
   for a particular database. No. of records to fetched is an input. */
DB_ERR_CODE TPC_SelectAllRecEx(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_BUF_STRU *pstBufData)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstBufData, "pstBufData is null pointer for select all record by order.");
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    DB_SORT_STRU pstSort = {.ucSortNum = 0};
    ret = AdapterSelectAllRecByOrderEx(ulCdbId, ulDbId, usRelNo, &pstSort, pstCond, pstFldFilter, pstBufData, true);
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_SELECT)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Select all ex perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usRelNo,
                .pstCond = pstCond,
                .pstFldFilter = pstFldFilter,
                .pstSort = NULL,
                .recNum = pstBufData->ulRecNum,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_SELECT, &detail);
        }
    }
    return ConvertErrorCode(ret);
}

Status AdapterSelectAllRecSingle(
    GmeConnT *conn, VOS_UINT32 ulDbId, DB_COND_STRU *pstCond, uint32_t fetchNum, AdptTopoHandleInfoItemT *info)
{
    DB_SORT_STRU pstSort = {.ucSortNum = 0};
    SimpRelScanCtxT scanCtx = {.dbId = ulDbId,
        .relId = info->tableId,
        .cond = pstCond,
        .fldFilter = NULL,
        .sort = &pstSort,
        .fetchNum = fetchNum,
        .isFetchSelect = true,
        .isFetchTopo = true,
        .handleCond = &info->handleCond,
        .dataStru = &info->result,
        .hpBeginAddr = &info->beginAddr,
        .resumeDataInfo = &info->resumeDataInfo,
        .matchedCnt = NULL};
    return GmeSimpleRelSelectAllRec(conn, &scanCtx, true);
}

Status AdapterSelectAllRecByTopo(
    GmeConnT *conn, VOS_UINT32 ulDbId, DB_COND_STRU *pstCond, AdptTopoHandleInfoT *topoHandle)
{
    for (uint32_t i = 0; i < topoHandle->maxNum; i++) {
        if (i == 0) {
            Status ret = AdapterSelectAllRecSingle(conn, ulDbId, pstCond, 0, &topoHandle->info[i]);
            if (ret != GMERR_OK && ret != VOS_ERRNO_DB_RECNOTEXIST) {  // topo查询没有满足条件数据返回0条，不报错
                return ret;
            }
            continue;
        }
        SimpRelSelectTopoResultT *result = &topoHandle->info[i - 1].result;
        for (; result->recIdx < DbListGetItemCnt(&result->recList); result->recIdx++) {
            SimpRelTopoRecItemT *item = (SimpRelTopoRecItemT *)DbListItem(&result->recList, result->recIdx);
            AdapterFillTopoIdxCondData(item, &topoHandle->info[i]);
            Status ret = AdapterSelectAllRecSingle(conn, ulDbId, NULL, 0, &topoHandle->info[i]);
            if (ret != GMERR_OK && ret != VOS_ERRNO_DB_RECNOTEXIST) {  // topo查询没有满足条件数据返回0条，不报错
                return ret;
            }
        }
    }
    return GMERR_OK;
}

Status AdapterV1CheckPath(DB_PATH_STRU *pstPath, uint16_t srcRelId)
{
    ADPT4V1_CHECK_OBJECT_NULL(pstPath, "pstPath is null pointer.");
    if (pstPath->edgeNum == 0 || pstPath->edgeNum > DB_PATH_EDGE_MAX_NUM) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDCONDITION,
            "Edge number of path(%" PRIu16 ") is zero or greater than the maximum limit(%" PRIu32 ").",
            pstPath->edgeNum, DB_PATH_EDGE_MAX_NUM);
        return VOS_ERRNO_DB_INVALIDCONDITION;
    }
    uint16_t target = srcRelId;
    for (uint32_t i = 0; i < pstPath->edgeNum; i++) {
        if (pstPath->edge[i].pstFldFilter == NULL) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NULLPTR, "Edge pstFldFilter is null");
            return VOS_ERRNO_DB_NULLPTR;
        }
        if (target != pstPath->edge[i].edgeInfo[0].relId) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDCONDITION, "Not a valid path");
            return VOS_ERRNO_DB_INVALIDCONDITION;
        }
        target = pstPath->edge[i].edgeInfo[1].relId;
    }
    return GMERR_OK;
}

Status AdapterCheckBeginSelectCond(DB_COND_STRU *cond, bool isTpc)
{
    const uint32_t maxCondCnt = isTpc ? TPC_COND_MAX : DB_COND_MAX;
    if (cond->usCondNum > maxCondCnt) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDCONDITION,
            "Number of input conditions(%" PRIu16 ") is greater than the maximum limit(%" PRIu32 ").", cond->usCondNum,
            maxCondCnt);
        return VOS_ERRNO_DB_INVALIDCONDITION;
    }
    for (uint32_t i = 0; i < cond->usCondNum; i++) {
        if (cond->aCond[i].enOp >= DB_OP_MAX_PREFIX12 && cond->aCond[i].enOp <= DB_OP_MIN_LARGER_EQUAL) {
            DB_LOG_AND_SET_LASERR(
                VOS_ERRNO_DB_NOTSUPPORT, "cond is %" PRIu32 " is %" PRIu32, i, (uint32_t)cond->aCond[i].enOp);
            return VOS_ERRNO_DB_NOTSUPPORT;
        } else if (cond->aCond[i].enOp == DB_OP_MAX_POSTFIX21) {
            DB_LOG_AND_SET_LASERR(
                VOS_ERRNO_DB_NOTSUPPORT, "cond is %" PRIu32 " is %" PRIu32, i, (uint32_t)cond->aCond[i].enOp);
            return VOS_ERRNO_DB_NOTSUPPORT;
        }
    }
    return GMERR_OK;
}

Status AdapterAllocTopoResult(uint16_t relId, SimpRelSelectTopoResultT *result, DB_REL_BUF_STRU *resultBuf)
{
    uint32_t recNum = DbListGetItemCnt(&result->recList);
    uint8_t *buf = NULL;
    if (recNum != 0) {
        buf = (uint8_t *)DbDynMemCtxAlloc(AdapterGetTopoRetMemctx(), result->projectLen * recNum);
        if (buf == NULL) {
            DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc topo result buf.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        uint32_t offset = 0;
        for (uint32_t i = 0; i < recNum; i++) {
            SimpRelTopoRecItemT *item = DbListItem(&result->recList, i);
            if (result->projectLen == result->recLen) {
                (void)memcpy_s(buf + offset, result->projectLen, item->buf, result->projectLen);
                offset += result->projectLen;
            } else {
                for (uint32_t j = 0; j < result->projectInfoNum; j++) {
                    (void)memcpy_s(buf + offset, result->projectInfo[j].size, item->buf + result->projectInfo[j].offset,
                        result->projectInfo[j].size);
                    offset += result->projectInfo[j].size;
                }
            }
        }
    }
    resultBuf->relId = relId;
    resultBuf->ulRecNum = recNum;
    resultBuf->usRecLen = result->projectLen;
    resultBuf->pBuf = buf;
    return GMERR_OK;
}

void AdapterReleaseMultiBuf(DbMemCtxT *memCtx, DB_MUTIL_BUF_STRU *result)
{
    for (uint32_t i = 0; i < result->realRelNum; i++) {
        DbDynMemCtxFree(memCtx, result->dataBuf[i].pBuf);
    }
    DbDynMemCtxFree(memCtx, result);
}

Status AdapterAllocMultiBufByHandle(AdptTopoHandleInfoT *topoHandle, uint32_t idx, DB_MUTIL_BUF_STRU **pstBuff)
{
    uint32_t bufCount = (idx == 0) ? topoHandle->maxNum : 1;
    uint32_t allocSize = sizeof(DB_MUTIL_BUF_STRU) + sizeof(DB_REL_BUF_STRU) * bufCount;
    DB_MUTIL_BUF_STRU *buf = (DB_MUTIL_BUF_STRU *)DbDynMemCtxAlloc(AdapterGetTopoRetMemctx(), allocSize);
    if (buf == NULL) {
        DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc topo result buf.");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    memset_s(buf, allocSize, 0, allocSize);
    buf->dataBuf = (DB_REL_BUF_STRU *)(buf + 1);
    buf->realRelNum = bufCount;
    if (idx == 0) {
        for (uint32_t i = 0; i < topoHandle->maxNum; i++) {
            Status ret =
                AdapterAllocTopoResult(topoHandle->info[i].tableId, &topoHandle->info[i].result, &buf->dataBuf[i]);
            if (ret != GMERR_OK) {
                AdapterReleaseMultiBuf(AdapterGetTopoRetMemctx(), buf);
                return ret;
            }
        }
    } else {
        Status ret =
            AdapterAllocTopoResult(topoHandle->info[idx].tableId, &topoHandle->info[idx].result, &buf->dataBuf[0]);
        if (ret != GMERR_OK) {
            AdapterReleaseMultiBuf(AdapterGetTopoRetMemctx(), buf);
            return ret;
        }
    }
    *pstBuff = buf;
    return GMERR_OK;
}

Status AdapterSetMultiBuf(
    AdptTopoHandleInfoT *topoHandle, uint32_t count, uint32_t handleId, DB_MUTIL_BUF_STRU **pstBuff)
{
    AdptV1TopoResultMgrT *topoResultPool = &g_adptV1Instance.topoResultMgr;
    DbRWLatchW(&topoResultPool->latch);
    uint32_t idx = 0;
    AdptTopoResultT *result = NULL;
    for (; idx < DbListGetItemCnt(&topoResultPool->topoResultList); idx++) {
        AdptTopoResultT *tmp = (AdptTopoResultT *)DbListItem(&topoResultPool->topoResultList, idx);
        if (tmp->result == NULL) {
            result = tmp;
            break;
        }
    }
    Status ret = AdapterAllocMultiBufByHandle(topoHandle, count, pstBuff);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(&topoResultPool->latch);
        return ret;
    }
    if (result == NULL) {
        result = (AdptTopoResultT *)DbNewListItem(&topoResultPool->topoResultList);
        if (result == NULL) {
            DbRWUnlatchW(&topoResultPool->latch);
            DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc topo result");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
    }
    result->handleId = handleId;
    result->result = *pstBuff;
    (*pstBuff)->memId = idx;
    DbRWUnlatchW(&topoResultPool->latch);
    return GMERR_OK;
}

void AdapterFreeMultiBuf(uint32_t memId)
{
    AdptV1TopoResultMgrT *topoResultPool = &g_adptV1Instance.topoResultMgr;
    DbRWLatchW(&topoResultPool->latch);
    uint32_t count = DbListGetItemCnt(&topoResultPool->topoResultList);
    if (memId >= count) {
        DbRWUnlatchW(&topoResultPool->latch);
        return;
    }
    AdptTopoResultT *result = (AdptTopoResultT *)DbListItem(&topoResultPool->topoResultList, memId);
    if (result == NULL) {
        DbRWUnlatchW(&topoResultPool->latch);
        return;
    }
    AdapterReleaseMultiBuf(AdapterGetTopoRetMemctx(), result->result);
    result->result = NULL;
    result->handleId = DB_MAX_UINT32;
    DbRWUnlatchW(&topoResultPool->latch);
    return;
}

void AdapterFreeMultiBufByHandleId(uint32_t handleId)
{
    AdptV1TopoResultMgrT *topoResultPool = &g_adptV1Instance.topoResultMgr;
    DbRWLatchW(&topoResultPool->latch);
    for (uint32_t idx = 0; idx < DbListGetItemCnt(&topoResultPool->topoResultList); idx++) {
        AdptTopoResultT *result = (AdptTopoResultT *)DbListItem(&topoResultPool->topoResultList, idx);
        if (result->handleId == handleId) {
            AdapterReleaseMultiBuf(AdapterGetTopoRetMemctx(), result->result);
            result->result = NULL;
            result->handleId = DB_MAX_UINT32;
        }
    }
    DbRWUnlatchW(&topoResultPool->latch);
    return;
}

/* This API selects all the records matching the condition and provide the */
/* output in the given sorted order */
DB_ERR_CODE TPC_SelectAllRecByOrder(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_SORT_STRU *pstSort,
    DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_DSBUF_STRU *pstDsBuf)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstDsBuf, "pstDsBuf is null pointer for select all record by order.");

    DB_BUF_STRU pstBufData;
    AdapterInitDbBuff(&pstBufData, pstDsBuf);
    ret = AdapterSelectAllRecByOrderEx(ulCdbId, ulDbId, usRelNo, pstSort, pstCond, pstFldFilter, &pstBufData, true);
    if (ret != GMERR_OK) {
        if (ret == VOS_ERRNO_DB_RECNOTEXIST) {
            pstDsBuf->usRecNum = 0;
            goto EXIT;
        }
        return ConvertErrorCode(ret);
    }

    AdapterSetDsBufByDbBuf(&pstBufData, pstDsBuf);
EXIT:
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (GetSysviewPerfStatState()) {
            RecordPerfStatInfo(TPC_API_SELECT_ALL_RECORD_BY_ORDER, startTime, endTime);
        }
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_SELECT_ORDER)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Select by order perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usRelNo,
                .pstCond = pstCond,
                .pstFldFilter = pstFldFilter,
                .pstSort = pstSort,
                .recNum = pstDsBuf->usRecNum,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_SELECT_ORDER, &detail);
        }
    }
    return ConvertErrorCode(ret);
}

/* This API selects all the records matching the condition and provide the */
/* output in the given sorted order */
DB_ERR_CODE TPC_SelectAllRecByOrderEx(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_SORT_STRU *pstSort,
    DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_BUF_STRU *pstBufData)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstBufData, "pstBufData is null pointer for select all record by order.");
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    ret = AdapterSelectAllRecByOrderEx(ulCdbId, ulDbId, usRelNo, pstSort, pstCond, pstFldFilter, pstBufData, true);
    if (ret != GMERR_OK && ret != VOS_ERRNO_DB_RECNOTEXIST) {
        return ConvertErrorCode(ret);
    }
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_SELECT_ORDER)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Select by order EX perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usRelNo,
                .pstCond = pstCond,
                .pstFldFilter = pstFldFilter,
                .pstSort = pstSort,
                .recNum = pstBufData->ulRecNum,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_SELECT_ORDER, &detail);
        }
    }
    return ConvertErrorCode(ret);
}

/* This function can be used to check for the existence of the record in table. */
DB_ERR_CODE TPC_CountMatchingRecs(
    VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond, VOS_UINT32 *pulRecNum)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstCond, "pstCond is null for count matched records.");
    ADPT4V1_CHECK_OBJECT_NULL(pulRecNum, "pulRecNum is null for count matched records.");
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    AdptV1ItemRefT conn = {0};
    uint32_t actDbId;
    ret = AdapterV1GetCdbConn(ulCdbId, ulDbId, DB_INVALID_UINT16, &conn, &actDbId);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    *pulRecNum = 0;
    DB_SORT_STRU sort = {.ucSortNum = 0};
    SimpRelScanCtxT scanCtx = {.dbId = actDbId,
        .relId = usRelNo,
        .cond = pstCond,
        .fldFilter = NULL,
        .sort = &sort,
        .dataStru = NULL,
        .matchedCnt = pulRecNum};
    ret = GmeSimpleRelSelectAllRec(conn.item, &scanCtx, true);
    AdapterV1QuickFreeCdbConn(ulCdbId, &conn);
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_COUNT)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Count match perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usRelNo,
                .pstCond = pstCond,
                .pstFldFilter = NULL,
                .pstSort = NULL,
                .recNum = *pulRecNum,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_COUNT, &detail);
        }
    }
    return ConvertErrorCode(ret);
}

/* This function can be used to get the first record matching the condition given by the user. */
DB_ERR_CODE TPC_SelectFirstRec(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_DSBUF_STRU *pstDsBuf)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstDsBuf, "pstDsBuf is null for select first record.");

    DB_BUF_STRU pstBufData;
    AdapterInitDbBuff(&pstBufData, pstDsBuf);
    // 设置只查询一条
    pstBufData.ulRecNum = 1;
    DB_SORT_STRU pstSort = {.ucSortNum = 0};
    ret = AdapterSelectAllRecByOrderEx(ulCdbId, ulDbId, usRelNo, &pstSort, pstCond, pstFldFilter, &pstBufData, true);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }

    AdapterSetDsBufByDbBuf(&pstBufData, pstDsBuf);
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (GetSysviewPerfStatState()) {
            RecordPerfStatInfo(TPC_API_SELECT_FIRST_RECORD, startTime, endTime);
        }
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_SELECT_FIRST)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Select first perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usRelNo,
                .pstCond = pstCond,
                .pstFldFilter = pstFldFilter,
                .pstSort = NULL,
                .recNum = 1,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_SELECT_FIRST, &detail);
        }
    }
    return GMERR_OK;
}

/* This function checks for the record existence in relation based on given condition */
DB_ERR_CODE TPC_RecordExist(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstCond, "pstCond is null for check record exist.");
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    AdptV1ItemRefT conn = {0};
    uint32_t actDbId;
    ret = AdapterV1GetCdbConn(ulCdbId, ulDbId, DB_INVALID_UINT16, &conn, &actDbId);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }

    DB_SORT_STRU sort = {.ucSortNum = 0};
    SimpRelScanCtxT scanCtx = {
        .dbId = actDbId, .relId = usRelNo, .cond = pstCond, .sort = &sort, .isCheckRecExist = true};
    ret = GmeSimpleRelSelectAllRec(conn.item, &scanCtx, true);
    AdapterV1QuickFreeCdbConn(ulCdbId, &conn);
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_RECORD_EXSIT)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Record exist perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usRelNo,
                .pstCond = pstCond,
                .pstFldFilter = NULL,
                .pstSort = NULL,
                .recNum = 0,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_RECORD_EXSIT, &detail);
        }
    }
    return ConvertErrorCode(ret);
}

void AdapterSetDiffResult(VOS_UINT32 *pulInsRecNum, VOS_VOID **ppInsRecList, VOS_UINT32 *pulDelRecNum,
    VOS_VOID **ppDelRecList, VOS_UINT32 *pulUpdRecNum, VOS_VOID **ppUpdRecList, SimpRelSelectDiffResultT *diffCtx)
{
    if (diffCtx != NULL) {
        *pulInsRecNum = diffCtx->pulInsRecNum;
        *pulDelRecNum = diffCtx->pulDelRecNum;
        *pulUpdRecNum = diffCtx->pulUpdRecNum;
        *ppInsRecList = diffCtx->ppInsRecList;
        *ppDelRecList = diffCtx->ppDelRecList;
        *ppUpdRecList = diffCtx->ppUpdRecList;
    } else {
        *pulInsRecNum = 0;
        *pulDelRecNum = 0;
        *pulUpdRecNum = 0;
        *ppInsRecList = NULL;
        *ppDelRecList = NULL;
        *ppUpdRecList = NULL;
    }
}

#ifdef ENABLE_VISTLIB
DB_ERR_CODE TPC_SelectCdbDiffData(VOS_UINT32 ulCdbId, VOS_UINT16 usRelNo, VOS_UINT32 *pulInsRecNum,
    VOS_VOID **ppInsRecList, VOS_UINT32 *pulDelRecNum, VOS_VOID **ppDelRecList, VOS_UINT32 *pulUpdRecNum,
    VOS_VOID **ppUpdRecList)
{
    DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NOTSUPPORT, "TPC_SelectCdbDiffData CDB %" PRIu32 " is not support.", ulCdbId);
    return VOS_ERRNO_DB_NOTSUPPORT;
}
#else
DB_ERR_CODE TPC_SelectCdbDiffData(VOS_UINT32 ulCdbId, VOS_UINT16 usRelNo, VOS_UINT32 *pulInsRecNum,
    VOS_VOID **ppInsRecList, VOS_UINT32 *pulDelRecNum, VOS_VOID **ppDelRecList, VOS_UINT32 *pulUpdRecNum,
    VOS_VOID **ppUpdRecList)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    ADPT4V1_CHECK_OBJECT_NULL(pulInsRecNum, "pulInsRecNum is null for selecting diff.");
    ADPT4V1_CHECK_OBJECT_NULL(ppInsRecList, "ppInsRecList is null for selecting diff.");
    ADPT4V1_CHECK_OBJECT_NULL(pulDelRecNum, "pulDelRecNum is null for selecting diff.");
    ADPT4V1_CHECK_OBJECT_NULL(ppDelRecList, "ppDelRecList is null for selecting diff.");
    ADPT4V1_CHECK_OBJECT_NULL(pulUpdRecNum, "pulUpdRecNum is null for selecting diff.");
    ADPT4V1_CHECK_OBJECT_NULL(ppUpdRecList, "ppUpdRecList is null for selecting diff.");

    if (ulCdbId == TPC_GLOBAL_CDB) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_TPC_INVALID_CDBID, "use global cdb to fetch diff.");
        return VOS_ERRNO_DB_TPC_INVALID_CDBID;
    }

    AdptV1CdbT *cdbBlock = NULL;
    ret = AdapterV1GetCdbBlock(ulCdbId, &cdbBlock);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    DB_ASSERT(cdbBlock != NULL);
    DbRWLatchW(&cdbBlock->latch);
    if (!AdapterV1IsCdbValid(cdbBlock)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_TPC_INVALID_CDBID, "CDB %" PRIu32 " is not valid.", ulCdbId);
        DbRWUnlatchW(&cdbBlock->latch);
        return VOS_ERRNO_DB_TPC_INVALID_CDBID;
    }
    SimpRelSelectDiffResultT diffCtx;
    AdapterDiffResutlInit(&diffCtx);
    DB_COND_STRU cond = {.usCondNum = 0};
    DB_SORT_STRU sort = {.ucSortNum = 0};
    SimpRelScanCtxT scanCtx = AdapterGetDiffScanCtx(cdbBlock->dbId, usRelNo, &cond, &sort, &diffCtx);
    ret = GmeSimpleRelSelectDiffData(cdbBlock->conn, &scanCtx, false);
    if (ret != GMERR_OK) {
        AdapterSetDiffResult(pulInsRecNum, ppInsRecList, pulDelRecNum, ppDelRecList, pulUpdRecNum, ppUpdRecList, NULL);
        AdapterDiffBufFree(diffCtx.ppInsRecList, diffCtx.ppDelRecList, diffCtx.ppUpdRecList);
        DbRWUnlatchW(&cdbBlock->latch);
        return ConvertErrorCode(ret);
    }
    AdapterSetDiffResult(pulInsRecNum, ppInsRecList, pulDelRecNum, ppDelRecList, pulUpdRecNum, ppUpdRecList, &diffCtx);
    DbRWUnlatchW(&cdbBlock->latch);
    if (GetSysviewPerfStatState()) {
        RecordPerfStatInfo(TPC_API_SELECT_CDB_DIFF_DATA, startTime, DbRdtsc());
    }
    return GMERR_OK;
}
#endif

VOS_VOID TPC_FreeDiffData(VOS_VOID *ppInsRecList, VOS_VOID *ppDelRecList, VOS_VOID *ppUpdRecList)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return;
    }

    AdapterDiffBufFree(ppInsRecList, ppDelRecList, ppUpdRecList);
    return;
}

#define SIMREL_REC_EXTEND_LEN 8

static void AdapterFreeTopoResult(DbMemCtxT *memCtx, SimpRelSelectTopoResultT *result)
{
    for (uint32_t i = 0; i < DbListGetItemCnt(&result->recList); i++) {
        SimpRelTopoRecItemT *item = (SimpRelTopoRecItemT *)DbListItem(&result->recList, i);
        if (item->buf != NULL) {
            DbDynMemCtxFree(memCtx, item->buf);
        }
    }
    if (result->projectInfo != NULL) {
        DbDynMemCtxFree(memCtx, result->projectInfo);
    }
    DbDestroyList(&result->recList);
    DbCreateListWithExtendSize(&result->recList, sizeof(SimpRelTopoRecItemT), SIMREL_REC_EXTEND_LEN, memCtx);
}

inline static void AdapterResetTopoResult(DbMemCtxT *memCtx, SimpRelSelectTopoResultT *result)
{
    for (uint32_t i = 0; i < DbListGetItemCnt(&result->recList); i++) {
        SimpRelTopoRecItemT *item = (SimpRelTopoRecItemT *)DbListItem(&result->recList, i);
        if (item->buf != NULL) {
            DbDynMemCtxFree(memCtx, item->buf);
            item->buf = NULL;
        }
    }
    DbClearList(&result->recList);
    result->recIdx = 0;
    result->fetchNum = 0;
}

ALWAYS_INLINE static void AdapterFreeTopoHandleInfo(DbMemCtxT *memCtx, AdptTopoHandleInfoItemT *info)
{
    if (info->handleCond.filterConds != NULL) {
        DbDynMemCtxFree(memCtx, info->handleCond.filterConds);
        info->handleCond.filterConds = NULL;
    }
    if (info->handleCond.allCondValueBuf != NULL) {
        DbDynMemCtxFree(memCtx, info->handleCond.allCondValueBuf);
        info->handleCond.allCondValueBuf = NULL;
    }
    info->handleCond.idxConds = NULL;
    info->handleCond.resumeConds = NULL;
    if (info->preIdxConds != NULL) {
        DbDynMemCtxFree(memCtx, info->preIdxConds);
        info->preIdxConds = NULL;
    }
    AdapterFreeTopoResult(AdapterGetHdlMemctx(), &info->result);
}

inline static void AdapterFreeTopoHandle(DbMemCtxT *memCtx, AdptTopoHandleInfoT *topoHandle)
{
    if (topoHandle == NULL) {
        return;
    }
    for (uint32_t i = 0; i < topoHandle->maxNum; i++) {
        AdapterFreeTopoHandleInfo(memCtx, &topoHandle->info[i]);
    }
    DbDynMemCtxFree(memCtx, topoHandle->info);
    DbDynMemCtxFree(memCtx, topoHandle);
}

void AdapterFreeHandleInfo(AdptHandleInfoT *handleInfo)
{
    handleInfo->state = HANDLE_FREE;
    handleInfo->conn = (AdptV1ItemRefT){0};
    handleInfo->pstSort.ucSortNum = 0;
    handleInfo->dbId = DB_INVALID_UINT32;
    handleInfo->cdbId = DB_INVALID_UINT16;
    handleInfo->tableId = DB_INVALID_UINT16;
    AdapterSetDefaultSelectHdlBeginAddr(&handleInfo->beginAddr);
    AdapterSetInvalidDataInfo(&handleInfo->resumeDataInfo);
    // 释放条件
    AdapterFreeHandleCond(AdapterGetHdlMemctx(), handleInfo->handleCond);
    if (handleInfo->topoHandle != NULL) {
        AdapterFreeTopoHandle(AdapterGetHdlMemctx(), handleInfo->topoHandle);
        handleInfo->topoHandle = NULL;
    }
}

DB_ERR_CODE TPC_CloseAllHandles(VOS_UINT32 ulDbId)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    AdptV1ItemRefT conn = {0};
    ret = AdapterV1AllocConn(&conn);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }

    ret = GmeSimpleRelCheckDbIsValid(conn.item, ulDbId, true, true);
    (void)AdapterV1FreeConn(&conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    DbRWLatchW(&handlePool->latch);
    uint32_t closeCnt = 0;
    for (uint32_t i = 0; i < DbGaListGetCount(&handlePool->handleList); i++) {
        AdptHandleInfoT *hdlInfo = DbGaListGet(&handlePool->handleList, i);
        DbSpinLock(&hdlInfo->lock);
        if (hdlInfo->dbId == ulDbId && hdlInfo->state != HANDLE_FREE) {
            DB_LOG_INFO("Free handle %" PRIu32 " succ, tableId is %" PRIu32 ", cdbId is %" PRIu32 ".", i + 1,
                hdlInfo->tableId, hdlInfo->cdbId);
            AdapterFreeHandleInfo(hdlInfo);
            ++closeCnt;
        }
        DbSpinUnlock(&hdlInfo->lock);
    }
    DB_LOG_INFO(
        "Close all handles finished, DB id: %" PRIu32 ", close handles total num: %" PRIu32 ".", ulDbId, closeCnt);
    DbRWUnlatchW(&handlePool->latch);
    return GMERR_OK;
}

DB_ERR_CODE TPC_ReleaseAllHandles(VOS_UINT32 ulCdbId)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }

    if (ulCdbId >= ADPT4V1_MAX_CDB_NUM) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_TPC_INVALID_CDBID, "cdbid %" PRIu32 " is not support", ulCdbId);
        return VOS_ERRNO_DB_TPC_INVALID_CDBID;
    }
    AdptV1CdbMgrT *cdbMgr = &g_adptV1Instance.cdbMgr;
    DbSpinLock(&cdbMgr->cdbMgrMutex);
    if (!cdbMgr->cdbBlocks[ulCdbId].isUsed) {
        DbSpinUnlock(&cdbMgr->cdbMgrMutex);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_TPC_INVALID_CDBID, "The given cdbId %" PRIu32 " is not in use.", ulCdbId);
        return VOS_ERRNO_DB_TPC_INVALID_CDBID;
    }

    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    DbRWLatchW(&handlePool->latch);
    for (uint32_t i = 0; i < DbGaListGetCount(&handlePool->handleList); i++) {
        AdptHandleInfoT *hdlInfo = DbGaListGet(&handlePool->handleList, i);
        DbSpinLock(&hdlInfo->lock);
        if (hdlInfo->cdbId == ulCdbId && hdlInfo->state != HANDLE_FREE) {
            AdapterFreeHandleInfo(hdlInfo);
        }
        DbSpinUnlock(&hdlInfo->lock);
    }
    DbRWUnlatchW(&handlePool->latch);
    DbSpinUnlock(&cdbMgr->cdbMgrMutex);
    return GMERR_OK;
}

bool AdapterIsCdbValid(VOS_UINT32 ulCdbId)
{
    if (ulCdbId == TPC_GLOBAL_CDB) {
        return true;
    }
    if (ulCdbId >= ADPT4V1_MAX_CDB_NUM) {
        return false;
    }
    DbRWLatchW(&g_adptV1Instance.cdbMgr.cdbBlocks[ulCdbId].latch);
    if (SECUREC_UNLIKELY(!AdapterV1IsCdbValid(&g_adptV1Instance.cdbMgr.cdbBlocks[ulCdbId]))) {
        DbRWUnlatchW(&g_adptV1Instance.cdbMgr.cdbBlocks[ulCdbId].latch);
        return false;
    }
    DbRWUnlatchW(&g_adptV1Instance.cdbMgr.cdbBlocks[ulCdbId].latch);
    return true;
}

DB_ERR_CODE TPC_EndSelect(VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    if (!AdapterIsCdbValid(ulCdbId)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_TPC_INVALID_CDBID, "CDB %" PRIu32 " is not in use.", ulCdbId);
        return VOS_ERRNO_DB_TPC_INVALID_CDBID;
    }
    if (hSelect == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle should not be zero");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    hSelect -= 1;  // handle计数从1开始
    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    DbRWLatchW(&handlePool->latch);
    AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, hSelect);
    if (handleInfo == NULL || handleInfo->cdbId != ulCdbId) {
        DbRWUnlatchW(&handlePool->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle cdbId not match or handleInfo is null");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    DbSpinLock(&(handleInfo->lock));
    if (handleInfo->state == HANDLE_FREE) {
        DbSpinUnlock(&(handleInfo->lock));
        DbRWUnlatchW(&handlePool->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle state not match when end topo");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    AdapterFreeHandleInfo(handleInfo);
    DbSpinUnlock(&(handleInfo->lock));
    DbRWUnlatchW(&handlePool->latch);
    return GMERR_OK;
}

Status AdapterCheckScanCtxAndInitHandleConds(
    GmeConnT *conn, SimpRelScanCtxT *scanCtx, DbMemCtxT *memCtx, SimpRelHandleCondT **handleCond, bool isTpc)
{
    Status ret = AdapterCheckBeginSelectCond(scanCtx->cond, isTpc);
    if (ret != GMERR_OK) {
        return ret;
    }

    AdapterSetIsTpcOp(conn, isTpc);
    ret = GmeSimpleRelCheckScanCtxAndInitHandleConds(conn, scanCtx, memCtx, handleCond, isTpc);
    if (ret != GMERR_OK) {
        return ret;
    }

    AdapterSetInvalidDataInfo(&(*handleCond)->lastDataInfo);
    return GMERR_OK;
}

Status AdapterAllocHandle(AdptHandleInfoT **handle, DB_SELHANDLE *index)
{
    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    for (uint32_t i = 0; i < DbGaListGetCount(&handlePool->handleList); i++) {
        AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, i);
        if (handleInfo != NULL && handleInfo->state == HANDLE_FREE) {
            *index = i;
            *handle = handleInfo;
            return GMERR_OK;
        }
    }
    if (DbGaListGetCount(&handlePool->handleList) == (VOS_NULL_WORD - 1)) {
        DB_LOG_AND_SET_LASERR(
            VOS_ERRNO_DB_NOHANDLEBLK, "maximum allowed handles (%" PRIu32 ") allocated", VOS_NULL_WORD - 1);
        return VOS_ERRNO_DB_NOHANDLEBLK;
    }
    AdptHandleInfoT *handleInfo = NULL;
    Status ret = DbGaListNew(&handlePool->handleList, sizeof(AdptHandleInfoT), (void **)&handleInfo);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "unable to alloc handle");
        return ret;
    }
    handleInfo->state = HANDLE_FREE;
    DbSpinInit(&handleInfo->lock);
    *handle = handleInfo;
    *index = handlePool->handleList.count - 1;
    return GMERR_OK;
}

void RecordBeginSelectPerfInfo(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usTblId, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_SORT_STRU *pstSort)
{
    if (IsSysviewPerfOn()) {
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_FETCH_RECORD)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Begin Select: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usTblId,
                .pstCond = pstCond,
                .pstFldFilter = pstFldFilter,
                .pstSort = pstSort,
                .recNum = DB_INVALID_UINT32,
                .costTime = 0};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_FETCH_RECORD, &detail);
        }
    }
}

DB_ERR_CODE TPC_BeginSelect(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usTblId, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstCond, "pstCond is NULL.");
    ADPT4V1_CHECK_OBJECT_NULL(phSelect, "phSelect is NULL.");
    ADPT4V1_CHECK_OBJECT_NULL(pstFldFilter, "pstFldfilter is NULL.");
    uint32_t actDbId;
    AdptV1ItemRefT conn = {0};
    ret = AdapterV1GetCdbConn(ulCdbId, ulDbId, DB_INVALID_UINT16, &conn, &actDbId);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    AdapterSetIsTpcOp(conn.item, true);
    SimpRelHandleCondT *handleCond = NULL;
    SimpRelScanCtxT scanCtx = {
        .dbId = actDbId, .relId = usTblId, .cond = pstCond, .fldFilter = pstFldFilter, .isFetchSelect = true};
    ret = AdapterCheckScanCtxAndInitHandleConds(conn.item, &scanCtx, AdapterGetHdlMemctx(), &handleCond, true);
    if (ret != GMERR_OK) {
        AdapterFreeHandleCond(AdapterGetHdlMemctx(), handleCond);
        AdapterV1FreeCdbConn(ulCdbId, &conn);
        return ConvertErrorCode(ret);
    }
    AdapterV1FreeCdbConn(ulCdbId, &conn);
    DbRWLatchW(&g_adptV1Instance.handleMgr.latch);
    AdptHandleInfoT *handleInfo;
    ret = AdapterAllocHandle(&handleInfo, phSelect);
    if (ret != GMERR_OK) {
        AdapterFreeHandleCond(AdapterGetHdlMemctx(), handleCond);
        DbRWUnlatchW(&g_adptV1Instance.handleMgr.latch);
        return ConvertErrorCode(ret);
    }
    DbSpinLock(&handleInfo->lock);
    AdapterSetCommonHandleInfoAfterAlloc(handleInfo, ulCdbId, actDbId, usTblId);
    handleInfo->fldFilter = *pstFldFilter;
    handleInfo->handleCond = handleCond;
    DbSpinUnlock(&handleInfo->lock);
    DbRWUnlatchW(&g_adptV1Instance.handleMgr.latch);
    RecordBeginSelectPerfInfo(ulCdbId, ulDbId, usTblId, pstCond, pstFldFilter, NULL);
    *phSelect += 1;  // handle计数从1开始
    return GMERR_OK;
}

Status AdapterV1CheckBeginIdxSelect(
    DB_SORT_STRU *pstSort, DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect)
{
    ADPT4V1_CHECK_OBJECT_NULL(pstSort, "pstSort is NULL.");
    ADPT4V1_CHECK_OBJECT_NULL(pstCond, "pstCond is NULL.");
    ADPT4V1_CHECK_OBJECT_NULL(phSelect, "phSelect is NULL.");
    ADPT4V1_CHECK_OBJECT_NULL(pstFldFilter, "pstFldfilter is NULL.");
    return GMERR_OK;
}

DB_ERR_CODE TPC_BeginIdxSelectByOrder(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usTblId, DB_SORT_STRU *pstSort,
    DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    if ((ret = AdapterV1CheckBeginIdxSelect(pstSort, pstCond, pstFldFilter, phSelect)) != GMERR_OK) {
        return ret;
    }
    uint32_t actDbId;
    AdptV1ItemRefT conn = {0};
    if ((ret = AdapterV1GetCdbConn(ulCdbId, ulDbId, DB_INVALID_UINT16, &conn, &actDbId)) != GMERR_OK) {
        return ConvertErrorCode(ret);
    }

    SimpRelHandleCondT *handleCond = NULL;
    SimpRelScanCtxT scanCtx = {.dbId = actDbId,
        .relId = usTblId,
        .cond = pstCond,
        .sort = pstSort,
        .fldFilter = pstFldFilter,
        .isFetchSelect = true};
    ret = AdapterCheckScanCtxAndInitHandleConds(conn.item, &scanCtx, AdapterGetHdlMemctx(), &handleCond, true);
    if (ret != GMERR_OK) {
        AdapterFreeHandleCond(AdapterGetHdlMemctx(), handleCond);
        AdapterV1FreeCdbConn(ulCdbId, &conn);
        return ConvertErrorCode(ret);
    }
    AdapterV1FreeCdbConn(ulCdbId, &conn);

    DbRWLatchW(&g_adptV1Instance.handleMgr.latch);
    AdptHandleInfoT *handleInfo;
    ret = AdapterAllocHandle(&handleInfo, phSelect);
    if (ret != GMERR_OK) {
        AdapterFreeHandleCond(AdapterGetHdlMemctx(), handleCond);
        DbRWUnlatchW(&g_adptV1Instance.handleMgr.latch);
        DB_LOG_ERROR(ret, "Alloc handle fail when begin selecting.");
        return ConvertErrorCode(ret);
    }
    DbSpinLock(&handleInfo->lock);
    AdapterSetCommonHandleInfoAfterAlloc(handleInfo, ulCdbId, actDbId, usTblId);
    handleInfo->pstSort = *pstSort;
    handleInfo->handleCond = handleCond;
    handleInfo->fldFilter = *pstFldFilter;
    DbSpinUnlock(&handleInfo->lock);
    DbRWUnlatchW(&g_adptV1Instance.handleMgr.latch);
    RecordBeginSelectPerfInfo(ulCdbId, ulDbId, usTblId, pstCond, pstFldFilter, pstSort);
    *phSelect += 1;  // handle计数从1开始
    return GMERR_OK;
}

ALWAYS_INLINE Status AdapterExecFetchSelect(AdptHandleInfoT *handleInfo, DB_BUF_STRU *pstBufData, bool isTPC)
{
    Status ret = GMERR_OK;
    SimpRelScanCtxT scanCtx = {.dbId = handleInfo->dbId,
        .relId = handleInfo->tableId,
        .cond = NULL,  // 查询条件已经分类放在handleCond中
        .fldFilter = &handleInfo->fldFilter,
        .sort = &handleInfo->pstSort,
        .dataStru = pstBufData,
        .isFetchSelect = true,
        .hpBeginAddr = &handleInfo->beginAddr,
        .handleCond = handleInfo->handleCond,
        .resumeDataInfo = &handleInfo->resumeDataInfo};
    ret = GmeSimpleRelSelectAllRec(handleInfo->conn.item, &scanCtx, isTPC);
    if (ret == GMERR_OK && pstBufData->ulRecNum == 0) {
        return VOS_ERRNO_DB_RECNOTEXIST;
    }
    return ret;
}

Status AdapterV1GetHandleDbId(AdptV1HdlMgrT *handlePool, VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect, uint32_t *dbId)
{
    DbRWLatchR(&handlePool->latch);
    AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, hSelect);
    if (handleInfo == NULL || handleInfo->cdbId != ulCdbId || handleInfo->state != HANDLE_USED) {
        // CDBID 与handle不一致，参数错误
        DbRWUnlatchR(&handlePool->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle cdbId not match or handle is null");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    *dbId = handleInfo->dbId;
    DbRWUnlatchR(&handlePool->latch);
    return GMERR_OK;
}

Status AdapterV1GetHandleInfo(AdptV1HdlMgrT *handlePool, DB_SELHANDLE hSelect, VOS_UINT32 *ulCdbId, uint32_t *dbId)
{
    DbRWLatchR(&handlePool->latch);
    AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, hSelect);
    if (handleInfo == NULL || handleInfo->state != HANDLE_USED) {
        // CDBID 与handle不一致，参数错误
        DbRWUnlatchR(&handlePool->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle cdbId not match or handle is null");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    *dbId = handleInfo->dbId;
    *ulCdbId = handleInfo->cdbId;
    DbRWUnlatchR(&handlePool->latch);
    return GMERR_OK;
}

Status AdapterV1CheckHandInfoAndGetConn(
    AdptV1HdlMgrT *handlePool, VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect, AdptHandleInfoT **outHandleInfo)
{
    if (!AdapterIsCdbValid(ulCdbId)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_TPC_INVALID_CDBID, "CDB %" PRIu32 " is not in use.", ulCdbId);
        return VOS_ERRNO_DB_TPC_INVALID_CDBID;
    }
    Status ret = GMERR_OK;
    if (hSelect == 0 || hSelect > DbGaListGetCount(&handlePool->handleList)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "Unabel to check handle id.");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }

    hSelect -= 1;  // handle计数从1开始
    uint32_t dbId;
    ret = AdapterV1GetHandleDbId(handlePool, ulCdbId, hSelect, &dbId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 现根据dbId获取连接，再给handle加锁，否则会死锁
    uint32_t actDbId;
    AdptV1ItemRefT conn;
    if ((ret = AdapterV1GetCdbConn(ulCdbId, dbId, DB_INVALID_UINT16, &conn, &actDbId)) != GMERR_OK) {
        return ret;
    }
    DbRWLatchR(&handlePool->latch);
    AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, hSelect);
    if (handleInfo == NULL || handleInfo->cdbId != ulCdbId || handleInfo->state != HANDLE_USED ||
        handleInfo->dbId != dbId) {
        // CDBID 与handle不一致，参数错误
        DbRWUnlatchR(&handlePool->latch);
        AdapterV1FreeCdbConn(ulCdbId, &conn);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle cdbId not match or handle is null");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    DbSpinLock(&(handleInfo->lock));
    handleInfo->conn = conn;
    DbRWUnlatchR(&handlePool->latch);

    *outHandleInfo = handleInfo;
    return GMERR_OK;
}

ALWAYS_INLINE static void RecordFetchPerf(
    VOS_UINT32 ulCdbId, uint64_t startTime, AdptHandleInfoT *handleInfo, DB_DSBUF_STRU *pstBuff)
{
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_FETCH_RECORD)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Fetch perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = handleInfo->dbId,
                .usRelNo = handleInfo->tableId,
                .pstCond = NULL,
                .pstFldFilter = NULL,
                .pstSort = NULL,
                .recNum = pstBuff->usRecNum,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_FETCH_RECORD, &detail);
        }
    }
}

DB_ERR_CODE TPC_FetchSelectRec(VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect, DB_DSBUF_STRU *pstBuff)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }

    if ((ret = AdapterCheckDsbuf(pstBuff)) != GMERR_OK) {
        return ConvertErrorCode(ret);
    }

    if (!AdapterIsCdbValid(ulCdbId)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_TPC_INVALID_CDBID, "CDB %" PRIu32 " is not in use.", ulCdbId);
        return VOS_ERRNO_DB_TPC_INVALID_CDBID;
    }

    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    AdptHandleInfoT *handleInfo = NULL;
    ret = AdapterV1CheckHandInfoAndGetConn(handlePool, ulCdbId, hSelect, &handleInfo);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }

    DB_BUF_STRU pstBufData;
    AdapterInitDbBuff(&pstBufData, pstBuff);
    ret = AdapterExecFetchSelect(handleInfo, &pstBufData, true);
    AdapterV1QuickFreeCdbConn(ulCdbId, &handleInfo->conn);
    if (ret != GMERR_OK) {
        if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
            pstBuff->usRecNum = 0;
            DB_LOG_ERROR(ret, "Fetch record worthless when execute select.");
            goto EXIT;
        }
        DbSpinUnlock(&(handleInfo->lock));  // AdapterV1CheckHandInfoAndGetConn内部加锁释放
        return ConvertErrorCode(ret);
    }
    AdapterSetDsBufByDbBuf(&pstBufData, pstBuff);
EXIT:
    RecordFetchPerf(ulCdbId, startTime, handleInfo, pstBuff);
    DbSpinUnlock(&(handleInfo->lock));  // AdapterV1CheckHandInfoAndGetConn内部加锁释放
    return ConvertErrorCode(ret);
}

void RecordSelectByPathPerf(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, uint64_t startTime)
{
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_SELECT_TOPO)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Select all by topo perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = ulDbId,
                .usRelNo = usRelNo,
                .pstCond = pstCond,
                .pstFldFilter = pstFldFilter,
                .pstSort = NULL,
                .recNum = DB_INVALID_UINT32,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_SELECT_TOPO, &detail);
        }
    }
}

DB_ERR_CODE TPC_SelectAllRecByPath(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_PATH_STRU *pstPath, DB_MUTIL_BUF_STRU **pstBuff)
{
#ifdef ENABLE_VISTLIB
    DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NOTSUPPORT, "SelectAllRecByPath CDB %" PRIu32 " is not support.", ulCdbId);
    return VOS_ERRNO_DB_NOTSUPPORT;
#else
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ret;
    }

    ADPT4V1_CHECK_OBJECT_NULL(pstCond, "pstCond is null pointer.");
    ADPT4V1_CHECK_OBJECT_NULL(pstFldFilter, "pstFldFilter is null pointer.");
    ADPT4V1_CHECK_OBJECT_NULL(pstBuff, "pstBuff is null pointer for select all record.");
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    ret = AdapterV1CheckPath(pstPath, usRelNo);
    if (ret != GMERR_OK) {
        return ret;
    }
    AdptV1ItemRefT conn = {0};
    uint32_t actDbId;
    ret = AdapterV1GetCdbConn(ulCdbId, ulDbId, DB_INVALID_UINT16, &conn, &actDbId);
    if (ret != GMERR_OK) {
        return ret;
    }
    AdapterSetIsTpcOp(conn.item, true);
    AdptTopoHandleInfoT *topoHandle = NULL;
    SimpRelTopoScanCtxT scanCtx = {
        .dbId = actDbId, .relId = usRelNo, .cond = pstCond, .fldFilter = pstFldFilter, .pstPath = pstPath};
    ret = GmeSimpleInitTopoHandleConds(conn.item, &scanCtx, AdapterGetHdlMemctx(), &topoHandle);
    if (ret != GMERR_OK) {
        AdapterV1FreeCdbConn(ulCdbId, &conn);
        return ret;
    }

    ret = AdapterSelectAllRecByTopo(conn.item, ulDbId, pstCond, topoHandle);
    if (ret != GMERR_OK) {
        AdapterFreeTopoHandle(AdapterGetHdlMemctx(), topoHandle);
        AdapterV1FreeCdbConn(ulCdbId, &conn);
        return ConvertErrorCode(ret);
    }

    ret = AdapterSetMultiBuf(topoHandle, 0, DB_MAX_UINT32, pstBuff);
    AdapterFreeTopoHandle(AdapterGetHdlMemctx(), topoHandle);
    AdapterV1FreeCdbConn(ulCdbId, &conn);
    RecordSelectByPathPerf(ulCdbId, ulDbId, usRelNo, pstCond, pstFldFilter, startTime);
    return ConvertErrorCode(ret);
#endif
}

Status AdapterV1BeginSelectCheck(DB_COND_STRU *pstCond, DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ret;
    }
    ADPT4V1_CHECK_OBJECT_NULL(pstCond, "pstCond is null pointer.");
    ADPT4V1_CHECK_OBJECT_NULL(pstFldFilter, "pstFldFilter is null pointer.");
    ADPT4V1_CHECK_OBJECT_NULL(phSelect, "pstBuff is null pointer for select all record.");
    ret = AdapterCheckBeginSelectCond(pstCond, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
#ifdef ENABLE_VISTLIB
DB_ERR_CODE TPC_BeginSelectByTopo(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usTblId, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect)
{
    DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NOTSUPPORT, "TPC_BeginSelectByTopo CDB %" PRIu32 " is not support.", ulCdbId);
    return VOS_ERRNO_DB_NOTSUPPORT;
}
#else
DB_ERR_CODE TPC_BeginSelectByTopo(VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usTblId, DB_COND_STRU *pstCond,
    DB_FIELDFILTER_STRU *pstFldFilter, DB_SELHANDLE *phSelect)
{
    Status ret = AdapterV1BeginSelectCheck(pstCond, pstFldFilter, phSelect);
    if (ret != GMERR_OK) {
        return ret;
    }
    AdptV1ItemRefT conn = {0};
    uint32_t actDbId;
    if ((ret = AdapterV1GetCdbConn(ulCdbId, ulDbId, DB_INVALID_UINT16, &conn, &actDbId)) != GMERR_OK) {
        return ret;
    }
    AdapterSetIsTpcOp(conn.item, true);
    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    DbRWLatchW(&handlePool->latch);
    AdptTopoHandleInfoT *topoHandle = NULL;
    DB_PATH_STRU pstPath;
    pstPath.edgeNum = 0;
    SimpRelTopoScanCtxT scanCtx = {
        .dbId = actDbId, .relId = usTblId, .cond = pstCond, .fldFilter = pstFldFilter, .pstPath = &pstPath};
    ret = GmeSimpleInitTopoHandleConds(conn.item, &scanCtx, AdapterGetHdlMemctx(), &topoHandle);
    AdapterV1FreeCdbConn(ulCdbId, &conn);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(&handlePool->latch);
        return ret;
    }
    AdptHandleInfoT *handleInfo;
    if ((ret = AdapterAllocHandle(&handleInfo, phSelect)) != GMERR_OK) {
        AdapterFreeTopoHandle(AdapterGetHdlMemctx(), topoHandle);
        DbRWUnlatchW(&handlePool->latch);
        DB_LOG_ERROR(ret, "Alloc handle fail when begin selecting.");
        return ConvertErrorCode(ret);
    }
    DbSpinLock(&handleInfo->lock);
    handleInfo->state = HANDLE_USED;
    handleInfo->conn = (AdptV1ItemRefT){0};
    handleInfo->cdbId = ulCdbId;
    handleInfo->dbId = ulDbId;
    handleInfo->tableId = usTblId;
    handleInfo->fldFilter = *pstFldFilter;
    handleInfo->handleCond = NULL;
    (void)memset_s(&handleInfo->pstSort, sizeof(DB_SORT_STRU), 0x00, sizeof(DB_SORT_STRU));
    handleInfo->topoHandle = topoHandle;
    AdapterSetInvalidDataInfo(&handleInfo->resumeDataInfo);
    AdapterSetDefaultSelectHdlBeginAddr(&handleInfo->beginAddr);
    DbSpinUnlock(&handleInfo->lock);
    DbRWUnlatchW(&handlePool->latch);
    RecordBeginSelectPerfInfo(ulCdbId, ulDbId, usTblId, pstCond, pstFldFilter, NULL);
    *phSelect += 1;  // handle计数从1开始
    return GMERR_OK;
}

static Status AdapterExecFetchTopoRec(AdptHandleInfoT *handleInfo, uint32_t fetchNum)
{
    Status ret = GMERR_OK;
    AdapterResetTopoResult(
        AdapterGetHdlMemctx(), &handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx].result);
    if (handleInfo->topoHandle->currentIdx == 0) {
        ret = AdapterSelectAllRecSingle(handleInfo->conn.item, handleInfo->dbId, NULL, fetchNum,
            &handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx]);
        return ret == VOS_ERRNO_DB_RECNOTEXIST ? GMERR_OK : ret;  // topo查询没有满足条件数据返回0条，不报错
    }
    SimpRelSelectTopoResultT *result = &handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx - 1].result;
    for (; result->recIdx < DbListGetItemCnt(&result->recList);) {
        SimpRelTopoRecItemT *item = (SimpRelTopoRecItemT *)DbListItem(&result->recList, result->recIdx);
        if (result->isScanFinish) {
            AdapterFillTopoIdxCondData(item, &handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx]);
            result->isScanFinish = false;
        }
        ret = AdapterSelectAllRecSingle(handleInfo->conn.item, handleInfo->dbId, NULL, fetchNum,
            &handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx]);
        if (ret != GMERR_OK && ret != VOS_ERRNO_DB_RECNOTEXIST) {  // topo查询没有满足条件数据返回0条，不报错
            return ret;
        }
        if (handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx].result.fetchNum < fetchNum) {
            result->isScanFinish = true;
            result->recIdx++;
        } else {
            break;
        }
    }
    return GMERR_OK;
}
#endif

void RecordFetchByTopoPerf(VOS_UINT32 ulCdbId, AdptHandleInfoT *handleInfo, uint64_t startTime)
{
    if (IsSysviewPerfOn()) {
        uint64_t endTime = DbRdtsc();
        if (IsSysviewPerfTraceTypeMatch(DB_PERF_TRACE_TYPE_FETCH_TOPO)) {
            SysviewPerfTraceInfo detail = {.infoDesc = "Fetch by topo perf: ",
                .dbName = "NULL",
                .cdbId = ulCdbId,
                .ulDbId = handleInfo->dbId,
                .usRelNo = handleInfo->tableId,
                .pstCond = NULL,
                .pstFldFilter = NULL,
                .pstSort = NULL,
                .recNum = DB_INVALID_UINT32,
                .costTime = AdapterGetCostTime(startTime, endTime)};
            RecordPerfTraceLog(DB_PERF_TRACE_TYPE_FETCH_TOPO, &detail);
        }
    }
}

DB_ERR_CODE AdapterV1FetchSelectTopoCheckPara(DB_SELHANDLE hSelect, VOS_UINT16 fetchCount, DB_MUTIL_BUF_STRU **pstBuff)
{
    ADPT4V1_CHECK_OBJECT_NULL(pstBuff, "pstBuff is null pointer for select all record.");
    if (hSelect == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle should not be zero");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    if (fetchCount == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDPARA, "fetchCount should not be zero");
        return VOS_ERRNO_DB_INVALIDPARA;
    }
    return GMERR_OK;
}

#ifdef ENABLE_VISTLIB
DB_ERR_CODE TPC_FetchSelectTopoRec(
    VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect, VOS_UINT16 fetchCount, DB_MUTIL_BUF_STRU **pstBuff)
{
    DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NOTSUPPORT, "TPC_FetchSelectTopoRec CDB %" PRIu32 " is not support.", ulCdbId);
    return VOS_ERRNO_DB_NOTSUPPORT;
}
#else
DB_ERR_CODE TPC_FetchSelectTopoRec(
    VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect, VOS_UINT16 fetchCount, DB_MUTIL_BUF_STRU **pstBuff)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    uint64_t startTime = 0;
    if (IsSysviewPerfOn()) {
        startTime = DbRdtsc();
    }
    ret = AdapterV1FetchSelectTopoCheckPara(hSelect, fetchCount, pstBuff);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    hSelect -= 1;  // handle计数从1开始

    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    uint32_t dbId;
    ret = AdapterV1GetHandleDbId(handlePool, ulCdbId, hSelect, &dbId);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t actDbId;
    AdptV1ItemRefT conn;
    if ((ret = AdapterV1GetCdbConn(ulCdbId, dbId, DB_INVALID_UINT16, &conn, &actDbId)) != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    AdapterSetIsTpcOp(conn.item, true);
    DbRWLatchR(&handlePool->latch);
    AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, hSelect);
    if (handleInfo == NULL || handleInfo->cdbId != ulCdbId || handleInfo->state != HANDLE_USED ||
        handleInfo->dbId != dbId) {
        // CDBID 与handle不一致，参数错误
        DbRWUnlatchR(&handlePool->latch);
        AdapterV1FreeCdbConn(ulCdbId, &conn);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle cdbId not match or handle is null");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    DbSpinLock(&(handleInfo->lock));
    handleInfo->conn = conn;
    DbRWUnlatchR(&handlePool->latch);

    ret = AdapterExecFetchTopoRec(handleInfo, fetchCount);
    AdapterV1FreeCdbConn(ulCdbId, &handleInfo->conn);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&(handleInfo->lock));
        return ConvertErrorCode(ret);
    }
    ret = AdapterSetMultiBuf(handleInfo->topoHandle, handleInfo->topoHandle->currentIdx, hSelect, pstBuff);
    RecordFetchByTopoPerf(ulCdbId, handleInfo, startTime);
    DbSpinUnlock(&(handleInfo->lock));
    return ConvertErrorCode(ret);
}
#endif

DB_ERR_CODE TPC_MoveNextByEdge(DB_SELHANDLE hSelect, DB_EDGE_CON_STRU edge)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    ADPT4V1_CHECK_OBJECT_NULL(edge.pstFldFilter, "Edge pstFldFilter is null pointer.");
    if (hSelect == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle should not be zero");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    hSelect -= 1;  // handle计数从1开始

    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    uint32_t dbId;
    uint32_t ulCdbId;
    ret = AdapterV1GetHandleInfo(handlePool, hSelect, &ulCdbId, &dbId);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t actDbId;
    AdptV1ItemRefT conn;
    if ((ret = AdapterV1GetCdbConn(ulCdbId, dbId, DB_INVALID_UINT16, &conn, &actDbId)) != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    AdapterSetIsTpcOp(conn.item, true);
    DbRWLatchR(&handlePool->latch);
    AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, hSelect);

    if (handleInfo == NULL || handleInfo->cdbId != ulCdbId || handleInfo->state != HANDLE_USED ||
        handleInfo->dbId != dbId) {
        // CDBID 与handle不一致，参数错误
        DbRWUnlatchR(&handlePool->latch);
        AdapterV1FreeCdbConn(ulCdbId, &conn);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle cdbId not match or handle is null");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    DbSpinLock(&(handleInfo->lock));
    handleInfo->conn = conn;
    DbRWUnlatchR(&handlePool->latch);

    SimpRelTopoScanCtxT scanCtx = {.dbId = actDbId, .edge = &edge};
    ret = GmeSimpleTopoHandleAddEdge(handleInfo->conn.item, &scanCtx, AdapterGetHdlMemctx(), handleInfo->topoHandle);
    AdapterV1FreeCdbConn(handleInfo->cdbId, &handleInfo->conn);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&(handleInfo->lock));
        return ConvertErrorCode(ret);
    }

    DbSpinUnlock(&(handleInfo->lock));
    return GMERR_OK;
}

DB_ERR_CODE TPC_MoveBack(DB_SELHANDLE hSelect)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    if (hSelect == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle should not be zero");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    hSelect -= 1;  // handle计数从1开始

    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    DbRWLatchR(&handlePool->latch);
    AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, hSelect);

    if (handleInfo == NULL) {
        // CDBID 与handle不一致，参数错误
        DbRWUnlatchR(&handlePool->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle cdbId not match or handle is null");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }

    DbSpinLock(&(handleInfo->lock));
    DbRWUnlatchR(&handlePool->latch);
    if (handleInfo->state != HANDLE_USED) {
        DbSpinUnlock(&(handleInfo->lock));
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle state not match");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }

    if (handleInfo->topoHandle->currentIdx == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_FAILURE, "It is first label");
        DbSpinUnlock(&(handleInfo->lock));
        return VOS_ERRNO_DB_FAILURE;
    }
    AdapterFreeTopoHandleInfo(AdapterGetHdlMemctx(), &handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx]);
    handleInfo->topoHandle->currentIdx--;
    handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx].result.recIdx = 0;
    handleInfo->topoHandle->info[handleInfo->topoHandle->currentIdx].result.isScanFinish = true;

    DbSpinUnlock(&(handleInfo->lock));
    return GMERR_OK;
}

DB_ERR_CODE TPC_EndTopoSelect(VOS_UINT32 ulCdbId, DB_SELHANDLE hSelect)
{
#ifdef ENABLE_VISTLIB
    DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NOTSUPPORT, "TPC_EndTopoSelect CDB %" PRIu32 " is not support.", ulCdbId);
    return VOS_ERRNO_DB_NOTSUPPORT;
#else
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }
    if (!AdapterIsCdbValid(ulCdbId)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_TPC_INVALID_CDBID, "CDB %" PRIu32 " is not in use.", ulCdbId);
        return VOS_ERRNO_DB_TPC_INVALID_CDBID;
    }
    if (hSelect == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle should not be zero");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    hSelect -= 1;  // handle计数从1开始
    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    DbRWLatchW(&handlePool->latch);
    AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, hSelect);
    if (handleInfo == NULL || handleInfo->cdbId != ulCdbId) {
        DbRWUnlatchW(&handlePool->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle cdbId not match or handle is null when end topo");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }

    DbSpinLock(&(handleInfo->lock));
    if (handleInfo->state == HANDLE_FREE) {
        DbSpinUnlock(&(handleInfo->lock));
        DbRWUnlatchW(&handlePool->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDHANDLE, "handle state not match when end topo");
        return VOS_ERRNO_DB_INVALIDHANDLE;
    }
    AdapterFreeHandleInfo(handleInfo);
    AdapterFreeMultiBufByHandleId(hSelect);
    DbSpinUnlock(&(handleInfo->lock));
    DbRWUnlatchW(&handlePool->latch);
    return GMERR_OK;
#endif
}

VOS_VOID TPC_FreeBufMemById(VOS_UINT32 memId)
{
    AdapterFreeMultiBuf(memId);
}

static Status AdapterCheckPrepareQueryPlanArgs(DB_COND_STRU *pstCond, DB_QUERY_PLAN_STRU *pstQueryPlan)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ret;
    }

    ADPT4V1_CHECK_OBJECT_NULL(pstCond, "NULL pointer for condition parameter.");
    ADPT4V1_CHECK_OBJECT_NULL(pstQueryPlan, "NULL pointer for query plan parameter.");

    if (pstCond->usCondNum > TPC_COND_MAX) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NULLPTR, "The number of conditions is greater than the maximum limit.");
        return VOS_ERRNO_DB_INVALIDCONDITION;
    }

    return GMERR_OK;
}

DB_ERR_CODE TPC_PrepareQueryPlan(
    VOS_UINT32 ulCdbId, VOS_UINT32 ulDbId, VOS_UINT16 usRelNo, DB_COND_STRU *pstCond, DB_QUERY_PLAN_STRU *pstQueryPlan)
{
#ifdef ENABLE_VISTLIB
    ulCdbId = TPC_GLOBAL_CDB;
#endif
    Status ret = AdapterCheckPrepareQueryPlanArgs(pstCond, pstQueryPlan);
    if (ret != GMERR_OK) {
        return ret;
    }

    AdptV1ItemRefT conn = {0};
    uint32_t actDbId;
    ret = AdapterV1GetCdbConn(ulCdbId, ulDbId, DB_INVALID_UINT16, &conn, &actDbId);
    if (ret != GMERR_OK) {
        return ConvertErrorCode(ret);
    }

    AdapterSetIsTpcOp(conn.item, true);
    ret = GmeSimpleRelPrepareQueryPlan(conn.item, actDbId, usRelNo, pstCond, pstQueryPlan);
    AdapterV1FreeCdbConn(ulCdbId, &conn);
    return ConvertErrorCode(ret);
}

DB_ERR_CODE TPC_SimpleSelectAllRec(DB_SELECT_STRU *pstSelect)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef ENABLE_VISTLIB
    pstSelect->ulCdbId = TPC_GLOBAL_CDB;
#endif
    return ConvertErrorCode(AdapterSelectAllRecWithQueryPlan(pstSelect, true, true));
}

DB_ERR_CODE TPC_SelectAllRec2(DB_SELECT_STRU *pstSelect)
{
    Status ret = AdapterV1CheckTpcIsEnable();
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef ENABLE_VISTLIB
    pstSelect->ulCdbId = TPC_GLOBAL_CDB;
#endif
    return ConvertErrorCode(AdapterSelectAllRecWithQueryPlan(pstSelect, false, true));
}
