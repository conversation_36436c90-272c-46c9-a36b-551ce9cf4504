/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:
 * Author:
 * Create: 2025-05-29
 */
#ifndef SE_INDEX_PLUGIN_H
#define SE_INDEX_PLUGIN_H

#include "dm_index_plugin.h"
#include "adpt_index_plugin.h"
#include "db_common_index_plugin.h"
#include "gmdb_south.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DB_RESERVE_HANDLE_NUM 1
#define DB_MAX_MULTIZONE 2
#define REDO_LOG_BUFF_DUAL 2
#define DB_PAGE_HEAD_RESERVED_SIZE 3
#define DB_MAX_PATH 256

typedef struct TagPageIdT {
    uint32_t deviceId;
    uint32_t blockId;
} PageIdT;

typedef struct TagPageMgrT PageMgrT;

typedef enum PageOption {
    ENTER_PAGE_NORMAL = 0x0,
    ENTER_PAGE_PINNED = 0x1,
    ENTER_PAGE_RESIDENT = 0x2,
    ENTER_PAGE_SCAN = 0x4,
    ENTER_PAGE_LOAD = 0x8,
    ENTER_PAGE_INIT = 0x10,
    ENTER_PAGE_WRITE = 0x20,
    ENTER_PAGE_NOWAIT = 0x40,
} PageOptionE;

typedef struct AllocPageParam {
    uint32_t spaceId;
    uint32_t trmId;
    uint32_t labelId;
    DbInstanceHdT dbInstance;
    void *labelRsmUndo;
} AllocPageParamT;

typedef struct FreePageParam {
    uint32_t spaceId;
    PageIdT addr;
    DbInstanceHdT dbInstance;
    void *labelRsmUndo;
} FreePageParamT;

typedef enum PageMgrExtraFuncType {
    PAGE_MGR_EXTRA_MEMDATA = 0,
    PAGE_MGR_EXTRA_DURABLE_MEMDATA,
    PAGE_MGR_EXTRA_BUFFER_POOL,
    PAGE_MGR_EXTRA_BUFFER_POOL_TRMID_OP_WITH_PAGE,
    PAGE_MGR_EXTRA_BUFFER_POOL_TRMID_OP,
    PAGE_MGR_EXTRA_BUFFER_POOL_PREFETCH_PAGES,
    PAGE_MGR_EXTRA_BUFFER_POOL_STOP_PREFETCH_PROC,
    // 提供resizebufferpool的能力
    PAGE_MGR_EXTRA_BUFFER_POOL_RESIZE,
    // 将指定的页从bufferPool的bucket中移除，模拟换出
    PAGE_MGR_EXTRA_BUFFER_POOL_INVALID_PAGE,
    PAGE_MGR_EXTRA_BUFFER_POOL_RESET,
    PAGE_MGR_EXTRA_BUTT
} PageMgrExtraFuncTypeE;

typedef StatusInter (*SeGetPageFunc)(
    PageMgrT *pageMgr, PageIdT pageAddr, uint8_t **page, PageOptionE option, bool isWrite);
typedef StatusInter (*SeGetPageFuncWithArg)(
    PageMgrT *pageMgr, PageIdT pageId, uint32_t option, void *recyArg, uint8_t **page);
typedef StatusInter (*SeGetPageInitFunc)(
    PageMgrT *pageMgr, uint32_t trmId, PageIdT pageId, PageOptionE option, uint8_t **page);
typedef void (*SeLeavePageFunc)(PageMgrT *pageMgr, PageIdT pageAddr, bool isChanged);
typedef StatusInter (*SeAllocPageFunc)(PageMgrT *pageMgr, AllocPageParamT *allocPageParam, PageIdT *pageAddr);
typedef StatusInter (*SeFreePageFunc)(PageMgrT *pageMgr, FreePageParamT *freePageParam);
typedef void *(*SeGetPageMgrRegisterExtraFunc)(void *pageMgr, PageMgrExtraFuncTypeE funcType);
typedef void (*SeLeaveVirtualPageFunc)(PageMgrT *pageMgr, PageIdT pageAddr, bool isChanged, uint8_t *page);

typedef enum StorageType {
    SE_MEMDATA = 0,
    SE_DURABLE_MEMDATA,
    SE_BUFFER_POOL,
    SE_STORAGE_TYPE_BORDER,
} StorageTypeE;

struct TagPageMgrT {
    // 基于性能考虑，将getPage和leavePage放前边
    SeGetPageFunc getPageFunc;
    SeGetPageFuncWithArg getPageWithArgFunc;
    SeGetPageInitFunc getPageInitFunc;
    SeLeavePageFunc leavePageFunc;
    SeLeaveVirtualPageFunc leaveVirtualPageFunc;
    SeAllocPageFunc allocPageFunc;
    SeFreePageFunc freePageFunc;
    StorageTypeE type;
    uint16_t pageSizeShiftBit;
    uint16_t pageAddrShiftBit;
    SeGetPageMgrRegisterExtraFunc getExtraFunc;
    // bufferpool直连读用于解栈
    void *sessionCtx;
};

#pragma pack(4)
typedef struct TagSeTrmIdCtrl {
    DbSpinLockT lock;
    uint32_t trmId;
} SeTrmIdCtrlT;
#pragma pack()

typedef enum IndexScanDirection {
    INDEX_SCAN_ASCEND,  /* ascend scan */
    INDEX_SCAN_DESCEND, /* descend scan */
    INDEX_SCAN_BUTT
} IndexScanDirectionE;

typedef enum IndexMultiVersionType {
    INDEX_ONE_VERSION_TYPE = 0,      // 对应 PCC, RU
    INDEX_MARK_DELETE_VERSION_TYPE,  // 对应 PCC, RC
    INDEX_PCC_RR_TYPE,
    INDEX_MULTI_VERSION_TYPE,  // 对应 OCC
    INDEX_PCC_RC_TYPE,
} IndexMultiVersionTypeE;

typedef struct IndexMetaCfg {
    uint32_t indexId;
    DmIndexTypeE idxType;
    DmIndexTypeE realIdxType;
    DmIndexConstraintE idxConstraint;
    IndexMultiVersionTypeE indexMultiVersionType;
    // 索引 应使用固定的seInstanceId;
    uint32_t indexCap : 31;  // 索引初始容量，主要用于配置hash索引初始化配置
    // 排序索引需包含一些属性的配置信息 property, 需要落盘的信息
    uint32_t isLabelLatchMode : 1;  // 是否是大表锁模式，大表锁模式不用加page latch
    uint32_t tableSpaceId;
    uint32_t tableSpaceIndex;
    uint8_t nullInfoBytes;  // keyBuf中位图的字节长度，主要供lpm索引使用
    bool isUseClusteredHash;
    bool hasVarchar;
    bool isHcGlobalLatch;  // hashcluster是否已使用全局锁
    bool isMemFirst;       // 是否使用内存优先模式，当前该成员仅对排序索引生效
    bool isVertexUseRsm;
#ifdef FEATURE_SIMPLEREL
    bool isNormalIdx;  // v1兼容大对象标记
#endif
    DbLatchT *hcLatch;
    uint32_t keyDataType;
    void *extendParam;  // 根据索引类型确定对应的参数
} IndexMetaCfgT;

typedef struct IdxBase {
    IndexMetaCfgT indexCfg;
    DbLatchT idxLatch;
    uint32_t isConstructed;
    uint32_t shmemCtxId;
    DbLatchT stashPageLatch;
    uint64_t validCode;
} IdxBaseT;

typedef struct StRedoPubBuf {
    DbSpinLockT lock;
    volatile uint32_t curBatchId;  // 每刷盘一次公共buffer（一个redoBatch)，curBatchId++，并记录到文件的batch头中
    uint64_t curLsn;
    ShmemPtrT flushBufShm;  // 大小等于公共buffer大小，开启tamperProofEnable后，大小增加DB_TAMPER_PROOF_DIGEST_LEN
    uint32_t flushBufPos;
    uint32_t flushBufSize;
    uint64_t lastFlushTime;
} RedoPubBufT;

typedef struct StRedoBufPart {
    DbSpinLockT lock;
    uint64_t lsn;
    uint32_t size;
    uint32_t writePos;  // redo log缓冲区写到的位置
    uint8_t *addr;      // redo log缓冲区首addr
} RedoBufPartT;

typedef struct StRedoDualBuf {
    volatile bool isFlushing;
    RedoBufPartT members[MAX_REDO_LOG_PART];
} RedoDualBufT;

// 持久化关键结构体对齐： (不区分64/32位平台)强制8字节对齐
#pragma pack(8)
typedef struct StRedoLogFileHead {
    uint64_t writePos;  // 单文件时：公共buffer刷盘时增加，永远不会减小，使用时和redoFileSize取余（包含文件头）
                        // 多文件时：公共buffer刷盘时增加，回收redo文件时会重置
    uint32_t fsn;  // 当前redo文件的文件序列号，文件切换时被赋值，文件回收复用后会被新序列号覆盖；单文件固定为1
    uint32_t blockSize;  // Redo文件对齐大小，当前固定为512B
    uint32_t checkSum;
    uint32_t padding;  // 结构体对齐padding
    uint64_t readPos;  // 单文件时：回收redo文件时更新到truncPoint，永远不会减小，使用时和redoFileSize取余（包含文件头）
                       // 多文件时不会使用
    DbDigest32T headDigest;
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[32];
#endif
} RedoLogFileHeadT;

typedef enum EnumRedoLogFileStatus {
    REDO_LOG_FILE_INACTIVE = 1,
    REDO_LOG_FILE_ACTIVE = 2,
    REDO_LOG_FILE_CURRENT = 3,
    REDO_LOG_FILE_UNUSED = 4,
} RedoLogFileStatusE;

typedef struct StRedoLogFileData {
    RedoLogFileHeadT head;
    uint32_t fileId;
    uint32_t size;  // redo文件的大小，包含文件头
    RedoLogFileStatusE status;
} RedoLogFileDataT;

typedef struct StRedoLogFile {
    RedoLogFileDataT *info;  // pubCtx->fileSet.logFileShm
    int32_t fd[DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM];
    char *filePath[DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM];
} RedoLogFileT;

typedef struct StRedoFileSetPub {
    DbSpinLockT lock;
    volatile uint32_t curFile;  // 当前正在使用的redo文件ID，多文件切换时递增，最后一个文件会切换回第一个；单文件不变
    volatile uint32_t curFsn;   // redo文件的序列号，从1开始，多文件切换时递增；单文件不变
    volatile uint32_t firstActiveFile;  // 第一个被激活的redo文件ID，firstActiveFile 到 curFile 之外的文件已回收
    uint64_t totalFreeSize;
    ShmemPtrT logFileShm;  // RedoLogFileDataT
} RedoFileSetPubT;

// 持久化关键结构体对齐： (不区分64/32位平台)强制8字节对齐
#pragma pack(8)
typedef struct {
    uint32_t fsn;      // file serial number, start at 1; unchanged when single redo file
    uint32_t padding;  // 8字节对齐
    uint64_t blockId;  // redo文件按照blockSize(512B)对齐，表示这个batch是在文件的第几个block
    uint64_t batchId;  // batch id
} RedoPointT;
#pragma pack()

typedef struct StRedoPubCtx {
    RedoPubBufT redoPubBuf;
    RedoFileSetPubT fileSet;
    RedoPointT curPoint;
} RedoPubCtxT;

typedef enum EnumRedoLogFlushByTrx {
    REDO_LOG_FLUSH_BY_TRX_DISABLED = 0,
    REDO_LOG_FLUSH_BY_TRX_SYNC = 1,
    REDO_LOG_FLUSH_BY_TRX_LAZY_SYNC = 2,
    REDO_LOG_FLUSH_BY_TRX_UNUSED = 3,
} RedoLogFlushByTrxE;

typedef struct StRedoConfig {
    uint32_t blockSize;  // redo文件对齐大小，当前固定为512B
    uint32_t pubBufSize;
    uint32_t bufParts;
    RedoLogFlushByTrxE flushByTrx;
    uint32_t flushBySize;
    uint32_t flushByTime;
    uint32_t flushCheckPeriod;
    char **redoDir;
    const char *redoFilePrefix;
    uint32_t fileSize;
    uint32_t fileCount;
    uint32_t ctxBufSize;  // redoctx default size
    uint32_t fileDropOnClose;
} RedoConfigT;

typedef struct TagPageHeadT {
    DbLatchT lock;  // 加锁控制页内数据的读写并发访问
    uint16_t checkSum;
    uint16_t beginPos;
    uint16_t endPos;    // 终端页尾预留32字节；非终端开启防篡改时，页尾记录32字节页摘要
    uint16_t freeSize;  // 包含删除的行的空间，可以用来加速垃圾清理
    uint32_t trmId;     // 页所属的容器Id,对应容器内的fileId语义
    PageIdT addr;
    PageIdT nextPageId;     // 为持久化预留
    uint64_t lsn;           // 为持久化预留
    uint32_t entryUsedNum;  // for primary index: the entry number that has been occupied
    // pageState: memdata分配给上层后，有可能上层还未初始化，就抢先被扫描线程加上页锁，从而访问到未初始化的页;
    // PAGE_UNINIT--未初始化，PAGE_USING--上层已初始化，加页latch时，pageState必须为PAGE_USING
    uint8_t pageState : 1;
    uint8_t isRsmUsing : 1;  // 保留内存使用，恢复时用于判断该页是否可以被回收
    uint8_t pageType : 6;
#ifdef FEATURE_PERSISTENCE
    // pageState 被压缩的页此变量会被打上True
    bool isCompressed;
    uint16_t pageCompressedSize;  // 页压缩后的大小
    uint32_t crcCheckSum;
    uint8_t isEncrypted;  // 是否加密标志，默认值为0，如果非0，则需要从页未取出hmac和iv进行解密
    uint8_t reserve[DB_PAGE_HEAD_RESERVED_SIZE];
#ifndef NDEBUG
    PageIdT permanentAddr;
#endif
#else
    uint8_t reserve[DB_PAGE_HEAD_RESERVED_SIZE];
#endif
} PageHeadT;
#pragma pack()

typedef struct StRedoReplayArgs {
    uint64_t trxId;
    uint32_t size;
    PageHeadT *pageHead;
    uint8_t *data;
    SeInstanceHdT seInstance;
} RedoReplayArgsT;

typedef StatusInter (*RedoLogReplayFuncT)(RedoReplayArgsT *replayArgs);

typedef struct StRedoReplayInfo {
    RedoLogReplayFuncT replayFunc;
} RedoReplayInfoT;

typedef struct StRedoMgr RedoMgrT;

typedef struct StRedoLogFileSet {
    RedoFileSetPubT *fileSetPub;  // pubCtx->fileSet
    RedoLogFileT *files;
    uint32_t fileCount;
    uint32_t fileMaxSize;
    uint32_t blockSize;
    uint32_t reserved;
    uint8_t *flushHeadBuf;
    uint32_t multizoneNum;
    uint32_t recoveryZoneId;
    RedoMgrT *redoMgr;
} RedoLogFileSetT;

typedef struct StRedoBuf {
    RedoPubBufT *redoPubBuf;  // pubCtx->redoPubBuf
    volatile uint16_t wid;    // 当前写入buffer的下标
    uint16_t partCount;
    uint32_t reserve;
    uint8_t *bufAddr;
    RedoDualBufT *buffers[REDO_LOG_BUFF_DUAL];
    uint8_t *flushBuf;  // pubCtx->redoPubBuf.flushBufShm
    RedoLogFileSetT *fileSet;
    RedoMgrT *redoMgr;
} RedoBufT;

typedef enum EnumRedoLogType {
    // page
    REDO_ENTER_PAGE,
    REDO_LEAVE_PAGE,
    REDO_MODIFY_PAGE,
    REDO_INIT_PAGE_HEAD,
    REDO_RESET_PAGE_HEAD,

    // trx
    REDO_TRX_BEGIN,
    REDO_TRX_COMMIT,
    REDO_TRX_ROLLBACK,

    // dml
    REDO_OP_INSERT,
    REDO_OP_UPDATE,
    REDO_OP_UPDATE_EDGE,     // 顶点更新边的属性
    REDO_OP_UPDATE_LINKSRC,  // 没有元数据需要记录的情况，用于记录linkSrc的变化
    REDO_OP_DELETE,
    REDO_OP_REPLACE_INSERT,
    REDO_OP_REPLACE_UPDATE,
    REDO_OP_MARK_DELETE,
    REDO_OP_PAGE_COMPRESS,
    REDO_OP_HEAP_CREATE,
    REDO_OP_HEAP_MODIFY,
    REDO_OP_CLEAR_MARK_DELETE,
    REDO_OP_CLEAR_RESERVE_SIZE,
    REDO_OP_UNLINK_ROLLPTR,
    REDO_OP_FIX_PAGE_INIT,
    REDO_OP_VAR_PAGE_INIT,
    REDO_OP_SCHEMA_CHANGE,
    REDO_OP_ROLLBACK,
    REDO_OP_BATCH_INSERT,

    // FixedHeap dml
    REDO_OP_FX_HP_INSERT,
    REDO_OP_FX_HP_UPDATE,
    REDO_OP_FX_HP_DELETE,
    REDO_OP_FX_HP_DELETE_ROLLBACK,
    REDO_OP_FX_HP_UNLINK_ROLLPTR,
    REDO_OP_FX_HP_CREATE,
    REDO_OP_FX_HP_PAGE_INIT,
    REDO_OP_FX_HP_CLOSE,

    // index
    REDO_OP_HASH_CREATE,
    REDO_OP_HASH_INSERT,
    REDO_OP_HASH_EXPAND,
    REDO_OP_HASH_EXPAND_SEGINIT,
    REDO_OP_HASH_EXPAND_REHASH,
    REDO_OP_HASH_EXPAND_UPDATE_SEGMAP,
    REDO_OP_HASH_EXPAND_SEGDIR_HEAD,
    REDO_OP_HASH_DEL,

    REDO_OP_BTREE_CREATE,
    REDO_OP_BTREE_UPDATE_STATISTICS,
    REDO_OP_BTREE_INIT_NODE,
    REDO_OP_BTREE_UNMARK,
    REDO_OP_BTREE_INSERT,
    REDO_OP_BTREE_TRANSFER,         // truncate mode
    REDO_OP_BTREE_TRANSFER_INSERT,  // insert specified slot
    REDO_OP_BTREE_UPDATE_CHILD_ID,
    REDO_OP_BTREE_UPDATE_LEAF_LINK,
    REDO_OP_BTREE_DELETE_RANGE,
    REDO_OP_BTREE_MARK_DELETE,
    REDO_OP_BTREE_DELETE,
    REDO_OP_BTREE_DELETE_LEFT_RANGE,
    REDO_OP_BTREE_INSERT_BIG_KEY,
    REDO_OP_BTREE_FREE_META,

    REDO_OP_DISKANN_SET_EDGE,
    REDO_OP_DISKANN_SET_NODE,
    REDO_OP_DISKANN_SET_TAG,
    REDO_OP_DISKANN_INIT_META,
    REDO_OP_DISKANN_APPEND_META_LIST,
    REDO_OP_DISKANN_RESET_META,
    REDO_OP_DISKANN_SET_NEW_SLAVE,
    REDO_OP_DISKANN_SET_NEXT_SLAVE_ID,
    REDO_OP_DISKANN_SET_PRE_SLAVE_ID,
    REDO_OP_DISKANN_REMOVE_FROM_NBS,
    REDO_OP_DISKANN_SET_FROZENS,
    REDO_OP_DISKANN_SET_PAGE_HEAD,
    REDO_OP_DISKANN_SET_DISKANN_NODE_HDR,
    REDO_OP_DISKANN_SET_META,
    REDO_OP_DISKANN_SET_DISKANN_DEL_LIST_HDR,
    REDO_OP_DISKANN_RESET_DISKANN_NODE,
    REDO_OP_DISKANN_ADD_TO_NODE_LIST,

    REDO_OP_LPASMEM_CHANGE,

    REDO_OP_HASH_SET_META,
    REDO_OP_HASH_SET_SEGMENT,
    REDO_OP_HASH_SEGMENT_PAGE_INIT,
    REDO_OP_HASH_FREE_DIR_PAGE,
    REDO_OP_HASH_SET_META_STASH_PAGE,
    REDO_OP_HASH_SET_SEGMENT_ADDR,
    REDO_OP_HASH_SET_SEGMENT_ENTRY,
    REDO_OP_HASH_SET_DIR_NEXT_PAGE_ID,
    REOD_OP_HASH_SET_META_DEPTH,
    REDO_OP_HASH_STASH_PAGE_INIT,
    REDO_OP_HASH_SET_STASH_ENTRY,
    REDO_OP_HASH_SET_SEGMENT_ENTRY_PROBELEN,
    REDO_OP_HASH_SEGMENT_PAGE_MARK_DELETE,
    REDO_OP_HASH_STASH_PAGE_MARK_DELETE,
    REDO_OP_HASH_SET_STASH_PREV_PAGE_ID,
    REDO_OP_HASH_SET_STASH_BIT_MAP,
    REDO_OP_HASH_SET_STASH_PROBELEN,
    REDO_OP_HASH_COPY_PAGE_DATA,

    // undo
    REDO_UNDO_GENERAL,
    REDO_UNDO_WRITE_RECORD,
    REDO_UNDO_RSEG_INIT,
    REDO_UNDO_SEG_INIT,
    REDO_UNDO_PAGE_INIT,
    REDO_UNDO_LOG_HEADER_CREATE,
    REDO_UNDO_RETAIN_CACHE_LOG_INIT,
    REDO_UNDO_SEG_TRUNCATE_UPDATE,
    REDO_SPACE_CLOSE,

    // lfs
    REDO_LFS_MGR_INIT,
    REDO_LFS_MGR_FRAGMENTED,
    REDO_LFS_SLOT,
    REDO_LFS_MGR_PAGECNT,
    REDO_LFS_DATA_PAGE,
    REDO_LFS_FSM_PAGE_HEAD,
    REDO_LFS_FSM_USED_SLOT_CNT,
    REDO_LFS_MGR_PAGECNTANDFSMLIST,
    REDO_LFS_MGR_EXTENT_FSM_SLOT,
    REDO_LFS_TABLE_SLOT,
    REDO_LFS_UPDATE_TABLE_CNT,

    // space
    REDO_SPACE_HEAD,
    REDO_SPACE_FREE_PAGE_OR_EXTENT,
    REDO_SPACE_FILE_EXTENT_IN_HEAD,
    REDO_SPACE_ALLOC_PAGE,
    REDO_SPACE_UPDATA_NEXT_PAGE_ADDR,

    // Catalog
    REDO_SET_PAGE_TYPE,
    REDO_META_UUID,
    REDO_META_MAX_TRXID,
    // ctrl file ，特殊的日志 recovery时不走GetPage
    // 一些没有逻辑addr页的日志
    REDO_NO_ADDR_PAGE_START,
    REDO_CTRL_PAGE,
    REDO_CTRL_PAGE_ALLOC,
    REDO_SPACE_CTRL_CREATE,
    REDO_DATAFILE_CTRL_CREATE,
    REDO_DATAFILE_CTRL_REMOVE,
    REDO_DATAFILE_CTRL_TRUNCATE,
    REDO_DATAFILE_CTRL_EXTEND,
    REDO_DEVICE_CTRL_CREATE,
    REDO_DEVICE_CTRL_REMOVE,
    REDO_EXTEND_CTRL_INSERT_SLOT,
    REDO_EXTEND_CTRL_INSERT_ROW,
    REDO_EXTEND_CTRL_DELETE,
    REDO_EXTEND_CTRL_ORGANIZE,
    REDO_CORE_CTRL,
    // CATA
    REDO_OP_CATA_UPDATE,
    REDO_NO_ADDR_PAGE_END,

    // compress
    REDO_PERSIST_COMPRESS_PAGE,

    REDO_DEVICE_HEAD_INIT,
    REDO_DEVICE_ALLOC_BLOCK,

    REDO_HEAP_ALLOC_LOB_ROW,
    REDO_HEAP_BIND_SUB_ROW,
    REDO_HEAP_BIND_DST_ROW,
    REDO_HEAP_UPDATE_DIR_NEXT_PTR,
    REDO_HEAP_UPDATE_DIR_PREV_PTR,
    REDO_HEAP_DIR_ROW_UPDATE_SUB_NUM,
    REDO_SPACE_APPEND_DEVICE,

    REDO_OP_BUTT,
} RedoLogTypeE;

struct StRedoMgr {
    DbSpinLockT lock;
    uint32_t priBufSize;  // 私有Buffer大小，mgr创建后就不会变化
    RedoPubCtxT *pubCtx;  // shared memory in multi-process，seIns->redoCtxShm
    RedoConfigT cfg;
    RedoBufT redoBuf;
    RedoLogFileSetT redoFiles;
    SeInstanceHdT seIns;
    MemUtilsT *memUtils;
    void *seDynMemCtx;      // storage engine dynamic memory context
    bool isStop;            // RedoTask后台线程是否已经关闭，后台线程和seIns，RedoMgr生命周期相同
    DbThreadHandle thread;  // RedoTask后台线程的线程句柄
    RedoReplayInfoT replayInfo[(int)REDO_OP_BUTT];  // redo日志的重演函数，初始化后就不会变化
    TagLinkedListT ctxCacheList;                    // 用于缓存空闲的RedoRunCtx
    uint32_t ctxCacheSize;                          // ctxCacheList 链表的元素数量
    uint16_t concurrentCnt;  // 并发原子范围的数量，redoBegin时加一，redoEng时减一
    uint16_t atomicPageCapacity;  // 原子范围数据页容量，最大为64，最小为32，根据bufferpool页数量来决定
    uint16_t atomicCtrlPageCapacity;  // 原子范围控制页容量，为atomicPageCapacity的二倍
    bool enable;
    bool useReservedMem;
    bool useReservedMemRecovery;
    bool crcCheckEnable;
    bool tamperProofEnable;
    bool isSingleRedoFile;
};

typedef Status (*SeUndoPurgerCreateFunc)(DbInstanceHdT dbInstance);
typedef Status (*SeDafPurgerCreateFunc)(void);
typedef bool (*SeKeepThreadAliveFunc)(DbInstanceHdT dbInstance);

typedef struct TagSeConfigT {
    uint32_t deviceSize;    // size of device, unit: K
    uint32_t extendSize;    // size of extend, unit: K
    uint16_t pageSize;      // size of page, unit: K
    uint16_t ctrlPageSize;  // size of ctrl page, unit: K
    uint32_t maxSeMem;      // total size of memory, unit: K (当前支持16G，更大需要使用uint32)
    uint16_t instanceId;    // current storage engine of id
    bool isUseRsm;
    bool isUseRsmExtendBlock;
    bool enableReleaseDevice;
    uint16_t maxLockShareCnt;   // 最大session数除以2
    uint16_t bufferPoolPolicy;  // the policy of recycling pages,default value is BUF_RECYCLE_TABLE(1)
    uint16_t maxTrxNum;     // 从配置文件中读出最大事务个数 + DB自己预留的个数(MAX_BG_WORKER_NUM)
    uint32_t rsmBlockCnt;   // 需要扣除给控制区的1个, 这里是存储的
    uint32_t rsmBlockSize;  // size of rsm, unit: M
    uint32_t rsmExtendBlockSize;            // size of rsm extend block, unit: M
    uint32_t rsmDfgmtRateThreshold;         // 触发保留内存搬迁回收的空闲率阈值(%)
    uint32_t fragmentationRateThreshold;    // 单表触发缩容的最小碎片率(%)
    uint32_t lockWakeupPeriod;              // 事务锁等待时被唤醒尝试加锁的周期(ms)
    uint32_t deadlockCheckPeriod;           // 事务锁等待时进行死锁检测的周期(ms)
    uint32_t lockJumpQueuePeriod;           // 事务锁等待时插队到等待队列队首的周期(ms)
    uint32_t lockTimeOut;                   // 事务锁等待超时的时间(ms)
    uint32_t fragmentationMemoryThreshold;  // 单表触发缩容的最小内存(M)
    SeHpTupleAddrMode heapTupleAddrMode;
    SeHpTupleAddrMode rsmHeapTupleAddrMode;
    SeUndoPurgerCreateFunc undoPurgerCreate;
    SeDafPurgerCreateFunc dafPurgerCreate;
    SeKeepThreadAliveFunc seKeepThreadAlive;

    // redo config
    uint32_t persCompMode;
    uint32_t redoPubBufSize;  // uint: B
    uint32_t redoBufParts;
    uint32_t redoFlushByTrx;
    uint32_t redoFlushBySize;  // uint: B
    uint32_t redoFlushByTime;
    uint32_t redoFlushCheckPeriod;
    char **redoDir;         // [DB_MAX_MULTIZONE][路径长度，按需分配];
    uint32_t redoFileSize;  // uint: B
    uint32_t redoFileCount;
    uint32_t redoFileDropOnClose;

    // check point config
    uint32_t ckptPeriod;     // checkpoint触发时间周期(s)
    uint32_t ckptThreshold;  // checkpoint触发队列水位
    uint32_t dwrBlockNum;
    uint32_t bufferPoolSize;  // size of bufferpool, unit: KB
    uint32_t bufferPoolNum;   // number of bufferpool instances
    uint32_t bpChunkSize;     // chunk size while page buf split , unit: KB, max 4G(cfg) 4T(field type)
    bool isRedoConfigInit;
    bool dwrEnable;  // double write enabled config
    bool crcCheckEnable;
    bool isReadOnlyInstance;  // read only instance , 此标志位决定checkpoint是否能落盘
    bool shaCheckEnable;
    bool condensedCtrlPages;  // ctrl page 紧密排布，按需申请，没有预留页
    bool fixPersistEnable;

    // encrypt config
    bool encryptReservedEnable;

    // space
    char **dataFileDirPath;  // [DB_MAX_MULTIZONE][路径长度，按需分配];
    char **ctrlFileDirPath;  // [DB_MAX_MULTIZONE][路径长度，按需分配];
    char recoveryPath[DB_MAX_PATH];
    char *ctrlFileName;
    char *systemSpaceFileName;
    char *undoSpaceFileName;
    char *userSpaceFileName;
    char *safeFileName;
    char *redoFilePrefix;
    char *tamperProofSoPath;  // 防篡改so路径，DB动态加载so中的摘要计算函数
    uint32_t dbFilesMaxCnt;
    uint32_t spaceMaxNum;
    uint32_t dbFileSize;         // unit: K
    bool enableSyncWriteFile;    // 是否以同步的方式写文件
    bool compressSpaceEnable;    // 是否打开压缩的space
    bool tamperProofEnable;      // 是否打开防篡改
    uint8_t bufferpoolMemType;   // Bufferpool在动态内存或共享内存部署
    uint32_t compressSpaceSize;  // 设置压缩的space大小

    // 多区持久化
    uint32_t recoveryZoneId;
    uint32_t multiZoneNum;

    uint16_t sharedModeEnable;

    bool preFetchPagesEnable;
    uint32_t maxPreFetchThreNum;
    uint32_t loadTablePriorityRatio;
    uint32_t bufferPoolPriorityRatio;
} SeConfigT;

typedef enum BufLruListType {
    LRU_LIST_NORMAL = 0,
    LRU_LIST_PRIORITY = 1,
    LRU_LIST_HIGH_PRIORITY = 2,
    // 淘汰类型总数标记位，如果位置变化，视图和BufPool结构体均需要修改
    LRU_LIST_STATS_SCAN = 3,
    LRU_LIST_TYPE_MAX = 4
} BufLruListTypeE;

// BUF_DESC_LIST_TAIL_ENTRY 从tail访问，tail-prev表示最近刚使用的
// BUF_DESC_LIST_HEAD_ENTRY 从head访问，head-next表示最久没有使用的
typedef struct {
    ShmemPtrT next;  // struct BufDesc *
    ShmemPtrT prev;  // struct BufDesc *
    uint32_t count;
} BufDescLinkedListT;

typedef struct BufDesc {
    DbSpinLockT lock;
    PageIdT pageId;
    void *page;
    void *attachedBucket;  // 所属的bucket
    uint32_t bufPoolId;
    uint32_t descId;
    uint32_t tableId;
    uint32_t priority;
    BufLruListTypeE listId;
    volatile double operateTime;
    volatile uint64_t threadId;
    volatile bool isWriting;
    bool isChanged;
    bool isResident;
    bool isPinned;
    bool isReadOnly;
    bool isDirty;
    uint8_t loadStatus;
    uint8_t inCkptBuf;
    uint16_t refCount;
    // struct BufDesc *, this BufDesc ptr, accelerate access
    ShmemPtrT thisPtr;
    struct BufDesc *hashNext;
    uint32_t hashNextDescId;
    BufDescLinkedListT lruLinkedNode;
    volatile uint32_t lockRetryCnt;

    // checkpoint
    RedoPointT truncPoint;
    ShmemPtrT ckptPrev;  // struct BufDesc *
    ShmemPtrT ckptNext;  // struct BufDesc *
    char ckptFlag;
    char reserve[3];
} BufDescT;

typedef struct TagCkptQueueT {
    DbSpinLockT lock;
    RedoPointT truncPoint;
    volatile uint32_t count;
    ShmemPtrT first;  // BufDescT *
    ShmemPtrT last;   // BufDescT *
    BufDescT *batchEnd;
} CkptQueueT;

typedef struct TagCkptPublicCtxT {
    DbSpinLockT lock;
    CkptQueueT queue;
} CkptPublicCtxT;

typedef struct TagCkptStatT {
    uint64_t performCount;
    uint64_t triggerCount;
    uint64_t flushPageCount;
    uint64_t flushCount;
    uint64_t flushBeginTime;
} CkptStatT;

typedef enum TagCkptModeT {
    CKPT_MODE_NONE = 0,  // 表示目前ckpt没有脏页可刷, 内部自更新的状态，外部不可设置
    CKPT_MODE_INC = 1,   // 表示目前ckpt使用增量刷盘方式，不保证内存内所有的脏页都会落盘
    CKPT_MODE_FULL = 2,  // 表示目前ckpt使用全量刷盘方式
    // 该方式目前仅能通过外部接口触发，会记录当前脏页的数量并将所有记录范围内的脏页落盘
    CKPT_MODE_BOOT_FULL = 3,  // DB初始化或恢复线程使用的FULL刷盘模式，不会喂狗
} CkptModeT;

typedef struct TagCkptSortItemT {
    BufDescT *buf;
    uint32_t itemId;
} CkptSortItemT;

typedef struct TagSeInstanceT SeInstanceT;

typedef struct TagCkptGroupT {
    uint32_t count;
    uint32_t capacity;
    uint32_t pageSize;
    uint32_t bufSize;
    uint32_t offset;
    uint32_t dwrBlockNum;  // 分批双写，每批的页数
    // 双写已执行成功的part计数
    uint32_t dwrPartIndex;
    // 刷盘当前已执行的part计数
    uint32_t flushPartIndex;
    // 记录group数组下标，用于记录刷到哪个位置，生命周期为一个group所有脏页落盘就重置该计数
    uint32_t flushStartPoint;
    char *buf;
    CkptSortItemT *items;
    SeInstanceT *seIns;
} CkptGroupT;

typedef struct CkptCtx {
    CkptPublicCtxT *pubCtx;
    // 生命周期为实例级别，CkptProc范围，判断checkpoint线程是否需要运行标志；CkptStopProc关闭时会设置为false
    volatile bool isRunning;
    // 实例级别，GmcFlushEnable或者异常锁库时候会尝试开启关闭checkpoint
    bool enable;  // 是否使能开启checkpoint刷盘能力
    // 实例级别，是否是终端场景（按需刷盘，不会开启checkpoint线程）
    bool isFlushOnMiniDemand;  // On-demand disk flushing
    bool isGroupDynAlloc;  // groupBuffer内存是否是每次刷盘时按需申请；当前只有bufferpoolSize为128KB时才会动态申请
    volatile bool updateFileDigest;  // 是否更新文件摘要，业务线程刷盘时候如果开启shaCheckEnable则会使能该开关
    volatile uint32_t fullSeqId;     // 全量刷盘次数，顺序递增，表示当前刷盘ID，
                                     // CkptTrigger触发perform或者CkptProcess4MiniKv时递增
    volatile uint32_t incrSeqId;     // 增量刷盘次数，顺序递增，checkpoint线程perform一次则递增
    volatile CkptModeT trigger;      // 当前刷盘的模式，刷盘成功之后设置为无效CKPT_MODE_NONE
    uint32_t period;                 // 初始化时来自配置项，支持在线修改
    uint32_t threshold;              // 初始化时来自配置项，支持在线修改
    volatile uint64_t flushEndTime;  // 刷盘时间
    DbThreadHandle thread;           // checkpoint线程handle
    RedoPointT truncPoint;           // truncate截断点
    RedoPointT lrpPoint;             // least recovery point
    SeInstanceT *seIns;
    CkptGroupT group;
    CkptStatT stat;        // 提供dfx视图
    uint8_t *ckptCompBuf;  // used for compress buffer for ckpt task
    // 生命周期仅限CkptProc范围, 其他线程CkptWaitFlushFinish会尝试读取这个值
    volatile StatusInter errPerformRet;  // perform执行异常时候的返回值
} CkptCtxT;

typedef struct BufChunk {
    uint32_t pageHwm;     //  high water mark, page which index lt hwm is allocated
    uint32_t recycleCnt;  //  recycle page count
    char *pageBuf;
    BufDescT *bufDescs;
} BufChunkT;

typedef struct BpChunkMgr {
    uint32_t chunkCapacity;        // Max chunk count in one buff pool
    uint32_t chunkCapacityLimit;   // Limit max chunk count in one buff pool, resize阶段可能会不断调低
    uint32_t allocatedChunkCnt;    // Number of chunks that have been allocated
    uint32_t chunkHwm;             // Chunks smaller than hwm have been applied for by bufferpool.
    BufChunkT **ckSlot;            // Max count is chunkCapacity
    uint32_t pageCapacityInChunk;  // Max page count in one chunk, static invariant
} BpChunkMgrT;

typedef uint64_t TrxIdT;

typedef struct RehashBucketMgrWrapper {
    ShmemPtrT backBucketMgrShm;
    // 当前最大事务ID，rehash前记录，辅助判断是否还有业务使用
    TrxIdT maxTrxId;
    uint8_t *oldBucket;
} RehashBucketMgrWrapperT;

typedef struct BufPool {
    DbSpinLockT lock;
    uint32_t capacity;
    uint64_t size;
    uint32_t scanListLimit;
    uint32_t highPriorityListMinimum;
    uint32_t priorityListMinimum;  // the minimum length of list that contains priority page
    uint32_t hwm;                  // high water mark
    ShmemPtrT pageBufShm;
    ShmemPtrT bufDescShm;
    ShmemPtrT bucketMgrShm;
    ShmemPtrT ringbufShm;
    RehashBucketMgrWrapperT rehashBucketWrapper;
    // all lru list
    BufDescLinkedListT list[LRU_LIST_TYPE_MAX];
    DbOamapT *tableMap;     // key is tableId, value is the number of pages
    BpChunkMgrT *chunkMgr;  // 为空表示不开启split chunk功能
} BufPoolT;

typedef struct BufGetDescArgs {
    PageIdT pageId;
    uint32_t options;
} BufGetDescArgsT;

typedef struct BufpoolMgr BufpoolMgrT;

typedef StatusInter (*BufpoolLRUEnqueueFunc)(BufpoolMgrT *mgr, PageOptionE option, void *enqueArg);
typedef StatusInter (*BufpoolRecycleFunc)(
    BufpoolMgrT *mgr, BufGetDescArgsT *args, void *recyArg, bool reTry, BufDescT **bufDesc);
typedef StatusInter (*BufPreprocessFunc)(BufpoolMgrT *mgr, void *recyArg, BufDescT *bufDesc);
typedef void (*BufSetBufDescListFunc)(uint32_t options, const void *recyArg, BufDescT *bufDesc);
typedef StatusInter (*BufDescSwitchLRUListFunc)(
    BufpoolMgrT *mgr, BufPoolT *bufPool, uint32_t options, const void *recyArg, BufDescT *bufDesc);

// buffer pool table map清理函数
typedef void (*TableMapClearFn)(void *ctx, void *tableMap, uint32_t tableId);
// buffer pool table map 获取page count函数
typedef uint32_t (*TableMapGetPageCntFn)(void *ctx, void *tableMap, uint32_t tableId);
typedef StatusInter (*TableMapIncPageCntFn)(void *ctx, void *tableMap, uint32_t tableId);
typedef void (*TableMapDecPageCntFn)(void *ctx, void *tableMap, uint32_t tableId);

typedef struct {
    TableMapClearFn tableMapClearFn;
    TableMapGetPageCntFn tableMapGetPageCntFn;
    TableMapIncPageCntFn tableMapIncPageCntFn;
    TableMapDecPageCntFn tableMapDecPageCntFn;
} TableMapFnsT;

typedef struct DbInterProcSpinRWLockFn DbInterProcSpinRWLockFnT;
typedef struct ConcurrentPrefetchInfo ConcurrentPrefetchInfoT;
typedef struct ConcurrentPrefetchCtx ConcurrentPrefetchCtxT;
typedef struct BpResizeMgr BpResizeMgrT;
typedef struct TrxMgr TrxMgrT;
typedef Status (*OptiTrxSetLabelLastTrxIdAndTrxCommitTime)(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance);
typedef Status (*OptiTrxGetLabelLastTrxIdAndTrxCommitTime)(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance);
typedef Status (*OptiTrxGetLabelName)(uint32_t labelId, char *labelName, DbInstanceHdT dbInstance);

typedef enum {
    TRX_EDGELABEL_HEAP,
    TRX_KVLABEL_HEAP,
    TRX_VERTEXLABEL_HEAP,
    TRX_CHECK_READVIEW_NUM
} TrxCheckLabelReadViewTypeE;

typedef struct SeSimpleLabelInfo {
    uint32_t labelsCnt;
    uint32_t *alterLabelsExt;
} SeSimpleLabelInfoT;

// 记录乐观事务相关的信息
typedef struct SeOptiTrxInfo {
    uint64_t trxId;                      // 事务ID，也是事务开始时刻的逻辑时钟
    uint64_t commitTs;                   // commit ID, 也是事务提交时刻的逻辑时钟
    char *peerProcInfo;                  // 记录该事务对应的对端信息，固定长度SE_CONN_INFO_MAX_LEN
    SeSimpleLabelInfoT vertexLabelInfo;  // 记录该事务所操作过的vertex表
    SeSimpleLabelInfoT kvLabelInfo;      // 记录该事务所操作过的kv表
    SeSimpleLabelInfoT edgeLabelInfo;    // 记录该事务所操作过的边表
    struct SeOptiTrxInfo *nextTrxInfo;   // 链表结构，指向一下个optiTrxInfo
} SeOptiTrxInfoT;

typedef struct SeOptiTrxInfoList {
    uint32_t listLen;
    SeOptiTrxInfoT *head;  // 链表中第一个optiTrxInfo
    SeOptiTrxInfoT *tail;  // 链表中最后一个optiTrxInfo
} SeOptiTrxInfoListT;

typedef enum {
    OPTI_TRX_COMMIT_NORMAL,  // 没有重试事务的状态，行为与事务正常提交一致
    OPTI_TRX_RETRY_COMMIT,  // 有重试事务正在执行的状态，控制重试事务串行执行、业务事务阻塞提交
    OPTI_TRX_CHANGE_STATE,  // 切换状态，优先处理业务提交事务队列
} OptiTrxRetryCommitStateE;

typedef struct DbQueue {
    DbMemCtxT *memCtx;  // memory context
    void *items;
    uint32_t itemSize;
    uint32_t capacity;  // capacity of items array
    uint32_t front;     // front index of the queue
    uint32_t rear;      // rear index of the queue
} DbQueueT;

typedef struct OptimisticTrxRetryCommitInfo {
    bool isInit;
    bool reserve[3];
    OptiTrxRetryCommitStateE commitState;
    TrxIdT retryTrxId;  // 为0表示当前没有重试事务
    DbQueueT retryTrxQueue;
    DbQueueT commitTrxQueue;
} OptiTrxRetryCommitInfoT;  // 对本结构体的操作，都需要在trxMgr->latch下

struct TrxMgr {
    DbLatchT latch;  // 直连读场景加这个锁需要记录session
    int32_t trxCnt;
    TrxIdT maxTrxId;
    TrxIdT minActiveNormalTrxId;
    uint16_t maxTrxNum;     // 从配置文件中读出最大事务个数 + DB自己预留的个数(MAX_BG_WORKER_NUM)
    uint8_t isPersistence;  // 是否持久化模式
    uint8_t reserved;
    uint32_t rwTrxIdsOffset;  // 读写事务ID链表的偏移量 TrxIdListT      rwTrxIds
    uint32_t roTrxIdsOffset;  // 只读事务ID链表的偏移量 TrxIdListT      roTrxIds
    uint32_t trxPoolOffset;   // trxPool的偏移量 TrxPoolT        trxPool;
    uint64_t maxTrxTimeUse;
    ShmemPtrT trxMgrShmAddr;  // 用于直连读场景，当客户端进程异常退出后利用其进行恢复
    OptiTrxSetLabelLastTrxIdAndTrxCommitTime
        setFunc[(uint32_t)TRX_CHECK_READVIEW_NUM];  // yang场景设置冲突域trxId的钩子函数
    OptiTrxGetLabelLastTrxIdAndTrxCommitTime
        getFunc[(uint32_t)TRX_CHECK_READVIEW_NUM];  // yang场景获取冲突域trxId的钩子函数
    OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM];
    // 记录历史乐观事务操作过的表信息的链表，在后台线程进行内存的回收
    SeOptiTrxInfoListT optiTrxHistoryInfoList;
    ShmemPtrT multiTrxMgrShm;  // 仅在Mini KV和sql场景下才可使用
    uint16_t instanceId;
    OptiTrxRetryCommitInfoT optiTrxRetryInfo;
};

struct BpResizeMgr {
    // 用于前台业务线程通知后台线程处理resize请求, 线程级
    DbSemT processSem;
    volatile bool isResizeRunning;
    DbThreadHandle resizeThreadHandle;
    // 存在并发，resize线程以及其他dfx线程会并发访问。但不必加锁，dfx有延迟是允许的
    volatile uint32_t resizeStatusCode;  // 0:offline, 1:recycle chunk, 2:rehash
    // 只有resize线程才会修改, 当前范围和配置项范围一致，uint32
    uint32_t currBufferpoolSize;
    // 判断是否需要rehash操作的base bufferpoolSize
    uint32_t currRehashBaseSize;
    // 使用原子操作，支持并发DbAtomicGet DbAtomicValCAS64 DbAtomicGet64 DbAtomicSet64
    volatile uint64_t newBufferpoolSize;  // 存在并发，需要加锁
    DbMemCtxT *chunkMemCtx;  // resize需要提前设置maxTotalPhySize，提前占坑，纵向隔离，避免内存被其他模块使用到
    uint64_t limitNewBufferpoolSize;  // 限制最大bufferpoolsize，静态值，初始化后不会变。unit bytes

    // 发生recycle，需要维护记录能够释放的chunkId list, >= targetChunkIdx不能回收
    uint32_t targetChunkIdx;  // 预期resize之后的targetChunk.
    // rehash 需要借助trxMgr获取最大事务id以及最小活跃事务id
    TrxMgrT *trxMgr;
    // 业务线程修改，之后resize线程读取
    bool needRehash;
};

struct ConcurrentPrefetchCtx {
    SeInstanceT *seInsPtr;
    uint32_t preFetchProcId;
    DbListT preFetchFileList;
    volatile bool isPreFetchProcRunning;
    volatile bool isPreFetchProcStopped;
    DbThreadHandle preFetchThread;
};

struct ConcurrentPrefetchInfo {
    uint32_t preFetchThreNum;
    ConcurrentPrefetchCtxT *preFetchThreCtxs;
};

typedef struct DbSpinlockFn DbSpinlockFnT;

typedef struct BpRingBufMgr {
    volatile bool isRingbufRunning;
    DbThreadHandle ringbufThreadHandle;
} BpRingBufMgrT;

typedef struct BufpoolMgr {
    PageMgrT base;
    uint32_t pageSize;
    SeInstanceT *seIns;
    BufPoolT *bufPool;
    uint32_t bufPoolNum;  // 多个bufferpool实例的个数
    BufpoolLRUEnqueueFunc lruEnqueueFunc;
    BufpoolRecycleFunc recycleFunc;
    BufPreprocessFunc preprocessFunc;
    BufSetBufDescListFunc setBufDescListFunc;
    BufDescSwitchLRUListFunc switchLRUListFunc;
    MemUtilsT *memUtils;
    DbSpinlockFnT *seLockFn;
    TableMapFnsT tableMapFns;
    DbInterProcSpinRWLockFnT *seRWLockFn;
    ConcurrentPrefetchInfoT *prefetchInfo;
    DbOamapT *trmIdListMap;  // key is trmId, value is not used.
    bool enableSplitChunk;   // 静态值，取决于chunkSize是否配置为非0，不会动态变化
    BpResizeMgrT *resizeMgr;
    BpRingBufMgrT *ringbufMgr;
    MemUtilsT memUtilsClt;
} BufpoolMgrT;

typedef struct StDataBasePubCtx {
    DbSpinLockT ctrlLock;  // 用于保护ctrl页的并发，所有ctrl页共用一个锁
    ShmemPtrT pagesShm;    // ctrlPage 共享内存
} StDataBasePubCtxT;

typedef struct StCtrlVersion {
    uint16_t main;
    uint16_t major;
    uint16_t revision;
    uint16_t inner;
} StCtrlVersionT;

typedef struct TagSeCtrlPageMgr {
    uint32_t blockHwm;       // ctrl page alloced so far
    uint32_t freeBlockCnt;   // free ctrl page cnt
    uint32_t freeBlockHead;  // first free ctrl page addr
} SeCtrlPageMgrT;

typedef enum EnumSePersistCompMode {
    COMP_MODE_NONE,
    COMP_MODE_PAGE,
    COMP_MODE_DEVICE,
} SePersistCompModeE;

typedef struct SysTblEntry {
    PageIdT heapAddr;
    PageIdT pkIndexAddr;
    PageIdT secIndexAddr;
} SysTblEntryT;
#define FILE_DIGEST_LENGTH 32
typedef struct PersFileDigest {
    uint8_t sha256[FILE_DIGEST_LENGTH];
} PersFileDigestT;

typedef enum EnumStorageStatus {
    // corectrl still in memory[creating phase]/storage is run on memory, this CAN'T be seem in file
    SE_IN_MEMORY_ONLINE,
    SE_ON_DISK_CREATE = SE_IN_MEMORY_ONLINE,
    // db is running, if this is shown in corectrl at reboot, meaning db is shut abnormally
    SE_ON_DISK_ONLINE,
    // db is closed, if this is shown in corectrl at reboot, meaning db is shut normally
    SE_ON_DISK_OFFLINE,
    // when corectrl shows SE_ON_DISK_OFFLINE on reboot, meaning db is shut normally, db would just open it
    SE_ON_DISK_OPEN_LOAD = SE_ON_DISK_OFFLINE,
    // when corectrl shows SE_ON_DISK_ONLINE on reboot, meaning db is shut abnormally, db would go through recover
    // process
    SE_ON_DISK_OPEN_RECOVER,
    // disk file is damaged, all services are denied, this CAN'T be seem in file
    SE_ON_DISK_EMRGNCY,
    // when corectrl shows SE_ON_DISK_BACKING_UP on reboot, meaning this is unfinished backup fileset, abort start
    SE_ON_DISK_BACKING_UP,
    // when corectrl shows SE_ON_DISK_BACK_UP_FULL on reboot, meaning this is finished backup fileset with data
    SE_ON_DISK_BACK_UP_FULL,
    // when corectrl shows SE_ON_DISK_BACK_UP_SCHEMA on reboot, meaning this is finished backup fileset without data
    SE_ON_DISK_BACK_UP_SCHEMA,
    SE_ON_DISK_UPGRADE,
    SE_STATUS_END,
} StorageStatusE;
#define SE_NAMESPACE_MAX 64
#define DB_ENCRYPT_MAC_LENGTH 16
#define CORE_ENCRYPT_SALT_LENGTH 16
#define DB_ENCRYPT_IV_LENGTH 12
#define CORE_ENCRYPT_ENCRYPT_FLAG_LEN 1
#define CORE_ENCRYPT_ENCRYPT_MODEL_LEN 7
#define CORE_ENCRYPT_ENCRYPT_RESERVED_LEN 3
#define CORE_ENCRYPT_CHECK_LENGTH 32
#define CORE_RESERVE_SIZE 199
#define SE_EXTEND_CTRL_ROW_LIST_SIZE 3
typedef struct StCoreCtrl {
    StCtrlVersionT version;
    uint32_t openCount;
    SeCtrlPageMgrT ctrlPageMgr;  // for manage ctrl page alloc & free
    uint32_t spaceCtrlEntry;     // space ctrl entry
    uint32_t fileCtrlEntry;      // file ctrl entry
    uint32_t devCtrlEntry;       // device ctrl entry
    uint32_t extendCtrlEntry;    // 8 byte align
    union {
        uint64_t padding;  // 确保指针跨平台大小一致
        time_t initTime;
    };
    uint16_t fileFormatVersion;  // ctrl文件版本
    uint16_t padding2;
    volatile StorageStatusE coreStatus;
    SePersistCompModeE compMode;
    uint32_t yangModelCheckNspIds[SE_NAMESPACE_MAX];  // yang语义校验中的模型校验使用的namespace id

    // sys tbl
    SysTblEntryT sysUserEntry;
    SysTblEntryT sysRoleEntry;
    SysTblEntryT sysNspEntry;
    SysTblEntryT sysvlEntry;
    SysTblEntryT sysElEntry;
    SysTblEntryT sysYangEntry;
    SysTblEntryT sysNodeEntry;
    SysTblEntryT sysPropEntry;
    SysTblEntryT sysIdxHeapEntry;
#ifdef FEATURE_SQL
    SysTblEntryT sysSqlRebuildHeapEntry;
#endif
    SysTblEntryT sysTsMapHeapEntry;
    uint32_t padding3;
    // ckpt
    RedoPointT truncPoint;
    RedoPointT lrpPoint;
    uint64_t lsn;
    // log

    uint32_t systemSpaceId;
    uint32_t undoSpaceId;
    uint32_t userSpaceId;
    uint32_t tempSpaceId;

    // undo
    PageIdT undoRsegPageId;
    // trx
    uint64_t maxTrxId;
    uint64_t magicNum;

    // encrypt, 从非加密到加密，或者重新加密的场景中以下值可能变化，所以没有放在StCoreCfgT中
    uint8_t hmac[DB_ENCRYPT_MAC_LENGTH];
    uint8_t iv[DB_ENCRYPT_IV_LENGTH];
    uint8_t salt[CORE_ENCRYPT_SALT_LENGTH];                 // 盐值
    uint8_t isEncrypted : CORE_ENCRYPT_ENCRYPT_FLAG_LEN;    // 是否加密
    uint8_t encryptModel : CORE_ENCRYPT_ENCRYPT_MODEL_LEN;  // 加密模型
    uint8_t reserved[CORE_ENCRYPT_ENCRYPT_RESERVED_LEN];
    uint8_t checkEncrypts[CORE_ENCRYPT_CHECK_LENGTH];  // 用于开库时校验密码

    // 不允许修改的关键参数，用于启动校验
    uint32_t persistMode;
    uint32_t deviceSize;
    uint32_t pageSize;
    uint32_t maxSeMem;
    uint32_t dbFilesMaxCnt;
    uint32_t dbFileSize;
    uint32_t redoFileCount;
    uint32_t redoFileSize;
    uint32_t crcCheckEnable;
    uint32_t sharedModeEnable;  // Shared mode enable, true means multi process is supported
    uint32_t storageType;
    uint32_t spaceMaxNum;
    uint32_t encryptReservedEnable;  // 开启后会在每个页页未保留32个字节，用于保存页加密信息

    bool isNormalShutDown;  // 标记优雅重启, 和redoFileDropOnClose打开有关
    bool isDebug;           // 0 release, 1 debug
    uint8_t reserveHolePadding[2];
    uint32_t shaCheckEnable;
    uint32_t ctrlPageSize;
    PersFileDigestT ctrlFileDigest;
    RedoPointT digestRedoPoint;  // 记录文件摘要时的 redo truncate point
    bool tamperProofEnable;

    uint8_t reserve[CORE_RESERVE_SIZE];
} StCoreCtrlT;

typedef struct StFile {
    char *name;
    int32_t handle;
} StFileT;

typedef struct TagSeMultiZoneCtrl {
    DbLatchT lock;
    uint8_t mainZoneId;
    bool zoneOnlineStatus[DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM];
} SeMultiZoneCtrlT;

typedef struct TagCtrlPagePool {
    DbSpinLockT lock;
    DbMemCtxT *memCtx;
    uint32_t pageSize;  // pageSize of ctrl page
    uint32_t poolSize;  // current page pool size, unit: page
    ShmemPtrT *page;
    MemUtilsT *memUtils;
    ProcessInfoMgrT *processMgr;
    ShmemPtrT pagesShm;
} CtrlPagePoolT;

typedef struct TagCtrlGroup {
    DbLatchT latch;
    uint32_t size;
    uint32_t arrSize;
    uint32_t usedCnt;
    uint32_t itemSize;
    DbMemCtxT *memCtx;
    uint8_t **item;
} CtrlGroupT;

typedef struct TagSeExtendCtrlRowMgr SeExtendCtrlRowMgrT;

struct TagSeExtendCtrlRowMgr {
    CtrlGroupT pageGroup;
    TagLinkedListT levelList[SE_EXTEND_CTRL_ROW_LIST_SIZE];
};

typedef struct TagSpcCompressMgrT SpcCompressMgrT;

typedef StatusInter (*SeFlushActPageToCompressFunc)(SeInstanceT *seIns);

typedef struct CompressBlockDesc {
    DbSpinLockT lock;
    bool valid;
    uint8_t reserve[3];
    PageIdT pageId;
    void *page;
    uint16_t pageCompressedSize;  // 页压缩后的大小, 等于pageSize表示未压缩
} CompressBlockDescT;

typedef struct CompressStat {
    uint32_t capacity;
    uint32_t compressedPageCnt;
    uint64_t usedSize;
    uint64_t swapInCnt;
} CompressAreaStatT;

struct TagSpcCompressMgrT {
    uint32_t pageCntPerDev;
    uint64_t compressAreaSize;  // unit: B
    SeFlushActPageToCompressFunc flushActPageToCompressFunc;
    uint8_t *compBuf;
    CompressBlockDescT *compDescs;
    CompressAreaStatT *compressAreaStat;
};

typedef struct TagMemStructMap {
    DbSpinLockT lock;
    DbOamapT map;
} MemStructMapT;

typedef struct TagDbFile DbFileT;
#define DB_MAX_MULTIZONE 2
#define DB_RESERVE_HANDLE_NUM 1
#define DB_MAX_NAME_LEN 32
typedef struct TagDbFileCtrl {
    uint32_t id;
    uint8_t status : 4;  // DbFileStatusE
    uint8_t fileType : 4;
    uint8_t reserve;
    uint16_t offset;
    char name[DB_MAX_NAME_LEN];
    uint32_t spaceId;
    uint32_t pageNum;      // 页面的计数，通过这个计算出文件实际使用的空间
    uint32_t extendNum;    // 扩展的次数, 通过这个计算出文件给数据申请的空间
    uint32_t deviceNum;    // 扩展的次数, 通过这个计算出文件给数据预留的空间
    PageIdT entry;         // 文件head所在页的pageId
    uint8_t reserve1[16];  // 预留16字节，为80字节
#if defined(IDS_HAOTIAN)
    bool customDir;
    char dir[DB_MAX_PATH];
#endif
} DbFileCtrlT;

typedef struct PageList {
    uint32_t count;
    PageIdT first;
    PageIdT last;
} PageListT;

typedef struct TagFreeFileDeviceInfo {
    uint32_t fileId;
    uint32_t blockHwm;  // 8字节对齐
    uint32_t fileIndex;
} FreeFileDeviceInfoT;

typedef struct SpaceHead {
    uint32_t unused;
    uint32_t blockHwm;
    uint32_t lastDeviceId;  // 最后分配的deviceId
    PageListT freePageList;
    uint64_t allocedSize;  // unit:B 扩容阈值, 超过该值触发扩容
    uint64_t usedSize;     // unit:B 当前使用大小
    uint32_t freeDevCnt;
    uint32_t padding;  // 结构体对齐padding
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[128];
#endif
    FreeFileDeviceInfoT freeDevInfo[];
} SpaceHeadT;

typedef struct TagDevCtrl {
    uint32_t id;         // id of this device
    bool online;         // checked if this device is in use
    uint8_t reserve;     // reserve
    uint16_t offset;     // offset from start of pagehead to dev ctrl
    uint32_t fileIndex;  // device index on file
    uint32_t fileId;     // id of file this device belongs to
    uint32_t spcId;      // id of space this device belongs to
    uint8_t reserve1[4];
} DevCtrlT;

typedef StatusInter (*SpaceAllocDeviceFunc)(SeInstanceT *seIns, DbFileT *file, uint32_t *deviceId);
typedef StatusInter (*SpaceAllocDeviceByIdFunc)(SeInstanceT *seIns, DbFileT *file, uint32_t deviceId);
typedef StatusInter (*SpaceFreeDeviceFunc)(SeInstanceT *seIns, DbFileT *file, uint32_t deviceId);
typedef StatusInter (*SpaceAllocPageFunc)(SeInstanceT *seIns, DbFileT *file, PageIdT *addr, bool *isNew);
typedef StatusInter (*SpaceAllocPageByIdFunc)(SeInstanceT *seIns, DbFileT *file, PageIdT addr);
typedef StatusInter (*SpaceFreePageFunc)(SeInstanceT *seIns, DbFileT *file, PageIdT addr);
typedef StatusInter (*SpaceFreePageCountFunc)(SeInstanceT *seIns, DbFileT *file, uint32_t *freePageCount);
typedef StatusInter (*SpaceGetDeviceHwmFunc)(const SeInstanceT *seIns, const DevCtrlT *devCtrl, uint32_t *blockHwm);

struct TagDbFile {
#ifdef IDS_HAOTIAN
    DbRWSpinLockT lock;  // 控制datafile文件的读写
#else
    DbSpinLockT lock;
#endif
    int32_t handle[DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM];  // file handle
    uint32_t blockSize;                                        // block size of this file, unit: B
    uint32_t dataOffset;                                       // data page offset from filehead
    DbFileCtrlT *ctrl;                                         // fileCtrl
    SpaceHeadT *head;
    SpaceAllocDeviceFunc allocDevice;
    SpaceAllocDeviceByIdFunc allocDeviceById;
    SpaceFreeDeviceFunc freeDevice;
    SpaceAllocPageFunc allocPage;
    SpaceAllocPageByIdFunc allocPageById;
    SpaceFreePageFunc freePage;
    SpaceFreePageCountFunc freePageCount;
    SpaceGetDeviceHwmFunc getDeviceHwm;
    uint32_t firstDevId;
    uint32_t lastDevId;
    uint32_t avlDevId;
};

typedef struct StDatabase {
    StDataBasePubCtxT *pubCtx;
    /* 用于控制按需持久化全量刷盘，刷盘期间停止一切存储的操作
     * 事务开启时，会为这个锁加上读锁，事务结束时解锁
     * 刷盘启动时，会为这个锁加上写锁，刷盘结束时解锁
     */
    DbLatchT coreLock;
    bool enable;  // 是否打开持久化能力，为false时临时关闭持久化
    StCoreCtrlT core;
    uint32_t diskBlockSize;

    uint8_t *compBuf;  // used for compress buffer for main task

    uint64_t checkMagic;       // 文件校验魔数字，恢复启动时的校验数，也用于增量刷盘
    uint64_t flushCheckMagic;  // 全量刷盘时使用的文件校验数，每全量刷一次，递增一次
    StFileT safeFile;          // safe file for double write
    DbMemCtxT *memCtx;
    SeMultiZoneCtrlT zoneCtrl;
    CtrlPagePoolT ctrlPagePool;
    CtrlGroupT spaceGroup;
    CtrlGroupT fileGroup;
    CtrlGroupT deviceGroup;
    SeExtendCtrlRowMgrT *extendMgr;
    DbFileT ctrlFile;
    SpcCompressMgrT *spcCompressMgr;
    MemStructMapT memMap;
    bool isCtrlPageInited;  // 多进程场景，ctrl page放在共享内存，只需要第一个进程进行初始化
} StDatabaseT;

typedef Status (*DbCheckFn)(void *args);

typedef Status (*SpinlockLockFn)(DbSpinLockT *lock, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs);

typedef struct DbSpinlockFn {
    SpinlockLockFn lockFn;
    DbCheckFn checkFn;
    uint32_t checkIntervalUs;
    void *checkArgs;
    // default enabled, if disabled, not execute any lock op
    bool enabled;
} DbSpinlockFnT;

typedef StatusInter (*RWLatchLatchWFn)(DbLatchT *latch, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs);

typedef struct {
    RWLatchLatchWFn rLatchFn;
    RWLatchLatchWFn wLatchFn;
    DbCheckFn checkFn;
    uint32_t intervalUs;
    void *checkArgs;
} InterProcRWLatchUtilT;

typedef Status (*SpinRWLockRLockFn)(DbRWSpinLockT *rwlock, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs);

typedef struct DbInterProcSpinRWLockFn {
    SpinRWLockRLockFn rLockFn;
    SpinRWLockRLockFn wLockFn;
    DbCheckFn checkFn;
    uint32_t checkIntervalUs;
    void *checkArgs;
} DbInterProcSpinRWLockFnT;

#pragma pack(4)
typedef struct TagSeInstanceT {
    uint64_t magicNum;
    uint32_t seTopShmMemCtxId;
    uint32_t dataMemMgrCtxId;
    uint32_t heapShmMemCtxId;
    uint32_t seAppShmMemCtxId;
    uint32_t clusteredHashShmMemCtxId;
#ifdef ART_CONTAINER
    uint32_t clusteredArtShmMemCtxId;
#endif
    uint32_t hashIndexShmMemCtxId;
    uint32_t hashLinklistShmMemCtxId;
    uint32_t indexArtShmMemCtxId;
#ifdef FEATURE_SIMPLEREL
    uint32_t indexTTreeShmMemCtxId;
#endif
    uint32_t hcShmMemCtxId;
    uint32_t chainedHashShmMemCtxId;
    uint32_t chainedHashShmMemCtxIdForConflict;
#ifdef FEATURE_HAC
    uint32_t hacShmMemCtxId;
#endif
    uint32_t fixedHeapShmMemCtxId;
    uint32_t stMgPubSubShmMemCtxId;
    uint16_t instanceId;
    bool isLockMgrInited;
    bool undoPurgerStarted;
    bool dafPurgerStarted;
    bool isUpgrading;  // 标记当前是否处于原地升级状态
    uint8_t rsmMigrationVersion;
    uint8_t reserve;
    ShmemPtrT devMgrShm;
    ShmemPtrT rsmBlockMgrShm;
    ShmemPtrT spaceMgrShm;
    ShmemPtrT rsmSpaceMgrShm;
    ShmemPtrT devMemCtxShm;
    ShmemPtrT trxMgrShm;
    ShmemPtrT undoSpaceShm;
    ShmemPtrT lockTblShmPtr;
    ShmemPtrT resColMgrShm;
    ShmemPtrT lockMgrShm;
    ShmemPtrT pageDescArray;
    ShmemPtrT ckptCtxShm;
    ShmemPtrT redoCtxShm;
    ShmemPtrT coreCtrlCtxShm;
    ShmemPtrT bufPoolShm;
    SeTrmIdCtrlT trmIdCtrl;
    void *seServerMemCtx;          // dynamic memory context private to the storage engine instance, only use in server!
    void *seEscapeMemCtx;          // 极限场景下回滚和提交预留内存, 用作逃生通道
    void *undoPurgerCtx;           // undo purger ctx
    void *dafPurgerCtx;            // daf purger ctx
    DbSpinLockT lockMgrInitLatch;  // 保护isLockMgrInited并发更新，控制一个存储实例仅创建一次锁池
    DbSpinLockT purgerStartLatch;  // 保护undoPurgerStarted并发更新，控制一个存储实例仅启动一个purger线程
    DbSpinLockT dafPurgerStartLatch;  // 保护dafPurgerStarted并发更新，控制一个存储实例仅启动一个purger线程
    StorageTypeE storageType;
    RecoveryStateE recoveryState;
    SeConfigT seConfig;
    RedoMgrT *redoMgr;
    CkptCtxT *ckptCtx;        // CkptCtxT
    BufpoolMgrT *bufpoolMgr;  // BufpoolMgrT
    StDatabaseT *db;          // StDatabaseT
    DbInstanceHdT dbInstance;
    void *pageMgr;
    void *mdMgr;     // 内存模式下(mdMgr == pageMgr)
    void *duMemMgr;  // 不为null时表示当前为bufferpool与dumemdata双持久化形态(此时pageMgr为bufpool)，其他情况下为null。
    MemUtilsT memUtils;  // ckpt、redo、coreCtrlPage等使用，minKv共享内存模式为fileMapMemCtx, 其它为seTopMemCtx
    FileMapMgrT *fileMapMgr;              // 非终端不会使用
    DbSpinlockFnT seLockFn;               // DbSpinlockFnT
    InterProcRWLatchUtilT utils;          // InterProcRWLatchUtilT
    DbInterProcSpinRWLockFnT seRWLockFn;  // DbInterProcSpinRWLockFnT
    bool needVfd;
    void *vfdMgr;
#ifdef FEATURE_LPASMEM
    DbOamapT *lpasIndexMap;
    void *adpt;
#endif
    void *loadTableMgr;
    void *trxLatchCtrl;
} SeInstanceT;
#pragma pack()

typedef struct StRedoRunCtx RedoRunCtxT;

typedef struct {
    DbRWSpinLockT lock;           // 保护DbSessionMgr并发
    uint32_t shmCtxId;            // 共享内存上下文ID
    uint32_t firstFreeId;         // 空闲链表头
    uint16_t maxNums;             // session的最大个数
    uint16_t usedCnt;             // 已经使用的session个数
    ShmemPtrT sessionPoolShmPtr;  // session pool的共享内存addr
} DbSessionMgr;

#ifdef LATCH_CONFLICT_DEBUG
typedef struct TagLatchStatR {
    uint32_t readerAverageWaitCount;
    uint32_t readerConflictCount;
    uint32_t readerTotalWaitCount;
    uint32_t readerMaxWaitCount;
} ReaderLatchConflictT;

typedef struct TagLatchStatW {
    uint32_t writerAverageWaitCount;
    uint32_t writerConflictCount;
    uint32_t writerTotalWaitCount;
    uint32_t writerMaxWaitCount;
} WriterLatchConflictT;

#endif

typedef struct {
    uint32_t id;              // 当前id
    uint32_t currAccessDsId;  // 当前使用的delta store id
    uint32_t nextSessionId;   // 下一个空闲session ID
    uint32_t privPolicyMode;  // authentication模式
    uint16_t dwTrxSlot;       // 直连写申请的事务槽id
    ShmemPtrT shmLatchStack;  // 锁栈共享内存addr
    ShmemPtrT shmRefArray;    // 共享内存引用计数数组的共享内存addr
    ShmemPtrT shmRole;
    void *role;        // 角色指针
    bool isFree;       // 当前session是否已经分配
    bool reserved[3];  // 字节补齐
#ifdef LATCH_CONFLICT_DEBUG
    ReaderLatchConflictT readerLatchInfo;
    WriterLatchConflictT writerLatchInfo;  // 统计latch锁冲突信息
#endif
} DbSessionT;

typedef enum TagShmRefType {
    REF_SHM_INVALID = 0,
    REF_SHM_HEAP_CURSOR,            // Heap 游标引用计数
    REF_SHM_CLUSTERED_HASH_CURSOR,  // 聚簇容器游标引用计数
    REF_SHM_MAX
} RefShmTypeE;

typedef struct {
    RefShmTypeE shmType;     // 共享内存引用计数类型
    uint32_t latchOffset;    // 锁在对应结构体上的偏移
    uint32_t openCntOffset;  // 引用计数变量在结构体上的偏移
    uint32_t refCount;       // 实际的open次数
    ShmemPtrT shmAddr;       // 实际共享内存指针
    // 存储表latch的信息，异常恢复时，先加上表latch再处理cursor计数，上面的引用计数++时才需传入
    uint32_t labelLatchVersionId;
    ShmemPtrT labelLatchShmAddr;  // 表latch共享内存addr
} ShmRefItem;

typedef struct TagShmRefDescT {
    uint32_t nextDescId;
    ShmRefItem item;
} ShmRefDescT;
#define DEFAULT_SHMREF_DESC_COUNT 16
typedef struct TagDbShmRefArrayT {
    uint32_t shmCtxId;         // 动态数组用的内存上下文ID
    uint32_t capacity;         // 数组中可以容纳元素的总的个数
    uint32_t curPos;           // 记录当前已经申请到的位置，一定不大于capacity
    uint32_t usedCnt;          // 已经使用的item个数, 一定不大于capacity
    uint32_t firstFreeDescId;  // free 链表的首ID
    uint32_t extendCount;      // 每次扩容时，扩容大小的基数
    uint32_t extendTimes;      // 数组扩充次数
    uint32_t reserve;          // 为了保证extShmAddr(atomic 64操作需要保证8字节对齐)对齐的预留字段
    union {
        ShmemPtrT extShmPtr;  // 当cacheDescs数组不够用时会申请一个新的指针数组，指针数组可以动态扩容。
        uint64_t extShmAddr;  // 这里虽然是4字节对齐，但是不是8字节对齐，实际情况arm32可能会导致core
    };
    ShmRefDescT cacheDescs[DEFAULT_SHMREF_DESC_COUNT];
} DbShmRefArrayT;

typedef enum TagLatchAcqModeE {
    LATCH_ACQ_SPIN,   // 自旋锁
    LATCH_ACQ_READ,   // 读锁
    LATCH_ACQ_WRITE,  // 写锁
} LatchAcqModeE;

typedef enum TagLatchType {
    LATCH_ADDR_INVALID = 0,
    LATCH_ADDR_PAGEID,                       // heap和index读写锁专用锁类型
    LATCH_ADDR_HASH_UNIQUE_INDEX_SHMEM,      // HashTableT     : idxLatch
    LATCH_ADDR_HASH_CLUSTER_INDEX_SHMEM,     // HashCluster     : idxLatch
    LATCH_ADDR_SORTED_INDEX_SHMEM,           // SortedIdxT     : indexLatch
    LATCH_ADDR_LPM_INDEX_SHMEM,              // LpmIndexT      : indexLatch
    LATCH_ADDR_LABELLATCH_RWLATCH_SHMEM,     // LabelRWLatchT  : rwlatch
    LATCH_ADDR_HEAP_CURSOR_LOCK_SHMEM,       // HeapT          : scanCursorLock
    LATCH_ADDR_CLUSTERED_HASH_LABEL_LOCK,    // ClusteredHashLabelT
    LATCH_ADDR_CLUSTERED_HASH_CURSOR_LOCK,   // ClusteredHashLabelT : scanCursorLock
    LATCH_ADDR_STASH_PAGE_SHMEM,             // HashIndexStashPage
    LATCH_ADDR_LABEL_HCLATCH_RWLATCH_SHMEM,  // LabelRWLatchT  : hash cluster latch
    LATCH_ADDR_FSM_MGR_LOCK,                 // heap的fsm的锁
    LATCH_ADDR_TRX_MGR_LOCK,                 // 事务管理trxMgr的锁
    LATCH_ADDR_OBJ_PRIV_RWLATCH_SHMEM,       // 权限信息 DmObjPrivT 的锁
    LATCH_ADDR_AGED_MGR_LATCH_SHMEM,         // 老化任务管理结构的锁
#ifdef FEATURE_HAC
    LATCH_ADDR_ACCELERATOR_LOCK,  // 加速器锁：软件模式属于共享内存，加速器模式属于硬件内存
#endif
#ifdef ART_CONTAINER
    LATCH_ADDR_ART_CONTAINER_LOCK,
#endif
    LATCH_ADDR_MAX
} LatchAddrTypeE;

typedef struct {
#ifdef FEATURE_HAC
    union {
        struct {
#endif
            // 原有共享内存的结构体
            uint8_t *virtAddr;
            ShmemPtrT shmAddr;
#ifdef FEATURE_HAC
        };
        struct {
            // 硬件锁需要的解锁回调函数与回调函数的参数，仅适用于LATCH_ADDR_ACCELERATOR_LOCK
            uint32_t latchPoolId;
            uint32_t latchId;
        };
    };
#endif
    LatchAcqModeE latchAcqMode;
    LatchAddrTypeE latchType;
    uint32_t pid;
} LatchStackItemT;

typedef enum TagStackStatusE {
    STATUS_NORMAL = 0,
    STATUS_ABNORMAL,
} StackStatusE;
#define MAX_LATCH_NUMS 15  // 目前客户端直连读流程最大支持15层嵌套加锁
#define LATCH_STACK_TOP (MAX_LATCH_NUMS + 1)  // 锁栈上限, 当加锁和解锁没有按照出栈入栈方式进行时，预留一把用于缓存.
typedef struct {
    uint32_t stackTop;                       // 栈顶指针
    LatchStackItemT stack[LATCH_STACK_TOP];  // 锁栈，用于记录加锁的类型和模式
    StackStatusE stackStatus;
#ifndef NDEBUG
    uint32_t pid;  // 该锁栈是服务端申请客户端使用，在SeAttachResSessionShareMem设置上客户端的值
#endif
} LatchStackT;

typedef StatusInter (*DbGetPageLatchFunc)(
    void *pageMgr, ShmemPtrT latchAddr, uint8_t **latch, uint32_t option, bool isWrite);
typedef void (*DbLeavePageLatchFunc)(void *pageMgr, ShmemPtrT latchAddr, bool commit);

typedef struct {
    DbSessionMgr *sessionMgr;
    ShmemPtrT *sessionPool;
    DbSessionT *session;
    DbShmRefArrayT *refArray;
    LatchStackT *latchStack;
    ShmRefDescT *lastRefDesc;  // 上一次访问的引用计数item
    uint32_t lastDescId;       // 上一次访问的元素下标, 加速查找
    bool isOpenSession;
    bool isDirectRead;
    bool isDirectWrite;
    bool reserved;
    void *pageMgr;
    DbGetPageLatchFunc getPageLatchFunc;
    DbLeavePageLatchFunc leavePageLatchFunc;
} DbSessionCtxT;

typedef struct TagSeRunCtxT {
    bool isInited;
    bool isPersistence;           // 是否持久化模式，用于性能优化
    uint16_t instanceId;          // the instance id of storage
    uint32_t trxLockTimeOut;      // 连接级别的事务锁超时时间（ms）
    DbSessionCtxT resSessionCtx;  // DbSessionCtxT
    void *sessionMemCtx;          // session (dynamic) memory ctx
    void *reserveMemCtx;          // 紧急场景下的备用 memory ctx，默认赋值为 seEscapeMemCtx
    void *seIns;                  // the storage instance of handle, SeInstanceT
    void *trxMgr;
    void *trx;
    void *undoCtx;
    void *redoCtx;
    void *pageMgr;
    void *mdMgr;  // 内存模式下(mdMgr == pageMgr)
    void *lockAqcPool;
    void *lockNotifyPool;
    void *lockTable;
    void *rsmCtx;  // 保留内存rsmem ctx
    void *activeTrxIds;
    uint32_t trxIdsSize;
    bool ddlRegistered;
    bool swapCtx;
} SeRunCtxT;

struct TagSeRunCtxT;
typedef struct TagSeRunCtxT *SeRunCtxHdT;

typedef enum EnumHeapOperationType {
    HEAP_OPTYPE_INSERT = 0,  // 顶点的插入场景 : page访问 写锁; fetch加行锁S, 读取最新数据
    HEAP_OPTYPE_DELETE,      // 顶点的删除场景 : page访问 写锁; fetch加行锁X, 读取最新数据
    HEAP_OPTYPE_UPDATE,  // 顶点的更新场景, 包括 边的更新首边addr的场景: page访问 写锁; fetch加行锁X, 读取最新数据
    HEAP_OPTYPE_UNDO_PURGER,  // 顶点的回收或者回滚流程: page访问: 根据读写场景加锁; 不加行锁(undo线程加事务锁,
                              // 或者回滚流程已经有事务锁)
    HEAP_OPTYPE_REPLACE_INSERT,  // 同 HEAP_OPTYPE_INSERT (推送场景区分, 其他一致)
    HEAP_OPTYPE_REPLACE_UPDATE,  // 同 HEAP_OPTYPE_UPDATE (推送场景区分, 其他一致)
    HEAP_OPTYPE_MODIFY,          // ---- 分界线 ---- 上面是修改场景, 下面是只读访问场景
    HEAP_OPTYPE_DIRECTREAD,  // 顶点直连读: page访问 读锁; 不加行锁; 读取最新已经提交的数据; [唯一一个不用事务的场景]
    HEAP_OPTYPE_NORMALREAD,  // 顶点 c/s模式的访问: page访问 读锁, 不加行锁; 需要事务, 根据readView 读取对应版本的数据
    HEAP_OPTYPE_EDGE_READ,  // 边的访问 : page 访问 读锁, 加行锁S; 读取最新数据
    HEAP_OPTYPE_NONE_OP,    // YANG场景的none操作，不会修改数据，用于定位到叶子节点
    HEAP_OPTYPE_MAX_TYPE
} HpOpTypeE;

typedef enum ContainerType {
    CONTAINER_HEAP,
    CONTAINER_CLUSTERED_HASH,
    CONTAINER_ART,
    CONTAINER_INVALID
} ContainerTypeE;

typedef enum EnumHeapPageType {
    HEAP_FIX_LEN_ROW_PAGE = 0,
    HEAP_VAR_LEN_ROW_PAGE = 1,
    HEAP_INVALID_PAGE_TYPE
} HpPageTypeE;

typedef enum EnumTupleType {
    HEAP_TUPLE_TYPE_VERTEX = 0,
    HEAP_TUPLE_TYPE_KV,
    HEAP_TUPLE_TYPE_INVALID,
} TupleTypeE;

typedef uint16_t PageSizeT;
typedef uint16_t SeInstanceIdT;

typedef struct TagHeapAccessCfgT {
    HpPageTypeE pageType;  // 目前先提供 HEAP_VAR_LEN_ROW_PAGE 的类型
    TupleTypeE tupleType;
    PageSizeT fixRowSize;  // pageType = HEAP_FIX_LEN_ROW_PAGE 时需要指定, 外面传进来的大小(未包含管理结构的开销)
    PageSizeT slotExtendSize;  // 在每个slot后面扩展的空间，FixPage和VarPage分配方式不同，详见这两种页的PageHead中的注释
    SeInstanceIdT seInstanceId;  // 共享内存申请需要使用该id
    bool isYangBigStore;         // 表是否是yang场景大对象
    bool isStatusMergeSubs;  // 标记是否是新订阅的表，新订阅的表可能会出现逻辑计数大于物理计数的临时情况
    bool isPersistent;             // 是否持久化（持久化模式下生效，临时表为false）
    bool isLabelLockSerializable;  // 串行化隔离级别下是否需要加表锁
    bool isUseRsm;                 // 是否走保留内存
#ifdef FEATURE_GQL
    bool skipRowLockPessimisticRR;  // cfg designed for FES, default: false
#endif
    ConcurrencyControlE ccType;  // 并发控制的类型
    TrxTypeE trxType;            // 表的事务类型
    IsolationLevelE isolation;   // 表的事务隔离级别
    uint32_t labelId;            // label Id
    uint32_t heapFileId;         // file node id, -> 持久化到文件, id_heapData.file
    uint32_t heapFsmFileId;      // file node id, -> 持久化到文件, id_heapFSM.file
    uint32_t tableSpaceId;       // 记录表所属的tableSpace的Id
    uint32_t tableSpaceIndex;    // 在device层, 所属tableSpace的下标
    uint32_t fsmTableSpaceIndex;  // 用于保留内存模式下，fsm页不落保留内存，表示其在device层所属共享内存的tableSpace下标
    uint64_t maxItemNum;  // 最大允许插入的记录数.
} HeapAccessCfgT;

typedef struct {
    DbSpinLockT lock;
    bool isInit;
    uint8_t reserved[3];
    HeapAccessCfgT cfg;
    ShmemPtrT lockStatShmPtr;
    ShmemPtrT heapShmAddr;
} HeapJumpT;

typedef struct TagHeapConstInfo {
    HpPageTypeE pageType;  // 目前先提供 HEAP_VAR_LEN_ROW_PAGE 的类型
    TupleTypeE tupleType;
    PageSizeT slotExtendSize;  // 在每个slot后面扩展的空间，FixPage和VarPage分配方式不同，详见这两种页的PageHead中的注释
    uint32_t heapShmMemCtxId;
    bool isYangBigStore;  // 表是否是yang场景大对象
    bool isStatusMergeSubs;  // 标记是否是新订阅的表，新订阅的表可能会出现逻辑计数大于物理计数的临时情况
    bool isPersistent;             // 是否持久化（持久化模式下生效，临时表为false）
    bool isLabelLockSerializable;  // 串行化隔离级别下是否需要加表锁
    bool verboseDfx;               // 全量dfx（持久化暂不支持）
    bool isUseRsm;                 // 是否走保留内存
#ifdef FEATURE_GQL
    bool skipRowLockPessimisticRR;  // cfg designed for FES, default: false
#endif
    ConcurrencyControlE ccType;  // 并发控制的类型
    TrxTypeE trxType;            // 表的事务类型
    IsolationLevelE isolation;   // 表的事务隔离级别
    uint32_t labelId;            // label Id
    uint32_t heapTrmId;          // file node id, -> 持久化到文件, id_heapData.file
    uint32_t heapFsmTrmId;       // file node id, -> 持久化到文件, id_heapFSM.file
    uint32_t tableSpaceId;       // 记录表所属的tableSpace的Id
    uint32_t tableSpaceIndex;    // 在device层, 所属tableSpace的下标
    uint32_t fsmTableSpaceIndex;
#ifdef FEATURE_PERSISTENCE
    // 避免影响纯内存态小型化指标，持久化宏隔离
    uint32_t padding;  // 8字节对齐
#endif
    uint64_t maxItemNum;            // 最大允许插入的记录数.
    PageSizeT maxRowRawSizeInPage;  // 页内最长的行的大小(不包括行头)

#ifdef FEATURE_PERSISTENCE
    // 避免影响纯内存态小型化指标，持久化宏隔离
    uint8_t padding2[6];  // 8字节对齐
#endif
} HeapConstInfoT;

typedef struct StaticPageInfo {
    bool isUseCache;  // MainStoreVertexLookUpHashOpt场景为true
    uint8_t reserve;  // 用于占位
    uint16_t rowCnt;
    PageSizeT oneRowOffset;        // oneRowSize + slotExtendSize
    PageSizeT firstRowHeadOffset;  // rowBegin + slotExtendSize
} StaticPageInfoT;

typedef struct TagHeapFixRowPageCfgT {
    PageSizeT oneRowSize;  // 行头+数据+字节对齐的大小
    PageSizeT rawRowSize;  // 数据真实长度
    uint16_t rowCnt;       // 表示分配的slot个数; (使用 fix size 对 page 进行 整除划分)
    PageSizeT slotExtendSize;  // 每个slot扩展空间大小，目前仅存储HashCluster链表指针，布局：slotExtend|rowHead|buf|
    PageSizeT rowBegin;  // HFPageHeadT rowBegin 才是是实际的 row的开始
    uint16_t reserve;
    PageSizeT pageReserveSize;  // 分配row后每张页剩余的大小
} HFPageCfgT;

typedef struct TagHeapRegularInfo {
    uint32_t maxRowSize;
    HFPageCfgT fixPageCfg;  // 定长page场景, 保存计算好的page划分情况, 存储的page上就不用保存这些信息
#ifdef FEATURE_PERSISTENCE
    // 避免影响纯内存态小型化指标，持久化宏隔离
    uint8_t padding[6];  // 8字节对齐
#endif
    uint64_t lastTruncateTrxId;  // 记录truncate操作时的最新trxId，用于处理purge线程还没来得及处理的undoLog
    uint64_t curRecCnt;
} HeapRegularInfoT;

typedef struct FsmList {
    uint32_t fsmPageIdx;
    uint32_t slotId;
    uint32_t listLen;
    uint32_t reserve;
} FsmListT;
#define FSM_PAGE_ID_SIZE 16  // 29 - FSM_SLOT_ID_SIZE
#define FSM_SLOT_ID_SIZE 13
typedef struct FsmListNode {
    uint64_t listId : 6;
    uint64_t prevFsmPageIdx : FSM_PAGE_ID_SIZE;  // 前一个slot的fsm页id
    uint64_t prevSlotId : FSM_SLOT_ID_SIZE;      // 前一个slot的在对用数组id
    uint64_t nextFsmPageIdx : FSM_PAGE_ID_SIZE;  // 后一个slot的fsm页id
    uint64_t nextSlotId : FSM_SLOT_ID_SIZE;      // 后一个slot的在对用数组id
    uint32_t dataPageId;                         // page desc id of this block
    uint16_t maxRowSize;  // 当前page的最大行的size，只记录历史最大版本（删除后不更新）
    uint16_t freeSize;    // 目前page size最大64K，去掉页头等损耗，uint16_t足够
} FsmListNodeT;

typedef uint32_t PageTotalSizeT;  // 64K的页，表示总量时需要32位，否则会溢出

typedef struct LfsConfig {
    uint32_t fsmFileId;         // fileId of FreeSpaceMapping(FSM) page, not data page
    uint32_t dataFileId;        // fileId of data page
    uint32_t shmArrayMemCtxId;  // memctx for lfs shmarray
    uint32_t labelId;
    PageTotalSizeT pageSize;  // pageSize of FSM page, <= 64KB
    HpPageTypeE pageType;     // pagetype of data page, fix or var
    bool needLock;
    bool isUseRsm;
    uint16_t reserveSize;  // FixType:if page's freeSize < reserveSize, page's freeSize = 0;VarType:fixSize of row
    uint32_t availSize;    // availSize of data page, page Size - pageHead - pageTail
    uint32_t tableSpaceIndex;
    uint32_t fsmTableSpaceIndex;
} LfsCfgT;

typedef struct FsmFragmentedInfo {
    int32_t lastFragmentedListId;
    uint32_t lastFragmentedFsmListIdx;
    uint32_t lastFragmentedPageId;
} FsmFragmentedInfoT;
#define FSM_VAR_LIST_COUNT 8
#define FSM_SLOT_NUM_IN_MGR 10
#define MAX_LV2_FSM_PAGE_NUMS 6u
typedef struct LabelFreeSpaceMgr {
    uint32_t fsmPageCnt;   // the number of fsm page, not data page
    uint32_t lv1TableCnt;  // 单表持有 lv1 table 的数量
    uint32_t lv2TableCnt;  // 单表持有 lv2 table 的数量
    uint32_t lastFsmPageId;
    uint32_t maxBlockCnt;
    uint16_t maxSlotPerFsmPage;  // if pageSize >= 64KB * sizeof(FsmListNodeT), change maxSlotPerFsmPage to uint32_t
    uint16_t listRange[FSM_VAR_LIST_COUNT];  // range of 7 level list
    uint16_t padding1;                       // 结构体对齐padding
    FsmListT fsmList[FSM_VAR_LIST_COUNT];
    FsmListNodeT slotArr[FSM_SLOT_NUM_IN_MGR];  // 内存优化，少量数据页时不申请FSM页，在此扩展slot中记录
    LfsCfgT cfgInfo;
    uint32_t releasedPageCnt;
    uint32_t freePageCnt;
    uint32_t usedPageCnt;
    FsmFragmentedInfoT fragmentedInfo;
    // In persistent, it can be recovered like [memdata] or just traverse all fsm page in disk
    uint32_t sysPageSize;
    uint32_t fsmPageIdNumsInMgr;
    uint32_t maxSlotPerFsmTablePage;  // 一个 page table 所能存储的slot数量
    uint32_t allFsmPageNum;           // 单表所能拥有的最大fsm page数量
    uint32_t lastUpperPageId;         // 只在请求页时用来缓存上一次DescId
    uint32_t padding2;                // 结构体对齐padding
    union {
        uint64_t paddingForPointer;  // 确保指针跨平台大小一致
        uint8_t *lastUpperPageAddr;  // 只在请求页时用来缓存上一次addr使用
    };
    PageIdT lv2TablePageId[MAX_LV2_FSM_PAGE_NUMS];
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[16];
#endif
    PageIdT fsmPageIds[];
} LfsMgrT;

typedef struct {
    uint32_t magicNum;
#ifdef FEATURE_PERSISTENCE
    // 避免影响纯内存态小型化指标，持久化宏隔离
    uint32_t padding;  // 结构体对齐padding
#endif
    HeapConstInfoT constInfo;
    HeapRegularInfoT regularInfo;
    ShmemPtrT runtimeInfoShmPtr;
    ShmemPtrT perfStatShmPtr;
    ShmemPtrT lockStatShmPtr;  // 事务锁冲突率视图, 内存小型化要求, 这个内存延后或者按需申
    // 持久化且非终端场景预留字段
#if defined(FEATURE_PERSISTENCE) && !defined(FEATURE_SQL)
    uint8_t reserve[16];
#endif
    LfsMgrT fsm;
} HeapT;

typedef struct TagScanCursorOpener {
    uint64_t pid;
    uint64_t threadId;
    uint64_t openTime;
} ScanCursorOpenerT;

typedef struct TagPageAddrT {
    uint32_t blockId;
    uint32_t pageId;
} PageAddrT;

typedef struct PageAddrInfo {
    PageAddrT pageAddr;
    union {
        uint64_t padding;
        PageHeadT *pageHead;
    };
} PageAddrInfoT;
#define HEAP_SCAN_CURSOR_OPENER_TOP_3 3
typedef struct TagHeapRuntimeInfo {
    DbSpinLockT scanCursorLock;    // 控制 totalCursorNum 变量的并发
    DbSpinLockT heapLazyInitLock;  // 原HeapT中lock，控制perfStatShmPtr并发初始化的锁
    uint32_t totalCursorNum;       // 处理后台碎片整理线程与业务扫描线程的互斥
    uint32_t allocCursorFailTimes;
    uint32_t cacheVersion;
    bool cachePageAvail;  // 缓存页可用
    bool cursorOpenerInit[HEAP_SCAN_CURSOR_OPENER_TOP_3];
    ScanCursorOpenerT cursorOpener[HEAP_SCAN_CURSOR_OPENER_TOP_3];
    // 缓存上一次申请的页addr信息，pageAddr和pageHead需配套使用，客户端不能直接使用pageHead，需根据pageAddr重新获取页
    PageAddrInfoT cachePageAddr;
    PageTotalSizeT cachePageFreeSize;  // 缓存的页上还有多少的可用空间
    PageSizeT cachePageMaxRowSize;     // 记录缓存页上的最大行大小，归还FSM需要用
    uint8_t rsmMigrationVersion;
    uint8_t reserve;
    // 如果有DbAtomicAdd64访问，需要8字节对齐
    uint64_t historyLinkRows;  // 记录累计出现过的跳转行的数量，STORAGE_HEAP_STAT视图查询时返回
} HeapRuntimeInfoT;

typedef struct TagHeapPerfOperationStaticsT {
    uint64_t cnt;
    uint64_t failCnt;
    uint64_t totalCost;
    uint64_t minCost;
    uint64_t maxCost;
} HpPerfOpStatT;

#ifndef NDEBUG
#define SE_HEAP_PERF
#endif

typedef enum EnumHeapPerfOperationType {
    HEAP_OP_INSERT = 0,
    HEAP_OP_DELETE,
    HEAP_OP_UPDATE,
    HEAP_OP_FETCH,
    HEAP_OP_FETCHNEXT,
    HEAP_OP_REPLACE_INSERT,
    HEAP_OP_REPLACE_UPDATE,
    HEAP_OP_MAX_TYPE
} HpPerfOpTypeE;

typedef struct TagHeapPerfStatT {
    DbSpinLockT lock;  // 用于保护HeapPerfStatT结构体中成员变量的读写并发
    uint32_t metaPageCnt;
    uint64_t phyItemNum;       // heap物理存储的item计数，包含未提交的数据
    uint64_t realItemNum;      // 乐观事务已提交的数据计数
    uint64_t writeBytes;       // 写入的总字节数，包含未提交的字节
    uint64_t deleteBytes;      // 删除的总字节数，包含未提交的字节
    uint64_t phyItemSize;      // 物理所占空间大小
    uint64_t historyLinkRows;  // 累计出现过的跳转行的数量，只增不减，truncate可置0
#ifdef SE_HEAP_PERF
    HpPerfOpStatT opStat[(int32_t)HEAP_OP_MAX_TYPE];
#endif
    uint64_t lastDefragTaskTime;  // 上一次触发整理任务的时间
    uint64_t defragmentationCnt;  // 统计压缩的次数
    uint64_t lastDefragTime;      // 上一次真正进行整理的时间
    double fragmentationRate;     // 碎片率
    uint32_t heapFileId;
    uint32_t pageCnt;
} HeapPerfStatT;

typedef struct LockConflictStat {
    uint64_t trxLockAcquireCount;  // 加锁总数
    uint64_t conflictCount;        // 锁冲突数
    uint64_t lockEscalationCount;  // 锁升级总数 X锁不涉及此项
    uint64_t lockEscalationFail;   // 锁升级失败数 X锁不涉及此项
    uint64_t conflictAcqFail;      // 锁冲突后加锁失败
    uint64_t timeout;              // 锁冲突后因超时未拿到锁数
    uint64_t deadlockRollback;     // 锁冲突后因死锁而回滚的事务数
} LockConflictStatT;
#define SE_LOCK_TYPE_NUM 3
#define SE_LOCK_ACQ_TYPE_NUM 4
typedef struct LockStat {
    DbSpinLockT lock;
    uint64_t tupleLockEscalateToLabelLock;
    LockConflictStatT lockConflictStat[SE_LOCK_TYPE_NUM][SE_LOCK_ACQ_TYPE_NUM];
} LockStatT;

struct TagFsmRunCtx {
    SeRunCtxT *seRunCtx;
    PageMgrT *pageMgr;
    LfsMgrT *fsmMgrPtr;
    PageIdT fsmMgrAddr;
    uint32_t offset;
    bool enableCache;
    bool enableRedo;
    bool enableLatch;
    bool directAccess;
};

typedef struct TagFsmRunCtx FsmRunCtxT;

typedef enum DmLabelType {
    VERTEX_LABEL,
    EDGE_LABEL,
    LABEL_SUBSCRIPTION,
    PATH_SUBSCRIPTION,
    RESOURCE_POOL,
    DOCUMENT_TABLE,
    MINI_KV_TABLE,
    KV_TABLE,
    PRIVILEGE,
#ifdef FEATURE_GQL
    COMPLEX_PATH,
    ONESHOT_SUB,
#endif
    LABEL_TYPE_NUMS,
} DmLabelTypeE;

typedef struct TagHeapDmDetailT {
    ShmemPtrT heapShmAddr;
    void *dmInfo;
    uint32_t dmUuid;
    DmLabelTypeE labelType;
} HeapDmDetailT;

struct TagHeapOpCtxT;
typedef struct TagHeapOpCtxT *HpRunHdlT;
typedef void *Handle;
typedef struct TagHeapDeSrlObjHdlT {
    Handle handle;
} HeapDeSrlObjHdlT;  // Heap DeSerial Object Handle

typedef struct TagHeapConstBuf {
    uint32_t bufSize;
    void *constBuf;  // 该addr的内存不应该被修改! 一般是存储page上的addr
} HeapConstBufT;

typedef struct TagHeapDmIndexIteratorT {
    uint32_t iterId;               // 内部使用: dm索引遍历迭代id. 在多个二级索引场景下有意义
    uint32_t dmUuid;               // 索引的全局id
    void *dmIndex;                 // 索引的data module句柄
    ShmemPtrT idxShmAddr;          // 索引的共享内存入口
    uint32_t indexType;            // 索引的类型
    bool isIterInited;             // 内部使用: 迭代器是否初始化
    bool isGet;                    // 用户判断是否获取到有效的索引信息
    bool isUniq;                   // 索引是否唯一
    bool needCheckIndexSatisfied;  // 是否需要过滤
} HeapDmIndexIterT;

typedef Status (*HeapAmSerialProc)(
    const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, HeapTupleBufT *serialBuf);
typedef void (*HeapAmFreeSrlBufProc)(
    const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, HeapTupleBufT *serialBuf);
typedef Status (*HeapAmDeSerialProc)(
    const HpRunHdlT heapRunHdl, HeapConstBufT *serialBuf, HeapDeSrlObjHdlT *deSrlObjHdl);
typedef Status (*HeapAmFreeDeSrlObjProc)(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT deSrlObjHdl);
typedef Status (*HeapAmExtraKeyBySerialBufProc)(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl,
    HeapConstBufT *serialBuf, uint32_t indexId, HeapTupleBufT *buf);
typedef void (*HeapAmGetCmpKeyBufProc)(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl, HeapTupleBufT *buf);
typedef Status (*HeapAmExtraKeyByDeSrlObjHdlProc)(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl,
    uint32_t indexId, uint8_t **keyBuf, uint32_t *length);
typedef void (*HeapAmGetDmDetailProc)(const HpRunHdlT heapRunHdl, HeapDmDetailT *dmDetail);
typedef void (*HeapAmGetDmPriKeyProc)(const HpRunHdlT heapRunHdl, HeapDmIndexIterT *dmPriKeyInfo);
typedef void (*HeapAmGetDmNextSndKeyProc)(const HpRunHdlT heapRunHdl, HeapDmIndexIterT *dmSndKeyInfo);
typedef Status (*HeapAmIsKeyPropeIncludeNullProc)(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl,
    uint32_t iterId, const HeapTupleBufT *tupleBuf, bool *keyPropeIncludeNull);
typedef void (*HeapAmVertexInitDmIndexInfoByIdxProc)(
    const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT emptyObjHdl, HeapDmIndexIterT *dmKeyInfo, uint32_t *labelId);

typedef struct TagHeapAccessMethodForDataModuleT {
    HeapAmSerialProc serialFun;
    HeapAmFreeSrlBufProc freeSrlBufFun;
    HeapAmDeSerialProc deSerialFun;
    HeapAmFreeDeSrlObjProc freeDeSrlObjFun;
    HeapAmExtraKeyBySerialBufProc extraKeyBySerialFun;
    HeapAmExtraKeyBySerialBufProc extraListUniqueKeyBySerialFun;
    HeapAmGetCmpKeyBufProc getCmpKeyBufFun;
    HeapAmExtraKeyByDeSrlObjHdlProc extraKeyByDeSerialFun;
    HeapAmExtraKeyByDeSrlObjHdlProc extraListUniqueKeyByDeSerialFun;
    HeapAmGetDmDetailProc getDmDetailFun;
    HeapAmGetDmPriKeyProc getDmPriKeyFun;
    HeapAmGetDmNextSndKeyProc getDmNextSndKeyFun;
    HeapAmIsKeyPropeIncludeNullProc isKeyPropeIncludeNullFun;
    HeapAmVertexInitDmIndexInfoByIdxProc getDmIdxIterFun;
} HeapAmForDmT;

typedef enum EnumHeapAccessMethodType {
    HEAP_ACCESS_METHOD_NORMAL = 0,
    HEAP_ACCESS_METHOD_LITE,
    HEAP_ACCESS_METHOD_LITE_BG,
    HEAP_ACCESS_METHOD_RSM_LITE_BG,  // 只在保留内存下使用，与LITE_BG的区别是undo走轻量化事务
    HEAP_ACCESS_METHOD_NUM = 4,      // 目前有4套钩子函数
} HpAccessMethodE;

typedef enum EnumHeapTrxAccessMethodType {
    HEAP_TRX_AM_INSERT = 0,
    HEAP_TRX_AM_DELETE,
    HEAP_TRX_AM_MARK_DELETE,
    HEAP_TRX_AM_MARK_DELETE_ROLLBACK,
    HEAP_TRX_AM_UPDATE,  // HEAP_TRX_AM_UPDATE_ROLLBACK update回滚时采用相同的钩子函数
    HEAP_TRX_AM_NORMALREAD,
    HEAP_TRX_AM_MAX_TYPE
} HpTrxAmTypeE;

typedef struct TuplePointer {
    uint32_t pageId;
    uint32_t slotId;  // page上的slot id，实际只使用12bit，保留20bit闲置
} TuplePointerT;

typedef void (*HeapAmSetLockAction)(HpRunHdlT heapRunHdl);

typedef TuplePointerT HpItemPointerT;

typedef StatusInter (*HeapAmAcquireRowLock)(HpRunHdlT heapRunHdl, HpItemPointerT itemPtr);

typedef struct TagHeapPageRowOperationInfoT *HpPageRowOpInfoHdlT;

typedef struct HeapRowInfoFromPartialUpdateUndo {
    uint16_t partDataLen;
    uint16_t reserve;
    uint32_t offsetOfRawData;
    uint8_t *partDataBuf;
} HeapRowInfoFromPrtUpdUndoT;

#pragma pack(8)
// 定义该结构主要原因是避免不对齐的读写. page页面是 4 字节对齐的, 但是 TrxIdT 是 8 字节;
typedef struct TagRowTransactionIdT {
    uint32_t trxIdH;  // 当前事务Id高32位
    uint32_t trxIdL;  // 当前事务Id低32位
} RowTrxIdT;

typedef struct TagRowTransactionInfoT {
    RowTrxIdT trxId;
    uint64_t rollPtr;  // 该条记录的上一个最新版本，存放在undo log中
} RowTrxInfoT;
#pragma pack()

typedef struct FsmDataPageHead {
    PageHeadT pageHead;
    uint32_t slotIdx;  // 根据LfsGetBlockIdByFsmPageInfo计算得出
    uint32_t padding;  // 8字节对齐下，隐式padding
} FsmDataPageHeadT;

#define PAGE_SIZE_T_RESERVE 1u
#define PAGE_SIZE_T_OFFSET 15u

typedef struct TagHeapPageRowSlotT {
    PageSizeT isFree : PAGE_SIZE_T_RESERVE;
    PageSizeT offset : PAGE_SIZE_T_OFFSET;
} HpRowSlotT;

typedef struct TagHeapVarRowPageHeadT {
    FsmDataPageHeadT baseHead;
    HpRowSlotT freeSlotHead;  // 删除slot后, 会挂在 freeSlotHead 的链表上, 每个 slot实际指向下一个已经删除的 slot
    uint16_t slotCnt;      // 表示已经分配的slot个数;
    uint16_t freeSlotCnt;  // 表示空闲的slot个数;
    uint16_t slotExtendSize;  // 每个slot扩展空间大小，每个slot后面扩展该size的空间，目前仅存储hashCluter链表指针
    PageSizeT maxModifyRowOffset;  // 页面压缩时，需要搬迁的数据行的offset上界
    PageSizeT slotDirEnd;          // page偏移: [sizeof(HVPageHeadT), slotDirEnd-1] 是 slot dictionary;
                                   // 后面是连续空闲空间的起始位置
    PageTotalSizeT freePosEnd;     // page偏移: [slotDirEnd, freePosEnd-1] 是 连续的空闲空间
                                   // page偏移: [freePosEnd, ...) 是row数据
                                   // 当page没有row时, freePosEnd 就是 下一个page的起始addr了.
                                   // 由于最大支持64Kb, 所以最大是 0x10000
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[8];
#endif
} HVPageHeadT;

typedef struct TagHeapFixRowPageHeadT {
    FsmDataPageHeadT baseHead;
    PageSizeT nextFreeSlotId;   // 指向下一个空闲的 slot, 删除slot后, 会挂在 nextFreeRow 的链表上
    uint16_t freeSlotCnt : 15;  // 表示空闲的slot个数
    bool isUpgNotSatisfy : 1;  // 升级时若页面不满足升级条件，会将页面freeSize置为0防止下次升级再对该页判断。
                               // 该标志位表示当前页面因不满足升级条件而把freesize置为0
    HFPageCfgT pageCfg;
    uint8_t padding[6];  // 结构体对齐padding
} HFPageHeadT;

typedef union TagHeapPageHeadPtrT {
    PageHeadT *pageHead;
    HVPageHeadT *varPageHead;
    HFPageHeadT *fixPageHead;
} HpPageHeadPtrT;

typedef struct TagHeapRowStateT {
    bool isDeleted : 1;  // 表示 事务 标记删除
    bool isLinkSrc : 1;
    bool isLinkDst : 1;
    bool isSliceHead : 1;
    bool isSliceSub : 1;
    bool isExist : 1;          // 效果等同于bitmap, 表示行是否被alloc, 只有heapfix使用
    bool reservePadding1 : 1;  // 结构体对齐padding
    bool reservePadding2 : 1;  // 结构体对齐padding
} HpRowStateT;

typedef struct TagHeapNormalFixRowHeadT {
    HpRowStateT rowState;  // 当 bitmap 上有效时, row有效;
    uint8_t magicNum;
    PageSizeT nextFreeSlotId;  // 当 bitmap 上无效时, row无效(空闲, 被删除)时, 指向下一个 空闲的 row
    PageSizeT size;            // 不包含头部, 表示row后面存储的实际长度
    PageSizeT reserve1;
    RowTrxInfoT trxInfo;  // row 有效时;
} HpNormalFixRowHead;

typedef struct TagHeapLinkSrcFixRowHeadT {
    HpRowStateT rowState;  // 当 bitmap 上有效时, row有效;
    uint8_t magicNum;
    PageSizeT nextFreeSlotId;  // 当 bitmap 上无效时, row无效(空闲, 被删除)时, 指向下一个 空闲的 row
    uint16_t reserve1;
    PageSizeT bufSize;    // 存储dstRow实际存的bufSize大小
    RowTrxInfoT trxInfo;  // row 有效时;
    HpItemPointerT linkItemPtr;
} HpLinkSrcFixRowHead;

typedef struct TagHeapLinkDstFixRowHeadT {
    HpRowStateT rowState;  // 当 bitmap 上有效时, row有效;
    uint8_t magicNum;
    PageSizeT nextFreeSlotId;  // 当 bitmap 上无效时, row无效(空闲, 被删除)时, 指向下一个 空闲的 row
    PageSizeT size;            // 不包含头部, 表示row后面存储的实际长度
    PageSizeT reserve1;
    HpItemPointerT linkItemPtr;
    RowTrxIdT trxId;
} HpLinkDstFixRowHead;

typedef struct TagHeapNormalRowHeadT {
    HpRowStateT rowState;  // rowState 所有bit都是0, 则是一个 normal row
    uint8_t magicNum;
    PageSizeT size;                 // 不包含头部, 表示row后面存储的实际长度
    PageSizeT rollbackReserveSize;  // 不包含头部, 表示为保障本行undo流程而预留的最大长度
    uint16_t reserve;
    RowTrxInfoT trxInfo;
} HpNormalRowHeadT;

typedef struct TagHeapLinkDstInfoT {
    RowTrxIdT trxId;
    PageSizeT size;  // 不包含头部, 表示row后面存储的实际长度
    PageSizeT reserved;
} HpLinkDstInfo;

typedef struct TagHeapLinkRowHeadT {
    HpRowStateT rowState;  // 当isLinkSrc==1, linkOffSet和linkBlockId 表示linkDts的位置. 当isLinkDts时,
                           // 则表示linkSrc的位置
    uint8_t magicNum;
    uint16_t reserve[2];
    PageSizeT rollbackReserveSize;  // 不包含头部, 表示为保障本行undo流程而预留的最大长度
    uint32_t bufSize;               // 存储dstRow（包含分片）实际存的bufSize大小
    HpItemPointerT linkItemPtr;
    union {
        RowTrxInfoT srcTrxInfo;  // 只有 isLinkSrc==1时, rowHead有事务信息;(该场景, row size固定)
        HpLinkDstInfo dstInfo;   // 只有 isLinkDst==1时, rowHead有size等信息;
    };
} HpLinkRowHeadT;

typedef struct TagHeapSliceSubRowHeadT {  // 分片子行
    HpRowStateT rowState;                 //
    uint8_t magicNum;
    PageSizeT size;  // 不包含头部, 表示row后面存储的实际长度
    HpItemPointerT sliceDirRowPtr;
    uint32_t sliceIdx;
    RowTrxIdT trxId;
} HpSliceSubRowHeadT;

typedef union TagHeapRowHeadPtrT {
    HpRowStateT *rowState;
    HpNormalFixRowHead *normalFixRowHead;
    HpLinkSrcFixRowHead *linkSrcFixRowHead;
    HpLinkDstFixRowHead *linkDstFixRowHead;
    HpNormalRowHeadT *normalRowHead;
    HpLinkRowHeadT *linkRowHead;
    HpLinkRowHeadT *sliceDirRowHead;  // 目录行的格式, 也是 linkDst.
    HpSliceSubRowHeadT *sliceSubRowHead;
} HpRowHeadPtrT;

typedef enum EnumHeapPageRowType {
    HEAP_FIX_ROW_NORMAL_ROW = 0,
    HEAP_FIX_ROW_LINK_SRC_ROW,
    HEAP_FIX_ROW_LINK_DST_ROW,
    HEAP_VAR_ROW_NORMAL_ROW,
    HEAP_VAR_ROW_LINK_SRC_ROW,
    HEAP_VAR_ROW_LINK_DST_ROW,
    HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW,
    HEAP_VAR_ROW_SLICE_SUB_ROW,
    HEAP_INVALID_ROW_TYPE,
} HpPageRowTypeE;

typedef struct TagHeapRowInfoT {  // 注意: 新增page相关的字段, 记得修改 HeapCopyRowInfoForAllocInSamePage
    HpItemPointerT itemPtr;
    uint32_t itemBlockId;  // 在扫描场景下，对于normal行和src行，记录blockId，用来校验扫描顺序的一致性
    bool isNeedCheckBlockIdAndPageState;
    bool isMarkDeleted;
    uint8_t reserved[2];  // pad
    HpPageHeadPtrT pageHeadPtr;
    PageIdT pageAddr;
    PageSizeT slotOffset;  // var page 场景 是页尾部的 HpRowSlotT 的偏移; fix page 场景 是 实际 row的偏移
    PageSizeT reserved2;  // pad
    HpRowSlotT *slot;     // noteb: 适配fix page
    HpRowHeadPtrT rowHeadPtr;  // 读当前page的时候, 在page锁下, 指向有效的rowaddr. 读历史版本是, 指向日志中addr.
    HpPageRowTypeE rowType;
    PageSizeT rawRowSize;           // 行的原始大小. 不包括行头
    PageSizeT rollbackReserveSize;  // 不包含头部, 表示为保障本行undo流程而预留的最大长度
} HeapRowInfoT;

typedef struct TagHeapSliceRowDirEntryT {
    HpItemPointerT sliceSubRowPtr;
} HpSliceRowDirEntryT;

typedef struct TagHeapSliceRowDir {
    HpItemPointerT prevItemPtr;
    HpItemPointerT nextItemPtr;
    uint32_t totalSize;  // 当前目录行指向的所有分片行总数据大小
    uint32_t sliceRowNum;
    HpSliceRowDirEntryT sliceRowEntries[];
} HpSliceRowDirT;

typedef struct HeapPageSliceRowCursor {
    uint32_t totalSize;
    bool isSubRowSearchFailed;
    HpSliceRowDirT *sliceDir;
    uint32_t bufPos;
} HpPageSliceRowCursorT;

// Type of transaction operations
typedef enum UndoRowOperationType {
    TRX_OP_INSERT = 0xa,
    TRX_OP_UPDATE,
    TRX_OP_UPDATE_PART,
    TRX_OP_DELETE,
    TRX_OP_CREATE,
    TRX_OP_TRUNCATE,
    TRX_OP_DROP,
    TRX_OP_RES_COL_ALLOC,
    TRX_OP_RES_COL_RELEASE,
    TRX_OP_CH_INSERT,
    TRX_OP_CH_UPDATE,
    TRX_OP_IDX_BUILD,
    TRX_OP_CU_RENAME,
    TRX_OP_RELABEL,
} UndoRowOpTypeE;

typedef struct HeapTrxPrevVersionRowInfo {  // 用于判断能否合并undoLog
    bool hasPrevVersion;  // 是否存在本事务操作过的前一版本，该字段为true时以下字段才有意义
    bool isOnMasterVersion;    // 前一版本是否在masterVersion上
    bool isInsertUndoLogType;  // 前一版本的undo的undoLog类型是否是insert
    bool hasLinkDstRow;        // 前一版本是否有跳转行/分片行
    uint32_t prevSavePointId;  // 前一版本的undo的savePointId
    uint32_t bufSize;          // 前一版本的bufSize
    uint16_t reserve[3];
    uint64_t undoRollPtr;        // 前一版本记录的undo的addr
    HpItemPointerT linkItemPtr;  // hasLinkDstRow为true时，记录前一版本的dstRow的addr
    // isOnMasterVersion为false时,需要在undo版本链上更新，需要记录下后一版本的信息
    bool isSkipUndoIndex;   // 后一版本的信息维护
    UndoRowOpTypeE opType;  // 后一版本的信息维护
    uint32_t savePointId;   // 后一版本的信息维护
} HpTrxPrevVersionRowInfoT;

typedef struct TagHeapPageFetchRowInfoT {
    /* 首行的信息 */
    HeapRowInfoT srcRowInfo;  // 读取操作, 最初的读取信息，srcRow是唯一可能有历史版本的行
    /* 分行, 跳转行的信息 */
    HpPageSliceRowCursorT sliceRowCursor;  // 如果该行关联了分行, 此处是临时申请内存, 保存分行信息.
    HeapRowInfoT dstRowInfo;  // 当首行没有直接存储内容, 而是跳转信息时, 需要二次以上的跳转 (比如跳转行, 分行场景)
                              // 注意的是, 读取过程中, srcRowInfo信息是稳定的,
                              // 后续每次跳转的 curRowInfo 利用 dstRowInfo 存储, 所以 dstRowInfo 里面的内容不是稳定的.
    HpTrxPrevVersionRowInfoT prevVersionRowInfo;  // 本事务操作过的本条记录的前一版本的信息
    /* 读取流程的 当前信息 */
    HeapRowInfoT *curRowInfo;    // 取上面记录信息的addr, 用来记录保存
    HpItemPointerT nextItemPtr;  // 本行是分行或者跳转行, 准备下个访问的addr
    /* 控制 的标记 */
    bool canAccessLinkDst : 1;  // 是否可以读 dst，用来控制能否直接读dst行还是必须通过src行跳转去读
    bool canAccessSubRow : 1;  // 是否可以读 sub，用来控制能否直接读sub行还是必须通过src行跳转去读
    bool isOnlyReadSrcRow : 1;         // 表示是否只读src, 默认false时需要访问 dst
    bool isCanAccessMarkDelRow : 1;    // 表示 可以访问 标记删除的行, 主要用于回滚流程的访问
    bool isFindPrevVersionInfo : 1;    // 表示，是否需要找到本事务操作的前一版本的信息
    bool isToGetOldestVisibleBuf : 1;  // yang场景getDiff使用(与canAccessSelfDeleted不会同时为true来使用),
                                       // 获取本条记录对本事务的最老可见版本
    bool isCanAccessSelfDeletedBuf : 1;  // yang场景getDiff使用, 可以读取本事务自己删除的版本
    bool isReadLatestCommittedBuf : 1;  // 在RR事务下，为了感知并发冲突，需要读取最新已提交数据
    bool isUndoOperationOk : 1;  // 记录undo操作是否成功，避免失败流程与回滚流程重复统计DFX
    /* 当前读取获取到 行的 buffer 信息 */
    bool is2LinkDstRow : 1;  // 表示 是否有跳转行
    bool isSliceRows : 1;    // 表示 是否有分片
    bool isReadFromPrtUpdUndo : 1;
    bool isReadFromUndo : 1;  // 表示 是否是历史版本(从undo中获取)
    bool isGetBuf : 1;
    bool isSelfDelBuf : 1;  // yang场景getDiff使用, 可以读取本事务自己删除的版本, 第1个可见版本是否是自己删除的
                            // 标记删除有可能是记录的其他事务未提交版本信息，需要继续往后再读一个版本
    bool reserve : 1;
    RowTrxInfoT rowTrxInfo;  // 读取时, 该行的 事务信息, 此处直接拷贝保存
    uint64_t targetRollPtr;  // 回滚场景设置使用，判断是否还需要读出dstRow的信息 UNDO_INVALID_ROLLPTR
    uint32_t bufSize;
    uint32_t reserve1;
    HeapRowInfoFromPrtUpdUndoT partDateFromUndo;  // isReadFromPrtUpdUndo 为true时使用

    void *buf;
    void *lobBuf;  // 大对象buffer
                   // 从部分更新undo读取行内容时，lobBuf存放还原后的历史版本
} HpPageFetchRowInfo;

typedef enum EnumUndoBypassTypeForUpdate {
    UNDO_OP_NORMAL = 0,               // 正常记录undoLog的情况
    UNDO_OP_BYPASS_TO_INSERT_RECORD,  // undoLog合并，且合并到insert操作的undoLog中
    UNDO_OP_BYPASS_TO_UPDATE_RECORD,  // undoLog合并，且合并到update操作的undoLog中
} UndoBypassTypeForUpdE;

typedef struct HpPageSliceRowInfo {
    bool isSliceRow;
    HpItemPointerT linkSrcRowPtr;   // 首行空间不够时, 可能存在跳转. 所以slice场景, 对外是 linkSrc;
                                    // 该字段, 在更新场景, 非slice row的跳转场景, 也使用;
    HpItemPointerT sliceDirRowPtr;  // linkDst行
    uint32_t sliceDirMemLen;        // 分片目录的内存长度
    uint32_t sliceRowNum;           // 分片个数
    uint32_t curSliceRowIdx;        // 当前处理的分片
    uint32_t reserved;
    HpSliceRowDirT *sliceDir;  // 动态内存临时保存目录
#ifdef FEATURE_PERSISTENCE
    uint32_t dataSize;  // 该分片目录保存的所有分片行数据大小总和
    HpItemPointerT nextSliceDirPtr;
    HpItemPointerT prevSliceDirPtr;  // 以上字段为大对象的分片目录行相关，与非持久化场景兼容，不作为单独结构体
    uint32_t dirRowNum;           // 分片目录行个数
    uint32_t curDirRowIdx;        // 当前处理的分片目录
    uint32_t bufSize;             // 大对象大小
    uint32_t subRowNumInLastDir;  // 最后一个分片目录的分片行个数
#endif
} HpPageSliceRowInfoT;

typedef struct TagHeapPageAllocRowInfoT {
    const void *buf;
    uint32_t bufSize;
    PageSizeT storeSize;
    PageSizeT requireSize;
    RowTrxInfoT newTrxInfo;
    uint16_t slotExtendSize;  // 在每个slot后面扩展的空间，FixPage和VarPage分配方式不同，详见这两种页的PageHead中的注释
    bool markDeletedState;  // 乐观事务下回滚场景使用，将其他事务设置的标记删除状态复原，其他场景默认为false
    bool isNewSlot;
    UndoBypassTypeForUpdE undoBypassType;  // update操作时，undoLog是否能够合并
    bool isUndoOperationOk;  // 记录undo操作是否成功，避免插入失败流程与回滚流程重复统计DFX
    bool isAllowLinkRowInOnePage;  // undoLog合并场景，有可能是往版本链写，此时允许src与dst在同一页
    bool isPartialUpdate;          // 部分更新场景，目前仅合并订阅删除转更新操作使用
    bool reserve;
    uint32_t offsetOfRawData;  // 仅isPartialUpdate为true时生效
    uint32_t flags;
    HeapRowInfoT newRowInfo;           // 新申请row的信息
    HpPageSliceRowInfoT sliceRowInfo;  // 申请分片场景
} HpPageAllocRowInfoT;

typedef struct TagHeapPageRowOperationInfoT {
    HpPageFetchRowInfo *fetchRowInfo;
    HpPageAllocRowInfoT *allocRowInfo;
    uint32_t updInPageOldStoreSize;
    bool hasUpdateRowInfo;  // 页内数据是否已经更新, 仅用于update流程
    uint8_t padding[3];
    uint32_t flags;
} HpPageRowOpInfoT;

typedef StatusInter (*HeapAmLogUndo)(
    HpRunHdlT heapRunHdl, uint32_t opType, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl, uint64_t *newRollPtr);

typedef StatusInter (*HeapAmSaveTrxInfo)(
    HpRunHdlT heapRunHdl, uint32_t opType, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl, uint64_t newRollPtr);

typedef void (*HeapAmRecover)(HpRunHdlT heapRunHdl, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl);

typedef struct TagHeapAccessMethodForTrxT {
    HeapAmSetLockAction setLockFunc;          // 设置锁、可见性等相关变量
    HeapAmAcquireRowLock acquireRowLockFunc;  // 判断锁升级、上锁等操作
    HeapAmLogUndo logUndoFunc;                // 记录undo
    HeapAmSaveTrxInfo saveTrxInfo;            // 保存事务信息
    HeapAmRecover recoverFunc;                // 恢复至正常状况的操作
} HeapAmForTrxT;

typedef struct {
    /* 在heap流程中设置的，不用reset */
    HpTrxAmTypeE hpAmIndex;  // heapAmForTrxFun的下标
    bool isAddRowLockOnFetch;  // 表示是否需要在heap内部的fetch row前加锁; 加锁的模式为  rowLockModeOnFetch
    /* isAcquireLockByTryOnce 字段为了避免上层的latch和事务锁在多个事务见互相依赖导致死锁:
     * 索引访问heap的 row1 时, 一般是持有latch的, 如果此时加事务锁需要等待(其他事务持有该行的锁), 而持有 row1的事务
     * 可能需要访问索引 此时, 就构成循环依赖, 为了避免该场景, 当持有上层 latch 和 事务锁 有交织场景, 使用 try
     * 的方式获取事务锁 try失败时, 释放latch, 并重试 */
    bool isAcquireLockByTryOnce;  // 事务锁的加锁模式时可以 try模式
    bool isRowLockExist;  // 表示是否已经获取了事务锁:注意,修改isRowLockExist为true时, 也要设置isNeedReadView
    bool isNeedReadView;  // RR模式下使用，如果 false, 表示直接访问master version, 否则需要检查readView
    uint32_t rowLockModeOnFetch;  // fetch 流程, 对行加锁; 枚举 SeLockModeE: SE_LOCK_MODE_MAX 表示不用加锁
    uint32_t reserve2;
    /* HeapAllocRunctx后不会变的（一个连接只缓存一个表） */
    const HeapAmForTrxT *heapAmForTrxFun;
} HeapTrxControlT;

typedef struct {
    /* HeapAllocRunctx后不会变的（一个连接只缓存一个表） */
    ConcurrencyControlE ccType;  // 并发控制的类型
    HpAccessMethodE accessMethodType;
    bool isSkipUndoIndex;  // 在yang场景中，若此标志位为true则更新边addr时不记录undo日志(有条件)，事务提交/回滚
                           // 不会去处理索引，但要处理dfx(dm做了边压缩，bufSize会改变)
    bool isToGetOldestVisibleBuf;    // yang场景getDiff使用(与canAccessSelfDeleted不会同时为true来使用),
                                     // 获取本条记录对本事务的最老可见版本
    bool isCanAccessSelfDeletedBuf;  // yang场景getDiff使用, 可以读取本事务自己删除的版本
    bool isReadLatestCommittedBuf;   // 在RR事务下，为了感知并发冲突，需要读取最新已提交数据
    bool isBackGround;
    bool isFixPage;                // 是否是定长page场景
    bool isNeedRowLock;            // 是否需要加事务行锁
    bool isLabelLockSerializable;  // 串行化隔离级别下是否需要加表锁
    /* 在heap流程中设置的，不用reset */
    bool isPageReadOnly;  // 访问heap的page, 是否只读
    // isRsmRecovery为true表示是保留内存的恢复(包含回滚/提交)流程，
    // 恢复扫描流程需要重置页latch、trxId、系统字段等，需要skip rsmUndo的生成
    // 如果事务回滚/提交，可能会重复执行(异常前执行一次，恢复后执行一次)，通过该标志位skip一些校验、rsmUndo记录等
    bool isRsmRecovery;
    bool isReadTargetNeighbor;  // V1兼容 TTREE读需要读目标版本的前一版本用于更新物理addr
    bool isNeedMarkDelete;      // V1兼容 是否需要标记删除 表锁+乐观RR，需要heap标记删除
    HeapTrxControlT trxContrl;
} HeapControlT;

typedef struct TransactionalContainerCtxHead {
    uint32_t id;                   // 对应类型的容器的 id. 比如 对应heap容器, id是 labelId.
    uint16_t type;                 // TrxContainerHdlTypeE 枚举
    uint16_t isGetLabelXLock : 1;  // 是否获取了对应的事务排他锁
    uint16_t isUsed : 1;  // 当前事务已经在使用，由使用容器的调用方设置，避免实际未使用，需要在提交时处理
    uint16_t needOpenHeapHdl : 1;
    uint16_t isFree : 1;  // 对应的资源是否已被释放，在未使用且未释放时，会释放容器保存的资源
    uint16_t isOpened : 1;  // 资源有Open类操作，标记在提交时执行Close
    uint16_t reserve : 11;
    void *parentMemCtx;  // undo场景使用的MemCtx, 表示该容器是从哪个MemCtx申请出来的
} TrxCntrCtxHeadT;

typedef struct TagTrxHeapStatT {
    bool isBytesChange;
    bool reserve[3];
    uint32_t insertNum;    // 事务对表项的写入记录数
    uint32_t deleteNum;    // 事务对表项的删除记录数
    uint64_t writeBytes;   // 事务对表项写入的字节数
    uint64_t deleteBytes;  // 事务对表项删除的字节数
} TrxHeapStatT;

typedef struct CallBackStruct {
    void (*callBack)(void *);  // callBack函数
    void *parameter;           // callBack函数入参
} CallBackStructT;

typedef struct IndexCtx IndexCtxT;
typedef TupleAddr HpTupleAddr;  // Heap label 中 heapTuple 的逻辑addr;
typedef struct IndexKey {
    uint8_t *keyData;
    uint32_t keyLen;
    uint32_t prefixPropeNum;  // the prefix property number corresponding to keyData
} IndexKeyT;

typedef Status (*IdxKeyCmpFunc)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch);
typedef Status (*IdxAddrCheckFunc)(const Handle heapRunHdl, HpTupleAddr addr, bool *isExist);
typedef Status (*IdxAddrCheckAndFetchFunc)(
    IndexCtxT *idxCtx, const HpRunHdlT heapRunHdl, HpTupleAddr addr, bool *isExist);
typedef Status (*IdxExprEvalValuesFunc)(
    void *expr, DmVertexT *vertex, DmValueT *values, uint32_t *valNum, uint32_t *valBufSize);

typedef struct HeapTransactionalCtx {
    TrxCntrCtxHeadT ctxHead;
    TrxHeapStatT opStat;  // 统计事务读写操作，用于在提交回滚进行判断或者回滚统计
    ShmemPtrT heapShmAddr;
    HpRunHdlT heapRunHdl;
    CallBackStructT commitCallback;  // 事务提交时的处理, 提供给存储以外的模块(QE/EE)注册使用; 注意: 该回调不应该失败!
    void *dmInfo;
    void *vertex;  // 事务回滚, 在索引比较场景使用. noteb: 后续应该 在索引容器的上下文 维护?
    IdxKeyCmpFunc hashCompare;  // 事务回滚, 在索引比较场景使用. noteb: 后续应该 在索引容器的上下文 维护?
    IdxAddrCheckFunc addrCheck;
    IdxAddrCheckAndFetchFunc addrCheckAndFetch;
    IdxExprEvalValuesFunc exprValuesEval;  // 表达式计算时用到
} HeapTrxCtxT;

typedef enum EnumSeLockMode {  // 锁模式的定义是从宽松到严格的, 比较 是否持有更加高级的锁时, 可以直接使用 大小比较完成
    SE_LOCK_MODE_IS,
    SE_LOCK_MODE_IX,
    SE_LOCK_MODE_S,
    SE_LOCK_MODE_X,
    SE_LOCK_MODE_MAX,
} SeLockModeE;

typedef struct {
    HpItemPointerT lastPageLogicPtr;    // 上次访问的page的逻辑addr (性能优化)
    void *lastPageHeadPtr;              // 上次访问的addr的page addr(vir)
    HpItemPointerT lastRowLogicPtr;     // 上次访问的行的逻辑addr(性能优化)
    HpItemPointerT hcLastPageLogicPtr;  // 以下四个为额外供hashcluster使用的缓存
    void *hcLastPageHeadPtr;
    void *hcFirstPageHeadPtr;
    ShmemPtrT hcLastPageShmAddr;
    HpItemPointerT hcLastRowLogicPtr;
    SeLockModeE lastRowLockMode;  // 缓存上次的加锁模式 (性能优化)
    uint32_t lastPageBlockId;
    uint32_t cacheVersion;
} HeapPageCacheT;

typedef struct TagSePageLatchAcquireInfo {
    uint32_t trmId;     // 预期的page对应的file
    uint32_t blockId;   // 预期的page对应的blockId
    uint32_t pageId;    // 预期的page对应的pageId
    uint8_t latchType;  // 对应是 读锁/写锁
    uint8_t pageType;  // 对应是 data/undo page，undo页为访问历史版本数据使用的，不加锁，仅在持久化下控制Get&Leave周期
    bool strictCheck;  // 是否需要严格校验到blockId也一致、PageBaseHeadT->pageState
    bool foldLog;      // 超数组时是否需要屏蔽错误日志
    PageHeadT *baseHead;  // 页面指针
} SePageAcqInfoT;

typedef struct TagSePageAcquireState {
    bool carryOver;
    bool relatch;  // 对上层的出参, 表明当前页是否有触发过relatch, 不能用于流程控制(上层可能不会清理该标记)
    bool latched;  // 是否已经加锁, 设置该标志用于 reset, free场景时,
    bool reserve;
} SePageAcqStateT;

typedef struct TagSePageLatchInfo {
    SePageAcqInfoT acqInfo;
    SePageAcqStateT state;
    uint32_t reserve;
} SePageLatchInfoT;

#define SE_PAGE_ORDER_MAX_PAGE_NUM 5

typedef struct TagSePageLatchSorterT {
    SePageLatchInfoT pageInfo[SE_PAGE_ORDER_MAX_PAGE_NUM];
    uint8_t pageNum;
    uint8_t relatchNum;
    uint8_t reserve[6];
} SePgLatchSorterT;

typedef struct TagHeapRollBackInfoT {
    TrxIdT oldTrxId;      // 用于回滚场景, 保存row设置上个版本的事务id
    uint64_t oldRollPtr;  // 用于回滚场景, 保存row设置上个版本的 undo roll ptr
    bool oldIsDeleted;    // 乐观事务下，用于回滚场景, 保存row设置上个版本的isDeleted
    bool reserve[7];
} HpRollBackInfo;

typedef Status (*SimpleIdxUpd)(HpTupleAddr addr, void *ttreeIdxUpdCtx, void *ctx);

typedef struct HeapCompV1Info {
    void *realInsertPtr;  // insert操作写入的物理addr, 更新大对象时为fetch插入获取到的物理addr
    void *realUpdateOrDelPtr;  // Update操作新增Undo链的物理addr
    void *targetPointer;  // 目标版本物理addr，目标版本的前一版本物理addr存buf(通常是master版本)
    void *masterPointer;  // 主版本物理ptr
    uint64_t targetRollPtr;  // 目标版本的逻辑addr
    void *undoReclaimPhyAddr;  // undo回收时数据在page的物理addr，大对象link src row，normal row为实际数据
    void *ttreeIdxUpdCtx;
    IndexCtxT **idxCtxArray;
    SimpleIdxUpd updFun;
    bool isFetchByTTree;  // TTree通过大对象src row head Fetch时不校验可见性
    bool reserve[2];
} HeapCompV1InfoT;

typedef struct TagHeapOpCtxT {
    /* HeapAllocRunctx后不会变的（一个连接只缓存一个表） */
    ContainerTypeE containerType;
    SeRunCtxT *seRunCtx;
    HeapJumpT *heapJump;  // 小型化需求，非持久化下，新增heapJump
    PageMgrT *pageMgr;
    HeapConstInfoT heapCfg;
    StaticPageInfoT staticPageInfo;
    ShmemPtrT heapShmAddr;
    ShmemPtrT heapJumpShmAddr;
    HeapT *heap;
    ShmemPtrT runtimeInfoShmPtr;
    HeapRuntimeInfoT *runtimeInfo;
    ShmemPtrT perfStatShmPtr;
    HeapPerfStatT *perfStat;
    ShmemPtrT lockStatShmPtr;
    LockStatT *lockStat;
    union {
        FsmRunCtxT *fsmRunCtx;  // heap使用
        LfsMgrT *fsmMgr;        // heap_mem使用
    };
    HeapDmDetailT dmDetail;  // 保存一份dmDetail，加速HeapAmGetDmDetailInfo直接返回
    const HeapAmForDmT *heapAmForDmFun;
    void *usrMemCtx;
    /* 需要通过reset来复用的(事务生命周期内的) */
    HeapControlT hpControl;
    HpOpTypeE hpOperation;
    uint32_t cursorNum : 30;
    uint32_t enableCache : 1;
    uint32_t enableCacheHeap : 1;
    HeapTrxCtxT *heapTrxCtx;  // 当前事务对应的上下文 (事务内, 同一个heap多次打开, 均对应同一个 heapTrxCtx)
    HeapPageCacheT pageCache;
    SePgLatchSorterT pageSorter;
    HpRollBackInfo rollBackInfo;
    HeapCompV1InfoT compV1Info;  // Used by Compatible V1
#ifdef FEATURE_SQL
    DmIndexTypeE backUpdateIdxType;
#endif
    uint16_t *offset;
    bool isTryGetPage;
} HeapRunCtxT;

typedef struct LpasPqAddrInfo {
    PageIdT slotId;
    uint32_t offset;
} LpasPqAddrInfoT;

typedef Status (*IdxSetPqIdFunc)(IndexCtxT *idxCtx, LpasPqAddrInfoT pqInfo);

typedef struct KeyCmpRes {
    int32_t cmpRet;
    bool isMatch;
    bool isSelfDelete;  // 是否是本事务删除的buff
} KeyCmpResT;

typedef struct TagHeapHacInfo {
    uint32_t heapTrmId;
    uint16_t rowBegin;    // 定长固定头部长度
    uint16_t oneRowSize;  // 定长tuple大小,含heap
    uint16_t fixRowHead;  // 定长tuple大小,不含heap
    uint16_t rowCnt;
    uint16_t slotExtendSize;
    uint16_t rawRowSize;
    uint16_t trmOffset;
    uint16_t existOffset;  // 预留给existBit做偏移计算
    uint8_t existBitMask;
    bool isFixPage;              // 定长
    ConcurrencyControlE ccType;  // 并发控制的类型
} HeapHacInfoT;

typedef Status (*IdxMultiVersionKeyCmpFunc)(
    IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, KeyCmpResT *keyCmpRes);
typedef Status (*IdxKeyCmpBufFunc)(
    IndexCtxT *idxCtx, IndexKeyT idxKey, const HeapTupleBufT *hpBuf, int32_t *cmpRet, bool *isMatch);
typedef HeapHacInfoT (*IdxGetHacInfoFunc)(const HpRunHdlT heapRunHdl);

typedef struct IdxCallBackFunc {
    IdxKeyCmpFunc keyCmp;
    IdxMultiVersionKeyCmpFunc multiVersionKeyCmp;
    IdxKeyCmpBufFunc hpBufCmp;
    IdxAddrCheckFunc addrCheck;
    IdxAddrCheckAndFetchFunc addrCheckAndFetch;
    IdxGetHacInfoFunc getHacInfo;
    IdxExprEvalValuesFunc exprValuesEval;
#ifdef FEATURE_SQL
    IdxSetPqIdFunc idxSetPqIdFunc;
#endif
} IdxCallBackFuncT;

typedef struct ClusteredHashLabelRunCtx *ChLabelRunHdlT;

#define DB_MAX_PROC_NAME_LEN 16

typedef Status (*ChLabelKeyCmpFunc)(
    Handle hcRunCtx, IndexKeyT idxKey, const HeapTupleBufT *heapTupleBuf, int32_t *cmpRet, bool *isMatch);

typedef struct TagLabelLatchT {
    uint32_t itemHead;  // 放到结构体首位, 放回shmArray加入空闲链表时，要用首32位记录下一个item的id
#ifndef IDS_HAOTIAN
    DbLatchT rwlatch;
    ShmemPtrT rwlatchShmptr;
#else
    DbRWSpinLockT rwlock;  // 数存场景使用，高并发下DbLatchT性能较差，替换为DbRWSpinLockT优化性能
    ShmemPtrT rwlockShmptr;
#endif
    uint32_t ruModeNum;
    volatile uint32_t versionId;
    bool isAddHcLatch;        // 是否在EE层加锁，当前在RU模式同时hashCluster索引数量大于0为true
    DbLatchT hcLatch;         // 锁当前表的hash cluster索引
    ShmemPtrT hcLatchShmPtr;  // hash cluster share memory addr，提供给客户端使用
    uint32_t bgTaskLock;  // 后台缩容任务必须和老化任务，降级任务串行，简单起见所有都串行
#ifndef NDEBUG
    uint32_t ruPid;  // DEBUG模式辅助定位，哪个进程持有ruModeNum锁。
    char ruProcName[DB_MAX_PROC_NAME_LEN];
#endif
} LabelRWLatchT;

typedef struct ChLabelOpenCfg {
    ContainerTypeE containerType;
    ShmemPtrT labelShmAddr;
    SeRunCtxHdT seRunCtx;
    void *usrMemCtx;
    void *vertexLabel;
    void *vertex;
    TupleBufT *tupleBuf;
    void *userData;
    void *indexKeyInfo;
    uint32_t labelLatchVersionId;
    uint32_t secIndexNum;
    IndexCtxT **secIdxCtx;
    ChLabelKeyCmpFunc keyCmp;
    LabelRWLatchT *labelRWLatch;
    bool isUseRsm;
} ChLabelOpenCfgT;

typedef struct TagChLabelCfgT {
    uint32_t seInstanceId;
    uint32_t initIndexCap;
    uint32_t indexId;
    uint32_t labelId;
    uint64_t maxItemNum;  // 容器可以存储的最大
    PageSizeT fixRowDataSize;
    PageSizeT slotExtendSize;  // 每个hashEntry额外预留一些空间, 用于存放一些逻辑addr指针, 如hashCluster索引
    ConcurrencyControlE ccType;  // 并发控制的类型
    uint32_t tableSpaceId;
    uint32_t tableSpaceIndex;
    bool isUseRsm;
} ChLabelCfgT;

typedef struct DashEhMemMeta {
    uint32_t hashSegBits;
    uint32_t hashEntryNumPerPage;
    uint32_t hashSegNumPerPage;
    uint32_t scaleInCnt;      // 缩容次数，每1s成功缩容一次，约需要136年才会达到计数上限值
    uint32_t dirNextItemIdx;  // 遍历dir page过程中，上一次遍历结束时的dir的idx
    uint32_t dirCurItemIdx;   // 搬迁升降级数据遍历dir page过程中，当前需要探测的dir的idx
    uint32_t dirCurItemCnt;   // 搬迁升降级数据遍历dir page过程中，当前dir已经遍历的次数
    uint32_t hashEntrySize;
} DashEhMemMetaT;

typedef struct TagDbShmArrayT {
    uint32_t count;            // 所有extent数组内的总的item个数
    uint32_t curPos;           // 记录当前已经申请到的位置，一定小于count
    uint32_t used;             // 已经使用的item个数
    uint32_t firstFreeDescId;  // free 链表的首ID
    uint32_t itemSize;         // 单个元素的大小，最小4字节，最大64字节
    uint32_t eachExtCount;     // 每个数组拥有的item数。
    uint32_t arrayMemCtxId;    // 动态数组用的内存上下文
    uint32_t instanceId;       // 实例ID，用于多实例
    uint32_t extCount;         // 描述extent数组内的个数，段数组内有多少个段
    uint32_t extUsed;          // 描述extent使用到段数组个数，段数组内用了多少个段
    ShmemPtrT extShmPtr;  // 一个指针数组，初始化时，会初始一个8个的段数组，每个段数组存储了capctiy个数的item。
    ShmemPtrT firstExt;  // 记录第一个段，加速普通场景
    bool isFree;         // 是否被释放
} DbShmArrayT;

typedef struct HashMemMgrBase {
    DbShmArrayT hashSwizzleArray;
    uint64_t version;
    bool isVertexUseRsm;
    int32_t seInstanceId;
    uint32_t fileId;
    uint32_t tableSpaceIndex;
} HashMemMgrBaseT;

typedef struct ClusteredHashMemMgr {
    HashMemMgrBaseT base;
    DashEhMemMetaT dashMemMeta;
} ClusteredHashMemMgrT;

typedef struct TagClusteredHashPerfStatT {
    uint64_t phyItemNum;        // 物理存储的item计数，包含未提交的数据
    uint64_t writeBytes;        // 表项写入的字节总数
    uint64_t deleteBytes;       // 表项删除的字节总数
    uint64_t chLastDefragTime;  // 记录上一次碎片整理的时间
    uint64_t deleteNum;         // 表项的删除记录数
    uint64_t heapItemNum;       // heap项记录数
    uint64_t chInsertCnt;       // 聚簇容器主键插入次数
    uint64_t chCollisionCnt;    // 聚簇容器主键冲突次数
} ClusteredHashPerfStatT;

typedef struct ChLabelPageMetaData {
    uint32_t pageRowSize;
    uint32_t tupleSize;
    uint16_t bucketPos;
    uint16_t stashBucketPos;
    uint16_t tuplePos;
    uint16_t tupleCntPerPage;
    uint16_t hashNormalBucketNumPerPage;
    uint16_t maxTupleCntPerOverFlowPage;
    uint32_t firstEntryPosPerOverFlowPage;
    uint32_t firstTuplePosPerOverFlowPage;
    PageIdT nextPageAddr;
} ChLabelPageMetaDataT;

typedef struct DashEhLabelMeta {
    uint32_t bucketSize;
    uint32_t availPageSize;
#ifndef NDEBUG
    uint32_t scaleInUpgradePageCnt;
#endif
    uint32_t scaleInProbCnt;
    uint32_t scaleInPageCnt;
    uint32_t overflowPageTupleUsed;
    uint16_t bitMapSize;
    uint16_t bloomFilterSize;
    uint16_t hashEntryPerNormalBucket;
    uint16_t bitmapSizePerOverFlowPage;
    uint16_t reverseCnt;
    ChLabelPageMetaDataT metaData;
} DashEhLabelMetaT;

typedef PageIdT IdxCachePageAddrT;
// 为确保truncate操作一定成功，缓存一些重新初始化需要用的页，确保申请页能成功
typedef struct IdxTruncatePageCache {
    bool enable;        // 是否启用truncate page缓存，只有truncate操作中启用
    uint32_t capacity;  // 缓存页最大个数
    uint32_t size;      // 当前缓存页的个数
    ShmemPtrT pagesAddr;
    IdxCachePageAddrT *pages;  // 页缓存数组
} IdxTruncatePageCacheT;

typedef struct ClusteredHashLabel {
    DbLatchT latch;  // 加锁控制isConstructed更新, 通过该标志位判断聚簇容器目录页是否已经创建
    ChLabelCfgT labelCfg;
    ClusteredHashMemMgrT memMgr;
    uint64_t version;
    uint32_t entryUsed;
    ClusteredHashPerfStatT perfStat;
    uint32_t shmemCtxId;
    uint32_t magicNum;
    DashEhLabelMetaT dashMeta;
    uint32_t keyLen;
    bool isHashSwizzleArrayInit;
    IdxTruncatePageCacheT trcPageCache;
    DbSpinLockT scanCursorLock;  // 控制 totalCursorNum 变量的并发
    uint32_t scanCursorRef;      // 处理后台缩容线程与业务扫描线程的互斥
    ShmemPtrT labelVarInfoPtr;
    uint32_t pageAddrArrCapacity;  // capacity of pageShmemPtr[]
} ClusteredHashLabelT;

typedef struct ClusteredHashLabelVarInfo {
    uint64_t maxItemNum;         // 表最大记录数，写缓存特性会修改
    uint32_t dirDepth;           // directory depth to indicate how many bits hashcode are used in directory
    uint32_t dirCap;             // diretory capacity of segments
    uint32_t dirPageCount;       // 目录页数目
    uint32_t segPageCount;       // 数据页数目
    uint32_t overflowPageCount;  // 溢出页数目
    uint32_t tupleUsed;          // 表记录数
    uint16_t upgradeVersion;     // 升降级版本
    uint32_t isConstructed;      // label是否完成目录页初始化
    bool hasScaledIn;            // label是否进行过缩容
    uint8_t reserved;
} ClusteredHashLabelVarInfoT;

typedef struct TagDirPageCacheItemT {
    uint32_t reserved;
    uint32_t dirPageId;
    uint8_t *dirPageAddr;
} DirPageCacheItemT;

#define IDX_PAGE_CACHE_COUNT 8u

typedef struct TagChLabelPageMemRunCtxT {
    ClusteredHashMemMgrT *memMgr;
    void *mdMgr;  // MdMgrT
    PageIdT *pageAddr;
    uint64_t version;
    DirPageCacheItemT dirCache[IDX_PAGE_CACHE_COUNT];
    uint32_t pagePtrArrCapacity;
    IdxTruncatePageCacheT *trcPageCache;
    uint32_t labelId;
    bool needCachePage;
} ChLabelPageMemRunCtxT;

typedef struct CHTransactionalCtx {
    TrxCntrCtxHeadT ctxHead;
    ShmemPtrT chShmAddr;
    ChLabelRunHdlT chRunCtx;
    CallBackStructT commitCallback;
    void *vertexLabel;  // 事务回滚, 在索引比较场景使用. noteb: 后续应该 在索引容器的上下文 维护?
    void *vertex;
    ChLabelKeyCmpFunc pKIndexKeyCmpFunc;  // 主键索引key比较钩子函数
    IdxKeyCmpFunc secIndexkeyCmp;         // 二级索引key比较钩子函数
    IdxAddrCheckFunc addrCheck;           // 聚簇容器逻辑addr校验钩子函数
    IdxAddrCheckAndFetchFunc addrCheckAndFetch;
    uint32_t labelLatchVersionId;
    uint32_t reserve;
    LabelRWLatchT *labelRWLatch;
} ChTrxCtxT;

typedef struct HashDirSegment {
    uint32_t segDepth;  // segment depth to indicate how many bits hashcode are used in segment
    uint32_t pattern;
    union {
        struct {
            uint32_t blockId;  // block id
            uint32_t pageId;
        };
        PageIdT pageAddr;
    } pageAddr;
} HashDirSegmentT;

typedef struct TagDbMemAddr {
    uint8_t *virtAddr;
    PageIdT pageAddr;
} DbMemAddrT;

typedef uint32_t HtHashCodeT;

typedef struct ChLabelOverFlowPageCtx {
    DbMemAddrT pageAddr;
    HtHashCodeT hashCode;
    uint32_t blockId;
    uint8_t *bitmapHdr;
    uint8_t *firstHashEntry;
    uint8_t *firstTuple;
} ChLabelOverFlowPageCtxT;

typedef struct ChLabelPageCtx {
    HashDirSegmentT *dirSeg;
    DbMemAddrT segAddr;
    uint64_t version;
    HtHashCodeT hashCode;
    uint32_t pattern;
    ChLabelPageMetaDataT pageMetaData;
    uint8_t *bloomFilterHdr;
    uint8_t *bitmapHdr;
    uint8_t *normalBucketHdr;
    uint8_t *stashBucketHdr;
    uint8_t *tupleHdr;
    uint32_t targetSlot;        // nth
    uint32_t neighborSlot;      // (n + 1)th
    uint32_t nextNeighborSlot;  // (n + 2)th
    uint32_t prevNeighborSlot;  // (n - 1))th
    bool needVisitOverFlowPage;
    ChLabelOverFlowPageCtxT overFlowPageCtx;
} ChLabelPageCtxT;

typedef struct ChBucketMeta {
    uint8_t cnt : 4;                 // 当前bucket中已使用的entry数量
    uint8_t localEntryCnt : 4;       // 属于本bucket的entry数量
    uint8_t prevBitMap;              // 某一位为1则表示该entry属于前一个bucket
    uint8_t allocBitMap;             // 当前bucket中被使用的entry
    uint8_t stashedCnt : 7;          // 当前bucket中，放在了stash bucket的个数
    uint8_t bucketOverFlowFlag : 1;  // 当前bucket中发生过溢出（放到了溢出页）, hashcode重复的key太多
    uint32_t stashedBitMap;          // 当前bucket中存在stash bucket的entry位置
} ChBucketMetaT;

typedef struct ChStashBucketMeta {
    uint16_t count;        // stash bucket 存储了多少条记录
    uint16_t reserved;     // 预留字节对齐
    uint32_t allocBitMap;  // 每个bit位标识对应hashEntry已经被占用
} ChStashBucketMetaT;

typedef struct ScaleInProcessInfo {
    ChBucketMetaT *bucketA;
    ChBucketMetaT *bucketB;
    ChBucketMetaT *bkNextA;
    ChBucketMetaT *bkNextB;
    ChStashBucketMetaT *stashBucketA;
    ChStashBucketMetaT *stashBucketB;
} ScaleInProcessInfoT;

typedef struct ClusteredHashLabelRunCtx {
    ContainerTypeE containerType;
    ChLabelOpenCfgT openCfg;
    ClusteredHashLabelT *chLabel;
    ClusteredHashLabelVarInfoT *labelVarInfo;  // chLabel会发生变化的信息
    PageIdT *dirPagesAddr;                     // chLabel记录目录页addr的数组
    ChLabelPageMemRunCtxT memRunCtx;
    SeRunCtxT *seRunCtx;
    HeapTupleBufT heapTuple;
    uint32_t heapTupleAllocSize;  // heapTuple实际申请的内存大小
    uint32_t partialsStoreSize;  // 发生表结构升级之后，存储在聚簇容器中单条记录的部分buf长度
    bool isPageReadOnly;
    bool isHoldHcIndexLockByUpper;
    bool isBatchOperation;
    bool isDegrade;
    uint32_t newSchemaVersionLength;
    ChTrxCtxT *chTrxCtx;  // 容器事务相关信息
    ChLabelPageCtxT pageCtx;
    uint64_t trxId;
    HeapTupleBufT cachedHeapTuple;  // merge操作使用的tuple缓存
    bool isExpand;
#ifndef NDEBUG
    uint8_t *lockPage;
    uint8_t *bucketCount;
    uint8_t *judgePageA;
    uint8_t *judgePageB;
    uint8_t *mergePageA;
    uint8_t *mergePageB;
    ScaleInProcessInfoT scaleInProcessInfo;
#endif
} ChLabelRunCtxT;

typedef struct IndexOpenCfg {
    SeRunCtxHdT seRunCtx;     // for iterator alloc
    void *vertex;             // 作为Key比较函数里面的vertex
    HpRunHdlT heapHandle;     // 索引调用heap获取heapTuple和key buffer
    ChLabelRunHdlT chHandle;  // 索引调用clustered hash获取tuple和key buffer

    // 1. 使用 union 封装
    // 2. DmIndexLabelBase 增加标记区分 kv 和 vl 类型
    // 用于排序索引进行key大小的比较
    union {
        DmIndexLabelBaseT *indexLabel;  // 基类  DmIndexLabelBaseT
        DmVlIndexLabelT *vlIndexLabel;  // vertex
        DmKvIndexLabelT *kvIndexLabel;  // kv  DmKvIndexLabelT
    };
    IdxCallBackFuncT callbackFunc;
    void *userData;
    void *keyInfo;
    void *vertexLabel;  // 适配catalog放共享内存
    bool needCheckIndexSatisfied;
    bool openDirectWrite;
    DmIndexTypeE indexType;
} IndexOpenCfgT;  // 对外结构体，open 排序索引时初始化handle所用

typedef struct TuplePointer32 {
    uint32_t pageIdAndSlotId;
} TuplePointer32T;

typedef struct DataInfo {
    TuplePointer32T addr32;  // 数据的逻辑addr
    uint8_t hasModify;  // 标识是否是修改过，修改过的数据无法根据物理addr判断可见性，需要走heap读接口
    uint8_t *buf;  // 数据的物理addr
} __attribute__((packed)) DataInfoT;

typedef struct HcDeletePrefetchCtx {
    bool isPrefetch;
    uint8_t filterResult;
    uint32_t bucketIndex;
    HpTupleAddr oldHeadTupleAddr;
    HpTupleAddr delTupleAddr;
    HpTupleAddr prevTupleAddr;
    HpTupleAddr nextTupleAddr;
    void *oldHeadHcPtr;
    void *delHcPtr;
    void *prevHcPtr;  // 需要维护它的链表后项
    void *nextHcPtr;  // 需要维护本记录链表前项
} HcDeletePrefetchCtxT;

typedef struct HcInsertPrefetchCtx {
    bool isPrefetch;
    uint8_t filterResult;
    uint32_t bucketIndex;
    HpTupleAddr insertTupleAddr;
    void *insertHcPtr;  // 需要维护本记录链表后项
} HcInsertPrefetchCtxT;

typedef struct HcPrefetchCtx {
    HcDeletePrefetchCtxT hcDeletePrefetchCtx;
    HcInsertPrefetchCtxT hcInsertPrefetchCtx;
} HcPrefetchCtxT;

typedef Handle IndexRunCtxT;

typedef struct IndexCtx {      // 开放的执行态的上下文信息
    IndexOpenCfgT idxOpenCfg;  // open使用的runcontext IndexOpenCfgT
    IndexMetaCfgT idxMetaCfg;  // 索引元数据，包含indexId等
    IdxBaseT *idxHandle;       // 特定索引的元数据 HashTableT, NonUniqueIndexT etc.
                               // metadatahandle 需要拷贝存储持久化IndexMetaCfgT内的元信息
    TupleBufT tupleBuf;        // TupleBufT
    uint64_t trxId;
    TupleAddr oldRowId;  // yang 场景中会对同一事务中主键相同的的插入\删除要求放回第一条数据的RowId,乐观事务索引是多版本
    ShmemPtrT idxShmAddr;
    IndexRunCtxT *idxRunCtx;  // IndexRunCtxT
    IndexKeyT idxKey;
    uint32_t destNode;  // TTREE查询返回插入位置,todo 多个唯一索引场景暂未处理
    bool isAcquireLockByTryOnce;
    bool constructedWhenOpen;
    bool batchLocked;  // 批量操作上锁判断，避免重复加解锁
    bool batchReadLock;
    bool batchWriteLock;
    bool isMemFirst;  // 是否使用内存优先模式，创建索引时确定
    uint8_t idxAllocType;  // 该idxCtx申请空间时使用的indextype，如果使用了不一致的indexType进行IdxOpen将报错
    bool isBatch;
    bool isInsert;
    bool hasOpened;  // 该idxCtx是否执行idxOpen成功
#ifdef ART_CONTAINER
    bool isUpdate;
#endif
    bool isLookupOldRecord;  // 仅用于 YANG diff 场景，标志通过主键查找旧 NPA 表数据
    bool scanDirChanged;
#ifdef FEATURE_HAC
    bool hasLookup;        // 插入前是否lookup过
    bool isKeyCmpByHac;    // 是否使用硬化的keycmp
    bool isHacBatchAsync;  // 是否使用异步通信模式
#endif
    void *kvBuffer;  // 仅用于 mini kv 场景, cursor中的缓存kv hpTupleBuf指针，用于move场景重新定位
#ifdef FEATURE_GQL
    bool isBatchReplaceOrDelete;  // 当前索引runCtx是否是batch replace 或者 batch delete场景
    HeapTupleBufT preAllocHpTupleBuf;  // 此块内存默认为空，非空时为hpTupleBuf预分配内存，减少申请内存的次数
                                       // HeapTupleBufT
    uint32_t preAllocHpTupleBufOffset;  // 记录预分配内存的偏移量
#endif
    DataInfoT ttreeDataInfo;       // TTree索引场景，需要提供节点物理addr和标记信息 DataInfoT
    HcPrefetchCtxT hcPrefetchCtx;  // HcPrefetchCtxT
                                   // 更新、删除操作提前缓存，解决直连写arm32虚拟内存不足无法映射时，数据完整性问题
    uint32_t ttreePos;  // 记录更新删除TTree位置信息
#ifdef FEATURE_SQL
    uint64_t indexAddrInfo;
    void *autoIdIndexCtx;      // 拓展的自增ID索引上下文
    uint32_t extendedKeySize;  // 在索引上的key后方拓展的内容长度
    uint32_t posOfPqAddr;      // 检索时需要取的PQ相对位置
    uint64_t autoId;           // 检索时返回给上层的自增ID，用于二次检索PQ值
    LpasPqAddrInfoT *pqInfo;   // 二次检索得到的PQ值
    void *pqIndexPlanState;    // 用于pq索引算法排序和scan的执行计划
    bool isSinglePqIndex;  // 如果是单PQ索引，直接将pq信息存在标量索引的拓展key后，能大幅提高检索性能
#endif
    bool isNormalIdx;      // 兼容v1使用，标识是否是大对象表的索引还是普通fixed表的索引
    void *updateOrDelPtr;  // 用于TTree插入场景,迁移后的undo链物理addr
    uint8_t *bigObjBuf;    // TTree 大对象数据中间缓存，用于 TTreeSearchDataInfo
    void *vertexIdBuffer;  // 用于Insert时返回VertexId, 或传入VertexId查找Vector
} IndexCtxT;

typedef struct VecBuildStatePara {
    uint32_t count;
    DmVecDistTypeE distType;
    uint64_t *dataId;
    DbDataTypeE dataType;
    void *vectors;     // two dimensional array for build
    DmValueT *scalar;  // DmValueT
    uint32_t scalarCount;
    uint32_t batchCount;
    bool sample;  // user determines whether to sample
    uint16_t dim;
    uint8_t reverse[6];
#ifndef NDEBUG
    DbMemCtxT *memCtx;  // 用例打桩函数中用  DbMemCtxT
#endif
    uint32_t sampleCount;
    float *sampleVec;
    LpasPqAddrInfoT *pqInfo;
} VecBuildStateParaT;

typedef enum IndexRangeType {
    INDEX_RANGE_CLOSED,    /* [low, high] range scan */
    INDEX_RANGE_OPENLEFT,  /* (low, high] range scan */
    INDEX_RANGE_OPENRIGHT, /* [low, high) range scan */
    INDEX_RANGE_OPENBOTH,  /* (low, high) range scan */
    INDEX_RANGE_BUTT
} IndexRangeTypeE;

// Art聚簇容器匹配时的
typedef enum MatchType { PREFIX_MATCH, RANGE_MATCH, NEAR_MATCH, ALL_MATCH, INVALID_MATCH } MatchTypeE;

typedef struct IndexScanCfg {
    IndexRangeTypeE scanType;       /* index scan type */
    IndexScanDirectionE scanDirect; /* index scan direction */
    IndexKeyT *leftKey;             /* the left bound key of the scan */
    IndexKeyT *rightKey;            /* the right bound key of the scan */
    uint8_t scanMode;               /* the mode of scan op */
    uint32_t scanLmit;
    void *scanPara;
    uint64_t nullDisableBitMap; /* the null disable bitmap of the index prope for scan */
                                /* 0x5 means prop_0 and prop_2 is null disabled */
                                /* the bits is up to DM_MAX_KEY_PROPE_NUM */
    MatchTypeE matchType;
    uint8_t condIdx;       /**前缀匹配的索引项 */
    uint8_t matchBytesNum; /**前缀匹配多少个字节 */
    uint8_t nearAIdx;      /**临近查询的A字段*/
    uint8_t nearBIdx;      /**临近查询的B字段，查询离A最近且B字段相同的数据*/
} IndexScanCfgT;

typedef void *IndexScanItrT;

typedef struct VectorArray {  // 动态数组，提供给IVF/DISKANN使用
    uint32_t length;          // 数组已经写入的元素个数
    uint32_t maxLen;          // 数组已最多可写入元素个数
    uint32_t elementSize;     // 每个元素占用的字节数
    void *items;
} VectorArrayT;

static inline uint32_t VectorArrayGetLength(const VectorArrayT *arr)
{
    DB_POINTER(arr);
    return arr->length;
}

Status VectorArrayGet(const VectorArrayT *array, uint32_t index, void **vec);

StatusInter SeAllocPage(PageMgrT *pageMgr, AllocPageParamT *allocPageParam, PageIdT *pageAddr);

#define RSM_INVALID_LABEL_ID 0

typedef enum TableLoadPriority {
    TABLE_LOAD_PRIORITY_LOW = 0,
    TABLE_LOAD_PRIORITY_MID,
    TABLE_LOAD_PRIORITY_HIGH,
} TableLoadPriorityE;
typedef struct TablePriorityRecyArg {
    uint32_t recycleFlag;  // 当前和DB_RECYCLE_PRIORITY淘汰策略绑定
    TableLoadPriorityE
        priority;  // 如果该页需要按照最高优先级如表load功能，则为INDEX_PRIORITY_HIGH，如果为索引优先，则为INDEX_PRIORITY_MID
} TablePriorityRecyArgT;

StatusInter SeGetPageWithArg(PageMgrT *pageMgr, PageIdT pageId, uint32_t option, void *recyArg, uint8_t **page);

StatusInter SeFreePage(PageMgrT *pageMgr, FreePageParamT *freePageParam);
void SeLeavePage(PageMgrT *pageMgr, PageIdT pageAddr, bool isChanged);
void *SeGetInstance(uint16_t instanceId);

Status VectorArrayInit(DbMemCtxT *memCtx, uint32_t maxLen, uint32_t elementSize, VectorArrayT **vectArray);

Status VectorArraySet(VectorArrayT *arr, uint32_t index, const void *vec);

Status SeAnnAllocAndInitItem(DbMemCtxT *ctx, size_t size, int setNum, uint8_t **dest);

Status VectorArrayAppend(DbMemCtxT *memCtx, void *vec, VectorArrayT **arr);

PageMgrT *SeGetPageMgr(uint16_t instanceId);

void VectorArrayFree(DbMemCtxT *memCtx, VectorArrayT *arr);

void SeAnnFreeItem(DbMemCtxT *ctx, uint8_t **dest);

bool VectorArrayContain(const VectorArrayT *arr, const void *target);

uint32_t SeGetNewTrmId(SeInstanceT *seIns);

Status VectorArrayReset(VectorArrayT *arr);

#define SE_INVALID_LPAS_ADDR ((PageIdT){.deviceId = 0u, .blockId = 0u})

RedoRunCtxT *SeGetCurRedoCtx(void);

void RedoLogBegin(RedoRunCtxT *redoCtx);

StatusInter RedoLogEnd(RedoRunCtxT *redoCtx, bool isLogCommit);

bool RedoIsPriBufEnough(RedoRunCtxT *redoCtx, uint32_t requiredSize);

void DbAdptLogWriteAndSetLastError(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...);

#define DB_LOG_AND_SET_LASERR(errCode, errDesc, ...)                                                             \
    DbAdptLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_ERR, errCode, __FILE__, __LINE__, \
        __func__, errDesc, ##__VA_ARGS__)

#define SE_LAST_ERROR(interErrCode, format, ...)                                             \
    DB_LOG_AND_SET_LASERR(DbGetExternalErrno(interErrCode), "LASTERR (%" PRIu32 ") " format, \
        DbGetInternalErrno(interErrCode), ##__VA_ARGS__)

void RedoLogAppend(RedoRunCtxT *redoCtx, const uint8_t *data, uint32_t size);

inline static void IdxConstructedFinish(IdxBaseT *idxBase)
{
    DB_POINTER(idxBase);
    idxBase->isConstructed = 1u;
}

void RedoLogWrite(RedoRunCtxT *redoCtx, int32_t type, const PageIdT *addr, const uint8_t *data, uint32_t size);

typedef enum UndoTrxResType {
    TRX_RES_HEAP = 1,
    TRX_RES_TOPO,
    TRX_RES_RES_COL,  // resid of res col is a logical id instead of a "row". need abstracting and refactoring
    TRX_RES_CLUSTERED_HASH,
    TRX_RES_BTREE,
    TRX_RES_DISKANN,
    TRX_RES_LPASMEM,
    TRX_RES_SPACE,
    TRX_RES_CU,
    TRX_RES_MAX,
} UndoTrxResTypeE;

typedef struct UndoRowOpInfo {
    HpRunHdlT runCtx;
    bool isRetained;
    bool isDeleted;  // 乐观事务下，需要记录上一个版本的状态，回滚时需要使用
    // isSkipUndoIndex为true时 处理此情况的undo时，不用去处理索引，
    // (本质原因是EE没有indexOpen，导致vertex为空，提交/回滚流程也需要省略对索引的处理，
    // 聚簇容器因为有indexOpne,不使用此标志位), 当前有两个场景：
    // 1. 首边add更新，注意虽然不处理索引，但buf长度会变化（dm做了边add压缩）
    // 2. 表降级后台刷新数据，此时只刷新buf长度，不改变索引
    bool isSkipUndoIndex;
    UndoTrxResTypeE resType;
    UndoRowOpTypeE opType;
    uint32_t labelId;
    uint64_t rowTrxId;
    uint64_t rowRollPtr;
    uint64_t rowId;
    uint32_t savePointId;
    uint16_t bufSize;  // buf的大小，用于DFX统计使用（与下面rowSize的区别：linkSrc时，rowSize为行头大小）
    uint16_t rollbackReserveSize;  // 用于乐观事务在版本链上回滚时候使用
    bool isPersistent;
    bool isRsm;
    bool isRecycled;  // purge回收时，可能批量回收，根据此标记位避免重复回收undoRec,只有更新会批量回收
    uint8_t reserve;
    uint32_t rowSize;  // 普通情况为src行一整行的长度，部分更新时为（src行头+部分内容）的长度
    uint8_t *rowBuf;

    DmLabelTypeE labelType;
    uint32_t operLabelId;    // 资源池操作下操作表的表id
    uint64_t containerAddr;  // could be shmemptr or pageid or cuPointer
    uint32_t logicalId;      // CU类型日志下操作所属的逻辑表id
    union {
        uint32_t spaceId;  // CU类型记录表的spaceId
        uint32_t cuId;     // CU类型日志记录起始cuId
    };
    uint32_t newLogicalId;
    uint32_t newPhysicalId;

    uint16_t srcRowHeadSize;
    uint16_t reserve2;
    uint32_t offsetOfRawData;  // 部分更新时使用
    uint8_t *srcRowHead;       // 部分更新时记录undoLog使用
    uint8_t *partDataBuf;      // 部分更新时记录undoLog使用,长度记录在bufSize
} UndoRowOpInfoT;

typedef struct UndoSpaceMeta {
    SeInstanceIdT seInstanceId;
    uint16_t numRsegs;  // number of rollback segments
    uint32_t undoPageReuseLimit;
    uint32_t maxRowSize;
    uint32_t undoFileId;   // each undo space will be stored in one file
    uint32_t undoSpaceId;  // each undo space will be stored in one file
    uint32_t usedPageCnt;  // number of undo pages currently used
} UndoSpaceMetaT;

typedef struct UndoSpace {
    DbRWSpinLockT rwLock;  // Undo 空间上的锁, 针对Undo空间进行操作时，通过加锁控制读写并发
    UndoSpaceMetaT meta;
    ShmemPtrT rsegShmPtr;
    uint32_t shmMemCtxId;
} UndoSpaceT;

typedef struct SeUndoCtx {
    UndoSpaceT *undoSpace;  // UndoSpaceT
    SeRunCtxT *seCtx;
    PageMgrT *pageMgr;
    RedoRunCtxT *redoCtx;
    void *newPtr;  // V1兼容场景使用heap更新新增的undo链物理addr
    bool enableCache;
#ifdef FEATURE_SIMPLEREL
    bool isUndoAllocDefault;  // 为true时undo预留页只从default space申请
#endif
} SeUndoCtxT;

typedef enum {
    TRX_STATE_NOT_STARTED,
    TRX_STATE_ROLLBACK,
    TRX_STATE_ACTIVE,
    TRX_STATE_ABORT,
    TRX_STATE_COMMITTED,
    TRX_STATE_FAILURE
} TrxStateE;

typedef enum {
    TRX_DIRECT_READ_STATE_READABLE,         // 没有超时，也没有读
    TRX_DIRECT_READ_STATE_READING,          // 没有超时，正在直连读
    TRX_DIRECT_READ_STATE_TIMEOUT_READING,  // 超时，正在直连读
    TRX_DIRECT_READ_STATE_UNREADABLE        // 超时，没有读，此时不允许再读需要回滚
} TrxDrStateE;

typedef Status (*TrxCommitCallBackFunc)(void *para);
typedef struct {
    TrxCommitCallBackFunc func;
    void *parameter;
} TrxCommitCallBackCfgT;

typedef struct TrxBase {
    /* transaction base info */
    uint16_t trxSlot;  // 事务池的slot const
    bool readOnly;
    bool isLiteTrx;  // during lite trx, only lite-trx-label operations and auto-commit allowed
    bool isBackGround;
    bool needSendSubWhenFailed;
    bool isInteractive;  // 是否交互式事务
#ifdef FEATURE_GQL
    bool skipRowLockPessimisticRR;  // cfg designed for FES, default: false
#endif
    bool isRecovery;  // 是否时重启恢复事务, 保留内存的恢复和文件持久化恢复都会使用，使用时注意区分
    bool isAllocNew;   // v1导入使用,标记为true,则在heapOpen时直接创建新容器
    uint8_t *keyData;  // 用于回滚时的hashKey buf
    uint32_t keyLen;   // 记录keyData的长度
    uint16_t connId;   // 事务对应的连接ID, 用于事务中断
    uint16_t reserve2 : 12;
    uint16_t isTrxForceCommit : 1;
    uint16_t isRetryTrx : 1;  // 隐式开启的重试事务
    uint16_t isPurger : 1;
    uint16_t needReset : 1;  // MiniKv多进程场景的重置相关资源
    double splitTime;        // 长任务喂狗分片时间周期
    /* transaction activity info */
    TrxTypeE trxType;
    TrxStateE state;
    TrxDrStateE drState;
    uint8_t processIdx;  // MiniKv多进程场景的进程标识
    uint8_t reserve3;
    uint16_t reserve4;
    uint64_t startTime;
    TrxIdT trxId;                 // 事务ID，也是事务开始时刻的逻辑时钟
    TrxIdT commitTs;              // commit ID, 也是事务提交时刻的逻辑时钟
    bool isNeedModifyPubsubList;  // 提交时是否要改变合并订阅链表结构。当前后台缩容事务提交时不改变合并订阅链表结构；
    bool recoveryNeedHandleResource;             // 重启恢复时是否需要自释放资源
    TrxCommitCallBackCfgT trxCommitCallBackCfg;  // 事务提交前需要先执行该函数
} TrxBaseT;

typedef struct AdvancedPtr {
    ShmemPtrT shmPtr;  // 用于获取首addr，放置开头保证拷贝时有效性
    void *ptr;         // 用于高性能访问
} AdvancedPtrT;

typedef struct UndoLiteRecord UndoLiteRecordT;

typedef struct UndoLiteInsertRecord {
    uint64_t trxId;
    uint64_t rowId;
} UndoLiteInsRec;

typedef struct UndoLiteUpdateRecord {
    bool isSkipUndoIndex;
    bool isUseBatchAllocMem;
    uint8_t reserve;
    uint32_t tupleLen;  // length of the updated tuple buffer
    uint64_t trxId;
    uint64_t rowId;            // tuple address of the updated tuple
    AdvancedPtrT tupleBuffer;  // uint8_t * old tuple buffer
} UndoLiteUpdRec;

typedef struct UndoLiteUpdatePartRecord {
    uint16_t srcRowHeadSize;
    uint16_t partDataLen;  // 目前部分更新不支持超过一个页的内容的更新
    uint32_t offsetOfRawData;
    uint32_t tupleLen;  // length of the updated tuple buffer
    uint64_t trxId;
    uint64_t rowId;            // tuple address of the updated tuple
    AdvancedPtrT tupleBuffer;  // 行头 + 被更新掉的部分旧内容
} UndoLiteUpdPartRec;

#define DM_RES_COL_MAX_COUNT 4

typedef struct UndoLiteResColRecord {
    uint32_t resCount;  // 每条记录最多绑定4个资源列，记录用了几个资源列
    uint32_t poolLabelId[DM_RES_COL_MAX_COUNT];
    uint64_t resIdArr[DM_RES_COL_MAX_COUNT];
    uint32_t operLabelId[DM_RES_COL_MAX_COUNT];
} UndoLiteResColRec;

typedef struct UndoLiteOperationRecord {
    UndoRowOpTypeE recordType;
    uint32_t labelId;
    union {
        UndoLiteInsRec insertRec;
        UndoLiteUpdRec updateRec;
        UndoLiteUpdPartRec updatePartRec;
        UndoLiteResColRec resColRec;
    } record;
} UndoLiteOpRecord;
#define UNDO_LITE_INIT_RECORD_NUM 2
typedef struct UndoLiteRecord {
    bool allowExtendUndoLog;  // 针对范围更新, 允许进行扩展recordExtendArr
    bool hadSetTupleNum;      // “主动设置tupleNum” 的不可重入标志
    bool isInited;            // 是否已经初始化, 用于在TrxLiteUndoLogInit区别是否重入函数
    bool isAllocFromList;
    bool hasResColUndoLog;  // 一个tuple最多包含4个资源列字段,用于标记是否中间某资源列出错,回滚时需把已申请的进行释放+
                            // 同时也表示是否需要为资源池的record先进行一次初始化
    bool isChLabel;         // 记录的是否为聚簇容器的undo log
    bool trxCommitFlag;  // 保留内存场景使用，当设置此标记位后，如果发生异常重启，恢复时继续提交该事务，否则执行回滚
    bool isRsmRecoverying;  // 保留内存场景使用，当设置为true时，表示是处理重启前的undo；否则是处理重启后的undo
    uint32_t tupleNum;         // 记录批量操作中操作的总行数
    uint32_t usedUndoRecNum;   // 实际使用到的位置
    uint32_t totalUndoRecNum;  // 长度为tupleNum * 2 (一个insert undo会对应一个资源列 undo)
    uint32_t extendStepSize;   // 扩展的步长
    UndoLiteOpRecord recordArr[UNDO_LITE_INIT_RECORD_NUM];
    AdvancedPtrT lastNodePtr;          // UndoLiteRecordsNodeT *
    AdvancedPtrT recordExtendNodeArr;  // UndoLiteRecordsNodeT *
                                       // 链表，第一次初始化时
                                       // 直接按(totalUndoRecNum - UNDO_LITE_INIT_RECORD_NUM)来扩展
} UndoLiteRecordT;

typedef struct LiteTrx {
    bool isUseRsm;  // lite for rsm
    /* cached single undo record */
    bool usedBuf;  // 记录是否已经使用cacheBuf
    bool usedCacheNode;
    bool reserved1;
    uint32_t bufLength;  // 记录cacheBuf的长度
    AdvancedPtrT cacheBuf;  // 从sessionMemCtx申请的一段buffer，其生命周期是连接级别 目前用于轻量级更新undo的tuple
    uint32_t cacheNodeSize;                // 记录 cacheExtendArrFirstNode 的长度
    AdvancedPtrT cacheExtendArrFirstNode;  // 从sessionMemCtx申请的缓存，只缓存 recordExtendNodeArr 第一个
                                           // UndoLiteRecordsNodeT 节点
    // uint8_t *
    UndoLiteRecordT cacheUndoLiteRec;  // for default
    UndoLiteRecordT *undoLiteRec;      // only used in lite transaction. at the front of the Trx to reduce cache miss
    RsmUndoRecordT *rsmUndoRec;        // 处理heap/聚簇容器写不完整的情况，指向tableInfo中的实例
    DbMemCtxT *periodMemCtx;           // 连接级别memCtx
} LiteTrxT;

typedef struct TrxIdList {
    uint16_t listLen;
    uint16_t capacity;
    uint32_t reserved;
    TrxIdT trxIds[];
} TrxIdListT;

typedef struct ReadView {
    bool isOpen;
    bool reserved[7];
    TrxIdT creatorTrxId;        // trx id of creating transaction
    TrxIdT highWaterMark;       // 高水位，大于等于highWaterMark的事务对view不可见
    TrxIdT lowWaterMark;        // 低水位，小于lowWaterMark的事务对view一定可见
    ShmemPtrT activeTrxIdsShm;  // 用于RR事务，在trx开始时保存的活跃读写事务ID列表，存于共享内存中由客户端和服务端共用
    TrxIdListT *activeTrxIdsTmp;  // 用于RC事务，在fetch操作时保存的活跃读写事务ID列表，存于动态内存使用完毕当场释放
} ReadViewT;

typedef struct TrxRseg TrxRsegT;

typedef struct TrxUndoLog TrxUndoLogT;

typedef enum UndoTypeEnum {
    TRX_UNDO_INVALID,
    TRX_UNDO_NORMAL,
    TRX_UNDO_RETAINED,
} UndoTypeE;

struct TrxUndoLog {
    uint16_t id;  // slot id in the rseg page
    uint16_t usedPageCnt;
    UndoTypeE type : 31;  // insert or update, copied from undo seg header
    bool isRetainedCache : 1;
    uint32_t state;
    TrxRsegT *rseg;
    TrxIdT trxId;
    uint32_t logHdrOffset;
    uint32_t recCnt;   // number of undo records
    uint64_t rollPtr;  // 当前undo的位置
    TrxUndoLogT *next;
    // Undo Persistence: consider if Should use dynamic addr
    PageIdT segHdrAddr;
    void *segHdrPage;  // dynamic memory addr of the undo seg hdr page which is the page of hdrBlockNo aboved
    PageIdT lastPageAddr;
    void *lastUndoPage;  // dynamic memory addr of the last undo page which is the page of lastBlockId aboved
};

struct TrxRseg {
    DbSpinLockT lock;  // protect the undo history list and the cached list
    UndoSpaceT *undoSpaceIns;
    SeInstanceT *seIns;

    TrxUndoLogT *normalUndoCached;
    TrxUndoLogT *retainedUndoCached;

    // Undo Persistence: consider if Should use dynamic addr
    PageIdT rsegPageId;  // share memory addr of Rollback segment page
    void *rsegPage;      // dynamic memory addr of Rollback segment page
};

typedef struct SavePointInfo {
    DbListT itemList;  // SavePointT
} SavePointInfoT;

typedef struct DafCommitActionCollection {
    TagLinkedListT commitActions;  // DAF delayed operation linked list, e.g., IdxDeleteActionsT
} DafCommitActionCollectionT;

typedef struct NormalTrxBase {
    IsolationLevelE isolationLevel;
    uint32_t savePointId;  // trx初始化时为0，本事务创建savePoint时递增，回滚/释放savePoint都不会修改该值
    ReadViewT readView;  // readView
    /* transaction undo segment info */
    TrxRsegT *rseg;             // the rollback segment assigned to trx
    TrxUndoLogT *normalUndo;    // 直连写只支持轻量化事务，不会涉及到客户端申请，服务端访问
    TrxUndoLogT *retainedUndo;  // 直连写只支持轻量化事务，不会涉及到客户端申请，服务端访问
    SavePointInfoT savePointInfo;
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    DafCommitActionCollectionT *dafCommitActionCollection;  // Collection of committed operations
#endif
} NormalTrxBaseT;

typedef struct TrxLockIdentifierT {
    uint32_t lockIdField1;  // 32位的锁键值属性1
    uint32_t lockIdField2;  // 32位的锁键值属性2
    uint32_t lockIdField3;  // 32位的锁键值属性3
    uint16_t lockIdField4;  // 16位的锁键值属性4
    uint8_t lockType;       // 锁类型，见 SeLockTypeE
    uint8_t reserved;       // 保留字段 & 字节对齐
} SeTrxLockIdentifierT;

typedef struct LockAcqInfo {
    /* 以下是调用锁申请接口的结果缓存，每次申请重置 */
    SeTrxLockIdentifierT lastAcqId;
    /* 以下是每次锁逻辑实际申请的结果缓存，每次申请重置 */
    uint8_t lockType;      // SeLockTypeE
    uint8_t lastLockMode;  // SeLockModeE
    uint8_t lastAcqType;   // SeLockAcqTypeE
    bool isGetLock;
    bool
        isNewAllocLock;  // 新申请的锁还是复用了已持有锁，对于新申请的锁，某些场景下可以立即释放（两阶段锁的例外场景，目前只有索引遍历）
                         // noteb: 有问题再看看
    bool isDeadLockVictim;            // 是否是死锁回滚事务
    bool isTupleLockEscalateToLabel;  // 是否发生行锁升级表锁
    bool isLockConfit;                // 是否发生锁冲突
    bool isTrySameLock;               // 是否发生锁冲突
    /* 以下是事务历史锁申请的结果缓存，不会主动重置 */
    bool isHoldLabelXLock;  // 当前事务是否已持有表X锁，置位后不会再复位（可能出现false
                            // postive），只能用于快速判断没有持有表锁
    /* 以下是事务历史锁申请的结果缓存，视条件设置 */
    uint8_t firstLockType;  // 记录相同一批try lock下，第一次申请类型
    uint8_t firstLockMode;  // 记录相同一批try lock下，第一次申请模式
    uint8_t firstAcqType;   // 记录相同一批try lock下，第一次申请方式
    uint8_t reserve[3];
} LockAcqInfoT;

typedef struct PessimisticTrx {
    /* transaction lock info */
    uint32_t holdLockNum;
    uint32_t holdLockAcqId;
    uint32_t lockNotifyId;
    uint32_t reserved;
    LockAcqInfoT lockAcqInfo;
} PessimisticTrxT;

typedef struct LabelReadViewInfo {
    uint32_t labelId;
    TrxCheckLabelReadViewTypeE type;
    uint32_t savePointId;  // 首次操作这个表的savepointId
    uint32_t firstModifySavePointId;  // 首次修改这个表的savepointId, 初始化为 DB_MAX_UINT32 ，表示事务对该表还没有修改
} LabelReadViewInfoT;

#define DEF_RESVED_VIEW_ITEM_CNT 7

typedef struct LabelReadView {
    uint32_t isTrxCommitCheckActive : 1;  // 是否激活检查
    uint32_t curItemCnt : 31;             // 当前itemArray用到第几位
    uint32_t savePointIdForCheckActive;  // 防止回滚到dml操作前,整个事务变成只做了读的事务,记录首次dml操作的savePointId
    LabelReadViewInfoT itemArray[DEF_RESVED_VIEW_ITEM_CNT];
    DbListT itemList;
} LabelReadViewT;

typedef struct OptimisticTrx {
    LabelReadViewT labelReadView;
} OptimisticTrxT;

typedef struct NormalTrx {
    NormalTrxBaseT base;
    PessimisticTrxT pesTrx;  // 此处不能用union，在TrxMgrAllocTrx时还不知道这个事务将用作悲观还是乐观
    OptimisticTrxT optTrx;  // 它们里面的成员需要都初始化，用union会相互覆盖
} NormalTrxT;

typedef void (*UpdateStatusMergeListFunc)(void *statusMergeSubDataSet);
typedef struct {
    UpdateStatusMergeListFunc func;
    void *statusMergeSubDataSet;
} UpdateStatusMergeListCfgT;

typedef struct TrxCntrTempCtx {
    TrxCntrCtxHeadT ctxHead;
    ShmemPtrT containerShmAddr;
    void *cntrRunHdl;
    void *label;
    void *vertexNode;  // used in purger. . noteb: 后续应该 在索引容器的上下文 维护?
} TrxCntrTempCtxT;

typedef union TagStorageTransactionalContainerCtxT {  // 存储事务性容器上下文
    TrxCntrCtxHeadT ctxHead;                          // common head
    HeapTrxCtxT heapTrxCtx;
    ChTrxCtxT chTrxCtx;
    TrxCntrTempCtxT trxCntrCtx;  // noteb: 后续将不同容器的 事务容器上下文拆分出来. 避免不同容器的信息混合
} SeTrxContainerCtxT;

typedef struct TrxContainerLruCacheNode {
    SeTrxContainerCtxT *container;
    struct TrxContainerLruCacheNode *next;
} TrxContainerLruCacheNodeT;
#define DEF_RESVED_ITEM_CNT 4
typedef struct TrxContainerCtxList {
    uint16_t itemCnt;
    uint16_t lruCacheItemCnt;
    bool enableLruCache;
    char reserve[3];
    SeTrxContainerCtxT *freeContainer;
    SeTrxContainerCtxT ctxs[DEF_RESVED_ITEM_CNT];
    DbListT list;
    TrxContainerLruCacheNodeT *lruCacheHead;
    TrxContainerLruCacheNodeT *lruCacheTail;
} TrxContainerCtxListT;

typedef struct Trx {
    TrxBaseT base;  // TrxBaseT
    // noteb: 把这两个trx union起来，需要修改一下初始化的地方，避免二者互相覆盖，放到下个commit
    LiteTrxT liteTrx;  // LiteTrxT
    NormalTrxT trx;    // NormalTrxT
    /* transactional resource info */
    void *trxPeriodMemCtx;  // 事务活跃阶段有效的 (dynamic) memory ctx, 每个事务提交/commit时清空一次
    UpdateStatusMergeListCfgT updateStatusMergeListCfg;  // 更新状态合并表list回调函数  UpdateStatusMergeListCfgT
    TrxMgrT *trxMgr;      // 某个时刻trx只对应一个线程，存储trxMgr虚拟addr进行加速  TrxMgrT
    SeRunCtxT *seRunCtx;  // 所在SeRunCtx的虚拟addr指针
    TrxContainerCtxListT cntrCtxList;  // noteb: optimize in lite trx process  TrxContainerCtxListT
    /* transaction dfx info */
    uint64_t tid;  // 线程id
} TrxT;

StatusInter TrxUndoReportRowOperation(
    SeUndoCtxT *undoCtx, TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t *rollPtr);

#define SE_ERROR(interErrCode, format, ...) DB_LOG_ERROR(DbGetExternalErrno(interErrCode), format, ##__VA_ARGS__)

static inline uint32_t SerializePageId(const PageMgrT *pageMgr, const PageIdT pageId)
{
    return (uint32_t)((pageId.deviceId << pageMgr->pageAddrShiftBit) | pageId.blockId);
}

#define DB_MAX_UINT32 0xFFFFFFFF
static inline PageIdT DeserializePageId(const PageMgrT *pageMgr, const uint32_t id)
{
    uint32_t bitMask = ~(DB_MAX_UINT32 << pageMgr->pageAddrShiftBit);
    return (PageIdT){.deviceId = id >> pageMgr->pageAddrShiftBit, .blockId = id & bitMask};
}

Status HeapFetchHpTupleBuffer(const HpRunHdlT heapRunHdl, HpTupleAddr heapTupleAddr, TupleBufT *tupleBuf);

typedef struct HeapUpdOutPara {
    bool *isUndoBypass;  // 表示在更新的时候是否发生了undo rec优化合并
} HeapUpdOutPara;

Status HeapLabelUpdateWithHpTupleBuf(
    HpRunHdlT heapRunHdl, const HeapTupleBufT *heapTupleBuf, HpTupleAddr heapTupleAddr, HeapUpdOutPara *out);

UndoRowOpInfoT BuildUndoRowWithIdxCtx(IndexCtxT *idxCtx, UndoTrxResTypeE type);

typedef struct DataIdAndPqAddr {
    HpTupleAddr dataId;
    LpasPqAddrInfoT pqInfo;
} DataIdAndPqAddrT;

typedef struct VecScanPara {  // 向量查询入参
    uint16_t dim;
    uint32_t probe;  // 向量查询list区域数量
    uint32_t topN;   // 向量查询前N个最接近向量
    DbDataTypeE type;
    void *vector;
    double distanceUpperBound;  // distance upper bound
    uint64_t *dataIdList;       // filtered data ids
    uint32_t dataIdListSize;    // size of filtered data ids
    uint32_t scanOffset;        // 扩展查询的偏移
    uint64_t totalNum;          // 索引中的数据量
    DataIdAndPqAddrT *data;     // 前过滤得到的pq形式
    uint32_t dataCount;         // 前过滤得到的数据总数
    bool isVecExtendSearch;
} VecScanParaT;

void SeLeavePage(PageMgrT *pageMgr, PageIdT pageAddr, bool isChanged);

typedef struct HpBatchOut {
    HpTupleAddr addrOut;
#ifdef FEATURE_SIMPLEREL
    void *realBuf;
#endif
    uint8_t *pageHeadOut;
    bool isHeapPageSort;
    bool isUndoByPass;
    uint8_t reserve[2];
    uint32_t realDeleteCnt;
} HpBatchOutT;

typedef struct IndexRemovePara {
    bool isErase;
    bool isGc;
#ifdef FEATURE_SQL
    LpasPqAddrInfoT *pq;
#endif
} IndexRemoveParaT;

typedef struct IndexUpdateInfo {
    IndexKeyT oldIdxKey;
    IndexKeyT newIdxKey;
    HpTupleAddr oldAddr;
    HpTupleAddr newAddr;
} IndexUpdateInfoT;

typedef struct IdxBatchLookupIter IdxBatchLookupIterT;

typedef union IdxTupleOrIter {
    HpTupleAddr addr;
    IndexScanItrT iter;
} IdxTupleOrIterT;

typedef enum IdxValueType {
    IDX_IS_NOT_FOUND = 0,
    IDX_IS_TUPLE_ADDR,  // 1对1场景
    IDX_IS_ITERATOR,    // 1对多场景，是迭代器
    IDX_MAX_VALUE_TYPE
} IdxValueTypeE;

typedef Status (*IdxCategorizeFunc)(
    IndexCtxT *idxCtx, IdxBatchLookupIterT *iter, IdxTupleOrIterT addrOrIter, uint32_t pos, IdxValueTypeE valueType);

typedef struct IdxBatchLookupPara {    // batchLookup 回调函数与执行层句柄
    IdxCategorizeFunc categorizeFunc;  // 依据isFound进行分类，replace或delete流程中
    IdxBatchLookupIterT *iter;         // batch lookup执行层句柄
} IdxBatchLookupParaT;

typedef struct IndexHashPerfStat {
    uint32_t pageCount;         // 申请的page总个数
    uint32_t perPageSize;       // 每个page的大小
    uint64_t pageSize;          // 实际申请的page总大小
    uint64_t usedMemSize;       // 实际使用的内存空间
    uint64_t hashInsertCnt;     // hash索引插入次数
    uint64_t hashCollisionCnt;  // hash索引冲突次数
    uint8_t hashCollisionRate;  // hash冲突率
} IndexHashPerfStatT;
#define MAX_NODE_TYPE_NUM 10u
typedef struct ArtMemPerfStat {
    uint32_t pageCount;
    uint32_t pageReleasedCount;
    uint32_t scaleInCount;
    uint16_t pageSize;

    uint16_t shmUtilization;  // eg. 90%, node used shm / total shm (except freeNode used shm)
    uint64_t usedShm;

    uint32_t nodeCounts[MAX_NODE_TYPE_NUM];
    uint32_t nodeCount;
    uint32_t freeNodeCounts[MAX_NODE_TYPE_NUM];
    uint32_t freeNodeCount;
} ArtMemPerfStatT;
#define DM_MAX_DISTANCE_RANGE_NUM 10
#define DM_MAX_DISTANCE_RANGE_STR_LEN 128
#define DM_MAX_CLUSTER_PATH_LENGTH 128
typedef struct VlIvfClusStatistics {
    char disToCentNumStr[DM_MAX_DISTANCE_RANGE_NUM][DM_MAX_DISTANCE_RANGE_STR_LEN];  // 该簇下向量到簇中心的距离分布
    char path[DM_MAX_CLUSTER_PATH_LENGTH];                                           // 该簇的路径
    uint64_t scanNum;                                                                // 该簇累计被查询的次数
    uint32_t pageNum;                                                                // 该簇使用的页面数量
    uint32_t vectorNum;                                                              // 该簇关联的向量数量
    uint8_t level;                                                                   // 该簇的层数
    float clusterRadius;                                                             // 该簇的半径
    bool isOod;                                                                      // 该簇是否为OOD
} VlIvfClusterStatisticsT;

typedef struct VlIvfIndexStatistics {
    uint64_t totalMemorySize;          // 实际申请的page总大小=totalMemorySize = pageSize * pageCount;
    uint64_t usedMemSize;              // 实际使用的内存空间
    uint32_t pageCount;                // 申请的page总个数
    uint32_t clusterSize;              // 簇的数量
    VlIvfClusterStatisticsT *cluster;  // 簇的统计信息
} VlIvfIndexStatisticsT;

typedef struct {
    uint32_t pageSize;            // the size of each page
    uint32_t totalNodeCount;      // the total number of nodes; totalNodeCount <= pageCount
    uint32_t leafCount;           // the number of leaves
    uint32_t internalNodeCount;   // the number of internal nodes
    uint64_t totalMemorySize;     // totalMemorySize = pageSize * pageCount;
    uint64_t occupiedMemorySize;  // the size of occupied memory
    uint32_t bigKeyPageCount;     // the count of big key page
} BTreeMemStatT;

typedef struct {
    uint64_t insertCount;    // the number of insertions thus far
    uint64_t deleteCount;    // the number of deletions thus far
    uint64_t splitCount;     // the number of splits thus far
    uint64_t freeCount;      // the number of empty node collections thus far
    uint64_t coalesceCount;  // the number of merge sibling nodes thus far, include insertion and deletion
} BTreePerfStatT;

typedef struct BTreeIndexStatistics {
    uint64_t recordCount;     // number of records in the tree
    uint32_t treeHeight;      // the height of the tree
    BTreeMemStatT memStat;    // memory statistics
    BTreePerfStatT perfStat;  // performance statistics
} BTreeIndexStatisticsT;

typedef union IndexStatistics {
    struct {
        uint32_t entryCapacity;  // dir 的capacity
        uint32_t entryUsed;      // entry 的使用个数
        uint32_t segmentCnt;     // segment 的个数
        uint32_t mvPageCnt;      // 使用的多版本页个数，非多版本页场景下为0
        uint32_t scaleInCnt;     // 主键索引成功缩容的次数
        uint32_t cacheLineLen;   // 最大冲突链长度（线性探测长度）
        IndexHashPerfStatT indexHashPerfStat;
    } hashIndex;
    struct {
        uint32_t bucketCnt;     // bucket 个数
        uint32_t bucketSize;    // 每个bucket 大小
        uint32_t nodeCnt;       // 链表节点个数
        uint32_t groupCnt;      // 聚簇个数
        uint32_t maxGroupLen;   // 最长聚簇链长度
        uint32_t averGroupLen;  // 聚簇链平均长度
        IndexHashPerfStatT indexHashPerfStat;
    } hashLinklistIndex;
    struct {
        uint32_t bucketCount;  // bucket总数
        uint32_t bucketUsed;   // 已使用的bucket数量
        uint64_t minListLen;   // 最短聚簇链长度
        uint64_t maxListLen;   // 最长聚簇链长度
        uint64_t averListLen;  // 聚簇链平均长度
        IndexHashPerfStatT indexHashPerfStat;
    } hcIndex;
#ifdef FEATURE_HAC
    struct {
        uint32_t bucketCnt;     // 一级索引一共分配的桶数量
        uint32_t bucketUsed;    // 一级索引已使用的桶数量
        uint64_t minTableSize;  // 二级表最小内存占用
        uint64_t maxTableSize;  // 二级表最大内存占用
        uint64_t avgTableSize;  // 二级表平均内存占用
        uint32_t cacheLineLen;  // 最大冲突链长度（线性探测长度）
        IndexHashPerfStatT indexHashPerfStat;
    } multiHashIndex;
#endif
    struct {
        char indexType[DM_MAX_NAME_LENGTH];
        uint64_t insertDataCount;
        uint32_t nodeSizes[ART_NODE_TYPE_NUM];
        ArtMemPerfStatT artMemPerfStat;
        bool isHashcluster;             // 临时变量，给gmsysview做标注，把HC完全记忆抹除之后将移除
        uint32_t insertKeyCallbackCnt;  // ART插入时调用keycmp回调函数的次数
        uint64_t lpmArrCnt;             // count of lpm address array
    } artIndex;
#ifdef FEATURE_VLIVF
    VlIvfIndexStatisticsT vlivfIndex;  // vlivf索引信息
#endif
    BTreeIndexStatisticsT bTreeIndex;
    struct {
        uint32_t pkCachelineLen;  // 主键索引最大冲突链长度，依据当前实现，固定设置为线性探测长度
        uint32_t localHashCachelineLen;  // localHash最大冲突链长度（二级索引，hash唯一与非唯一）
        uint32_t pkAvgCollisionRate;     // 主键索引全局平均冲突率
        uint32_t localHashAvgCollisionRate;  // localHash索引全局平均冲突率（二级索引，hash唯一与非唯一）
    } indexGlobal;
} IndexStatisticsT;  // 索引的统计信息

typedef struct IndexScaleInCfg {
    double splitTime;    // 入参，长任务分片时间
    uint64_t startTime;  // 入参，分片开始的时间
    bool isOverTime;     // 出参，运行是否超过时间片
    uint8_t reserve;
    uint16_t reserve1;
    uint32_t reserve2;
} IndexScaleInCfgT;

typedef struct IdxFunc {
    Status (*idxCreate)(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr);
    Status (*idxDrop)(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
    void (*idxCommitDrop)(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
    Status (*idxTruncate)(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
    void (*idxCommitTruncate)(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
    Status (*idxOpen)(IndexCtxT *idxCtx);
    void (*idxClose)(IndexCtxT *idxCtx);
    Status (*idxInsert)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
    Status (*idxBatchInsert)(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum);
    Status (*idxDelete)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, IndexRemoveParaT removePara);
    Status (*idxBatchDelete)(
        IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara);
    Status (*idxUpdate)(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara);
    Status (*idxBatchUpdate)(
        IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara);
    Status (*idxEndBatchModify)(IndexCtxT *idxCtx);
    Status (*idxLookup)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr *addr, bool *isFound); /* 可以为空 */
    Status (*idxBatchLookup)(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para);
    Status (*idxBeginScan)(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter);
    Status (*idxScan)(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound);
    void (*idxEndScan)(const IndexCtxT *idxCtx, IndexScanItrT iter);
    Status (*idxSetDirection)(
        IndexCtxT *idxCtx, IndexScanItrT iter, IndexScanDirectionE scanDirection); /* 目前仅btree存在，其他可以为空 */
    Status (*idxUndoInsert)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
    Status (*idxUndoRemove)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
    Status (*idxUndoUpdate)(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara);
    Status (*idxGetKeyCount)(IndexCtxT *idxCtx, IndexKeyT idxKey, uint64_t *count); /* 可以为空 */
    Status (*idxStatView)(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat);
    Status (*idxGetPageSize)(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize);
    Status (*idxScaleIn)(IndexCtxT *idxCtx, IndexScaleInCfgT *idxScaleCfg); /* 可以为空 */
    Status (*idxGetEstimateMemSize)(
        uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size);
    void (*idxGetCtxSize)(size_t *ctxSize, size_t *iterSize);
    Status (*idxPreload)(IndexCtxT *idxCtx, void *userData);
    Status (*idxTableLoad)(IndexCtxT *idxCtx);
    Status (*idxLoad)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
#ifdef FEATURE_LPASMEM
    Status (*idxSetExtendKey)(IndexCtxT *idxCtx, IndexKeyT key, uint32_t offset, uint8_t *value, uint32_t len);
#endif
} IdxFuncT;

typedef struct AnnFunc {
    Status (*annIdxBuild)(IndexCtxT *idxCtx, VecBuildStateParaT *buildStatePara);
    Status (*annIdxUndoBuild)(ShmemPtrT idxShmAddr, uint16_t instanceId);
    Status (*annIdxGetVector)(IndexCtxT *idxCtx, DmValueT *propertyValue);
} AnnFuncT;

void AnnFuncRegister(uint8_t indexType, const AnnFuncT *const idxFunc);

void IdxAmFuncRegister(uint8_t indexType, const IdxFuncT *const idxFunc);

typedef struct LpasMemRedoParam {
    PageIdT vertexId;
    uint32_t edgeOffsetFromVertex;
    uint16_t outDegree;
} LpasMemRedoParamT;

StatusInter RedoLogReplayRegister(RedoMgrT *redoMgr, RedoLogTypeE type, RedoLogReplayFuncT replayFunc);

#define REG_REPLAY_FUNC(seIns, type, func)                                                      \
    do {                                                                                        \
        StatusInter ret;                                                                        \
        if ((ret = RedoLogReplayRegister(((seIns)->redoMgr), type, func)) != STATUS_OK_INTER) { \
            SE_ERROR(ret, "register replay type: %" PRId32 "", type);                           \
            DB_ASSERT(false);                                                                   \
            return ret;                                                                         \
        }                                                                                       \
    } while (0)

typedef struct LpasMemEdge {
    uint32_t numNeighbor;   // 当前节点的邻居节点的数量
    PageIdT *nexts;         // 邻居的vertexID
    float *distance;        // 邻居节点到当前节点的距离
    uint8_t *neighborVecs;  // 邻居节点的PQ-Vectors或raw-vectors
} LpasMemEdgeT;

typedef struct {
    void (*redoChangePageFunc)(
        const LpasMemRedoParamT *redoParam, const LpasMemEdgeT *edge, uint16_t pqVecDim, RedoRunCtxT *redoCtx);
} LpasMemIndexRedoAmT;

void LpasMemIndexRedoSetAmFunc(const LpasMemIndexRedoAmT *am);

typedef void *(*FunctionPointer)(void);

#ifdef __cplusplus
}
#endif
#endif  // SE_INDEX_PLUGIN_H
