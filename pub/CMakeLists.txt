set(MODULE_NAME gmpub)

add_library(${MODULE_NAME} INTERFACE)
target_include_directories(${MODULE_NAME} INTERFACE ${CMAKE_CURRENT_SOURCE_DIR}/include)
if (NOT FEATURE_SIMPLEREL)
    # 嵌入式与非嵌入式都需要 gmc_errno.h
    list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gmc_errno.h)
    if(IDS_HAOTIAN)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/ukre/include/adpt_index_plugin.h)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/ukre/include/db_common_index_plugin.h)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/ukre/include/dm_index_plugin.h)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/ukre/include/se_index_plugin.h)
    endif()
    if(NOT EMBEDDED_MODE)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gmc*.h ${CMAKE_CURRENT_SOURCE_DIR}/include/gm_udf.h)
    endif()

    if (NOT FEATURE_YANG)
        list(REMOVE_ITEM GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gmc_yang.h)
        list(REMOVE_ITEM GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gmc_yang_type.h)
    endif()

    if (FEATURE_SQL)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gme_sql_api.h)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gme_api.h)
    endif()
    if (FEATURE_STREAM AND NOT FEATURE_CS)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gme_api.h)
    endif()


    if(FEATURE_SERVER_FUNC_REG OR FEATURE_CLT_SERVER_SAME_PROCESS)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gm_adpt.h)
        if (EMBEDDED_MODE)
            list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gme_register.h)
        else()
            list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gms.h)
            list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gmc_persist.h)
        endif()
    else()
        list(REMOVE_ITEM GMDB_PUBLIC_HEADERS ${CMAKE_CURRENT_SOURCE_DIR}/include/gm_adpt.h)
    endif()
    if(FEATURE_REPLICATION)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/include/gm_ha.h)
    endif()
else()
    list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${GMDB_ROOT_DIR}/adapter4v1/pub/include/tpc_types.h)
    list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${GMDB_ROOT_DIR}/adapter4v1/pub/include/tpc_api_def.h)
    if (V1_UTILS)
        list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${GMDB_ROOT_DIR}/adapter4v1/pub/include/tpc_stub.h)
    endif()
    list(APPEND GMDB_PUBLIC_HEADERS_TEMP ${GMDB_ROOT_DIR}/adapter4v1/pub/include/db_api_def.h)
endif()

file(GLOB GMDB_PUBLIC_HEADERS ${GMDB_PUBLIC_HEADERS_TEMP})

if (NOT FEATURE_STREAM OR (FEATURE_STREAM AND NOT FEATURE_CS))
    list(REMOVE_ITEM GMDB_PUBLIC_HEADERS ${CMAKE_CURRENT_SOURCE_DIR}/include/gmc_stream.h)
endif()

if (NOT FEATURE_VLIVF)
    list(REMOVE_ITEM GMDB_PUBLIC_HEADERS ${CMAKE_CURRENT_SOURCE_DIR}/include/gmc_vector.h)
endif ()

set_target_properties(${MODULE_NAME} PROPERTIES PUBLIC_HEADER "${GMDB_PUBLIC_HEADERS}")
install(TARGETS ${MODULE_NAME} PUBLIC_HEADER DESTINATION include)
