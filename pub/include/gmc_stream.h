/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-12-25
 */
#ifndef GMC_STREAM_H
#define GMC_STREAM_H

#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @ingroup gmc_stream
 * @par 描述：
 * 申请开启流表polling写入，指定写入表名，返回size长度的buffer
 * @attention buffer请严格限制写入长度和传入的size相等，不允许更高buffer指针指向
 * @param[in] instanceId 要写入的实例id
 * @param[in] labelName 写入的表名
 * @param[in] size 申请的长度
 * @param[out] buffer 返回一段size长度的内存
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcStreamBeginOneWayWrite(uint16_t instanceId, const char *labelName, size_t size, void **buffer);

/**
 * @ingroup gmc_stream
 * @par 描述：调用GmcStreamBeginOneWayWrite后用来结束本次写入，通过abort控制是否实际写入本次数据
 * 结束polling写入
 * @param[in] abort true:丢弃本次写入；false：本次写入是有效的
 */
GMC_EXPORT void GmcStreamEndOneWayWrite(bool abort);

#ifdef __cplusplus
}
#endif

#endif  // GMC_STREAM_H
