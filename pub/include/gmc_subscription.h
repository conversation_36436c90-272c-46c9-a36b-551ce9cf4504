/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_subscription
 * Description: File provide GMC APIs related to subscription.
 *              Function order: DDL\DML\other func.
 *              Please keep new function in its corresponding position.
 * Author:
 * Create: 2021-3-25
 */

#ifndef GMC_SUBSCRIPTION_H
#define GMC_SUBSCRIPTION_H

#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @defgroup gmc_subscription 订阅接口
 */

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 在创建顶点标签后，创建订阅关系。
 * @attention @li
 * 创建订阅关系时，如果以fixed、bytes、bitfield（包含bitfield8、bitfield16、bitfield32、bitfield64）类型字段作为条件字段，
 *  条件值支持设置为十六进制，例如：{ "property":"F0", "value":"0xffffffffffffffffffffffffffffffff" }
 *  @li 对DML查询过滤条件中的“FFFFF”的方式不支持去除0x的变更，会保存原来的直接使用其值的，包含0x字符串的值。
 *  @li
 * 输入的合法十六进制字符串需以0x/0X开头，且所有的字符必须是十六进制里的标准合法字符，如果有一个字符不符合，则会退回到非十六进制支持模式，
 *  例如“0xGH”形式的值会直接解析为字符串。
 *  @li fixed类型的输入数据的值，类似"nhp_svc_context_normal_prio", "type": "fixed", "size": 16,
 *  "default":
 * "0xffffffffffffffffffffffffffffffff"，对应的default值长度在以0x开头的情况下，为size*2+2，在非以0x开头的情况下，输入的数据长度为size的长度。
 *  @li bytes类型的输入数据的值，类似"nhp_svc_context_normal_prio", "type": "bytes", "size": 16,
 *  "default": "0xffffffffffffffffffffffffffffffff"，对应的default值长度在以0x开头的情况下，小于等于size*2+2，
 *  在非以0x开头的情况下，输入的数据长度为小于等于size的长度。
 *  @li
 * 请勿随意更改入参stmt、channel结构，以GMDB接口构造为准，入参config、userCb需严格按照GmcSubConfigT，GmcSubCallbackT结构体及函数定义构造，上述入参安全性均由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] config 订阅关系的配置json，请参见GmcSubConfigT结构体。
 * @param[in] channel 订阅连接通道。
 * @param[in] userCb 订阅回调函数，参考GmcSubCallbackT。
 * @param[in] userData 用户data。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSubscribe(
    GmcStmtT *stmt, const GmcSubConfigT *config, const GmcConnT *channel, GmcSubCallbackT userCb, void *userData);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 在执行完订阅操作后，删除订阅关系，并释放资源。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] subName 待删除的订阅关系名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcUnSubscribe(GmcStmtT *stmt, const char *subName);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 暂停合并订阅回调（暂停后连接对应的所有订阅关系停止回调）
 * @attention
 * @li 此接口仅限于合并订阅
 * @li 请勿修改入参subConn结构，以GMDB接口构造为主，安全性由调用者保护
 * @li 如需继续消费，请调用GmcStmgConnResume
 * @param[in] subConn 订阅连接
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcStmgConnSuspend(GmcConnT *subConn);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 继续合并订阅回调。
 * @attention
 * @li 此接口仅限于合并订阅
 * @li 请勿修改入参subConn结构，以GMDB接口构造为主，安全性由调用者保护
 * @li 消费者使用此接口继续订阅消费，同GmcStmgConnSuspend成对使用
 * @param[in] subConn 订阅连接
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcStmgConnResume(GmcConnT *subConn);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 单表订阅中，获取推送的主键值（对应创建订阅关系时的"key"）。
 * @attention @li 对于KV表，用户拿到的主键值即为KV的键值。
 *  @li
 * 对于VertexLabel表，用户拿到的主键值实际是一个序列化好的内部数据结构，如果用户不了解其实现原理，不建议尝试直接读取其内容。
 *  @li
 * 对于GMC_SUB_EVENT_DELETE、GMC_SUB_EVENT_KV_REMOVE、GMC_SUB_EVENT_AGED事件，因为对应的记录已被删除，后续可能无法通过主键值继续操作该条记录。
 *  @li 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[out] key 主键，在接收buffer重置之前有效。\n
 * 说明：\n
 *  @li 对于KV表，用户拿到的主键值即为KV的键值。
 *  @li 对于VERTEX表，用户拿到的主键值实际是一个序列化好的内部数据结构，用户如果不了解其实现，不建议尝试直接读取其内容。
 *  @li 对于DELETE、KV_REMOVE、AGED事件，因为对应的记录已经被删除，后续可能无法通过主键值继续操作该条记录。
 * @param[out] size key所需的buffer大小。如果不关心key的实际大小，size可为NULL。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSubGetKey(GmcStmtT *stmt, const void **key, uint32_t *size);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 一般用于GmcSubscribe接口的回调函数中，获取订阅推送顶点的标签名称。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准。出参name大小与size需要保证对应关系准确，其安全性由调用者保证
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] index 对于简单path订阅，传入表在path中的索引序号。对于非简单path订阅，参数将被忽略。
 * @param[out] name 用于保存顶点标签名称的缓冲区。
 * @param[out] size 缓冲区的容量大小。
 * 说明：\n
 *  @li 如果成功，size将返回strlen(name)
 *  @li 如果输出缓冲区太小，无法容纳顶点标签名称，size将返回预期容量
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSubGetLabelName(GmcStmtT *stmt, uint32_t index, char *name, uint32_t *size);

/* msg type of subscription */
#define GMC_SUB_MSG_OLD_DATA 0x00000001
#define GMC_SUB_MSG_NEW_DATA 0x00000002
#define GMC_SUB_MSG_KEY_DATA 0x00000004
#define GMC_SUB_MSG_DELTA_DATA 0x00000008

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 一般用于GmcSubscribe接口的回调函数中，单表订阅时，获取当前实际可用的消息类型。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[out] msgType 当前实际可用的消息类型。
 * 说明：\n
 * GmcSubMsgInfoT::msgType是用户订阅的消息类型，如果用户同时订阅了old object和new object，
 * GmcSubMsgInfoT::msgType会等于(GMC_SUB_MSG_OLD_DATA | GMC_SUB_MSG_NEW_DATA)。\n
 * 但是，对于GMC_SUB_EVENT_REPLACE和GMC_SUB_EVENT_KV_SET事件，旧对象不一定会存在，
 * 此时，可以通过该接口获取实际可用的消息类型，得到的msgType会等于GMC_SUB_MSG_NEW_DATA。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSubGetMsgType(GmcStmtT *stmt, uint32_t *msgType);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 一般用于GmcSubscribe接口的回调函数中，在增量订阅中，指定获取旧顶点或新顶点。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] mode 获取数据的方式，请参见GmcSubFetchModeE。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSubSetFetchMode(GmcStmtT *stmt, GmcSubFetchModeE mode);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 单表订阅中，触发一次全表数据推送。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] subName 订阅关系名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSubTriggerScan(GmcStmtT *stmt, const char *subName);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 在创建分区表后，开启对账。
 * @attention @li
 * 如果由于冲突链过长导致事务锁申请超时，从而造成开启对账失败并且返回错误码GMERR_LOCK_NOT_AVAILABLE时，需要客户端进行重试开启对账操作。
 *  @li 开启对账会触发事务提交。
 *  @li 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] labelName
 * 标签名称，目前仅支持顶点标签，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @param[in] partitionId 如果此标签支持分区，请指定分区索引，否则指定GMC_FULL_Table
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcBeginCheck(GmcStmtT *stmt, const char *labelName, uint8_t partitionId);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 结束对账。
 * @attention @li 如果由于冲突链过长,
 * 导致事务锁申请超时，从而造成结束对账失败并且返回错误码GMERR_LOCK_NOT_AVAILABLE，需要客户端进行重试结束对账操作。
 *  @li 结束对账会触发事务提交。
 *  @li 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] labelName
 * 标签名称，目前仅支持顶点标签，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @param[in] partitionId 如果此标签支持分区，请指定分区索引，否则指定GMC_FULL_Table
 * @param[in] isAbnormal 如果为true，将中止当前帐户检查会话，并且没有数据将过期
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcEndCheck(GmcStmtT *stmt, const char *labelName, uint8_t partitionId, bool isAbnormal);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 结束对账。
 * @attention @li 如果由于冲突链过长,
 * 导致事务锁申请超时，从而造成结束对账失败并且返回错误码GMERR_LOCK_NOT_AVAILABLE，需要客户端进行重试结束对账操作。
 *  @li 结束对账会触发事务提交。
 *  @li 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] labelName
 * 标签名称，目前仅支持顶点标签，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @param[in] isAbnormal 如果为true，将中止当前帐户检查会话，并且没有数据将过期
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcEndAllPartitionCheck(GmcStmtT *stmt, const char *labelName, bool isAbnormal);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 获取对账数据信息句柄。
 * @attention
 * 请勿随意更改入参stmt结构，以GMDB接口构造为准。出参checkInfo需严格按照GmcCheckInfoT结构体及函数定义构造，上述入参安全性均由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] labelName
 * 标签名称，目前仅支持顶点标签，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @param[in] partitionId 如果此标签支持分区，请指定分区索引，否则指定GMC_FULL_Table
 * @param[out] checkInfo 指定标签的检查信息\n
 *  它将一直有效，直到父stmt被释放，或被对GmcGetCheckInfo的另一个调用覆盖
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetCheckInfo(
    GmcStmtT *stmt, const char *labelName, uint8_t partitionId, GmcCheckInfoT **checkInfo);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 结束对账后，获取对账状态。如果未开启对账，则获取到的对账状态为GMC_CHECK_STATUS_NORMAL。
 * @attention 请勿随意更改入参checkInfo，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] checkInfo GmcGetCheckInfo返回的句柄
 * @param[out] checkStatus 检查状态
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetCheckStatus(const GmcCheckInfoT *checkInfo, GmcCheckStatusE *checkStatus);

/**
 * @ingroup gmc_subscription
 * @par 描述：
 * 在调用GmcBeginCheck开启对账之后，并使用GMC_OPERATION_CHECK_REPLACE进行replace操作时设置的用户比较函数。
 * 注：这里为了兼容V3会做转换，如果prepare时类型为GMC_OPERATION_REPLACE就转换为GMC_OPERATION_CHECK_REPLACE
 * @attention @li 此接口需要在prepare GMC_OPERATION_CHECK_REPLACE或GMC_OPERATION_REPLACE后使用，否则报错。
 * @li 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] compareFunc check replace的用户比较函数，在类型为GMC_OPERATION_REPLACE时允许为NULL。
 * @param[in] userData 用户传入的check replace对账的数据，需要和使用stmt设置的值一样。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSetCheckReplaceCmpFunc(GmcStmtT *stmt, GmcCheckReplaceCmpFuncT compareFunc, void *userData);

#ifdef __cplusplus
}
#endif

#endif /* GMC_SUBSCRIPTION_H */
