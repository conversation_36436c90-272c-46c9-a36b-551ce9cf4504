/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: Interfaces for user designated function registration.
 * Author: lixicheng
 * Create Date : 2025
 */

#ifndef GM_ADPT_H
#define GM_ADPT_H

#include <stdbool.h>
#include <inttypes.h>
#include <stdio.h>
#include <sys/shm.h>
#include <pthread.h>
#include <gmc_types.h>

#ifdef __cplusplus
extern "C" {
#endif

#if defined(__GNUC__)
#define GM_EXPORT __attribute__((visibility("default")))
#else
#define GM_EXPORT
#endif

/**
 * @defgroup gms 服务端接口
 */

typedef const void *GmsMemInitArgs;
typedef const void *GmsMemAllocArgs;
typedef void *GmsMemFreePtr;
typedef const void *GmsMemFreeArgs;
typedef const void *GmsMemFinalizeArgs;
typedef const void *GmsShmGetArgs;
typedef const void *GmsShmAtArgs;
typedef const void *GmsShmDtArgs;
typedef const void *GmsShmCtlArgs;
typedef pthread_t GmsThreadHandle;

typedef struct {
    uint32_t hungLevel1Ms;
    uint32_t hungLevel2Ms;
    uint32_t hungLevel3Ms;
} GmsMonitorThresholdT;

typedef enum {
    GM_EPOLL_CTL_ADD = 0,
    GM_EPOLL_CTL_DEL = 1,
    GM_EPOLL_CTL_MOD = 2,
    GM_EPOLL_CTL_BUTT,
} GmEpollCtlOpE;

typedef enum {
    GM_PIPE_FD = 0,
    GM_COMMON_FD = 1,
    GM_FD_BUTT,
} GmFdTypeE;

typedef struct {
    uint8_t family;
    uint8_t reverd;
    uint16_t port;   // 传入的大端uint16_t
    uint8_t ip[16];  // 传入的大端uint32_t
} PipeAddr;

/**
 * @ingroup gms
 * @par 描述：日志句柄初始化；允许不注册该接口，则写日志时，传NULL指针至GmsLogWriteFuncT接口。
 * @return 日志句柄，返回NULL时认为调用接口失败。
 */
typedef void *(*GmsLogHandleInitFuncT)(void);

/**
 * @ingroup gms
 * @par 描述：日志句柄销毁，服务端退出时调用。
 * GmsLogHandleInitFuncT未注册时，不注册该接口；注册GmsLogHandleInitFuncT时，要求注册该接口。
 */
typedef void (*GmsLogHandleUnInitFuncT)(void *logHandle);

/**
 * @ingroup gms
 * @par 描述：写日志接口。
 * @param[in] logHandle 日志句柄。通过注册的GmsLogHandleInitFuncT获取，若不注册，则入参传NULL
 * @param[in] level 日志级别。
 * 0：EMERGENCY，紧急日志；1：ERROR，错误日志；2：WARN，告警日志；3.INFO，信息日志；4.DEBUG，debug日志
 * @param[in] errorCode 错误码。
 * @param[in] str 日志内容，必定包含'\0'。
 */
typedef void (*GmsLogWriteFuncT)(void *logHandle, uint32_t level, int32_t errorCode, const char *str);

/**
 * @ingroup gms
 * @par 描述：设置日志打印级别接口。
 * @param[in] logHandle 日志句柄。通过注册的GmsLogHandleInitFuncT获取，若不注册，则入参传NULL
 * @param[in] level 日志级别。
 * 0：EMERGENCY，紧急日志；1：ERROR，错误日志；2：WARN，告警日志；3.INFO，信息日志；4.DEBUG，debug日志
 * @param[in] duration 持续时间，单位s，取值范围：[0,2147483647]，0表示持续时间无限。
 */
typedef void (*GmsLogLevelSetFuncT)(void *logHandle, uint32_t level, uint32_t durationSec);

/**
 * @ingroup gms
 * @par 描述：动态内存申请，用于DB向底层申请内存。
 * @param[in] size 内存申请大小。
 * @param[in] args 申请内存入参，由上层初始化时注册，可以为NULL。
 * @return 动态内存段虚拟地址addr，失败返回-1。
 */
typedef void *(*GmsMemAllocFuncT)(uint32_t size, GmsMemAllocArgs args);

/**
 * @ingroup gms
 * @par 描述：动态内存去初始化函数，用于服务端结束时。
 * @param[in] args 去初始化入参，由上层自行注册，可以为NULL。
 * @return 成功返回GMERR_OK，失败返回错误码。
 */
typedef int32_t (*GmsMemFinalizeFuncT)(GmsMemFinalizeArgs args);

/**< 。 */
/**
 * @ingroup gm
 * @par 描述：用户指定的内存申请函数单次申请的上限。
 * @return 返回用户指定的内存申请函数单次申请的上限。
 */
typedef uint32_t (*GmsMemSetMaxStepSizeFuncT)(void);

/**
 * @ingroup gms
 * @par 描述：动态内存释放，用于DB向底层释放内存。
 * @param[in] ptr 内存释放指针。
 * @param[in] args 释放内存参数，由上层初始化时注册，可以为NULL。
 * @return 无。
 */
typedef void (*GmsMemFreeFuncT)(GmsMemFreePtr ptr, GmsMemFreeArgs args);

/**
 * @ingroup gms
 * @par 描述：动态内存初始化函数，用于服务端启动前，较早初始化。
 * @param[in] args 初始化入参，由上层自行注册，可以为NULL。
 * @return 成功返回GMERR_OK，失败返回错误码。
 */
typedef int32_t (*GmsMemInitFuncT)(GmsMemInitArgs args);

/**
 * @ingroup gms
 * @par 描述：共享内存初始化函数，用于服务端启动前，较早初始化。
 * @param[in] shmInitArgs 初始化入参，由上层自行注册，可以为NULL。
 * @return 成功返回GMERR_OK，失败返回错误码。
 */
typedef int32_t (*GmsShmInitFuncT)(void *shmInitArgs);

/**
 * @ingroup gms
 * @par 描述：回收共享内存函数类型指针。
 * @param[in] shmFinalizeArgs 共享内存回收参数。
 * @return 成功返回GMERR_OK，失败返回错误码。
 */
typedef int32_t (*GmsShmFinalizeFuncT)(void *shmFinalizeArgs);

/**
 * @ingroup gms
 * @par 描述：创建共享内存。
 * @param[in] key 共享内存段标识符。
 * @param[in] size 共享内存大小。
 * @param[in] shmFlag 权限和其他选项，例如 0660|IPC_CREAT,目前只有(int32_t)(shmFlag|(uint32_t)IPC_CREAT)和0两种传值。
 * @param[in] shmGetArgs 创建共享内存参数。
 * @return shm_id，失败返回-1。
 */
typedef int32_t (*GmsShmGetFuncT)(int32_t key, uint32_t size, uint32_t shmFlag, GmsShmGetArgs shmGetArgs);

/**
 * @ingroup gms
 * @par 描述：映射共享内存。如果成功，系统将内存段的引用计数+1。直连写地址集中管理会指定shmaddr
 *      普通场景shmflg为共享内存权限，如读写（0）和只读（010000 SHM_RDONLY，
 *      直连写地址集中管理会传((int32_t)shmFlag | SHM_REMAP | SHM_RND)和(SHM_REMAP | SHM_RND)
 * @param[in] shmId 共享内存id。
 * @param[in] addr 调用者希望映射的地址,普通场景为NULL，直连写场景指定。
 * @param[in] shmFlag 权限和其他映射选项。
 * @param[in] shmAtArgs 映射共享内存参数。
 * @return 共享内存段虚拟地址addr，失败返回-1。
 */
typedef void *(*GmsShmAtFuncT)(int32_t shmId, void *addr, uint32_t shmFlag, GmsShmAtArgs shmAtArgs);

/**
 * @ingroup gms
 * @par 描述：解映射共享内存。如果成功，系统将内存段的引用计数-1。
 * @param[in] addr 共享内存段首虚拟地址。
 * @param[in] shmDtArgs 解映射共享内存参数。
 * @return 成功返回0，失败返回-1。
 */
typedef int32_t (*GmsShmDtFuncT)(void *addr, GmsShmDtArgs shmDtArgs);

/**
 * @ingroup gms
 * @par 描述：操作一个共享内存段。
 * @param[in] shmId 共享内存id。
 * @param[in] cmd 操作类型,DB目前会传IPC_RMID销毁共享内存段。
 * @param[in] ds 检查共享内存段权限是否匹配。[ds]:指向shmid_ds结构体的指针，内含共享内存段的信息。
 * @param[in] shmCtlArgs 操作共享内存参数。
 * @return 成功返回0，失败返回-1。
 */
typedef int32_t (*GmsShmCtlFuncT)(int32_t shmId, int32_t cmd, struct shmid_ds *ds, GmsShmCtlArgs shmCtlArgs);

typedef struct {
    int32_t fd; /**< socket通信fd */
    void *ctx;  /**< 通信句柄上下文，用于拓展*/
} GmsSockPipeT;

/**
 * @ingroup gms
 * @par 描述：服务端创建阻塞式监听句柄。
 * @param[in] ip 入参，服务端监听IP。"************"
 * @param[in] port 入参，监听端口号。
 * @param[out] pipe 出参，监听句柄，接受客户端建连请求。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsTcpOpenListenT)(const char *ip, uint16_t port, GmsSockPipeT *pipe);

/**
 * @ingroup gms
 * @par 描述：客户端向服务端发送建连请求。
 * @param[in] lsnrPipe 监听句柄,由GmsUsockOpenListen创建
 * @param[out] acceptPipe 与客户端通信句柄，不能为NULL。
 * @param[in] timeoutMs accept超时时间，未收到建连请求，接口同样需要返回。
 * @param[in] bufSize 设置接受到的通信句接收和发送缓存的大小。
 * @param[in] nonBlock 设置接受到的通信句柄接收和发送模式为阻塞或非阻塞。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsTcpAcceptT)(
    const GmsSockPipeT *lsnrPipe, GmsSockPipeT *acceptPipe, int32_t timeoutMs, uint32_t bufSize, bool nonBlock);

/**
 * @ingroup gms
 * @par 描述：客户端向服务端发送建连请求。
 * @param[in] ip 入参，服务端监听IP。"************"
 * @param[in] port 入参，监听端口号。
 * @param[out] pipe 客户端通信句柄。
 * @param[in] bufSize 设置通信句柄的接收和发送缓存的大小。
 * @param[in] nonBlock 设置通信句柄接收和发送模式为阻塞或非阻塞。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmTcpConnectT)(const char *ip, uint16_t port, GmsSockPipeT *pipe, uint32_t bufSize, bool nonBlock);

/**
 * @ingroup gms
 * @par 描述：获取对端IP地址+端口信息。
 * @param[in] pipe 客户端通信句柄。
 * @param[out] ip 对端IP地址。
 * @param[in] size IP地址缓存长度。
 * @param[out] port 对端端口信息。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmTcpGetPeerName)(GmsSockPipeT *pipe, char *ip, size_t size, uint16_t *port);

/**
 * @ingroup gms
 * @par 描述：获取本端IP地址+端口信息。
 * @param[in] pipe 客户端通信句柄。
 * @param[out] ip 本端IP地址。
 * @param[in out] size IP地址缓存区长度.
 * @param[out] port 对端端口信息。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmTcpGetLocalName)(GmsSockPipeT *pipe, char *ip, size_t size, uint16_t *port);

/**
 * @ingroup gms
 * @par 描述：根据用户名获取本端校验值。
 * @param[in] pipe 客户端通信句柄。
 * @param[in] userName 用户名。
 * @param[in] userNameLenth 用户名长度，包含'\0'。
 * @param[out] pwd 校验值。
 * @param[in] pwdLenth 校验值地址缓存区长度.
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmTcpGetCredit)(
    GmsSockPipeT *pipe, const char *userName, size_t userNameLenth, char *pwd, size_t pwdLenth);

/**
 * @ingroup gms
 * @par 描述：控制该ip地址的通信规则。
 * @param[in] ip 入参，服务端监听IP。"************".
 * @param[in] cmd 控制命令0为允许连接，1为拒绝连接.
 * @return @li 返回0，为成功@li其他返回值视为失败.
 */
typedef int32_t (*GmTcpIpControl)(const char *ip, uint32_t cmd, ...);

/**
 * @ingroup gms
 * @par 描述：服务端创建阻塞式监听句柄。
 * @param[in] unixDomain 通信域文件路径。
 * @param[out] pipe 出参，监听句柄，接受客户端建连请求。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsUsockOpenListenT)(const char *unixDomain, GmsSockPipeT *pipe);

/**
 * @ingroup gms
 * @par 描述：客户端向服务端发送建连请求。
 * @param[in] lsnrPipe 监听句柄,由GmsUsockOpenListen创建
 * @param[out] acceptPipe 与客户端通信句柄，不能为NULL。
 * @param[in] timeoutMs accept超时时间，未收到建连请求，接口同样需要返回。
 * @param[in] bufSize 设置接受到的通信句接收和发送缓存的大小。
 * @param[in] nonBlock 设置接受到的通信句柄接收和发送模式为阻塞或非阻塞。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsUsockAcceptT)(
    const GmsSockPipeT *lsnrPipe, GmsSockPipeT *acceptPipe, int32_t timeoutMs, uint32_t bufSize, bool nonBlock);

/**
 * @ingroup gms
 * @par 描述：客户端向服务端发送建连请求。
 * @param[in] unixDomain 通信域文件路径，不能为NULL。
 * @param[out] pipe 客户端通信句柄。
 * @param[in] clientPath 客户端bind绑定路径，可传NULL。
 * @param[in] bufSize 设置通信句柄的接收和发送缓存的大小。
 * @param[in] nonBlock 设置通信句柄接收和发送模式为阻塞或非阻塞。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsUsockConnectT)(
    const char *unixDomain, GmsSockPipeT *pipe, const char *clientPath, uint32_t bufSize, bool nonBlock);

/**
 * @ingroup gms
 * @par 描述：服务端创建阻塞式监听句柄。
 * @param[in] addr 通信域地址结构体。
 * @param[out] pipe 出参，监听句柄，接受客户端建连请求。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsUsockOpenListen2T)(PipeAddr *addr, GmsSockPipeT *pipe);

/**
 * @ingroup gms
 * @par 描述：客户端向服务端发送建连请求。
 * @param[in] peer 对端通信地址。
 * @param[out] pipe 客户端通信句柄。
 * @param[in] local 本地通信地址。
 * @param[in] bufSize 设置通信句柄的接收和发送缓存的大小。
 * @param[in] nonBlock 设置通信句柄接收和发送模式为阻塞或非阻塞。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsUsockConnect2T)(
    PipeAddr *peer, GmsSockPipeT *pipe, PipeAddr *local, uint32_t bufSize, bool nonBlock);

/**
 * @ingroup gms
 * @par 描述：服务端创建阻塞式监听句柄。
 * @param[in] addr 入参，通信地址。
 * @param[out] pipe 出参，监听句柄，接受客户端建连请求。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsTcpOpenListen2T)(PipeAddr *addr, GmsSockPipeT *pipe);

/**
 * @ingroup gms
 * @par 描述：客户端向服务端发送建连请求。
 * @param[in] addr 入参，通信地址。
 * @param[out] pipe 客户端通信句柄。
 * @param[in] bufSize 设置通信句柄的接收和发送缓存的大小。
 * @param[in] nonBlock 设置通信句柄接收和发送模式为阻塞或非阻塞。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmTcpConnect2T)(PipeAddr *addr, GmsSockPipeT *pipe, uint32_t bufSize, bool nonBlock);

/**
 * @ingroup gms
 * @par 描述：获取对端IP地址+端口信息。
 * @param[in] pipe 客户端通信句柄。
 * @param[in] addr 通信对端地址。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmTcpGetPeerName2T)(GmsSockPipeT *pipe, PipeAddr *addr);

/**
 * @ingroup gms
 * @par 描述：获取本端IP地址+端口信息。
 * @param[in] pipe 客户端通信句柄。
 * @param[in] addr 本端通信地址。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmTcpGetLocalName2T)(GmsSockPipeT *pipe, PipeAddr *addr);

/**
 * @ingroup gms
 * @par 描述：关闭通信句柄。
 * @param[in] pipe 通信句柄。
 */
typedef void (*GmsSockClosePipeT)(GmsSockPipeT *pipe);

/**
 * @ingroup gms
 * @par 描述：获取对端信息。
 * @param[in] pipe 通信句柄。
 * @param[out] pid 通信句柄。
 * @param[out] uid 通信句柄。
 * @param[out] gid 通信句柄。
 */
typedef int32_t (*GmsUsockGetCredT)(const GmsSockPipeT *pipe, uint32_t *pid, uint32_t *uid, uint32_t *gid);

/**
 * @ingroup gms
 * @par 描述：设置通信句柄阻塞属性。
 * @param[in] pipe 通信句柄。
 * @param[in] nonBlock true则非阻塞，false阻塞。
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsSockSetNoBlockT)(GmsSockPipeT *pipe, bool nonBlock);

/**
 * @ingroup gms
 * @par 描述：设置阻塞通信句柄的接收阻塞时间
 * 例如设置2000ms，则调用阻塞接收接口BlockSockRecv，最迟2s后返回结果。
 * @param[in] pipe 通信句柄。
 * @param[in] timeoutMs 超时时间
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsBlockSockSetRecvTimeoutT)(GmsSockPipeT *pipe, uint32_t timeoutMs);

/**
 * @ingroup gms
 * @par 描述：设置阻塞通信句柄的发送阻塞时间
 * 例如设置2000ms，则调用阻塞发送接口BlockSockSend，最迟2s后返回结果。
 * @param[in] pipe 通信句柄。
 * @param[in] timeoutMs 超时时间
 * @return @li 返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsBlockSockSetSendTimeoutT)(GmsSockPipeT *pipe, uint32_t timeoutMs);

/**
 * @ingroup gms
 * @par 描述：阻塞通信句柄发送
 * @param[in] pipe 通信句柄
 * @param[in] buff 发送缓存
 * @param[in] size 缓存大小，字节
 * @param[out] sendSize 实际发送的大小，单位字节；可以为NULL
 * @return @li SOKET_PIPE_SUCCESS，操作成功
 * @return @li SOKET_PIPE_PEER_CLOSED，对端关闭
 * @return @li SOKET_PIPE_SEND_BUFFER_FULL，未发送够指定字节数
 * @return @li 其他
 */
typedef int32_t (*GmsBlockSockSendT)(const GmsSockPipeT *pipe, const char *buff, uint32_t size, uint32_t *sendSize);

/**
 * @ingroup gms
 * @par 描述：阻塞通信句柄接收
 * @param[in] pipe 通信句柄
 * @param[in] buff 接收缓存
 * @param[in] size 缓存大小，字节
 * @param[out] recvSize 实际接收的大小，单位字节；可以为NULL
 * @return @li SOKET_PIPE_SUCCESS，操作成功
 * @return @li SOKET_PIPE_PEER_CLOSED，对端关闭
 * @return @li SOKET_PIPE_RECV_TIMEOUT，未接收够指定字节数
 * @return @li 其他
 */
typedef int32_t (*GmsBlockSockRecvT)(const GmsSockPipeT *pipe, char *buff, uint32_t size, uint32_t *recvSize);

/**
 * @ingroup gms
 * @par 描述：非阻塞通信句柄发送
 * @param[in] pipe 通信句柄
 * @param[in] buff 发送缓存
 * @param[in] size 缓存大小，字节
 * @param[in] timeoutMs 阻塞时间，为0时，表示不阻塞
 * @param[out] sendSize 实际发送的大小，单位字节；可以为NULL
 * @return @li SOKET_PIPE_SUCCESS，操作成功
 * @return @li SOKET_PIPE_PEER_CLOSED，对端关闭
 * @return @li SOKET_PIPE_SEND_BUFFER_FULL，未发送够指定字节数
 * @return @li 其他
 */
typedef int32_t (*GmsNonBlockSockSendT)(
    const GmsSockPipeT *pipe, const char *buff, uint32_t size, uint32_t timeoutMs, uint32_t *sendSize);

/**
 * @ingroup gms
 * @par 描述：非阻塞通信句柄接收
 * @param[in] pipe 通信句柄
 * @param[in] buff 接收缓存
 * @param[in] size 缓存大小，字节
 * @param[in] timeoutMs 阻塞时间，为0时，表示不阻塞
 * @param[out] recvSize 实际接收的大小，单位字节；可以为NULL
 * @return @li SOKET_PIPE_SUCCESS，操作成功
 * @return @li SOKET_PIPE_PEER_CLOSED，对端关闭
 * @return @li SOKET_PIPE_RECV_TIMEOUT，未接收够指定字节数
 * @return @li 其他
 */
typedef int32_t (*GmsNonBlockSockRecvT)(
    const GmsSockPipeT *pipe, char *buff, uint32_t size, uint32_t timeoutMs, uint32_t *recvSize);

/**
 * @ingroup gms
 * @par 描述：获取通道接收缓存大小，即GmsUsockAccept、GmsUsockConnect入参bufSize大小
 * @param[in] pipe 通信句柄
 * @param[out] recvBuffSize 单位字节。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsSockGetRecvBuffSizeT)(GmsSockPipeT *pipe, uint32_t *recvBuffSize);

/**
 * @ingroup gms
 * @par 描述：获取通道发送缓存大小，即GmsUsockAccept、GmsUsockConnect入参bufSize大小
 * @param[in] pipe 通信句柄
 * @param[out] sendBuffSize 单位字节。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsSockGetSendBuffSizeT)(GmsSockPipeT *pipe, uint32_t *sendBuffSize);

/**
 * @ingroup gms
 * @par 描述：获取通道未接收的缓存大小
 * @param[in] pipe 通信句柄
 * @param[out] unRecvSize 单位字节。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsSockGetUnRecvBytesT)(GmsSockPipeT *pipe, uint32_t *unRecvSize);

/**
 * @ingroup gms
 * @par 描述：获取通道未发送的缓存大小
 * @param[in] pipe 通信句柄
 * @param[out] unSendSize 单位字节。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsSockGetUnSendBytesT)(GmsSockPipeT *pipe, uint32_t *unSendSize);

/**
 * @ingroup gms
 * @par 描述：创建epoll
 * @param[in] maxEvent 支持监听的最大事件数
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmEpollCreateT)(uint32_t maxEvent);

/**
 * @ingroup gms
 * @par 描述：添加、删除epoll事件
 * @param[in] epollFd epoll句柄
 * @param[in] op 操作类型
 * @param[in] fd 操作的fd
 * @param[in] event fd事件配置
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmEpollCtlT)(int32_t epollFd, GmEpollCtlOpE op, int32_t fd, void *event);

/**
 * @ingroup gms
 * @par 描述：等待epoll事件
 * @param[in] epollFd epoll句柄
 * @param[out] events 活跃的事件队列
 * @param[in] maxEvents 最大事件数
 * @param[in] timeoutMs 等待超时时间，Unit：毫秒
 * @return @li返回-1，epoll故障 @li返回0，无事件 @li返回大于0
 */
typedef int32_t (*GmEpollWaitT)(int32_t epollFd, void *events, int32_t maxEvents, int32_t timeoutMs);

/**
 * @ingroup gms
 * @par 描述：关闭epoll
 * @param[in] epollFd epoll句柄
 */
typedef void (*GmEpollCloseT)(int32_t epollFd);

/**
 * @ingroup gms
 * @par 描述：设置epoll 单个事件大小
 * @return @li返回单个事件大小，Unit:B
 */
typedef uint16_t (*GmEpollGetEventSizeT)(void);

/**
 * @ingroup gms
 * @par 描述：添加事件时，设置事件监听类型、用户自定义数据
 * @param[in] event 事件
 * @param[in] eventMask 监听事件掩码
 * @param[in] ptr 用户自定义数据
 * @param[in] fdType fd类型：通信fd、普通OS fd
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmEpollSetEventT)(void *event, uint32_t eventMask, void *ptr, GmFdTypeE fdType);

/**
 * @ingroup gms
 * @par 描述：事件触发时，获取事件监听类型、用户自定义数据
 * @param[in] event 事件
 * @param[out] eventMask 触发事件掩码
 * @param[out] ptr 用户自定义数据
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmEpollGetEventT)(void *event, uint32_t *eventMask, void **ptr);

/**
 * @ingroup gms
 * @par 描述：获取用户保留内存
 * @param[out] addr 保留内存地址
 * @param[out] size 保留内存大小
 * @param[out] forRecovery 是否用保留内存恢复数据
 */
typedef void (*GmsGetRedoBufferReservedSegmentFuncT)(uint8_t **addr, uint32_t *size, bool *forRecovery);

/**
 * @ingroup gms
 * @par 描述：持久化压缩函数，用于压缩持久化落盘数据，内存由调用者管理，函数内部不做内存的申请
 * @param[out] dest 压缩后的buf
 * @param[in and out] destSize 入参会做为dest的可用大小，出参为压缩后buf的长度
 * @param[in] src 待压缩的buf
 * @param[in] srcSize 待压缩buf的长度
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsPersistCompressFuncT)(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize);

/**
 * @ingroup gms
 * @par 描述：持久化解压函数，用于解压持久化落盘数据，内存由调用者管理，函数内部不做内存的申请
 * @param[out] dest 解压后的buf
 * @param[in and out] destSize 入参会做为dest的可用大小，出参为解压后buf的长度
 * @param[in] src 待解压的buf
 * @param[in] srcSize 待解压buf的长度
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsPersistDecompressFuncT)(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize);

/**
 * @ingroup gms
 * @par 描述：根据输入的数据，计算一个数据摘要
 * @param[in] data 需要计算摘要的数据buffer
 * @param[in] dataLen 数据buffer的长度
 * @param[out] digest 最终计算出的摘要，摘要的长度固定为32字节
 * @param[in] digestLen 输入的摘要buffer的长度，固定位32字节
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsDigest32GenerateFuncT)(uint8_t *data, uint32_t dataLen, uint8_t *digest, uint32_t digestLen);

/**
 * @ingroup gms
 * @par 描述：持久文件名转换函数，用于将原始的持久化文件名字转换成用户自定义的文件名字
 * @param[in] src 原始持久化文件名字符串
 * @param[out] dest 用户接收用户自定义的文件名buffer，用户将自定义文件名写入该buffer
 * @param[in] destSize 用户自定义的文件名buffer的长度
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsPersistFileNameFilterFuncT)(const char *src, char *dest, uint32_t destSize);

/* 线程操作相关数据结构 */
/* 任务执行函数 */
typedef void *(*GmsThreadEntryProc)(void *param);
/* 任务退出函数 */
typedef void *(*GmsThreadExitProc)(void *param);
/* 线程创建参数结构体 */
typedef struct {
    char name[32];
    int32_t priority; /* 任务优先级 */
    int32_t type;     /* HPE环境不用 */
    GmsThreadEntryProc entryFunc;
    GmsThreadExitProc exitFunc;
    void *entryArgs; /* 任务执行函数的参数 */
    void *exitArgs;  /* 任务退出函数的参数 */
    uint32_t stackSize;
    uint32_t cpu;         /* 逻辑CPU */
    uint32_t bindCpuFlag; /* 绑核属性标志:1标识绑核，0标识不指定核 */
    void *userAttr;       /* 用户可扩展属性 */
} GmsThreadAttrsT;

/* 线程操作相关接口 */
/**
 * @ingroup gms
 * @par 描述：线程创建函数，用于创建并开始执行一个新线程
 * @param[in] threadAttrs 线程属性，用于创建线程时设置线程属性
 * @param[in] handle 线程标识符，用于标识当前线程
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCreateFuncT)(const GmsThreadAttrsT *threadAttrs, GmsThreadHandle *handle);

/**
 * @ingroup gms
 * @par 描述：等待线程终止函数，将调用join的线程优先执行，当前正在执行的线程阻塞，直到调用join线程执行完毕
 * @param[in] handle 被join的线程标识符
 * @param[out] result 指向一个被join线程的返回返回码的指针的指针
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadJoinFuncT)(GmsThreadHandle handle, void **result);

/**
 * @ingroup gms
 * @par 描述：向指定线程发送信号函数
 * @param[in] handle 线程标识符
 * @param[in] signal 信号
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadKillFuncT)(GmsThreadHandle handle, int32_t signal);

/**
 * @ingroup gms
 * @par 描述：设置指定的线程名称
 * @param[in] handle 线程标识符
 * @param[in] threadName 需要设置的线程名
 * @return 返回当前线程标识符
 */
typedef int32_t (*GmsThreadSetNameFuncT)(GmsThreadHandle handle, const char *threadName);

/**
 * @ingroup gms
 * @par 描述：获取指定线程的线程名
 * @param[in] handle 线程标识符
 * @param[in] threadName 获取线程名称存储的缓冲区
 * @param[in] len 缓冲区的大小
 * @return 返回当前线程标识符
 */
typedef int32_t (*GmsThreadGetNameFuncT)(GmsThreadHandle handle, char *threadName, size_t len);

/**
 * @ingroup gms
 * @par 描述：获取当前线程标识符
 * @param[in] handle 线程标识符
 * @return 返回当前线程标识符
 */
typedef GmsThreadHandle (*GmsThreadGetTidFuncT)(void);

/**
 * @ingroup gms
 * @par 描述：获取当前线程tid
 * @return 返回当前线程tid
 */
typedef uint64_t (*GmsThreadGetSelfIdFuncT)(void);

/**
 * @ingroup gms
 * @par 描述：使当前线程睡眠指定的时间
 * @param[in] timeUs 睡眠时间，单位微秒(us)
 * @return 无
 */
typedef void (*GmsThreadSleepFuncT)(uint32_t timeUs);

/**
 * @ingroup gms
 * @par 描述：当前DB自旋锁中调用，加锁失败若干次后调用
 * @param[in] 无
 * @return 无
 */
typedef void (*GmsThreadScheYieldFuncT)(void);

/**
 * @ingroup gms
 * @par 描述：初始化一个条件变量。
 * @param[in] cond 指向条件变量的指针(由GmsThreadCondT 创建)
 * @param[in] attr 指向条件变量属性的指针。可以为NULL，表示使用默认属性。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondInitFuncT)(pthread_cond_t *cond, const pthread_condattr_t *attr);

/**
 * @ingroup gms
 * @par 描述：销毁条件变量，并释放与之相关的系统资源。
 * @param[in] cond 指向要销毁的条件变量的指针(由GmsThreadCondT 创建)
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondDestroyFuncT)(pthread_cond_t *cond);

/**
 * @ingroup gms
 * @par 描述：用于唤醒等待在条件变量上的一个线程。
 * @param[in] cond 指向条件变量的指针(由GmsThreadCondT 创建)
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondSignalFuncT)(pthread_cond_t *cond);

/**
 * @ingroup gms
 * @par 描述：唤醒所有等待给定条件变量的线程。
 * @param[in] cond 指向条件变量的指针(由GmsThreadCondT 创建)
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondBroadcastFuncT)(pthread_cond_t *cond);

/**
 * @ingroup gms
 * @par
 * 描述：在等待条件变量时，阻塞线程并释放互斥锁。当被pthread_cond_signal或pthread_cond_broadcast唤醒时，线程重新获得锁，并继续执行。
 * @param[in] cond 等待的条件变量(由GmsThreadCondT 创建)
 * @param[in] mutex 所使用的互斥锁
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondWaitFuncT)(pthread_cond_t *cond, pthread_mutex_t *mutex);

/**
 * @ingroup gms
 * @par 描述：类似于`GmsThreadCondWaitFuncT`，但加入了超时限制。如果条件在超时之前没有得到满足，函数返回一个错误码。
 * @param[in] cond 指向条件变量的指针(由GmsThreadCondT 创建)
 * @param[in] mutex 指向互斥锁的指针。在调用函数之前，必须先锁定互斥锁。
 * @param[in] timeSpec 指向 `timespec` 结构的指针，表示等待的绝对时间。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondTimedwaitFuncT)(
    pthread_cond_t *cond, pthread_mutex_t *mutex, const struct timespec *timeSpec);

/**
 * @ingroup gms
 * @par 描述：初始化条件变量属性对象
 * @param[in] attr 指向条件变量属性对象的指针
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondAttrInitFuncT)(pthread_condattr_t *attr);

/**
 * @ingroup gms
 * @par 描述：销毁条件变量属性对象
 * @param[in] attr 指向待销毁的条件变量属性对象的指针
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondAttrDestroyFuncT)(pthread_condattr_t *attr);

/**
 * @ingroup gms
 * @par 描述：设置条件变量的时钟属性
 * @param[in] attr 指向条件变量属性对象的指针
 * @param[in] clockId 时钟类型，可以是以下值之一：
    - `CLOCK_REALTIME`：表示使用系统实时时钟，即 `time()` 函数返回的时间。
    - `CLOCK_MONOTONIC`：表示使用单调时钟，这个时钟不受系统时间的影响，它只会单调递增，可以用来测量时间间隔。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadCondAttrSetClockFuncT)(pthread_condattr_t *attr, clockid_t clockId);

/**
 * @ingroup gms
 * @par 描述：用于初始化互斥锁
 * @param[in] mutex 指向互斥锁结构体的指针
 * @param[out] attr 指向互斥锁属性结构体的指针，如果attr为NULL，则使用默认的属性
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadMutexInitFuncT)(pthread_mutex_t *mutex, const pthread_mutexattr_t *attr);

/**
 * @ingroup gms
 * @par 描述：获取指定的互斥锁，如果该锁已经被其他线程占用，则调用线程会被阻塞，直到该锁被释放为止
 * @param[in] mutex 指向互斥锁对象的指针
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadMutexLockFuncT)(pthread_mutex_t *mutex);

/**
 * @ingroup gms
 * @par 描述：释放一个互斥锁
 * @param[in] mutex 指向要释放的互斥锁的指针
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadMutexUnlockFuncT)(pthread_mutex_t *mutex);

/**
 * @ingroup gms
 * @par 描述：销毁一个互斥锁
 * @param[in] mutex 指向互斥锁的指针，它指向需要销毁的互斥锁。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadMutexDestroyFuncT)(pthread_mutex_t *mutex);

/**
 * @ingroup gms
 * @par 描述：初始化一个读写锁
 * @param[in] rwlock_ 指向读写锁变量的指针。
 * @param[in] attr 指向读写锁属性的指针，通常为 NULL。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadRwlockInitFuncT)(pthread_rwlock_t *rwlock, const pthread_rwlockattr_t *attr);

/**
 * @ingroup gms
 * @par 描述：用于以写入模式锁定读写锁
 * @param[in] lock 指向要锁定的读写锁的指针
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadRwlockWrlockFuncT)(pthread_rwlock_t *lock);

/**
 * @ingroup gms
 * @par 描述：用于获取读写锁的读锁
 * @param[in] lock 指向读写锁对象的指针，该锁对象必须是由 `pthread_rwlock_init()` 函数初始化过的。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadRwlockRdlockFuncT)(pthread_rwlock_t *lock);

/**
 * @ingroup gms
 * @par 描述：用于释放读写锁
 * @param[in] lock 指向读写锁对象的指针。
 * @return @li返回0，为成功@li其他返回值视为失败
 */
typedef int32_t (*GmsThreadRwlockUnlockFuncT)(pthread_rwlock_t *lock);

/**
 * @ingroup gms
 * @par 描述：用于将信息输出到屏幕上
 * @param[in] format 格式化字符串
 * @param[in] param 格式化字符串中的参数
 * @return @li返回字符串长度，为成功@li负数为失败
 */
typedef int32_t (*GmsPrintScreenFuncT)(const char *format, va_list param);

/**
 * @ingroup gms
 * @par 描述：服务端发生挂死后的处理函数
 * @param[in] threadId 发生挂死的线程id
 * @param[in] ms 挂死持续时间，单位毫秒
 * @attention 调用该接口后，仍要调用服务端默认挂死处理函数退出进程
 */
typedef void (*GmsThreadHungCallback)(GmsThreadHandle threadId, uint32_t ms);

/**
 * @ingroup gms
 * @par 描述：创建服务端死循环监控任务
 * @param[in] monitorThr 死循环监控参数
 * @param[in] theaedHungCallback 服务端发生挂死后的处理函数
 * @return @li返回0，为成功@li其他返回值视为失败
 * @attention 该接口仅支持服务端，监控句柄由用户申请和管理，DB不显示持有该句柄
 */
typedef int32_t (*GmsThreadMonitorCreate)(GmsMonitorThresholdT *monitorThr, GmsThreadHungCallback theaedHungCallback);

/**
 * @ingroup gms
 * @par 描述：死循环监控任务统计清零，开始下一个监控周期
 * @return @li返回0，为成功@li其他返回值视为失败
 * @attention 该接口仅支持服务端，监控句柄由用户申请和管理，DB不显示持有该句柄
 */
typedef int32_t (*GmsThreadMonitorBegin)(void);

/**
 * @ingroup gms
 * @par 描述：死循环监控任务统计，判断是否上报挂死
 * @return @li返回0，为成功@li其他返回值视为失败
 * @attention 该接口仅支持服务端，监控句柄由用户申请和管理，DB不显示持有该句柄
 */
typedef int32_t (*GmsThreadMonitorEnd)(void);

/**
 * @ingroup gms
 * @par 描述：销毁服务端死循环监控任务
 * @attention 该接口仅支持服务端，监控句柄由用户申请和管理，DB不显示持有该句柄
 */
typedef void (*GmsThreadMonitorDestroy)(void);

/**
 * @ingroup gms
 * @par 描述：业务注册登录鉴权的钩子函数
 * @attention 该接口仅支持服务端，监控句柄由用户申请和管理，DB不显示持有该句柄
 */
typedef bool (*GmsLoginVerifyFunc)(const char *userName, const char *pwd);

/**
 * @ingroup gms
 * 日志、通信、内存、线程调度适配函数的结构体。不推荐使用
 */
typedef struct {
    // 日志适配函数
    GmsLogHandleInitFuncT logHandleInit;     /**< 日志句柄初始化函数，请参见GmsLogHandleInitFuncT。 */
    GmsLogHandleUnInitFuncT logHandleUnInit; /**< 日志句柄销毁函数，请参见GmsLogHandleUnInitFuncT。 */
    GmsLogWriteFuncT logWrite;               /**< 写日志函数，请参见GmsLogWriteFuncT。 */
    // 内存适配函数
    GmsMemInitFuncT memInitFunc;         /**< 初始化内存函数，请参见GmsMemInitFuncT。 */
    GmsMemAllocFuncT memAllocFunc;       /**< 申请内存函数，请参见GmsMemAllocFuncT。 */
    GmsMemAllocArgs memAllocArgs;        /**< 申请内存函数用户自定义入参，请参见GmsMemAllocArgs。 */
    GmsMemFreeFuncT memFreeFunc;         /**< 释放内存函数，请参见GmsMemFreeFuncT。 */
    GmsMemFreeArgs memFreeArgs;          /**< 释放内存函数用户自定义入参，请参见GmsMemFreeArgs。 */
    GmsMemFinalizeFuncT memFinalizeFunc; /**< 回收内存函数，请参见GmsMemFinalizeFuncT。 */
    uint32_t maxSingleStepAllocSize; /**< 单次申请内存的最大大小，用户传入0或者全F默认不生效。 */
    GmsShmInitFuncT shmInitFunc;     /**< 初始化共享内存函数，请参见GmsShmInitFuncT。 */
    GmsShmFinalizeFuncT shmFinalizeFunc; /**< 回收共享内存函数，请参见GmsShmFinalizeFuncT。 */
    GmsShmGetFuncT shmGetFunc;           /**< 创建共享内存函数，请参见GmsShmGetFuncT。 */
    GmsShmGetArgs shmGetArgs;  /**< 创建共享内存函数用户自定义入参，请参见GmsShmGetArgs。 */
    GmsShmAtFuncT shmAtFunc;   /**< 映射共享内存函数，请参见GmsShmAtFuncT。 */
    GmsShmAtArgs shmAtArgs;    /**< 映射共享内存函数用户自定义入参，请参见GmsShmAtArgs。 */
    GmsShmDtFuncT shmDtFunc;   /**< 解映射共享内存函数，请参见GmsShmDtFuncT。 */
    GmsShmDtArgs shmDtArgs;    /**< 解映射共享内存函数用户自定义入参，请参见GmsShmDtArgs。 */
    GmsShmCtlFuncT shmCtlFunc; /**< 操作一个共享内存段函数，请参见GmsShmCtlFuncT。 */
    GmsShmCtlArgs shmCtlArgs; /**< 操作一个共享内存段函数用户自定义入参，请参见GmsShmCtlArgs。 */
    // 通信适配函数
    GmsUsockOpenListenT usockOpenListen; /**< 服务端创建阻塞式监听句柄，请参见GmsUsockOpenListenT。 */
    GmsUsockAcceptT usockAccept;         /**< 服务端接收客户端建连请求，请参见GmsUsockAcceptT。 */
    GmsUsockConnectT usockConnect;       /**< 客户端向服务端发送建连请求，请参见GmsUsockConnectT。 */
    GmsSockClosePipeT sockClosePipe;     /**< 关闭通信句柄，请参见GmsSockClosePipeT。 */
    GmsUsockGetCredT usockGetCred;       /**< 获取对端信息，请参见GmsUsockGetCredT。 */
    GmsSockSetNoBlockT sockSetNoBlock;   /**< 设置通信句柄阻塞属性，请参见GmsSockSetNoBlockT。 */
    GmsBlockSockSetRecvTimeoutT blockSockSetRecvTimeout; /**< 设置阻塞通信句柄的接收阻塞时间。
                                                          */
    GmsBlockSockSetSendTimeoutT blockSockSetSendTimeout; /**< 设置阻塞通信句柄的发送阻塞时间。
                                                          */
    GmsBlockSockSendT blockSockSend;             /**< 阻塞通信句柄发送，请参见GmsBlockSockSendT。 */
    GmsBlockSockRecvT blockSockRecv;             /**< 阻塞通信句柄接收，请参见GmsBlockSockRecvT。 */
    GmsNonBlockSockSendT nonBlockSockSend;       /**< 非阻塞通信句柄发送，请参见GmsNonBlockSockSendT。 */
    GmsNonBlockSockRecvT nonBlockSockRecv;       /**< 非阻塞通信句柄发送，请参见GmsNonBlockSockRecvT。 */
    GmsSockGetRecvBuffSizeT sockGetRecvBuffSize; /**< 获取通道接收缓存大小，请参见GmsSockGetRecvBuffSizeT。 */
    GmsSockGetSendBuffSizeT sockGetSendBuffSize; /**< 获取通道发送缓存大小，请参见GmsSockGetSendBuffSizeT。 */
    GmsSockGetUnRecvBytesT sockGetUnRecvBytes; /**< 获取通道未接收的缓存大小，请参见GmsSockGetUnRecvBytesT。 */
    GmsSockGetUnSendBytesT sockGetUnSendBytes; /**< 获取通道未发送的缓存大小，请参见GmsSockGetUnSendBytesT。 */

    // 持久化适配
    GmsGetRedoBufferReservedSegmentFuncT getReservedMemFunc; /**< 获取redo保留内存。 */
    // 持久化压缩解压函数
    GmsPersistCompressFuncT persistCompressFunc;     /**< 持久化压缩函数，请参见GmsPersistCompressFuncT。 */
    GmsPersistDecompressFuncT persistDecompressFunc; /**< 持久化解压函数，请参见GmsPersistDecompressFuncT。 */
    GmsPersistFileNameFilterFuncT persistFileNameFilterFunc; /**< 请参见GmsPersistFileNameFilterFuncT。 */
    GmsPersistCompressFuncT persistSpaceCompressFunc; /**< 压缩区压缩函数，请参见GmsPersistSpaceCompressFuncT。 */
    GmsPersistDecompressFuncT persistSpaceDecompressFunc;
    /**< 压缩区解压函数，请参见GmsPersistSpaceDecompressFuncT。 */
    // 持久化摘要计算函数
    GmsDigest32GenerateFuncT digest32GenerateFunc; /**< 生成摘要函数，请参见GmsDigest32GenerateFuncT。 */
    // 线程适配函数
    GmsThreadCreateFuncT threadCreate;       /**< 线程创建函数，请参见GmsThreadCreateFuncT。 */
    GmsThreadJoinFuncT threadJoin;           /**< 线程等待函数，请参见GmsThreadJoinFuncT。 */
    GmsThreadKillFuncT threadKill;           /**< 向线程发送信号，请参见GmsThreadKillFuncT。 */
    GmsThreadSetNameFuncT threadSetName;     /**< 设置指定的线程名称*/
    GmsThreadGetNameFuncT threadGetName;     /**< 获取指定线程的名称*/
    GmsThreadGetTidFuncT threadGetTid;       /**< 获取线程tid，请参见GmsThreadGetTidFuncT。 */
    GmsThreadGetSelfIdFuncT threadGetSelfId; /**< 获取当前线程tid，请参见GmsThreadGetSelfIdFuncT。 */
    GmsThreadSleepFuncT threadSleep;         /**< 线程睡眠函数，请参见GmsThreadSleepFuncT。 */
    GmsThreadScheYieldFuncT threadScheYield; /**< 自旋锁中调用，请参见GmsThreadScheYieldFuncT。*/

    // 线程条件适配函数
    GmsThreadCondInitFuncT threadCondInit;       /**< 初始化条件变量，请参见GmsThreadCondInitFuncT。 */
    GmsThreadCondDestroyFuncT threadCondDestroy; /**< 销毁条件变量，请参见GmsThreadCondDestroyFuncT。*/
    GmsThreadCondSignalFuncT threadCondSignal; /**< 发送信号通知等待此条件变量的线程，请参见GmsThreadCondSignalFuncT。*/
    GmsThreadCondBroadcastFuncT
        threadCondBroadcast; /**< 广播通知所有等待此条件变量的线程，请参见GmsThreadCondBroadcastFuncT。*/
    GmsThreadCondWaitFuncT threadCondWait; /**< 等待条件变量被激活，阻塞当前线程，请参见GmsThreadCondWaitFuncT。*/
    GmsThreadCondTimedwaitFuncT
        threadCondTimedwait; /**<等待条件变量被激活，阻塞当前线程，但会在指定时间内超时返回，请参见GmsThreadCondTimedwaitFuncT。*/
    GmsThreadCondAttrInitFuncT threadCondAttrInit; /**< 用于初始化条件变量属性对象，请参见GmsThreadCondAttrInitFuncT。*/
    GmsThreadCondAttrDestroyFuncT
        threadCondAttrDestroy; /**< 用于销毁条件变量属性对象，请参见GmsThreadCondAttrDestroyFuncT。*/
    GmsThreadCondAttrSetClockFuncT
        threadCondAttrSetClock; /**< 用于设置条件变量属性对象的时钟，请参见GmsThreadCondAttrSetClockFuncT。*/

    // 线程互斥锁适配函数
    GmsThreadMutexInitFuncT threadMutexInit;     /**< 用于初始化互斥锁，请参见GmsThreadMutexInitFuncT。 */
    GmsThreadMutexLockFuncT threadMutexLock;     /**< 用于加锁互斥锁，请参见GmsThreadMutexLockFuncT。 */
    GmsThreadMutexUnlockFuncT threadMutexUnlock; /**< 用于解锁互斥锁，请参见GmsThreadMutexUnlockFuncT。 */
    GmsThreadMutexDestroyFuncT threadMutexDestroy; /**< 用于销毁互斥锁，请参见GmsThreadMutexDestroyFuncT。 */
    GmsThreadRwlockInitFuncT threadRwlockInit;     /**< 初始化读写锁，请参见GmsThreadRwlockInitFuncT。 */
    GmsThreadRwlockWrlockFuncT threadRwlockWrlock; /**< 获取写入锁，请参见GmsThreadRwlockWrlockFuncT。 */
    GmsThreadRwlockRdlockFuncT threadRwlockRdlock; /**< 获取读取锁，请参见 GmsThreadRwlockRdlockFuncT。*/
    GmsThreadRwlockUnlockFuncT threadRwlockUnlock; /**< 释放读写锁，请参见GmsThreadRwlockUnlockFuncT。 */

    // 屏显适配函数
    GmsPrintScreenFuncT printFunc;
} GmsAdptFuncsT;

/**
 * @ingroup gms
 * @par 描述：
 * 在拉起服务端前，提供用户适配的函数。不推荐使用
 * @attention 请勿随意更改入参adaptFuncs结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] adaptFuncs 适配函数，请参见GmsAdptFuncsT结构体。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsRegAdaptFuncs(GmsAdptFuncsT *adaptFuncs);

/************************************以下是推荐使用的注册接口************************************/
/**
 * @ingroup gms
 * @par 描述：
 * 用户自定义接口对应的注册码
 * @attention 不允许在中间插入其他注册码，只允许在各类型注册码最后一个插入
 */
typedef enum {
    GM_ADPT_FUNC_BEGIN = 0,
    /* 日志接口opt范围：[0,9] */
    GM_ADPT_FUNC_LOG_FUNCS_BEGIN = GM_ADPT_FUNC_BEGIN,
    GM_ADPT_FUNC_LOG_INIT = GM_ADPT_FUNC_LOG_FUNCS_BEGIN,        // GmsLogHandleInitFuncT
    GM_ADPT_FUNC_LOG_UNINIT = GM_ADPT_FUNC_LOG_FUNCS_BEGIN + 1,  // GmsLogHandleUnInitFuncT
    GM_ADPT_FUNC_LOG_WRITE = GM_ADPT_FUNC_LOG_FUNCS_BEGIN + 2,   // GmsLogWriteFuncT
    GM_ADPT_FUNC_LOG_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 10,
    GM_ADPT_FUNC_LOG_SET_LEVEL = GM_ADPT_FUNC_LOG_FUNCS_BEGIN + 3,  // GmsLogLevelSetFuncT
    /* 动态内存接口opt范围：[10,29] */
    GM_ADPT_FUNC_MEM_FUNCS_BEGIN = GM_ADPT_FUNC_BEGIN + 10,
    GM_ADPT_FUNC_MEM_INIT = GM_ADPT_FUNC_MEM_FUNCS_BEGIN,               // GmsMemInitFuncT
    GM_ADPT_FUNC_MEM_ALLOC = GM_ADPT_FUNC_MEM_FUNCS_BEGIN + 1,          // GmsMemAllocFuncT
    GM_ADPT_FUNC_MEM_ALLOC_ARGS = GM_ADPT_FUNC_MEM_FUNCS_BEGIN + 2,     // GmsMemAllocArgs
    GM_ADPT_FUNC_MEM_FREE = GM_ADPT_FUNC_MEM_FUNCS_BEGIN + 3,           // GmsMemFreeFuncT
    GM_ADPT_FUNC_MEM_FREE_ARGS = GM_ADPT_FUNC_MEM_FUNCS_BEGIN + 4,      // GmsMemFreeArgs
    GM_ADPT_FUNC_MEM_FINALIZE = GM_ADPT_FUNC_MEM_FUNCS_BEGIN + 5,       // GmsMemFinalizeFuncT
    GM_ADPT_FUNC_MEM_MAX_STEP_SIZE = GM_ADPT_FUNC_MEM_FUNCS_BEGIN + 6,  // GmsMemSetMaxStepSizeFuncT

    GM_ADPT_FUNC_MEM_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 30,
    /* 共享内存接口opt范围：[30,49] */
    GM_ADPT_FUNC_SHM_FUNCS_BEGIN = GM_ADPT_FUNC_MEM_FUNCS_BUTT,
    GM_ADPT_FUNC_SHM_INIT = GM_ADPT_FUNC_SHM_FUNCS_BEGIN,             // GmsShmInitFuncT
    GM_ADPT_FUNC_SHM_FINALIZE = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 1,     // GmsShmFinalizeFuncT
    GM_ADPT_FUNC_SHM_GET = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 2,          // GmsShmGetFuncT
    GM_ADPT_FUNC_SHM_GET_ARGS = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 3,     // GmsShmGetArgs
    GM_ADPT_FUNC_SHM_AT = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 4,           // GmsShmAtFuncT
    GM_ADPT_FUNC_SHM_AT_ARGS = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 5,      // GmsShmAtArgs
    GM_ADPT_FUNC_SHM_DETACH = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 6,       // GmsShmDtFuncT
    GM_ADPT_FUNC_SHM_DETACH_ARGS = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 7,  // GmsShmDtArgs
    GM_ADPT_FUNC_SHM_CTL = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 8,          // GmsShmCtlFuncT
    GM_ADPT_FUNC_SHM_CTL_ARGS = GM_ADPT_FUNC_SHM_FUNCS_BEGIN + 9,     // GmsShmCtlArgs
    GM_ADPT_FUNC_SHM_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 50,
    /* 通信接口opt范围：[50,129] */
    GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN = GM_ADPT_FUNC_SHM_FUNCS_BUTT,
    GM_ADPT_FUNC_USOCKET_OPEN_LISTEN = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN,       // GmsUsockOpenListenT
    GM_ADPT_FUNC_USOCKET_ACCEPT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 1,        // GmsUsockAcceptT
    GM_ADPT_FUNC_USOCKET_CONNECT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 2,       // GmsUsockConnectT
    GM_ADPT_FUNC_USOCKET_GET_CRED = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 3,      // GmsUsockGetCredT
    GM_ADPT_FUNC_USOCKET_OPEN_LISTEN2 = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 4,  // GmsUsockOpenListen2T
    GM_ADPT_FUNC_USOCKET_CONNECT2 = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 5,      // GmsUsockConnect2T

    GM_ADPT_FUNC_SOCKET_OPEN_LISTEN = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 10,     // GmsTcpOpenListenT
    GM_ADPT_FUNC_SOCKET_ACCEPT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 11,          // GmsTcpAcceptT
    GM_ADPT_FUNC_SOCKET_CONNECT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 12,         // GmTcpConnectT
    GM_ADPT_FUNC_SOCKET_GET_PEER_NAME = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 13,   // GmTcpGetPeerName
    GM_ADPT_FUNC_SOCKET_GET_LOCAL_NAME = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 14,  // GmTcpGetLocalName
    GM_ADPT_FUNC_SOCKET_GET_CREDIT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 15,      // GmTcpGetCredit
    GM_ADPT_FUNC_SOCKET_IP_CONTROL = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 16,      // GmTcpIpControl

    GM_ADPT_FUNC_SOCKET_CLOSE = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 20,             // GmsSockClosePipeT
    GM_ADPT_FUNC_SOCKET_SET_NONBLOCK = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 21,      // GmsSockSetNoBlockT
    GM_ADPT_FUNC_SOCKET_SET_RECV_TIMEOUT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 22,  // GmsBlockSockSetRecvTimeoutT
    GM_ADPT_FUNC_SOCKET_SET_SEND_TIMEOUT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 23,  // GmsBlockSockSetSendTimeoutT

    GM_ADPT_FUNC_SOCKET_BOLCK_RECV = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 30,     // GmsBlockSockRecvT
    GM_ADPT_FUNC_SOCKET_BOLCK_SEND = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 31,     // GmsBlockSockSendT
    GM_ADPT_FUNC_SOCKET_NONBOLCK_RECV = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 32,  // GmsNonBlockSockRecvT
    GM_ADPT_FUNC_SOCKET_NONBOLCK_SEND = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 33,  // GmsNonBlockSockSendT

    GM_ADPT_FUNC_SOCKET_GET_RECV_BUFF_SIZE = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 40,     // GmsSockGetRecvBuffSizeT
    GM_ADPT_FUNC_SOCKET_GET_SEND_BUFF_SIZE = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 41,     // GmsSockGetSendBuffSizeT
    GM_ADPT_FUNC_SOCKET_GET_REMAIN_RECV_BYTES = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 42,  // GmsSockGetUnRecvBytesT
    GM_ADPT_FUNC_SOCKET_GET_REMAIN_SEND_BYTES = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 43,  // GmsSockGetUnSendBytesT

    GM_ADPT_FUNC_EPOLL_CREATE = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 50,          // GmEpollCreateT
    GM_ADPT_FUNC_EPOLL_CTL = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 51,             // GmEpollCtlT
    GM_ADPT_FUNC_EPOLL_WAIT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 52,            // GmEpollWaitT
    GM_ADPT_FUNC_EPOLL_CLOSE = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 53,           // GmEpollCloseT
    GM_ADPT_FUNC_EPOLL_GET_EVENT_SIZE = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 54,  // GmEpollGetEventSizeT
    GM_ADPT_FUNC_EPOLL_SET_EVENT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 55,       // GmEpollSetEventT
    GM_ADPT_FUNC_EPOLL_GET_EVENT = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 56,       // GmEpollGetEventT

    GM_ADPT_FUNC_LOGIN_RULE = GM_ADPT_FUNC_SOCKET_FUNCS_BEGIN + 60,  // GmsLoginVerifyFunc

    GM_ADPT_FUNC_SOCKET_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 130,
    /* 持久化接口opt范围：[130,149] */
    GM_ADPT_FUNC_PERSIST_FUNCS_BEGIN = GM_ADPT_FUNC_SOCKET_FUNCS_BUTT,
    GM_ADPT_FUNC_PERSIST_GET_REDO_BUF_RESERVED_SEGMENT = GM_ADPT_FUNC_PERSIST_FUNCS_BEGIN,
    // GmsGetRedoBufferReservedSegmentFuncT
    GM_ADPT_FUNC_PERSIST_COMPRESS = GM_ADPT_FUNC_PERSIST_FUNCS_BEGIN + 1,          // GmsPersistCompressFuncT
    GM_ADPT_FUNC_PERSIST_DECOMPRESS = GM_ADPT_FUNC_PERSIST_FUNCS_BEGIN + 2,        // GmsPersistDecompressFuncT
    GM_ADPT_FUNC_PERSIST_FILE_FILTER = GM_ADPT_FUNC_PERSIST_FUNCS_BEGIN + 3,       // GmsPersistFileNameFilterFuncT
    GM_ADPT_FUNC_PERSIST_SPACE_COMPRESS = GM_ADPT_FUNC_PERSIST_FUNCS_BEGIN + 4,    // GmsPersistCompressFuncT
    GM_ADPT_FUNC_PERSIST_SPACE_DECOMPRESS = GM_ADPT_FUNC_PERSIST_FUNCS_BEGIN + 5,  // GmsPersistDecompressFuncT
    GM_ADPT_FUNC_PERSIST_DIGEST_GENERATE = GM_ADPT_FUNC_PERSIST_FUNCS_BEGIN + 6,   // GmsDigest32GenerateFuncT
    GM_ADPT_FUNC_PERSIST_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 150,
    /* 线程接口opt范围：[150,209] */
    GM_ADPT_FUNC_THREAD_FUNCS_BEGIN = GM_ADPT_FUNC_PERSIST_FUNCS_BUTT,
    GM_ADPT_FUNC_THREAD_CREATE = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN,            // GmsThreadCreateFuncT
    GM_ADPT_FUNC_THREAD_JOIN = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 1,          // GmsThreadJoinFuncT
    GM_ADPT_FUNC_THREAD_KILL = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 2,          // GmsThreadKillFuncT
    GM_ADPT_FUNC_THREAD_SET_NAME = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 3,      // GmsThreadSetNameFuncT
    GM_ADPT_FUNC_THREAD_GET_NAME = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 4,      // GmsThreadGetNameFuncT
    GM_ADPT_FUNC_THREAD_GET_TID = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 5,       // GmsThreadGetTidFuncT
    GM_ADPT_FUNC_THREAD_GET_SELF_TID = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 6,  // GmsThreadGetSelfIdFuncT
    GM_ADPT_FUNC_THREAD_SLEEP = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 7,         // GmsThreadSleepFuncT
    GM_ADPT_FUNC_THREAD_YIELD = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 8,         // GmsThreadScheYieldFuncT

    GM_ADPT_FUNC_THREAD_COND_INIT = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 20,            // GmsThreadCondInitFuncT
    GM_ADPT_FUNC_THREAD_COND_DESTROY = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 21,         // GmsThreadCondDestroyFuncT
    GM_ADPT_FUNC_THREAD_COND_SIGNAL = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 22,          // GmsThreadCondSignalFuncT
    GM_ADPT_FUNC_THREAD_COND_BROADCAST = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 23,       // GmsThreadCondBroadcastFuncT
    GM_ADPT_FUNC_THREAD_COND_WAIT = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 24,            // GmsThreadCondWaitFuncT
    GM_ADPT_FUNC_THREAD_COND_TIME_WAIT = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 25,       // GmsThreadCondTimedwaitFuncT
    GM_ADPT_FUNC_THREAD_COND_ATTR_INIT = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 26,       // GmsThreadCondAttrInitFuncT
    GM_ADPT_FUNC_THREAD_COND_ATTR_DESTROY = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 27,    // GmsThreadCondAttrDestroyFuncT
    GM_ADPT_FUNC_THREAD_COND_ATTR_SET_CLOCK = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 28,  // GmsThreadCondAttrSetClockFuncT

    GM_ADPT_FUNC_THREAD_MUTEX_INIT = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 40,     // GmsThreadMutexInitFuncT
    GM_ADPT_FUNC_THREAD_MUTEX_LOCK = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 41,     // GmsThreadMutexLockFuncT
    GM_ADPT_FUNC_THREAD_MUTEX_UNLOCK = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 42,   // GmsThreadMutexUnlockFuncT
    GM_ADPT_FUNC_THREAD_MUTEX_DESTROY = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 43,  // GmsThreadMutexDestroyFuncT

    GM_ADPT_FUNC_THREAD_RWLOCK_INIT = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 50,    // GmsThreadRwlockInitFuncT
    GM_ADPT_FUNC_THREAD_RWLOCK_W_LOCK = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 51,  // GmsThreadRwlockWrlockFuncT
    GM_ADPT_FUNC_THREAD_RWLOCK_R_LOCK = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 52,  // GmsThreadRwlockRdlockFuncT
    GM_ADPT_FUNC_THREAD_RWLOCK_UNLOCK = GM_ADPT_FUNC_THREAD_FUNCS_BEGIN + 53,  // GmsThreadRwlockUnlockFuncT

    GM_ADPT_FUNC_THREAD_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 210,

    /* 屏显接口opt范围：[210,219] */
    GM_ADPT_FUNC_PRINT_FUNCS_BEGIN = GM_ADPT_FUNC_THREAD_FUNCS_BUTT,
    GM_ADPT_FUNC_COMMON_PRINT = GM_ADPT_FUNC_PRINT_FUNCS_BEGIN,  // GmsPrintScreenFuncT
    GM_ADPT_FUNC_PRINT_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 220,

    /* 服务端进程喂狗、死循环监控接口opt范围：[220,229] */
    GM_ADPT_FUNC_MONITOR_FUNCS_BEGIN = GM_ADPT_FUNC_PRINT_FUNCS_BUTT,
    GM_ADPT_FUNC_MONITOR_CREATE = GM_ADPT_FUNC_PRINT_FUNCS_BEGIN,         // GmsThreadMonitorCreate
    GM_ADPT_FUNC_MONITOR_DESTROY = GM_ADPT_FUNC_PRINT_FUNCS_BEGIN + 1,    // GmsThreadMonitorDestroy
    GM_ADPT_FUNC_MONITOR_BEGIN = GM_ADPT_FUNC_PRINT_FUNCS_BEGIN + 2,      // GmsThreadMonitorBegin
    GM_ADPT_FUNC_MONITOR_END = GM_ADPT_FUNC_PRINT_FUNCS_BEGIN + 3,        // GmsThreadMonitorEnd
    GM_ADPT_FUNC_MONITOR_HUNG_PROC = GM_ADPT_FUNC_PRINT_FUNCS_BEGIN + 4,  // GmsThreadHungCallback
    GM_ADPT_FUNC_MONITOR_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 230,

    // 待删除 [230-250]
    GM_ADPT_FUNC_DOPHI_FUNCS_BEGIN = GM_ADPT_FUNC_MONITOR_FUNCS_BUTT,
    GM_ADPT_FUNC_SOCKET_OPEN_LISTEN2 = GM_ADPT_FUNC_DOPHI_FUNCS_BEGIN,         // GmsTcpOpenListen2T
    GM_ADPT_FUNC_SOCKET_CONNECT2 = GM_ADPT_FUNC_DOPHI_FUNCS_BEGIN + 1,         // GmTcpConnect2T
    GM_ADPT_FUNC_SOCKET_GET_PEER_NAME2 = GM_ADPT_FUNC_DOPHI_FUNCS_BEGIN + 2,   // GmTcpGetPeerName2
    GM_ADPT_FUNC_SOCKET_GET_LOCAL_NAME2 = GM_ADPT_FUNC_DOPHI_FUNCS_BEGIN + 3,  // GmTcpGetLocalName2
    GM_ADPT_FUNC_DOPHI_FUNCS_BUTT = GM_ADPT_FUNC_BEGIN + 250,

    GM_ADPT_FUNC_BUTT
} GM_ADPT_FUNC_OPT_E;

typedef void *GmAdptFuncsHandle;
typedef void *GmAdptFunc;

/**
 * @ingroup gms
 * @par 描述：
 * 创建一个适配函数句柄，用于注册适配函数。
 * @param[out] handle 适配函数句柄。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmCreateAdptFuncsHandle(GmAdptFuncsHandle *handle);

/**
 * @ingroup gms
 * @par 描述：
 * 添加注册适配函数。
 * @param[in] handle 适配函数句柄。
 * @param[in] option 适配函数类型，请参见GM_ADPT_FUNC_OPT_E。
 * @param[in] func 用户适配的函数。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmAddAdptFunc(GmAdptFuncsHandle handle, GM_ADPT_FUNC_OPT_E option, GmAdptFunc func);

/**
 * @ingroup gms
 * @par 描述：
 * 注册适配函数。
 * @attention
 * 单例模式，一个进程只生效一次，后续接口注册会返回重复注册错误码，必须早于GmsServerMain/GmcInit调用，否则不生效
 * @param[in] handle 适配函数句柄。
 * @return @li GMERR_OK @li GMERR_DUPLICATE_OBJECT，重复注册或者服务端、客户端已启动
 * @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmRegAdaptFuncs(GmAdptFuncsHandle handle);

/**
 * @ingroup gms
 * @par 描述：
 * 销毁适配函数句柄。
 * @param[in] handle 适配函数句柄。
 */
GM_EXPORT void GmDestroyAdptFuncsHandle(GmAdptFuncsHandle *handle);

/************************************以下是tools的入口************************************/
/**
 * @ingroup gms
 * @par 描述：
 * 用于更新数据库内部数据，删除表数据，生成测试数据
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsCmdMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 用于查看gmserver.ini文件中配置项信息，或者在线修改gmserver.ini文件中配置项的值
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsAdminMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 用于查询错误码的详细信息，包括错误码含义、可能原因和解决方法
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsErrorMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 用于导出表、数据或资源池
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsExportMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 用于导入表文件、导入数据、绑定资源池到资源表，或者绑定扩展资源池到资源池
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsImportMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 查看给定进程的日志控制信息，设置给定进程的日志级别，以及打开或关闭给定进程的本地日志写开关等
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsLogMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 用于给数据库导入白名单，授予用户对象权限或者系统权限
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsRuleMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 为入侵保护系统（Instrusion Protection system）工具，当DB感知到存在威胁时，可以通过此工具采取一定的策略进行防御
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsIpsMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 入侵保护系统（Instrusion Protection system）工具,当DB感知到存在威胁时，可以通过此工具采取一定的策略进行检测
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsIdsMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 支持离线检查Schema升级的兼容性，并对表结构升级后的内存增量进行评估
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsAsstMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 用于动态删除表，进行表结构升降级等
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsDDLMain(int32_t argc, char **argv);

/**
 * @ingroup gms
 * @par 描述：
 * 查询系统视图信息、表记录数、表记录信息，以及获取告警信息等
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GM_EXPORT int32_t GmsSysviewMain(int32_t argc, char **argv);

#ifdef __cplusplus
}
#endif

#endif /* GM_ADPT_H */
