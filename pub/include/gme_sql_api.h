/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: File provide GME SQL APIs for embedded scenarios
 * Author: SQL
 * Create: 2024-01-29
 */
#ifndef GME_SQL_API_H
#define GME_SQL_API_H

#include "gme_api.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef void GmeSqlStmtT;

/**
** @brief process each row of the query result
** @param userData the user input data info
** @param colCount column count
** @param colValues column values
** @param colNames  column names
** @return Status Return code indicating success or failure
**/
typedef int32_t (*GmeSqlExecuteCallback)(void *userData, uint16_t colCount, char **colValues, char **colNames);

/**
** @brief process query stmts
** @param conn the connection handle for embedded service
** @param str query stmts, e.g. sql, gql
** @param callback callback function for processing query results
** @param errMsg error message for the processing
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlExecute(
    GmeConnT *conn, const char *str, GmeSqlExecuteCallback callback, void *data, char **errMsg);

/**
** @brief get bound parameter index by name
** @param sqlStmt the prepared stmt
** @param str The string used to search the index
** @return return the index number;
**/
GME_EXPORT uint32_t GmeSqlBindParameterIndex(GmeSqlStmtT *stmt, const char *str);

/**
** @brief prepare a query stmt
** @param conn the connection handle for embedded service
** @param str input SQL string
** @param len length of the SQL string
** @param stmt return the prepared stmt
** @param unused return the unused string part or NULL
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlPrepare(
    GmeConnT *conn, const char *str, uint32_t len, GmeSqlStmtT **stmt, const char **unused);

/**
** @brief reset a query stmt
** @param sqlStmt the prepared stmt
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlReset(GmeSqlStmtT *stmt);

/**
** @brief release a query stmt
** @param sqlStmt the prepared stmt
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlFinalize(GmeSqlStmtT *stmt);

/**
** @brief step a query stmt
** @param sqlStmt the prepared stmt
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlStep(GmeSqlStmtT *stmt);

/**
** @brief get the column num of current select stmt
** @param sqlStmt the prepared and stepped select stmt
** @return uint32_t column num
**/
GME_EXPORT uint32_t GmeSqlColumnCount(GmeSqlStmtT *stmt);

/**
** @brief get the column value type of current row
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return GmeDbDataTypeE the value type
**/
GME_EXPORT GmeDbDataTypeE GmeSqlColumnType(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief get the column value of current row of select stmt
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return GmeDbValueT the dynamically typed value object or NULL if value not exist
**/
GME_EXPORT GmeDbValueT GmeSqlColumnValue(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief get the column value of current row of select stmt and covert it to int64
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return int64_t the value converted to int64
                   or 0 if value not exist or the value cannot be converted to int
**/
GME_EXPORT int64_t GmeSqlColumnInt64(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief get the column value of current row of select stmt and covert it to int32
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return int32_t the value converted to int32
                   or 0 if value not exist or the value cannot be converted to int
**/
GME_EXPORT int32_t GmeSqlColumnInt(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief get the column value of current row of select stmt and covert it to double
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return double the value converted to double
                   or 0 if value not exist or the value cannot be converted to double
**/
GME_EXPORT double GmeSqlColumnDouble(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief get the column value of current row of select stmt and covert it to text
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return char * the value converted to text or NULL if value not exist.
                  returned result's memory will be freed when calling GmeSqlFinalize.
**/
GME_EXPORT const char *GmeSqlColumnText(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief get the column value of current row of select stmt and covert it to blob
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return char * the value converted to blob or NULL if value not exist.
                  returned result's memory will be freed when calling GmeSqlFinalize.
**/
GME_EXPORT const void *GmeSqlColumnBlob(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief get the column value of current row of select stmt and covert it to floatvector
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @param dim [out param] dimension of the returned float vector. 0 if value does not exist.
** @return float * the value converted to floatvector or NULL if value does not exist.
                  returned result's memory will be freed when calling GmeSqlFinalize.
**/
GME_EXPORT const float *GmeSqlColumnFloatVector(GmeSqlStmtT *stmt, uint32_t index, uint32_t *dim);

/**
** @brief get the column value of current row of select stmt and covert it to float16vector
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @param dim [out param] dimension of the returned float vector. 0 if value does not exist.
** @return float16 * the value converted to float16vector or NULL if value does not exist.
                  returned result's memory will be freed when calling GmeSqlFinalize.
**/
GME_EXPORT const float16 *GmeSqlColumnFloat16Vector(GmeSqlStmtT *stmt, uint32_t index, uint32_t *dim);

/**
** @brief get the column value byte of current row
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return uint32_t the byte size of the column
**/
GME_EXPORT uint32_t GmeSqlColumnBytes(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief get the column name of current row
** @param sqlStmt the prepared and stepped select stmt
** @param index the index of column
** @return uint32_t the byte size of the column
**/
GME_EXPORT char *GmeSqlColumnName(GmeSqlStmtT *stmt, uint32_t index);

/**
** @brief bind an int32 value to the parameter of specified index
** @param sqlStmt the prepared stmt
** @param idx parameter's index
** @param val value to bind
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlBindInt(GmeSqlStmtT *stmt, uint32_t idx, int32_t val);

/**
** @brief bind an int64 value to the parameter of specified index
** @param sqlStmt the prepared stmt
** @param idx parameter's index
** @param val value to bind
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlBindInt64(GmeSqlStmtT *stmt, uint32_t idx, int64_t val);

/**
** @brief bind an double value to the parameter of specified index
** @param sqlStmt the prepared stmt
** @param idx parameter's index
** @param val value to bind
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlBindDouble(GmeSqlStmtT *stmt, uint32_t idx, double val);

/**
** @brief bind an string value to the parameter of specified index
** @param sqlStmt the prepared stmt
** @param idx parameter's index
** @param val value to bind
** @param len value's length without '\0', strlen(), 0~1024*1024
** @param freeFunc Release function after GmeSqlStep, nullable. Will be called when calling GmeSqlReset or
GmeSqlFinalize, or the parameter get override.
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlBindText(
    GmeSqlStmtT *stmt, uint32_t idx, const char *val, int32_t len, void (*freeFunc)(void *));

/**
** @brief bind an object value to the parameter of specified index
** @param sqlStmt the prepared stmt
** @param idx parameter's index
** @param val value to bind
** @param len value's length, 0~2147483647 MAX_INT32
** @param freeFunc Release function after GmeSqlStep, nullable. Will be called when calling GmeSqlReset or
GmeSqlFinalize, or the parameter get override.
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlBindBlob(
    GmeSqlStmtT *stmt, uint32_t idx, const void *val, int32_t len, void (*freeFunc)(void *));

/**
** @brief bind an float vector to the parameter of specified index
** @param stmt the prepared stmt
** @param idx parameter's index
** @param val float vector to bind
** @param dim dimension of vector, 1 ~ 1536
** @param freeFunc Release function after GmeSqlStep, nullable. Will be called when calling GmeSqlReset or
GmeSqlFinalize, or the parameter get override.
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlBindFloatVector(
    GmeSqlStmtT *stmt, uint32_t idx, float *val, uint32_t dim, void (*freeFunc)(void *));

/**
** @brief bind an float16 vector to the parameter of specified index
** @param stmt the prepared stmt
** @param idx parameter's index
** @param val float16 vector to bind
** @param dim dimension of vector, 1 ~ 1536
** @param freeFunc Release function after GmeSqlStep, nullable. Will be called when calling GmeSqlReset or
GmeSqlFinalize, or the parameter get override.
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlBindFloat16Vector(
    GmeSqlStmtT *stmt, uint32_t idx, float16 *val, uint32_t dim, void (*freeFunc)(void *));

/**
** @brief convert an float vector to float16 vector
** @param val float vector to convert
** @param dim dimension of vector, 1 ~ 1536
** @param out [out param]returned float16 vector
**/
GME_EXPORT void GmeSqlFloatVectorToFloat16Vector(const float *val, uint32_t dim, float16 *out);

/**
** @brief convert an float16 vector to float vector
** @param val float16 vector to convert
** @param dim dimension of vector, 1 ~ 1536
** @param out [out param]returned float vector
**/
GME_EXPORT void GmeSqlFloat16VectorToFloatVector(const float16 *val, uint32_t dim, float *out);

/**
** @brief bind an null value to the parameter of specified index, i.e. remove a bound parameter
** @param sqlStmt the prepared stmt
** @param idx parameter's index
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSqlBindNull(GmeSqlStmtT *stmt, uint32_t idx);

/**
** @brief Allocate memory for external use.
** @param size Size to allocate
** @return Allocated pointer
**/
GME_EXPORT void *GmeMalloc(uint32_t size);

/**
** @brief Free the memory allocated by GmeMalloc.
** @param ptr Pointer to the memory
**/
GME_EXPORT void GmeFree(void *ptr);

/**
** @brief this functions used to configure db instance parameter
** @param conn the connection handle for embedded service
** @param type the parameter type of db instance
** @param value the parameter value need to configure of db instance
** @return Status Return code indicating success or failure
**/
GME_EXPORT int32_t GmeSetConfig(GmeConnT *conn, GmeConfigTypeE type, GmeDbValueT value);

/**
** @brief this functions used to get db instance config
** @param conn the connection handle for embedded service
** @param type the parameter type of db instance
** @return GmeDbValueT the dynamically typed value object or NULL if value not exist
**/
GME_EXPORT GmeDbValueT GmeGetConfig(GmeConnT *conn, GmeConfigTypeE type);

/**
** @brief this function returns the number of rows modified, inserted or
** deleted by the most recently completed INSERT, UPDATE or DELETE
** statement on the database connection
** @param conn the connection handle for embedded service
** @return return the number of changes in the most recent call to GmeSqlExecute.
**/
GME_EXPORT uint32_t GmeSqlChanges(GmeConnT *conn);

/**
** @brief this functions returns the total number of rows inserted, modified or
** deleted by all [INSERT], [UPDATE] or [DELETE] statements completed
** since the database connection was opened
** @param conn the connection handle for embedded service
** @return return the number of changes since the connection handle was opened.
**/
GME_EXPORT uint32_t GmeSqlTotalChanges(GmeConnT *conn);

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* GME_SQL_API_H */
