/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: gmc_yang.h
 * Description: File provide Yang APIs relate to vertex\list\superfield\edge\path\index module.
 *              Function order: DDL\DML\other func.
 *              Please keep new function in its corresponding position.
 * Author:
 * Create: 2022-07-18
 */

#ifndef GMC_YANG_H
#define GMC_YANG_H

#include "gmc_types.h"
#include "gmc_yang_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @ingroup gmc_batch
 * @par 描述：
 * 设置生成diff tree的操作类型。
 * @attention 请勿随意更改入参batchOption结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] batchOption batch选项。
 * @param[in] diffType diff操作类型。若不设置该选项，默认值为GMC_YANG_DIFF_OFF(表示不生成diff)。参见GmcYangDiffTypeE。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangBatchOptionSetDiffType(GmcBatchOptionT *batchOption, GmcYangDiffTypeE diffType);
/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang批操作开始时初始化根节点，即将根节点的第一个字段（主键字段，uint32类型）的值设置为 1。
 * @param[in] batch 批操作句柄。
 * @param[in] rootStmt 根节点stmt句柄。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangSetRoot(GmcBatchT *batch, GmcStmtT *rootStmt);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 在 YANG 批操作中绑定父子节点关系，即通过 parentStmt
 * 设置好特定父节点后，可以借助这个接口指定接下来要操作该父节点下的子节点， 然后就可以用 childStmt 设置子节点的属性值。
 * @param[in] batch 批操作句柄。
 * @param[in] parentStmt 父节点stmt句柄，已经设置好临时ID。
 * @param[in] childStmt 子节点stmt句柄。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangBindChild(GmcBatchT *batch, GmcStmtT *parentStmt, GmcStmtT *childStmt);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 设置顶点的属性值,并附带属性操作类型。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] propValue 包括属性名称，属性类型，属性值，内存大小等。注意，此时设定的propertyId不生效。
 * @param[in] opType 属性操作类型。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangSetVertexProperty(GmcStmtT *stmt, GmcPropValueT *propValue, GmcYangPropOpTypeE opType);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * vertex节点转换枚举和identity类型的值，可将key转value，value转key
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] convertType 转换类型，可选key转value，或者value转key
 * @param[in] inPropValue 包括属性名称，属性类型，属性值，内存大小等。注意，此时设定的propertyId不生效。
 * @param[out] outPropValue 转换后的属性值和size。注意，outPropValue中的value内存由调用者申请
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangConvertVertexProperty(
    GmcStmtT *stmt, GmcConvertTypeE convertType, GmcPropValueT *inPropValue, GmcPropValueT *outPropValue);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 设置List元素插入信息，插入位置，以及参考点主键信息。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] listLocator list元素信息。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangSetListLocator(GmcStmtT *stmt, GmcYangListLocatorT *listLocator);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 设置fetch diff的操作类型。
 * @param[in] opt option 对象。
 * @param[in] mode diff操作类型。若不设置该选项，默认值为GMC_FETCH_DIFF_REPORT_ALL。参见 GmcFetchDiffModeE。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangDiffFetchOptionSetMode(GmcFetchDiffOptionT *opt, GmcFetchDiffModeE mode);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 创建diff fetch的option接口, 需要调用 GmcYangDiffFetchOptionDestroy 释放
 * @param[in/out] option option 对象。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangDiffFetchOptionCreate(GmcFetchDiffOptionT **option);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 释放diff fetch的option接口
 * @param[in/out] option option 对象。
 */
GMC_EXPORT void GmcYangDiffFetchOptionDestroy(GmcFetchDiffOptionT *option);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 对于批操作的diff类型为GMC_YANG_DIFF_DELAY_READ_ON, Client端可调用该函数以异步的方式获取并解析出diff结果树信息。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] fetchRet
 * 上一批查询返回结果。对于查询结果超大的情况下需要分多个批次调用该接口才能将数据查完。第一批查询时填“NULL”.
 * 查询后续批次时必须为该批次的前一批 GmcYangFetchExecuteDoneT 返回的结果。
 * @param[in] userCb  用户的回调函数，参考 GmcYangFetchExecuteDoneT 。
 * @param[in] userData  回调参数
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 * @warning 当返回值是GMERR_OK时，该接口内部会释放fetchRet资源，fetchRet不能再被使用了；
 * 当返回值不是GMERR_OK时，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 */
GMC_EXPORT int32_t GmcYangFetchDiffExecuteAsync(
    GmcStmtT *stmt, const GmcFetchRetT *fetchRet, GmcYangFetchExecuteDoneT userCb, void *userData);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 对于批操作的diff类型为GMC_YANG_DIFF_DELAY_READ_ON, Client端可调用该函数以异步的方式获取并解析出diff结果树信息。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] fetchRet
 * 上一批查询返回结果。对于查询结果超大的情况下需要分多个批次调用该接口才能将数据查完。第一批查询时填“NULL”.
 * 查询后续批次时必须为该批次的前一批 GmcYangFetchExecuteDoneT 返回的结果。
 * @param[in] opt  Diff fetch 的选项，可以传NULL，为NULL时使用默认选项。调用DB提供接口进行创建与释放。
 * @param[in] userCb  用户的回调函数，参考 GmcYangFetchExecuteDoneT 。
 * @param[in] userData  回调参数
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 * @warning 当返回值是GMERR_OK时，该接口内部会释放fetchRet资源，fetchRet不能再被使用了；
 * 当返回值不是GMERR_OK时，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 */
GMC_EXPORT int32_t GmcYangFetchDiffExtExecuteAsync(GmcStmtT *stmt, const GmcFetchRetT *fetchRet,
    GmcFetchDiffOptionT *opt, GmcYangFetchExecuteDoneT userCb, void *userData);
/**
 * @ingroup gmc_yang
 * @par 描述：
 * 对于批操作的diff类型为GMC_YANG_DIFF_DELAY_READ_ON, Client端可调用该函数以异步的方式获取序列化的diff buf
 * 用户可以进一步调用接口拷贝到自己的内存空间，用于跨设备传输以及跨设备解析
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] fetchRet
 * 上一批查询返回结果。对于查询结果超大的情况下需要分多个批次调用该接口才能将数据查完。第一批查询时填“NULL”.
 * 查询后续批次时必须为该批次的前一批 GmcYangFetchExecuteDoneT 返回的结果。
 * @param[in] userCb  用户的回调函数，参考 GmcYangFetchExecuteDoneT 。
 * @param[in] userData  回调参数
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 * @warning 当返回值是GMERR_OK时，该接口内部会释放fetchRet资源，fetchRet不能再被使用了；
 * 当返回值不是GMERR_OK时，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 */
GMC_EXPORT int32_t GmcYangFetchDiffBufExecuteAsync(
    GmcStmtT *stmt, const GmcFetchRetT *fetchRet, GmcYangFetchExecuteDoneT userCb, void *userData);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 对于批操作的diff类型为GMC_YANG_DIFF_DELAY_READ_ON, Client端可调用该函数以异步的方式获取序列化的diff buf。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] fetchRet
 * 上一批查询返回结果。对于查询结果超大的情况下需要分多个批次调用该接口才能将数据查完。第一批查询时填“NULL”.
 * 查询后续批次时必须为该批次的前一批 GmcYangFetchExecuteDoneT 返回的结果。
 * @param[in] opt   Diff fetch 的选项，可以传NULL，为NULL时使用默认选项。调用DB提供接口进行创建与释放。
 * @param[in] userCb  用户的回调函数，参考 GmcYangFetchExecuteDoneT 。
 * @param[in] userData  回调参数
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 * @warning 当返回值是GMERR_OK时，该接口内部会释放fetchRet资源，fetchRet不能再被使用了；
 * 当返回值不是GMERR_OK时，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 */
GMC_EXPORT int32_t GmcYangFetchDiffBufExtExecuteAsync(GmcStmtT *stmt, const GmcFetchRetT *fetchRet,
    GmcFetchDiffOptionT *opt, GmcYangFetchExecuteDoneT userCb, void *userData);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景下，用于 subtree 或 diff 查询结果解析时获取 GmcYangTreeT 对象树的根节点 root。
 * @attention 请勿随意更改入参yangTree结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] yangTree 查询的结果树句柄。
 * @param[out] rootNode 查询结果树的节点句柄。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetRootNode(const GmcYangTreeT *yangTree, GmcYangNodeT **rootNode);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * yang场景下，用于 subtree 或 diff 查询结果解析时获取下一个 GmcYangNodeT 节点。
 * 该遍历方式是深度优先遍历，prevNode非空时取下一个兄弟节点；prevNode为空时取第一个子节点。
 * @attention 请勿随意更改入参parentNode、prevNode结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] parentNode 预备遍历node节点的父亲节点。
 * @param[in] prevNode 预备遍历node节点的前驱节点。
 * @param[out] currNode 获取到的新的node节点。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetNext(
    const GmcYangNodeT *parentNode, const GmcYangNodeT *prevNode, GmcYangNodeT **currNode);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景下，获取 GmcYangNodeT 对象节点名。用于diff数据解析和subtree查询结果解析场景。
 * @param[in] node GmcYangNodeT 节点句柄。
 * @param[out] name GmcYangNodeT 节点名指针。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetName(const GmcYangNodeT *node, const char **name);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * yang场景下，获取 GmcYangNodeT 对象节点类型，用于diff数据解析和subtree查询结果解析场景。
 * @param[in] node GmcYangNodeT节点句柄。
 * @param[out] type 获取到的节点类型。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetType(const GmcYangNodeT *node, GmcYangNodeTypeE *type);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * yang场景下，获取 GmcYangNodeT 对象的更新状态，包括update、remove和create，仅用于diff查询结果数据解析场景。
 * @param[in] node GmcYangNodeT节点句柄。
 * @param[out] diffOp 获取node的更新状态。 参考 GmcDiffOpTypeE 。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetDiffOpType(const GmcYangNodeT *node, GmcDiffOpTypeE *diffOp);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * yang场景下，获取GmcYangNodeTypeE为 GMC_YANG_TYPE_LEAF
 * 的GmcYangNodeT对象值，用于diff数据解析和subtree查询结果解析场景。
 * @param[in] node GmcYangNodeT 节点句柄。
 * @attention diff查询结果解析时，如果 node 的 GmcDiffOpTypeE 为
 * GMC_DIFF_OP_UPDATE、GMC_DIFF_OP_CREATE时获取新值； 仅用于diff查询结果解析，subtree查询结果解析时只需要获取旧值,
 * 调用该接口报错
 * @param[out] value 获取到字段值句柄。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetNewValue(const GmcYangNodeT *node, GmcYangNodeValueT **value);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * yang场景下，获取GmcYangNodeTypeE为 GMC_YANG_TYPE_LEAF
 * 的GmcYangNodeT对象值，用于diff数据解析和subtree查询结果解析场景。
 * @param[in] node GmcYangNodeT 节点句柄。
 * @attention diff查询结果解析时，如果 node 的 GmcDiffOpTypeE 为
 * GMC_DIFF_OP_UPDATE、GMC_DIFF_OP_REMOVE时获取老值; subtree查询结果解析时返回实际值。
 * @param[out] value 获取到字段值句柄。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetOldValue(const GmcYangNodeT *node, GmcYangNodeValueT **value);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景下，获取 GmcYangNodeT 节点的主键索引属性个数，仅用于diff数据解析场景。
 * @param[in] currNode GmcYangNodeT节点句柄。
 * @param[out] propNum 获取到的主键索引属性个数。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetKeyPropNum(const GmcYangNodeT *currNode, uint32_t *propNum);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景下，获取 GmcYangNodeT 节点的主键索引的属性值。仅用于diff数据解析场景。
 * GmcYangNodeTypeE为 GMC_YANG_TYPE_CONTAINER 或 GMC_YANG_TYPE_LIST 时获取该node节点的主键属性值。
 * @param[in] currNode GmcYangNodeT 对象句柄。
 * @param[in] propId 主键索引字段下标。
 * @param[out] propValue 获取到的主键索引属性值。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetKeyPropValue(
    const GmcYangNodeT *currNode, uint32_t propId, GmcYangNodeValueT **propValue);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景下，获取 GmcYangNodeTypeE 为 GMC_YANG_LIST的 GmcYangNodeT
 * 节点在事务操作修改后的前驱节点的主键索引的属性值。 仅用于GmcDiffOpTypeE 为  GMC_DIFF_OP_UPDATE 和 GMC_DIFF_OP_CREATE
 * 时才有更新后的前驱节点，即新的前驱。
 * @param[in] currNode GmcYangNodeT 对象句柄。
 * @param[in] propId 主键索引字段下标。
 * @param[out] propValue 获取到的主键索引属性值。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetNewPrevKey(
    const GmcYangNodeT *currNode, uint32_t propId, GmcYangNodeValueT **propValue);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景下，获取 GmcYangNodeTypeE 为 GMC_YANG_LIST的 GmcYangNodeT
 * 节点在事务操作修改前的前驱节点的主键索引的属性值。 仅用于GmcDiffOpTypeE 为  GMC_DIFF_OP_UPDATE 和 GMC_DIFF_OP_REMOVE
 * 时才有更新前的前驱节点，即老的前驱。
 * @param[in] currNode GmcYangNodeT 对象句柄。
 * @param[in] propId 主键索引字段下标。
 * @param[out] propValue 获取到的主键索引属性值。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeGetOldPrevKey(
    const GmcYangNodeT *currNode, uint32_t propId, GmcYangNodeValueT **propValue);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 释放yang tree节点遍历信息。
 * @param[in] yangTree diff结果树结构句柄。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangFreeTree(const GmcYangTreeT *yangTree);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 获取yang tree node是否存在新的前驱节点，当节点类型为GMC_YANG_CONTAINER|GMC_YANG_LIST|GMC_YANG_LEAFLIST时有意义。
 * @attention
 * 仅用于 diff 数据解析场景、且映射为 vertex 类型的节点（即部分打散下的 list 或全打散下的 container 和
 * list），否则报错。
 * @param[in] node yang tree node句柄。
 * @param[out] isExist yang tree node是否存在前驱节点。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeHasNewPrev(const GmcYangNodeT *node, bool *isExist);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 获取yang tree node是否存在旧的前驱节点，当节点类型为GMC_YANG_CONTAINER|GMC_YANG_LIST|GMC_YANG_LEAFLIST时有意义。
 * @attention
 * 仅用于 diff 数据解析场景、且映射为 vertex 类型的节点（即部分打散下的 list 或全打散下的 container 和
 * list），否则报错。
 * @param[in] node yang tree node句柄。
 * @param[out] isExist yang tree node是否存在前驱节点。
 * @return @li GMERR_OK@li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangNodeHasOldPrev(const GmcYangNodeT *node, bool *isExist);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景获取错误路径信息，线程级变量，只保留最近一次服务端返回的ErrorPath信息
 * @param[out] errPathInfo: 错误路径信息结构体，参考GmcErrorPathInfoT结构体，包含两个字符串， errorMsg和errorPath
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetErrorPathInfo(GmcErrorPathInfoT *errPathInfo);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景用于释放error-path信息使用的内存等资源
 * 用户获取完了error-path信息之后需调用该接口释放内存；如果不调用，则下次error-path信息来临时自动释放并重新分配用于存储新的。
 * 建议用户每次调用GmcYangGetErrorPathInfo后能调用该接口进行释放，以免存留缓存内存影响小型化内存底噪。
 */
GMC_EXPORT void GmcYangFreeErrorPathInfo(void);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景编辑树模型的孩子节点，根据孩子节点名设置孩子节点的原语操作类型，创建孩子节点并获取其句柄。
 * 此接口既用于DML操作，又用于subtree过滤。
 * DML操作的choice/case路径补全场景下，传入的name是由choice/case构成的通往孩子节点的路径，接口内部会对路径上的每个节点进行编辑
 * @attention
 * 用于subtree过滤时，会默认将新创建的过滤树子节点设置为容器过滤（容器过滤标志位设为true），
 * 之后如果调用GmcYangSetNodeProperty接口对该子节点设置了属性或者调用GmcYangEditChildNode接口创建了更下层子节点，
 * 再将其容器过滤标志位设置为false。
 * @param[in] node 父节点句柄。
 * @param[in] name 孩子节点名字。在choice/case路径补全场景下是一条由choice/case节点名构成的路径
 * @param[in] opType 对孩子节点的原语操作类型，支持六原语操作类型和顶点的一致，也支持subtree过滤操作类型。
 * @param[out] child 返回孩子节点的句柄
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangEditChildNode(GmcNodeT *node, const char *name, GmcOperationTypeE opType, GmcNodeT **child);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 用于 obj模式编辑subtree条件时指定对 leaflist 的过滤条件以与逻辑连接。
 * 此接口仅用于subtree过滤，仅支持对leaflist的过滤条件进行设置，同个leaflist节点的过滤条件连接方式应相同
 * @param[in] node subtree查询条件节点句柄。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangSubtreeSetAndCondition(GmcNodeT *node);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景根据属性名获取属性propId和属性类型propType
 * 此接口既用于可DML操作产生的GmcNode，又可用于subtree过滤产生的GmcNode。
 * @param[in] node 当前节点句柄。
 * @param[in] propName 要获取的属性名字
 * @param[out] fieldId 出参属性的id。
 * @param[out] propertyType 出参属性的type
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetPropTypeByName(
    const GmcNodeT *node, const char *propName, uint32_t *fieldId, uint32_t *propertyType);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景根据属性名获取union属性实际类型propType
 * 此接口只用于查询数据产生的GmcNode。如果传入的非union数据类型则报错。
 * @param[in] node 当前节点句柄。
 * @param[in] propName 要获取的属性名字
 * @param[out] fieldId 出参属性的id。
 * @param[out] propertyType 出参属性的type
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetPropUnionTypeByName(const GmcNodeT *node, const char *propName, uint32_t *propertyType);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景根据属性名称设置节点的属性值。可用于普通DML操作设置vertex或node的属性，也可用于subtree过滤设置内容过滤和叶子过滤。
 * @param[in] node 节点句柄。
 * @param[in] propValue 属性信息结构体。注意，此时设定的propertyId不生效。
 * @attention
 * 当做叶子过滤时，将GmcPropValueT中的type设置为GMC_DATATYPE_NULL数据类型，value设置为NULL，size设置为0即可。
 * 当DML操作中opType为GMC_YANG_PROPERTY_OPERATION_DELETE或GMC_YANG_PROPERTY_OPERATION_REMOVE时，也可以这样设置
 * @param[in] optype 属性的操作类型，请参见GmcYangPropOpTypeE。
 * @attention
 * 当用于subtree过滤设置内容过滤或叶子过滤时，必须将optType设置为GMC_YANG_PROPERTY_OPERATION_CREATE，否则报错。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangSetNodeProperty(GmcNodeT *node, const GmcPropValueT *propValue, GmcYangPropOpTypeE optype);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景根据节点获取节点下属性和子节点的个数。
 * @param[in] node 节点句柄。
 * @param[out] number 出参，当前节点下属性和子节点的个数。
 * @attention
 * 树模型中，number只表示下一层的属性与子node个数之和
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetChildPropAndNodeNumber(GmcNodeT *node, uint32_t *number);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * yang场景根据下标id获取node节点的孩子类型与名字。
 * @param[in] node 节点句柄。
 * @param[in] id 子节点或属性的下标id。
 * @param[out] isProp 标识当前下标对应的孩子是否为属性。
 * @param[out] name 孩子名字指针。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetChildTypeAndNameById(GmcNodeT *node, uint32_t id, bool *isProp, const char **name);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * node节点转换枚举和identity类型的值，可将key转value，value转key
 * @param[in] node 节点句柄。
 * @param[in] convertType 转换类型，可选key转value，或者value转key
 * @param[in] inPropValue 包括属性名称，属性类型，属性值，内存大小等。注意，此时设定的propertyId不生效。
 * @param[out] outPropValue 转换后的属性值和size。注意，outPropValue中的value内存由调用者申请
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangConvertNodeProperty(
    GmcNodeT *node, GmcConvertTypeE convertType, GmcPropValueT *inPropValue, GmcPropValueT *outPropValue);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * subtree和diff结果树节点转换枚举和identity类型的值，可将key转value，value转key
 * @param[in] node yang tree node句柄。
 * @param[in] nodeValue 当前节点的值，当前如果为字段，只会返回int32_t类型，所以转换只存在int32_t转string
 * @param[out] outPropValue 转换后的属性值和size。注意，outPropValue中的value内存由调用者申请
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangConvertResultProperty(
    GmcYangNodeT *node, GmcYangNodeValueT *nodeValue, GmcPropValueT *outPropValue);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * Yang场景进行业务校验异步接口
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] config 用户传入的需要校验的配置参数
 * @param[in] userCb 用户的回调函数，参考GmcYangValidateDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangValidateAsync(
    GmcStmtT *stmt, GmcValidateConfigT *config, GmcYangValidateDoneT userCb, void *userData);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * Yang场景进行模型校验异步接口
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] userCb 用户的回调函数，参考GmcYangValidateDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangValidateModelAsync(GmcStmtT *stmt, GmcYangValidateDoneT userCb, void *userData);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * Yang场景，subtree查询异步接口。在回调函数中获取到fetchRet后，根据过滤模式需要调用GmcYangFetchRetDeparse或者
 * GmcYangFetchJsonRetDeparse接口解析取出具体查询结果。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] filter 用户配置的过滤查询条件，请参见
 * GmcSubtreeFilterT，查询第一批结果时filter必须非空，查询后续批次时可以为空“NULL”。
 * @param[in] fetchRet 上一批查询返回结果。查询第一批结果时fetchRet必须为空“NULL”；
 * 查询后续批次时必须为该批次的前一批GmcYangFetchExecuteDoneT返回的结果。
 * @param[in] userCb 用户的回调函数，参考 GmcYangFetchExecuteDoneT 。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 * @warning 当返回值是GMERR_OK时，该接口内部会释放fetchRet资源，fetchRet不能再被使用了；
 * 当返回值不是GMERR_OK时，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 */
GMC_EXPORT int32_t GmcYangSubtreeFilterExecuteAsync(
    GmcStmtT *stmt, GmcSubtreeFilterT *filter, GmcFetchRetT *fetchRet, GmcYangFetchExecuteDoneT userCb, void *userData);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * Yang场景，subtree查询异步接口。在回调函数中获取到fetchRet后，根据过滤模式需要调用GmcYangFetchRetDeparse或者
 * GmcYangFetchJsonRetDeparse接口解析取出具体查询结果。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] option 用户配置的过滤选项，通过 GmcYangCreateSubtreeOption
 * 创建，查询第一批结果时filter必须非空，查询后续批次时可以为空“NULL”。
 * @param[in] fetchRet 上一批查询返回结果。查询第一批结果时fetchRet必须为空“NULL”；
 * 查询后续批次时必须为该批次的前一批GmcYangFetchExecuteDoneT返回的结果。
 * @param[in] userCb 用户的回调函数，参考 GmcYangFetchExecuteDoneT 。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 * @warning 当返回值是GMERR_OK时，该接口内部会释放fetchRet资源，fetchRet不能再被使用了；
 * 当返回值不是GMERR_OK时，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 */
GMC_EXPORT int32_t GmcYangSubtreeFilterExtExecuteAsync(
    GmcStmtT *stmt, GmcSubtreeOptionT *option, GmcFetchRetT *fetchRet, GmcYangFetchExecuteDoneT userCb, void *userData);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 解析结构化查询结果信息。
 * @param[in] fetchRet 查询结果句柄。
 * @param[out] isEnd 表示当前查询结果批次是否为最后一批。
 * 如果isEnd为false，表示当前查询未完成，还有剩余批次需要用户继续调用查询接口
 * （如GmcYangSubtreeFilterExecuteAsync、GmcYangFetchDiffExecuteAsync等）进行下一批查询；
 * 如果isEnd为true，表示当前查询已完成，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 * @param[out] treeReply 返回指向查询结果树数组的指针。
 * @param[out] count 查询结果树个数
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangFetchRetDeparse(
    const GmcFetchRetT *fetchRet, bool *isEnd, const GmcYangTreeT ***treeReply, uint32_t *count);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 解析查询diff结果fetchRet包含查询是否全部返回以及当前diff buffer的长度。
 * @param[in] fetchRet 查询结果句柄。
 * @param[out] isEnd 表示当前查询结果批次是否为最后一批。
 * 如果isEnd为false，表示当前查询未完成，还有剩余批次需要用户继续调用查询接口 GmcYangFetchDiffBufExecuteAsync
 * 进行下一批查询； 如果isEnd为true，表示当前查询已完成，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 * @param[out] bufLen 表示Diff查询结果buf的长度。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangFetchDiffBufRetDeparse(const GmcFetchRetT *fetchRet, bool *isEnd, uint32_t *bufLen);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 解析查询结果diff buffer
 * @param[in] fetchRet 查询结果句柄。
 * @param[in] bufLen 用户传入的buf大小。
 * @param[out] diffBuf 用户传入的buf。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangFetchDiffBuf(const GmcFetchRetT *fetchRet, uint32_t bufLen, uint8_t *diffBuf);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 从推送消息中解析获取订阅的diff数据，用户需调用 GmcYangFetchRetDeparse 从fetchRet句柄解析diff数据。
 * 用户使用完fetchRet数据后必须显式调用GmcYangFreeFetchRet释放fetchRet，否则内存泄漏。
 * @param[in] 订阅回调返回客户端stmt句柄。
 * @param[out] 推送的diff数据结果句柄。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcFetchSubsDiffTree(GmcStmtT *stmt, GmcFetchRetT **fetchRet);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 通过用户的diff buffer重新解析查询结果
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] bufLen 用户传入的buf大小
 * @param[in] diffBuf 用户传入的buf
 * @param[out] fetchRet 查询结果句柄。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangDiffFetchRetFromBuf(
    GmcStmtT *stmt, uint32_t bufLen, const uint8_t *diffBuf, GmcFetchRetT **fetchRet);

/**
 * @ingroup gmc_yang
 * @par 描述：
 * 解析json形式的查询结果信息
 * @param[in] fetchRet 查询结果句柄。
 * @param[out] isEnd 表示当前查询结果批次是否为最后一批。
 * 如果isEnd为false，表示当前查询未完成，还有剩余批次需要用户继续调用查询接口GmcYangSubtreeFilterExecuteAsync进行下一批查询；
 * 如果isEnd为true，表示当前查询已完成，用户必须调用 GmcYangFreeFetchRet 释放fetchRet资源。
 * @param[out] jsonReply 返回指向查询结果json串数组的指针。
 * @param[out] count 返回查询结果json串的个数
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangFetchJsonRetDeparse(
    const GmcFetchRetT *fetchRet, bool *isEnd, const char ***jsonReply, uint32_t *count);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * Yang场景，释放 subtree 或 diff 查询返回结果的内存等资源。
 * @attention 用户使用完GmcFetchRetT的查询结果数据之后须调用该接口释放内存。
 *            如果查询需要分批进行，且前面的一次查询结果 GmcFetchRetT 数据被使用完成之后没有调用该接口释放内存，
 *            那么，在下一次查询请求时传入 GmcFetchRetT 指针参数时，GmcFetchRetT 的内存会被自动释放。
 *            但是，如果有分批多次查询，必须在获取最后一次查询的结果后调用此接口释放内存，否则内存泄漏。
 * @param[in] fetchRet 查询返回结果指针。
 */
GMC_EXPORT void GmcYangFreeFetchRet(GmcFetchRetT *fetchRet);

/**
 * @ingroup gmc_yang
 * @par
 * 描述：用于choice/case路径补全中，获取从父节点到属性或子节点之间缺失的choice/case路径信息
 * 在YANG的场景下，模型映射时通过choice/case节点来描述互斥关系，但是业务实际操作不感知choice/case节点，会直接操作case的字段或
 * 子节点，导致操作失败。报错后使用本接口可获取从父节点到属性或子节点之间缺失的choice/case路径信息，据其重新进行失败的操作。
 * 如果存在多条路径，只会返回一条。
 * @param[in] node 父节点句柄
 * @param[in] childName 要操作的 case 的字段名或子节点名
 * @param[in] childType 要操作的是字段还是node还是vertex
 * @param[out] pathInfo 返回父节点到属性或子节点之间缺失的choice/case路径信息
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetChoiceCasePath(
    const GmcNodeT *node, const char *childName, GmcYangChildElementTypeE childType, GmcLostPathInfoT *pathInfo);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 配置数据进行模型导入异步接口
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] config 导入配置。
 * @param[in] userCb 用户的回调函数，参考GmcImportDataDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangImportDataAsync(
    GmcStmtT *stmt, const GmcPersistenceConfigT *config, GmcImportDataDoneT userCb, void *userData);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 配置数据进行模型导出异步接口
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] config 导出配置。
 * @param[in] userCb 用户的回调函数，参考GmcExportDataDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangExportDataAsync(
    GmcStmtT *stmt, const GmcPersistenceConfigT *config, GmcExportDataDoneT userCb, void *userData);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 查询leafref引用对象接口
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] path 查询对象。
 * @param[in] option 查询可选配置。
 * @param[in] userCb 用户的回调函数，参考GmcGetLeafrefDataDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetLeafrefPathAsync(
    GmcStmtT *stmt, const char *path, GmcGetLeafrefPathOptionsT *option, GmcGetLeafrefDataDoneT userCb, void *userData);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 创建查询leafref引用对象的option接口, 需要调用 GmcYangGetLeafrefPathOptionsDestroy 释放
 * @param[in] mode 获取leafref引用对象的模式。
 * @param[in/out] leafrefOption option 对象。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangGetLeafrefPathOptionsCreate(
    GmcGetLeafrefPathModeE mode, GmcGetLeafrefPathOptionsT **leafrefOption);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 释放 leafref option接口
 * @param[in] leafrefOption option 对象。
 */
GMC_EXPORT void GmcYangGetLeafrefPathOptionsDestroy(GmcGetLeafrefPathOptionsT *leafrefOption);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 校验propValue和字段定义的默认值是否相等
 * @param[in] node 节点句柄 对象。
 * @param[in] propValue 用户写入的属性信息。
 * @param[out] defaultState 出参，默认值校验的结果。
 */
GMC_EXPORT int32_t GmcYangCheckNodePropertyWithDefault(
    GmcNodeT *node, const GmcPropValueT *propValue, GmcYangPropDefaultStateE *defaultState);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 创建获取subtree查询选项结构体, 需要调用 GmcYangDestroySubtreeOption 释放
 * @param[in/out] subtreeOption option 对象。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangCreateSubtreeOption(GmcSubtreeOptionT **subtreeOption);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 设置GMC_FETCH_JSON_RFC7951或GMC_FETCH_FULL_JSON_RFC7951查询模式下默认值的前缀。
 * @param[in] subtreeOption option 对象。
 * @param[in] defaultPrefix 用户传入的 defaultPrefix 字符串，用于RFC 7951格式查询时指定默认值前缀信息。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangSubtreeOptionSetJsonDefaultPrefix(
    GmcSubtreeOptionT *subtreeOption, const char *defaultPrefix);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 设置subtree查询选项结构体的 subtreeFilter
 * @param[in] subtreeOption option 对象。
 * @param[in] subtreeFilter 用户构造的subtree查询条件，参考 GmcSubtreeFilterT。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangSubtreeOptionSetFilter(GmcSubtreeOptionT *subtreeOption, GmcSubtreeFilterT *subtreeFilter);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 释放subtree查询选项结构体
 * @param[in/out] subtreeOption option 对象。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT void GmcYangDestroySubtreeOption(GmcSubtreeOptionT *subtreeOption);

#ifdef __cplusplus
}
#endif

#endif /* GMC_YANG_H */
