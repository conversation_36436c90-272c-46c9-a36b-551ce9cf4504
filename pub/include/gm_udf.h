/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: gm_udf.h
 * Description: File provide GMDB APIs for datalog user define functions.
 * Author: GMDBv5 EE Team
 * Create: 2022-8-25
 */

#ifndef GM_UDF_H
#define GM_UDF_H

#include <stdarg.h>
#include "stdlib.h"
#include "stdint.h"
#include "gmc_errno.h"

#ifdef __cplusplus
extern "C" {
#endif

#if defined(__GNUC__)
#define GM_EXPORT __attribute__((visibility("default")))
#else
#define GM_EXPORT
#endif

/**
 * @defgroup gm_udf GMDBv5 提供给Datalog UDF的API。
 */

/**
 * @ingroup gm_udf
 * UDF函数执行的上下文。
 */
typedef struct GmUdfCtx GmUdfCtxT;
typedef struct GmUdfShmCtx GmUdfShmCtxT;

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数中申请内存。
 * @attention 请勿将内存使用的范围超过函数的生命周期。
 * @param[in] ctx UDF函数执行的上下文。
 * @param[in] size 申请内存的大小。
 * @return @li NULL申请内存失败 @li 其他返回值，申请内存的地址。
 */
GM_EXPORT void *GmUdfMemAlloc(GmUdfCtxT *ctx, size_t size);

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数中释放内存。
 * @attention 只能释放从相同内存上下文中申请的内存。
 * @param[in] ctx UDF函数执行的上下文。
 * @param[in] ptr 内存的地址。
 */
GM_EXPORT void GmUdfMemFree(GmUdfCtxT *ctx, void *ptr);

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数中获取NamespaceName。
 * @param[in] ctx UDF函数执行的上下文。
 * @return @li NULL获取namespaceName失败 @li 其他返回值，NamespaceName字符串。
 */
GM_EXPORT const char *GmUdfGetNamespaceName(GmUdfCtxT *ctx);

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数中获取NamespaceId。
 * @param[in] ctx UDF上下文。
 * @param[in] namespaceId namespaceId的地址。
 * @return @li 0获取namespaceId成功 @li 其他返回值获取namespaceId失败
 */
GM_EXPORT int32_t GmUdfGetNamespaceId(GmUdfCtxT *ctx, uint32_t *namespaceId);

/**
 * @ingroup gm_udf
 * UDF函数中读取元组。
 */
typedef struct GmUdfReader GmUdfReaderT;

/**
 * @ingroup gm_udf
 * @par 描述
 * @attention 元组的内存在函数执行结束后统一释放。
 * UDF函数中获取迭代器中下一个元组。
 * @param[in] reader 迭代器。
 * @param[out] tuple 元组。
 * @return @li GMERR_OK 获取到元组。
           @li GMERR_NO_DATA 迭代结束没有更多元组。
           @li 其他错误码为异常。
 */
GM_EXPORT int32_t GmUdfGetNext(GmUdfReaderT *reader, void **tuple);

/**
 * @ingroup gm_udf
 * @par 描述
 * @attention value的内存在函数执行结束后统一释放。
 * UDF函数中根据key获取能力集KV表中对应value的值。
 * @param[in] key 查询的能力集KV表项的key。
 * @param[in] keyLen 查询的能力集KV表项的keyLen。
 * @param[out] value 查询的能力集KV表项的value。
 * @param[out] valueLen 查询的能力集KV表项的valueLen。
 * @return @li GMERR_OK 获取到value。
           @li GMERR_DATATYPE_MISMATCH 获取到的value长度超过valueLen。
           @li GMERR_NO_DATA 未获取到对应value。
           @li 其他错误码为异常。
 */
GM_EXPORT int32_t GmUdfGetAccessKV(GmUdfCtxT *ctx, void *key, const uint32_t keyLen, void *value, uint32_t *valueLen);

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数中创建索引扫描的迭代器。
 * @attention 只支持origin表。
 * @param[in] ctx UDF函数执行的上下文。
 * @param[in] tupleStruLen 元组的结构体长度
 * @param[in] tuple 用于传入索引key值。
 * @param[in] tableSeqNum 表的编号，和.d文件access_current选项中表顺序一致，从0开始。
 * @param[in] indexSeqNum 使用的索引编号，和.d文件索引的定义编号一致。
 * @param[out] reader 迭代器。
 * @return @li GMERR_OK 成功。
           @li 其他错误码为错误。
 */
GM_EXPORT int32_t GmUdfCreateCurrentReaderByIndex(GmUdfCtxT *ctx, size_t tupleStruLen, void *tuple,
    uint32_t tableSeqNum, uint32_t indexSeqNum, GmUdfReaderT **reader);

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数中创建读origin表的迭代器。
 * @param[in] ctx UDF函数执行的上下文。
 * @param[in] tableSeqNum 表的编号，和.d文件access_current选项中表顺序一致，从0开始。
 * @param[out] reader 迭代器。
 * @return @li GMERR_OK 成功。
           @li 其他错误码为错误。
 */
GM_EXPORT int32_t GmUdfCreateCurrentReader(GmUdfCtxT *ctx, uint32_t tableSeqNum, GmUdfReaderT **reader);

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数中创建读Delta表的迭代器。
 * @param[in] ctx UDF函数执行的上下文。
 * @param[in] tableSeqNum 表的编号，和.d文件access_delta选项中表顺序一致，从0开始。
 * @param[out] reader 迭代器。
 * @return @li GMERR_OK 成功。
           @li 其他错误码为错误。
 */
GM_EXPORT int32_t GmUdfCreateDeltaReader(GmUdfCtxT *ctx, uint32_t tableSeqNum, GmUdfReaderT **reader);

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数中销毁迭代器。
 * @param[in] ctx UDF函数执行的上下文。
 * @param[in] reader 迭代器。
 */
GM_EXPORT void GmUdfDestroyReader(GmUdfCtxT *ctx, GmUdfReaderT *reader);

/**
 * @ingroup gm_udf
 * 提供将元组写入Delta表的能力。
 */
typedef struct GmUdfWriter GmUdfWriterT;

/**
 * @ingroup gm_udf
 * @par 描述：
 * 将元组写入Delta表的。
 * @attention @li 插入的元组需要和表的schema定义匹配。
 *            @li tuple内存所有权不会被转移到writer，需要UDF自己管理内存。
 * @param[in] writer 结果集。
 * @param[in] tupleStruLen 待插入元组的结构体长度
 * @param[in] tuple 待插入的tuple元组。
 * @return @li GMERR_OK 成功。
           @li 其他错误码为错误。
 */
GM_EXPORT int32_t GmUdfAppend(GmUdfWriterT *writer, size_t tupleStruLen, void *tuple);

/**
 * @ingroup gm_udf
 * @par 描述：
 * 获取Delta表的TableWriter的接口
 * @attention @li 只支持Delta表。
 *            @li 使用后无需关闭接口，相关资源会自动释放。
 * @param[in] ctx UDF函数执行的上下文。
 * @param[in] tableSeqNum 表的编号，和.d文件中配置的访问表列表顺序一致，从0开始。
 * @param[out] writer Delta表的写句柄。
 * @return @li GMERR_OK 成功。
           @li 其他错误码为错误。
 */
GM_EXPORT int32_t GmUdfGetDeltaWriter(GmUdfCtxT *ctx, uint32_t tableSeqNum, GmUdfWriterT **writer);

/**
 * @ingroup gm_udf
 * @par 描述：
 * UDF函数声明,包括普通的%function函数和表过期处理函数。
 * @param[in] tuple UDF操作的元组。
 * @param[in] ctx UDF函数中使用的内存上下文。
 * @return @li GMERR_OK 成功。
           @li 其他错误码为错误。
 */
typedef int32_t (*DtlFunction)(void *tuple, GmUdfCtxT *ctx);

/**
 * @ingroup gm_udf
 * @par 描述：
 * 聚合函数声明
 * @param[in] input 输入的分组后的元组，每次函数调用输入一个分组的元组。
 * @param[out] output 聚合函数中后返回的元组。
 * @param[in] ctx UDF函数执行的上下文。
 * @return @li GMERR_OK 成功。
           @li 其他错误码为错误。
 */
typedef int32_t (*DtlAggManyToMany)(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx);

/**
 * @ingroup gm_udf
 * @par 描述：
 * 聚合函数声明
 * @param[in] input 输入的分组后的元组，每次函数调用输入一个分组的元组。
 * @param[out] outpuLen 聚合函数中返回的元组结构体长度
 * @param[out] output 聚合函数中后返回的元组,元组的内存由UDF申请，不需要UDF自己释放。
 * @param[in] ctx UDF函数执行的上下文。
 * @return @li GMERR_OK 成功。
           @li 其他错误码为错误。
 */
typedef int32_t (*DtlAggManyToOne)(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx);

/**
 * @ingroup gm_udf
 * @par 描述：
 * 比较函数声明
 * @param[in] tuple1 用于比较的元组1。
 * @param[in] tuple2 用于比较的元组2。
 * @param[in] ctx UDF函数执行的上下文。
 * @return @li 负数，tuple1<tuple2;
           @li 0，tuple1=tuple2;
           @li 正数，tuple1>tuple2;
 */
typedef int32_t (*DtlCompare)(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx);

/*
 * @ingroup gm_udf
 * @par 描述:
 * 超时函数声明
 * @param[in] timeoutTuple 超时元组
 * @param[out] extraTupleLen 超时函数新生成的元组结构体的长度
 * @param[out] extraTuple 超时函数新生成的元组
 * @param[in] ctx UDF函数执行的上下文
 * @return @li GMERR_OK 成功
           @li 其他错误码为错误
 */
typedef int32_t (*DtlTimeout)(const void *timeoutTuple, size_t *extraTupleLen, void **extraTuple, GmUdfCtxT *ctx);

/**
 * @ingroup gm_udf
 * @par 描述：
 * TBM表udf声明
 * @param[in] op DML操作类型，1表示删除操作，0表示插入操作。
 * @param[in] tuple 存放序列化数据。
 * @return @li GMERR_OK ;
 */
typedef int32_t (*TbmUdf)(uint32_t op, void *tuple);

/**
 * @ingroup DtlInitT
 * @brief init函数为datalog udf函数, 用于datalog资源初始化。
 * @param[in] ctx UDF函数执行的上下文。
 * @return @li GMERR_OK ;
 */
typedef int32_t (*DtlInitT)(GmUdfCtxT *ctx);

/**
 * @ingroup DtlInitT
 * @brief uninit函数为datalog udf函数，用于datalog资源去初始化。
 * @param[in] ctx UDF函数执行的上下文。
 * @return @li GMERR_OK ;
 */
typedef int32_t (*DtlUninitT)(GmUdfCtxT *ctx);

/**
 * @ingroup DtlPreInvokeT
 * @brief pre_invoke函数为datalog udf函数, 在datalog DML操作处理输出表前调用。
 * @param[in] ctx UDF函数执行的上下文。
 * @return @li GMERR_OK ;
 */
typedef int32_t (*DtlPreInvokeT)(GmUdfCtxT *ctx);

/**
 * @ingroup DtlPostInvokeT
 * @brief post_invoke函数为datalog udf函数，在datalog DML操作处理输出表后调用。
 * @param[in] ctx UDF函数执行的上下文。
 * @return @li GMERR_OK ;
 */
typedef int32_t (*DtlPostInvokeT)(GmUdfCtxT *ctx);

/**
 * @ingroup gm_udf
 * 消息通知表UDF函数批量消息结构。
 */
typedef struct GmMsgNotifyTupleChange {
    uint32_t op;   // operation type, 0:insert, 1:delete, 2:update
    void *oldTup;  // delete tuple
    void *newTup;  // insert tuple
} GmMsgNotifyTupleChangeT;

/**
 * @ingroup gm_udf
 * @par 描述：
 * 消息通知表udf声明
 * @param[in] batch 批消息数组。
 * @param[in] batchLen 数组长度。
 * @return @li GMERR_OK ;
 */
typedef int32_t (*MsgNotifyUdf)(GmMsgNotifyTupleChangeT *batch, uint32_t batchLen);

/**
 * @ingroup gm_udf
 * @par 描述：
 * 消息通知表的compare udf声明
 * @param[in] tup1 进行比较的记录1
 * @param[in] tup2 进行比较的记录2
 * @return @li GMERR_OK ;
 */
typedef int32_t (*MsgNotifyCmpUdf)(const void *tup1, const void *tup2);

/**
 * @ingroup gm_udf
 * @par 描述：
 * 提供给TBM组件的日志函数,TBM在UDF中将该函数指针赋值全局日志钩子变量,后续调用日志钩子打印日志
 * 实现将TBM模块日志打印到DB日志文件中的功能
 * @param[in] level 日志级别
 * @param[in] formatStr 格式化字符串
 * @param[in] args 格式化字符串中的参数
 */
void DbTbmLogger(uint32_t level, const char *formatStr, va_list args);

/**
 * @ingroup gm_udf
 * @par 描述：
 * 提供给TBM组件, 用于双拼场景下将失败信息传递给TBM组件的结构体
 */
typedef struct GmRequestInfo {
    const char *errorLog;  // buffer长度，固定为500B
} GmRequestInfoT;

/**
 * @ingroup gm_udf
 * @par 描述：
 * 提供给TBM组件, 用于双拼场景下将失败信息传递给TBM组件的函数钩子
 * @param[in] ctx UDF函数执行的上下文。
 * @param[in] info 失败信息
 */
typedef int32_t (*DtlTransFailT)(GmUdfCtxT *ctx, GmRequestInfoT *info);

#ifdef __cplusplus
}
#endif

#endif  // GM_UDF_H
