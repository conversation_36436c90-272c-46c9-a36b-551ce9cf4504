/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: gmc_types.h
 * Description: header file for data type definition
 * Author:
 * Create: 2020-7-31
 */

#ifndef GMC_TYPES_H
#define GMC_TYPES_H

#include <inttypes.h>
#include <stdbool.h>
#include <stddef.h>
#include <pthread.h>
#include "gmc_errno.h"

#ifdef __cplusplus
extern "C" {
#endif

#if defined(__GNUC__)
#define GMC_EXPORT __attribute__((visibility("default")))
#else
#define GMC_EXPORT
#endif

/**
 * @defgroup gmc_types 相关类型定义&枚举&数据结构
 */

typedef struct GmcConnT GmcConnT;
typedef struct GmcStmtT GmcStmtT;
typedef struct GmcDirectReadStmtT GmcDirectReadStmtT;
typedef struct GmcConnOptionsT GmcConnOptionsT;
typedef struct GmcStmtViewT GmcStmtViewT;
typedef struct GmcNodeT GmcNodeT;
typedef struct GmcIndexKeyT GmcIndexKeyT;
typedef struct GmcCheckInfoT GmcCheckInfoT;
typedef struct GmcBatchT GmcBatchT;
typedef struct GmcRespT GmcRespT;
typedef struct GmcImportDatalogOptionsT GmcImportDatalogOptionsT;
typedef struct GmcUnimportDatalogOptionsT GmcUnimportDatalogOptionsT;

/* 定义stmt中ModelType类型，需要和MODEL_STREAM、MODEL_TS的枚举值对应 */
#define GMC_MODEL_STREAM 8
#define GMC_MODEL_TS 6

/* Transaction Application Scenario */
#define GMC_TRANS_USED_IN_CS 1  // CS: The client sends the usocket message to the server.
#define GMC_TRANS_NONE 0

// client处理错误最大记录数
#define GMC_SUB_BATCH_MAX 128
// ===================================enum===============================

/**
 * @ingroup gmc_types
 * warning资源类别、及其对应的warningID的枚举。warning资源相关说明，请参见warning说明。
 */
typedef enum {
    GMC_ALARM_SHM_USED_INFO = 0, /**< 已使用的总的共享内存的warning。 */
    GMC_ALARM_ASYNC_CONN_RING,   /**< 异步通道消息堆积的warning。 */
    GMC_ALARM_SUB_CONN_RING,     /**< 订阅通道消息堆积的warning。 */
    GMC_ALARM_CONNECTION_NUMBER, /**< 连接通道总数（异步通道&订阅通道）的warning。 */
    GMC_ALARM_CLIENT_STMT,       /**< 系统总共可创建stmt个数的warning。 */
    GMC_ALARM_VETEX_EDGE_DATA, /**< 存储业务数据占用共享内存（包括heap、index、edge、undo、fsm等）的warning。 */
    GMC_ALARM_SUB_MSG_POOL, /**< 订阅消息池占用内存的warning。\n 当前暂不支持该warning资源类别。 */
    GMC_ALARM_DYM_USED_INFO,         /**< 已使用的总的动态内存的warning。 */
    GMC_ALARM_HUNG_WORKER,           /**< 线程挂死warning，线程长时间处理某个任务，未进行喂狗 */
    GMC_ALARM_TABLE_SPACE_USED_INFO, /**< tableSpace已使用的内存的warning。 */
    GMC_ALARM_RSM_TABLE_SPACE_USED_INFO, /**< rsm-tableSpace已使用的内存的warning。 */
    GMC_ALARM_STORAGE_SPACE_USED_INFO,   /**< storage space访问磁盘空间满warning。 */
    GMC_ALARM_STORAGE_FILE,              /**< storage file访问异常warning。 */
    GMC_ALARM_ID_UNDEFINED,              /**< 未定义的warning。 */
    GMC_ALARM_ID_BUTT,                   /**< 无效的warningID，返回错误。 */
} GmcAlarmIdE;

/**
 * @ingroup gmc_types
 * GmcRespT类型的枚举。
 */
typedef enum {
    GMC_RESP_INVALIED,          /**< 默认创建的Resp类型，为无效RESP，返回错误。 */
    GMC_RESP_SEND_FAILED_INDEX, /**< 发送错误信息。 */
    GMC_RESP_SEND_BATCH_INSERT, /**< 发送批量插入点标签。 */
    GMC_RESP_BUTT,              /**< 无效的Resp类型，返回错误。 */
} GmcRespModeTypeE;

/**
 * @ingroup gmc_types
 * warning数据源类型的枚举。
 */
typedef enum {
    GMC_ALARM_SERVER = 0,    /**< 表示由服务端产生的warning数据。 */
    GMC_ALARM_CLIENT,        /**< 表示由客户端产生的warning数据。 */
    GMC_ALARM_SRC_TYPE_BUTT, /**< 表示无效的warning数据源，返回错误。 */
} GmcAlarmSrcTypeE;

/**
 * @ingroup gmc_types
 * DB过去一段时间内warning状态的枚举。
 */
typedef enum {
    GMC_ALARM_EMPTY = 0,      /**< 两次查询之间warning状态无变化 */
    GMC_ALARM_CLEARED,        /**< warning消除 */
    GMC_ALARM_ACTIVE,         /**< warning激活 */
    GMC_ALARM_ACTIVE_CLEARED, /**< warning激活后消除 */
    GMC_ALARM_STATUS_BUTT,    /**< 无效的warning状态，返回错误。 */
} GmcAlarmStatusE;

/**
 * @ingroup gmc_types
 * 表对账状态的枚举。
 */
typedef enum {
    GMC_CHECK_STATUS_NORMAL,   /**< 对账未开启。 */
    GMC_CHECK_STATUS_CHECKING, /**< 对账开启。 */
    GMC_CHECK_STATUS_ABNORMAL, /**< 上次对账异常。 */
    GMC_CHECK_STATUS_BUTT,     /**< 异常。 */
} GmcCheckStatusE;

/**
 * @ingroup gmc_types
 * 标记带有范围索引的vertex扫描区间边界的开/闭条件枚举。
 */
typedef enum {
    GMC_COMPARE_RANGE_OPEN = 0, /**< 区间边界为开区间。 */
    GMC_COMPARE_RANGE_CLOSED,   /**< 区间边界为闭区间。 */
    GMC_COMPARE_RANGE_BUTT,     /**< 无效的区间边界条件，返回错误。 */
} GmcCompareFlagE;

/**
 * @ingroup gmc_types
 * 连接类型的枚举。
 */
typedef enum {
    GMC_CONN_TYPE_SYNC,  /**< 同步连接。 */
    GMC_CONN_TYPE_ASYNC, /**< 异步连接。 */
    GMC_CONN_TYPE_SUB,   /**< 订阅连接。 */
    GMC_CONN_TYPE_BUTT,  /**< 无效的连接类型，返回错误。 */
} GmcConnTypeE;

/**
 * @ingroup gmc_types
 * 数据类型的枚举。追加类型只能放在末尾，顺序必须与服务器上的顺序一致。
 * 注：GmcDataTypeE枚举值发生变更，需要同步修改gmadmin工具中g_gmadminDataTypeDesc的枚举字符串。
 * @attention @li 位域就是把多个字段存储在一个Byte中（单字节位域），达到减少存储开销的目的。
 *  @li 当前只支持在位域上使用过滤字段，不支持在位域上构建索引，也不支持订阅。
 *  @li 位域只针对定长字段进行处理：\n
 *      如果多个相同的位域类型的字段在JSON文件中处于同一个Node且连续，会按照位域的大小尽可能地存储，无法存储的字段则另起一个位域存储。\n
 *      如果多个相同的位域类型的字段在JSON文件中不连续或者处于不同的Node，则会分开成多个位域进行存储。
 */
typedef enum {
    GMC_DATATYPE_CHAR,       /**< 有符号整型数据类型。 */
    GMC_DATATYPE_UCHAR,      /**< 无符号整型数据类型。 */
    GMC_DATATYPE_INT8,       /**< 有符号单字节整型。 */
    GMC_DATATYPE_UINT8,      /**< 无符号单字节整型。 */
    GMC_DATATYPE_INT16,      /**< 有符号双字节整型。 */
    GMC_DATATYPE_UINT16,     /**< 无符号双字节整型。 */
    GMC_DATATYPE_INT32,      /**< 有符号四字节整型。 */
    GMC_DATATYPE_UINT32,     /**< 无符号四字节整型。 */
    GMC_DATATYPE_BOOL,       /**< 布尔类型。 */
    GMC_DATATYPE_INT64,      /**< 有符号八字节整型。 */
    GMC_DATATYPE_UINT64,     /**< 无符号八字节整型。 */
    GMC_DATATYPE_FLOAT,      /**< 单精度浮点型。 */
    GMC_DATATYPE_DOUBLE,     /**< 双精度浮点型。 */
    GMC_DATATYPE_RESOURCE,   /**< 资源类型。\n
说明：\n
Tree模型下的资源字段仅支持建立在根节点上。\n
资源字段的nullable属性值不能为true，若不配置该属性，默认值为false。\n
资源字段不允许设置默认值。\n
资源类型采用uint64存储。\n
一个资源列的名字最大长度为64字节。\n
update/merge操作时，不能更新资源列字段。\n
当前只支持对资源字段的插入和删除，不支持对资源字段的修改。\n
一个资源表中最多允许有4个资源字段。\n
资源表中的资源字段必须一次性全部设置完。\n
当前不支持删除资源表，也不支持清空资源表中的数据。\n
对资源表进行replace操作时，如果存在主键相同的数据，资源字段的resid和DB中当前这条记录的resid一致才能成功，其他场景会报错。\n
*/
    GMC_DATATYPE_TIME,       /**< 时间类型。 int64_t */
    GMC_DATATYPE_BITFIELD8,  /**< 单字节位域。 uint8_t */
    GMC_DATATYPE_BITFIELD16, /**< 双字节位域。 uint16_t */
    GMC_DATATYPE_BITFIELD32, /**< 四字节位域。 uint32_t */
    GMC_DATATYPE_BITFIELD64, /**< 八字节位域。 uint64_t */
    GMC_DATATYPE_PARTITION,  /**< 分区字段类型。\n
说明：\n
每张表只能定义一个分区字段。\n
分区字段只能定义在根节点上。\n
分区字段的取值范围为[0,15]。\n
分区字段的值不支持更新。\n
分区字段的nullable属性值不能为true。\n
分区字段不允许有default属性值。\n
分区字段不能作为索引字段，因此索引的唯一性不会因为分区产生隔离。\n
*/
    GMC_DATATYPE_STRING,     /**< 字符串类型，以'\\0'为结束符。 */
    GMC_DATATYPE_BYTES,      /**< 变长数组类型。 */
    GMC_DATATYPE_FIXED,      /**< 定长数组类型。 */
    GMC_DATATYPE_BITMAP,     /**< 位图数据类型。\n
说明：\n
位图类型数据在创建时，必须设置size，否则提示错误。\n
size最大支持8*4*1024(bit)，且创建后不再变化。\n
位图类型数据值允许为null，如果值为null则只支持全量更新。\n
不支持default字段。\n
支持部分更新，但查询时只能是全量查询。\n
不支持在bitmap列上建立索引。\n
*/
    GMC_DATATYPE_NULL,       /**< 表示当前value为空值，非严格意义上的数据类型。\n
说明：\n
如果使用该数据类型来设置属性值时，其属性值长度参数值需填写为0。
*/
    GMC_DATATYPE_ENUM,       /**<yang 字段专用类型 int32_t。*/
    GMC_DATATYPE_IDENTITY,   /**<yang 字段专用类型 int32_t。*/
    GMC_DATATYPE_EMPTY,      /**<yang 字段专用类型 int32_t。*/
    GMC_DATATYPE_UNION,      /**<yang 字段专用类型 int32_t。*/
    GMC_DATATYPE_BUTT,       /**< 无效的数据类型，返回错误。 */
} GmcDataTypeE;

/**
 * @ingroup gmc_types
 * 流控等级的枚举。
 */
typedef enum {
    GMC_DB_FLOW_CTRL_LEVEL_0,    /**< 表示没有流控。 */
    GMC_DB_FLOW_CTRL_LEVEL_1,    /**< 表示一级流控等级（低）。 */
    GMC_DB_FLOW_CTRL_LEVEL_2,    /**< 表示二级流控等级（中）。 */
    GMC_DB_FLOW_CTRL_LEVEL_3,    /**< 表示三级流控等级（高）。 */
    GMC_DB_FLOW_CTRL_LEVEL_BUTT, /**< 表示无效的流控等级，返回错误。 */
} GmcDbFlowCtrlLevelE;

/**
 * @ingroup gmc_types
 * Epoll操作类型的枚举。
 */
typedef enum {
    GMC_EPOLL_ADD, /**< 添加fd。 */
    GMC_EPOLL_DEL, /**< 移除fd。 */
    GMC_EPOLL_MOD  /**< 修改fd事件。 */
} GmcEpollCtlTypeE;

/**
 * @ingroup gmc_types
 * 事务的隔离级别的枚举。
 */
typedef enum {
    GMC_TX_ISOLATION_UNCOMMITTED = 0, /**< 读未提交。 */
    GMC_TX_ISOLATION_COMMITTED,       /**< 读已提交。 */
    GMC_TX_ISOLATION_REPEATABLE,      /**< 可重复读。 */
    GMC_TX_ISOLATION_SERIALIZABLE,    /**< 可串行化。 */
    GMC_TX_ISOLATION_DEFAULT, /**< 创建Namespace: 使用服务端默认配置; 开启事务: 使用Namespace的隔离级别。 */
    GMC_TX_ISOLATION_BUTT /**< 无效的隔离级别类型，返回错误。 */
} GmcIsolationTypeE;

/**
 * @ingroup gmc_types
 * 事务类型的枚举。
 */
typedef enum {
    GMC_PESSIMISITIC_TRX = 0, /**< 悲观事务。 */
    GMC_OPTIMISTIC_TRX,       /**< 乐观事务。 */
    GMC_DEFAULT_TRX, /**< 创建Namespace: 使用服务端默认配置; 开启事务: 使用Namespace的事务类型*/
} GmcTrxTypeE;

/**
 * @ingroup gmc_types
 * 事务属性配置的结构体。
 */
typedef struct {
    GmcTrxTypeE trxType;              /**< 事务类型。 */
    GmcIsolationTypeE isolationLevel; /**< 隔离级别。 */
} GmcTrxCfgT;

/**
 * @ingroup gmc_types
 * 指定命名空间配置的结构体。
 */
typedef struct {
    const char *namespaceName;  /**< 命名空间名。 */
    const char *userName;       /**< 命名空间所属用户名。 */
    const char *tablespaceName; /**< 表空间名。 */
    GmcTrxCfgT trxCfg;          /**< 隔离级别配置。 */
} GmcNspCfgT;

/**
 * @ingroup gmc_types
 * 指定表空间配置的结构体。
 */
typedef struct {
    const char *tablespaceName; /**< 表空间名。 */
    uint16_t initSize;          /**< 表空间初始大小。 */
    uint16_t stepSize;          /**< 表空间扩展步长。 */
    uint16_t maxSize;           /**< 表空间上限。 */
} GmcTspCfgT;

// separated successful vertex dml event count
#define GMC_STATISTICS_TYPE_INSERT 0x0001
#define GMC_STATISTICS_TYPE_DELETE 0x0002
#define GMC_STATISTICS_TYPE_UPDATE 0x0004
#define GMC_STATISTICS_TYPE_REPLACE 0x0008
#define GMC_STATISTICS_TYPE_MERGE 0x0010
#define GMC_STATISTICS_TYPE_SET 0x0020
#define GMC_STATISTICS_TYPE_REMOVE 0x0040
#define GMC_STATISTICS_TYPE_TOTAL 0x007f

// separated failed vertex dml event count
#define GMC_STATISTICS_TYPE_INSERT_FAILED 0x0100
#define GMC_STATISTICS_TYPE_DELETE_FAILED 0x0200
#define GMC_STATISTICS_TYPE_UPDATE_FAILED 0x0400
#define GMC_STATISTICS_TYPE_REPLACE_FAILED 0x0800
#define GMC_STATISTICS_TYPE_MERGE_FAILED 0x1000
#define GMC_STATISTICS_TYPE_SET_FAILED 0x2000
#define GMC_STATISTICS_TYPE_REMOVE_FAILED 0x4000
#define GMC_STATISTICS_TYPE_TOTAL_FAILED 0x7f00

/**
 * @ingroup gmc_types
 * 操作类型的枚举。
 */
typedef enum {
    GMC_OPERATION_INSERT,                /**< 插入。 */
    GMC_OPERATION_INSERT_WITH_RESOURCE,  /**< 插入,带有资源表。 */
    GMC_OPERATION_REPLACE,               /**< 替换。 */
    GMC_OPERATION_REPLACE_WITH_RESOURCE, /**< 替换,带有资源表。 */
    GMC_OPERATION_REPLACE_GRAPH,         /**< 先删除顶点子树，然后再替换。 */
    GMC_OPERATION_DELETE,                /**< 删除。 */
    GMC_OPERATION_DELETE_GRAPH,          /**< 关联删除顶点，若顶点不存在则报错。 */
    GMC_OPERATION_REMOVE_GRAPH,          /**< 关联删除顶点。 */
    GMC_OPERATION_MERGE,                 /**< 合并。 */
    GMC_OPERATION_UPDATE,                /**< 更新。 */
    GMC_OPERATION_UPDATE_VERSION,        /**< 更新记录版本号。 */
    GMC_OPERATION_NONE,                  /**< 不对顶点做任何操作。用于YANG中生成diff树。 */
    GMC_OPERATION_SCAN,                  /**< 查询。 */
    GMC_OPERATION_CREATE_VERTEX_LABEL,   /**< 建立点集合。 */
    GMC_OPERATION_CREATE_EDGE_LABEL,     /**< 建立边集合。 */
    GMC_OPERATION_DROP_VERTEX_LABEL,     /**< 删除点集合。 */
    GMC_OPERATION_DROP_EDGE_LABEL,       /**< 删除边集合。 */
    GMC_OPERATION_CREATE_KV_TABLE,       /**< 建立kv表。 */
    GMC_OPERATION_DROP_KV_TABLE,         /**< 删除kv表。 */
    GMC_OPERATION_ALTER_VERTEX_LABEL,    /**< 表结构升级。 */
    GMC_OPERATION_REPLAY,                /**< DLR重演。 */
    GMC_OPERATION_SUBTREE_FILTER,        /**< YANG子树过滤。 */
    GMC_OPERATION_CHECK_REPLACE,         /**<
                                            客户端检查数据，如果数据相同，只更新记录版本号，否则replace,
                                            不建议在未开启对账的场景中使用该接口，如果新老数据一致报错误码GMERR_TABLE_NOT_IN_CHECKING，
                                            如果新老数据不一致，则该操作类型相当于GMC_OPERATION_REPLACE。
                                          */
    GMC_OPERATION_SQL_INSERT,            /** 不区分表名大小写的注入（包括时序、流计算）。 */
    GMC_OPERATION_TS_EXEC_PREPARE,       /** 时序参数化查询。 */
    GMC_OPERATION_LOAD_INDEX,            /** 外部加载索引，目前仅支持VLIVF索引 */
    GMC_OPERATION_BUTT,                  /**< 无效的操作类型，返回错误。 */
} GmcOperationTypeE;

/**
 * @ingroup gmc_types
 * 将带有范围索引的vertex扫描结果排序方向的枚举。
 */
typedef enum {
    GMC_ORDER_ASC,  /**< 升序排序。 */
    GMC_ORDER_DESC, /**< 降序排序。 */
    GMC_ORDER_BUTT, /**< 无效的排序类型，返回错误。 */
} GmcOrderDirectionE;

#ifdef ART_CONTAINER
typedef enum {
    GMC_PREFIX_SCAN, /**< 前缀匹配 */
    GMC_RANGE_SCAN,  /**< 范围查询 */
    GMC_NEAR_SCAN,   /**< 临近查询 */
    GMC_INVALID_SCAN /**< 无效查询类型 */
} GmcScanTypeE;
#endif

/**
 * @ingroup gmc_types
 * DB当前warning状态的枚举。
 */
typedef enum {
    GMC_NORMAL_STATUS = 0,        /**< 表示非warning状态。 */
    GMC_ALARM_STATUS,             /**< 表示warning状态。 */
    GMC_ALARM_SIMPLE_STATUS_BUTT, /**< 表示无效的warning状态，返回错误。 */
} GmcSimpleStatusE;

/**
 * @ingroup gmc_types
 * 客户端stmt句柄属性类型的枚举。
 */
typedef enum {
    GMC_STMT_ATTR_AFFECTED_ROWS,        /**< 操作影响记录条数。 */
    GMC_STMT_ATTR_AUTO_DELETE_EDGE,     /**< 自动删除边。 */
    GMC_STMT_ATTR_AUTO_CREATE_EDGE,     /**< 自动创建边。 */
    GMC_STMT_ATTR_PRE_FETCH_ROWS,       /**< 单个消息包中最大预取记录条数。 */
    GMC_STMT_ATTR_CACHE_ROWS,           /**< 单个消息包中实际记录条数。 */
    GMC_STMT_ATTR_DTL_ERROR_CODE,       /**< datalog请求返回的错误码。 */
    GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, /**< datalog请求返回的发生错误的表名。 */
    GMC_STMT_ATTR_USE_NEW_DESERI,       /**< 标记vertex使用新的反序列化接口。 */
    GMC_STMT_ATTR_NOT_DISTRIBUTE,       /**< 标记datalog请求不下发*/
    GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE,    /**< ts结构化写行数设定，在结构化写之前必须设置。 */
    GMC_STMT_ATTR_TS_BIND_BY_COL,       /**< ts结构化写按列方式绑定。 */
    GMC_STMT_ATTR_TS_BIND_BY_ROW,       /**< ts结构化写按行方式绑定。 */
    GMC_STMT_ATTR_RESULT_COLS,          /**< ts获取结果集列数/需要绑定的列数。 */
    GMC_STMT_ATTR_RESULT_ROWS,          /**< ts获取结果集行数。 */
    GMC_STMT_ATTR_MODEL_TYPE,           /**< 模型类型（GQL/SQL/FASTPATH/DATALOG/...）*/
    GMC_STMT_ATTR_BUTT,                 /**< 无效的stmt句柄属性类型，返回错误。 */
} GmcStmtAttrTypeE;

/**
 * @ingroup gmc_types
 * 客户端连接句柄属性类型的枚举。
 */
typedef enum {
    GMC_CONN_ATTR_INSTANCE_ID, /**< 连接的DB实例Id */
    GMC_CONN_ATTR_BUTT,        /**< 无效的conn句柄属性类型，返回错误。 */
} GmcConnAttrTypeE;

/**
 * @ingroup gmc_types
 * 订阅事件类型的枚举。
 * @attention @li 订阅关系存在GMC_SUB_EVENT_AGED或GMC_SUB_EVENT_DELETE事件时，执行对账删除，才会推送老化事件。
 * @li 如果订阅了GMC_SUB_EVENT_DELETE事件，老化操作也会推送，如果只订阅了GMC_SUB_EVENT_AGED事件，删除操作不会推送。
 */
typedef enum {
    GMC_SUB_EVENT_INSERT,                           /**< Vertex的插入事件。 */
    GMC_SUB_EVENT_DELETE,                           /**< Vertex的删除事件。 */
    GMC_SUB_EVENT_KV_REMOVE = GMC_SUB_EVENT_DELETE, /**< KV数据或Vertex的删除事件。 */
    GMC_SUB_EVENT_UPDATE,                           /**< Vertex的更新事件。 */
    GMC_SUB_EVENT_REPLACE,                          /**< Vertex的替换事件。 */
    GMC_SUB_EVENT_REPLACE_INSERT,                   /**< Vertex的替换插入事件。 */
    GMC_SUB_EVENT_REPLACE_UPDATE,                   /**< Vertex的替换更新事件。 */
    GMC_SUB_EVENT_MERGE,                            /**< Vertex的合并事件。 */
    GMC_SUB_EVENT_MERGE_INSERT,                     /**< Vertex的合并插入事件。 */
    GMC_SUB_EVENT_MERGE_UPDATE,                     /**< Vertex的合并更新事件。 */
    GMC_SUB_EVENT_KV_SET,       /**< KV数据操作事件，包括KV数据插入、KV数据更新和KV数据替换。 */
    GMC_SUB_EVENT_INITIAL_LOAD, /**< 初始化加载事件。 */
    GMC_SUB_EVENT_INITIAL_LOAD_EOF,   /**< 初始化加载结束事件。 */
    GMC_SUB_EVENT_AGED,               /**< 老化事件，预留参数，当前暂不支持。 */
    GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN, /**< 触发全量推送开始事件。 */
    GMC_SUB_EVENT_TRIGGER_SCAN,       /**< 触发全量推送事件。 */
    GMC_SUB_EVENT_TRIGGER_SCAN_END,   /**< 触发全量推送结束事件。 */
    GMC_SUB_EVENT_MODIFY,             /**< status merge专用推送事件。*/
    GMC_SUB_EVENT_DIFF,               /* *< Yang 的 report-all模式diff的事件。 */
    GMC_SUB_EVENT_DIFF_EXPLICIT,      /* *< Yang 的 explicit模式diff的事件。 */
    GMC_SUB_EVENT_BUTT,               /**< 无效的事件类型，返回错误。 */
} GmcSubEventTypeE;

/**
 * @ingroup gmc_types
 * 单表订阅中数据获取方式的枚举。
 */
typedef enum {
    GMC_SUB_FETCH_NEW,         /**< 获取推送的新数据。 */
    GMC_SUB_FETCH_OLD,         /**< 获取推送的旧数据。 */
    GMC_SUB_FETCH_KEY,         /**< 获取推送的主键值，只适用于VERTEX表。 */
    GMC_SUB_FETCH_DATA_BY_KEY, /**< 通过推送的主键值获取服务端的当前最新数据，只适用于VERTEX表。 */
    GMC_SUB_FETCH_DELTA,       /**< 获取推送的增量数据，只适用于VERTEX表。 */
    GMC_SUB_FETCH_BUTT,        /**< 无效的数据获取方式，返回错误。 */
} GmcSubFetchModeE;

/**
 * @ingroup gmc_types
 * Tree模型中的Node类型的枚举。
 */
typedef enum {
    GMC_NODE_RECORD, /**< 当前Node为一条普通记录，记录中一般包含多个属性。 */
    GMC_NODE_ARRAY, /**< 当前Node为静态数组（即fixed_array），静态数组的每个元素为一条记录。 */
    GMC_NODE_VECTOR, /**< 当前Node为动态数组（为vector或array），动态数组的每个元素为一条记录。 */
    GMC_NODE_BUTT /**< 无效的Node类型，返回错误。 */
} GmcTreeNodeTypeE;

/**
 * @ingroup gmc_types
 * batch句柄类型的枚举。
 */
typedef enum {
    GMC_BATCH_NORMAL, /**< Batch类型为普通Batch，支持批量操作的基础能力。 */
    GMC_BATCH_YANG,   /**< Batch类型为Yang，支持Yang模型批量操作。 */
    GMC_BATCH_DLR,    /**< Batch类型为DLR，支持DLR批量操作。 */
    GMC_BATCH_BUTT    /**< 无效Batch类型，返回错误。 */
} GmcBatchTypeE;

/**
 * @ingroup gmc_types
 * VertexLabel类型的枚举。
 */
typedef enum {
    GMC_VERTEX_TYPE_NORMAL,  /**< 普通表。 */
    GMC_VERTEX_TYPE_YANG,    /**< YANG表。 */
    GMC_VERTEX_TYPE_DATALOG, /**< Datalog表。 */
    GMC_VERTEX_TYPE_TS,      /**< 时序表。 */
    GMC_VERTEX_TYPE_BUTT     /**< 无效的表类型。 */
} GmcVertexLabelTypeE;

/**
 * @ingroup gmc_types
 * Datalog表类型的枚举。
 */
typedef enum {
    GMC_DTL_NORMAL,              /**< 普通表。 */
    GMC_DTL_RESOURCE_SEQUENTIAL, /**< 固定资源池资源表。 */
    GMC_DTL_RESOURCE_PUBSUB,     /**< pubsub型资源表。 */
    GMC_DTL_UPDATE,              /**< 可更新表。 */
    GMC_DTL_TRANSIENT_FIELD,     /**< transient field表。 */
    GMC_DTL_TRANSIENT_TUPLE,     /**< transient tuple表。 */
    GMC_DTL_TRANSIENT_FINISH,    /**< transient finish表。 */
    GMC_DTL_TBM,                 /**< TBM 表。 */
    GMC_DTL_MSG_NOTIFY,          /**< 消息通知表。 */
    GMC_DTL_EXTERN,              /**< 外部表。 */
    GMC_DTL_STATE,               /**< 状态表。 */
    GMC_DTL_NOTIFY,              /**< 同步订阅推送表。 */
    GMC_DTL_TYPE_BUTT            /**< 无效的表类型。 */
} GmcDtlLabelTypeE;

/**
 * @ingroup gmc_types
 * 持久化物理备份模式的枚举
 */
typedef enum {
    GMC_DATABASE_BACKUP_FULL,   /**< 全量备份模式 */
    GMC_DATABASE_BACKUP_SCHEMA, /**< 元数据备份模式 */
} GmcDatabaseBackupModeE;

// =================================struct==========================================

#define GMC_MAX_ALARM_DETAIL_LEN 256  // 最大warning详情长度

/**
 * @ingroup gmc_types
 * warning数据的结构体。
 */
typedef struct {
    GmcAlarmSrcTypeE srcType;              /**< 数据源类型 */
    GmcAlarmStatusE status;                /**< warning状态 */
    GmcSimpleStatusE alarmStatus;          /**< DB当前的warning状态 */
    double activeValue;                    /**< warning值 */
    double clearedValue;                   /**< 消除warning值 */
    double activeThreshold;                /**< warning激活阈值 */
    double clearedThreshold;               /**< warning清除阈值 */
    uint64_t succTimes;                    /**< 累计资源申请成功次数 */
    uint64_t failTimes;                    /**< 累计资源申请失败次数 */
    uint64_t userDefRscId;                 /**< 非全局告警项的location信息 */
    uint64_t alarmId;                      /**< alarmId*/
    uint64_t reserved3;                    /**< 保留字段 */
    uint64_t reserved4;                    /**< 保留字段 */
    char detail[GMC_MAX_ALARM_DETAIL_LEN]; /**< warning细节信息 */
} GmcAlarmDataT;

/**
 * @ingroup gmc_types
 * yang相关的枚举
 */
typedef enum {
    GMC_YANG_DIFF_OFF,           /**< 关闭Diff功能。 */
    GMC_YANG_DIFF_DELAY_READ_ON, /**< 从服务端延迟获取Diff Tree。 */
    GMC_YANG_DIFF_BUTT           /**< 无效Diff类型，返回错误。 */
} GmcYangDiffTypeE;

/**
 * @ingroup gmc_types
 * batch说明结构体。
 */
typedef struct {
    GmcBatchTypeE batchType;   /**< batch句柄的类型，用以判断基于该batch句柄可提供正确的操作 */
    GmcYangDiffTypeE diffType; /**< yang模型diff产生类型，默认为GMC_YANG_DIFF_OFF */
    uint32_t batchOrder;       /**< batch执行顺序 */
    uint32_t batchErrCtl;      /**< batch错误停止机制 */
    uint32_t batchRecycleSize; /**< 客户端batch报文回收大小 */
    uint32_t batchLimitSize;   /**< batch报文大小上限 */
    uint32_t maxBatchOpNum;    /**< batch操作数大小上限 */
    bool inited;               /**< 是否初始化，未初始化会报错 */
} GmcBatchOptionT;

/**
 * @ingroup gmc_types
 * batch buffer结构体。
 */
typedef struct {
    bool *batchBufValid; /**< batch buffer是否有效 */
    void *batchBuf; /**< batch buffer指针，有resource的时候指向收到的buffer，没有的时候设置为NULL, 类型为FixBuffer*指针
                     */
} GmcBatchRetT;

/**
 * @ingroup gmc_types
 * 位图数据类型的结构体。
 */
typedef struct {
    uint16_t beginPos; /**< 要更新的bitmap的起始位置 */
    uint16_t endPos;   /**< 要更新的bitmap的终止位置 */
    uint8_t *bits;     /**< bitmap[beginPos、endPos]更新value值 */
} GmcBitMapT;

/**
 * @ingroup gmc_types
 * KV元组的结构体。
 * @attention key和value的指针为任意类型指针，指针指向的有效区域大小必须和keyLen和valueLen保持一致。\n
 * key的最大长度为512字节。\n
 * value的最大长度为（pageSize-2）kB，但不能超过30kB，即使页的大小为64kB。pageSize的取值及修改方法请参见2.4.1-gmserver.ini。如果是string类型，则长度为strlen()+1。
 */
typedef struct {
    void *key;         /**< key的指针。 */
    uint32_t keyLen;   /**< key的长度。 */
    void *value;       /**< value的指针。 */
    uint32_t valueLen; /**< value的长度。 */
} GmcKvTupleT;

#define GMC_PROPERTY_NAME_MAX_LEN 128

/**
 * @ingroup gmc_types
 * 属性值的结构体。
 */
typedef struct {
    uint32_t propertyId;                          /**< 属性ID。 */
    char propertyName[GMC_PROPERTY_NAME_MAX_LEN]; /**< 属性名称，其字符串长度应不超过128个字节（包含结束符"\0"）。 */
    GmcDataTypeE type;                            /**< 数据类型，请参见GmcDataTypeE。 */
    uint32_t size;                                /**< 属性值长度。 */
    void *value;                                  /**< 属性值。 */
} GmcPropValueT;

/**
 * @ingroup gmc_types
 * 范围表达数组结构体。
 */
typedef struct {
    GmcPropValueT *lValue;    /**< 左值列表，参见GmcPropValueT。 */
    GmcPropValueT *rValue;    /**< 右值列表，参见GmcPropValueT。 */
    GmcCompareFlagE lFlag;    /**< 左比较符，参见GmcCompareFlagE。 */
    GmcCompareFlagE rFlag;    /**< 右比较符，参见GmcCompareFlagE。 */
    GmcOrderDirectionE order; /**< 结果排序的顺序，参见GmcOrderDirectionE。 */
#ifdef ART_CONTAINER
    GmcScanTypeE scanType; /**< 扫描方式：前缀、范围、临近扫描 */
    uint8_t condIdx;       /**前缀匹配的索引项 */
    uint8_t matchBytesNum; /**前缀匹配多少个字节 */
    uint8_t nearAIdx;      /**临近查询的A字段*/
    uint8_t nearBIdx;      /**临近查询的B字段，查询离A最近且B字段相同的数据*/
#endif
} GmcRangeItemT;

typedef struct {
    GmcCompareFlagE lFlag;    /**< 左比较符，参见GmcCompareFlagE。 */
    GmcCompareFlagE rFlag;    /**< 右比较符，参见GmcCompareFlagE。 */
    GmcOrderDirectionE order; /**< 结果排序的顺序，参见GmcOrderDirectionE。 */
} GmcRangeItemFlagT;

/**
 * @ingroup gmc_types
 * 资源信息。
 */
typedef struct {
    uint32_t resIdNum;  /**< 资源id个数。 */
    uint64_t *resIdBuf; /**< 资源id缓存。 */
} GmcResourceInfoT;

/**
 * @ingroup gmc_types
 * 订阅关系的配置Json的结构体。
 */
typedef struct {
    const char *subsName; /**< 订阅关系名称，其字符串长度限制为128个字节（包含结束符"\0"）。 */
    const char *configJson; /**< 订阅关系的配置json，详细说明请参见SubsInfo定义和说明。 */
} GmcSubConfigT;

/**
 * @ingroup gmc_types
 * 订阅推送消息结构体。
 */
typedef struct {
    GmcSubEventTypeE eventType; /**< 订阅事件类型，请参见GmcSubEventTypeE。 */
    uint16_t msgType; /**< 当前接收到的订阅推送消息类型，建议使用GmcSubGetMsgType获取更准确的值。 */
    uint16_t labelCount;          /**< 顶点标签个数。 */
    uint64_t serialNumber;        /**< 订阅关系推送次数。 */
    const char *subscriptionName; /**< 订阅关系名称。 */
    const char *connectionName;   /**< 连接名称。 */
    char *labelName;
} GmcSubMsgInfoT;

/**
 * @ingroup gmc_types
 * batch执行顺序的枚举。
 */
typedef enum {
    GMC_BATCH_ORDER_STRICT = 0x0, /**< batch中每个命令都是一个事务，事务失败仅回滚当前失败的命令。 */
    GMC_BATCH_ORDER_SEMI = 0x1,    /**< 按照命令添加顺序合并同一个表的命令为一个事务。 */
    GMC_BATCH_ORDER_HYBRID = 0x02, /**< 按照命令添加顺序，在服务端会进行合并。 */
    GMC_BATCH_ORDER_BUTT           /**< 用来作校验。 */
} GmcBatchOrderTypeE;

/**
 * @ingroup gmc_types
 * forceCommit类型的枚举。
 */
typedef enum {
    GMC_FORCE_COMMIT_DISABLED, /* *< 默认模式，冲突后不进行重试。 */
    GMC_FORCE_COMMIT_MODE_0, /* *< 冲突后会尝试重演，会进行冲突校验和数据校验，都成功后才提交。 */
    GMC_FORCE_COMMIT_MODE_1, /* *< 冲突后会尝试重演，不进行冲突校验，采用merge方式进行合并，会进行数据校验。 */
    GMC_FORCE_COMMIT_MODE_2, /* *< 冲突后会尝试重演，不会进行冲突校验，也不进行数据校验，直接强制提交。 */
    GMC_FORCE_COMMIT_BUTT, /* *< 无效的类型 */
} GmcForceCommitModeE;

/* *
 * @ingroup gmc_types
 * 事务属性配置的结构体。
 */
typedef struct {
    bool readOnly;                       /**< 只读事务。 */
    uint8_t transMode;                   /**< 事务模式。\n
                  GMC_TRANS_USED_IN_CS：即客户端发送消息给服务端，简称CS模式。\n
                  GMC_TRANS_USED_IN_DSTORE：即客户端直连内存，简称DS模式。
                  */
    GmcIsolationTypeE type;              /**< 隔离级别。 */
    GmcTrxTypeE trxType;                 /**< 事务类型分悲观，乐观, 或服务端指定。 */
    GmcForceCommitModeE forceCommitMode; /**< 强制提交的事务模式，默认为非强制提交。 */
} GmcTxConfigT;

/* *
 * @ingroup gmc_types
 * 结构化写入filter支持的比较运算符的枚举。
 */
typedef enum {
    GMC_OP_EQUAL = 0,                /**< 等于 */
    GMC_OP_LARGE_EQUAL,              /**< 大于等于 */
    GMC_OP_LARGE,                    /**< 大于 */
    GMC_OP_SMALL,                    /**< 小于 */
    GMC_OP_SMALL_EQUAL,              /**< 小于等于 */
    GMC_OP_IP_VECTOR_SIMILARITY,     /**< 向量IP相似性 */
    GMC_OP_L2_VECTOR_SIMILARITY,     /**< 向量L2相似性 */
    GMC_OP_COSINE_VECTOR_SIMILARITY, /**< 向量Cosine相似性 */
    GMC_OP_INVALID                   /* 最大值报错 */
} GmcFilterCompSymbolE;

/* *
 * @ingroup gmc_types
 * 结构化写入filter的结构体。
 */
typedef struct {
    uint32_t fieldId;     /**< 属性顺序号，从0开始 */
    const char *nodeName; /**< 节点名称 */
    int32_t compOp;       /**< 比较运算符，当前支持比较运算符有：=、>=、>、<、<= */
    void *value;          /**< 指向输入值的指针，不允许为NULL值 */
    uint32_t valueLen;    /**< 输入value指针的长度，必须和实际的value长度一致，不允许为0 */
} GmcFilterStructT;

typedef const void *GmcMemInitArgs;
typedef const void *GmcMemAllocArgs;
typedef void *GmcMemFreePtr;
typedef const void *GmcMemFreeArgs;
typedef const void *GmcMemFinalizeArgs;

typedef void *GmcAdptFuncsHandle;

/* resource pool default value */
#define AUTO_POOL_ID (uint64_t)0xFFFF
#define AUTO_START_IDX (uint64_t)0xFFFFFFFF

/* label type */
#define GMC_LABEL_TYPE_VERTEX 1u
#define GMC_LABEL_TYPE_KV 2u

#define GMC_FULL_TABLE 0xff

// ==============================function pointer===========================
/**
 * @ingroup gmc_types
 * @par 描述：
 * 异步batch支持所有操作回调函数原型，预留接口，暂时不被调用，功能暂未支持。
 * @param[in] userData 用户data。
 * @param[in] totalNum 批量操作执行的总操作数。
 * @param[in] successNum 批量操作执行成功的个数。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcBatchContinuousDoneT)(
    void *userData, uint32_t totalNum, uint32_t successNum, int32_t *status, const char **errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcBatchExecuteAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] batchRet 用于解析batch操作结果，参见GmcBatchRetT。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcBatchDoneT)(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 连接的流控等级的回调函数。
 * @param[in] args APP回调函数参数。
 * @param[out] flowCtrlLevel 连接的流控等级。
 * @return 无。
 */
typedef void (*GmcConnFlowCtrlLevelNoticeT)(void *args, GmcDbFlowCtrlLevelE flowCtrlLevel);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcKvCreateTableAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcCreateKvTableDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcCreateNamespaceAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcCreateNamespaceDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * Tablespace异步接口的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcTablespaceDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcCreateVertexLabelAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcCreateVertexLabelDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 异步执行DML操作接口GmcExecuteAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcDeleteVertexDoneT)(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcDropNamespaceAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcDropNamespaceDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcClearNamespaceAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcClearNamespaceDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcTruncateNamespaceAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcTruncateNamespaceDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * Epoll注册函数原型。
 * @param[in] fd 文件描述符fd。
 * @param[in] type Epoll操作类型，参考GmcEpollCtlTypeE。
 * @return 无。
 */
typedef int32_t (*GmcEpollRegT)(int32_t fd, GmcEpollCtlTypeE type);

/**
 * @ingroup gmc_types
 * @par 描述：
 * Epoll注册函数原型。
 * @param[in] fd 文件描述符fd。
 * @param[in] type Epoll操作类型，参考GmcEpollCtlTypeE。
 * @param[in] events epoll响应事件。
 * @param[in] usrData 用户数据。
 * @return 无。
 */
typedef int32_t (*GmcEpollRegWithUserDataT)(int32_t fd, GmcEpollCtlTypeE type, uint32_t events, void *userData);

/**
 * @ingroup gmc_types
 * @par 描述：
 * Epoll注册函数原型。
 * @param[in] fd 文件描述符fd。
 * @param[in] type Epoll操作类型，参考GmcEpollCtlTypeE。
 * @param[in] events epoll响应事件。
 * @param[in] conn 连接句柄，DB内部将连接句柄传回epoll注册函数后，用户可以将连接句柄挂在epoll_event上。
 * @param[in] usrData 用户数据。
 * @return 无。
 */
typedef int32_t (*GmcEpollRegWithConnT)(
    int32_t fd, GmcEpollCtlTypeE type, uint32_t events, const GmcConnT *conn, void *userData);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 异步执行DML操作接口GmcExecuteAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcInsertVertexDoneT)(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 异步执行DML操作接口GmcExecuteAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] resInfo 资源信息，参见GmcResourceInfoT。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcInsertVertexWithResourceDoneT)(
    void *userData, uint32_t affectedRows, GmcResourceInfoT *resInfo, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcKvRemoveAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcKvRemoveDoneT)(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcKvSetAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcKvSetDoneT)(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 申请内存函数类型指针。
 * @param[in] size 内存申请大小。
 * @param[in] args 内存申请参数。
 * @return 无。
 */
typedef void *(*GmcMemAllocFuncT)(uint32_t size, GmcMemAllocArgs args);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 回收内存函数类型指针。
 * @param[in] args 内存回收参数。
 * @return 无。
 */
typedef int32_t (*GmcMemFinalizeFuncT)(GmcMemFinalizeArgs args);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 释放内存函数类型指针。
 * @param[in] ptr 内存释放指针。
 * @param[in] args 内存释放参数。
 * @return 无。
 */
typedef void (*GmcMemFreeFuncT)(GmcMemFreePtr ptr, GmcMemFreeArgs args);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 初始化内存函数类型指针。
 * @param[in] args 内存初始化参数。
 * @return 无。
 */
typedef int32_t (*GmcMemInitFuncT)(GmcMemInitArgs args);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 执行GmcRegAdaptFuncs设置的用户写日志回调函数原型，兼容v3。
 * @param[in] handle 日志句柄。
 * @param[in] level 客户端日志告警级别。
 * @param[in] errCode 错误码字符串。
 * @param[in] logBuf 日志字符串。
 * @return void。
 */
typedef void (*GmcLogWriteFuncT)(void *handle, uint32_t level, const char *errCode, const char *logBuf);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 异步执行DML操作接口GmcExecuteAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcMergeVertexDoneT)(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 异步执行DML操作接口GmcExecuteAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcReplaceVertexDoneT)(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 异步执行DML操作接口GmcExecuteAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] resInfo 资源信息，参见GmcResourceInfoT。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcReplaceVertexWithResourceDoneT)(
    void *userData, uint32_t affectedRows, GmcResourceInfoT *resInfo, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 订阅接口GmcSubscribe的回调函数。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] Info 订阅推送信息，参见GmcSubMsgInfoT。
 * @param[in] userdata 用户data。
 * @return 无。
 */
typedef void (*GmcSubCallbackT)(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userdata);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 订阅推送异常的回调函数。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] userdata 用户data。
 * @return 无。
 */
typedef void (*GmcSubFailedCallbackT)(GmcStmtT *stmt, void *userdata);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcKvTruncateTableAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcTruncateKvTableDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcTruncateVertexLabelAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcTruncateVertexLabelDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcDeleteAllFastAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcDeleteAllFastDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 异步执行DML操作接口GmcExecuteAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] affectedRows 操作影响记录条数。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcUpdateVertexDoneT)(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcUseNamespaceAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcUseNamespaceDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcKvDropTableAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcDropKvTableDoneT)(void *userData, int32_t status, const char *errMsg);
/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcCreateEdgeLabelAsync的用户回调函数原型。
 * @param[in] userData 用户数据。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcCreateEdgeLabelDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcDropVertexLabelAsync的用户回调函数原型。
 * @param[in] userData 用户data。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcDropVertexLabelDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcDropEdgeLabelAsync的用户回调函数原型。
 * @param[in] userData 用户数据。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcDropEdgeLabelDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcSetCheckReplaceCmpFunc的用户回调函数原型。
 * @param[in] readStmt 直连读拿出来的数据存放的临时stmt句柄。
 * @attention @li 入参readStmt为只读stmt,
 * 当前只支持GmcGetVertexPropertySizeByName、GmcGetVertexPropertySizeById、
 * GmcGetVertexPropertyByName、GmcGetVertexPropertyById 四个接口操作该stmt，其余接口操作该stmt会造成未定义结果。
 * @param[in] userData 用户传入的用于比较的数据。
 * @return 比较结果。
 */
typedef bool (*GmcCheckReplaceCmpFuncT)(GmcStmtT *readStmt, void *userData);

/**
 * @ingroup gmc_types
 * 异步DML操作回调函数结构体。
 */
typedef struct {
    union {
        GmcInsertVertexDoneT insertCb;                         /**< 用户回调函数GmcInsertVertexDoneT。 */
        GmcInsertVertexWithResourceDoneT insertWithResourceCb; /**< 用户回调函数GmcInsertVertexWithResourceDoneT。 */
        GmcMergeVertexDoneT mergeCb;                           /**< 用户回调函数GmcMergeVertexDoneT。 */
        GmcReplaceVertexDoneT replaceCb;                       /**< 用户回调函数GmcReplaceVertexDoneT。 */
        GmcReplaceVertexWithResourceDoneT replaceWithResourceCb; /**< 用户回调函数GmcReplaceVertexWithResourceDoneT。 */
        GmcDeleteVertexDoneT deleteCb;                           /**< 用户回调函数GmcDeleteVertexDoneT。 */
        GmcUpdateVertexDoneT updateCb;                           /**< 用户回调函数GmcUpdateVertexDoneT。 */
        GmcCreateVertexLabelDoneT createVertexWithNameCb; /**< 用户回调函数GmcCreateVertexLabelWithNameAsync。 */
    };
    void *userData;
} GmcAsyncRequestDoneContextT;

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcTransStartAsync、GmcTransCommitAsync、GmcTransRollBackAsync的用户回调函数原型。
 * @param[in] userData 用户数据。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcTransDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * 内存适配函数的结构体。
 */
typedef struct {
    GmcMemInitFuncT memInitFunc;         /**< 初始化内存函数，请参见GmcMemInitFuncT。 */
    GmcMemAllocFuncT memAllocFunc;       /**< 申请内存函数，请参见GmcMemAllocFuncT。 */
    GmcMemFreeFuncT memFreeFunc;         /**< 释放内存函数，请参见GmcMemFreeFuncT。 */
    GmcMemFinalizeFuncT memFinalizeFunc; /**< 回收内存函数，请参见GmcMemFinalizeFuncT。 */
    uint32_t maxSingleStepAllocSize;     /**< 用户指定的内存申请函数单次申请的上限。 */
} GmcMemAdptFuncsT;

/**
 * @ingroup gmc_types
 * 用户注册的日志函数结构体。
 */
typedef struct {
    GmcLogWriteFuncT userWriteFunc; /**< 日志写函数，通过用户注册传入，请参见 GmcLogWriteFuncT 。 */
    void *handle;                   /**< 日志handle，通过用户注册传入，类型固定为void *。 */
} GmcLogAdptFuncsT;

/**
 * @ingroup gmc_types
 * 序列化算法的枚举。
 */
typedef enum {
    GMC_SERI_VERSION_DEFAULT = 0x0, /**< DM序列化算法（默认算法）。 */
    GMC_SERI_VERSION_INVALID        /**< 无效的序列化算法。 */
} GmcDmSeriVerE;

/**
 * @ingroup gmc_types
 * 预留字段结构体。
 */
typedef struct {
    uint32_t fixedPropResvSize; /**< 定长属性预留长度 */
    uint32_t propertyAddNum;    /**< 添加的属性数量 */
} GmcStructureResvT;

/**
 * @par 描述：序列化回调函数原型。
 * @param[in] seri 输入参数结构体
 * @param[in] destBuf 目的buffer的地址
 * @param[in] reservedSize 预留字段结构体。
 * @return @li STATUS_OK @li 其他返回值，请参见“错误码参考”章节。
 */
typedef int32_t (*GmcSeriFuncDoneT)(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize);

/**
 * @par 描述：反序列化回调函数原型。
 * @param[in] seri 反序列化结构体
 * @param[in] srcBuf 序列化buffer的地址。
 * @param[in] srcLen srcBuf的大小。
 * @param[in] reservedSize 预留字段结构体。
 * @return @li STATUS_OK @li 其他返回值，请参见“错误码参考”章节。
 */
typedef int32_t (*GmcDeseriFuncDoneT)(
    void *deSeri, const uint8_t *srcBuf, uint32_t srcLen, GmcStructureResvT *reservedSize);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 获取元数据缓存的用户回调函数原型。
 * @param[out] userData 用户数据
 * @param[out] status 服务端处理结果。
 * @param[out] errMsg 错误消息，GMERR_OK时为NULL。
 * @param[out] errIndex 出错的元素下标。
 * @return 无。
 */
typedef void (*GmcPrefetchLabelsDoneT)(void *userData, int32_t status, const char *errMsg, uint32_t errIndex);

/**
 * @ingroup gmc_types
 * 结构化执行序列化参数的结构体。
 */
typedef struct {
    GmcSeriFuncDoneT seriFunc;  // 回调函数
    GmcDmSeriVerE version;      // 使用的序列化算法种类
    uint8_t *obj;               // 序列化vertex使用的buffer
    uint32_t bufSize;           // 用户计算出来的序列化之后需要buf大小
    void *userData;             // 用户实现的序列化算法中需要使用的入参
} GmcSeriT;

/**
 * @ingroup gmc_types
 * 结构化执行反序列化参数的结构体。
 */
typedef struct {
    GmcDeseriFuncDoneT deseriFunc;  // 回调函数
    uint8_t *obj;                   // 反序列化使用的结构体
    uint32_t objSize;               // 反序列化使用的结构体大小
    GmcDmSeriVerE version;          // 使用的序列化算法种类
    void *userData;                 // 用户实现的序列化算法中需要使用的入参
} GmcDeseriT;

/**
 * @ingroup gmc_types
 * 结构化输入左右键结构体。
 */
typedef struct {
    uint32_t keyId;         /**< Keyid，输入索引的编号。 */
    GmcSeriT *leftKeySeri;  /**< 左key，参见GmcSeriT。 */
    GmcSeriT *rightKeySeri; /**< 右key，参见GmcSeriT。 */
} GmcRangeKeySeriT;

/**
 * @ingroup gmc_types
 * 输入buf的信息结构体。
 */
typedef struct {
    uint8_t *buf;    /**< buf，用户输入的缓存指针。 */
    uint32_t bufLen; /**< bufLen，用户希望拷贝的buffer长度。 */
    uint32_t vertexOffset; /**< vertexOffset，orm生成的vertex头长度。 代码中已不在使用，可适配用例一起删除*/
} GmcStructBufferT;

/**
 * @ingroup gmc_types
 * 节点名和属性顺序号的结构体。
 */
typedef struct {
    const char *nodeName; /**< 节点名。 */
    uint32_t propId;      /**< 属性顺序号 */
} GmcNodeNameAndPropIdT;

/**
 * @ingroup gmc_types
 * 索引名和索引属性顺序号的结构体。
 */
typedef struct {
    const char *keyName; /**< 索引名。 */
    uint32_t order;      /**< 索引属性顺序号 */
} GmcKeyNameAndOrderT;

/**
 * @ingroup gmc_types
 * @par 描述：
 * 启动客户端保护线程时，所设置的用户回调函数。返回值等于GMERR_OK时，保护线程继续进行信号等待，线程不退出；
 * 返回值不等于GMERR_OK时，保护线程退出。
 * @attention
 * 建议用户在此回调函数中对特定的sigNo返回值非GMERR_OK，即提供一种保护线程的退出机制。
 * @param[in] sigNo 客户端保护线程接收到的信号值。
 * @return @li 该返回值用户根据需要自行定义。
 */
typedef int32_t (*GmcProtectThreadCrashHandlerT)(int sigNo);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 启动客户端保护线程的设置参数。
 */
typedef struct {
    void *sigMask;                              /**< 用户设置的允许接收信号，应使用sigset_t定义。 */
    GmcProtectThreadCrashHandlerT crashHandler; /**< 用户设置的回调函数，需要与GmcProtectThreadCrashHandlerT函数原型。
                                                 */
} GmcProtectThreadArgsT;

/**
 * @ingroup gmc_types
 * gmserver.ini文件中配置项的具体信息的枚举。
 */
typedef enum GmcCfgInfo {
    GMC_CFG_INFO_TYPE_NAME = 0,          /**< 配置项名称 */
    GMC_CFG_INFO_TYPE_DESC = 1,          /**< 该配置项的描述信息 */
    GMC_CFG_INFO_TYPE_CUR_VALUE = 2,     /**< 该配置项的当前值 */
    GMC_CFG_INFO_TYPE_MIN_VALUE = 3,     /**< 该配置项的最小范围值 */
    GMC_CFG_INFO_TYPE_MAX_VAULE = 4,     /**< 该配置项的最大范围值 */
    GMC_CFG_INFO_TYPE_DEFAULT_VALUE = 5, /**< 该配置项的默认值 */
    GMC_CFG_INFO_TYPE_CHANGE_MODE = 6,   /**< 该配置项允许的修改模式。 */
    GMC_CFG_INFO_TYPE_DATA_TYPE = 7,     /**< 该配置项值的数据类型。 */
} GmcCfgInfoTypeE;

typedef enum GmcLabelType {
    GMC_VERTEX_LABEL_TYPE = 0, /**< VERTEX_LABEL */
    GMC_EDGE_LABEL_TYPE = 1,   /**< EDGE_LABEL */
} GmcLabelTypeE;

#define USER_INFO_LEN 128

/**
 * @brief PSSP透传过来的结构体，用于存储信号相关的信息
 * @since Yunshan V100R024C00
 */
typedef struct {
    int32_t signalNum;  // 信号类型
    int32_t reserved;   // 保留字
    void *sigInfo;      // linux系统中记录信号上下文内容的结构体指针siginfo_t
    void *signalCon;    // linux系统中记录当前寄存器和栈信息等内容的结构体指针
} GmcSignalInfoT;

/**
 * @brief 信号的出参，用于存储DB逻辑处理后的输出数据
 * @since Yunshan V100R024C00
 */
typedef struct {
    uint32_t policy;      // 返回给信号函数的处理策略
    uint32_t rebootCode;  // 重启错误码
    uint64_t reserved;
    char userInfo[USER_INFO_LEN];  // 输出给平台的dfx字符串
} GmcSignalOutputDataT;

/**
 * @ingroup gmc_types
 * 向量相似度类型的枚举。
 */
typedef enum {
    GMC_VECTOR_METRIC_IP = 0, /**< 点积相似度。 */
    GMC_VECTOR_METRIC_L2,     /**< L2相似度。 */
    GMC_VECTOR_METRIC_COSINE, /**< 余弦相似度。 */
    GMC_VECTOR_METRIC_BUTT,   /**< 无效的相似度度量算法，返回错误。 */
} GmcVectorMetricE;

/**
 * @brief Gmc接口导出二进制文件接口的入参
 */
typedef struct {
    const char *exportDirPath;  // 导出文件存放的路径, 最大255字符，必须绝对路径，不超过10层
    const char **tablesName;    // 要导出的指定表
    uint32_t tableNum;          // 表数量
    uint32_t threadNum;         // 线程数
    uint32_t timeoutMs;         // 超时时间
} GmcExportParaT;

#ifdef __cplusplus
}
#endif

#endif /* GMC_TYPES_H */
