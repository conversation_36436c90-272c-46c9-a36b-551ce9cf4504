/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: gmc_vector.h
 * Description: File provide GMC APIs relate to vector module.
 * Author:
 * Create: 2025-05-15
 */

#ifndef GMC_VECTOR_H
#define GMC_VECTOR_H

#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @defgroup gmc_vector 向量接口
 */

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 获取指定字段的向量与用户输入的向量的相似性。
 *  @li
 * 请勿随意更改入参stmt结构，以GMDB接口构造为准, 其安全性由调用者保证。
 * @param[in] stmt 节点句柄。
 * @param[in] propName 属性名称。
 * @param[in] queryVec 查询向量，由用户输入。
 *  @li 当propName不是量化属性字段时，其长度需要与属性长度保持一致；
 *  @li 当propName是量化属性字段时，其长度需要与该属性的量化source属性长度一致。
 * @param[in] metric 向量相似度算法类型。
 * @param[out] dis 获取到的两个向量的相似度。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetVecSimilarity(
    GmcStmtT *stmt, const char *propName, float *queryVec, float *dis, GmcVectorMetricE metric);

#ifdef __cplusplus
}
#endif

#endif /* GMC_VECTOR_H */
