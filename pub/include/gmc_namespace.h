/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_namespace.h
 * Description: File provide GMC APIs related to respool.
 *              Function order: DDL\DML\other func.
 *              Please keep new function in its corresponding position.
 * Author:
 * Create: 2021-10-23
 */

#ifndef GMC_NAMESPACE_H
#define GMC_NAMESPACE_H

#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @defgroup gmc_namespace 客户端接口
 */

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 同步创建命名空间（Namespace），命名空间作为一种元数据信息，需要保存在Catalog中。不同命名空间中的对象可以使用相同的名字，通过Schema和name保证其对象的唯一性。
 * @attention @li 当前命名空间中包含以下几种元数据类型：\n
 *                  点标签：VertexLabel\n
 *                  边标签：EdgeLabel\n
 *                  表级订阅：LabelSubscription\n
 *                  资源池：ResourcePool\n
 *                  KV表：KvTable\n
 *  @li 边标签上的命名空间必须与出点标签和入点标签上的命名空间保持一致。\n
 *  @li 一个订阅路径上必须与该路径上所有标签的命名空间保持一致。\n
 *  @li 不同的Namespace之间，不支持建边标签和订阅操作。\n
 *  @li 不支持基于边的跨命名空间关联查询。\n
 *  @li Namespace中不支持嵌套定义Namespace。\n
 *  @li 一个Namespace通常属于一个DB，而一个DB中可以有多个Namespace（Namespace个数最多不超过64个）。\n
 *  @li
 * 服务器启动成功后，会自动创建一个全局KV表（表名为T_GMDB）；此外，每新创建一个NameSpace时，也会在该NameSpace下自动创建一个全局KV表（表名为T_GMDB）。\n
 *  @li 同一个NameSpace下，KV表与vertexLabel不能同名。\n
 *  @li 用户可以指定资源池ID，但必须保证不同命名空间下的同名资源池的ID不同。\n
 *  @li 由于当前含有资源列的表无法删除，因此创建了该表的命名空间也无法删除。\n
 *  @li 资源池和要绑定的资源表必须在同一个命名空间下。\n
 *  @li 要扩展的资源池和扩展后的资源池必须在同一个命名空间下。\n
 *  @li 目前一个stmt中可以跨命名空间访问标签。
 *  @li 入参stmt不能随意更改结构，以GMDB接口构造为准。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName
 * 待创建的命名空间名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。其命名规则请参见Name命名规则。\n
 * 说明：\n
 *          不能使用系统默认的命名空间名称system、sysview或者public。\n
 *          命名空间名称的长度应不超过128字节（包含结束符"\0"）。\n
 *          当前不支持“namespace.label”的写法来操作该namespace下的label。
 * @param[in] userName
 * 该命名空间所属的用户名，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出，或者为null。\n 说明：\n
 *          userName的长度应不超过32字节。\n
 *          如果指定了userName，表示为该指定用户创建命名空间。\n
 *          如果不指定userName，表示为当前连接用户创建命名空间。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcCreateNamespace(GmcStmtT *stmt, const char *namespaceName, const char *userName);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 同步创建命名空间（Namespace）并指定命名空间的事务隔离类型，命名空间作为一种元数据信息，需要保存在Catalog中。不同命名空间中的对象可以使用相同的名字，通过Schema和name保证其对象的唯一性。
 * @attention @li 当前命名空间中包含以下几种元数据类型：\n
 *                  点标签：VertexLabel\n
 *                  边标签：EdgeLabel\n
 *                  表级订阅：LabelSubscription\n
 *                  资源池：ResourcePool\n
 *                  KV表：KvTable\n
 *  @li 边标签上的命名空间必须与出点标签和入点标签上的命名空间保持一致。\n
 *  @li 一个订阅路径上必须与该路径上所有标签的命名空间保持一致。\n
 *  @li 不同的Namespace之间，不支持建边标签和订阅操作。\n
 *  @li 不支持基于边的跨命名空间关联查询。\n
 *  @li Namespace中不支持嵌套定义Namespace。\n
 *  @li 一个Namespace通常属于一个DB，而一个DB中可以有多个Namespace（Namespace个数最多不超过64个）。\n
 *  @li
 * 服务器启动成功后，会自动创建一个全局KV表（表名为T_GMDB）；此外，每新创建一个NameSpace时，也会在该NameSpace下自动创建一个全局KV表（表名为T_GMDB）。\n
 *  @li 同一个NameSpace下，KV表与vertexLabel不能同名。\n
 *  @li 用户可以指定资源池ID，但必须保证不同命名空间下的同名资源池的ID不同。\n
 *  @li 由于当前含有资源列的表无法删除，因此创建了该表的命名空间也无法删除。\n
 *  @li 资源池和要绑定的资源表必须在同一个命名空间下。\n
 *  @li 要扩展的资源池和扩展后的资源池必须在同一个命名空间下。\n
 *  @li 目前一个stmt中可以跨命名空间访问标签。
 *  @li 入参stmt不能随意更改结构，以GMDB接口构造为准。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] nspCfg.namespaceName
 * 待创建的命名空间名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。其命名规则请参见Name命名规则。\n
 * 说明：\n
 *          不能使用系统默认的命名空间名称system、sysview或者public。\n
 *          命名空间名称的长度应不超过128字节（包含结束符"\0"）。\n
 *          当前不支持“namespace.label”的写法来操作该namespace下的label。
 * @param[in] nspCfg.tablespaceName
 * 该命名空间待绑定的表空间名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出，或者为null。\n
 * 说明：\n
 *          tablespaceName的长度不应超过128字节。\n
 *          该表空间必须是GmcCreateTablespace、GmcCreateTablespaceAsync之前已经创建好的Tablespace，如果使用不存在的表空间则报错。\n
 *          如未指定表空间，则默认绑定到public。\n
 * @param[in] nspCfg.userName
 * 该命名空间所属的用户名，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出，或者为null。\n 说明：\n
 *          userName的长度应不超过32字节。\n
 *          如果指定了userName，表示为该指定用户创建命名空间。\n
 *          如果不指定userName，表示为当前连接用户创建命名空间。
 * @param[in] nspCfg.trxCfg
 * 该命名空间下表的事务隔离级别，目前支持 GMC_TX_ISOLATION_COMMITTED + GMC_PESSIMISITIC_TRX、
 *          GMC_TX_ISOLATION_REPEATABLE + GMC_OPTIMISTIC_TRX、 GMC_TX_ISOLATION_SERIALIZABLE + GMC_PESSIMISITIC_TRX
 * 三种组合；
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcCreateNamespaceWithCfg(GmcStmtT *stmt, GmcNspCfgT *nspCfg);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 异步创建命名空间（Namespace），命名空间作为一种元数据信息，需要保存在Catalog中。不同命名空间中的对象可以使用相同的名字，通过Schema和name保证其对象的唯一性。
 * @attention @li 当前命名空间中包含以下几种元数据类型：\n
 *              点标签：VertexLabel\n
 *              边标签：EdgeLabel\n
 *              表级订阅：LabelSubscription\n
 *              资源池：ResourcePool\n
 *              KV表：KvTable\n
 *  @li 边标签上的命名空间必须与出点标签和入点标签上的命名空间保持一致。\n
 *  @li 一个订阅路径上必须与该路径上所有标签的命名空间保持一致。\n
 *  @li 不同的Namespace之间，不支持建边标签和订阅操作。\n
 *  @li 不支持基于边的跨命名空间关联查询。\n
 *  @li Namespace中不支持嵌套定义Namespace。\n
 *  @li 一个Namespace通常属于一个DB，而一个DB中可以有多个Namespace（Namespace个数最多不超过64个）。\n
 *  @li
 * 服务器启动成功后，会自动创建一个全局KV表（表名为T_GMDB）；此外，每新创建一个NameSpace时，也会在该NameSpace下自动创建一个全局KV表（表名为T_GMDB）。\n
 *  @li 同一个NameSpace下，KV表与vertexLabel不能同名。\n
 *  @li 用户可以指定资源池ID，但必须保证不同命名空间下的同名资源池的ID不同。\n
 *  @li 由于当前含有资源列的表无法删除，因此创建了该表的命名空间也无法删除。\n
 *  @li 资源池和要绑定的资源表必须在同一个命名空间下。\n
 *  @li 要扩展的资源池和扩展后的资源池必须在同一个命名空间下。\n
 *  @li 目前一个stmt中可以跨命名空间访问标签。
 *  @li
 * 入参stmt不能随意更改结构，以GMDB接口构造为准，入参userCb需严格按照3.11.4.5-GmcCreateNamespaceDoneT结构体及函数定义构造，入参安全性均由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName
 * 待创建的命名空间名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。其命名规则请参见Name命名规则。\n
 * 说明：\n
 *                  不能使用系统默认的命名空间名称system、sysview或者public。\n
 *                  命名空间名称的长度应不超过128字节（包含结束符"\0"）。\n
 *                  当前不支持“namespace.label”的写法来操作该namespace下的label。
 * @param[in] userName
 * 该命名空间所属的用户名，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出，或者为null。\n 说明：\n
 *                  userName的长度应不超过32字节。\n
 *                  如果指定了userName，表示为该指定用户创建命名空间。\n
 *                  如果不指定userName，表示为当前连接用户创建命名空间。
 * @param[in] userCb 用户的回调函数，参考GmcCreateNamespaceDoneT。
 * @param[in] userData 用户data。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcCreateNamespaceAsync(
    GmcStmtT *stmt, const char *namespaceName, const char *userName, GmcCreateNamespaceDoneT userCb, void *userData);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 异步创建命名空间（Namespace）并指定命名空间的事务隔离类型，同时支持将命名空间绑定到特定表空间，命名空间作为一种元数据信息，需要保存在Catalog中。\n
 * 不同命名空间中的对象可以使用相同的名字，通过Schema和name保证其对象的唯一性。\n
 * @attention @li 当前命名空间中包含以下几种元数据类型：\n
 *              点标签：VertexLabel\n
 *              边标签：EdgeLabel\n
 *              表级订阅：LabelSubscription\n
 *              资源池：ResourcePool\n
 *              KV表：KvTable\n
 *  @li 边标签上的命名空间必须与出点标签和入点标签上的命名空间保持一致。\n
 *  @li 一个订阅路径上必须与该路径上所有标签的命名空间保持一致。\n
 *  @li 不同的Namespace之间，不支持建边标签和订阅操作。\n
 *  @li 不支持基于边的跨命名空间关联查询。\n
 *  @li Namespace中不支持嵌套定义Namespace。\n
 *  @li 一个Namespace通常属于一个DB，而一个DB中可以有多个Namespace（Namespace个数最多不超过64个）。\n
 *  @li
 * 服务器启动成功后，会自动创建一个全局KV表（表名为T_GMDB）；此外，每新创建一个NameSpace时，也会在该NameSpace下自动创建一个全局KV表（表名为T_GMDB）。\n
 *  @li 同一个NameSpace下，KV表与vertexLabel不能同名。\n
 *  @li 用户可以指定资源池ID，但必须保证不同命名空间下的同名资源池的ID不同。\n
 *  @li 由于当前含有资源列的表无法删除，因此创建了该表的命名空间也无法删除。\n
 *  @li 资源池和要绑定的资源表必须在同一个命名空间下。\n
 *  @li 要扩展的资源池和扩展后的资源池必须在同一个命名空间下。\n
 *  @li 目前一个stmt中可以跨命名空间访问标签。
 *  @li
 * 入参stmt不能随意更改结构，以GMDB接口构造为准，入参userCb需严格按照3.11.4.5-GmcCreateNamespaceDoneT结构体及函数定义构造，入参安全性均由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] nspCfg.namespaceName
 * 待创建的命名空间名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。其命名规则请参见Name命名规则。\n
 * 说明：\n
 *                  不能使用系统默认的命名空间名称system、sysview或者public。\n
 *                  命名空间名称的长度应不超过128字节（包含结束符"\0"）。\n
 *                  当前不支持“namespace.label”的写法来操作该namespace下的label。
 * @param[in] nspCfg.userName
 * 该命名空间所属的用户名，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出，或者为null。\n 说明：\n
 *                  userName的长度应不超过32字节。\n
 *                  如果指定了userName，表示为该指定用户创建命名空间。\n
 *                  如果不指定userName，表示为当前连接用户创建命名空间。
 * @param[in] nspCfg.tablespaceName
 * 该命名空间待绑定的表空间名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出，或者为null。\n
 * 说明：\n
 *                  tablespaceName的长度不应超过128字节。\n
 *                  该表空间必须是GmcCreateTablespace、GmcCreateTablespaceAsync之前已经创建好的Tablespace，如果使用不存在的表空间则报错。\n
 *                  如未指定表空间，则默认绑定到public。\n
 * @param[in] nspCfg.trxCfg
 * 该命名空间下表的事务隔离级别，目前支持 GMC_TX_ISOLATION_COMMITTED + GMC_PESSIMISITIC_TRX、
 *          GMC_TX_ISOLATION_REPEATABLE + GMC_OPTIMISTIC_TRX、 GMC_TX_ISOLATION_SERIALIZABLE + GMC_PESSIMISITIC_TRX
 * 三种组合；
 * @param[in] userCb 用户的回调函数，参考GmcCreateNamespaceDoneT。
 * @param[in] userData 用户data。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcCreateNamespaceWithCfgAsync(
    GmcStmtT *stmt, GmcNspCfgT *nspCfg, GmcCreateNamespaceDoneT userCb, void *userData);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 在删除对应命名空间内的资源后，同步删除命名空间。
 * @attention @li 当前不支持Namespace中元数据的级联删除。\n
 *  @li 由于当前含有资源列的表无法删除，因此创建了该表的命名空间也无法删除。
 *  @li 入参stmt不能随意更改结构，以GMDB接口构造为准。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName
 * 待删除的命名空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。\n 说明：\n
 *                  不能删除系统默认的命名空间名称system、sysview和public，否则提示错误。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcDropNamespace(GmcStmtT *stmt, const char *namespaceName);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 在删除对应命名空间内的资源后，异步删除命名空间。
 * @attention @li 当前不支持Namespace中元数据的级联删除。\n
 *  @li 由于当前含有资源列的表无法删除，因此创建了该表的命名空间也无法删除。
 *  @li
 * 入参stmt不能随意更改结构，以GMDB接口构造为准，入参userCb需严格按照3.11.4.10-GmcDropNamespaceDoneT结构体及函数定义构造，入参安全性均由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName 待删除的命名空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * 说明：\n
 *       不能删除系统默认的命名空间名称system、sysview和public，否则提示错误。
 * @param[in] userCb 用户的回调函数，参考GmcDropNamespaceDoneT。
 * @param[in] userData 用户data。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcDropNamespaceAsync(
    GmcStmtT *stmt, const char *namespaceName, GmcDropNamespaceDoneT userCb, void *userData);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 异步清除命名空间下所有表数据，但是保留表定义。
 * @attention @li 当前不支持Namespace中元数据的级联删除。\n
 *  @li
 * 入参stmt不能随意更改结构，以GMDB接口构造为准，入参userCb需严格按照GmcTruncateNamespaceDoneT结构体及函数定义构造，入参安全性均由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName
 * 待删除数据的命名空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。 说明：\n
 *       不能删除系统默认的命名空间名称system、sysview和public，否则提示错误。
 * @param[in] userCb 用户的回调函数，参考GmcTruncateNamespaceDoneT。
 * @param[in] userData 用户data。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTruncateNamespaceAsync(
    GmcStmtT *stmt, const char *namespaceName, GmcTruncateNamespaceDoneT userCb, void *userData);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 同步清除命名空间下所有表数据以及表定义。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName
 * 待清理的命名空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。 说明：\n
 *       不能清理系统默认的命名空间名称system、sysview和public，否则提示错误。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcClearNamespace(GmcStmtT *stmt, const char *namespaceName);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * 异步清除命名空间下所有表数据以及表定义。
 * @attention @li 当前不支持Namespace中元数据的级联删除。\n
 *  @li
 * 入参stmt不能随意更改结构，以GMDB接口构造为准，入参userCb需严格按照GmcClearNamespaceDoneT结构体及函数定义构造，入参安全性均由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName
 * 待清理的命名空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。 说明：\n
 *       不能删除系统默认的命名空间名称system、sysview和public，否则提示错误。
 * @param[in] userCb 用户的回调函数，参考GmcClearNamespaceDoneT。
 * @param[in] userData 用户data。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcClearNamespaceAsync(
    GmcStmtT *stmt, const char *namespaceName, GmcClearNamespaceDoneT userCb, void *userData);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * （同步操作）通过namespaceName切换当前连接使用的目标命名空间。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName 命名空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。\n
 * 说明：\n
 *          该命名空间必须是GmcCreateNamespace之前已经创建好的Namespace，如果使用不存在的命名空间则报错。\n
 *          如果不指定命名空间，则使用系统默认的命名空间public。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcUseNamespace(GmcStmtT *stmt, const char *namespaceName);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * （异步操作）通过namespaceName切换当前连接使用的目标命名空间。
 * @attention
 * 请勿随意更改入参stmt结构，以GMDB接口构造为准。入参userCb需严格按照GmcUseNamespaceDoneT结构体及函数定义构造，上述入参安全性均由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName 命名空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。\n
 * 说明：\n
 *          该命名空间必须是GmcCreateNamespace之前已经创建好的Namespace，如果使用不存在的命名空间则报错。\n
 *          如果不指定命名空间，则使用系统默认的命名空间public。
 * @param[in] userCb 用户的回调函数，参考GmcUseNamespaceDoneT。
 * @param[in] userData 用户data。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcUseNamespaceAsync(
    GmcStmtT *stmt, const char *namespaceName, GmcUseNamespaceDoneT userCb, void *userData);

/**
 * @ingroup gmc_namespace
 * @par 描述：
 * （异步操作）绑定namespace到特定的tablespace。
 * @attention @li
 * 请勿随意更改入参stmt结构，以GMDB接口构造为准。入参userCb需严格按照GmcTablespaceDoneT结构体及函数定义构造，上述入参安全性均由调用者保证。\n
 * @li Namespace中必须为空，即不允许存在其他资源。\n
 * @li 对于同一个Namespace，不支持切换绑定到当前绑定的Tablespace。\n
 * @li 不支持将系统默认的命名空间system、sysview或者public绑定到Tablespace。\n
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] namespaceName 命名空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。\n
 * 说明：该命名空间必须是GmcCreateNamespace之前已经创建好的Namespace，如果使用不存在的命名空间则报错。\n
 * @param[in] tablespaceName 表空间的名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。\n
 * 说明：该表空间必须是GmcCreateTablespace之前已经创建好的Tablespace，如果使用不存在的表空间则报错。\n
 * @param[in] userCb 用户的回调函数，参考GmcTablespaceDoneT。
 * @param[in] userData 用户data。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcBindNamespaceToTableSpaceAsync(
    GmcStmtT *stmt, const char *namespaceName, const char *tablespaceName, GmcTablespaceDoneT userCb, void *userData);

#ifdef __cplusplus
}
#endif

#endif /* GMC_NAMESPACE_H */
