/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gme_awareness.h
 * Description: Provide gme awareness api
 * Author: liufuchenxing
 * Create: 2024-03-12
 */

#ifndef GME_AWARENESS_H
#define GME_AWARENESS_H

#include "gme_api.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef int32_t (*GmeAwarenessSubFunT)(
    const uint8_t *data, uint32_t size);  // Register func to subscribe awareness update

/**
 * @ingroup gme_awareness
 * @brief write awareness log to db.
 * @param[in] conn Connection which handles this operation.
 * @param[in] data awareness data.
 * @param[in] size awareness data size.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeAwarenessWriteLog(GmeConnT *conn, const uint8_t *data, uint32_t size);

/**
 * @ingroup gme_awareness
 * @brief subscribe awareness update data.
 * @param[in] conn Connection which handles this operation.
 * @param[in] func awareness subscribe function.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeAwarenessSubscribe(GmeConnT *conn, GmeAwarenessSubFunT func);

#ifdef __cplusplus
}
#endif

#endif /* GME_AWARENESS_H */
