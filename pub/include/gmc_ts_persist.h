/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: Interfaces for ts persistence
 */

#ifndef GMC_TS_PERSIST_H
#define GMC_TS_PERSIST_H

#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @ingroup gmc
 * @par 描述：
 * 校验持久化文件中的数据
 * @attention 以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端句柄
 * @param[in] ctrlFilePath 持久化控制文件所在路径，长度不超过200
 * @param[in] tempFilePath 临时文件所在路径，长度不超过200
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSwapDataDir(GmcStmtT *stmt, const char *ctrlFilePath, const char *tempFilePath);

#ifdef __cplusplus
}
#endif

#endif /* GMC_TS_PERSIST_H */
