/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Interfaces for user designated function registration in server process.
 * Author: zhouhaonan
 * Create: 2023-08-07
 */

#ifndef GMS_H
#define GMS_H

#include <stdbool.h>
#include <inttypes.h>
#include <stdio.h>
#include <sys/shm.h>
#include <pthread.h>
#include "gm_adpt.h"

#ifdef __cplusplus
extern "C" {
#endif

#if defined(__GNUC__)
#define GMS_EXPORT __attribute__((visibility("default")))
#else
#define GMS_EXPORT
#endif

/**
 * @defgroup gms 服务端接口
 */

/**
 * @ingroup gms
 * @par 描述：
 * 拉起服务端。
 * @attention argc和argv参数要保证合法，传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[in] argc 入参的个数。
 * @param[in] argv 保存入参字符串的数组。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMS_EXPORT int32_t GmsServerMain(int32_t argc, char *argv[]);

/**
 * @ingroup gms
 * @par 描述：
 * 清理当前共进程下gmserver实例的共享内存。
 * @attention 退出前调用该接口，调用完该接口后server不可用;
 * @attention 清理共享内存操作需确保无客户端对共享内存进行引用，如仍有客户端引用则则会清理失败
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMS_EXPORT int32_t GmsShmemClear(void);

/**
 * @ingroup gms
 * @par 描述：
 * 查询系统三级hung结果
 * @attention 传入的参数的个数和实际的参数个数要对应，其安全性由调用者保证。
 * @param[out] handle 出参，保存hung线程id，返回值为GMERR_OK时，该值有效。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMS_EXPORT int32_t GmsGetHungResult(GmsThreadHandle *handle);

#ifdef __cplusplus
}
#endif

#endif /* GMS_H */
