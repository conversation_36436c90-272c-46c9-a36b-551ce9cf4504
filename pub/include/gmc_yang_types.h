
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: source file for yang types
 * Author: chengzhangpei
 * Create: 2022-08-01
 */

#ifndef GMC_YANG_TYPES_H
#define GMC_YANG_TYPES_H

#include <inttypes.h>
#include <stdbool.h>
#include "gmc_errno.h"
#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/* subtree filter */
#define GMC_LOCATION_FILTER 0x01  // list位置过滤

// Yang校验类型
#define GMC_YANG_VALIDATION_MANDATORY 0x0001  // mandatory 校验，被校验数据节点非法则报错。
#define GMC_YANG_VALIDATION_WHEN 0x0002  // when 校验，被校验数据节点自身变更引起数据非法则报错，否则非法数据则被删除。
#define GMC_YANG_VALIDATION_WHEN_FORCE 0x0004  // when 校验增强，被校验数据节点非法则删除。
#define GMC_YANG_VALIDATION_MUST 0x0008        // must 校验，被校验数据节点非法则报错。
#define GMC_YANG_VALIDATION_LEAFREF 0x0010     // leafref 校验，被校验数据节点非法则报错。
#define GMC_YANG_VALIDATION_MIN 0x0020         // min-elements 校验，被校验数据节点非法则报错。
#define GMC_YANG_VALIDATION_MAX 0x0040         // max-elements 校验，被校验数据节点非法则报错。
#define GMC_YANG_VALIDATION_UNDEFINED 0x0080   // 校验类型最大值。
// 校验所有语句（when采用常规校验方式）
#define GMC_YANG_VALIDATION_ALL (0x7F ^ GMC_YANG_VALIDATION_WHEN_FORCE)
// 校验所有语句（when校验失败的节点直接删除）
#define GMC_YANG_VALIDATION_ALL_FORCE (0x7F ^ GMC_YANG_VALIDATION_WHEN)

/**
 * @ingroup gmc_types
 * YANG数据合法性校验配置参数。
 */
typedef struct {
    uint32_t type;        // 校验类型
                          /*
                           * GMC_YANG_VALIDATION_WHEN，when校验，被校验数据节点自身变更引起数据非法则报错，否则被删除。
                           * GMC_YANG_VALIDATION_WHEN_FORCE，when校验增强，
                           * GMC_YANG_VALIDATION_WHEN不同的是：只要被校验数据节点非法则被删除。GMC_YANG_VALIDATION_WHEN和
                           * 和GMC_YANG_VALIDATION_WHEN_FORCE只选一种方式即可，若同时选择则执行GMC_YANG_VALIDATION_WHEN_FORCE
                           * GMC_YANG_VALIDATION_MUST，must 校验，被校验数据节点非法则报错。
                           * GMC_YANG_VALIDATION_LEAFREF，leafref 校验，被校验数据节点非法则报错。
                           * GMC_YANG_VALIDATION_MANDATORY，mandatory 校验，被校验数据节点非法则报错。
                           * GMC_YANG_VALIDATION_MIN，min-elements 校验，被校验数据节点非法则报错。
                           * GMC_YANG_VALIDATION_MAX，max-elements 校验，被校验数据节点非法则报错。
                           * type可以同时选择多种类型校验，比如 (GMC_YANG_VALIDATION_WHEN_FORCE | GMC_YANG_VALIDATION_MUST)
                           */
    const char *cfgJson;  // 校验参数，json字符串。最多支持1个参数，如 R"({"name":
                          // "running"})"，且仅支持string，uint32，bool和double4种类型
} GmcValidateConfigT;

typedef struct GmcYangTreeT GmcYangTreeT; /**< yang tree 结果树实体 */
typedef struct GmcYangNodeT GmcYangNodeT; /**< yang tree 遍历中间结构体 */

typedef struct GmcFetchRetT GmcFetchRetT; /**< yang  diff 或subtree 查询结果结构体 */

typedef struct GmcGetLeafrefPathOptionsT GmcGetLeafrefPathOptionsT; /**< yang 获取leafref引用选项结构体 */

typedef struct GmcSubtreeOptionT GmcSubtreeOptionT; /**< yang 获取subtree查询选项结构体 */

typedef struct GmcFetchDiffOptionsT GmcFetchDiffOptionT; /**< yang fetchdiff 选项结构体 */

typedef enum GmcGetLeafrefPathModeE {
    GMC_LEAFREF_GET_PATH_ALL = 0,
    GMC_LEAFREF_GET_PATH_ONE = 1,
    GMC_LEAFREF_GET_PATH_BUTT
} GmcGetLeafrefPathModeE;

/* subtree 配置属性查询时对于默认值的处理模式 */
typedef enum {
    GMC_SUBTREE_FILTER_DEFAULT = 0,  // 默认模式，同时查询配置和状态节点
    GMC_SUBTREE_FILTER_CONFIG = 1,   // 查询配置节点
    GMC_SUBTREE_FILTER_STATE = 2,    // 查询状态节点
    GMC_SUBTREE_FILTER_BUTT          // 无效值，返回错误
} GmcSubtreeWithConfigModeE;

/**
 * @ingroup gmc_types
 * Yang字段五原语操作类型的枚举。
 */
typedef enum {
    GMC_YANG_PROPERTY_OPERATION_NONE,   /**< 字段五原语初始值 */
    GMC_YANG_PROPERTY_OPERATION_CREATE, /**< 创建字段值，字段有值则报错。 */
    GMC_YANG_PROPERTY_OPERATION_MERGE,  /**< 修改字段值，不存在则创建，存在则更新 */
    GMC_YANG_PROPERTY_OPERATION_REPLACE, /**< 修改字段值, 不存在则创建，存在则替换（将已存在的数据删除） */
    GMC_YANG_PROPERTY_OPERATION_DELETE, /**< 删除字段值，字段无值则报错。 */
    GMC_YANG_PROPERTY_OPERATION_REMOVE, /**< 删除字段值。 */
    GMC_YANG_PROPERTY_OPERATION_BUTT    /**< 无效的操作类型，返回错误。 */
} GmcYangPropOpTypeE;

/**
 * @ingroup gmc_types
 * Yang 默认值校验的结果枚举。
 */
typedef enum {
    GMC_YANG_PROPERTY_WITH_DEFAULT_NONE,      /* *< 该字段未定义默认值 */
    GMC_YANG_PROPERTY_WITH_DEFAULT_EQUAL,     /* *< 输入值和默认值相等。 */
    GMC_YANG_PROPERTY_WITH_DEFAULT_NOT_EQUAL, /* *< 输入值和默认值不相等 */
    GMC_YANG_PROPERTY_WITH_DEFAULT_BUTT       /* *< 无效的类型，返回错误。 */
} GmcYangPropDefaultStateE;

/**
 * @ingroup gmc_types
 * Yang List操作位置类型枚举。
 */
typedef enum {
    GMC_YANG_LIST_POSITION_FIRST,  /**< 移动到List头部 */
    GMC_YANG_LIST_POSITION_LAST,   /**< 移动到List尾部 */
    GMC_YANG_LIST_POSITION_REMAIN, /**< 停留在当前位置 */
    GMC_YANG_LIST_POSITION_BEFORE, /**< 某个位置之前 */
    GMC_YANG_LIST_POSITION_AFTER,  /**< 某个位置之后 */
    GMC_YANG_LIST_POSITION_BUTT    /**< 无效的位置类型，返回错误。 */
} GmcYangListPositionE;

typedef struct {
    GmcYangListPositionE position; /**< list位置描述。 */
    GmcPropValueT **refKeyFields;  /**< 参考点字段数组。 */
    uint32_t refKeyFieldsCount;    /**< 参考字段数。 */
} GmcYangListLocatorT;

// 和服务端QryErrorPathCodeE一一对应
typedef enum {
    GMC_VIOLATES_UNIQUE = 0,              // list元素不满足唯一性
    GMC_VIOLATES_INSERT_PARAMETER,        // list元素移动位置操作参数有误
    GMC_VIOLATES_CREATE,                  // 原语操作create创建的元素已存在
    GMC_VIOLATES_DELETE,                  // 原语操作delete删除不存在的元素
    GMC_VIOLATES_MANDATORY_LEAF,          // mandatory为true的leaf对象不存在
    GMC_VIOLATES_MANDATORY_CHOICE,        // mandatory为true的choice对象不存在
    GMC_VIOLATES_MIN_ELEMENTS,            // leaflist实例小于数组最少元素限制
    GMC_VIOLATES_MAX_ELEMENTS,            // leaflist实例大于数组最多元素限制
    GMC_VIOLATES_NAMESPACE_EXISTS,        // namespace已存在
    GMC_VIOLATES_NAMESPACE_NOT_EXISTS,    // namespace不存在
    GMC_VIOLATES_NAMESPACE_INVALID_NAME,  // 无效namespace名字
    GMC_VIOLATES_NAMESPACE_NOT_EMPTY,     // 删除非空namespace
    GMC_VIOLATES_WHEN,                    // 不满足when约束
    GMC_VIOLATES_MUST,                    // 不满足must约束
    GMC_VIOLATES_LEAFREF,                 // 不满足LEAFREF约束
    GMC_VIOLATES_BUTT,
} GmcErrorPathCodeE;

typedef struct {
    bool validateRes;    // 校验结果，true表示全部成功，false表示有失败
    uint32_t failCount;  // 模型校验时表示失败的count数目，数据校验时暂未使用
} GmcValidateResT;

// yang error-path 信息
typedef struct {
    GmcErrorPathCodeE errorCode;
    uint32_t errorClauseIndex;
    const char *errorMsg;
    const char *errorPath;
} GmcErrorPathInfoT;

// 仅用于 choice/case 路径补全场景，表示所要操作的是 case 下的字段还是 node 还是 vertex
typedef enum {
    GMC_YANG_TYPE_FIELD,   // 表示操作 case 下的字段
    GMC_YANG_TYPE_NODE,    // 表示操作 case 下的 node
    GMC_YANG_TYPE_VERTEX,  // 表示操作 case 下的 vertex
} GmcYangChildElementTypeE;

// 仅用于 choice/case 路径补全场景，存放缺失的 choice/case 路径
typedef struct GmcLostPathInfo {
    char *path;             /**< 获取缺失的choice/case路径信息。 */
    uint32_t totalPathLen;  /**< path内存的总长度。 */
    uint32_t actualPathLen; /**< 缺失的choice/case路径实际长度。 */
} GmcLostPathInfoT;

typedef enum {
    GMC_YANG_FIELD,     /**< 代表yang tree信息类型为属性信息 */
    GMC_YANG_CONTAINER, /**< 代表yang tree信息类型为vertex信息，包括container、choice和case */
    GMC_YANG_LIST,      /**< 代表yang tree信息类型为list vertex信息 */
    GMC_YANG_CHOICE,    /**< 代表yang tree信息类型为choice信息 */
    GMC_YANG_CASE,      /**< 代表yang tree信息类型为case信息 */
    GMC_YANG_LEAFLIST,  /**< 代表yang tree信息类型为leaf list信息 */
} GmcYangNodeTypeE;

typedef enum {
    GMC_DIFF_OP_INVALID, /**< 代表转换出错的情况 */
    GMC_DIFF_OP_UPDATE,  /**< 代表节点变更类型为update */
    GMC_DIFF_OP_REMOVE,  /**< 代表节点变更类型为remove */
    GMC_DIFF_OP_CREATE,  /**< 代表节点变更类型为create */
} GmcDiffOpTypeE;

/* subtree 查询时对于默认值的处理模式 */
typedef enum {
    GMC_DEFAULT_FILTER_EXPLICIT = 0,  // explicit模式：去除非用户设置的字段
    GMC_DEFAULT_FILTER_TRIM = 1,      // trim模式：去除所有等于默认值的字段
    GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED = 2,  // report_all_tagged模式：保留所有有效字段，非用户设置的字段会附加tag标记
    GMC_DEFAULT_FILTER_REPORT_ALL = 3,  // report_all模式：保留所有有效字段，非用户设置的字段不会附加tag标记
    GMC_DEFAULT_FILTER_BUTT  // 无效的defaltMode，返回错误
} GmcSubtreeWithDefaultModeE;

// GmcYangConvert接口的参数
typedef enum {
    GMC_CONVERT_INT_TO_STR = 0,  // 用于枚举和identity的value转换成key
    GMC_CONVERT_STR_TO_INT,      // 用于枚举和identity的key转换成value
    GMC_CONVERT_BUTT
} GmcConvertTypeE;

// 修改时需要同步修改 DmSubtreeFetchModeE
typedef enum {
    GMC_FETCH_JSON = 0,  // 通过json设置条件
    GMC_FETCH_OBJ,       // 通过编辑查询树结构设置条件
    GMC_FETCH_FULL_JSON,  // 全量查询当前数据集数据，返回json形式的数据，该模式下不用设置查询条件
    GMC_FETCH_FULL_OBJ,  // 全量查询当前数据集数据，返回OBJ形式数据，该模式下不用设置查询条件
    GMC_FETCH_MODEL,         // 查询YangTreeschema模型
    GMC_FETCH_MODEL_LIST,    // 查询所有根节点名字
    GMC_FETCH_JSON_RFC7951,  // 通过编辑查询树结构设置条件，返回RFC7951格式编码的subtree字符串
    GMC_FETCH_FULL_JSON_RFC7951,  // 全量查询当前数据集数据，返回RFC7951格式编码的subtree字符串
    GMC_FETCH_BUTT,               // 无效的filter mode，返回错误
} GmcFetchModeE;

typedef enum GmcFetchDiffModeE {
    GMC_FETCH_DIFF_REPORT_ALL = 0,  // report all 模式查询diff
    GMC_FETCH_DIFF_EXPLICIT         // explicit 模式查询diff，只查询用户编辑的diff
} GmcFetchDiffModeE;

typedef enum {
    GMC_ATTRIBUTE_NAME = 0,  // enum或identity类型的name
    GMC_ATTRIBUTE_VALUE,     // enum或identity类型的value
    GMC_ATTRIBUTE_BUTT,
} GmcAttributeTypeE;

typedef struct {
    GmcAttributeTypeE type;
    uint32_t size;  // enum或identity类型，设置值的长度
    void *value;    // enum或identity类型，设置的值
} GmcAttributePropertyT;

typedef struct GmcSubtreeFilterItem {
    const char *rootName; /* *< subtree对应的根节点名字，仅在 json 过滤模式下使用，obj 过滤模式下必须写 NULL。 */
    union {
        const char *json;          /* *< 如果是json过滤模式，指针类型为 const char* */
        const GmcNodeT *obj;       /* *< 如果是obj过滤模式，指针类型为 GmcNodeT* */
    } subtree;                     /* *< 存放子树过滤的过滤条件 */
    uint32_t jsonFlag;             /* *< 指定json的返回格式 */
    uint32_t maxDepth : 8;         /* *< 子树过滤最大深度，默认为0 */
    uint32_t isLocationFilter : 1; /* *< 是否是位置查询，使用宏 GMC_LOCATION_FILTER 设值 */
    uint32_t defaultMode : 3;      /* *< 默认值过滤模式，使用枚举设置值 GmcSubtreeWithDefaultModeE */
    uint32_t configFlag : 2;       /* *< 配置属性查询标志，使用枚举设置值 GmcSubtreeWithConfigModeE */
    uint32_t reserved : 18;        /* *< 预留 */
    struct GmcSubtreeFilterItem *next; /* *< 下一棵查询树 */
} GmcSubtreeFilterItemT;

/**
 * @ingroup gmc_types
 * subtree过滤函数的subtree相关结构体。
 */
typedef struct {
    uint32_t filterMode;            // 过滤模式，参考 GmcFetchModeE
    GmcSubtreeFilterItemT *filter;  // 过滤树链表头
} GmcSubtreeFilterT;

typedef struct {
    const char *name;  /**< 属性名称，其字符串长度应不超过128个字节（包含结束符"\0"）。 */
    GmcDataTypeE type; /**< 数据类型，请参见GmcDataTypeE。 */
    uint32_t size;     /**< 属性值长度。 */
    const void *value; /**< 属性值。 */
    bool isDefault; /**< 该属性是否来源于默认值。仅在GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED模式下有意义。 */
} GmcYangNodeValueT;

typedef enum {
    GMC_PERSISTENCE_MODE_YANG = 0,  // Yang导入导出
    GMC_PERSISTENCE_MODE_BUTT,      // 无效的导入导出模式
} GmcPersistenceModeE;

typedef enum {
    GMC_DIGEST_ALGORITHM_NONE = 0,  // 不做可靠性性检验
    GMC_DIGEST_ALGORITHM_CRC,       // CRC检验可靠性
    GMC_DIGEST_ALGORITHM_HMAC,      // HMAC检验可靠性
} GmcDigestAlgorithmE;

typedef struct {
    GmcPersistenceModeE mode;       // 持久化模式，当前只支持YANG
    GmcDigestAlgorithmE algorithm;  // 可靠性检验算法
    const char *dir;                // 持久化目录
    const char *password;           // HMAC算法用户key
} GmcPersistenceConfigT;

typedef struct GmcLeafrefPath {
    bool isEnd;
    int8_t reserved[3];  // 字节对齐
    uint32_t num;
    const char **paths;
} GmcLeafrefPathT;

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcYangValidateAsync的用户回调函数原型。
 * @param[in] userData 用户数据。
 * @param[in] validateRes 校验结果。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * diff 或subtree 的用户回调函数原型。
 * @param[in] userData 数据。
 * @param[in] fetchRet 服务端返回的查询结果句柄。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 * @warning 如果fetchRet是最后一批获取结果或者不再作为subtree、diff的查询的输入条件时，用户必须调用 GmcYangFreeFetchRet
 * 释放资源。
 */
typedef void (*GmcYangFetchExecuteDoneT)(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcYangImportDataAsync的用户回调函数原型。
 * @param[in] userData 用户数据。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcImportDataDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcYangExportDataAsync的用户回调函数原型。
 * @param[in] userData 用户数据。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcExportDataDoneT)(void *userData, int32_t status, const char *errMsg);

/**
 * @ingroup gmc_types
 * @par 描述：
 * 接口GmcYangGetLeafrefPathAsync的用户回调函数原型。
 * @param[in] userData 用户数据。
 * @param[in] leafrefPathes 返回的引用对象。
 * @param[in] status 服务器端操作处理结果。
 * @param[in] errMsg 错误信息。
 * @return 无。
 */
typedef void (*GmcGetLeafrefDataDoneT)(
    void *userData, GmcLeafrefPathT leafrefPathes, int32_t status, const char *errMsg);
#ifdef __cplusplus
}
#endif

#endif /* GMC_YANG_TYPES_H */
