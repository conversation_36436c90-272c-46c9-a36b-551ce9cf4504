/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: gmc.h
 * Description: File include all GMC APIs. You should not add function in this file.
 * client API function header comment format:
 *     @par 描述： Briefly describe the function    // Capitalize the first letter
 *     @param para1: briefly describe para1
 *     @param para2: briefly describe para1
 *     @param para3: briefly describe para1 (output parameter)    // output parameter should be marked
 *     @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 * Author:
 * Create: 2020-7-31
 */

#ifndef GMC_H
#define GMC_H

#include "gmc_batch.h"
#include "gmc_connection.h"
#include "gmc_graph.h"
#include "gmc_kv.h"
#include "gmc_namespace.h"
#include "gmc_respool.h"
#include "gmc_subscription.h"
#include "gmc_tree.h"
#include "gmc_types.h"
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @defgroup gmc 客户端接口
 */

/**
 * @ingroup gmc
 * @par 描述：
 * 根据告警ID从服务端获取告警数据。
 * @attention
 * 请勿随意更改入参stmt结构，以GMDB接口构造为准，入参alarmData必须以GmcAlarmDataT结构体的数据结构为准，请勿随意更改其结构，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。\n
 * 说明：\n
 *  该stmt只能是同步stmt，如果是异步stmt，则提示错误。
 * @param[in] alarmId 告警ID，请参见GmcAlarmIdE。
 * @param[out] alarmData 获取到的告警数据，数据结构请参见GmcAlarmDataT。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetAlarmData(GmcStmtT *stmt, GmcAlarmIdE alarmId, GmcAlarmDataT *alarmData);

/**
 * @ingroup gmc
 * @par 描述：
 * 用于在判断操作失败后获取其详细错误信息。
 * @attention 当前只支持获取最后一次错误操作的详细信息。
 * @return @li 错误信息字符串（保证非NULL），最大长度（包括结束符）为1024字节。
 */
GMC_EXPORT const char *GmcGetLastError(void);

/**
 * @ingroup gmc
 * @par 描述：
 * 在GmcFetch后调用此接口，根据查询属性序列号依次获取特定查询属性的值。
 * @attention
 * 请勿随意更改入参stmt结构，以GMDB接口构造为准，propValue大小与propSize需要保证对应关系准确，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] id 查询属性序列号，从0开始。
 * @param[out] propValue 获取到的查询属性值地址。
 * @param propSize [in/out] 入参为查询属性值的大小，出参为实际属性的字节大小。\n
 *                         如果用户传入参数大小小于实际属性大小，则报错，并返回实际长度，用户需要重新申请空间获取属性值。
 * @param[out] isNull 查询属性值是否为空。\n
 *                  @li true：是\n
 *                  @li false：否
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetPropertyById(
    const GmcStmtT *stmt, uint32_t id, void *propValue, uint32_t *propSize, bool *isNull);

/**
 * @ingroup gmc
 * @par 描述：
 * 初始化数据库对象，使GmcConnect可创建连接，如果参数是一个空值，则该函数初始化客户端框架、子模块且分配memctx资源。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcInit(void);

/**
 * @ingroup gmc_types
 * 适配类型的枚举。
 */
typedef enum {
    GMC_ADPT_MEMORY, /**< 内存操作函数。 */
    GMC_ADPT_LOG,    /**< 日志写函数。 */
    GMC_ADPT_BUTT,   /**< 无效的适配类型，返回错误。 */
} GmcAdptTypeE;

/**
 * @ingroup gmc
 * @par 描述：
 * 在客户端初始化前，提供用户适配的函数。
 * @attention 请勿随意更改入参adaptFuncs结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] adaptType 适配类型，请参见GmcAdptTypeE。
 * @param[in] adaptFuncs 适配函数，请参见GmcMemAdptFuncsT或者GmcLogAdptFuncsT结构体。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */

GMC_EXPORT int32_t GmcRegAdaptFuncs(GmcAdptTypeE adaptType, GmcAdptFuncsHandle adaptFuncs);

/**
 * @ingroup gmc
 * @par 描述：
 * 在调用GmcConnect建连之前注册异步超时epoll函数，通过pthread_create函数创建多线程，客户端调用该接口将客户端超时定时器fd注册到用户epoll对象。
 * @attention @li 此接口仅在异步连接的场景下使用。
 * @li 请勿随意更改入参epollReg结构，需严格按照GmcEpollRegT函数定义构造，其安全性由调用者保证。
 * @param[in] epollReg epoll注册函数。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcRegTimeoutEpollFunc(GmcEpollRegT epollReg);

/**
 * @ingroup gmc
 * @par 描述：
 * 在调用GmcConnect建连之前注册异步超时epoll函数，通过pthread_create函数创建多线程，客户端调用该接口将客户端超时定时器fd注册到用户epoll对象,可传入用户数据
 * @attention @li 此接口仅在异步连接的场景下使用。
 * @li 请勿随意更改入参epollReg结构，需严格按照GmcEpollRegWithUserDataT函数定义构造，其安全性由调用者保证。
 * @param[in] epollReg epoll注册函数。
 * @param[in] userData 用户数据，通常为epollFd。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcRegTimeoutEpollFuncWithUserData(GmcEpollRegWithUserDataT epollReg, void *userData);

/**
 * @ingroup gmc
 * @par 描述：
 * 在调用GmcConnect建连之前，注册心跳epoll函数。客户端通过调用该接口将客户端心跳定时器fd注册到用户epoll对象，注册完成后，后续建立的连接均启动心跳功能。
 * @attention @li 此接口支持在同步连接、异步连接、订阅连接的场景下使用。
 * @li 此接口不可重复执行，如果重复操作会返回错误码GMERR_FEATURE_NOT_SUPPORTED。
 * @li 请勿随意更改入参epollReg结构，需严格按照GmcEpollRegT函数定义构造，其安全性由调用者保证。
 * @param[in] epollReg epoll注册函数。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcRegHeartBeatEpollFunc(GmcEpollRegT epollReg);

/**
 * @ingroup gmc
 * @par 描述：
 * 在调用GmcConnect建连之前，注册心跳epoll函数。客户端通过调用该接口将客户端心跳定时器fd注册到用户epoll对象，注册完成后，后续建立的连接均启动心跳功能。
 * @attention @li 此接口支持在同步连接、异步连接、订阅连接的场景下使用。
 * @li 此接口不可重复执行，如果重复操作会返回错误码GMERR_FEATURE_NOT_SUPPORTED。
 * @li 请勿随意更改入参epollReg结构，需严格按照GmcEpollRegWithUserDataT函数定义构造，其安全性由调用者保证。
 * @param[in] epollReg epoll注册函数。
 * @param[in] userData 用户数据，通常为epollFd。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcRegHeartBeatEpollFuncWithUserData(GmcEpollRegWithUserDataT epollReg, void *userData);

/**
 * @ingroup gmc
 * @par 描述：
 * 支持批量DML操作执行后，提交事务。
 * @attention 请勿随意更改入参conn结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] conn 连接句柄。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransCommit(GmcConnT *conn);

/**
 * @ingroup gmc
 * @par 描述：
 * 回滚事务。
 * @attention @li 事务开启之后，如果执行失败，则需要用户手动执行回滚操作。\n
 * @li 长事务回滚较大数据量时，会阻塞客户端同步请求。例如，单个事务写入大量数据时，可能会因内存不足而触发事务回滚，\n
 *        回滚耗时较久超过60秒，导致服务端一直没有给客户端回消息，客户端的同步接口长时间读不到响应，返回17004的错误码值。
 * @li 请勿随意更改入参conn结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] conn 连接句柄。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransRollBack(GmcConnT *conn);

/**
 * @ingroup gmc
 * @par 描述：
 * 开启事务。
 * @attention @li
 * 当前事务不支持GmcOperationTypeE中的GMC_OPERATION_INSERT_WITH_RESOURCE、GMC_OPERATION_REPLACE_WITH_RESOURCE、
 *   GMC_OPERATION_UPDATE_VERSION操作类型。
 *   @li 当前不支持事务查询操作。
 *   @li 事务开启之后，如果执行失败，则需要用户手动执行回滚操作。
 *   @li 长事务回滚较大数据量时，会阻塞客户端同步请求。例如，单个事务写入大量数据时，可能会因内存不足而触发事务回滚，
 *        回滚耗时较久超过60秒，导致服务端一直没有给客户端回消息，客户端的同步接口长时间读不到响应，返回17004的错误码值。
 *   @li 请勿随意更改入参conn结构，以GMDB接口构造为准，config需严格按照GmcTxConfigT结构体构造，其安全性由调用者保证。
 * @param[in] conn 连接句柄。
 * @param[in] config 对事务的属性配置，请参见GmcTxConfigT。\n
 *            此参数注意，当config->trxType被赋值为GMC_DEFAULT_TRX 时，说明采取默认namespace的隔离级别设置。
 * 说明：\n
 * 如果config参数传入值为NULL，表示默认读写事务。此外，由于锁池只有1k个entry且不支持扩展，故所有未提交的事务涉及的顶点不能超过1k，否则DML执行过程会报错。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransStart(GmcConnT *conn, const GmcTxConfigT *config);

/**
 * @ingroup gmc
 * @par 描述：
 * 异步开启事务。
 * @attention @li
 * 当前事务不支持GmcOperationTypeE中的GMC_OPERATION_INSERT_WITH_RESOURCE、GMC_OPERATION_REPLACE_WITH_RESOURCE、
 *   GMC_OPERATION_UPDATE_VERSION操作类型。
     @li 异步开启事务只支持cs模式
 *   @li 当前不支持事务查询操作。
 *   @li 事务开启之后，如果执行失败，则需要用户手动执行回滚操作。
 *   @li 长事务回滚较大数据量时，会阻塞客户端同步请求。例如，单个事务写入大量数据时，可能会因内存不足而触发事务回滚，
 * 回滚耗时较久超过60秒，导致服务端一直没有给客户端回消息，客户端的同步接口长时间读不到响应，返回17004的错误码值。
 * @param[in] conn 连接句柄。
 * @param[in] config 对事务的属性配置，请参见GmcTxConfigT。\n
 * 说明：\n
 * 如果config参数传入值为NULL，表示默认读写事务。此外，由于锁池只有1k个entry且不支持扩展，故所有未提交的事务涉及的顶点不能超过1k，否则DML执行过程会报错。
 * @param[in] userCb 用户的回调函数，参考GmcTransDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransStartAsync(GmcConnT *conn, const GmcTxConfigT *config, GmcTransDoneT userCb, void *userData);

/**
 * @ingroup gmc
 * @par 描述：
 * 异步提交事务。
 * @attention @li 异步提交事务仅支持CS模式
 * @param[in] conn 连接句柄。
 * @param[in] userCb 用户的回调函数，参考GmcTransDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransCommitAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData);

/**
 * @ingroup gmc
 * @par 描述：
 * 异步回滚事务。
 * @attention @li 事务开启之后，如果执行失败，则需要用户手动执行回滚操作。\n
 *   @li 长事务回滚较大数据量时，会阻塞客户端同步请求。例如，单个事务写入大量数据时，可能会因内存不足而触发事务回滚，\n
 *        回滚耗时较久超过60秒，导致服务端一直没有给客户端回消息，客户端的同步接口长时间读不到响应，返回17004的错误码值。
 *   @li 异步回滚事务仅支持CS模式
 * @param[in] conn 连接句柄。
 * @param[in] userCb 用户的回调函数，参考GmcTransDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransRollBackAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData);

/**
 * @ingroup gmc
 * @par 描述：savepoint是在数据库事务处理中实现“子事务”（subtransaction），也称为嵌套事务（英语：nested transaction）\n
 * 的方法。事务可以回滚到savepoint而不影响savepoint创建前的变化。不需要放弃整个事务。
 * 异步创建savepoint。
 * @attention @li 单个savepoint名称最长64字节，不指定名称时，表示匿名savepoint。\n
 *   @li savepoint的个数的上限，和其他DML类似，取决于系统配置undo段的内存大小。\n
 *   @li savepoint在一个事务内可以重名，但是rollback savepoint和release savepoint只对最近的savepoint生效。（GMDB
 * 502和Pg类似，\n 如果savepoint时，存在重复名称，保留旧的savepoint，而回滚（ROLLBACK TO SAVEPOINT name）或者 \n
 *        释放（RELEASE SAVEPOINT name）时，使用最近的一个。（该实现和SQL标准不兼容）。
 *   @li 只能在显式开启的事务中，使用savepoint功能，事务提交或者回滚后，该事务下的savepoint全部失效。
 *   @li 事务失败状态下，不允许创建savepoint。。
 *   @li 请勿随意更改入参conn结构，以GMDB接口构造为准。
 * @param[in] conn 连接句柄。
 * @param[in] savepointName 待创建的savepoint名称。
 * @param[in] userCb 用户的回调函数，参考GmcTransDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransCreateSavepointAsync(
    GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData);

/**
 * @ingroup gmc
 * @par 描述：savepoint是在数据库事务处理中实现“子事务”（subtransaction），也称为嵌套事务（英语：nested transaction）\n
 *         的方法。事务可以回滚到savepoint而不影响savepoint创建前的变化。不需要放弃整个事务。
 * 异步释放savepoint
 * @attention @li savepoint在一个事务内可以重名，但是rollback savepoint和release
 * savepoint只对最近的savepoint生效。（GMDB 502和Pg类似，\n
 *        如果savepoint时，存在重复名称，保留旧的savepoint，而回滚（ROLLBACK TO SAVEPOINT name）或者 \n
 *        释放（RELEASE SAVEPOINT name）时，使用最近的一个。（该实现和SQL标准不兼容）。
 *   @li 请勿随意更改入参conn结构，以GMDB接口构造为准。
 * @param[in] conn 连接句柄。
 * @param[in] savepointName 待释放的savepoint名称。
 * @param[in] userCb 用户的回调函数，参考GmcTransDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransReleaseSavepointAsync(
    GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData);

/**
 * @ingroup gmc
 * @par 描述：savepoint是在数据库事务处理中实现“子事务”（subtransaction），也称为嵌套事务（英语：nested transaction）\n
 *          的方法。事务可以回滚到savepoint而不影响savepoint创建前的变化。不需要放弃整个事务。
 * 异步回滚savepoint。
 * @attention @li 回滚不存在的savepoint会报错。\n
 *   @li 回滚savepoint不能有效释放事务锁资源。\n
 *   @li
 * 回滚savepoint只会回滚在该savepoint被建立之后执行的所有命令。该保存点保持有效并且可以在以后再次回滚到他（如有需要）。\n
 *   @li 回滚savepoint会隐式的销毁在该savepoint之后建立的所有savepoint。\n
 *   @li 请勿随意更改入参conn结构，以GMDB接口构造为准。
 * @param[in] conn 连接句柄。
 * @param[in] savepointName 需要回滚的savepoint名称。
 * @param[in] userCb 用户的回调函数，参考GmcTransDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransRollBackSavepointAsync(
    GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData);

/**
 * @ingroup gmc
 * @par 描述：
 * （异步接口）乐观事务写写冲突的冲突检测接口。
 * @attention @li 如果此时连接未处于事务之中，回调函数的status直接返回GMERR_OK。\n
 *   @li 如果此时连接处于悲观事务，回调函数的status直接返回GMERR_OK。\n
 *   @li 如果此时连接处于乐观事务，如果发生写写冲突，回调函数的status返回GMERR_RESTRICT_VIOLATION。
 * 如果没有冲突，则返回GMERR_OK。\n
 * @param[in] conn 连接句柄。
 * @param[in] userCb 用户的回调函数，参考GmcTransDoneT。
 * @param[in] userData 用户数据。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcTransCheckOptimisticTrxConflictAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData);

/**
 * @ingroup gmc
 * @par 描述：
 * 卸载客户端相关资源，强制清空内存的所有资源。
 * @attention
 * 该接口的调用属于高危操作，严重时可导致客户端所在进程异常退出，请勿随意更改客户端的全局变量及操作句柄。释放资源后请勿再次调用客户端相关变量和句柄。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcUnInit(void);

/**
 * @ingroup gmc
 * @par 描述：
 * 在建表后，根据表名获取label的类型。
 * @attention @li 当前只支持查询VertexLabel、kvTable的表类型，暂不支持查询EdgeLabel和系统表的表类型。
 *  @li 请勿随意更改入参stmt结构，以GMDB接口构造为准。
 * @param[in] stmt 客户端句柄
 * @param[in] labelName 需要查询的label名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @param[out] label_type 函数的返回值。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetLabelTypeByName(GmcStmtT *stmt, const char *labelName, uint32_t *labelType);

/**
 * @ingroup gmc
 * @par 描述：
 * 在建表后，根据表名获取VertexLabel的类型。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准。
 * @param[in] stmt 客户端句柄
 * @param[in] labelName 需要查询的label名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @param[out] vertexLabelType VertexLabel的具体类型，包括普通表、YANG表、Datalog表等。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetVertexLabelTypeByName(
    GmcStmtT *stmt, const char *labelName, GmcVertexLabelTypeE *vertexLabelType);

/**
 * @ingroup gmc
 * @par 描述：
 * 在建表后，根据Datalog表名获取Datalog表的类型。
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准。
 * @param[in] stmt 客户端句柄
 * @param[in] labelName 需要查询的label名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @param[out] datalogLabelType Datalog表的具体类型。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetDatalogLabelTypeByName(
    GmcStmtT *stmt, const char *labelName, GmcDtlLabelTypeE *datalogLabelType);

/**
 * @ingroup gmc
 * @par 描述：
 * 根据表名获取表是否存在。
 * @attention @li 当前只支持查询VertexLabel、kvTable，暂不支持查询EdgeLabel和系统表。
 *  @li 请勿随意更改入参stmt结构，以GMDB接口构造为准。
 * @param[in] stmt 客户端句柄
 * @param[in] labelName 需要查询的label名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @param[out] isExist 函数的返回值。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcLabelIsExist(GmcStmtT *stmt, const char *labelName, bool *isExist);

/**
 * @ingroup gmc
 * @par 描述：
 * 在线修改服务端gmserver.ini文件中配置项的值。
 * @attention @li
 * 请勿随意更改入参stmt结构，以GMDB接口构造为准。入参value大小与valueSize需要保证对应关系准确，其安全性由调用者保证
 *  @li 当前gmserver.ini文件中，仅部分配置项支持通过gmadmin工具或者调用GmcSetCfg接口进行在线修改，\n
 *      但是修改的是DB内部的配置项，而不会修改gmserver.ini文件本身，且修改后立即生效。
 * @param[in] stmt 客户端句柄
 * @param[in] confName 配置项名称。
 * @param[in] type 配置项值的数据类型，请参见GmcDataTypeE。
 * @param[in] value 配置项值的长度。
 * @param[in] valueSize 配置项值的长度。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSetCfg(
    GmcStmtT *stmt, const char *confName, GmcDataTypeE type, const void *value, uint32_t valueSize);

/**
 * @ingroup gmc
 * @par 描述：
 * 获取gmserver.ini文件中配置项的值
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端句柄
 * @param[in] confName 配置项名称，需要确保该字符串以'\0'结尾，否则严重时可导致客户端所在进程异常退出。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetCfg(GmcStmtT *stmt, const char *confName);

/**
 * @ingroup gmc
 * @par 描述：
 * 获取gmserver.ini文件中配置项的具体信息。
 * @attention 以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端句柄
 * @param[in] infoType 配置项信息类型，请参见GmcCfgInfoTypeE。
 * @param[out] type 获取到的配置项值的数据类型，请参见GmcDataTypeE。
 * @param[out] value
 * 获取到的配置项值的长度,此接口采用浅拷贝的方式获取value，用户不得修改value指向的内存。同时在用户使用value过程中，禁止通过GmcFreeStmt释放
 * 或GmcPrepareStmtByLabelName、GmcPrepareStmtByLabelNameWithVersion、GmcResetStmt等接口重置内存，
 * 否则可能会导致结果不可预期，严重可导致进程退出。
 * @param[out] valueSize 获取到的配置项值的长度。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetCfgInfoByType(
    GmcStmtT *stmt, GmcCfgInfoTypeE infoType, GmcDataTypeE *type, void **value, uint32_t *valueSize);

/**
 * @ingroup gmc
 * @par 描述：
 * 在线修改客户端配置项的值。
 * @attention 以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] confName 配置项名称。
 * @param[in] type 配置项值的数据类型，请参见GmcDataTypeE。
 * @param[in] value 配置项值的长度。
 * @param[in] valueSize 配置项值的长度。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSetCltCfg(const char *cfgName, GmcDataTypeE type, const void *value, uint32_t valueSize);

/**
 * @ingroup gmc
 * @par 描述：
 * 客户端异常信号处理钩子函数，可以用来保护共享内存的操作不被打断，让客户端进程优雅退出，具体接管的信号由业务结合实际情况自定义
 * 客户端异常退出时，会detach客户端操作的共享内存
 * 客户端收到异常信号时有三种处理策略，会根据客户端接管信号线程状态决定处理策略，平台根据输出策略进行后续进程资源回收等处理。
 * @attention
 * 本函数非最终的信号处理，仅包装信号处理db模块的核心逻辑，且信号注册完本函数后需要调用GmcSignalRegisterNotify来通知db信号已经成功注册
 *
 * 三种处理策略：
 * @li 客户端信号接管线程不在操作共享内存区，使用策略0，等待其他客户端线程退出共享内存区，接着走系统默认处理函数
 * @li 客户端信号接管线程在操作共享内存区，且信号为外部信号，比如SIGABRT、SIGTERM等，使用策略1，等待其它线程退出共享
 * 内存操作，第一次外部信号处理完毕，返回用户态继续接管信号线程的共享内存区操作，操作完毕后，重新抛出该信号，让系统回收
 * 进程资源
 * @li 客户端信号接管线程在操作共享内存区，且信号为内部信号，即内核产生的信号比如SIGSEGV、SIGBUS错误，使用策略2，平台
 * 会让单板重启
 * @param[in] signalInfo 信号处理函数传入的参数，包含此次信号的信息比如signo, siginfo_t
 * @param[out] outputData 信号处理输出，包含处理策略，单板重启的reboot code，以及相关dfx日志信息
 * @return db信号处理的返回码Status，可参见“错误码参考”章节
 */
GMC_EXPORT int32_t GmcCrashHandlerHook(GmcSignalInfoT *signalInfo, GmcSignalOutputDataT *outputData);

/**
 * @ingroup gmc
 * @par 描述：
 * 客户端信号注册成功通知DB函数，信号注册由业务进程注册，注册完毕后需要告知DB信号接管函数已经注册，否则预期的直连写操作会
 * 降级为C/S操作，创建状态合并订阅时会报错
 */
GMC_EXPORT void GmcSignalRegisterNotify(void);

/**
 * @ingroup gmc
 * @par 描述：
 * 客户端侧打开共享内存安全门。在安全门外访问安全域内的内存会导致core。
 */
GMC_EXPORT void GmcOpenGate(void);

/**
 * @ingroup gmc
 * @par 描述：
 * 客户端侧关闭共享内存安全门。在安全门外访问安全域内的内存会导致core。
 */
GMC_EXPORT void GmcCloseGate(void);

/**
 * @ingroup gmc
 * @par 描述：
 * 导出指定表的二进制文件，并且保存在exportPara中指定的路径下。若指定的路径下已有相同表导出的旧版本数据文件，则进行覆盖（全部指定的表都导出成功时，才会覆盖）。
 * @attention
 * @li 请勿随意更改入参stmt结构，以GMDB接口构造为准, 其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] exportPara 导出指定表的二进制文件接口的入参，里面包括指定的表名数组、存放的路径等。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcExportTables(GmcStmtT *stmt, GmcExportParaT *exportPara);

/**
 * @ingroup gmc
 * @par 描述：
 * 删除exportFilePath路径下，所有导出的表二进制文件（.bin_data文件）。
 * @param[in] stmt 客户端stmt句柄。
 * @param[in] path 指定要清空的表二进制数据文件所存放的路径。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcClearExportFile(GmcStmtT *stmt, const char *path);

/**
 * @ingroup gmc
 * @par 描述：
 * 删除path路径下，指定表的导出的二进制文件。
 * @param[in] path 指定要删除的表二进制数据文件所存放的路径。
 * @param[in] tablesName 指定要删除导出文件的表名数组。
 * @param[in] tableNum 指定要删除的导出文件的个数。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcDeleteExportFileByTableName(
    GmcStmtT *stmt, const char *path, const char **tablesName, uint32_t tableNum);

#ifdef __cplusplus
}
#endif

#endif /* GMC_H */
