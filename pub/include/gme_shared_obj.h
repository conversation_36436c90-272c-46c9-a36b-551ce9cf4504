/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gme_shared_obj.h
 * Description: Provide gme sharedObj api
 * Author:
 * Create: 2024-02-06
 */

#ifndef GME_SHARED_OBJ_H
#define GME_SHARED_OBJ_H

#include "gme_api.h"

#ifdef __cplusplus
extern "C" {
#endif

#define INHERITED_ATTRIBUTE_INDICATOR UINT16_MAX  // The number indicates that the attribute is continued.

typedef enum GmeSharedObjType { GME_SEQUENCE_TYPE = 0 } GmeSharedObjTypeE;

typedef struct GmeSeqEmbed {
    char *type;
    char *path;
} GmeSeqEmbedT;

/**
 * @ingroup gme_shared_obj
 * @brief Create one shared object named sharedObjName.
 * @param[in] conn Connection which handles this operation.
 * @param[in] type Type of shared object.
 * @param[in] sharedObjName Name of shared object.
 * @return @li GMERR_OK @li error code.
 */
GME_EXPORT int32_t GmeCreateSharedObj(GmeConnT *conn, GmeSharedObjTypeE type, const char *sharedObjName);

/**
 * @ingroup gme_shared_obj
 * @brief Drop one shared object named sharedObjName.
 * @param[in] conn Connection which handles this operation.
 * @param[in] type Type of shared object.
 * @param[in] sharedObjName Name of shared object.
 * @return @li GMERR_OK @li error code.
 */
GME_EXPORT int32_t GmeDropSharedObj(GmeConnT *conn, GmeSharedObjTypeE type, const char *sharedObjName);

/**
 * @ingroup gme_shared_obj
 * @brief Insert content into sequence with given index.
 * @param[in] conn Connection which handles this operation.
 * @param[in] sequenceName Name of this sequence.
 * @param[in] index Insert location.
 * @param[in] content Insert string.
 * @param[in] attributes For example: {"bold":true,"hw_font":"2.0"}
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeSeqInsert(
    GmeConnT *conn, const char *sequenceName, uint32_t index, const char *content, const char *attributes);

/**
 * @ingroup gme_shared_obj
 * @brief Read one sequence object to value string.
 * @param[in] conn Connection which handles this operation.
 * @param[in] sequenceName Name of the sequence object.
 * @param[out] value String read from sequence object.
 * @return GMERR_OK if success
 */
GME_EXPORT int32_t GmeSeqRead(GmeConnT *conn, const char *sequenceName, char **value);

/**
 * @ingroup gme_shared_obj
 * @brief Read one sequence object to value string as delta format.
 * @param[in] conn Connection which handles this operation.
 * @param[in] sequenceName Name of the sequence object.
 * @param[in] snapshot Snap of the latest verion.
 * @param[in] snapshotPerv Snap of the pervious verion.
 * @param[out] value String read from sequence object in delta format.
 * @return GMERR_OK if success
 */
GME_EXPORT int32_t GmeSeqReadInDeltaMode(
    GmeConnT *conn, const char *sequenceName, void *snapshot, void *snapshotPerv, char **value);
/**
 * @ingroup gme_shared_obj
 * @brief Free the sequence value.
 * @param[in] value value read from sequence object.
 */
GME_EXPORT int32_t GmeFreeSeqValue(char *value);

/**
 * @ingroup gme_shared_obj
 * @brief Delete content into sequence with given index.
 * @param[in] conn Connection which handles this operation.
 * @param[in] sequenceName Name of this sequence.
 * @param[in] index Delete location.
 * @param[in] length Length of delete string.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeSeqDelete(GmeConnT *conn, const char *sequenceName, uint32_t index, uint32_t length);

/**
 * @ingroup gme_shared_obj
 * @brief Assign attributes into sequence with given length from given index
 * @param[in] conn Connection which handles this operation
 * @param[in] sequenceName Name of this sequence
 * @param[in] index Assign start location
 * @param[in] length Content length need assign attributes
 * @param[in] attributes For example: {"hw_font":"1.45"}
 * @return GMERR_OK if success
 */
GME_EXPORT int32_t GmeSeqAssignAttributes(
    GmeConnT *conn, const char *sequenceName, uint32_t index, uint32_t length, const char *attributes);

/**
 * @ingroup gme_shared_obj
 * @brief Enable the undo and redo features for the specific sequence
 * @param[in] conn Connection which handles this operation
 * @param[in] sharedObjName Name of this sharedObj
 * @param[in] sharedObjType type of this sharedObj
 * @return GMERR_OK if success
 */
GME_EXPORT int32_t GmeSharedObjOpHistoryEnable(
    GmeConnT *conn, const char *sharedObjName, GmeSharedObjTypeE sharedObjType);

/**
 * @ingroup gme_shared_obj
 * @brief Disable the undo and redo features for the specific sequence
 * @param[in] conn Connection which handles this operation
 * @param[in] sharedObjName Name of this sharedObj
 * @param[in] sharedObjType type of this sharedObj
 * @return GMERR_OK if success
 */
GME_EXPORT int32_t GmeSharedObjOpHistoryDisable(
    GmeConnT *conn, const char *sharedObjName, GmeSharedObjTypeE sharedObjType);

/**
 * @ingroup gme_shared_obj
 * @brief Undo operation for the specific sequence
 * @param[in] conn Connection which handles this operation
 * @param[in] sharedObjName Name of this sharedObj
 * @param[in] sharedObjType type of this sharedObj
 * @return GMERR_OK if success
 */
GME_EXPORT int32_t GmeSharedObjOpHistoryUndo(
    GmeConnT *conn, const char *sharedObjName, GmeSharedObjTypeE sharedObjType);

/**
 * @ingroup gme_shared_obj
 * @brief Redo operation for the specific sequence
 * @param[in] conn Connection which handles this operation
 * @param[in] sharedObjName Name of this sharedObj
 * @param[in] sharedObjType type of this sharedObj
 * @return GMERR_OK if success
 */
GME_EXPORT int32_t GmeSharedObjOpHistoryRedo(
    GmeConnT *conn, const char *sharedObjName, GmeSharedObjTypeE sharedObjType);

typedef int32_t (*GmeSharedObjSubFuncT)(void);  // Register func to subscribe

/**
 * @ingroup gme_shared_obj
 * @brief Subscribe one sequence object.
 * @param[in] db Pointer to the db.
 * @param[in] sequenceName Name of the sequence object.
 * @param[in] subFunc Register func.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeSeqSub(GmeConnT *conn, const char *sequenceName, GmeSharedObjSubFuncT subFunc);

/**
 * @ingroup gme_shared_obj
 * @brief Cancel subscribe one sequence object.
 * @param[in] db Pointer to the db.
 * @param[in] sequenceName Name of the sequence object.
 * @return GMERR_OK if success
 */
GME_EXPORT int32_t GmeSeqUnsub(GmeConnT *conn, const char *sequenceName);

/**
 * @brief Insert embed object to one sequence with given index.
 * @param[in] conn Connection which handles this operation.
 * @param[in] sequenceName Name of this sequence.
 * @param[in] index Insert location.
 * @param[in] embed Insert embed object.
 * @param[in] attrStr The attributes of embed object, now is only support {}.
 * @return GRD_OK if success.
 */
GME_EXPORT int32_t GmeSeqEmbedInsert(
    GmeConnT *conn, const char *sequenceName, uint32_t index, GmeSeqEmbedT *embed, const char *attrStr);

/**
 * @brief Allocate memory for snapshot and init snapshot.
 * @param[in] conn Connection which handles this operation.
 * @param[out] snapshot the pointer of snapshot of DmSnap type.
 * @return GRD_OK if success.
 */
GME_EXPORT int32_t GmeInitSnap(GmeConnT *conn, void **snapshot);

/**
 * @brief convert snaptshot of Json format to Dmsnapshot.
 * @param[in] conn Connection which handles this operation.
 * @param[in] snapshotOrigin snapshot of Json format.
 * @param[out] snapshotTarget snapshot of Dmsnapshot type.
 * @return GRD_OK if success.
 */
GME_EXPORT int32_t GmeSnap2DmSnap(GmeConnT *conn, const char *snapshotOrigin, void *snapshotTarget);

/**
 * @brief Free Dmsnapshot.
 * @param[in] conn Connection which handles this operation.
 * @param[in] sequenceName snapshot of Dmsnapshot type.
 * @return GRD_OK if success.
 */
GME_EXPORT void GmeFreeSnap(GmeConnT *conn, void *snapshot);

/**
 * @brief Get snap Json format from sequence.
 * @param[in] conn Connection which handles this operation.
 * @param[in] sequenceName Name of this sequence.
 * @param[out] snap snapshot as string type.
 * @return GRD_OK if success.
 */
GME_EXPORT int32_t GmeGetSnap(GmeConnT *conn, const char *sequenceName, char **snap);

#ifdef __cplusplus
}
#endif

#endif /* GME_SHARED_OBJ_H */
