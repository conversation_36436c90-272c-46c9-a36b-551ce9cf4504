#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
# version V1.0
#
# use bep to build binary consistency
#
set -e
. "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/ci.sh
. "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/make_haclib.sh
. "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/make_openssl.sh
. "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/hilib_install/make_hilib.sh
. "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/make_cjson.sh
. "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/make_zlib.sh
. "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/make_patch_obj.sh

GMDB_DIR=$( cd $(dirname $0) && cd ../.. && pwd)
TMP_DIR="${GMDB_DIR}"/build/tmp
BUILD_DIR="${GMDB_DIR}"/build
OUTPUT_DIR="${GMDB_DIR}"/output
ASAN_INIT_PATH="/mnt/hgfs/share_vmware/asan/tools/ASAN_init.sh"
g_cmake_feature_opt=''

function env_asan_init() {
    if [ "${os}" = "rtosv2" ] || [ "${os}" = "hpe" ]; then
        if [ -f "${ASAN_INIT_PATH}" ]; then
            "${ASAN_INIT_PATH}" off
        else
            echo "Error: ${ASAN_INIT_PATH} does not exist, can not init ASAN env"
            exit 1
        fi
        if [ "${os}" = "rtosv2" ]; then
            "${ASAN_INIT_PATH}" on ysl
        else
            "${ASAN_INIT_PATH}" on hpk
        fi
    else
        "${GMDB_DIR}"/scripts/ASAN_init.sh off && "${GMDB_DIR}"/scripts/ASAN_init.sh on "${os}"
    fi
}

function env_toolchain() {
    [[ "${asan}" == '1' ]] && env_asan_init
    . ${GMDB_DIR}/toolchain_env.sh
    if [ "${bsc_llvm}" = '1' ]; then
        source_toolchain_env_bsc_llvm ${arch} ${os}
    elif [ "${use_glibc}" = '1' ]; then
        source_toolchain_env ${arch} ${product}
    elif [ "${os}" = 'rtosv2' ]; then
        source_toolchain_env ${arch} ${product}
    elif [ "${os}" = 'rtosv2x' ]; then
        source_toolchain_env ${arch}-hm
    elif [ "${os}" = 'hpe' ]; then
        source_toolchain_env ${arch}-hpe ${product}
    elif [ "${os}" = 'umhpe' ]; then
        source_toolchain_env ${arch}-umhpe
    else
        source_toolchain_env ${arch}
    fi
    export ARCH=${arch}
    export OS=${os}
    if [ "${bsc_llvm}" = "1" ]; then
      if [ "${os}" = "rtosv2" ]; then
        llvm_prefix=${LLVM_PREFIX}
      fi
      export AR="${llvm_prefix}"llvm-ar
      export CXX="${llvm_prefix}"clang++
      export OBJCOPY_TOOL="${llvm_prefix}"llvm-objcopy
      export STRIP_TOOL="${llvm_prefix}"llvm-strip
      if [ "${os}" != "rtosv2" ]; then
        if [ "${arch}" = "aarch64" ]; then
          llvm_prefix="aarch64-hpk-"
        fi
        if [ "${arch}" = "armv7l" ] || [ "${arch}" = "arm32" ]; then
          llvm_prefix="arm-hpe-"
        fi
        if [ "${arch}" = "x86_64" ]; then
          llvm_prefix="x86_64-hpe-"
        fi
      fi
      export LIB_LINKER="${llvm_prefix}"clang
      export EXE_LINKER="${llvm_prefix}"clang
      export COMPILER="${llvm_prefix}"clang
      export COMPILER_CXX="${llvm_prefix}"clang++
    else
      export AR="${CROSS_COMPILE}"ar
      export OBJCOPY_TOOL="${CROSS_COMPILE}"objcopy
      export STRIP_TOOL="${CROSS_COMPILE}"strip
      export LIB_LINKER="${CROSS_COMPILE}"gcc
      export EXE_LINKER="${CROSS_COMPILE}"gcc
      export COMPILER="${CROSS_COMPILE}"gcc
    fi


    if [[ "${arch}" == 'x86' ]];then
        export CROSS_COMPILE_FLAG='-m32'
    fi
}

function env_toolchain_clang() {
    export CC=clang
    export CXX=clang++
}

add_keywords_to_lex_file() {
    # 定义匹配字符串的变量，并转义特殊字符
    MATCH_STRING="const SqlKeyWordInfoT g_sqlKeyWordInfo\[\] = {"

    # 检查是否提供了两个文件路径参数
    if [ -z "$1" ] || [ -z "$2" ]; then
        echo "Usage: $0 <source_file_path> <target_file_path>"
        return 1
    fi

    # 检查源文件是否存在
    if [ ! -f "$1" ]; then
        echo "Source file does not exist: $1"
        return 1
    fi

    # 检查目标文件是否存在
    if [ ! -f "$2" ]; then
        echo "Target file does not exist: $2"
        return 1
    fi

    # 读取源文件的每一行，并添加四个空格
    while IFS= read -r line; do
        # 检查行是否非空
        if [[ -n "$line" ]]; then
            # 使用sed将行添加到目标文件中匹配的行后面
            echo "    $line" | sed -i "/$MATCH_STRING/r /dev/stdin" "$2"
        fi
    done < "$1"

    target_dir=$(dirname "$2")

    # 提取数组定义并排序，保存到临时文件
    awk "/$MATCH_STRING/,/};/ { if (/};/) exit; if (/$MATCH_STRING/) {next} print }" "$2" \
      | sort -t',' -k1,1d > "$target_dir/tmp.l"

    # 删除原文件中g_sqlKeyWordInfo定义中的内容
    sed -i "/$MATCH_STRING/,/};/{/$MATCH_STRING/!{/};/!d}}" "$2"

    # 为g_sqlKeyWordInfo填充排好序的定义
    sed -i "/$MATCH_STRING/ r $target_dir/tmp.l" "$2"

    # 删除临时文件
    rm -rf "$target_dir/tmp.l"
}

function gen_grammar_scanner() {
    if [[ "$1" == '1' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/compiler/datalog/parser"
    flex cpl_dtl.l
    sed -i 's/yy_size_t /int /g' lex.yy.c
    sed -i 's/size_t n/int n/g' lex.yy.c
    bison -dy cpl_dtl.y
    popd
}

function gen_grammar_scanner_ts() {
    if [[ "$1" == '1' ]];then
        return
    fi
    cp -rf "${GMDB_DIR}/src/compiler/public/parser/cpl_public_sql.l" "${GMDB_DIR}/src/compiler/ts/parser/cpl_ts.l"
    pushd "${GMDB_DIR}/src/compiler/ts/parser"
    add_keywords_to_lex_file ts_keywords.txt cpl_ts.l
    sed -i "s@g_sqlParserPos@g_tsParserPos@g" cpl_ts.l
    sed -i "s@g_sqlKeyWordInfo@g_tsKeyWordInfo@g" cpl_ts.l
    sed -i "s@g_numSqlKeyWordInfo@g_numTsKeyWordInfo@g" cpl_ts.l
    sed -i "s@%option prefix=\"sql_\"@%option prefix=\"ts_\"@g" cpl_ts.l
    sed -i "s@sql_error@ts_error@g" cpl_ts.l
    sed -i "<EMAIL><EMAIL>.h@g" cpl_ts.l
    sed -i "s@sql_lval@ts_lval@g" cpl_ts.l
    sed -i '/COMBINE/d' cpl_ts.l
    sed -i '/LPASMEM/d' cpl_ts.l
    sed -i '/TABLESPACE/d' cpl_ts.l
    sed -i '/DATAFILE/d' cpl_ts.l
    flex -P ts_ cpl_ts.l
    sed -i 's/yy_size_t /int /g' lex.ts_.c
    sed -i 's/size_t n/int n/g' lex.ts_.c
    bison -dy -Werror -Wno-yacc -Wno-deprecated cpl_ts.y
    popd
}

function gen_grammar_scanner_stream() {
    if [[ "$1" == '1' ]];then
        return
    fi
    cp -rf "${GMDB_DIR}/src/compiler/public/parser/cpl_public_sql.l" "${GMDB_DIR}/src/compiler/stream/parser/cpl_stream.l"
    pushd "${GMDB_DIR}/src/compiler/stream/parser"
    add_keywords_to_lex_file stream_keywords.txt cpl_stream.l
    sed -i "s@g_sqlParserPos@g_streamParserPos@g" cpl_stream.l
    sed -i "s@g_sqlKeyWordInfo@g_streamKeyWordInfo@g" cpl_stream.l
    sed -i "s@g_numSqlKeyWordInfo@g_numstreamKeyWordInfo@g" cpl_stream.l
    sed -i "s@%option prefix=\"sql_\"@%option prefix=\"stream_\"@g" cpl_stream.l
    sed -i "s@sql_error@stream_error@g" cpl_stream.l
    sed -i "<EMAIL><EMAIL>.h@g" cpl_stream.l
    sed -i "s@sql_lval@stream_lval@g" cpl_stream.l
    sed -i '/COMBINE/d' cpl_stream.l
    sed -i '/LPASMEM/d' cpl_stream.l
    sed -i '/TABLESPACE/d' cpl_stream.l
    sed -i '/DATAFILE/d' cpl_stream.l
    sed -i '/#include "cpl_public_sql_parser_common.h"/a #include "cpl_stream_parser.h"' cpl_stream.l
    flex -P stream_ cpl_stream.l
    sed -i 's/yy_size_t /int /g' lex.stream_.c
    sed -i 's/size_t n/int n/g' lex.stream_.c
    bison -dy -Werror -Wno-yacc -Wno-deprecated cpl_stream.y
    popd
}

function gen_grammar_scanner_sql() {
    if [[ "$1" == '1' ]];then
        return
    fi
    cp -rf "${GMDB_DIR}/src/compiler/public/parser/cpl_public_sql.l" "${GMDB_DIR}/src/compiler/sql/parser/cpl_sql.l"
    pushd "${GMDB_DIR}/src/compiler/sql/parser"
    sed -i '/#include "cpl_public_sql_parser_common.h"/a #include "cpl_sql_parser.h"' cpl_sql.l
    flex -P sql_ cpl_sql.l
    sed -i 's/yy_size_t /int /g' lex.sql_.c
    sed -i 's/size_t n/int n/g' lex.sql_.c
    bison -dy -Werror -Wno-yacc -Wno-deprecated cpl_sql.y
    popd
}

function gen_grammar_scanner_sql_terminal() {
    if [[ "$1" == '0' ]];then
        return
    fi
    cp -rf "${GMDB_DIR}/src/src_terminal/compiler_terminal/public/parser/cpl_public_sql.l" "${GMDB_DIR}/src/src_terminal/compiler_terminal/sql/parser/cpl_sql.l"
    pushd "${GMDB_DIR}/src/src_terminal/compiler_terminal/sql/parser"
    sed -i '/#include "cpl_public_sql_parser_common.h"/a #include "cpl_sql_parser.h"' cpl_sql.l
    flex -P sql_ cpl_sql.l
    sed -i 's/yy_size_t /int /g' lex.sql_.c
    sed -i 's/size_t n/int n/g' lex.sql_.c
    bison -dy -Werror -Wno-yacc -Wno-deprecated cpl_sql.y


    cp -rf "${GMDB_DIR}/src/src_terminal/compiler_terminal/public/parser/cpl_sql_invert.l" "${GMDB_DIR}/src/src_terminal/compiler_terminal/sql/parser/cpl_sql_invert.l"
    sed -i '/#include "cpl_public_sql_parser_common.h"/a #include "cpl_sql_parser.h"' cpl_sql_invert.l
    flex -P sql_invert_ cpl_sql_invert.l
    sed -i 's/yy_size_t /int /g' lex.sql_invert_.c
    sed -i 's/size_t n/int n/g' lex.sql_invert_.c
    bison -dy -Werror -Wno-yacc -Wno-deprecated cpl_sql_invert.y
    popd
}

function gen_grammar_scanner_gql() {
    if [[ "$1" == '1' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/compiler/gql/parser"
    flex -P gql_ cpl_gql.l
    sed -i 's/yy_size_t /int /g' lex.gql_.c
    sed -i 's/size_t n/int n/g' lex.gql_.c
    bison -dy -Werror -Wno-yacc -Wno-deprecated cpl_gql.y
    popd
}

function gen_grammar_scanner_gql_terminal() {
    if [[ "$1" == '0' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/src_terminal/compiler_terminal/gql/parser"
    flex -P gql_ cpl_gql.l
    sed -i 's/yy_size_t /int /g' lex.gql_.c
    sed -i 's/size_t n/int n/g' lex.gql_.c
    bison -dy -Werror -Wno-yacc -Wno-deprecated cpl_gql.y

    flex -P gql_invert_ cpl_gql_invert.l
    sed -i 's/yy_size_t /int /g' lex.gql_invert_.c
    sed -i 's/size_t n/int n/g' lex.gql_invert_.c
    bison -dy -Werror -Wno-yacc -Wno-deprecated cpl_gql_invert.y
    popd
}

function clean_grammar_scanner() {
    if [[ "$1" == '1' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/compiler/datalog/parser"
    rm -f lex.yy.c y.tab.c y.tab.h
    popd
}

function clean_grammar_scanner_ts() {
    if [[ "$1" == '1' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/compiler/ts/parser"
    rm -f lex.ts_.c ts.tab.c ts.tab.h cpl_ts.l
    popd
}

function clean_grammar_scanner_stream() {
    if [[ "$1" == '1' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/compiler/stream/parser"
    rm -f lex.stream_.c stream.tab.c stream.tab.h cpl_stream.l
    popd
}

function clean_grammar_scanner_sql() {
    if [[ "$1" == '1' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/compiler/sql/parser"
    rm -f lex.sql_.c sql.tab.c sql.tab.h cpl_sql.l
    popd
}

function clean_grammar_scanner_sql_terminal() {
    if [[ "$1" == '0' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/src_terminal/compiler_terminal/sql/parser"
    rm -f lex.sql_.c lex.sql_invert_.c sql.tab.c sql.tab.h sql_invert.tab.c sql_invert.tab.h cpl_sql.l cpl_sql_invert.l
    popd
}

function clean_grammar_scanner_gql() {
    if [[ "$1" == '1' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/compiler/gql/parser"
    rm -f lex.gql_.c gql.tab.c gql.tab.h
    popd
}

function clean_grammar_scanner_gql_terminal() {
    if [[ "$1" == '0' ]];then
        return
    fi
    pushd "${GMDB_DIR}/src/src_terminal/compiler_terminal/gql/parser"
    rm -f lex.gql_.c gql.tab.c gql.tab.h  lex.gql_invert_.c gql_invert.tab.c gql_invert.tab.h
    popd
}

function generate_features_cmake_opt() {
	features_arr=($(echo "$1" | tr ',' ' '))
	features_num=${#features_arr[@]}
	for ((i=0; i<features_num; i++)); do
		g_cmake_feature_opt="${g_cmake_feature_opt} -D${features_arr[$i]^^}=ON "
	done
}

declare -A db_args_map=()

# 输入:字符串"key1=val1;key2=val2;key3=val3"
# 输出:解析为map，保存在db_args_map变量中
function str_to_map() {
    local str=$1
    IFS=';' read -ra pairs <<< "$str"
    for pair in "${pairs[@]}"; do
        key=$(echo "$pair" | awk -F= '{print $1}')
        value=$(echo "$pair" | awk -F= '{print substr($0, index($0, "=")+1)}')
        db_args_map["$key"]="$value"
    done
    echo "${db_args_map[@]}"  # 输出关联数组内容（调试用）
    return 0
}

function build_db()
{
    str_to_map "$1"
    for key_name in "${!db_args_map[@]}";do
        declare "${key_name}=${db_args_map[${key_name}]}"
    done
    cmake_custom_args=""
    # if argument number is more than 10, please use ${10} ...

    # call flex and bison
    gen_grammar_scanner ${terminal}
    gen_grammar_scanner_ts ${terminal}
    gen_grammar_scanner_stream ${terminal}
    gen_grammar_scanner_sql ${terminal}
    gen_grammar_scanner_sql_terminal ${terminal}
    gen_grammar_scanner_gql ${terminal}
    gen_grammar_scanner_gql_terminal ${terminal}

    if [ "${build_type}" = 'debug' ]; then
        cmake_custom_args+=" -DDEBUG=1"
    else
        if [ "${build_type}" = 'release' ]; then
            cmake_custom_args+=" -DRELEASE=1"
        fi
        coverage="0"
        asan="0"
        enablecp="0"
        tracepc="0"
        fuzz="0"
        extramsg="0"
        ubsan="0"
        tsan="0"
    fi

    if [ "${hpe}" = "1" ]; then
        cmake_custom_args+=" -DHPE=1"
        os="hpe"
    fi
    if [ "${os}" = "umhpe" ]; then
        cmake_custom_args+=" -DHPE=1"
        cmake_custom_args+=" -DUMHPE=1"
    fi

    if [ "${arch}" = "arm32" ] || [ "${arch}" = "arm32a15le" ] || [ "${arch}" = "armv7l" ]; then
        cmake_custom_args+=" -DARM32=1"
    elif [ "${arch}" = "x86" ]; then
        cmake_custom_args+=" -DX86=1"
    elif [ "${arch}" = "x86_64" ]; then
        cmake_custom_args+=" -DX86_64=1"
    fi

    if [ x"$features" = x"ALL" ]; then
        echo "building all features"
        g_cmake_feature_opt="-DFEATURE_ALL=ON"
    else
        generate_features_cmake_opt "$features"
        echo "feature cmake option: ${g_cmake_feature_opt}"
    fi

    if [ x"$single_so" = x"1" ]; then
        g_cmake_single_so="-DSINGLE_SO=ON"
    fi

    if [ "${build_path}" != "0" ]; then
       cmake_custom_args+=" -DGMDB_BUILD_DIR=${build_path}"
    fi

    if [ "${output_path}" != "0" ]; then
       cmake_custom_args+=" -DGMDB_OUTPUT_DIR=${output_path}"
    fi

    # 区分是系统是32位还是64位
    if [ "${arch}" = "arm32" ] || [ "${arch}" = "arm32a15le" ] || [ "${arch}" = "x86" ] || [ "${arch}" = "armv7l" ]; then
        cmake_custom_args+=" -DSYS32BITS=1"
    fi

    if [ "${no_persistence}" != "0" ]; then
       cmake_custom_args+=" -DNO_PERSISTENCE=ON"
    fi

    cmake_custom_args+=" -DGMDB_VERSION=${GMDB_VERSION} -DCOVERAGE=${coverage} -DASAN=${asan} -DENABLE_CRASHPOINT=${enablecp} \
        -DPERF_SAMPLE_STAT=${perfsample} -DMEMTRACE=${memtrace} -DTRACEPC=${tracepc} -DTARGET_OS=${os} -DFUZZ=${fuzz} \
        -DEXTRAMSG=${extramsg} -DGCC_ONLY=${gcconly} -DEXPERIMENTAL=${experimental} -DDATALOG_UNINSTALL=${experimental} -DMODULARBUILD=${modular_build} \
        -DSECUREFIXBUF=${securefixbuf} -DCOMPILE_PATCH=${build_patch} -DUBSAN=${ubsan} -DTSAN=${tsan} \
        -DPNFHOSTTOOLS=${pnfhosttools} -DREFCNTTRACE=${refcnttrace} -DSTRIP=${build_strip} \
        -DFEATURES=${features} -DFUZZCOLLECT=${fuzz_collect_corpus} -DLIBFUZZER_RUN=${libfuzzer_run} -DMSG_DUMP=${msg_dump} \
        -DDIRECT_WRITE=${direct_write} -DTERMINAL=${terminal} -DSAFETY_GATE=${safety_gate} -DEXPERIMENTAL_GUANGQI=${experimental_guangqi} \
        -DPERF_INSTRUCTIONS=${perf_instructions} -DYANG_VALIDATION=${yang_validation} -DCFI=${cfi}  -DOPTIMIZE_SIZE=${optimize_size} \
        -DSTARTUP_TIME=${startup_time} -DEXAMPLES=${examples} -DHILIB=${hilib} -DWARM_REBOOT=${warm_reboot} \
        -DBOOT_FUZZ=${boot_fuzz}  -DMEMPOISON=${mempoison} -DHPE_SIMULATION=${hpe_simulation} -DINJECT=${inject} -DJSON_VERSION=${json_version} \
        -DACCELERATOR_MODE=${accelerator_mode} -DTS_MULTI_INST=${ts_multi_inst} -DENABLE_HAC=${enable_hac} -DBISHENGC=${bishengc} \
        -DSTREAM_MULTI_INST=${stream_multi_inst} -DBSC_LLVM=${bsc_llvm} -DUSE_GLIBC=${use_glibc} -DART_CONTAINER=${art_container} -DIDS_HAOTIAN=${ids_haotian} \
        -DV1_UTILS=${v1_utils} -DCOMPILE_PATCH_OBJ=${build_patch_obj} -DYANG_DEBUG=${yang_debug} -DMINI_BAG=${mini_bag} -DNO_LTO=${no_lto} \
        -DMINI_LOG=${mini_log} -DPATCH_C2H_RELOAD=${patch_c2h_reload} -DEXPERIMENTAL_NERGC=${experimental_nergc} -DV1_VIST=${v1_vist} -DNEED_TS_PATCH=${need_ts_patch} \
        -DMERGE_QUEUE=${merge_queue}"

    cmake_custom_args+=" ${g_cmake_feature_opt} ${g_cmake_single_so}"

    if [ "${os}" = "rtosv2" ] || [ "${os}" = "rtosv2x" ] || [ "${os}" = "hpe" ] || [ "${os}" = "rtos" ] || [ "${os}" = "umhpe" ] || ( [ "${os}" = "euler" ] && [ "${arch}" != "$(uname -p)" ] ); then
        env_toolchain
    fi
    if [ "${libfuzzer_run}" = "1" ]; then
        # 强制使用clang，设置环境变量
        env_toolchain_clang
    fi

    if [ "${tracepc}" = '1' ]; then
        pushd "${GMDB_DIR}"/test/fuzz/tracepc
        sh build_tracepc.sh
        popd
    fi

    if [ "${ubsan}" = '1' ]; then
        local ubsan_path=$("${CROSS_COMPILE}"gcc ${CROSS_COMPILE_FLAG} -print-file-name=libubsan.so)
        if [ ! -L "${ubsan_path}" ]; then
            echo "[ERROR]: Failed to locate valid ubsan lib."
            exit -1
        fi
    fi

    if [ "${tsan}" = '1' ]; then
        local tsan_path=$("${CROSS_COMPILE}"gcc ${CROSS_COMPILE_FLAG} -print-file-name=libtsan.so)
        if [ ! -L "${tsan_path}" ]; then
            echo "[ERROR]: Failed to locate valid tsan lib."
            exit -1
        fi
    fi

    if [ "${libfuzzer_run}" = "1" ]; then
        # libfuzzer强制使用clang的工具链
        toolchain_name="toolchain-aarch64-clang.cmake"
    else
        toolchain_name="toolchain-${arch}-${os}.cmake"
    fi

    if [ "${build_path}" != "0" ]; then
        TMP_DIR="${build_path}"/tmp
        BUILD_DIR="${build_path}"
        echo "modifying build tmp directory to ${TMP_DIR} ..."
    fi

    if [ "${output_path}" != "0" ]; then
        OUTPUT_DIR="${output_path}"
    fi
    cmake . ${cmake_custom_args} -DCMAKE_TOOLCHAIN_FILE=${GMDB_DIR}/cmake/${toolchain_name} -B ${TMP_DIR} --no-warn-unused-cli

    echo "[Info]: Set env successs, ARCH=${arch}, os=${os}, before make"
    cpu_count=$(grep "processor" /proc/cpuinfo -c)
    if [[ x"${cpu_count}" == x ]];then
        cpu_count=8
    fi

    if [ "x${MAKE_PARALLEL_NUM}" != "x" ] && [[ "${MAKE_PARALLEL_NUM}" =~ ^[0-9]+$ ]]; then
        # 允许直接通过外部变量设置，在一些特殊的场景控制并行任务数量
        cpu_count=$((${MAKE_PARALLEL_NUM}*2))
    fi

    # 生成compile_commands.json.unify文件用于加速门禁检查
    # 下面这条语句仅在CI中的codecheck阶段有效，并且运行结束后会直接退出脚本。
    generate_compile_commands_json_unify ${TMP_DIR}/compile_commands.json ${TMP_DIR}/compile_commands.json.unify

    if [ "${build_patch_obj}" != "0" ]; then
        # 使用所有环境变量
        build_patch_obj
    else
        cd "${TMP_DIR}"/
        build_haclib "${os}" "${arch}"
        build_openssl "${os}" "${arch}" "${bsc_llvm}" "${experimental_guangqi}"
        build_hilib "${os}" "${arch}"
        # db的cmake参数会影响三方库编译，现独立编译，不继承参数
        build_cjson "${os}" "${arch}"
        build_zlib "${os}" "${arch}"
        # 编译db
        cmake --build ${TMP_DIR} --parallel ${cpu_count}
        cmake --install ${TMP_DIR}
    fi

    if [ x"$os" = x"euler" ]; then
        if [ -e "$GMDB_DIR/build" ]; then
            # 数存昊天多so编译暂不检查
            if [ x"$single_so" != x"1" ] && [ x"$coverage" = x"0" ] && [ x"$ids_haotian" = x"0" ]; then
                # 编译之后，校验动态库之间link依赖是否符合预期
                python3 $GMDB_DIR/scripts/build_scripts/component_check/check_lib_link.py
	            if [ $? != 0 ] ; then echo "Terminating while check lib link failed..." >&2 ; exit 1 ; fi

                # 编译之后，检查源文件编译是否有重复包含或者漏编译的
                python3 $GMDB_DIR/scripts/build_scripts/component_check/check_build_source.py
                # 获取上个命令的运行结果，异常则shell中exit 1
	            if [ $? != 0 ] ; then echo "Terminating while check build source failed..." >&2 ; exit 1 ; fi
                :
            fi
        else
            echo "no build directory, please check"
            exit 1
        fi
    fi

    # 编译patch_obj依赖源文件名无重复，需要校验so包含的文件名唯一
    # 当前仅光启使用该编译方式，光启场景下才校验
    if [ "${experimental_nergc}" != "0" ] || [ "${experimental_guangqi}" != "0" ] || [ "${build_patch_obj}" != "0" ]; then
        [ "${build_type}" == 'release' ] && [ "${inject}" == "0" ] && check_name_uniq
    fi

    if [[ "${asan}" == '1' ]];then
        if [ "${os}" = "rtosv2" ] || [ "${os}" = "hpe" ];then
            "${ASAN_INIT_PATH}" off
        else
            "${GMDB_DIR}"/scripts/ASAN_init.sh off
        fi
    fi
    clean_grammar_scanner ${terminal}
    clean_grammar_scanner_ts ${terminal}
    clean_grammar_scanner_stream ${terminal}
    clean_grammar_scanner_sql ${terminal}
    clean_grammar_scanner_sql_terminal ${terminal}
    clean_grammar_scanner_gql ${terminal}
    clean_grammar_scanner_gql_terminal ${terminal}
}

build_db $@
