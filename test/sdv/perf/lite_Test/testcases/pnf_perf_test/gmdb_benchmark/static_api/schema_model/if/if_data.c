
#include "if_data.h"
#include "static_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
#include "db_if.h"
#include "db_trunk.h"
#include "cpu_cycles.h"
#include <pthread.h>

static pthread_mutex_t g_if_mutex;

DB_DEF_CPU_CYCLES(if_CreateObj);
DB_DEF_CPU_CYCLES(if_Release);
DB_DEF_CPU_CYCLES(if_Read);
DB_DEF_CPU_CYCLES(if_ResetObj);
DB_DEF_CPU_CYCLES(if_CreateChild);
DB_DEF_CPU_CYCLES(if_CreateIndex);
DB_DEF_CPU_CYCLES(if_GetFirst);
DB_DEF_CPU_CYCLES(if_GetSecond);
DB_DEF_CPU_CYCLES(if_SetFirst);
DB_DEF_CPU_CYCLES(if_SetSecond);
DB_DEF_CPU_CYCLES(if_CreateRootIter);
DB_DEF_CPU_CYCLES(if_RootIterNext);
DB_DEF_CPU_CYCLES(if_CreateIterator);
DB_DEF_CPU_CYCLES(if_IteratorNext);
DB_DEF_CPU_CYCLES(if_GetSuperField);
DB_DEF_CPU_CYCLES(if_GetAllField);
DB_DEF_CPU_CYCLES(if_SetAllField);

p_static_schema_model_t g_if_model = NULL;

static_if_sub_entry_t g_if_subid[LITE_SUBID_MAX_NUM];

static_model_sub_tbl_entry_t g_if_sub_func_tbl[] = {
    [IF_SUB_TYPE_0] = { .id = IF_SUB_TYPE_0, .sub_spec_func = static_if_IfSubScribeForTbTp },
    [IF_SUB_TYPE_1] = { .id = IF_SUB_TYPE_1, .sub_spec_func = static_if_IfSubScribeForL3Info },
    [IF_SUB_TYPE_2] = { .id = IF_SUB_TYPE_2, .sub_spec_func = static_if_SubPortSwitchChange },
    [IF_SUB_TYPE_3] = { .id = IF_SUB_TYPE_3, .sub_spec_func = static_if_SubFwdIfTypeChange },
    [IF_SUB_TYPE_4] = { .id = IF_SUB_TYPE_4, .sub_spec_func = static_if_SubTbTpChange },
    [IF_SUB_TYPE_5] = { .id = IF_SUB_TYPE_5, .sub_spec_func = BIGHEART_DB_SubIfTable },
    [IF_SUB_TYPE_6] = { .id = IF_SUB_TYPE_6, .sub_spec_func = static_if_SubIfCreateEvt },
    [IF_SUB_TYPE_7] = { .id = IF_SUB_TYPE_7, .sub_spec_func = static_if_SubIfDataSetMacEvt },
    [IF_SUB_TYPE_8] = { .id = IF_SUB_TYPE_8, .sub_spec_func = static_if_SubIfDataSetIpv4MtuEvt },
    [IF_SUB_TYPE_9] = { .id = IF_SUB_TYPE_9, .sub_spec_func = static_if_SubIfDataSetIpv6MtuEvt },
    [IF_SUB_TYPE_10] = { .id = IF_SUB_TYPE_10, .sub_spec_func = static_if_SubIfShutActionEvt },
    [IF_SUB_TYPE_11] = { .id = IF_SUB_TYPE_11, .sub_spec_func = static_if_SubIfNotifyEvt },
    [IF_SUB_TYPE_12] = { .id = IF_SUB_TYPE_12, .sub_spec_func = static_if_SubIfDeleteEvt },
    [IF_SUB_TYPE_13] = { .id = IF_SUB_TYPE_13, .sub_spec_func = static_if_SubIfInheritEvt },
    [IF_SUB_TYPE_14] = { .id = IF_SUB_TYPE_14, .sub_spec_func = static_if_SubIfRemoveEvt },
    [IF_SUB_TYPE_15] = { .id = IF_SUB_TYPE_15, .sub_spec_func = static_if_IfLinkUpDownEvt },
    [IF_SUB_TYPE_16] = { .id = IF_SUB_TYPE_16, .sub_spec_func = static_if_IoctlLink2PhyEvt },
    [IF_SUB_TYPE_17] = { .id = IF_SUB_TYPE_17, .sub_spec_func = static_if_IoctlPhy2LinkEvt },
    [IF_SUB_TYPE_18] = { .id = IF_SUB_TYPE_18, .sub_spec_func = static_if_IoctlLink2LinkEvt },
    [IF_SUB_TYPE_19] = { .id = IF_SUB_TYPE_19, .sub_spec_func = static_if_L2ifSubPortIoctlEvt },
    [IF_SUB_TYPE_20] = { .id = IF_SUB_TYPE_20, .sub_spec_func = static_if_L2ifSubL2L3SwitchEvt },
    [IF_SUB_TYPE_21] = { .id = IF_SUB_TYPE_21, .sub_spec_func = static_if_SubscribeTrunkState }, // trunk
    [IF_SUB_TYPE_22] = { .id = IF_SUB_TYPE_22, .sub_spec_func = static_if_SubscribeTrunkMemberStateChangeEvent }, // trunk
    [IF_SUB_TYPE_23] = { .id = IF_SUB_TYPE_23, .sub_spec_func = static_if_SubscribeTrunkMemberOpration }, // l2 node
    [IF_SUB_TYPE_KEY] = { .id = IF_SUB_TYPE_KEY, .sub_spec_func = static_if_SubscribePushKey },
    [IF_SUB_TYPE_CUR_DATA] = { .id = IF_SUB_TYPE_CUR_DATA, .sub_spec_func = static_if_SubscribePushCurData },
    [IF_SUB_TYPE_OLD_DATA] = { .id = IF_SUB_TYPE_OLD_DATA, .sub_spec_func = static_if_SubscribePushOldData },
    [IF_SUB_TYPE_CUR_AND_OLD_DATA] = { .id = IF_SUB_TYPE_CUR_AND_OLD_DATA, .sub_spec_func = static_if_SubscribePushCurAndOldData }
};

status_t static_if_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                        db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    DB_START_TEST_CPU_CYCLES(if_CreateObj);
    ret = db_create_if_obj(obj_type, conn_type, &obj);
    DB_STOP_TEST_CPU_CYCLES(if_CreateObj);
    *object = obj;
    return ret;
}

status_t static_if_create_db_obj_spec_conn_func(db_object_type obj_type, db_connect_t conn,
                                                  db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_if_obj_specific_conn(obj_type, conn, &obj);           
    CHECK_OK_RET(ret, "Create if spec conn object.");

    *object = obj;
    return ret;
}

void static_if_release_db_obj_func(db_object object)
{
    DB_START_TEST_CPU_CYCLES(if_Release);
    db_release_if_object(object);
    DB_STOP_TEST_CPU_CYCLES(if_Release);
}

status_t static_if_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    DB_START_TEST_CPU_CYCLES(if_ResetObj);
    ret = db_reset_if_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(if_ResetObj);

    return ret;
}

status_t static_if_create_db_batch_obj_func(db_conn_type type, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    DB_START_TEST_CPU_CYCLES(if_CreateObj);
    ret = db_create_if_obj(BATCH_OPERATION, type, &obj);
    DB_STOP_TEST_CPU_CYCLES(if_CreateObj);
    *object = obj;
    return ret;
}

status_t static_if_reset_db_bobj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    DB_START_TEST_CPU_CYCLES(if_ResetObj);
    ret = db_reset_if_batch_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(if_ResetObj);

    return ret;
}

status_t static_if_primary_key_malloc_func(void **key)
{
    *key = malloc(sizeof(db_if_key_t));
    return 0;
}

void static_if_primary_key_free_func(void *key)
{
    free(key);
}

status_t static_if_struct_data_malloc_func(void **data, uint32_t *length)
{
#if _SPECIAL_COMPLEX
    *length = sizeof(if_struct_t);
    *data = malloc(*length);
    return 0;
#else 
    return 0;
#endif
}

void static_if_struct_data_free_func(void *data)
{
#if _SPECIAL_COMPLEX
    free(data);
#else 
    return;
#endif
}

status_t static_if_get_field_func(db_object object, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    unsigned int ifindex;
    char if_name[64];
    unsigned int vrid;
    unsigned int if_type;
    unsigned int shutdown;
    unsigned int linkup;
    unsigned int tbtp;
    unsigned int tb;
    unsigned int tp;
    unsigned int port_switch;
    unsigned int fwdIfType;
    unsigned char mac[6];
    unsigned short ipv4_mtu;
    unsigned short ipv6_mtu;
    unsigned int on_board;
    unsigned int lagid;
    uint64_t speed;
    uint32_t get_field_uint32;

    uint32_t if_macaddress_len = 0;
    uint32_t if_name_len = 0;
    int8_t *macAddress = NULL;
    int8_t *name = NULL;
    db_child_iterator child_iterator = NULL;
    unsigned int trunk_id;
    unsigned int vlan_id;
    unsigned int l2_portindex;
    unsigned int tunnel_id;
    unsigned int vsi;
    unsigned int id;
    if (read_field_cnt <= 1) {
        ret = db_get_if_linkup(object, &linkup);
        CHECK_OK_RET(ret, "db_get_if_linkup");
        return ret;
    }
    
    (void)db_get_if_ifindex(object, &ifindex);
    DB_START_TEST_CPU_CYCLES(if_GetFirst);
    (void)db_get_if_name(object, (int8_t **)&name, &if_name_len);
    DB_STOP_TEST_CPU_CYCLES(if_GetFirst);
    (void)memcpy_s(if_name, if_name_len, name, if_name_len);
    (void)db_get_if_vrid(object, &vrid);
    (void)db_get_if_if_type(object, (uint32_t *)&if_type);
    (void)db_get_if_shutdown(object, &shutdown);
    (void)db_get_if_linkup(object, &linkup);
    (void)db_get_if_tbtp(object, &tbtp);
    (void)db_get_if_tb(object, &tb);
    (void)db_get_if_tp(object, &tp);
    (void)db_get_if_port_switch(object, &port_switch);
    (void)db_get_if_ipv4_mtu(object, &ipv4_mtu);
    (void)db_get_if_ipv4_enable(object, &ipv4_mtu);
    (void)db_get_if_ipv6_mtu(object, &ipv6_mtu);
    (void)db_get_if_ipv6_enable(object, &ipv4_mtu);    
    (void)db_get_if_on_board(object, &on_board);
    (void)db_get_if_forwardType(object, &fwdIfType);
    (void)db_get_if_macAddress(object, (int8_t **)&macAddress, &if_macaddress_len);
    (void)memcpy_s(mac, if_macaddress_len, macAddress, if_macaddress_len);
    (void)db_get_if_lagid(object, &lagid);
    (void)db_get_if_hppsvcflg(object, &lagid);
    (void)db_get_if_error_down(object, &lagid);
    (void)db_get_if_speed(object, &speed);

    (void)db_get_if_link_protocol(object, &get_field_uint32);
    (void)db_get_if_vrf_index(object, &get_field_uint32);
    (void)db_get_if_port_group_id(object, &get_field_uint32);
    (void)db_get_if_if_group_id(object, &get_field_uint32);
    (void)db_get_if_if_df(object, &get_field_uint32);
    (void)db_get_if_encap_type(object, &get_field_uint32);
    (void)db_get_if_is_subif(object, &get_field_uint32);
    (void)db_get_if_mainifindex(object, &get_field_uint32);
    (void)db_get_if_logicTB(object, &get_field_uint32);
    (void)db_get_if_logicTP(object, &get_field_uint32);
    (void)db_get_if_vlandomain(object, &get_field_uint32);
    (void)db_get_if_coreId(object, &get_field_uint32);

    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    (void)db_create_child_node(object, NULL, "l2", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);
    DB_START_TEST_CPU_CYCLES(if_GetSecond);
    (void)db_get_if_l2_trunk_id(child_iterator, &trunk_id);
    DB_STOP_TEST_CPU_CYCLES(if_GetSecond);
    (void)db_get_if_l2_vlan_id(child_iterator, &vlan_id);
    (void)db_get_if_l2_vsi(child_iterator, &vsi);
    (void)db_get_if_l2_l2_portindex(child_iterator, &l2_portindex);
    (void)db_get_if_l2_tunnel_id(child_iterator, &tunnel_id);
    (void)db_get_if_l2_bd_id(child_iterator, &tunnel_id);

    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    (void)db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);
    (void)db_get_if_dev_dev_id(child_iterator, &id);
    (void)db_get_if_dev_chassis_id(child_iterator, &id);
    (void)db_get_if_dev_card_id(child_iterator, &id);
    (void)db_get_if_dev_port_id(child_iterator, &id);
    (void)db_get_if_dev_slot_id(child_iterator, &id);
    (void)db_get_if_dev_unit_id(child_iterator, &id);
    (void)db_get_if_dev_port_num(child_iterator, &id);

    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    (void)db_create_child_node(object, NULL, "port", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);
    (void)db_get_if_port_speed(child_iterator, &speed);
    (void)db_get_if_port_duplex(child_iterator, &id);
    (void)db_get_if_port_flow_control(child_iterator, &id);
    (void)db_get_if_port_phy_type(child_iterator, &id);
    (void)db_get_if_port_jumbo(child_iterator, &id);
    (void)db_get_if_port_baud(child_iterator, &id);
    (void)db_get_if_port_rmon(child_iterator, &id);
    (void)db_get_if_port_phy_link(child_iterator, &id);
    (void)db_get_if_port_if_mib(child_iterator, &id);
    (void)db_get_if_port_on_board(child_iterator, &id);

    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    (void)db_create_child_node(object, NULL, "ifm", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);
    int8_t *szBuf = NULL;
    unsigned int ulLen = 0;
    int8_t pszBuf[64] = { 0 };
    uint32_t simple_name;
    uint32_t is_configure;
    (void)db_get_if_ifm_simple_name(child_iterator, &simple_name);
    (void)db_get_if_ifm_is_configure(child_iterator, &is_configure);
    (void)db_get_if_ifm_description(child_iterator, &szBuf, &ulLen);
    memcpy_s(pszBuf, ulLen, szBuf, ulLen);
    return ret;
}

status_t static_if_convert_obj_to_struct(db_object object, void *data)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_struct_t *obj_struct = (if_struct_t *)data;
    int8_t *buf_name = NULL;
    int8_t *buf_mac = NULL;
    int8_t *buf_desc = NULL;
    uint32_t buf_len = 0;
    db_child_iterator child_iterator = NULL;
    (void)db_get_if_ifindex(object, &(obj_struct->ifindex));
    DB_START_TEST_CPU_CYCLES(if_GetFirst);
    (void)db_get_if_name(object, (int8_t **)&buf_name, &buf_len);
    DB_STOP_TEST_CPU_CYCLES(if_GetFirst);
    (void)memcpy_s(obj_struct->name, buf_len, buf_name, buf_len);
    (void)db_get_if_vrid(object, &(obj_struct->vrid));
    (void)db_get_if_if_type(object, (uint32_t *)&(obj_struct->if_type));
    (void)db_get_if_shutdown(object, &(obj_struct->shutdown));
    (void)db_get_if_linkup(object, &(obj_struct->linkup));
    (void)db_get_if_tbtp(object, &(obj_struct->tbtp));
    (void)db_get_if_tb(object, &(obj_struct->tb));
    (void)db_get_if_tp(object, &(obj_struct->tp));
    (void)db_get_if_port_switch(object, &(obj_struct->port_switch));
    (void)db_get_if_forwardType(object, &(obj_struct->forwardType));
    (void)db_get_if_macAddress(object, (int8_t **)&buf_mac, &buf_len);
    (void)memcpy_s(obj_struct->macAddress, buf_len, buf_mac, buf_len);
    (void)db_get_if_ipv4_mtu(object, &(obj_struct->ipv4_mtu));
    (void)db_get_if_ipv6_mtu(object, &(obj_struct->ipv6_mtu));
    (void)db_get_if_on_board(object, &(obj_struct->on_board));
    (void)db_get_if_lagid(object, &(obj_struct->lagid));

    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    ret = db_create_child_node(object, NULL, "l2", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);
    if (ret) {
        return ret;
    }
    obj_struct->l2_flag = 1;
    DB_START_TEST_CPU_CYCLES(if_GetSecond);
    (void)db_get_if_l2_trunk_id(child_iterator, &(obj_struct->l2->trunk_id));
    DB_STOP_TEST_CPU_CYCLES(if_GetSecond);
    (void)db_get_if_l2_vlan_id(child_iterator, &(obj_struct->l2->vlan_id));
    (void)db_get_if_l2_vsi(child_iterator, &(obj_struct->l2->vsi));
    (void)db_get_if_l2_l2_portindex(child_iterator, &(obj_struct->l2->l2_portindex));
    (void)db_get_if_l2_tunnel_id(child_iterator, &(obj_struct->l2->tunnel_id));

    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    ret = db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);
    if (ret) {
        return ret;
    }
    obj_struct->dev_flag = 1;
    (void)db_get_if_dev_dev_id(child_iterator, &(obj_struct->dev->dev_id));
    (void)db_get_if_dev_chassis_id(child_iterator, &(obj_struct->dev->chassis_id));
    (void)db_get_if_dev_card_id(child_iterator, &(obj_struct->dev->card_id));
    (void)db_get_if_dev_port_id(child_iterator, &(obj_struct->dev->port_id));
    (void)db_get_if_dev_slot_id(child_iterator, &(obj_struct->dev->slot_id));
    (void)db_get_if_dev_unit_id(child_iterator, &(obj_struct->dev->unit_id));

    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    ret = db_create_child_node(object, NULL, "ifm", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);
    if (ret) {
        return ret;
    }
    obj_struct->ifm_flag = 1;
    (void)db_get_if_ifm_simple_name(child_iterator, &(obj_struct->ifm->simple_name));
    (void)db_get_if_ifm_is_configure(child_iterator, &(obj_struct->ifm->is_configure));
    (void)db_get_if_ifm_description(child_iterator, &buf_desc, &buf_len);
    if (sizeof(obj_struct->ifm->description) != buf_len) {
        ret = -1;
        printf("get len : %u, expect %lu", buf_len, sizeof(obj_struct->ifm->description));
        return ret;
    }
    memcpy_s(obj_struct->ifm->description, buf_len, buf_desc, buf_len);

#endif
    return ret;
}

status_t setIfDevNode(db_object object, void *obj_struct, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX    
    if_dev_t* dev = (if_dev_t*)obj_struct;
    dev->dev_id = uiVrIndex;
    dev->chassis_id = uiVrIndex;
    dev->slot_id = uiVrIndex;
    dev->card_id = uiVrIndex;
    dev->unit_id = uiVrIndex;
    dev->port_id = uiVrIndex;
    return 0;

#else
    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    ret = db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);

    ret = db_set_if_dev_dev_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_dev_id");
    ret = db_set_if_dev_chassis_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_chassis_id");
    ret = db_set_if_dev_card_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_card_id");
    ret = db_set_if_dev_port_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_port_id");
    ret = db_set_if_dev_slot_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_slot_id");
    ret = db_set_if_dev_unit_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_unit_id");
    ret = db_set_if_dev_port_num(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_dev_port_num");    
    return ret;
#endif
    return ret;
}

status_t setIfL2Node(db_object object, void *obj_struct, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_l2_t* l2 = (if_l2_t*)obj_struct;
    l2->trunk_id = uiVrIndex;
    l2->vlan_id = uiVrIndex;
    l2->l2_portindex = uiVrIndex;
    l2->vsi = uiVrIndex;
    l2->tunnel_id = uiVrIndex;
    return 0;

#else
    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    ret = db_create_child_node(object, NULL, "l2", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);

    ret = db_set_if_l2_trunk_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_trunk_id");
    ret = db_set_if_l2_vlan_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_vlan_id");
    ret = db_set_if_l2_l2_portindex(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_l2_portindex");
    ret = db_set_if_l2_vsi(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_vsi");
    ret = db_set_if_l2_tunnel_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_tunnel_id");
    ret = db_set_if_l2_bd_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_l2_bd_id");    
    return ret;
#endif
    return ret;
}

status_t setIfIfmNode(db_object object, void *obj_struct, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_ifm_t* ifm = (if_ifm_t*)obj_struct;
    ifm->simple_name = uiVrIndex;
    // int8_t arr[64] = {0};
    // memset(arr, 0, 64);
    // memcpy(ifm->description, (int8_t *)(arr), 64);
    memcpy(ifm->description, (int8_t *)(ZERO_64_BYTES), 64);
    ifm->is_configure = uiVrIndex;
    ifm->main_ifindex = uiVrIndex;
    ifm->sub_max_num = uiVrIndex;
    ifm->sub_curr_num = uiVrIndex;
    ifm->error_down = uiVrIndex;
    ifm->statistic = uiVrIndex;
    ifm->vsys_id = uiVrIndex;
    ifm->zone_id = uiVrIndex;
    ifm->last_up_time = uiVrIndex;
    ifm->last_down_time = uiVrIndex;
    return 0;

#else
    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    ret = db_create_child_node(object, NULL, "ifm", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);

    ret = db_set_if_ifm_simple_name(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_simple_name");
    ret = db_set_if_ifm_description(child_iterator, (int8_t *)(ZERO_64_BYTES), 64);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_description");
    ret = db_set_if_ifm_is_configure(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_is_configure");
    ret = db_set_if_ifm_main_ifindex(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_main_ifindex");
    ret = db_set_if_ifm_sub_max_num(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_sub_max_num");
    ret = db_set_if_ifm_sub_curr_num(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_sub_curr_num");
    ret = db_set_if_ifm_error_down(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_error_down");
    ret = db_set_if_ifm_statistic(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_statistic");
    ret = db_set_if_ifm_vsys_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_vsys_id");
    ret = db_set_if_ifm_zone_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_zone_id");
    ret = db_set_if_ifm_last_up_time(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_last_up_time");
    ret = db_set_if_ifm_last_down_time(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_last_down_time");
    return ret;
#endif

    return ret;
}


status_t setIfPortNode(db_object object, void *obj_struct, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
     

#if _SPECIAL_COMPLEX
    if_port_t* port = (if_port_t*)obj_struct;
    port->speed = uiVrIndex;
    port->duplex = uiVrIndex;
    port->flow_control = uiVrIndex;
    port->phy_type = uiVrIndex;
    port->jumbo = uiVrIndex;
    port->baud = uiVrIndex;
    port->rmon = uiVrIndex;
    port->phy_link = uiVrIndex;
    port->if_mib = uiVrIndex;
    port->on_board = uiVrIndex;
    return 0;

#else

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    ret = db_create_child_node(object, NULL, "port", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);

    ret = db_set_if_port_speed(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_speed");
    ret = db_set_if_port_duplex(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_duplex");
    ret = db_set_if_port_flow_control(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_flow_control");
    ret = db_set_if_port_phy_type(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_phy_type");
    ret = db_set_if_port_jumbo(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_jumbo");
    ret = db_set_if_port_baud(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_baud");
    ret = db_set_if_port_rmon(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_rmon");
    ret = db_set_if_port_phy_link(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_phy_link");
    ret = db_set_if_port_if_mib(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_if_mib");    
    ret = db_set_if_port_on_board(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_port_on_board");
    
    return ret;
#endif

    return ret;
}


status_t static_if_set_field_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
    int8_t arr[64] = { 0 };
    memcpy(arr, (int8_t *)&uiVrIndex, 8);
    // ret = db_set_if_ifindex(object, (uint32_t)(uiVrIndex & ((uint32_t)~0)));
    // CHECK_OK_RET_DEBUG(ret, "db_set_if_ifindex");
    ret = db_set_if_name(object, (int8_t *)arr, 64);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_name");
    ret = db_set_if_vrid(object, 0);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_vrid");
    ret = db_set_if_if_type(object, 0);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_if_type");
    ret = db_set_if_shutdown(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_shutdown");
    ret = db_set_if_linkup(object, 0);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_linkup");
    ret = db_set_if_tbtp(object, 0xffffffff);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tbtp");
    ret = db_set_if_tb(object, 0xffffffff);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tb");
    ret = db_set_if_tp(object, 0xffffffff);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tp");
    ret = db_set_if_port_switch(object, 0);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tp");
    ret = db_set_if_forwardType(object, 0);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tp");
    ret = db_set_if_macAddress(object, (int8_t *)arr, 6);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_l2_l3_flag");
    ret = db_set_if_ipv4_mtu(object, 1500);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_ipv4_mtu");
    ret = db_set_if_ipv4_enable(object, 1);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_ipv4_enable");    
    ret = db_set_if_ipv6_mtu(object, 0xffff);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_ipv6_mtu");
    ret = db_set_if_ipv6_enable(object, 1);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_ipv6_mtu");    
    ret = db_set_if_on_board(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_on_board");
    ret = db_set_if_lagid(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");
    ret = db_set_if_hppsvcflg(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");
    ret = db_set_if_error_down(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");
    ret = db_set_if_speed(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");

    ret = db_set_if_link_protocol(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_link_protocol");
    ret = db_set_if_vrf_index(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_vrf_index");
    ret = db_set_if_port_group_id(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_port_group_id");
    ret = db_set_if_if_group_id(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_if_group_id");
    ret = db_set_if_if_df(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_if_df");
    ret = db_set_if_encap_type(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_encap_type");
    ret = db_set_if_is_subif(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_is_subif");
    ret = db_set_if_mainifindex(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_mainifindex");
    ret = db_set_if_logicTB(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_logicTB");
    ret = db_set_if_logicTP(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_logicTP");
    ret = db_set_if_vlandomain(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_vlandomain");
    ret = db_set_if_coreId(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_coreId");

    ret = setIfDevNode(object, NULL, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set  dev node");

    ret = setIfL2Node(object, NULL, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set  l2 node");

    ret = setIfIfmNode(object, NULL, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set  ifm node");

    ret = setIfPortNode(object, NULL, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set  port node");
  
    return ret;
}

int static_if_set_obj_struct_data(void *data, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
#if _SPECIAL_COMPLEX
    if_struct_t *obj_struct = (if_struct_t *)data;
    
    int8_t arr[64] = { 0 };
    memcpy(arr, (int8_t *)&uiVrIndex, 8);
    obj_struct->ifindex = (uint32_t)(uiVrIndex & ((uint32_t)~0));
    memcpy(obj_struct->name, (int8_t *)arr, 64);
    obj_struct->vrid = 0;
    obj_struct->if_type = 0;
    obj_struct->shutdown = uiVrIndex;
    obj_struct->linkup = 0;
    obj_struct->tbtp = 0xffffffff;
    obj_struct->tb = 0xffffffff;
    obj_struct->tp = 0xffffffff;
    obj_struct->port_switch = 0;
    obj_struct->forwardType = 0;
    memcpy(obj_struct->macAddress, (int8_t *)arr, 6);
    obj_struct->ipv4_mtu = 1500;
    obj_struct->ipv6_mtu = 0xffff;
    obj_struct->on_board = uiVrIndex;
    obj_struct->lagid = uiVrIndex;

    if (array > 0) {
        obj_struct->dev_flag = 1;
        (void)setIfDevNode(NULL, obj_struct->dev, uiVrIndex);
        obj_struct->l2_flag = 1;
        (void)setIfL2Node(NULL, obj_struct->l2, uiVrIndex);
        obj_struct->ifm_flag = 1;
        (void)setIfIfmNode(NULL, obj_struct->ifm, uiVrIndex);
    } 

    obj_struct->port_flag = 0;
#endif

    return 0;
}

int static_if_check_field_value(void *data, uint64_t check_value, uint32_t array, uint32_t thread_local_num)
{
#ifdef TEST_DEBUG
#if _SPECIAL_COMPLEX
    if_struct_t expect_data;
    memset(&expect_data, 0, sizeof(expect_data));
    if_dev_t dev = {0};
    if_l2_t l2 = {0};
    if_ifm_t ifm = {0};
    if_port_t port = {0};
    expect_data.dev = &dev;
    expect_data.l2 = &l2;
    expect_data.ifm = &ifm;
    expect_data.port = &port;
    (void)static_if_set_obj_struct_data(&expect_data, check_value, array, thread_local_num);

    if_struct_t *obj_struct = (if_struct_t *)data;

    CHECK_INT_EQUAL_DEBUG(obj_struct->ifindex, expect_data.ifindex);
    CHECK_ARRAY_EQUAL_DEBUG(obj_struct->name, expect_data.name, sizeof(obj_struct->name));
    // CHECK_INT_EQUAL_DEBUG(obj_struct->vrid, expect_data.vrid);
    CHECK_INT_EQUAL_DEBUG(obj_struct->if_type, expect_data.if_type);
    CHECK_INT_EQUAL_DEBUG(obj_struct->shutdown, expect_data.shutdown);

    CHECK_INT_EQUAL_DEBUG(obj_struct->linkup, expect_data.linkup);
    CHECK_INT_EQUAL_DEBUG(obj_struct->tbtp, expect_data.tbtp);
    CHECK_INT_EQUAL_DEBUG(obj_struct->tb, expect_data.tb);
    CHECK_INT_EQUAL_DEBUG(obj_struct->tp, expect_data.tp);

    CHECK_INT_EQUAL_DEBUG(obj_struct->port_switch, expect_data.port_switch);
    CHECK_INT_EQUAL_DEBUG(obj_struct->forwardType, expect_data.forwardType);
    CHECK_ARRAY_EQUAL_DEBUG(obj_struct->macAddress, expect_data.macAddress, sizeof(obj_struct->macAddress));
    CHECK_INT_EQUAL_DEBUG(obj_struct->ipv4_mtu, expect_data.ipv4_mtu);
    CHECK_INT_EQUAL_DEBUG(obj_struct->ipv6_mtu, expect_data.ipv6_mtu);
    CHECK_INT_EQUAL_DEBUG(obj_struct->on_board, expect_data.on_board);
    // CHECK_INT_EQUAL_DEBUG(obj_struct->lagid, expect_data.lagid);
    CHECK_INT_EQUAL_DEBUG(obj_struct->dev_flag, expect_data.dev_flag);
    CHECK_INT_EQUAL_DEBUG(obj_struct->l2_flag, expect_data.l2_flag);
    CHECK_INT_EQUAL_DEBUG(obj_struct->ifm_flag, expect_data.ifm_flag);
    CHECK_INT_EQUAL_DEBUG(obj_struct->port_flag, expect_data.port_flag);

    if (obj_struct->dev_flag) {
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->dev_id, expect_data.dev->dev_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->chassis_id, expect_data.dev->chassis_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->slot_id, expect_data.dev->slot_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->card_id, expect_data.dev->card_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->unit_id, expect_data.dev->unit_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->port_id, expect_data.dev->port_id);
    }

    if (obj_struct->l2_flag) {
        // CHECK_INT_EQUAL_DEBUG(obj_struct->l2->trunk_id, expect_data.l2->trunk_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->l2->vlan_id, expect_data.l2->vlan_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->l2->l2_portindex, expect_data.l2->l2_portindex);
        CHECK_INT_EQUAL_DEBUG(obj_struct->l2->vsi, expect_data.l2->vsi);
        CHECK_INT_EQUAL_DEBUG(obj_struct->l2->tunnel_id, expect_data.l2->tunnel_id);
    }
    
    if (obj_struct->ifm_flag) {
        CHECK_INT_EQUAL_DEBUG(obj_struct->ifm->simple_name, expect_data.ifm->simple_name);
        CHECK_ARRAY_EQUAL_DEBUG(obj_struct->ifm->description, expect_data.ifm->description, DB_static_if_DESCRIPTION_LEN);
        CHECK_INT_EQUAL_DEBUG(obj_struct->ifm->is_configure, expect_data.ifm->is_configure);
    }

    if (obj_struct->port_flag) {
        return -1;
    }

#endif
#endif

    return STATUS_OK;
}

status_t static_if_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_struct_t obj_struct;
    memset(&obj_struct, 0, sizeof(obj_struct));
    if_dev_t dev = {0};
    if_l2_t l2 = {0};
    if_ifm_t ifm = {0};
    if_port_t port = {0};
    obj_struct.dev = &dev;
    obj_struct.l2 = &l2;
    obj_struct.ifm = &ifm;
    obj_struct.port = &port;
    
    (void)static_if_set_obj_struct_data(&obj_struct, uiVrIndex, array, thread_local_num);

set_again:
    ret = db_set_if_all_fields(object, &obj_struct, sizeof(obj_struct)); // root object struct size
    if (ret == STATUS_QUEUE_EMPTY) {
        usleep(50);
        goto set_again;
    }
    return ret;

#else
    ret = db_set_if_ifindex(object, (uint32_t)(uiVrIndex & ((uint32_t)~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_ifindex");
    ret = static_if_set_field_func(object, uiVrIndex, array, thread_local_num);
    return ret;

#endif

    return ret;
}

status_t static_if_get_field_by_key_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_if_key_t key_data = {0};
    key_info.index_id = DB_IF_IF_PK_ID;
    key_info.key_len = sizeof(key_data);
    key_data.ifindex = (uint32_t)(uiVrIndex & ((uint32_t)~0));
    DB_START_TEST_CPU_CYCLES(if_Read);
    ret = db_read_obj(object, &key_info, &key_data);
    DB_STOP_TEST_CPU_CYCLES(if_Read);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "db_read_obj");
        return ret;
    }
    ret = static_if_get_field_func(object, read_field_cnt);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "static_if_get_field_func");
        return ret;
    }
    return ret;
}

status_t static_if_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, void *key_info, void *key_data)
{
    db_if_key_t *pri_key_data = (db_if_key_t *)key_data;
    db_key_info_t *pri_key_info = (db_key_info_t *)key_info;
    memset(pri_key_data, 0, sizeof(db_if_key_t));
    memset(pri_key_info, 0, sizeof(db_key_info_t));
    pri_key_info->index_id = DB_IF_IF_PK_ID;
    pri_key_info->key_len = sizeof(db_if_key_t);
    pri_key_data->ifindex = (uint32_t)(uiVrIndex & ((uint32_t)~0));
    return 0;
}

status_t static_if_update_bykey_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, uint32_t delta_value, uint32_t field_cnt)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_if_key_t key_data = {0};
    // key_info.index_id = DB_IF_IF_PK_ID;
    // key_info.key_len = sizeof(key_data);
    // key_data.ifindex = (uint32_t)(uiVrIndex & ((uint32_t)~0));
    (void)static_if_key_set_func(object, uiVrIndex, thread_local_num, &key_info, &key_data);
    uiVrIndex += delta_value;
    if (field_cnt <= 1) {
        ret = db_set_if_linkup(object, uiVrIndex);
        CHECK_OK_RET(ret, "db_set_if_linkup");
        return ret;
    } else if (field_cnt <= 2) {
        ret = db_set_if_shutdown(object, uiVrIndex);
        CHECK_OK_RET(ret, "db_set_if_shutdown");
        ret = db_set_if_linkup(object, uiVrIndex);
        CHECK_OK_RET(ret, "db_set_if_linkup");
        return ret;
    } else {}

    int8_t arr[64] = { 0 };
    memcpy(arr, (int8_t *)&uiVrIndex, 8);

    ret = db_set_if_name(object, (int8_t *)arr, 64);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_name");
    ret = db_set_if_vrid(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_vrid");
    ret = db_set_if_if_type(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_if_type");
    ret = db_set_if_shutdown(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_shutdown");
    ret = db_set_if_linkup(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_linkup");
    ret = db_set_if_tbtp(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tbtp");
    ret = db_set_if_tb(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tb");
    ret = db_set_if_tp(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tp");
    ret = db_set_if_port_switch(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tp");
    ret = db_set_if_forwardType(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_tp");
    ret = db_set_if_macAddress(object, (int8_t *)arr, 6);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_l2_l3_flag");
    ret = db_set_if_ipv4_mtu(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_linkup");
    ret = db_set_if_ipv6_mtu(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_ipv6_mtu");
    ret = db_set_if_on_board(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_on_board");
    ret = db_set_if_lagid(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");

    db_child_iterator dev_child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    // ret = db_create_child_node(object, NULL, "dev", &key_info, &key_data, &dev_child_iterator);
    ret = db_create_child_node(object, NULL, "dev", NULL, NULL, &dev_child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);

    ret = db_set_if_dev_dev_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_dev_id");
    ret = db_set_if_dev_chassis_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_chassis_id");
    ret = db_set_if_dev_card_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_card_id");
    ret = db_set_if_dev_port_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_port_id");
    ret = db_set_if_dev_slot_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_slot_id");
    ret = db_set_if_dev_unit_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_unit_id");

    
    db_child_iterator l2_child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    // ret = db_create_child_node(object, NULL, "l2", &key_info, &key_data, &l2_child_iterator);
    ret = db_create_child_node(object, NULL, "l2", NULL, NULL, &l2_child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);

    ret = db_set_if_l2_trunk_id(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_trunk_id");
    ret = db_set_if_l2_vlan_id(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_vlan_id");
    ret = db_set_if_l2_l2_portindex(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_l2_portindex");
    ret = db_set_if_l2_vsi(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_vsi");
    ret = db_set_if_l2_tunnel_id(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_tunnel_id");

    
    db_child_iterator ifm_child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_CreateChild);
    // ret = db_create_child_node(object, NULL, "ifm", &key_info, &key_data, &ifm_child_iterator);
    ret = db_create_child_node(object, NULL, "ifm", NULL, NULL, &ifm_child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_CreateChild);

    ret = db_set_if_ifm_simple_name(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_simple_name");
    ret = db_set_if_ifm_description(ifm_child_iterator, (int8_t *)(ZERO_64_BYTES), 64);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_description");
    ret = db_set_if_ifm_is_configure(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_is_configure");
    ret = db_set_if_ifm_main_ifindex(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_main_ifindex");
    ret = db_set_if_ifm_sub_max_num(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_sub_max_num");
    ret = db_set_if_ifm_sub_curr_num(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_sub_curr_num");
    ret = db_set_if_ifm_error_down(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_error_down");
    ret = db_set_if_ifm_statistic(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_statistic");
    ret = db_set_if_ifm_vsys_id(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_vsys_id");
    ret = db_set_if_ifm_zone_id(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_zone_id");
    ret = db_set_if_ifm_last_up_time(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_last_up_time");
    ret = db_set_if_ifm_last_down_time(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_last_down_time");

    // db_child_iterator child_iterator = NULL;
    // DB_START_TEST_CPU_CYCLES(if_CreateChild);
    // ret = db_create_child_node(object, NULL, "l2", &key_info, &key_data, &child_iterator);
    // DB_STOP_TEST_CPU_CYCLES(if_CreateChild);

    // ret = db_set_if_l2_trunk_id(child_iterator, uiVrIndex + 1);
    // CHECK_OK_RET_DEBUG(ret, "set_if_l2_trunk_id");

    // ret = db_update_obj(object, &key_info, &key_data);
    // CHECK_OK_RET_DEBUG(ret, "db_update_obj failed.");

    return ret;
}

status_t static_if_register_schema_model(void *schema_model)
{
    g_if_model = (p_static_schema_model_t)(schema_model);
    if (g_if_model->schema_json_file_name == NULL) {
        g_if_model->schema_json_file_name = "if";
        g_if_model->obj_create_func = static_if_create_db_obj_func;
        g_if_model->obj_create_spec_conn_func = static_if_create_db_obj_spec_conn_func;
        g_if_model->obj_release_func = static_if_release_db_obj_func;
        g_if_model->obj_reset_func = static_if_reset_db_obj_func;
        g_if_model->bobj_reset_func = static_if_reset_db_bobj_func;
        // g_if_model->bobj_create_func = static_if_create_db_batch_obj_func;
        g_if_model->obj_set_func = static_if_set_obj_func;
        g_if_model->malloc_primary_key_func = static_if_primary_key_malloc_func;
        g_if_model->free_primary_key_func = static_if_primary_key_free_func;
        g_if_model->malloc_struct_data_func = static_if_struct_data_malloc_func;
        g_if_model->free_struct_data_func = static_if_struct_data_free_func;
        g_if_model->getfield_bykey_func = static_if_get_field_by_key_func;
        g_if_model->pri_key_set_func = static_if_key_set_func;
        g_if_model->getfield_func = static_if_get_field_func;
        g_if_model->update_func = NULL;
        g_if_model->update_bykey_func = static_if_update_bykey_func;
        g_if_model->set_sub_func = static_if_set_sub_func;
        g_if_model->unsub_all_func = static_if_unsub_all;
        g_if_model->cal_ops_func = static_if_cal_all_cb_cnt;

        (void)pthread_mutex_init(&g_if_mutex, NULL);
        (void)memset(g_if_subid, 0, sizeof(static_if_sub_entry_t) * LITE_SUBID_MAX_NUM);
    }
    return 0;
}

// if sub api

unsigned int static_if_set_sub_func(unsigned int type, db_connect_t sub_conn)
{
    unsigned int loop;
    if (type > IF_SUB_TYPE_INVALID) {
        return STATUS_INVALID_ARGUMENT;
    }

    for (loop = 0; loop < IF_SUB_TYPE_INVALID; loop++) {
        if (loop == type) {
            return g_if_sub_func_tbl[type].sub_spec_func(sub_conn);
        }
    }
    return STATUS_INVALID_ARGUMENT;
}

int static_if_get_sub_num(unsigned int *ret_id)
{
    unsigned int loop;
    (void)pthread_mutex_lock(&g_if_mutex);
    for (loop = 0; loop < LITE_SUBID_MAX_NUM; loop++) {
        if ((g_if_subid[loop].is_use == 0) && (g_if_subid[loop].sid == NULL)) {
            g_if_subid[loop].is_use = 1;
            *ret_id = loop;
            break;
        }
    }
    (void)pthread_mutex_unlock(&g_if_mutex);
    if (loop >= LITE_SUBID_MAX_NUM) {
        return -1;
    }
    return 0;
}

int static_if_set_subid(unsigned int id, db_subid sid)
{
    (void)pthread_mutex_lock(&g_if_mutex);
    if (id >= LITE_SUBID_MAX_NUM) {
        return -1;
    }
    g_if_subid[id].sid = sid;
    (void)pthread_mutex_unlock(&g_if_mutex);
    return 0;
}

void static_if_set_sub_cb_cnt(void *user_data)
{
    p_static_if_sub_entry_t sub_entry = (p_static_if_sub_entry_t)user_data;
    struct timeval nowtime;
    gettimeofday(&nowtime, DB_NULL);
    if (sub_entry->cb_cnt_tot == 0) {
        sub_entry->start = nowtime;
    } else {
        sub_entry->stop = nowtime;
    }
    sub_entry->cb_cnt_tot = sub_entry->cb_cnt_tot + 1;
    // *(uint32_t *)user_data = *(uint32_t *)user_data + 1;
}

void static_if_cal_all_cb_cnt(uint32_t *sub_tot_cb, void *time_cost) 
{
    unsigned int loop;
    uint32_t tot_cb = 0;
    double cost;
    double cost_max;
    TimeFlow(g_if_subid[0].start, g_if_subid[0].stop, cost);
    cost_max = cost;
    for (loop = 0; loop < LITE_SUBID_MAX_NUM; loop++) {
        if (g_if_subid[loop].sid != NULL) {
            tot_cb += g_if_subid[loop].cb_cnt_tot;
            TimeFlow(g_if_subid[loop].start, g_if_subid[loop].stop, cost);
            if (cost_max < cost) {
                cost_max = cost;
            }
        }
    }
    *(uint32_t *)sub_tot_cb = tot_cb;
    *(double *)time_cost = cost_max;
}

int static_if_unsub_all()
{
    unsigned int loop;
    db_object unsub_obj;
    int ret;
    for (loop = 0; loop < LITE_SUBID_MAX_NUM; loop++) {
        unsub_obj = NULL;
        if (g_if_subid[loop].sid != NULL) {
            ret = db_create_if_obj(UNSUB_OBJ, SYNC_MODE, &unsub_obj);
            if (ret == STATUS_OK) {
                db_unsubscribe(unsub_obj, g_if_subid[loop].sid);
                db_release_if_object(unsub_obj); 
                g_if_subid[loop].sid = NULL;
                g_if_subid[loop].is_use = 0;       
            }
        }
    }
    return 0;
}

void static_if_tbtp_notification_callback(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num,
                                             void *user_data)
{
    int ret;
    unsigned int ifindex = 0;
    unsigned int tb = 0;
    unsigned int tp = 0;
    db_object object = NULL;

    static_if_set_sub_cb_cnt(user_data);
    switch (obj_info->event_type) {
        case OBJ_INSERTED:
            object = obj_info->object;
            break;
        case OBJ_MODIFIED:
            object = obj_info->object;
            break;
        case OBJ_DELETED:
            object = obj_info->old_obj;
            break;
        default:
            return;
    }

    ret = db_get_if_ifindex(object, &ifindex);
    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_tb(object, &tb);
    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_tp(object, &tp);
    if (ret != STATUS_OK) {
        return;
    }

    return;
}

unsigned int static_if_IfSubScribeForTbTp(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    // 创建sub对象
    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    CHECK_OK_RET(ret, "db_create_if_object");

    // 订阅所有事件
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    CHECK_OK_RET(ret, "db_sub_add_event OBJ_MODIFIED");

    ret = db_sub_add_field(object, "if", "tb", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field tb");

    ret = db_sub_add_field(object, "if", "tp", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field tp");

    //ret = db_sub_reg_callback(object, static_if_tbtp_notification_callback, &(g_if_subid[sid]));
    ret = db_sub_reg_callback(object, static_if_tbtp_notification_callback, &(g_if_subid[sid]));
    CHECK_OK_RET(ret, "db_sub_reg_callback");

    //db_subid sub_id = db_subscribe(object);
    db_subid sub_id = NULL;
    ret = db_sub_with_conn(sub_conn, object, &sub_id);

    if (sub_id == NULL) {
        LOG_ERROR(-1, "Sub all event failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    if (object != NULL) {
        db_release_if_object(object);
    }
    return ret;
}

void static_if_L3Info_notification_callback(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num,
                                             void *user_data)
{
    int ret;
    db_object object = NULL;
    IfmL3Info stL3Info = { 0 };
    IfmL3Info *l3Info = &stL3Info;
    int8_t *mac_address = NULL;
    uint32_t if_l2_mac_address_len = 0;
    db_child_iterator child_iterator = NULL;

    if (NULL == obj_info) {
        return;
    }

    static_if_set_sub_cb_cnt(user_data);
    
    switch (obj_info->event_type) {
        case OBJ_INSERTED:
            object = obj_info->object;
            break;
        case OBJ_MODIFIED:
            object = obj_info->object;
            break;
        case OBJ_DELETED:
            object = obj_info->old_obj;
            break;
        default:
            return;
    }

    if (NULL == object) {
        return;
    }
    ret = db_get_if_ifindex(object, &l3Info->ifindex);
    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_if_type(object, &l3Info->if_type);
    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_tb(object, &l3Info->tb);
    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_tp(object, &l3Info->tp);

    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_ipv4_mtu(object, &l3Info->ipv4_mtu);

    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_port_switch(object, &l3Info->port_switch);

    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_linkup(object, &l3Info->linkup);

    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_forwardType(object, &l3Info->fwdIfType);

    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_macAddress(object, (int8_t **)&mac_address, &if_l2_mac_address_len);

    if (ret != STATUS_OK) {
        return;
    }

    (void)memcpy_s(l3Info->macAddress, if_l2_mac_address_len, mac_address, if_l2_mac_address_len);

    ret = db_create_child_node(object, NULL, "l2", NULL, NULL, &child_iterator);
    if (ret != STATUS_OK) {
        return;
    }
    ret = db_get_if_l2_vsi(child_iterator, &l3Info->vsi);
    if (ret != STATUS_OK) {
        return;
    }
    ret = db_get_if_l2_vlan_id(child_iterator, &l3Info->vlan_id);
    if (ret != STATUS_OK) {
        return;
    }

    return;
}

unsigned int static_if_IfSubScribeForL3Info(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    // 创建sub对象
    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    CHECK_OK_RET(ret, "db_create_if_object");

    // 订阅所有事件
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    CHECK_OK_RET(ret, "db_sub_add_event OBJ_MODIFIED");

    ret = db_sub_add_field(object, "if", "port_switch", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field port_switch");

    ret = db_sub_add_field(object, "if", "if_type", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field if_type");

    ret = db_sub_add_field(object, "if", "ipv4_mtu", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field ipv4_mtu");

    ret = db_sub_add_field(object, "if", "linkup", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field linkup");


    ret = db_sub_add_field(object, "if", "forwardType", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field fwdIfType");

    ret = db_sub_add_field(object, "if", "macAddress", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field macAddress");

    ret = db_sub_add_field(object, "l2", "vsi", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field vsi");

    ret = db_sub_add_field(object, "l2", "vlan_id", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field vlan_id");

    ret = db_sub_reg_callback(object, static_if_L3Info_notification_callback, &(g_if_subid[sid]));
    CHECK_OK_RET(ret, "db_sub_reg_callback");

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    CHECK_OK_RET(ret, "db_sub_add_push_op");

        //db_subid sub_id = db_subscribe(object);
    db_subid sub_id = NULL;
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(-1, "Sub all event failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    if (object != NULL) {
        db_release_if_object(object);
    }
    return ret;
}

unsigned int static_if_GetPortSwitchByObj(db_object object, unsigned int *ifIndex, unsigned int *port_switch)
{
    int ret;
    if ((ifIndex == NULL) || (port_switch == NULL)) {
        return VOS_ERR;
    }
    ret = db_get_if_ifindex(object, ifIndex);
    // CHECK_OK_RET(ret, "db_sub_reg_callback");

    ret = db_get_if_port_switch(object, port_switch);
    // CHECK_OK_RET(ret, "db_sub_reg_callback");

    return ret;
}

unsigned int static_if_GetFwdIfType(unsigned int ifindex, unsigned int *fwdIfType)
{
    int ret;
    db_object object = NULL;
    db_if_key_t dbIfKey = {0};
    db_key_info_t key_info = {0};
    key_info.key_len = sizeof(db_if_key_t);
    key_info.index_id = DB_IF_IF_PK_ID;
    // 创建sub对象
    ret = db_create_if_obj(READ_OBJ, SYNC_MODE, &object);
    // CHECK_OK_RET(ret, "db_create_if_object");

    dbIfKey.ifindex = ifindex;

    ret = db_read_obj(object, &key_info, &dbIfKey);
    if(ret == STATUS_OK) {
        ret = db_get_if_forwardType(object, fwdIfType);
        // CHECK_OK_RET(ret, "db_get_if_forwardType");
    }

    if (object != NULL) {
        db_release_if_object(object);
    }
    return ret;
}

void FWM_SVC_MstpPortSwitch(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num,
                                void *user_data)
{
    unsigned int ret;
    unsigned int uiIfindex = 0;
    unsigned int uiPortSwitch = 0;
    unsigned int portType = 0;
    db_object object = NULL;
    
    static_if_set_sub_cb_cnt(user_data);
    
    switch (obj_info->event_type) {
        case OBJ_INSERTED:
            object = obj_info->object;
            break;
        case OBJ_MODIFIED:
            object = obj_info->object;
            break;
        case OBJ_DELETED:
            object = obj_info->old_obj;
            break;
        default:
            return;
    }
    
    ret = static_if_GetPortSwitchByObj(object, &uiIfindex, &uiPortSwitch);
    if (ret != VOS_OK) {
        // LOG_ERROR(ret, " getPortSwitchByObj fail,Ret:%d.", ret);
        return;
    }


    ret = static_if_GetFwdIfType(uiIfindex, &portType);
    if (ret != VOS_OK) {
        // LOG_ERROR(ret, "Port switch get port type fail,ifindex:%d,Ret:%d.", uiIfindex, ret);
        return;
    }

    // static_if_SvcPortSwitchProc(portType, uiIfindex, uiPortSwitch); // 暂未跟踪到里面

    return;
}

unsigned int static_if_SubPortSwitchChange(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");
    // 创建sub对象
    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    CHECK_OK_RET(ret, "db_create_if_object");

    // 订阅所有事件
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    CHECK_OK_RET(ret, "db_sub_add_event OBJ_MODIFIED");

    ret = db_sub_add_field(object, "if", "port_switch", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field port_switch");

    ret = db_sub_reg_callback(object, FWM_SVC_MstpPortSwitch, &(g_if_subid[sid]));
    CHECK_OK_RET(ret, "db_sub_reg_callback");

        //db_subid sub_id = db_subscribe(object);
    db_subid sub_id = NULL;
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(-1, "Sub all event failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    if (object != NULL) {
        db_release_if_object(object);
    }
    return ret;
}

unsigned int static_if_GetFwdIfTypeByObj(db_object object, unsigned int *ifIndex, unsigned int *fwdIfType)
{
    int ret;
    if ((fwdIfType == NULL)||(ifIndex == NULL)||(object == NULL)) {
        return VOS_ERR;
    }
    ret = db_get_if_ifindex(object, ifIndex);
    CHECK_OK_RET(ret, "db_get_if_ifindex");

    ret = db_get_if_forwardType(object, fwdIfType);
    CHECK_OK_RET(ret, "db_get_if_ifindex");

    return ret;
}

void static_if_ProcForwardType(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num,
                                  void *user_data)
{
    unsigned int ret = VOS_OK;
    unsigned int oldIfindex = 0;
    unsigned int newIfindex = 0;
    unsigned int oldFwdType = 0;
    unsigned int newFwdType = 0;

    static_if_set_sub_cb_cnt(user_data);
    
    if (obj_info->event_type == OBJ_INSERTED) {
        ret = static_if_GetFwdIfTypeByObj(obj_info->object, &newIfindex, &newFwdType);
        if (ret != VOS_OK) {
            // LOG_ERROR(ret, "getFwdIfTypeByNewObj Fail,ret:%d.", ret);
            return;
        }

        if (newFwdType == 0xFFFFFFFF) {
            return;
        }
    } else if (obj_info->event_type == OBJ_MODIFIED) {
        ret = static_if_GetFwdIfTypeByObj(obj_info->old_obj, &oldIfindex, &oldFwdType);
        if (ret != VOS_OK) {
            // LOG_ERROR(ret, "getFwdIfTypeByOldObj Fail,ret:%d.", ret);
            return;
        }

        ret = static_if_GetFwdIfTypeByObj(obj_info->object, &newIfindex, &newFwdType);
        if (ret != VOS_OK) {
            // LOG_ERROR(ret, "getFwdIfTypeByNewObj Fail,ret:%d.", ret);
            return;
        }

        if (oldIfindex != newIfindex || (oldFwdType != 0xFFFFFFFF &&
                oldFwdType != 0)) {
            return;
        }
    } else {
        return;
    }

    return;
}

unsigned int static_if_SubFwdIfTypeChange(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");
    // 创建sub对象
    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    CHECK_OK_RET(ret, "db_create_if_obj");

    // 订阅所有事件
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    CHECK_OK_RET(ret, "db_sub_add_event OBJ_MODIFIED");

    ret = db_sub_add_field(object, "if", "forwardType", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field forwardType");

    ret = db_sub_reg_callback(object, static_if_ProcForwardType, &(g_if_subid[sid]));
    CHECK_OK_RET(ret, "db_sub_reg_callback");

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    CHECK_OK_RET(ret, "db_sub_add_push_op");

        //db_subid sub_id = db_subscribe(object);
    db_subid sub_id = NULL;
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(-1, "Sub all event failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    if (object != NULL) {
        db_release_if_object(object);
    }
    return ret;
}

void static_if_TbTpChange_notification_callback(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num,
                                             void *user_data)
{
    db_object object = NULL;

    if (NULL == obj_info) {
        return;
    }

    static_if_set_sub_cb_cnt(user_data);
    
    switch (obj_info->event_type) {
        case OBJ_INSERTED:
            object = obj_info->object;
            break;
        case OBJ_MODIFIED:
            object = obj_info->object;
            break;
        case OBJ_DELETED:
            object = obj_info->old_obj;
            break;
        default:
            return;
    }

    if (NULL == object) {
        return;
    }
    
    int ret;
    unsigned int ifindex = 0;
    unsigned int tb = 0;
    unsigned int tp = 0;

    ret = db_get_if_ifindex(object, &ifindex);
    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_tb(object, &tb);
    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_tp(object, &tp);
    if (ret != STATUS_OK) {
        return;
    }

    return;
}

unsigned int static_if_SubTbTpChange(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");
    // 创建sub对象
    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    CHECK_OK_RET(ret, "db_create_if_obj");

    // 订阅所有事件
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    CHECK_OK_RET(ret, "db_sub_add_event OBJ_MODIFIED");

    ret = db_sub_add_field(object, "if", "tb", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field tb");

    ret = db_sub_add_field(object, "if", "tp", NULL, 0, 0);
    CHECK_OK_RET(ret, "db_sub_add_field tp");

    ret = db_sub_reg_callback(object, static_if_TbTpChange_notification_callback, &(g_if_subid[sid]));
    CHECK_OK_RET(ret, "db_sub_reg_callback");

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    CHECK_OK_RET(ret, "db_sub_add_push_op");

        //db_subid sub_id = db_subscribe(object);
    db_subid sub_id = NULL;
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(-1, "Sub all event failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    if (object != NULL) {
        db_release_if_object(object);
    }
    return ret;
}

unsigned int BIGHEART_DB_GetIfDevInfo(unsigned int ifIndex, IfDevInfo* devInfo)
{
    VOS_INT32 ret;
    db_object object = NULL;
    db_child_iterator child_iterator = NULL;
    db_if_key_t key_data;
    db_key_info_t key_info;
    
    key_info.key_len = sizeof(db_if_key_t);
    key_info.index_id = DB_IF_IF_PK_ID;

    key_data.ifindex = ifIndex;

    ret = db_create_if_obj(READ_OBJ, SYNC_MODE, &object);
    if (ret != VOS_OK) {
        return VOS_ERR;;
    }

    ret = db_read_obj(object, &key_info, &key_data);
    if (ret != VOS_OK) {
        db_release_if_object(object);
        return VOS_ERR;;
    }

    ret = db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
    if (ret != VOS_OK) {
        db_release_if_object(object);
        return VOS_ERR;;
    }

    (void)db_get_if_dev_dev_id(child_iterator, &devInfo->dev_id);
    (void)db_get_if_dev_chassis_id(child_iterator, &devInfo->chassis_id);
    (void)db_get_if_dev_slot_id(child_iterator, &devInfo->slot_id);
    (void)db_get_if_dev_card_id(child_iterator, &devInfo->card_id);
    (void)db_get_if_dev_unit_id(child_iterator, &devInfo->unit_id);
    (void)db_get_if_dev_port_id(child_iterator, &devInfo->port_id);

    db_release_if_object(object);
    return VOS_OK;
}

unsigned int BIGHEART_DB_GetIfData(BigHeartIfTableData* pIfData, db_object object)
{

    (void)db_get_if_ifindex(object, &(pIfData->ifIndex));
    (void)db_get_if_forwardType(object, &(pIfData->forwardType));
    (void)db_get_if_tb(object, &(pIfData->tb));
    (void)db_get_if_tp(object, &(pIfData->tp));

    (void)BIGHEART_DB_GetIfDevInfo(pIfData->ifIndex, &(pIfData->devInfo));

    return VOS_OK;
}

void BIGHEART_DB_IfSubCallback(char* tableName, db_sub_obj_info* objInfo, unsigned int objInfoNum,
                                   void* userData)
{
    unsigned int i;
    unsigned int ret;
    db_sub_obj_info* obj = objInfo;
    db_object object = NULL;
    BigHeartIfTableData IfTableData = { 0 };

    if (obj == NULL) {
        LOG_ERROR(-1, " object info is null.");
        return;
    }

    for (i = 0; i < objInfoNum; ++i) {

        IfTableData.operType = (obj->event_type == OBJ_DELETED) ? BIGHEART_IFLIST_DEL : BIGHEART_IFLIST_ADD;
        object = (obj->event_type == OBJ_DELETED) ? obj->old_obj : obj->object;
        if (object == NULL) {
            LOG_ERROR(ret, " object is null.");
            continue;
        }
      
        ret = BIGHEART_DB_GetIfData(&IfTableData, object);
        if (ret != VOS_OK) {
            LOG_ERROR(ret, "Get if data from object failed.");
            continue;
        }

        // DEL

        obj++;
    }
    return;
}

db_subid g_ifSubId = NULL;
unsigned int BIGHEART_DB_SubIfTable(db_connect_t sub_conn)
{
    int ret;
    unsigned int value;
    db_object object = NULL;
    db_subid subId = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB create object failed, ret %d.", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_INSERTED | OBJ_DELETED | OBJ_INITIAL_LOAD);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe iffrom DB add event failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    value = FWDIF_PHY_MAIN;
    ret = db_sub_add_field(object, "if", "forwardType", &value, sizeof(unsigned int), SUB_CMP_EQUAL);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe db_sub_add_field failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_add_push_op(object, PUSH_CUR_DATA | PUSH_OLD_DATA);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB add push type failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_reg_callback(object, BIGHEART_DB_IfSubCallback, &(g_if_subid[sid]));
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if set callback failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &subId);
    if (subId == NULL) {
        LOG_ERROR(ret, "Subscribe from DB failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, subId);

    g_ifSubId = subId;
    db_release_if_object(object);

    return VOS_OK;
}

static_if_IFNET_EVT_SET_FLAG_S g_stbesifeventflag = { 0 };
static_if_IFNET_EVT_STAT_INFO_S *g_pstbesifeventstatic = NULL;
static_if_L2IF_EVT_SET_FLAG_S g_stbesl2ifeventflag = { 0 };
static_if_L2IF_EVT_STATISTIC_S *g_pstbesl2ifeventstatic = NULL;

static_if_IFNET_EVT_SET_FLAG_S *static_if_gGetEventFlag()
{
    static_if_IFNET_EVT_SET_FLAG_S *pstbesifeventflag = NULL;

    pstbesifeventflag = &g_stbesifeventflag;

    return pstbesifeventflag;
}

static_if_IFNET_EVT_STAT_INFO_S *static_if_GetEventStatic()
{
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    if (NULL == g_pstbesifeventstatic) {
        g_pstbesifeventstatic = (static_if_IFNET_EVT_STAT_INFO_S *)malloc(sizeof(static_if_IFNET_EVT_STAT_INFO_S));
        if (NULL == g_pstbesifeventstatic) {
            return NULL;
        }
        (void)memset_s(g_pstbesifeventstatic, sizeof(static_if_IFNET_EVT_STAT_INFO_S), 0, sizeof(static_if_IFNET_EVT_STAT_INFO_S));
    }

    pstbesifeventstatic = g_pstbesifeventstatic;

    return pstbesifeventstatic;
}

static_if_L2IF_EVT_STATISTIC_S *static_if_L2ifGetEventStatic()
{
    static_if_L2IF_EVT_STATISTIC_S *pstbesl2iftstatic = NULL;

    if (NULL == g_pstbesl2ifeventstatic) {
        g_pstbesl2ifeventstatic = (static_if_L2IF_EVT_STATISTIC_S *)malloc(sizeof(static_if_L2IF_EVT_STATISTIC_S));
        if (NULL == g_pstbesl2ifeventstatic) {
            return NULL;
        }
        (void)memset_s(g_pstbesl2ifeventstatic, sizeof(static_if_L2IF_EVT_STATISTIC_S), 0, sizeof(static_if_L2IF_EVT_STATISTIC_S));
    }

    pstbesl2iftstatic = g_pstbesl2ifeventstatic;

    return pstbesl2iftstatic;
}

void static_if_IfCreateCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    unsigned int ulIfIndex = 0;
    int ret;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);

    static_if_set_sub_cb_cnt(user_data);
    
    switch (obj_info[0].event_type) {
        case OBJ_INSERTED: 
        {
            // TESET_LOG_INFO("Receive OBJ_INSERTED event. ");

            ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
            if (ret != STATUS_OK) {
                LOG_ERROR(ret, "failed to read if object, ret: %u! ", ret);
                return;
            }

            // DEL

            pstbesifeventstatic->ulIfCreate++;

            break;
        }

        case OBJ_DELETED:    
        case OBJ_INITIAL_LOAD: 
        case OBJ_DROP_TABLE: 
        case OBJ_MODIFIED:   
        {
            // TESET_LOG_INFO("Receive gmdb event [%u]. ", obj_info[0].event_type);

            break;
        }

        default:
            break;
    }

    return;
}

unsigned int static_if_SubIfCreateEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;
    static_if_IFNET_EVT_SET_FLAG_S *pstbesifeventflag = NULL;

    pstbesifeventflag = static_if_gGetEventFlag();
  
    if (pstbesifeventflag->ucIfCreateFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IfCreateCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed !");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    pstbesifeventflag->ucIfCreateFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IfDataSetMacCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    
    int ret;
    unsigned int ulIfIndex;
    int8_t *szOldIfMac = NULL;
    int8_t *szNewIfMac = NULL;
    unsigned int ulOldLength = 0;
    unsigned int ulNewLength = 0;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);
    static_if_NULL_CHECK(obj_info);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "wrong obj_info_num ");
        return;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to db_get_if_ifindex. ret: %u ", ret);
            return;
        }

        ret = db_get_if_macAddress(obj_info[0].object, &szNewIfMac, &ulNewLength);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to db_get_if_macAddress. ret: %u ", ret);
            return;
        }

        ret = db_get_if_macAddress(obj_info[0].old_obj, &szOldIfMac, &ulOldLength);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to db_get_if_macAddress. ret: %u ", ret);
            return;
        }

        if (ulNewLength == ulOldLength) {
            ret = memcmp(szNewIfMac, szOldIfMac, ulNewLength);
            if (STATUS_OK == ret) {
                return;
            }
        }

        // DEL

        pstbesifeventstatic->ulIfDataSetMac++;
    }

    return;
}

unsigned int static_if_SubIfDataSetMacEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIfDataSetMacFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u !", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_field(object, "if", "macAddress", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IfDataSetMacCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed. !");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIfDataSetMacFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IfDataSetIpv4MtuCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    
    int ret;
    unsigned int ulIfIndex;
    VOS_UINT16 ulOldMtu;
    VOS_UINT16 ulNewMtu;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);
    static_if_NULL_CHECK(obj_info);

  
    static_if_set_sub_cb_cnt(user_data);
    
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to db_get_if_ifindex. ");
            return ;
        }

        ret = db_get_if_ipv4_mtu(obj_info[0].object, &ulNewMtu);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if old ipv4_mtu . ret: %u ", ret);
            return ;
        }

        ret = db_get_if_ipv4_mtu(obj_info[0].old_obj, &ulOldMtu);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new ipv4_mtu. ret: %u ", ret);
            return ;
        }

        if (ulNewMtu == ulOldMtu) {
            return ;
        }

        // DEL

        pstbesifeventstatic->ulIfDataSetIpv4Mtu++;
    }
    return ;
}

unsigned int static_if_SubIfDataSetIpv4MtuEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIfDataSetIpv4MtuFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u ", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u ", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u ", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_field(object, "if", "ipv4_mtu", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add field failed. ret: %u ", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IfDataSetIpv4MtuCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u ", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed. ");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIfDataSetIpv4MtuFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IfDataSetIpv6MtuCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    
    int ret;
    unsigned int ulIfIndex;
    VOS_UINT16 ulOldMtu;
    VOS_UINT16 ulNewMtu;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);
    static_if_NULL_CHECK(obj_info);

  
    static_if_set_sub_cb_cnt(user_data);
    
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to db_get_if_ifindex. ret: %u ", ret);
            return ;
        }

        ret = db_get_if_ipv6_mtu(obj_info[0].object, &ulNewMtu);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to db_get_if_ipv6_mtu. ret: %u ", ret);
            return ;
        }

        ret = db_get_if_ipv6_mtu(obj_info[0].old_obj, &ulOldMtu);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to db_get_if_ipv6_mtu. ret: %u ", ret);
            return ;
        }

        if (ulNewMtu == ulOldMtu) {
            return ;
        }

        // DEL

        pstbesifeventstatic->ulIfDataSetIpv6Mtu++;
    }
    return ;
}

unsigned int static_if_SubIfDataSetIpv6MtuEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIfDataSetIpv6MtuFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u !", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_field(object, "if", "ipv6_mtu", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IfDataSetIpv6MtuCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed !");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIfDataSetIpv6MtuFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IfShutActionCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    
    int ret;
    unsigned int ulIfIndex;
    unsigned int ulShutDown;
    unsigned int ulOldShutDown;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);
    static_if_NULL_CHECK(obj_info);

    static_if_set_sub_cb_cnt(user_data);
    
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to db_get_if_ifindex. ret: %u ", ret);
            return ;
        }

        ret = db_get_if_shutdown(obj_info[0].object, &ulShutDown);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if shutdown. ret: %u ", ret);
            return ;
        }

        ret = db_get_if_shutdown(obj_info[0].old_obj, &ulOldShutDown);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if old shutdown. ret: %u ", ret);
            return ;
        }

        if (ulShutDown == ulOldShutDown) {
            return ;
        }

    }

    return ;
}

unsigned int static_if_SubIfShutActionEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIfShutActionFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u !", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_add_field(object, "if", "shutdown", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IfShutActionCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed !");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);
    
    g_stbesifeventflag.ucIfShutActionFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

unsigned int static_if_GetLinkUpAndOnBoard(db_sub_obj_info *obj_info, unsigned int *OldBoardStatus,
                                   unsigned int *OldLinkStatus, unsigned int *NewBoardStatus,
                                   unsigned int *NewLinkStatus)
{
    int ret;

    // 入参和出参指针已经在上层接口判空
    ret = db_get_if_on_board(obj_info[0].old_obj, OldBoardStatus);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to get if old board status, ret: %u !", ret);
        return VOS_ERR;
    }

    ret = db_get_if_linkup(obj_info[0].old_obj, OldLinkStatus);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to get if old link status, ret: %u !", ret);
        return VOS_ERR;
    }

    ret = db_get_if_on_board(obj_info[0].object, NewBoardStatus);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to get if new board status, ret: %u !", ret);
        return VOS_ERR;
    }

    ret = db_get_if_linkup(obj_info[0].object, NewLinkStatus);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to get if new link status, ret: %u !", ret);
        return VOS_ERR;
    }

    return VOS_OK;
}

void static_if_NotifyEvtCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    unsigned int ulOldBoardStatus = 0;
    unsigned int ulNewBoardStatus = 0;
    unsigned int ulOldLinkStatus = 0;
    unsigned int ulNewLinkStatus = 0;
    unsigned int ulIfIndex = 0;
    unsigned int ulSlot = 0;
    
    db_child_iterator child_iterator_dev = NULL;
    int ret;

    static_if_NULL_CHECK(obj_info);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ; }

  
    if (obj_info[0].event_type == OBJ_MODIFIED) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event.");

        ret = static_if_GetLinkUpAndOnBoard(obj_info, &ulOldBoardStatus, &ulOldLinkStatus,
                                      &ulNewBoardStatus, &ulNewLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Failed to get linkup or on_board, ret: %u !", ret);
            return ;
        }

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Failed to get if index, ret: %u !", ret);
            return ;
        }

        ret = db_create_child_node(obj_info[0].object, NULL, "dev", NULL, NULL, &child_iterator_dev);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Failed to create dev child object, ret: %u !", ret);
            return ;
        }

        ret = db_get_if_dev_slot_id(child_iterator_dev, &ulSlot);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Failed to get if slot, ret: %u !", ret);
            return ;
        }

        // DEL
    }

    return ;
}

unsigned int static_if_SubIfNotifyEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

    if (g_stbesifeventflag.ucIfNotifyFlg) { return VOS_OK; }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "on_board", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add Board_role field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "linkup", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add Board_role field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_NotifyEvtCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed !");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIfNotifyFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IfDeleteCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    unsigned int ulIfIndex = 0;
    
    int ret;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_DELETED == obj_info[0].event_type) {
        // TESET_LOG_INFO("Receive OBJ_DELETED event. ");

        ret = db_get_if_ifindex(obj_info[0].old_obj, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if ifIndex. ret: %u ", ret);
            return ;
        }

        // DEL

        pstbesifeventstatic->ulIfDelete++;
    }

    return ;
}

unsigned int static_if_SubIfDeleteEvt(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;
    db_subid sub_id = NULL;

  
    if (g_stbesifeventflag.ucIfDeleteFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u ", ret);
        return VOS_OK;
    }

  
    ret = db_sub_add_event(object, OBJ_DELETED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u ", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u ", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IfDeleteCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u ", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        ret = VOS_ERR;
        LOG_ERROR(ret, "db_subscribe failed. ");
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIfDeleteFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IfInheritCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int If_type = 0;
    unsigned int ulIfIndex = 0;
    
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_NULL_CHECK(pstbesifeventstatic);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (obj_info[0].event_type == OBJ_MODIFIED) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if ifindex. ret: %u ", ret);
            return ;
        }

        ret = db_get_if_if_type(obj_info[0].old_obj, &If_type);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if type. ret: %u ", ret);
            return ;
        }

    }

    return ;
}

unsigned int static_if_SubIfInheritEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIfInheritFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u ", ret);
        return VOS_OK;
    }

  
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u ", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u ", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "if_type", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add if_type field failed. ret: %u ", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IfInheritCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u ", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed. ");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIfInheritFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IfRemoveCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int ulIfIndex = 0;
    
    unsigned int ulOldBoardStatus = 0;
    unsigned int ulNewBoardStatus = 0;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;

    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (NULL == obj_info || obj_info_num != 1 || NULL == table_name) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if ifindex. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_on_board(obj_info[0].object, &ulNewBoardStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if old board status. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_on_board(obj_info[0].old_obj, &ulOldBoardStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if old board status. ret: %u ", ret);
            return ;
        }

        if (ulOldBoardStatus == ulNewBoardStatus) {
            return ;
        }

      
        if (ulOldBoardStatus == 1) {
            // DEL

            pstbesifeventstatic->ulIfRemove++;
        }
    }

    return ;
}

unsigned int static_if_SubIfRemoveEvt(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;
    db_subid sub_id = NULL;

  
    if (g_stbesifeventflag.ucIfRemoveFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create devm object. ret: %u ", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u ", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u ", ret);
        goto ReleaseObject;
    }

    // 只要有变化就通知
    ret = db_sub_add_field(object, "if", "on_board", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add on_board field failed. ret: %u ", ret);
        goto ReleaseObject;
    }
    ret = db_sub_reg_callback(object, static_if_IfRemoveCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u ", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed. ");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIfRemoveFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IfLinkUpDownCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int ulIfIndex = 0;
    
    unsigned int ulNewLinkStatus = 0;
    unsigned int ulOldLinkStatus = 0;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;
    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_NULL_CHECK(pstbesifeventstatic);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get logic slot. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_linkup(obj_info[0].object, &ulNewLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_linkup(obj_info[0].old_obj, &ulOldLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u ", ret);
            return ;
        }

        if (ulNewLinkStatus == ulOldLinkStatus) {
            return ;
        }

      
        if (ulNewLinkStatus == 1) {
          
            // DEL
            pstbesifeventstatic->ulIfLinkUp++;
        } else {
          
            // DEL
            pstbesifeventstatic->ulIfLinkDown++;
        }
    }

    return ;
}

unsigned int static_if_IfLinkUpDownEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIfLinkUpDownFlg) { return VOS_OK; }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create devm object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "linkup", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add linkup field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IfLinkUpDownCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed !");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIfLinkUpDownFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IoctlLink2PhyCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int ulIfIndex = 0;
    
    unsigned int ulNewShutStatus = 0;
    unsigned int ulOldShutStatus = 0;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;
    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if ifindex. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_shutdown(obj_info[0].object, &ulNewShutStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_shutdown(obj_info[0].old_obj, &ulOldShutStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u ", ret);
            return ;
        }

        if (ulNewShutStatus == ulOldShutStatus) {
            return ;
        }

        if (ulNewShutStatus == 0) {
          
            // DEL
            pstbesifeventstatic->ulIoctlLink2Phydown++;
        }
    }

    return ;
}

unsigned int static_if_IoctlLink2PhyEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIoctlLink2PhyFlg) { return VOS_OK; }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create devm object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "shutdown", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add linkup field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IoctlLink2PhyCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        ret = VOS_ERR;
        LOG_ERROR(ret, "db_subscribe failed !");
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIoctlLink2PhyFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IoctlPhy2LinkCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int ulIfIndex = 0;
    
    unsigned int ulNewPhyLinkStatus = 0;
    unsigned int ulOldPhyLinkStatus = 0;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;
    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if ifindex. ret: %u ", ret);
            return ;
        }

        // static_if_GetPhyState      物理状态
        ret = db_get_if_linkup(obj_info[0].object, &ulNewPhyLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_linkup(obj_info[0].old_obj, &ulOldPhyLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u ", ret);
            return ;
        }

        if (ulNewPhyLinkStatus == ulOldPhyLinkStatus) {
            return ;
        }

        if (ulNewPhyLinkStatus == 1) {
          
            // DEL
            pstbesifeventstatic->ulIoctlPhy2LinkUp++;
        } else {
          
            // DEL
            pstbesifeventstatic->ulIoctlPhy2LinkDown++;
        }
    }

    return ;
}

unsigned int static_if_IoctlPhy2LinkEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIoctlPhy2LinkFlg) { return VOS_OK; }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create devm object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "linkup", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add linkup field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IoctlPhy2LinkCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        ret = VOS_ERR;
        LOG_ERROR(ret, "db_subscribe failed !");
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIoctlPhy2LinkFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_IoctlLink2LinkCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int ulIfIndex = 0;
    
    unsigned int ulNewLinkStatus = 0;
    unsigned int ulOldLinkStatus = 0;
    static_if_IFNET_EVT_STAT_INFO_S *pstbesifeventstatic = NULL;
    pstbesifeventstatic = static_if_GetEventStatic();

    static_if_NULL_CHECK(pstbesifeventstatic);

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED == obj_info[0].event_type) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get logic slot. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_linkup(obj_info[0].object, &ulNewLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u ", ret);
            return ;
        }

      
        ret = db_get_if_linkup(obj_info[0].old_obj, &ulOldLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u ", ret);
            return ;
        }
        if (ulNewLinkStatus == ulOldLinkStatus) {
            return ;
        }

        if (ulNewLinkStatus == 1) {
            // IF_IOCTL_PhyUpIOCtl
            // DEL
            pstbesifeventstatic->ulIoctlLink2LinkUp++;
        } else {
            // IF_IOCTL_PhyUpIOCtl
            // DEL
            pstbesifeventstatic->ulIoctlLink2LinkDown++;
        }
    }

    return ;
}

unsigned int static_if_IoctlLink2LinkEvt(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;

  
    if (g_stbesifeventflag.ucIoctlLink2LinkFlg) { return VOS_OK; }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create devm object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "linkup", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add linkup field failed. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_IoctlLink2LinkCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed !");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesifeventflag.ucIoctlLink2LinkFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_L2ifPortIoctlCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    unsigned int usLinkStatus = 0;
    unsigned int usOldLinkStatus = 0;
    unsigned int ulIfIndex = 0;
    int ret;
    static_if_L2IF_EVT_STATISTIC_S *pstbesl2ifstatic = NULL;

    pstbesl2ifstatic = static_if_L2ifGetEventStatic();

    static_if_NULL_CHECK(pstbesl2ifstatic);
    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED != obj_info[0].event_type) {
        return ;
    }

    // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

    ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to get ifindex. ret: %u ", ret);
        return ;
    }
    // 获取新值
    ret = db_get_if_linkup(obj_info[0].object, &usLinkStatus);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to get linkup. ret: %u ", ret);
        return ;
    }

    // 获取旧值
    ret = db_get_if_linkup(obj_info[0].old_obj, &usOldLinkStatus);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to get linkup. ret: %u ", ret);
        return ;
    }

    if (usLinkStatus == usOldLinkStatus) {
        return ;
    }

    if (usLinkStatus == 1) {
        // DEL
        pstbesl2ifstatic->ulPortIoctlUp++;

    } else {
        // DEL
        pstbesl2ifstatic->ulPortIoctlDown++;
    }

    return ;
}

static_if_TRUNK_EVT_SET_FLAG_S g_stbestrunkeventflag = { 0 };
static_if_TRUNK_EVT_STATISTIC_S *g_pstbestrunkeventstatic = NULL;

static_if_TRUNK_EVT_SET_FLAG_S *static_if_TrunkGetEventFlag()
{
    static_if_TRUNK_EVT_SET_FLAG_S *pstbestrunkeventflag = NULL;

    pstbestrunkeventflag = &g_stbestrunkeventflag;

    return pstbestrunkeventflag;
}

unsigned int static_if_L2ifSubPortIoctlEvt(db_connect_t sub_conn)
{
    db_object object;
    db_subid sub_id;
    int ret;

  
    if (g_stbesl2ifeventflag.ucPortIoctlFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u ", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u ", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u ", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "linkup", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add linkup field failed. ret: %u ", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_L2ifPortIoctlCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u ", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed. ");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesl2ifeventflag.ucPortIoctlFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_L2ifl2l3SwitchCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    unsigned int ulPortSwich;
    unsigned int ulOldPortSwich;
    unsigned int ulIfIndex;
    int ret;
    static_if_L2IF_EVT_STATISTIC_S *pstbesl2ifstatic = NULL;

    pstbesl2ifstatic = static_if_L2ifGetEventStatic();

    static_if_NULL_CHECK(pstbesl2ifstatic);

    static_if_NULL_CHECK(table_name);
    static_if_NULL_CHECK(obj_info);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters. ");
        return ;
    }

    if (OBJ_MODIFIED != obj_info[0].event_type) {
        return ;
    }

    // TESET_LOG_INFO("Receive OBJ_MODIFIED event. ");

    ret= db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
    if (VOS_OK != ret) {
        LOG_ERROR(ret, "db_get_port_vlanmap_ifindex failed. ret: %u ", ret);
        return ;
    }

    ret= db_get_if_port_switch(obj_info[0].object, &ulPortSwich);
    if (VOS_OK != ret) {
        LOG_ERROR(ret, "db_get_if_port_switch failed. ret: %u ", ret);
        return ;
    }

    ret= db_get_if_port_switch(obj_info[0].old_obj, &ulOldPortSwich);
    if (VOS_OK != ret) {
        LOG_ERROR(ret, "db_get_if_port_switch old failed. ret: %u ", ret);
        return ;
    }

    return ;
}

unsigned int static_if_L2ifSubL2L3SwitchEvt(db_connect_t sub_conn)
{
    db_object object;
    db_subid sub_id;
    int ret;

  
    if (g_stbesl2ifeventflag.ucL2L3SwichFlg) {
        return VOS_OK;
    }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u ", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u ", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u ", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "port_switch", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add if.port_switch field failed. ret: %u ", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_L2ifl2l3SwitchCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u ", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed. ");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    g_stbesl2ifeventflag.ucL2L3SwichFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

unsigned int BDS_TrunkGetTrunkIdByIfIndex(unsigned int ulIfIndex)
{
    int ret;
    unsigned int ulTrunkId = INVALID_IFINDEX;
    db_object object = NULL;
    db_root_iterator root_iterator = NULL;
    db_trunk_trunk_k_if_t trunkKey;
    db_key_info_t keyInfo;

    trunkKey.trunk_if_index = ulIfIndex;
    keyInfo.key_len = sizeof(db_trunk_trunk_k_if_t);
    keyInfo.index_id = DB_TRUNK_TRUNK_K_IF_ID;

    ret = db_create_trunk_obj(QUERY_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        return 0;
    }

    ret = db_create_root_iter(object, &keyInfo, &trunkKey, &root_iterator);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to db_create_root_iter. ret: %u !", ret);
        db_release_trunk_object(object);
        return ulTrunkId;
    }

    ret = db_root_iterator_next(root_iterator); // 唯一索引
    if (ret == STATUS_OK) {
        ret = db_get_trunk_trunk_id(object, &ulTrunkId);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to read if trunk_if_index. ret: %u !", ret);
        }
    }

    db_release_trunk_object(object);
    return ulTrunkId;
}

unsigned int BDS_IF_INTF_GetTrunkIdByIfIndex(unsigned int ulIfIndex, unsigned int *pulTrunkID)
{
    unsigned int ulTrunkID = INVALID_IFINDEX;

    if (pulTrunkID == NULL) {
        return STATUS_INVALID_ARGUMENT;
    }

    ulTrunkID = BDS_TrunkGetTrunkIdByIfIndex(ulIfIndex);
    if (ulTrunkID == INVALID_IFINDEX) {
        return VOS_ERR;
    }

    *pulTrunkID = ulTrunkID;

    return VOS_OK;
}

static_if_TRUNK_EVT_STATISTIC_S *static_if_TrunkGetEventStatic()
{
    static_if_TRUNK_EVT_STATISTIC_S *pstbestrunktstatic = NULL;

    if (g_pstbestrunkeventstatic == NULL) {
        g_pstbestrunkeventstatic = (static_if_TRUNK_EVT_STATISTIC_S *)malloc(sizeof(static_if_TRUNK_EVT_STATISTIC_S));
        if (g_pstbestrunkeventstatic == NULL) {
            return NULL;
        }
        (void)memset_s(g_pstbestrunkeventstatic, sizeof(static_if_TRUNK_EVT_STATISTIC_S), 0, sizeof(static_if_TRUNK_EVT_STATISTIC_S));
    }

    pstbestrunktstatic = g_pstbestrunkeventstatic;

    return pstbestrunktstatic;
}

unsigned int static_if_TrunkStateChangeProc(unsigned int ulIfIndex, unsigned int ulNewLinkStatus, unsigned int ulOldLinkStatus)
{
    unsigned int ulTrunkID = INVALID_IFINDEX;
    int ret;
    static_if_TRUNK_EVT_STATISTIC_S *pstbestrunkstatic = NULL;

    pstbestrunkstatic = static_if_TrunkGetEventStatic();

    if (pstbestrunkstatic == NULL) {
        return STATUS_INVALID_ARGUMENT;
    }

    if (ulNewLinkStatus == ulOldLinkStatus) {
        return VOS_OK;
    }

    ret= BDS_IF_INTF_GetTrunkIdByIfIndex(ulIfIndex, &ulTrunkID);
    if (ret!= VOS_OK) {
        return VOS_ERR;
    }

  
    if (ulNewLinkStatus == 1) {
        pstbestrunkstatic->ulTrunkStateUp++;
    }

  
    if (ulNewLinkStatus == 0) {
        pstbestrunkstatic->ulTrunkStateDown++;
    }

    return VOS_OK;
}

void static_if_TrunkStateChangeCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int ulIfIndex = INVALID_IFINDEX;
    unsigned int ulNewLinkStatus = 0;
    unsigned int ulOldLinkStatus = 0;

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

  
    static_if_set_sub_cb_cnt(user_data);
    
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters.");
        return ;
    }

    if (obj_info[0].event_type == OBJ_MODIFIED) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event.");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get logic slot. ret: %u !", ret);
            return ;
        }

      
        ret = db_get_if_linkup(obj_info[0].object, &ulNewLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u !", ret);
            return ;
        }

      
        ret = db_get_if_linkup(obj_info[0].old_obj, &ulOldLinkStatus);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u !", ret);
            return ;
        }
    }

    (void)static_if_TrunkStateChangeProc(ulIfIndex, ulNewLinkStatus, ulOldLinkStatus);
    return;
}

unsigned int static_if_SubscribeTrunkState(db_connect_t sub_conn)
{
    unsigned int ulIfType = 0;
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;
    static_if_TRUNK_EVT_SET_FLAG_S *pstbestrunkeventflag = NULL;

    pstbestrunkeventflag = static_if_TrunkGetEventFlag();
    if (pstbestrunkeventflag->ucTrunkStateFlg) { return VOS_OK; }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "linkup", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add if.linkup field fail. ret: %u !", ret);
        goto ReleaseObject;
    }

    ulIfType = 4;
    ret = db_sub_add_field(object, "if", "if_type", &ulIfType, sizeof(ulIfType), SUB_CMP_EQUAL);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add if.if_type field fail. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_TrunkStateChangeCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        LOG_ERROR(ret, "db_subscribe failed !");
        ret = VOS_ERR;
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    pstbestrunkeventflag->ucTrunkStateFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

unsigned int BDS_TrunkGetIndexByTrunkID(unsigned int ulTrunkId)
{
    unsigned int ifTrunkIfIndex = INVALID_IFINDEX;
    int ret;
    db_object object = NULL;
    db_trunk_key_t trunkKey;
    db_key_info_t keyInfo;

    trunkKey.trunk_id = ulTrunkId;
    keyInfo.key_len = sizeof(db_trunk_key_t);
    keyInfo.index_id = DB_TRUNK_TRUNK_PK_ID;

    ret = db_create_trunk_obj(READ_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        return ifTrunkIfIndex;
    }

    ret = db_read_obj(object, &keyInfo, &trunkKey);
    if (ret != STATUS_OK) {
        // LOG_ERROR(ret, "Failed to read trunk object, ret: %u !", ret);
        db_release_trunk_object(object);
        return ifTrunkIfIndex;
    }

    ret = db_get_trunk_trunk_if_index(object, &ifTrunkIfIndex);
    if (ret != STATUS_OK) {
        // LOG_ERROR(ret, "Failed to get field trunk ifindex, ret: %u !", ret);
        ifTrunkIfIndex = INVALID_IFINDEX;
    }

    db_release_trunk_object(object);
    return ifTrunkIfIndex;
}

unsigned int static_if_GetLinkState(unsigned int ulIfIndex, unsigned int *pulIfLinkState)
{
    int ret;
    unsigned int ulIfLinkState = 0;
    db_object statusobject = NULL;
    db_if_key_t ifKey;
    db_key_info_t keyInfo;

    ifKey.ifindex = ulIfIndex;
    keyInfo.key_len = sizeof(db_if_key_t);
    keyInfo.index_id = DB_IF_IF_PK_ID;

    if(pulIfLinkState == NULL) {
        return VOS_ERR;
    }

    ret = db_create_if_obj(READ_OBJ, SYNC_MODE, &statusobject);
    if (STATUS_OK != ret) {
        LOG_ERROR(ret, "db_create_if_obj.");
        return VOS_ERR;
    }

    ret = db_read_obj(statusobject, &keyInfo, &ifKey);
    if (ret != STATUS_OK) {
        // LOG_ERROR(ret, "failed to read if object. ret: %u", ret);
        goto RELEASE_OBJECT;
    }

    ret = db_get_if_linkup(statusobject, &ulIfLinkState);
    if (ret != STATUS_OK) {
        // LOG_ERROR(ret, "failed to get field ulIfLineState. ret: %u", ret);
        goto RELEASE_OBJECT;
    }

    *pulIfLinkState = (ulIfLinkState == 1) ? 1 : 0;

RELEASE_OBJECT:
    db_release_if_object(statusobject);
    return ret;
}

unsigned int BDS_TrunkGetLinkStatus(unsigned int ulTrunkId, unsigned int *pulIfLinkState)
{
    unsigned int ulTrunkIfIndex = 0;

    if (pulIfLinkState == NULL) {
        return VOS_ERR;
    }

    ulTrunkIfIndex = BDS_TrunkGetIndexByTrunkID(ulTrunkId);

    return static_if_GetLinkState(ulTrunkIfIndex, pulIfLinkState);
}

unsigned int static_if_TrunkMemberStateChangeProc(unsigned int ulTrunkId, unsigned int ulIfIndex, unsigned int ulLinkState)
{
    int ret;
    unsigned int ulTrunkLinkState = 0;
    static_if_TRUNK_EVT_STATISTIC_S *pstbestrunkstatic = NULL;

    pstbestrunkstatic = static_if_TrunkGetEventStatic();

    if (pstbestrunkstatic == NULL) {
        return VOS_ERR;
    }

    ret = BDS_TrunkGetLinkStatus(ulTrunkId, &ulTrunkLinkState);
    if (ret != VOS_OK) {
        return VOS_OK;
    }

    if (ulLinkState == 1) {

        pstbestrunkstatic->ulTrunkMemberUp++;
    }

    if (ulLinkState == 0) {

        pstbestrunkstatic->ulTrunkMemberDown++;
    }


    pstbestrunkstatic->ulTrunkMemberUpDown++;

    return VOS_OK;
}

void static_if_TrunkMemberStateChangeCallBack(char *table_name, db_sub_obj_info *obj_info,
                                              uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int ulTrunkId = INVALID_IFINDEX;
    unsigned int ulIfIndex = INVALID_IFINDEX;
    unsigned int ulTrunkIfIndex;
    unsigned int ulLinkState = 0;
    unsigned int ulOldLinkState = 0;

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_set_sub_cb_cnt(user_data);
    
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters.");
        return ;
    }

    if (obj_info[0].event_type != OBJ_MODIFIED) {
        return ;
    }

    ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Failed to get if.ifindex. ret: %u !", ret);
        return ;
    }

    ret = BDS_IF_INTF_GetTrunkIdByIfIndex(ulIfIndex, &ulTrunkId);
    if (ret != VOS_OK) {
        // LOG_ERROR(ret, "Failed to get ulTrunkId. ret: %u !", ret);
        return ;
    }

    ulTrunkIfIndex = BDS_TrunkGetIndexByTrunkID(ulTrunkId);
    if (ulTrunkIfIndex == 0 || ulTrunkIfIndex == INVALID_IFINDEX) {
        // TESET_LOG_INFO("Needn't to process for un trunk member. ulTrunkIfIndex: %u !", ulTrunkIfIndex);
        return ;
    }

    ret = db_get_if_linkup(obj_info[0].object, &ulLinkState);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Failed to get if.linkup. ret: %u !", ret);
        return ;
    }

    ret = db_get_if_linkup(obj_info[0].old_obj, &ulOldLinkState);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Failed to get old if.linkup !");
        return ;
    }

    if (ulOldLinkState == ulLinkState) {
        return ;
    }

    (void)static_if_TrunkMemberStateChangeProc(ulTrunkId, ulIfIndex, ulLinkState);
    return;
}

unsigned int static_if_SubscribeTrunkMemberStateChangeEvent(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;
    static_if_TRUNK_EVT_SET_FLAG_S *pstbestrunkeventflag = NULL;

    pstbestrunkeventflag = static_if_TrunkGetEventFlag();
    if (pstbestrunkeventflag->ucTrunkMemberStateFlg) { return VOS_OK; }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event fail. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "if", "linkup", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add if.trunk_id field fail. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_TrunkMemberStateChangeCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function fail. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        ret = VOS_ERR;
        LOG_ERROR(ret, "db_subscribe fail !");
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    pstbestrunkeventflag->ucTrunkMemberStateFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_TrunkMemberOpCallBack(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num, void *user_data)
{
    int ret;
    unsigned int ulIfIndex = INVALID_IFINDEX;
    unsigned int ulNewTrunkId = INVALID_IFINDEX;
    unsigned int ulOldTrunkId = INVALID_IFINDEX;
    db_child_iterator child_iterator = NULL;
    db_child_iterator old_child_iterator = NULL;

    static_if_NULL_CHECK(obj_info);
    static_if_NULL_CHECK(table_name);

    static_if_set_sub_cb_cnt(user_data);
    
  
    if (obj_info_num != 1) {
        LOG_ERROR(-1, "Failed to get the input parameters.");
        return ;
    }

    if (obj_info[0].event_type == OBJ_MODIFIED) {
        // TESET_LOG_INFO("Receive OBJ_MODIFIED event.");

        ret = db_get_if_ifindex(obj_info[0].object, &ulIfIndex);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get logic slot. ret: %u !", ret);
            return ;
        }

        ret = db_create_child_node(obj_info[0].object, NULL, "l2", NULL, NULL, &child_iterator);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to create if.l2 child object. ret: %u !", ret);
            return ;
        }

      
        ret = db_get_if_l2_trunk_id(child_iterator, &ulNewTrunkId);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u !", ret);
            return ;
        }

        ret = db_create_child_node(obj_info[0].old_obj, NULL, "l2", NULL, NULL, &old_child_iterator);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to create if.l2 old child object. ret: %u !", ret);
            return ;
        }

      
        ret = db_get_if_l2_trunk_id(old_child_iterator, &ulOldTrunkId);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "failed to get if new link status. ret: %u !", ret);
            return ;
        }
    }

    return;
}

unsigned int static_if_SubscribeTrunkMemberOpration(db_connect_t sub_conn)
{
    db_object object = NULL;
    db_subid sub_id = NULL;
    int ret;
    static_if_TRUNK_EVT_SET_FLAG_S *pstbestrunkeventflag = NULL;

    pstbestrunkeventflag = static_if_TrunkGetEventFlag();
    if (pstbestrunkeventflag->ucTrunkMemberOpFlg) { return VOS_OK; }

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "failed to create if object. ret: %u !", ret);
        return VOS_ERR;
    }

  
    ret = db_sub_add_event(object, OBJ_MODIFIED);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event fail. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add push_op. ret: %u !", ret);
        goto ReleaseObject;
    }

  
    ret = db_sub_add_field(object, "l2", "trunk_id", NULL, 0, 0);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add if.linkup field fail. ret: %u !", ret);
        goto ReleaseObject;
    }

    ret = db_sub_reg_callback(object, static_if_TrunkMemberOpCallBack, &(g_if_subid[sid]));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed. ret: %u !", ret);
        goto ReleaseObject;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &sub_id);
    if (sub_id == NULL) {
        ret = VOS_ERR;
        LOG_ERROR(ret, "db_subscribe fail !");
        goto ReleaseObject;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, sub_id);

    pstbestrunkeventflag->ucTrunkMemberOpFlg = 1;

ReleaseObject:
    db_release_if_object(object);
    return ret;
}

void static_if_subscribe_default_callback(char *table_name, db_sub_obj_info *obj_info, uint32_t obj_info_num,
                                             void *user_data)
{
    db_object object = NULL;

    if (NULL == obj_info) {
        return;
    }

    static_if_set_sub_cb_cnt(user_data);
    
    switch (obj_info->event_type) {
        case OBJ_INSERTED:
            object = obj_info->object;
            break;
        case OBJ_MODIFIED:
            object = obj_info->object;
            break;
        case OBJ_DELETED:
            object = obj_info->old_obj;
            break;
        default:
            return;
    }

    if (NULL == object) {
        return;
    }
    
    int ret;
    unsigned int ifindex = 0;
    unsigned int linkup = 0;

    ret = db_get_if_ifindex(object, &ifindex);
    if (ret != STATUS_OK) {
        return;
    }

    ret = db_get_if_linkup(object, &linkup);
    if (ret != STATUS_OK) {
        return;
    }

    return;
}

unsigned int static_if_SubscribePushKey(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;
    db_subid subId = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB create object failed, ret %d.", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe iffrom DB add event failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_reg_callback(object, static_if_subscribe_default_callback, &(g_if_subid[sid]));
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if set callback failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &subId);
    if (subId == NULL) {
        LOG_ERROR(ret, "Subscribe from DB failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, subId);

    g_ifSubId = subId;
    db_release_if_object(object);

    return VOS_OK;
}

unsigned int static_if_SubscribePushCurData(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;
    db_subid subId = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB create object failed, ret %d.", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe iffrom DB add event failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_add_push_op(object, PUSH_CUR_DATA);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB add push type failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_reg_callback(object, static_if_subscribe_default_callback, &(g_if_subid[sid]));
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if set callback failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &subId);
    if (subId == NULL) {
        LOG_ERROR(ret, "Subscribe from DB failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, subId);

    g_ifSubId = subId;
    db_release_if_object(object);

    return VOS_OK;
}

unsigned int static_if_SubscribePushOldData(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;
    db_subid subId = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB create object failed, ret %d.", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe iffrom DB add event failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_add_push_op(object, PUSH_OLD_DATA);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB add push type failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_reg_callback(object, static_if_subscribe_default_callback, &(g_if_subid[sid]));
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if set callback failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &subId);
    if (subId == NULL) {
        LOG_ERROR(ret, "Subscribe from DB failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, subId);

    g_ifSubId = subId;
    db_release_if_object(object);

    return VOS_OK;
}

unsigned int static_if_SubscribePushCurAndOldData(db_connect_t sub_conn)
{
    int ret;
    db_object object = NULL;
    db_subid subId = NULL;

    unsigned int sid;
    ret = static_if_get_sub_num(&sid);
    CHECK_OK_RET(ret, "static_if_get_sub_num");

    ret = db_create_if_obj(SUB_OBJ, SYNC_MODE, &object);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB create object failed, ret %d.", ret);
        return VOS_ERR;
    }

    ret = db_sub_add_event(object, OBJ_ALL_EVENTS);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe iffrom DB add event failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_add_push_op(object, PUSH_CUR_DATA | PUSH_OLD_DATA);
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if from DB add push type failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

    ret = db_sub_reg_callback(object, static_if_subscribe_default_callback, &(g_if_subid[sid]));
    if (ret != VOS_OK) {
        LOG_ERROR(ret, "Subscribe if set callback failed, ret %d.", ret);
        db_release_if_object(object);
        return VOS_ERR;
    }

        //subId = db_subscribe(object);
    ret = db_sub_with_conn(sub_conn, object, &subId);
    if (subId == NULL) {
        LOG_ERROR(ret, "Subscribe from DB failed.");
        db_release_if_object(object);
        return VOS_ERR;
    }
    static_print_sub_conf(object);
    (void)static_if_set_subid(sid, subId);

    g_ifSubId = subId;
    db_release_if_object(object);

    return VOS_OK;
}
