
#include "local_db_fe_port_statistics.h"
#include "local_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
#include "db_fe_port_statistics.h"
#include "cpu_cycles.h"
p_local_db_schema_model_t g_fe_port_statistics_model = NULL;

status_t local_db_fe_port_statistics_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                                        db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    DB_START_TEST_CPU_CYCLES(CreateObj);
    ret = db_create_fe_port_statistics_obj(obj_type, conn_type, &obj);
    DB_STOP_TEST_CPU_CYCLES(CreateObj);
    *object = obj;
    return ret;
}

void local_db_fe_port_statistics_release_db_obj_func(db_object object)
{
    DB_START_TEST_CPU_CYCLES(Release);
    db_release_fe_port_statistics_object(object);
    DB_STOP_TEST_CPU_CYCLES(Release);
}

status_t local_db_fe_port_statistics_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    DB_START_TEST_CPU_CYCLES(ResetObj);
    ret = db_reset_fe_port_statistics_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(ResetObj);

    return ret;
}

status_t local_db_fe_port_statistics_create_db_batch_obj_func(db_conn_type type, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    DB_START_TEST_CPU_CYCLES(CreateObj);
    ret = db_create_fe_port_statistics_obj(BATCH_OPERATION, type, &obj);
    DB_STOP_TEST_CPU_CYCLES(CreateObj);
    *object = obj;
    return ret;
}

status_t local_db_fe_port_statistics_reset_db_bobj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;
    DB_START_TEST_CPU_CYCLES(ResetObj);
    ret = db_reset_fe_port_statistics_batch_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(ResetObj);

    return ret;
}

status_t local_db_fe_port_statistics_primary_key_malloc_func(void **key, uint32_t *length)
{
    *length = sizeof(db_fe_port_statistics_key_t);
    *key = malloc(sizeof(db_fe_port_statistics_key_t));
    return 0;
}

void local_db_fe_port_statistics_primary_key_free_func(void *key)
{
    free(key);
}

status_t local_db_fe_port_statistics_struct_data_malloc_func(void **data, uint32_t *length)
{
    *length = sizeof(fe_port_statistics_struct_t);
    *data = malloc(*length);
    return 0;
}

void local_db_fe_port_statistics_struct_data_free_func(void *data)
{
    free(data);
}

status_t local_db_fe_port_statistics_get_field_func(db_object object, uint32_t get_field_cnt)
{
    status_t ret = STATUS_OK;

    uint32_t unit;
    uint32_t port;
    uint64_t uiVrIndex;

    ret = db_get_fe_port_statistics_unit(object, &unit);
    CHECK_OK_RET_DEBUG(ret, "set db_get_fe_port_statistics_unit failed.");
    ret = db_get_fe_port_statistics_port(object, &port);
    CHECK_OK_RET_DEBUG(ret, "set db_get_fe_port_statistics_port failed.");
    ret = db_get_fe_port_statistics_InOctets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InOctets field");

    ret = db_get_fe_port_statistics_InUcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InUcastPkts field");

    ret = db_get_fe_port_statistics_InNUcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InNUcastPkts field");

    ret = db_get_fe_port_statistics_InCongestionDrops(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InCongestionDrops field");

    ret = db_get_fe_port_statistics_InErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InErrors field");

    ret = db_get_fe_port_statistics_OutOctets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutOctets field");

    ret = db_get_fe_port_statistics_OutUcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutUcastPkts field");

    ret = db_get_fe_port_statistics_OutNUcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutNUcastPkts field");

    ret = db_get_fe_port_statistics_OutDiscards(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutDiscards field");

    ret = db_get_fe_port_statistics_OutErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutErrors field");

    ret = db_get_fe_port_statistics_OutQLen(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutQLen field");

    ret = db_get_fe_port_statistics_IpInReceives(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IpInReceives field");

    ret = db_get_fe_port_statistics_IpInHdrErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IpInHdrErrors field");

    ret = db_get_fe_port_statistics_IpForwDatagrams(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IpForwDatagrams field");

    ret = db_get_fe_port_statistics_IpInDiscards(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IpInDiscards field");

    ret = db_get_fe_port_statistics_Dot1dBpDelayExceDiscs(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dBpDelayExceDiscs field");

    ret = db_get_fe_port_statistics_Dot1dBpMtuExcDiscs(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dBpMtuExcDiscs field");

    ret = db_get_fe_port_statistics_Dot1dTpPortInFrames(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dTpPortInFrames field");

    ret = db_get_fe_port_statistics_Dot1dTpPortOutFrames(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dTpPortOutFrames field");

    ret = db_get_fe_port_statistics_Dot1dPortInDiscards(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dPortInDiscards field");

    ret = db_get_fe_port_statistics_EtherStatsDropEvents(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsDropEvents field");

    ret = db_get_fe_port_statistics_EtherStatsMulticastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsMulticastPkts field");

    ret = db_get_fe_port_statistics_EtherStatsBroadcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsBroadcastPkts field");

    ret = db_get_fe_port_statistics_EtherStatsUndersizePkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsUndersizePkts field");

    ret = db_get_fe_port_statistics_EtherStatsFragments(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsFragments field");

    ret = db_get_fe_port_statistics_EtherStats64Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats64Octets field");

    ret = db_get_fe_port_statistics_EtherStats65to127Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats65to127Octets field");

    ret = db_get_fe_port_statistics_EtherStats128to255Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats128to255Octets field");

    ret = db_get_fe_port_statistics_EtherStats256to511Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats256to511Octets field");

    ret = db_get_fe_port_statistics_EtherStats512to1023Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats512to1023Octets field");

    ret = db_get_fe_port_statistics_EtherStats1024to1518Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats1024to1518Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOversizePkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOversizePkts field");

    ret = db_get_fe_port_statistics_EtherStatsJabbers(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsJabbers field");

    ret = db_get_fe_port_statistics_EtherStatsOctets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOctets field");

    ret = db_get_fe_port_statistics_EtherStatsPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsPkts field");

    ret = db_get_fe_port_statistics_EtherStatsCRCAlignErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsCRCAlignErrors field");

    ret = db_get_fe_port_statistics_EtherStatsTXNoErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsTXNoErrors field");

    ret = db_get_fe_port_statistics_EtherStatsRXNoErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsRXNoErrors field");

    ret = db_get_fe_port_statistics_Dot3StatsAlignmentErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsAlignmentErrors field");

    ret = db_get_fe_port_statistics_Dot3StatsFCSErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsFCSErrors field");

    ret = db_get_fe_port_statistics_Dot3StatsFrameTooLongs(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsFrameTooLongs field");

    ret = db_get_fe_port_statistics_Dot3StatsSymbolErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsSymbolErrors field");

    ret = db_get_fe_port_statistics_Dot3ControlInUnknownOpcodes(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3ControlInUnknownOpcodes field");

    ret = db_get_fe_port_statistics_Dot3InPauseFrames(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3InPauseFrames field");

    ret = db_get_fe_port_statistics_Dot3OutPauseFrames(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3OutPauseFrames field");

    ret = db_get_fe_port_statistics_IfHCInOctets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCInOctets field");

    ret = db_get_fe_port_statistics_IfHCInUcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCInUcastPkts field");

    ret = db_get_fe_port_statistics_IfHCInMulticastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCInMulticastPkts field");

    ret = db_get_fe_port_statistics_IfHCInBroadcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCInBroadcastPkts field");

    ret = db_get_fe_port_statistics_IfHCOutOctets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCOutOctets field");

    ret = db_get_fe_port_statistics_IfHCOutUcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCOutUcastPkts field");

    ret = db_get_fe_port_statistics_IfHCOutMulticastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCOutMulticastPkts field");

    ret = db_get_fe_port_statistics_IfHCOutBroadcastPckts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCOutBroadcastPckts field");

    ret = db_get_fe_port_statistics_Ipv6IfStatsInReceives(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsInReceives field");

    ret = db_get_fe_port_statistics_Ipv6IfStatsInDiscards(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsInDiscards field");

    ret = db_get_fe_port_statistics_Ipv6IfStatsInHdrErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsInHdrErrors field");

    ret = db_get_fe_port_statistics_Ipv6IfStatsInMcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsInMcastPkts field");

    ret = db_get_fe_port_statistics_Ipv6IfStatsOutForwDatagrams(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsOutForwDatagrams field");

    ret = db_get_fe_port_statistics_Ipv6IfStatsOutDiscards(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsOutDiscards field");

    ret = db_get_fe_port_statistics_Ipv6IfStatsOutMcastPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsOutMcastPkts field");

    ret = db_get_fe_port_statistics_EtherRxOversizePkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherRxOversizePkts field");

    ret = db_get_fe_port_statistics_EtherTxOversizePkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherTxOversizePkts field");

    ret = db_get_fe_port_statistics_InLengthOutOfRanges(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InLengthOutOfRanges field");

    ret = db_get_fe_port_statistics_OutPurgeAndCellErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutPurgeAndCellErrors field");

    ret = db_get_fe_port_statistics_RxOversizeOverSizePktsFor_Rmon(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics RxOversizeOverSizePktsFor_Rmon field");

    ret = db_get_fe_port_statistics_RxErrorPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics RxErrorPkts field");

    ret = db_get_fe_port_statistics_TxErrorPkts(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics TxErrorPkts field");

    ret = db_get_fe_port_statistics_Timestamp_coll_cur(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Timestamp_coll_cur field");

    ret = db_get_fe_port_statistics_IfInDiscards(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfInDiscards field");

    ret = db_get_fe_port_statistics_IfInUnknownProtos(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfInUnknownProtos field");

    ret = db_get_fe_port_statistics_Dot3StatsSinColliFrames(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsSinColliFrames field");

    ret = db_get_fe_port_statistics_Dot3StatsMultiColliFrames(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsMultiColliFrames field");

    ret = db_get_fe_port_statistics_Dot3StatsSQETTestErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsSQETTestErrors field");

    ret = db_get_fe_port_statistics_Dot3StatsDeferredTransms(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsDeferredTransms field");

    ret = db_get_fe_port_statistics_Dot3StatsLateCollisions(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsLateCollisions field");

    ret = db_get_fe_port_statistics_Dot3StatsExcessiveCollisions(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsExcessiveCollisions field");

    ret = db_get_fe_port_statistics_Dot3StatsMacTransErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsMacTransErrors field");

    ret = db_get_fe_port_statistics_Dot3StatsCarrierSenseErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsCarrierSenseErrors field");

    ret = db_get_fe_port_statistics_Dot3StatsMacRcvErrors(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsMacRcvErrors field");

    ret = db_get_fe_port_statistics_SnmpIeee8021RcvPfc1Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc1Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021RcvPfc2Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc2Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021RcvPfc3Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc3Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021RcvPfc4Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc4Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021RcvPfc5Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc5Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021RcvPfc6Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc6Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021RcvPfc7Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc7Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021RcvPfc8Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc8Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021TransPfc1Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc1Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021TransPfc2Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc2Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021TransPfc3Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc3Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021TransPfc4Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc4Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021TransPfc5Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc5Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021TransPfc6Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc6Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021TransPfc7Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc7Req field");

    ret = db_get_fe_port_statistics_SnmpIeee8021TransPfc8Req(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc8Req field");

    ret = db_get_fe_port_statistics_EtherStatsIn1519toMaxOctets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsIn1519toMaxOctets field");

    ret = db_get_fe_port_statistics_EtherStatsOut64Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut64Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOut65to127Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut65to127Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOut128to255Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut128to255Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOut256to511Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut256to511Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOut512to1023Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut512to1023Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOut1024to1518Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut1024to1518Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOut1519toMaxOctets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut1519toMaxOctets field");

    ret = db_get_fe_port_statistics_EtherStatsOut1519to2047Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut1519to2047Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOut2048to4095Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut2048to4095Octets field");

    ret = db_get_fe_port_statistics_EtherStatsOut4095to9216Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut4095to9216Octets field");

    ret = db_get_fe_port_statistics_EtherStats1519to2047Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats1519to2047Octets field");

    ret = db_get_fe_port_statistics_EtherStats2048to4095Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats2048to4095Octets field");

    ret = db_get_fe_port_statistics_EtherStats4095to9216Octets(object, &uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats4095to9216Octets field");

    return ret;
}

status_t local_db_fe_port_statistics_set_field_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
    ret = db_set_fe_port_statistics_unit(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set db_set_fe_port_statistics_unit failed.");
    ret = db_set_fe_port_statistics_port(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set db_set_fe_port_statistics_port failed.");

    /* Set all root field. */

    ret = db_set_fe_port_statistics_InOctets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InOctets field");

    ret = db_set_fe_port_statistics_InUcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InUcastPkts field");

    ret = db_set_fe_port_statistics_InNUcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InNUcastPkts field");

    ret = db_set_fe_port_statistics_InCongestionDrops(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InCongestionDrops field");

    ret = db_set_fe_port_statistics_InErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InErrors field");

    ret = db_set_fe_port_statistics_OutOctets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutOctets field");

    ret = db_set_fe_port_statistics_OutUcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutUcastPkts field");

    ret = db_set_fe_port_statistics_OutNUcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutNUcastPkts field");

    ret = db_set_fe_port_statistics_OutDiscards(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutDiscards field");

    ret = db_set_fe_port_statistics_OutErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutErrors field");

    ret = db_set_fe_port_statistics_OutQLen(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutQLen field");

    ret = db_set_fe_port_statistics_IpInReceives(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IpInReceives field");

    ret = db_set_fe_port_statistics_IpInHdrErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IpInHdrErrors field");

    ret = db_set_fe_port_statistics_IpForwDatagrams(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IpForwDatagrams field");

    ret = db_set_fe_port_statistics_IpInDiscards(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IpInDiscards field");

    ret = db_set_fe_port_statistics_Dot1dBpDelayExceDiscs(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dBpDelayExceDiscs field");

    ret = db_set_fe_port_statistics_Dot1dBpMtuExcDiscs(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dBpMtuExcDiscs field");

    ret = db_set_fe_port_statistics_Dot1dTpPortInFrames(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dTpPortInFrames field");

    ret = db_set_fe_port_statistics_Dot1dTpPortOutFrames(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dTpPortOutFrames field");

    ret = db_set_fe_port_statistics_Dot1dPortInDiscards(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot1dPortInDiscards field");

    ret = db_set_fe_port_statistics_EtherStatsDropEvents(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsDropEvents field");

    ret = db_set_fe_port_statistics_EtherStatsMulticastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsMulticastPkts field");

    ret = db_set_fe_port_statistics_EtherStatsBroadcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsBroadcastPkts field");

    ret = db_set_fe_port_statistics_EtherStatsUndersizePkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsUndersizePkts field");

    ret = db_set_fe_port_statistics_EtherStatsFragments(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsFragments field");

    ret = db_set_fe_port_statistics_EtherStats64Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats64Octets field");

    ret = db_set_fe_port_statistics_EtherStats65to127Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats65to127Octets field");

    ret = db_set_fe_port_statistics_EtherStats128to255Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats128to255Octets field");

    ret = db_set_fe_port_statistics_EtherStats256to511Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats256to511Octets field");

    ret = db_set_fe_port_statistics_EtherStats512to1023Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats512to1023Octets field");

    ret = db_set_fe_port_statistics_EtherStats1024to1518Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats1024to1518Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOversizePkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOversizePkts field");

    ret = db_set_fe_port_statistics_EtherStatsJabbers(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsJabbers field");

    ret = db_set_fe_port_statistics_EtherStatsOctets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOctets field");

    ret = db_set_fe_port_statistics_EtherStatsPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsPkts field");

    ret = db_set_fe_port_statistics_EtherStatsCRCAlignErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsCRCAlignErrors field");

    ret = db_set_fe_port_statistics_EtherStatsTXNoErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsTXNoErrors field");

    ret = db_set_fe_port_statistics_EtherStatsRXNoErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsRXNoErrors field");

    ret = db_set_fe_port_statistics_Dot3StatsAlignmentErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsAlignmentErrors field");

    ret = db_set_fe_port_statistics_Dot3StatsFCSErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsFCSErrors field");

    ret = db_set_fe_port_statistics_Dot3StatsFrameTooLongs(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsFrameTooLongs field");

    ret = db_set_fe_port_statistics_Dot3StatsSymbolErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsSymbolErrors field");

    ret = db_set_fe_port_statistics_Dot3ControlInUnknownOpcodes(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3ControlInUnknownOpcodes field");

    ret = db_set_fe_port_statistics_Dot3InPauseFrames(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3InPauseFrames field");

    ret = db_set_fe_port_statistics_Dot3OutPauseFrames(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3OutPauseFrames field");

    ret = db_set_fe_port_statistics_IfHCInOctets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCInOctets field");

    ret = db_set_fe_port_statistics_IfHCInUcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCInUcastPkts field");

    ret = db_set_fe_port_statistics_IfHCInMulticastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCInMulticastPkts field");

    ret = db_set_fe_port_statistics_IfHCInBroadcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCInBroadcastPkts field");

    ret = db_set_fe_port_statistics_IfHCOutOctets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCOutOctets field");

    ret = db_set_fe_port_statistics_IfHCOutUcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCOutUcastPkts field");

    ret = db_set_fe_port_statistics_IfHCOutMulticastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCOutMulticastPkts field");

    ret = db_set_fe_port_statistics_IfHCOutBroadcastPckts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfHCOutBroadcastPckts field");

    ret = db_set_fe_port_statistics_Ipv6IfStatsInReceives(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsInReceives field");

    ret = db_set_fe_port_statistics_Ipv6IfStatsInDiscards(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsInDiscards field");

    ret = db_set_fe_port_statistics_Ipv6IfStatsInHdrErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsInHdrErrors field");

    ret = db_set_fe_port_statistics_Ipv6IfStatsInMcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsInMcastPkts field");

    ret = db_set_fe_port_statistics_Ipv6IfStatsOutForwDatagrams(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsOutForwDatagrams field");

    ret = db_set_fe_port_statistics_Ipv6IfStatsOutDiscards(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsOutDiscards field");

    ret = db_set_fe_port_statistics_Ipv6IfStatsOutMcastPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Ipv6IfStatsOutMcastPkts field");

    ret = db_set_fe_port_statistics_EtherRxOversizePkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherRxOversizePkts field");

    ret = db_set_fe_port_statistics_EtherTxOversizePkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherTxOversizePkts field");

    ret = db_set_fe_port_statistics_InLengthOutOfRanges(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics InLengthOutOfRanges field");

    ret = db_set_fe_port_statistics_OutPurgeAndCellErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics OutPurgeAndCellErrors field");

    ret = db_set_fe_port_statistics_RxOversizeOverSizePktsFor_Rmon(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics RxOversizeOverSizePktsFor_Rmon field");

    ret = db_set_fe_port_statistics_RxErrorPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics RxErrorPkts field");

    ret = db_set_fe_port_statistics_TxErrorPkts(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics TxErrorPkts field");

    ret = db_set_fe_port_statistics_Timestamp_coll_cur(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Timestamp_coll_cur field");

    ret = db_set_fe_port_statistics_IfInDiscards(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfInDiscards field");

    ret = db_set_fe_port_statistics_IfInUnknownProtos(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics IfInUnknownProtos field");

    ret = db_set_fe_port_statistics_Dot3StatsSinColliFrames(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsSinColliFrames field");

    ret = db_set_fe_port_statistics_Dot3StatsMultiColliFrames(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsMultiColliFrames field");

    ret = db_set_fe_port_statistics_Dot3StatsSQETTestErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsSQETTestErrors field");

    ret = db_set_fe_port_statistics_Dot3StatsDeferredTransms(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsDeferredTransms field");

    ret = db_set_fe_port_statistics_Dot3StatsLateCollisions(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsLateCollisions field");

    ret = db_set_fe_port_statistics_Dot3StatsExcessiveCollisions(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsExcessiveCollisions field");

    ret = db_set_fe_port_statistics_Dot3StatsMacTransErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsMacTransErrors field");

    ret = db_set_fe_port_statistics_Dot3StatsCarrierSenseErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsCarrierSenseErrors field");

    ret = db_set_fe_port_statistics_Dot3StatsMacRcvErrors(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics Dot3StatsMacRcvErrors field");

    ret = db_set_fe_port_statistics_SnmpIeee8021RcvPfc1Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc1Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021RcvPfc2Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc2Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021RcvPfc3Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc3Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021RcvPfc4Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc4Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021RcvPfc5Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc5Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021RcvPfc6Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc6Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021RcvPfc7Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc7Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021RcvPfc8Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021RcvPfc8Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021TransPfc1Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc1Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021TransPfc2Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc2Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021TransPfc3Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc3Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021TransPfc4Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc4Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021TransPfc5Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc5Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021TransPfc6Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc6Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021TransPfc7Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc7Req field");

    ret = db_set_fe_port_statistics_SnmpIeee8021TransPfc8Req(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics SnmpIeee8021TransPfc8Req field");

    ret = db_set_fe_port_statistics_EtherStatsIn1519toMaxOctets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsIn1519toMaxOctets field");

    ret = db_set_fe_port_statistics_EtherStatsOut64Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut64Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOut65to127Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut65to127Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOut128to255Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut128to255Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOut256to511Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut256to511Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOut512to1023Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut512to1023Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOut1024to1518Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut1024to1518Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOut1519toMaxOctets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut1519toMaxOctets field");

    ret = db_set_fe_port_statistics_EtherStatsOut1519to2047Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut1519to2047Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOut2048to4095Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut2048to4095Octets field");

    ret = db_set_fe_port_statistics_EtherStatsOut4095to9216Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStatsOut4095to9216Octets field");

    ret = db_set_fe_port_statistics_EtherStats1519to2047Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats1519to2047Octets field");

    ret = db_set_fe_port_statistics_EtherStats2048to4095Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats2048to4095Octets field");

    ret = db_set_fe_port_statistics_EtherStats4095to9216Octets(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "[fe_port_stat_db] batch set fe port statistics EtherStats4095to9216Octets field");

    return ret;
}

status_t local_db_fe_port_statistics_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
#ifdef NORMAL_INTF
    ret = local_db_fe_port_statistics_set_field_func(object, uiVrIndex, array, thread_local_num);
#else
    fe_port_statistics_struct_t obj_struct;
    memset(&obj_struct, 0, sizeof(obj_struct));

    obj_struct.unit = (uiVrIndex & (uint32_t)(~0));
    obj_struct.port = (uiVrIndex & (uint32_t)(~0));

    /* Set all root field. */

    obj_struct.InOctets = uiVrIndex;
    obj_struct.InUcastPkts = uiVrIndex;
    obj_struct.InNUcastPkts = uiVrIndex;
    obj_struct.InCongestionDrops = uiVrIndex;
    obj_struct.InErrors = uiVrIndex;
    obj_struct.OutOctets = uiVrIndex;
    obj_struct.OutUcastPkts = uiVrIndex;
    obj_struct.OutNUcastPkts = uiVrIndex;
    obj_struct.OutDiscards = uiVrIndex;
    obj_struct.OutErrors = uiVrIndex;
    obj_struct.OutQLen = uiVrIndex;
    obj_struct.IpInReceives = uiVrIndex;
    obj_struct.IpInHdrErrors = uiVrIndex;
    obj_struct.IpForwDatagrams = uiVrIndex;
    obj_struct.IpInDiscards = uiVrIndex;
    obj_struct.Dot1dBpDelayExceDiscs = uiVrIndex;
    obj_struct.Dot1dBpMtuExcDiscs = uiVrIndex;
    obj_struct.Dot1dTpPortInFrames = uiVrIndex;
    obj_struct.Dot1dTpPortOutFrames = uiVrIndex;
    obj_struct.Dot1dPortInDiscards = uiVrIndex;
    obj_struct.EtherStatsDropEvents = uiVrIndex;
    obj_struct.EtherStatsMulticastPkts = uiVrIndex;
    obj_struct.EtherStatsBroadcastPkts = uiVrIndex;
    obj_struct.EtherStatsUndersizePkts = uiVrIndex;
    obj_struct.EtherStatsFragments = uiVrIndex;
    obj_struct.EtherStats64Octets = uiVrIndex;
    obj_struct.EtherStats65to127Octets = uiVrIndex;
    obj_struct.EtherStats128to255Octets = uiVrIndex;
    obj_struct.EtherStats256to511Octets = uiVrIndex;
    obj_struct.EtherStats512to1023Octets = uiVrIndex;
    obj_struct.EtherStats1024to1518Octets = uiVrIndex;
    obj_struct.EtherStatsOversizePkts = uiVrIndex;
    obj_struct.EtherStatsJabbers = uiVrIndex;
    obj_struct.EtherStatsOctets = uiVrIndex;
    obj_struct.EtherStatsPkts = uiVrIndex;
    obj_struct.EtherStatsCRCAlignErrors = uiVrIndex;
    obj_struct.EtherStatsTXNoErrors = uiVrIndex;
    obj_struct.EtherStatsRXNoErrors = uiVrIndex;
    obj_struct.Dot3StatsAlignmentErrors = uiVrIndex;
    obj_struct.Dot3StatsFCSErrors = uiVrIndex;
    obj_struct.Dot3StatsFrameTooLongs = uiVrIndex;
    obj_struct.Dot3StatsSymbolErrors = uiVrIndex;
    obj_struct.Dot3ControlInUnknownOpcodes = uiVrIndex;
    obj_struct.Dot3InPauseFrames = uiVrIndex;
    obj_struct.Dot3OutPauseFrames = uiVrIndex;
    obj_struct.IfHCInOctets = uiVrIndex;
    obj_struct.IfHCInUcastPkts = uiVrIndex;
    obj_struct.IfHCInMulticastPkts = uiVrIndex;
    obj_struct.IfHCInBroadcastPkts = uiVrIndex;
    obj_struct.IfHCOutOctets = uiVrIndex;
    obj_struct.IfHCOutUcastPkts = uiVrIndex;
    obj_struct.IfHCOutMulticastPkts = uiVrIndex;
    obj_struct.IfHCOutBroadcastPckts = uiVrIndex;
    obj_struct.Ipv6IfStatsInReceives = uiVrIndex;
    obj_struct.Ipv6IfStatsInDiscards = uiVrIndex;
    obj_struct.Ipv6IfStatsInHdrErrors = uiVrIndex;
    obj_struct.Ipv6IfStatsInMcastPkts = uiVrIndex;
    obj_struct.Ipv6IfStatsOutForwDatagrams = uiVrIndex;
    obj_struct.Ipv6IfStatsOutDiscards = uiVrIndex;
    obj_struct.Ipv6IfStatsOutMcastPkts = uiVrIndex;
    obj_struct.EtherRxOversizePkts = uiVrIndex;
    obj_struct.EtherTxOversizePkts = uiVrIndex;
    obj_struct.InLengthOutOfRanges = uiVrIndex;
    obj_struct.OutPurgeAndCellErrors = uiVrIndex;
    obj_struct.RxOversizeOverSizePktsFor_Rmon = uiVrIndex;
    obj_struct.RxErrorPkts = uiVrIndex;
    obj_struct.TxErrorPkts = uiVrIndex;
    obj_struct.Timestamp_coll_cur = uiVrIndex;
    obj_struct.IfInDiscards = uiVrIndex;
    obj_struct.IfInUnknownProtos = uiVrIndex;
    obj_struct.Dot3StatsSinColliFrames = uiVrIndex;
    obj_struct.Dot3StatsMultiColliFrames = uiVrIndex;
    obj_struct.Dot3StatsSQETTestErrors = uiVrIndex;
    obj_struct.Dot3StatsDeferredTransms = uiVrIndex;
    obj_struct.Dot3StatsLateCollisions = uiVrIndex;
    obj_struct.Dot3StatsExcessiveCollisions = uiVrIndex;
    obj_struct.Dot3StatsMacTransErrors = uiVrIndex;
    obj_struct.Dot3StatsCarrierSenseErrors = uiVrIndex;
    obj_struct.Dot3StatsMacRcvErrors = uiVrIndex;
    obj_struct.SnmpIeee8021RcvPfc1Req = uiVrIndex;
    obj_struct.SnmpIeee8021RcvPfc2Req = uiVrIndex;
    obj_struct.SnmpIeee8021RcvPfc3Req = uiVrIndex;
    obj_struct.SnmpIeee8021RcvPfc4Req = uiVrIndex;
    obj_struct.SnmpIeee8021RcvPfc5Req = uiVrIndex;
    obj_struct.SnmpIeee8021RcvPfc6Req = uiVrIndex;
    obj_struct.SnmpIeee8021RcvPfc7Req = uiVrIndex;
    obj_struct.SnmpIeee8021RcvPfc8Req = uiVrIndex;
    obj_struct.SnmpIeee8021TransPfc1Req = uiVrIndex;
    obj_struct.SnmpIeee8021TransPfc2Req = uiVrIndex;
    obj_struct.SnmpIeee8021TransPfc3Req = uiVrIndex;
    obj_struct.SnmpIeee8021TransPfc4Req = uiVrIndex;
    obj_struct.SnmpIeee8021TransPfc5Req = uiVrIndex;
    obj_struct.SnmpIeee8021TransPfc6Req = uiVrIndex;
    obj_struct.SnmpIeee8021TransPfc7Req = uiVrIndex;
    obj_struct.SnmpIeee8021TransPfc8Req = uiVrIndex;
    obj_struct.EtherStatsIn1519toMaxOctets = uiVrIndex;
    obj_struct.EtherStatsOut64Octets = uiVrIndex;
    obj_struct.EtherStatsOut65to127Octets = uiVrIndex;
    obj_struct.EtherStatsOut128to255Octets = uiVrIndex;
    obj_struct.EtherStatsOut256to511Octets = uiVrIndex;
    obj_struct.EtherStatsOut512to1023Octets = uiVrIndex;
    obj_struct.EtherStatsOut1024to1518Octets = uiVrIndex;
    obj_struct.EtherStatsOut1519toMaxOctets = uiVrIndex;
    obj_struct.EtherStatsOut1519to2047Octets = uiVrIndex;
    obj_struct.EtherStatsOut2048to4095Octets = uiVrIndex;
    obj_struct.EtherStatsOut4095to9216Octets = uiVrIndex;
    obj_struct.EtherStats1519to2047Octets = uiVrIndex;
    obj_struct.EtherStats2048to4095Octets = uiVrIndex;
    obj_struct.EtherStats4095to9216Octets = uiVrIndex;


set_again:
    ret = db_set_fe_port_statistics_all_fields(object, &obj_struct, sizeof(obj_struct));
    if (ret == STATUS_QUEUE_EMPTY) {
        usleep(50);
        goto set_again;
    }
#endif

    return ret;
}

status_t local_db_fe_port_statistics_get_field_by_key_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_fe_port_statistics_key_t key_data = {0};
    key_info.index_id = DB_FE_PORT_STATISTICS_FE_PORT_STATISTICS_PK_ID;
    key_info.key_len = sizeof(key_data);
    key_data.unit = (uiVrIndex & (uint32_t)(~0));
    key_data.port = (uiVrIndex & (uint32_t)(~0));
    DB_START_TEST_CPU_CYCLES(Read);
    ret = db_read_obj(object, &key_info, &key_data);
    DB_STOP_TEST_CPU_CYCLES(Read);
    if (ret != STATUS_OK) {
        return ret;
    }
    fe_port_statistics_struct_t obj_struct;
    DB_START_TEST_CPU_CYCLES(GetAllField);
    ret = db_get_fe_port_statistics_all_fields(object, &obj_struct, sizeof(fe_port_statistics_struct_t));
    DB_STOP_TEST_CPU_CYCLES(GetAllField);
    return ret;
}

status_t local_db_fe_port_statistics_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, void *key_info, void *key_data)
{
    db_fe_port_statistics_key_t *pri_key_data = (db_fe_port_statistics_key_t *)key_data;
    db_key_info_t *pri_key_info = (db_key_info_t *)key_info;
    memset(pri_key_data, 0, sizeof(db_fe_port_statistics_key_t));
    memset(pri_key_info, 0, sizeof(db_key_info_t));
    pri_key_info->index_id = DB_FE_PORT_STATISTICS_FE_PORT_STATISTICS_PK_ID;
    pri_key_info->key_len = sizeof(db_fe_port_statistics_key_t);
    pri_key_data->unit = (uiVrIndex & (uint32_t)(~0));
    pri_key_data->port = (uiVrIndex & (uint32_t)(~0));
    return 0;
}

status_t local_db_fe_port_statistics_register_shcema_model(void *schema_model)
{
    if (g_fe_port_statistics_model == NULL) {
        g_fe_port_statistics_model = (p_local_db_schema_model_t)(schema_model);

        g_fe_port_statistics_model->obj_create_func = local_db_fe_port_statistics_create_db_obj_func;
        g_fe_port_statistics_model->obj_release_func = local_db_fe_port_statistics_release_db_obj_func;
        g_fe_port_statistics_model->obj_reset_func = local_db_fe_port_statistics_reset_db_obj_func;
        g_fe_port_statistics_model->bobj_reset_func = local_db_fe_port_statistics_reset_db_bobj_func;
        g_fe_port_statistics_model->bobj_create_func = local_db_fe_port_statistics_create_db_batch_obj_func;
        g_fe_port_statistics_model->obj_set_func = local_db_fe_port_statistics_set_obj_func;
        g_fe_port_statistics_model->malloc_primary_key_func = local_db_fe_port_statistics_primary_key_malloc_func;
        g_fe_port_statistics_model->free_primary_key_func = local_db_fe_port_statistics_primary_key_free_func;
        g_fe_port_statistics_model->malloc_struct_data_func = local_db_fe_port_statistics_struct_data_malloc_func;
        g_fe_port_statistics_model->free_struct_data_func = local_db_fe_port_statistics_struct_data_free_func;
        g_fe_port_statistics_model->getfield_bykey_func = local_db_fe_port_statistics_get_field_by_key_func;
        g_fe_port_statistics_model->key_set_func = local_db_fe_port_statistics_key_set_func;
        g_fe_port_statistics_model->getfield_func = local_db_fe_port_statistics_get_field_func;
    }
    return 0;
}
