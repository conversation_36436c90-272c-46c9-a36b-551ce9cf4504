#ifndef _YANG_COMMON_H_
#define _YANG_COMMON_H_
#include "gm_errno.h"
#include "gm_tm_yang.h"
#include "gm_yang.h"
#include "gm_server.h"
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <malloc.h>
#include <string.h>
#include <ctype.h>
#include <stdint.h>
#include "securec.h"
#include <sys/stat.h> 
#include <sys/types.h>
#include <dirent.h>
#include <sys/time.h>
#include <sys/epoll.h>
#include "gtest/gtest.h"
#include <assert.h>
#include <time.h>
#include <semaphore.h>
#include <pthread.h>
#include <regex.h>
#include <stdarg.h>
#include <sys/wait.h>
#include <sys/prctl.h>
#include <unistd.h>
#include <sys/syscall.h>

#ifdef __cplusplus
extern "C" {
#endif

#define SUCCESS 0
#define FAILED 1

#define YANG_SERVER_LOCATOR "shm:unix_emserver_yang"
#define LITE_SERVER_LOCATOR "shm:unix_emserver"

#if defined(TEST_USE_SHM)
    #if (RTOSV2X == 1)
        #define TEST_DEFAULT_SERVER_LOCATOR "shm:unix_emserver_yang"
    #else
        #define TEST_DEFAULT_SERVER_LOCATOR "shm:unix_emserver"
    #endif
#elif defined(TEST_USE_USOCKET)
#define TEST_DEFAULT_SERVER_LOCATOR  "usocket:unix_emserver"
#elif defined(TEST_USE_TCP)
#define TEST_DEFAULT_SERVER_LOCATOR  "tcp:127.0.0.1:54321"
#endif

/* test in GX */
#define IS_GX_MODE 0
/* test in 6730 */
#define IS_6730_MODE 0
/* asan */
#ifdef YANGDB_ASAN
    #ifndef IS_ASAN_MODE
        #define IS_ASAN_MODE 1
    #endif
#else
    #ifndef IS_ASAN_MODE
        #define IS_ASAN_MODE 0
    #endif
#endif
//长稳每个线程循环的次数，ASAN时使用
#define CYCLE_COUNT 10
//异步接口模式
#define IS_ASYNC_MODE 1
#define MAX_ASYNC_QUEUE 5

//获取内核线程pid,系统内唯一
#define gettid() syscall(__NR_gettid)

#define CheckReturn(ret, str)                           \
do                                               \
{                                                \
    if (ret != SUCCESS)                            \
    {                                            \
        printf("%s failed. ret = %d. line:%d.\n", str, ret, __LINE__);  \
        return 0;                        \
    }                                            \
} while (0)

#define DMDB_MAX_SCHEMA_LENGTH (1024 * 1024)

status_t dmdb_create_object_by_json(schema_t schema, const char *json, object_t *object);
void yang_atomic_batch_write_object(connection_t conn, uint32_t total_num, object_t *object);

#ifndef READ_FILE_CONTENT
#define READ_FILE_CONTENT
long readFileContent(char const* path, char** buf, bool add_nul)
{
    FILE*  fp;
    size_t fsz;
    long   off_end;
    int    rc;
    char *pBuffer = NULL;
    /* Open the file */
    fp = fopen(path, "rb");
    if (NULL == fp)
    {
        return -1L;
    }
    /* Seek to the end of the file */
    rc = fseek(fp, 0L, SEEK_END);
    if (0 != rc)
    {
        fclose(fp);
        return -1L;
    }
    /* Byte offset to the end of the file (size) */
    if (0 > (off_end = ftell(fp)))
    {
        fclose(fp);
        return -1L;
    }
    fsz = (size_t)off_end;
    /* Allocate a buffer to hold the whole file */
    pBuffer = (char*)malloc(fsz + (int)add_nul);
    if (NULL == pBuffer)
    {
        fclose(fp);
        return -1L;
    }
    /* Rewind file pointer to start of file */
    (void)fseek(fp, 0L, SEEK_SET);
    /* Slurp file into buffer */
    if (fsz != fread(pBuffer, 1, fsz, fp))
    {
        free(pBuffer);
        fclose(fp);
        return -1L;
    }
/* Close the file */
    if (EOF == fclose(fp))
    {
        free(pBuffer);
        return -1L;
    }
    if (add_nul)
    {
        /* Make sure the buffer is NUL-terminated, just in case */
        pBuffer[fsz] = '\0';
    }
    *buf = pBuffer;
    /* Return the file size */
    return (long)fsz;
}
#endif

static void print_object_text(object_t object, bool print_null=false)
{
    char *object_text = NULL;

    if(print_null == true){
        ASSERT_EQ(dmdb_create_object_text(object, DMDB_CONVERT_BYTES_TO_HEX, &object_text), STATUS_OK);
    }
    else{
        ASSERT_EQ(dmdb_create_object_text(object, DMDB_CONVERT_BYTES_TO_HEX | DMDB_NOT_EXPORT_NULL_FIELD, &object_text), STATUS_OK);
    }
    printf("The json text is:\n %s \n", object_text);
    dmdb_release_object_text(object_text);
    object_text = NULL;
}

static void write_object_by_json(connection_t conn, schema_t schema, const char* obj_json_path)
{
    char *object_json = NULL;
    object_t object;

    readFileContent(obj_json_path, &object_json, true);
    ASSERT_NE((void *)NULL, object_json);
    // printf("[write object]: %s\n", object_json);
    ASSERT_EQ(STATUS_OK, dmdb_create_object_by_json(schema, object_json, &object));
    free(object_json);
    // print_object_text(object, true);
    yang_atomic_batch_write_object(conn, 1, &object);
    ASSERT_EQ(STATUS_OK, dmdb_release_object(object));
}

static void create_expect_object(schema_t schema, const char* exp_obj_json_path, object_t* expect_object)
{
    char* expect_json;

    readFileContent(exp_obj_json_path, &expect_json, true);
    ASSERT_NE((void *)NULL, expect_json);
    //printf("[expect object]:%s\n", expect_json);
    ASSERT_EQ(STATUS_OK, dmdb_create_object_by_json(schema, expect_json, expect_object));
    free(expect_json);
}

int32_t yang_object_compare(object_t src_obj, object_t dst_obj, bool compare_object_null=false)
{
    char *src_txt = NULL;
    char *dst_txt = NULL;
    int ret;
    //printf("src:%p, dst：%p\n", src_obj, dst_obj);

    if(compare_object_null == true){
        ret = dmdb_create_object_text(src_obj, (DMDB_CONVERT_BYTES_TO_HEX), &src_txt);
        if (ret != STATUS_OK) {
            return ret;
        }
        ret = dmdb_create_object_text(dst_obj, (DMDB_CONVERT_BYTES_TO_HEX), &dst_txt);
        if (ret != STATUS_OK) {
            return ret;
        }
    }
    else{
        ret = dmdb_create_object_text(src_obj, (DMDB_CONVERT_BYTES_TO_HEX|DMDB_NOT_EXPORT_NULL_FIELD), &src_txt);
        if (ret != STATUS_OK) {
            return ret;
        }
        ret = dmdb_create_object_text(dst_obj, (DMDB_CONVERT_BYTES_TO_HEX|DMDB_NOT_EXPORT_NULL_FIELD), &dst_txt);
        if (ret != STATUS_OK) {
            return ret;
        }
    }

    ret = strcmp(src_txt, dst_txt);
    if (src_txt != NULL) {
        dmdb_release_object_text(src_txt);
    }
    if (dst_txt != NULL) {
        dmdb_release_object_text(dst_txt);
    }

    return ret;
}

/* object_r->file 使用完后删除
    writeFileContent(object_r, "object_expect/001_01_object_array_node_leaflist_real5.json");
*/
int writeFileContent(object_t object, char const* path, bool write_null=false)
{
    FILE *fp;
    int ret;
    char *object_text;
    if(write_null == true){
        ret = dmdb_create_object_text(object, DMDB_CONVERT_BYTES_TO_HEX, &object_text);
        //printf("The write text is:\n %s \n", object_text);
        if (ret != STATUS_OK) {
            return ret;
        }
    }
    else{
        ret = dmdb_create_object_text(object, (DMDB_CONVERT_BYTES_TO_HEX|DMDB_NOT_EXPORT_NULL_FIELD), &object_text);
        //printf("The write text is:\n %s \n", object_text);
        if (ret != STATUS_OK) {
            return ret;
        }
    }

    /* Open the file */
    fp = fopen(path, "wb");

    if (NULL == fp)
    {
        return -1L;
    }

    fprintf(fp, object_text);
    fclose(fp);
    
    if (object_text != NULL) {
        dmdb_release_object_text(object_text);
    }
    return 0;
}

void yang_creat_table(const char *schema_name, connection_t *conn, schema_t *schema, const char *table_name)
{
    status_t ret=0;
    char *schema_json = NULL;
    readFileContent(schema_name, &schema_json, true);
    //printf("schema json is : %s\n", schema_json);
    ASSERT_NE((void *)NULL,schema_json);

    ret = dmdb_connect(TEST_DEFAULT_SERVER_LOCATOR, NULL, "osc", conn);
    ASSERT_EQ(STATUS_OK, ret);

    dmdb_drop_database(*conn, "osc");
    ASSERT_EQ(STATUS_OK, dmdb_create_database(*conn, "osc", NULL));
    ASSERT_EQ(STATUS_OK, dmdb_select_database(*conn, "osc"));
    ASSERT_EQ(STATUS_OK, dmdb_create_table_sync(*conn, table_name, schema_json, NULL));

    free(schema_json);
    ret = dmdb_get_schema_by_table_name_sync(*conn, table_name, 0, schema);
    ASSERT_EQ(STATUS_OK, ret);
    ASSERT_NE((void *)NULL, *schema);
}

void yang_creat_database_table(connection_t conn, const char *schema_path, schema_t *schema, const char *database_name, const char *table_name)
{
    char *schema_json = NULL;
    readFileContent(schema_path, &schema_json, true);
    //printf("schema json is : %s\n", schema_json);
    ASSERT_NE((void *)NULL,schema_json);
    //删除数据集、包括其中的表定义和所有数据
    dmdb_drop_database(conn, database_name);
    ASSERT_EQ(STATUS_OK, dmdb_create_database(conn, database_name, NULL));
    ASSERT_EQ(STATUS_OK, dmdb_select_database(conn, database_name));
    ASSERT_EQ(STATUS_OK, dmdb_create_table_sync(conn, table_name, schema_json, NULL));
    free(schema_json);
    schema_json = NULL;
    ASSERT_EQ(STATUS_OK, dmdb_get_schema_by_table_name_sync(conn, table_name, 0, schema));
    ASSERT_NE((void *)NULL, *schema);
}

void yang_creat_table_for_database(connection_t conn, const char *schema_path, schema_t *schema, const char *table_name)
{
    char *schema_json = NULL;
    readFileContent(schema_path, &schema_json, true);
    //printf("schema json is : %s\n", schema_json);
    ASSERT_NE((void *)NULL,schema_json);
    dmdb_drop_table_sync(conn, table_name);
    ASSERT_EQ(STATUS_OK, dmdb_create_table_sync(conn, table_name, schema_json, NULL));
    free(schema_json);
    schema_json = NULL;
    ASSERT_EQ(STATUS_OK, dmdb_get_schema_by_table_name_sync(conn, table_name, 0, schema));
    ASSERT_NE((void *)NULL, *schema);
}

/*
  T1_K2_value=-1, k2=null;
  T1_K2_value= 0, k2=empty;
  T1_K2_value<-1, k2=k2;
  T1_K2_value>0, k2=value;
*/
void yang_edit_array_node_item(schema_t schema, node_t node, const char *key_name, node_t *item, int T1_key_value, int position_value, dmdb_edit_operation_e operation, dmdb_array_pos_e position, int T1_K2_value=-2)
{
    dmdb_value_t value;
    value.empty = false;
    value.type = DMDB_DATATYPE_UINT32;
    obj_key_t hk;
    obj_key_t position_key;
    if(operation == DMDB_EDIT_NONE ){
        return;
    }
    value.value.uint_val = position_value;
    ASSERT_EQ(STATUS_OK, dmdb_create_key(schema, key_name, 1, &value, &position_key));
    value.value.uint_val = T1_key_value;
    ASSERT_EQ(STATUS_OK, dmdb_create_key(schema, key_name, 1, &value, &hk));
    ASSERT_EQ(STATUS_OK, dmdb_edit_array_node(node, operation, position, position_key, hk, item));
    ASSERT_EQ(STATUS_OK, dmdb_release_key(hk));
    ASSERT_EQ(STATUS_OK, dmdb_release_key(position_key));
    if((operation == DMDB_EDIT_DELETE) || (operation == DMDB_EDIT_REMOVE) ){
        return;
    }
    ASSERT_EQ(STATUS_OK, dmdb_edit_field(*item, "K1", operation, &value));
    if(T1_K2_value > 0) {
        value.value.uint_val = T1_K2_value;
        ASSERT_EQ(STATUS_OK, dmdb_edit_field(*item, "K2", operation, &value));
    }
    else if(T1_K2_value == -1) {
        value.type = DMDB_DATATYPE_NULL;
        ASSERT_EQ(STATUS_OK, dmdb_edit_field(*item, "K2", operation, &value));
    }
    else if(T1_K2_value == 0) {
        value.empty = true;
        value.type = DMDB_DATATYPE_UINT32;
        ASSERT_EQ(STATUS_OK, dmdb_edit_field(*item, "K2", operation, &value));
    }

}

void yang_edit_array_node_item_mk3(schema_t schema, node_t node, const char *key_name, node_t *item, dmdb_edit_operation_e operation, const char *string1, const char *string2, uint32_t value32, dmdb_array_pos_e position, const char *pos_string1=NULL, const char *pos_string2=NULL, uint32_t pos_value32=0, status_t expect_ret=STATUS_OK)
{
    obj_key_t hk;
    obj_key_t position_key;

    dmdb_value_t value[3];
    value[0].empty = false;
    value[1].empty = false;
    value[2].empty = false;
    value[0].type = DMDB_DATATYPE_STRING;
    value[0].value.ref = string1;
    value[1].type = DMDB_DATATYPE_STRING;
    value[1].value.ref = string2;
    value[2].type = DMDB_DATATYPE_UINT32;
    value[2].value.uint_val = value32;
    //printf("string1:%s, string2:%s, value32:%u,position:%d\n", string1, string2, value32, position);
    ASSERT_EQ(STATUS_OK, dmdb_create_key(schema, key_name, 3, value, &hk));
    if(position == DMDB_ARRAY_FIRST || position == DMDB_ARRAY_LAST || position == DMDB_ARRAY_STAY){
        ASSERT_EQ(expect_ret, dmdb_edit_array_node(node, operation, position, NULL, hk, item));
        ASSERT_EQ(STATUS_OK, dmdb_release_key(hk));
    }
    else{
        //printf("pos_string1:%s, pos_string2:%s, pos_value32:%u\n", pos_string1, pos_string2, pos_value32);
        if(pos_string1 == NULL || pos_string2 == NULL ||  pos_value32==0){
            printf("pos_string1:%s, pos_string2:%s, pos_value32:%u\n", pos_string1, pos_string2, pos_value32);
            ASSERT_EQ(STATUS_OK, -1);
        }
        dmdb_value_t pos_value[3];
        pos_value[0].empty = false;
        pos_value[1].empty = false;
        pos_value[2].empty = false;
        pos_value[0].type = DMDB_DATATYPE_STRING;
        pos_value[0].value.ref = pos_string1;
        pos_value[1].type = DMDB_DATATYPE_STRING;
        pos_value[1].value.ref = pos_string2;
        pos_value[2].type = DMDB_DATATYPE_UINT32;
        pos_value[2].value.uint_val = pos_value32;
        ASSERT_EQ(STATUS_OK, dmdb_create_key(schema, key_name, 3, pos_value, &position_key));
        ASSERT_EQ(expect_ret, dmdb_edit_array_node(node, operation, position, position_key, hk, item));
        ASSERT_EQ(STATUS_OK, dmdb_release_key(hk));
        ASSERT_EQ(STATUS_OK, dmdb_release_key(position_key));
    }

    if(expect_ret == STATUS_OK && (operation == DMDB_EDIT_MERGE || operation == DMDB_EDIT_CREATE || operation == DMDB_EDIT_REPLACE)){
        ASSERT_EQ(STATUS_OK, dmdb_edit_field(*item, "protocol", operation, &value[0]));
        ASSERT_EQ(STATUS_OK, dmdb_edit_field(*item, "ip", operation, &value[1]));
        ASSERT_EQ(STATUS_OK, dmdb_edit_field(*item, "port", operation, &value[2]));
    }
}

static void yang_edit_field_uint32(node_t node, const char *field_name, dmdb_edit_operation_e operation, bool is_null, bool is_empty, uint32_t base_value)
{
    dmdb_value_t value;
    value.empty = false;
    value.type = DMDB_DATATYPE_UINT32;

    if(is_null == true){
        value.type = DMDB_DATATYPE_NULL;
    }
    if(is_empty == true){
        value.empty = true;
    }
    value.value.uint_val = base_value;

    ASSERT_EQ(STATUS_OK, dmdb_edit_field(node, field_name, operation, &value));
}
static void yang_edit_field_string(node_t node, const char *field_name, dmdb_edit_operation_e operation, bool is_null, bool is_empty, const char *string)
{
    dmdb_value_t value;
    value.empty = false;
    value.type = DMDB_DATATYPE_STRING;

    if(is_null == true){
        value.type = DMDB_DATATYPE_NULL;
    }
    if(is_empty == true){
        value.empty = true;
    }
    value.value.ref = string;

    ASSERT_EQ(STATUS_OK, dmdb_edit_field(node, field_name, operation, &value));
}

static void yang_edit_field_array_uint32(node_t node, const char *field_name, dmdb_edit_operation_e operation, dmdb_array_pos_e position, uint32_t base_value, uint32_t pos_value)
{
    dmdb_value_t value;
    value.empty = false;
    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = base_value;

    dmdb_value_t position_value;
    position_value.empty = false;
    position_value.type = DMDB_DATATYPE_UINT32;
    position_value.value.uint_val = pos_value;

    ASSERT_EQ(STATUS_OK, dmdb_edit_field_array(node, field_name, operation, position, &position_value, &value));
}
static void yang_edit_field_array_string(node_t node, const char *field_name, dmdb_edit_operation_e operation, dmdb_array_pos_e position, const char *string, const char *pos_string)
{
    dmdb_value_t value;
    value.empty = false;
    value.type = DMDB_DATATYPE_STRING;
    value.value.ref = string;

    dmdb_value_t position_value;
    position_value.empty = false;
    position_value.type = DMDB_DATATYPE_STRING;
    position_value.value.ref = pos_string;
    ASSERT_EQ(STATUS_OK, dmdb_edit_field_array(node, field_name, operation, position, &position_value, &value));
}

//for diff
char node_type[5][9] = {"FIELD", "NODE", "ARRAY", "ITEM", "LEAFLIST"};
char diff_operation[4][8] = {"UPDATE", "CREATE", "REMOVE", "INVALID"};
char diff_pos[5][7] = {"BEFORE", "AFTER", "FIRST", "LAST", "STAY"};
#define STRING_MAX  (1024*13+50)

void yang_sprintf_value(dmdb_value_t value, int *offset, char *string)
{
    unsigned int i = 0;
    if(value.type == DMDB_DATATYPE_NULL){
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "null");
    }

    else if(value.empty == true){
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "empty");
    }
    else {
        switch(value.type){
            case DMDB_DATATYPE_STRING:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%s", value.value.ref);
                break;
            case DMDB_DATATYPE_BYTES://fixed bytes
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "0x");
                for(i = 0; i < value.value.length; i++){
                    *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%02x", (uint8_t)(((char *)value.value.ref)[i]));
                }
                break;
            case DMDB_DATATYPE_BOOLEAN:
                if(value.value.bool_val == false){
                    *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "false");
                }
                else{
                    *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "true");
                }
                break;
            case DMDB_DATATYPE_INT8:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%d", value.value.char_val);
                break;
            case DMDB_DATATYPE_UINT8:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%u", value.value.byte_val);
                break;
            case DMDB_DATATYPE_INT16:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%d", value.value.short_val);
                break;
            case DMDB_DATATYPE_UINT16:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%u", value.value.ushort_val);
                break;
            case DMDB_DATATYPE_INT32:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%d", value.value.int_val);
                break;
            case DMDB_DATATYPE_UINT32:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%u", value.value.uint_val);
                break;
            case DMDB_DATATYPE_INT64:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%lld", value.value.long_val);
                break;
            case DMDB_DATATYPE_UINT64:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%llu", value.value.ulong_val);
                break;
            case DMDB_DATATYPE_FLOAT:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%f", value.value.float_val);
                break;
            case DMDB_DATATYPE_DOUBLE:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "%lf", value.value.double_val);
                break;
            case DMDB_DATATYPE_TIME:
            case DMDB_DATATYPE_BITS:
            default:
                *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "invalid");
                break;
        }
    }
}

void yang_sprintf_array_node_mk_field_1(dmdb_diff_data_t data, int *offset, char *string)
{
    dmdb_value_t value;
    int ret;
    ret = dmdb_get_field(data.node.node, "K1", &value); //member key field_name must be "K1"
    if(ret == STATUS_OK){
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "K1:");
        yang_sprintf_value(value, offset, string);
    }
    else{
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "K1:INVALID");   
    }
    *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos:%s", diff_pos[data.node.pos]);
    if(data.node.pos != DMDB_ARRAY_FIRST && data.node.pos != DMDB_ARRAY_STAY){
        ret = dmdb_get_field(data.node.pos_node, "K1", &value); //member key field_name must be "K1"
        if(ret == STATUS_OK){
            *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos_K1:");
            yang_sprintf_value(value, offset, string);
        } 
        else{
            *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos_K1:INVALID");   
        }
    }
    *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "\n");
}

void yang_sprintf_array_node_mk_field_3(dmdb_diff_data_t data, int *offset, char *string)
{
    dmdb_value_t value;
    int ret;
    ret = dmdb_get_field(data.node.node, "protocol", &value); //member key field_name must be "protocol"
    if(ret == STATUS_OK){
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "protocol:");
        yang_sprintf_value(value, offset, string);
    }
    else{
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "protocol:INVALID");   
    }
    ret = dmdb_get_field(data.node.node, "ip", &value); //member key field_name must be "ip"
    if(ret == STATUS_OK){
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", ip:");
        yang_sprintf_value(value, offset, string);
    }
    else{
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "ip:INVALID");   
    }
    ret = dmdb_get_field(data.node.node, "port", &value); //member key field_name must be "port"
    if(ret == STATUS_OK){
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", port:");
        yang_sprintf_value(value, offset, string);
    }
    else{
        *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "port:INVALID");   
    }
    *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos:%s", diff_pos[data.node.pos]);
    if(data.node.pos != DMDB_ARRAY_FIRST && data.node.pos != DMDB_ARRAY_STAY){
        ret = dmdb_get_field(data.node.pos_node, "protocol", &value); //member key field_name must be "protocol"
        if(ret == STATUS_OK){
            *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos_protocol:");
            yang_sprintf_value(value, offset, string);
        } 
        else{
            *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos_protocol:INVALID");   
        }
        ret = dmdb_get_field(data.node.pos_node, "ip", &value); //member key field_name must be "ip"
        if(ret == STATUS_OK){
            *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos_ip:");
            yang_sprintf_value(value, offset, string);
        } 
        else{
            *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos_ip:INVALID");   
        }
        ret = dmdb_get_field(data.node.pos_node, "port", &value); //member key field_name must be "port"
        if(ret == STATUS_OK){
            *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos_port:");
            yang_sprintf_value(value, offset, string);
        } 
        else{
            *offset += sprintf_s(string + *offset, STRING_MAX - *offset, ", pos_port:INVALID");   
        }
    }
    *offset += sprintf_s(string + *offset, STRING_MAX - *offset, "\n");
}

void yang_print_diff_data(FILE *fp, dmdb_diff_info_t child_info, int mk_field_num, bool old_data)
{
    char string[STRING_MAX];
    int offset=0;
    dmdb_diff_data_t data;
    if(old_data == true) {
        offset = sprintf_s(string, STRING_MAX,"%s:%s, op:%s, ", node_type[child_info.type], child_info.name, diff_operation[child_info.op]);
        data = child_info.old_data;
    } else {
        offset = sprintf_s(string, STRING_MAX,"%s:%s, op:%s, data: NEW, ", node_type[child_info.type], child_info.name, diff_operation[child_info.op]);
        data = child_info.new_data;
    }

    if(child_info.type == DMDB_DIFF_FIELD){
        offset += sprintf_s(string + offset, STRING_MAX - offset, "value:");
        yang_sprintf_value(data.field.value, &offset, string);
        offset += sprintf_s(string + offset, STRING_MAX - offset, ", pos:%s", diff_pos[data.field.pos]);
        if(data.field.pos != DMDB_ARRAY_FIRST && data.field.pos != DMDB_ARRAY_STAY){
            offset += sprintf_s(string + offset, STRING_MAX - offset, ", pos_value:");
            yang_sprintf_value(data.field.pos_value, &offset, string);
        }
        offset += sprintf_s(string + offset, STRING_MAX - offset, "\n");
    }
    else if(child_info.type == DMDB_DIFF_NODE || child_info.type == DMDB_DIFF_ARRAY){
        if(data.node.is_empty == false){
            offset += sprintf_s(string + offset, STRING_MAX - offset, "empty:false\n");
        }
        else{
            offset += sprintf_s(string + offset, STRING_MAX - offset, "empty:true\n");
        }
    }
    else if(child_info.type == DMDB_DIFF_ARRAY_NODE){
        if(mk_field_num == 1){
            yang_sprintf_array_node_mk_field_1(data, &offset, string);
        }
        else if(mk_field_num == 3){
            yang_sprintf_array_node_mk_field_3(data, &offset, string);
        }
        else{
            offset += sprintf_s(string + offset, STRING_MAX - offset, "not support mk_field_num:%d, need add\n", mk_field_num);
        }
    }
    else if(child_info.type == DMDB_DIFF_FIELD_ARRAY){
        if(data.field.is_empty == false){
            offset += sprintf_s(string + offset, STRING_MAX - offset, "empty:false\n");
        }
        else{
            offset += sprintf_s(string + offset, STRING_MAX - offset, "empty:true\n");
        }
    }
    fprintf(fp, string);
}

void yang_print_diff_info(FILE *fp, dmdb_diff_info_t child_info, int mk_field_num)
{
    switch(child_info.op) {
        case 0: //UPDATE
            yang_print_diff_data(fp, child_info, mk_field_num, true); // old data
            yang_print_diff_data(fp, child_info, mk_field_num, false); // new data
            break;
        case 1: //CREATE
            yang_print_diff_data(fp, child_info, mk_field_num, false); // new data
            break;
        case 2: //REMOVE
            yang_print_diff_data(fp, child_info, mk_field_num, true); // old data
            break;
        default: //INVALID
            printf("Error, %s:%s, op:%s, ", node_type[child_info.type], child_info.name, diff_operation[child_info.op]);
            ASSERT_EQ(0, 1);
            break;
    }
}

void yang_get_next_diff(FILE *fp, diff_node_t parent, diff_node_t child, int mk_field_num)
{
    diff_node_t child_next = NULL;
    bool is_first = true;
    while (is_first == true || child != NULL) {
        is_first = false;
        dmdb_diff_info_t child_info;
        ASSERT_EQ(STATUS_OK, dmdb_get_next_diff_node(parent, child, &child_next, &child_info));
        dmdb_release_diff_node(child);
        child = child_next;
        if (child_next == NULL) {
            break;
        }
        if (fp != NULL) {
            yang_print_diff_info(fp, child_info, mk_field_num);
        }
        yang_get_next_diff(fp, child_next, NULL, mk_field_num);
    }
}

void yang_diff_to_file(char const *path, dmdb_diff_tree_info_t diff_tree_info, int mk_field_num=1, bool print_table_name=true)
{
    FILE *fp;
    //printf("mk_field_num:%u\n", mk_field_num);
    /* Open the file */
    if (path == NULL) {
        yang_get_next_diff(NULL, diff_tree_info.root_node, NULL, mk_field_num);
        return;
    }
    fp = fopen(path, "wb");
    if (NULL == fp)
    {
        return;
    }
    if (print_table_name == true) {
        fprintf(fp, "user:%s, table:%s\n", diff_tree_info.database_name, diff_tree_info.table_name);
    }
    yang_print_diff_info(fp, diff_tree_info.root_info, 0);
    yang_get_next_diff(fp, diff_tree_info.root_node, NULL, mk_field_num);
    fflush(fp);

    fclose(fp);
}

int32_t yang_compare_diff_file(const char *path1, const char *path2)
{
    int ret;
    char *file1 = NULL;
    char *file2 = NULL;

    readFileContent(path1, &file1, true);
    //printf("file1 is : %s\n", file1);
    readFileContent(path2, &file2, true);
    //printf("file2 is : %s\n", file2);
    if(file1 != NULL && file2 != NULL){
        ret = strcmp(file1, file2);
        free(file1);
        free(file2);
        if (ret != STATUS_OK) {
            char cmd[1024] = {0};
            sprintf_s(cmd, sizeof(cmd), "diff %s %s", path1, path2);
            system(cmd);
        }
    }
    else{
        printf("%s:%d, file1: %p, file2: %p\n", __func__, __LINE__, file1, file2);
        ret = -1;
    }
    return ret;
}

void yang_delete_file_space(const char *path1, const char *path2)
{
    char *json = NULL;

    readFileContent(path1, &json, true);
    ASSERT_NE((void *)NULL, json);
    char string[1024];
    int max = 1024;

    FILE *fp;
    fp = fopen(path2, "wb");
    if (NULL == fp)
    {
        return;
    }
    int i=0;
    int offset=0;  
    while(json[i] != '\0'){
        if(json[i] != '\n'){
            if(json[i] != ' '){
                offset += sprintf_s(string + offset, max - offset, "%c", json[i]);
           }
        }
        else{
            //offset += sprintf_s(string + offset, max - offset, "%c", json[i]);
            fprintf(fp, string);
            offset=0;
        }
        i++;
    }
    fflush(fp);
    fclose(fp);

    free(json);
}

void yang_get_string_size_delete_space(char *string_origin, uint64_t *length)
{
    ASSERT_NE((void *)NULL, string_origin);
    ASSERT_NE((void *)NULL, length);

    uint64_t length_origin = strlen(string_origin);
    char *string_new = (char *)malloc(length_origin + 1);

    int i = 0;
    int j = 0;
    while(string_origin[i] != '\0') {
        if(string_origin[i] != ' ' && string_origin[i] != '\n') {
            string_new[j] = string_origin[i];
            j++;
        }
        i++;
    }
    string_new[j] = '\0';
    *length = strlen(string_new) + 1;
    //printf("string new:%s, length:%llu\n", string_new, *length);
    free(string_new);
}

static void yang_check_result_error_path(dmdb_error_msg_t* error_msg, dmdb_error_msg_t* expect_msg)
{
    printf("[code]:%d\n", error_msg->error_code);
    printf("[msg]:%s\n", error_msg->error_msg);
    printf("[path]:%s\n", error_msg->error_path);
    ASSERT_EQ(error_msg->error_code, expect_msg->error_code);
    //ASSERT_STREQ(error_msg->error_msg, expect_msg->error_msg);  
    ASSERT_STREQ(error_msg->error_path, expect_msg->error_path);
}
uint64_t com_gettimeofday_usec()
{
    struct timeval now;
    uint64_t usec;
    gettimeofday(&now, NULL);
    usec = ((uint64_t)now.tv_sec * 1000 * 1000) + (uint64_t)now.tv_usec;
    return usec;
}
unsigned long long com_gettimeofday_msec()
{
    struct timeval now;
    unsigned long long msec;
    gettimeofday(&now, NULL);
    msec = ((unsigned long long)now.tv_sec * 1000) + ((unsigned long long)now.tv_usec / 1000);
    return msec;
}
unsigned long long com_gettimeofday_sec()
{
    struct timeval now;
    unsigned long long sec;
    gettimeofday(&now, NULL);
    sec = (unsigned long long)now.tv_sec + ((unsigned long long)now.tv_usec / 1000 / 1000);
    return sec;
}

void yang_atomic_batch_update_object(connection_t conn, uint32_t total_num, object_t *object, obj_key_t *pk, bool fetch_diff = false)
{
    batch_t batch_handle = NULL;
    uint32_t success_num;
    
    ASSERT_EQ(STATUS_OK, dmdb_prepare_atomic_batch(conn, &batch_handle));
    for(uint32_t i = 0; i < total_num; i++){
        ASSERT_EQ(STATUS_OK, dmdb_add_update(batch_handle, NULL, object[i], pk[i], fetch_diff ? DMDB_FETCH_DIFF_BY_NEW_ORDER : 0));
    }   
    ASSERT_EQ(STATUS_OK, dmdb_execute_batch(batch_handle, &total_num, &success_num));
    ASSERT_EQ(total_num, success_num);
    ASSERT_EQ(STATUS_OK, dmdb_close_batch(batch_handle));
    batch_handle = NULL;
}

void yang_atomic_batch_write_object(connection_t conn, uint32_t total_num, object_t *object)
{
    batch_t batch_handle = NULL;
    uint32_t success_num;
    
    ASSERT_EQ(STATUS_OK, dmdb_prepare_atomic_batch(conn, &batch_handle));
    for(uint32_t i = 0; i < total_num; i++){
        ASSERT_EQ(STATUS_OK, dmdb_add_write(batch_handle, NULL, object[i], 0));
    }
    ASSERT_EQ(STATUS_OK, dmdb_execute_batch(batch_handle, &total_num, &success_num));
    ASSERT_EQ(total_num, success_num);
    ASSERT_EQ(STATUS_OK, dmdb_close_batch(batch_handle));
    batch_handle = NULL;
}

//全量读编辑filter object
void yang_edit_filter_object_for_read_table(schema_t schema, dmdb_value_t *value, object_t *filter_object, const char *pk_field_name = "F1")
{
    ASSERT_EQ(STATUS_OK, dmdb_create_object(schema, false, filter_object));
    node_t root;
    ASSERT_EQ(STATUS_OK, dmdb_get_root_node(*filter_object, &root));
    ASSERT_EQ(STATUS_OK, dmdb_edit_field(root, pk_field_name, DMDB_EDIT_MERGE, value));
    //print_object_text(*filter_object);
}
//subtree同步读全量数据
void yang_sync_read_full_object_by_filter(connection_t conn, schema_t schema, obj_key_t pk, dmdb_value_t *value, object_t *reply_obj, const char *pk_field_name = "F1")
{
    object_t filter_object = NULL;
    ASSERT_NO_FATAL_FAILURE(yang_edit_filter_object_for_read_table(schema, value, &filter_object, pk_field_name));

    dmdb_filter_type_t filter;
    filter.flag = DMDB_FILTER_BY_SUBTREE;
    filter.filter_spec.subtree = filter_object;
    filter.with_flag = 0;
    ASSERT_EQ(STATUS_OK, dmdb_read_by_filter_sync(conn, pk, filter, reply_obj));

    ASSERT_EQ(STATUS_OK, dmdb_release_object(filter_object));
}

/*****************************异步接口*************************************/
uint32_t async_callback_num = 0; //只在一个地方调用，多线程不要用，使用前清零
//异步读操作回调示例
void yang_async_batch_read_callback(uint64_t version, object_t object, void *async_user_data, uint64_t msg_id, status_t status)
{
    async_callback_num++;
#if (__WORDSIZE == 64) || (RTOSV2X == 1) 
    printf("%s, version:%lu, object:%p, async_user_data:%s, msg_id:%lu, status:%d, async_callback_num:%u\n", __func__, version, object, (char *)async_user_data, msg_id, status, async_callback_num);
#else
    printf("%s, version:%llu, object:%p, async_user_data:%s, msg_id:%llu, status:%d, async_callback_num:%u\n", __func__, version, object, (char *)async_user_data, msg_id, status, async_callback_num);
#endif
    EXPECT_EQ(STATUS_OK, status);
    //print_object_text(object);

#if 0 //根据实际情况，用自己的回调函数，添加这部分
    EXPECT_EQ(count, msg_id);
    ASSERT_STREQ("conn", (char *)async_user_data)
    char* object_json = NULL;
    object_t object_expect;
    readFileContent(path[count], &object_json, true);
    //printf("schema json is : %s\n", object_json);
    ASSERT_NE((void*)NULL, object_json);
    EXPECT_EQ(STATUS_OK, dmdb_create_object_by_json(schema, object_json, &object_expect));
    free(object_json);
    EXPECT_EQ(STATUS_OK, yang_object_compare(object_expect, object));
    ASSERT_EQ(STATUS_OK, dmdb_release_object(object_expect));
#endif
    ASSERT_EQ(STATUS_OK, dmdb_release_object(object));
}
//异步写操作回调示例
void yang_async_batch_write_callback(uint32_t batch_id, uint32_t total_num, uint32_t success_num, diff_tree_t *diff_trees, uint32_t count, status_t status, void* async_user_data)
{
    async_callback_num++;
    printf("%s,status:%d, batch_id:%u, total_num:%u, success_num:%u, diff count:%u, async_callback_num:%u, diff_trees:%p, async_user_data:%p\n", __func__, status, batch_id, total_num, success_num, count, async_callback_num, diff_trees, async_user_data);
    ASSERT_EQ(STATUS_OK, status);
    ASSERT_EQ(total_num, success_num);
    ASSERT_EQ((uint32_t)0, count);
} 


//异步更新操作回调示例
void yang_async_batch_update_callback(uint32_t batch_id, uint32_t total_num, uint32_t success_num, diff_tree_t *diff_trees, uint32_t count, status_t status, void* async_user_data)
{
    async_callback_num++;
    printf("%s,status:%d, batch_id:%u, total_num:%u, success_num:%u, diff count:%u, async_callback_num:%u, diff_trees:%p\n", __func__, status, batch_id, total_num, success_num, count, async_callback_num, diff_trees);
    ASSERT_EQ(STATUS_OK, status);
    ASSERT_EQ(total_num, success_num);
    ASSERT_EQ(total_num, count);
    dmdb_release_diff(async_user_data);
}
//异步写操作
void yang_async_atomic_batch_write_object(connection_t conn, uint32_t total_num, object_t *object, dmdb_batch_diff_done_t dmdb_batch_diff_callback, uint32_t batch_id = 0, status_t expect_ret = STATUS_OK)
{
    batch_t batch_handle;

    ASSERT_EQ(STATUS_OK, dmdb_prepare_atomic_batch(conn, &batch_handle));
    for(uint32_t i = 0; i < total_num; i++){
        ASSERT_EQ(STATUS_OK, dmdb_add_write(batch_handle, NULL, object[i], 0));
    }
    ASSERT_EQ(expect_ret, dmdb_execute_batch_async_with_callback(batch_handle, batch_id, conn, dmdb_batch_diff_callback));
    ASSERT_EQ(STATUS_OK, dmdb_close_batch(batch_handle));
}
//异步更新操作
void yang_async_atomic_batch_update_object(connection_t conn, uint32_t total_num, object_t *object, obj_key_t *pk, dmdb_batch_diff_done_t dmdb_batch_diff_callback, bool fetch_diff = true, uint32_t batch_id = 0, status_t expect_ret = STATUS_OK)
{
    batch_t batch_handle;

    ASSERT_EQ(STATUS_OK, dmdb_prepare_atomic_batch(conn, &batch_handle));
    for(uint32_t i = 0; i < total_num; i++){
        ASSERT_EQ(STATUS_OK, dmdb_add_update(batch_handle, NULL, object[i], pk[i], fetch_diff ? DMDB_FETCH_DIFF_BY_NEW_ORDER : 0));
    }
    ASSERT_EQ(expect_ret, dmdb_execute_batch_async_with_callback(batch_handle, batch_id, conn, dmdb_batch_diff_callback));
    ASSERT_EQ(STATUS_OK, dmdb_close_batch(batch_handle));
}


/*监听的事件
EPOLLIN:    触发该事件，表示对应的文件描述符上有可读数据。(包括对端SOCKET正常关闭)；
EPOLLOUT:   触发该事件，表示对应的文件描述符上可以写数据；
EPOLLPRI:   表示对应的文件描述符有紧急的数据可读（这里应该表示有带外数据到来）；
EPOLLERR:   表示对应的文件描述符发生错误；
EPOLLHUP:   表示对应的文件描述符被挂断；
EPOLLET:    将EPOLL设为边缘触发(Edge Triggered)模式，这是相对于水平触发(Level Triggered)来说的。
EPOLLONESHOT:只监听一次事件，当监听完这次事件之后，如果还需要继续监听这个socket的话，需要再次把这个socket加入到EPOLL队列里
参数说明：
    one_callback_wait_count:每一个异步回调都要监听one_callback_wait_count次，一个conn有可能一次处理多个异步回调
*/

//添加conn的fd到epoll监听队列
int yang_add_conn_to_epoll_listen_queue(connection_t *conn, uint32_t conn_num, uint32_t listen_max_events, uint32_t listen_events = (EPOLLET | EPOLLIN | EPOLLHUP | EPOLLERR))
{
    if(conn_num == 0 || conn_num > 1024 || listen_max_events == 0) {
         printf("conn_num :%u, total_num:%u, request: 0 < conn_num <=1024, total_num > 0.\n", conn_num, listen_max_events);
         return 1;
    }
    status_t ret;
    int epfd;
    int sockfd[1024];
    struct epoll_event ctrl;
    ctrl.events = listen_events;

    epfd = epoll_create(listen_max_events);
    if(epfd < 0) {
        printf("epoll create failed, epfd = %d\n", epfd);
        EXPECT_GT(epfd, 0);
        return -1;
    }
    for(uint32_t i = 0; i < conn_num; i++) {
        sockfd[i] = dmdb_get_fd(conn[i]);
        if(sockfd[i] < 0) {
            close(epfd);
            printf("dmdb_get_fd failed, i:%u\n", i);
            return -2;
        }
        ctrl.data.ptr = conn[i];
        ret = epoll_ctl(epfd, EPOLL_CTL_ADD, sockfd[i], &ctrl);
        if(ret != 0) {
            close(epfd);
            printf("add sockfd to epoll queue failed, i:%u\n", i);
            return -3;
        }
    }
    /*
    printf("epfd:%u\n", epfd);
    printf("sockfd: ");
    for(uint32_t i = 0; i < conn_num; i++) {
        printf("%u ", sockfd[i]);
    }
    printf("\n");
    */
    return epfd;
}
//从epoll监听队列中，删除conn的fd
int yang_delete_conn_from_epoll_listen_queue(int epfd, connection_t *conn, uint32_t conn_num)
{
    if(conn_num == 0 || conn_num > 1024 ) {
         printf("conn_num :%u, request: 0 < conn_num <=1024,\n", conn_num);
         return -1;
    }
    int ret = 0;
    int sockfd[1024] = {0};

    for(uint32_t i = 0; i < conn_num; i++) {
        sockfd[i] = dmdb_get_fd(conn[i]);
        if(sockfd[i] < 0) {
            printf("dmdb_get_fd failed, i:%u, sockfd:%d\n", i, sockfd[i]);
            return -2;
        }
        ret = epoll_ctl(epfd, EPOLL_CTL_DEL, sockfd[i], NULL);
        if(ret != 0) {
            printf("delete sockfd to epoll queue failed, i:%u, sockfd:%d\n", i, sockfd[i]);
            return -3;
        }
    }
    return ret;
}

/******异步监听多个connect连接******/
int32_t yang_epoll_listen_connect_num_new(int epfd, connection_t *conn, uint32_t conn_num, uint32_t *async_callback_init_num, uint32_t total_num, uint32_t one_callback_wait_count = 6, \
                                            uint32_t listen_events = (EPOLLET | EPOLLIN | EPOLLHUP | EPOLLERR), int32_t timeout = 10000, status_t expect_dmdb_run_staus = STATUS_OK, \
                                            uint32_t pthread_num = 0, uint32_t conn_id = 0, bool is_print = false)
{
    if(epfd < 0) {
        printf("[ pthread%u ][ conn%-2u ] epoll create failed, epfd = %d, conn[0]:%p, listen_events:0x%x\n", pthread_num, conn_id, epfd, &conn[0], listen_events);
        EXPECT_GT(epfd, 0);
        return -1;
    }
    if(conn_num == 0 || conn_num > 1024 || total_num == 0) {
         printf("[ pthread%u ][ conn%-2u ] conn_num :%u, total_num:%u, request: 0 < conn_num <=1024, total_num > 0.\n", pthread_num, conn_id, conn_num, total_num);
         return 1;
    }
    int ret, dmdb_run_ret;
    unsigned long long start_cycles;
    unsigned long long stop_cycles;
    unsigned long long time_diff;
    start_cycles = com_gettimeofday_sec();
    uint32_t tmp = *async_callback_init_num;
    struct epoll_event wait;
    uint32_t wait_count = 0;
    while (*async_callback_init_num != total_num) {
        ret = epoll_wait(epfd, &wait, 1, timeout);
        if (ret > 0) {
            if (wait.events & EPOLLERR ) {
                printf("[ pthread%u ][ conn%-2u ] epoll wait events EPOLLERR\n", pthread_num, conn_id);
                printf("[ pthread%u ][ conn%-2u ] %s, async_callback_num:%u, total_num:%u, async exec failed\n", pthread_num, conn_id, __func__, *async_callback_init_num, total_num);
                *async_callback_init_num = 0;
                return -5;
            }
            if (wait.events & EPOLLHUP ) {
                printf("[ pthread%u ][ conn%-2u ] epoll wait events EPOLLHUP\n", pthread_num, conn_id);
                printf("[ pthread%u ][ conn%-2u ] %s, async_callback_num:%u, total_num:%u, async exec failed\n", pthread_num, conn_id, __func__, *async_callback_init_num, total_num);
                *async_callback_init_num = 0;
                return -6;
            } 
            if (wait.events & EPOLLIN) {//接收到数据，读socket
                connection_t run_conn = wait.data.ptr;
                dmdb_run_ret = dmdb_run(run_conn, wait.events);
                EXPECT_EQ(expect_dmdb_run_staus, dmdb_run_ret);
                if (dmdb_run_ret != expect_dmdb_run_staus) {
                    *async_callback_init_num = 0;
                    return dmdb_run_ret;
                } else {
                    if (tmp != *async_callback_init_num) {
                        tmp = *async_callback_init_num;
                        if (is_print == true) {
                            printf("[ pthread%u ][ conn%-2u ] wait_count:%u, async_callback_num:%u, total_num:%u\n", pthread_num, conn_id, wait_count, *async_callback_init_num, total_num);
                        }
                        wait_count = 0;
                        start_cycles = com_gettimeofday_sec();
                    }
                }
            }
            if (wait.events & EPOLLOUT ) { //有数据待发送，写socket
                printf("[ pthread%u ][ conn%-2u ] epoll wait events EPOLLOUT\n", pthread_num, conn_id);
                printf("[ pthread%u ][ conn%-2u ] %s, async_callback_num:%u, total_num:%u, async exec failed\n", pthread_num, conn_id, __func__, *async_callback_init_num, total_num);
            }
            if((wait.events & ( EPOLLIN | EPOLLOUT | EPOLLHUP | EPOLLERR)) == 0){
                printf("[ pthread%u ][ conn%-2u ] epoll wait events 0x%x\n", pthread_num, conn_id, wait.events);
                printf("[ pthread%u ][ conn%-2u ] %s, async_callback_num:%u, total_num:%u, async exec failed\n", pthread_num, conn_id, __func__, *async_callback_init_num, total_num);
                *async_callback_init_num = 0;
                return -7;
            }
        }
        wait_count++;
        stop_cycles = com_gettimeofday_sec();
        time_diff = stop_cycles - start_cycles;
        if (stop_cycles < start_cycles) {
            printf("[ EORROR ] stop_cycles %llu, start_cycles %llu\n", stop_cycles, start_cycles);
            time_diff = 0;
        }
        if (time_diff > 60 && (wait_count % one_callback_wait_count) == 0) {
            printf("[ WARNING ][ pthread%u ][ conn%-2u ] , wait_count over %d: wait_count:%u, wait_time:%llu s, async_callback_num:%u, total_num:%u\n", pthread_num, conn_id, one_callback_wait_count, wait_count, time_diff, *async_callback_init_num, total_num);
        }
        if (*async_callback_init_num != total_num && time_diff > 600 ){
            printf("[ EORROR ][ pthread%u ][ conn%-2u ] , wait_count over %d: wait_count:%u, wait_time:%llu s, async_callback_num:%u, total_num:%u\n", pthread_num, conn_id, one_callback_wait_count, wait_count, time_diff, *async_callback_init_num, total_num);
            ret = -4;
            break;
        }
        ret = STATUS_OK;
    }
    if (is_print == true) {
        if (total_num == *async_callback_init_num) {
            printf("[ pthread%u ][ conn%-2u ] %s, async_callback_num:%u, total_num:%u, async exec success\n", pthread_num, conn_id, __func__, *async_callback_init_num, total_num);
        } else {
            printf("[ pthread%u ][ conn%-2u ] %s, async_callback_num:%u, total_num:%u, async exec failed\n", pthread_num, conn_id, __func__, *async_callback_init_num, total_num);
        }
    }
    *async_callback_init_num = 0;
    return ret;
}

// drop all database in server
#define DATABASE_MAX_NUM 255

bool is_rtosv2x_local_database(char *database_name)
{
    if (strcmp(database_name, "startup") == 0 || 
        strcmp(database_name, "candidate") == 0 || 
        strcmp(database_name, "running") == 0) {
        return true;
    }

    return false;
}

void yang_drop_all_database(connection_t conn)
{
    char names[DATABASE_MAX_NUM][DMDB_MAX_DATABASE_NAME_LEN] = {0};
    uint32_t count = 0;
    ASSERT_EQ(STATUS_OK, dmdb_get_database_names(conn, DATABASE_MAX_NUM, names, &count));
    for(uint32_t i = 0; i < count; i++){
#if (RTOSV2X == 1)
        if (is_rtosv2x_local_database(names[i])) {
            continue;
        }
#endif
        ASSERT_EQ(STATUS_OK, dmdb_drop_database(conn, names[i]));
        //printf("database has been dropped: %s\n", names[i]);
    }
}


#define DEBUF_PRINTF(num) do { printf("%d............\n", num); } while (0);

// 用户自定义校验user_data函数
typedef status_t (*check_user_data_func)(void *user_data, uint32_t data_len);
typedef struct tag_yang_reg_user_data_t {
    int32_t epoll_fd;
    pthread_t epoll_pthread_id;
    bool is_can_exit;
    void *user_data;
    uint32_t data_len;
    check_user_data_func check_func;
    bool is_return_user_code;
    int32_t user_code;
} yang_reg_user_data_t;

typedef struct tag_epoll_pthread_data_t {
    int fd;
    sem_t sem;
    bool *is_can_exit;
} epoll_pthread_data_t;

// 钩子函数，默认使用这个就行
int32_t yang_epoll_ctl(int32_t fd, dmdb_epoll_ctrl_type type, uint32_t event_mask, void *user_data)
{
    int ret = 0;
    yang_reg_user_data_t *reg_user_data = (yang_reg_user_data_t *)user_data;

    // 用户控制直接返回某个错误码
    if (reg_user_data->is_return_user_code) {
        return reg_user_data->user_code;
    }

    // 校验user_data
    if (reg_user_data->check_func != NULL) {
        ret = reg_user_data->check_func(reg_user_data->user_data, reg_user_data->data_len);
        if (ret != STATUS_OK) {
            printf("[ ERROR ] %s:%d, check user_data failed, ret:%d\n", __FILE__, __LINE__, ret);
            EXPECT_EQ(STATUS_OK, ret);
        }
    }

    // 注册fd
    if (type == DMDB_EPOLL_EVENT_ADD_FD) {
        struct epoll_event event;
        event.data.fd = fd;
        event.events = event_mask;
        ret  = epoll_ctl(reg_user_data->epoll_fd, EPOLL_CTL_ADD, fd, &event);
    } else {
        ret  = epoll_ctl(reg_user_data->epoll_fd, EPOLL_CTL_DEL, fd, NULL);
    }
    if (ret != 0) {
        const char *err_info = strerror(errno);
        printf("[ ERROR ] %s:%d, epoll_ctrl failed, epoll_fd=%d, fd=%d type=%d, Error:[%d:%s]\n", 
                __FILE__, __LINE__, reg_user_data->epoll_fd, fd, (int)type, errno, err_info);
    }

    return ret;
}

// epoll_wiat处理事件线程
void *yang_epoll_wait_do_handle_event(void *arg)
{
    epoll_pthread_data_t *epoll_pthread_data = (epoll_pthread_data_t *)arg;
    int epoll_fd = epoll_pthread_data->fd;
    bool *is_can_exit = epoll_pthread_data->is_can_exit;

    int ret = sem_post(&epoll_pthread_data->sem);
    if (ret != 0) {
        printf("[ ERROR ] %s:%d, sem_post failed, ret:%d, errmsg=%s\n", __FILE__, __LINE__, ret, strerror(errno));
        pthread_exit(NULL);
    }

    struct epoll_event events[1024];
    uint32_t timeout = 2000;
    while (!(*is_can_exit)) {
        int nfds = epoll_wait(epoll_fd, events, 1024, timeout);
        if (nfds < 0) {
            //printf("[ ERROR ] %s:%d, epoll_wait failed, ret:%d, errmsg=%s\n", 
            //        __FILE__, __LINE__, nfds, strerror(errno));
            //exit(1);
            continue;
        } else {
            for (int32_t i = 0; i < nfds; i++) {
                int fd = events[i].data.fd;
                // 伪代码：确认是db的fd
                if (1) {
                    int ret = dmdb_handle_event(fd, EPOLLIN | EPOLLOUT | EPOLLHUP | EPOLLERR);
                    if (ret != 0) {
                        printf("[ ERROR ] %s:%d, dmdb_handle_event failed, fd=%d, ret=%d\n", 
                                __FILE__, __LINE__, fd, ret);
                        continue;
                    }
                }
            }
        }
    }
    // printf("yang_epoll_wait_do_handle_event epoll thread exit. epoll_fd=%d\n", epoll_fd);
    pthread_exit(NULL);
}

// 默认的用户数据
char g_yang_default_user_data[] = "yang_user_data";
uint32_t g_yang_default_user_data_len = sizeof(g_yang_default_user_data);

// 默认的校验用户数据的函数
status_t yang_default_check_user_data(void *user_data, uint32_t data_len)
{
    // printf("%s\n", __func__);
    return strncmp((char *)user_data, g_yang_default_user_data, data_len);
}

// user_data为NULL校验函数
status_t yang_check_user_data_null(void *user_data, uint32_t data_len)
{
    // printf("%s\n", __func__);
    if (user_data != NULL) {
        return -1;
    }
    return STATUS_OK;
}

// epoll_wait线程开启
typedef void * (*pthread_start_func)(void *);
int yang_epoll_wait_pthread_start(pthread_t *heartbeat_epoll_thread_id, pthread_start_func start_func, bool *is_can_exit)
{
    int ret;

    // 创建epoll_fd
    int32_t epoll_fd = epoll_create(1024);
    if (epoll_fd <= 0) {
        printf("[ ERROR ] %s:%d, epoll_create failed, ret:%d, err_msg=%s.\n", 
                __FILE__, __LINE__, epoll_fd, strerror(errno));
        return epoll_fd;
    }

    // 初始化传给线程函数的参数
    epoll_pthread_data_t epoll_pthread_data;
    epoll_pthread_data.fd = epoll_fd;
    epoll_pthread_data.is_can_exit = is_can_exit;
    ret = sem_init(&epoll_pthread_data.sem, 0, 0);
    if (ret != 0) {
        printf("[ ERROR ] %s:%d, sem_init failed, ret:%d, errmsg=%s\n", __FILE__, __LINE__, ret, strerror(errno));
        return ret;
    }

    // 开始epoll线程，死循环监听事件
    ret = pthread_create(heartbeat_epoll_thread_id, NULL, start_func, (void *)&epoll_pthread_data);
    if (ret != 0) {
        printf("[ ERROR ] %s:%d, pthread_create failed, errmsg=%s\n", __FILE__, __LINE__, strerror(errno));
        return ret;
    }

    // 等待线程启动，线程拿到epoll_fd
    ret = sem_wait(&epoll_pthread_data.sem);
    if (ret != 0) {
        printf("[ ERROR ] %s:%d, sem_wait failed, ret:%d, errmsg=%s\n", __FILE__, __LINE__, ret, strerror(errno));
        return ret;
    }

    // 信号量销毁
    ret = sem_destroy(&epoll_pthread_data.sem);
    if (ret != 0) {
        printf("[ ERROR ] %s:%d, sem_destroy failed, ret:%d, errmsg=%s\n", __FILE__, __LINE__, ret, strerror(errno));
        return ret;
    }

    return epoll_fd;
}

int yang_epoll_wait_pthread_end(yang_reg_user_data_t *reg_user_data)
{
    reg_user_data->is_can_exit = true;
    void *pthread_exit_status;
    int ret = pthread_join(reg_user_data->epoll_pthread_id, &pthread_exit_status);
    if (ret != 0) {
        printf("[ ERROR ] %s:%d, pthread_join %lu failed, ret:%d, errmsg=%s\n", 
                __FILE__, __LINE__, reg_user_data->epoll_pthread_id, ret, strerror(errno));
        return ret;
    }
    close(reg_user_data->epoll_fd);
    reg_user_data->epoll_fd = -1;

    return 0;
}

/* 
 * yang心跳注册新接口
 * reg_info: 钩子函数: 默认使用yang_epoll_ctl
 * reg_user_data: 用户自定义的数据
 * is_re_start_one_epoll_pthread: 是否重新创建1个epoll线程
 */
status_t yang_heartbeat_reg(dmdb_ctrl_event_fd_reg_t reg_info, yang_reg_user_data_t *reg_user_data, 
                            bool is_re_start_one_epoll_pthread)
{
    // 开始epoll_wai线程
    if (is_re_start_one_epoll_pthread) {
        // 设置epoll线程不会退出
        reg_user_data->is_can_exit = false;
        reg_user_data->is_return_user_code = false;

        pthread_t epoll_thread_id;
        int epoll_fd = yang_epoll_wait_pthread_start(&epoll_thread_id, yang_epoll_wait_do_handle_event, &reg_user_data->is_can_exit);
        if (epoll_fd <= 0) {
            printf("[ ERROR ] %s:%d, epoll_wait_pthread_start failed, ret:%d\n", __FILE__, __LINE__, epoll_fd);
            return epoll_fd;
        }
        // 初始化用户数据
        reg_user_data->epoll_fd = epoll_fd;
        reg_user_data->epoll_pthread_id = epoll_thread_id;
    }

    // 注册
    return dmdb_heartbeat_reg(reg_info, (void *)reg_user_data);
}

#ifdef __cplusplus
}
#endif

#endif
