#ifndef __PERF_COMMON_H__
#define __PERF_COMMON_H__
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <dlfcn.h>

#include "gmc.h"
#include "gmc_tablespace.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "testutil.h"
#include "gmc_internal.h"
#include "gmc_test.h"

#include "clt_stmt.h"

#define CUSTOM_LEN32 32
#define CUSTOM_LEN128 128
#define MAX_THREAD_NUM 2048
#define COMM_STR_SIZE 2048
#define MAX_COL_SIZE 16385

#define FIELD_VAR_NONE 0
#define FIELD_VAR_CONST 1
#define FIELD_VAR_NULL 2
#define FIELD_VAR_SERIAL 3
#define FIELD_VAR_RANGE 4
#define FIELD_VAR_RANDOM 5

#if defined ENV_RTOSV2X
#define TEST_RANDOM(seed) rand()
#else
#define TEST_RANDOM(seed) nrand48(seed)
#endif

#if defined ENV_RTOSV2X
int g_envType = 2;
#elif defined ENV_RTOSV2
int g_envType = 1;
#elif defined ENV_SUSE
int g_envType = 3;
#else
int g_envType = 0;
#endif

#if defined RUN_INDEPENDENT
int g_perfMode = 0;
#elif defined RUN_DATACOM_DAP
int g_perfMode = 1;
#else
int g_perfMode = 2;
#endif

int g_perfType = 0;  // 0:sta  1:perf
char g_perfServer[3][128] = {"usocket:/run/verona/unix_emserver", "channel:", "channel:"};
char g_perfUser[CUSTOM_LEN32] = "lite";
char g_perfPasswd[64] = "abcd";

pthread_mutex_t g_connConcurrentLock;
int g_connConcurrentSize = 0;

bool g_renameTable = true;
int g_logLevel = 0;
int g_threadCount = 1;
bool g_maxRecordIsCheck = false;
char g_strText[1024] = {0};
char g_programName[CUSTOM_LEN32];
// for sn
int g_snThrdCount = 0;
int g_snSubCount = 1;
bool g_snLatency = false;
struct snObject {
    int offset1;
    int pathNum;
    int tNum;
    uint64_t tSum;
    char *schText;
    char metaName[64];
    char subName[64];
    GmcSubConfigT subCfg;
};
int g_snEnableFlag = 0;
int g_snObjNum = 0;
bool g_snSegregate = false;
snObject **g_snObjEntry = NULL;
struct perfContent;
struct snContex {
    snObject *s;
    perfContent *p;
};
snContex *g_ctx;

#define TH_STAT_INIT 0
#define TH_STAT_PRESHELL 1
#define TH_STAT_PREPARE 2
#define TH_STAT_WAIT 3
#define TH_STAT_RUN 4
#define TH_STAT_AFTER 5
#define TH_STAT_SUC 6
#define TH_STAT_ERR 7

#define CYCLES_PER_S 999500

#define checkRetureCode(ret, str)                     \
    {                                                 \
        if (ret != 0) {                               \
            printf("ret = %d, msg = %s\n", ret, str); \
            exit(1);                                  \
        }                                             \
    }

#define defInThreadStr(ret, id, status, str1, str2)                                         \
    if (ret != 0) {                                                                         \
        if (str2 == NULL) {                                                                 \
            printf("[tid:%d] errcode:%d, functionName:%s\n", id, ret, str1);                \
        } else {                                                                            \
            printf("[tid:%d] errcode:%d, functionName:%s, info:%s\n", id, ret, str1, str2); \
        }                                                                                   \
        status = TH_STAT_ERR;                                                               \
        return NULL;                                                                        \
    }

#define defInThreadVal(ret, id, status, str, val)                                     \
    if (ret != 0) {                                                                   \
        printf("[tid:%d] errcode:%d, functionName:%s, info:%d\n", id, ret, str, val); \
        status = TH_STAT_ERR;                                                         \
        return NULL;                                                                  \
    }

//  for stability test

#define OBJ_TYPE_VERTEX 0
#define OBJ_TYPE_EDGE 1
#define OBJ_TYPE_KV 3
#define OBJ_TYPE_NAMESPACE 4
#define OBJ_TYPE_RES 5
#define OBJ_TYPE_BILL 6
#define OBJ_TYPE_TABLESPACE 7

#define OPR_TYPE_DML 0
#define OPR_TYPE_DDL 1
#define OPR_TYPE_SUB_TAB 2
#define OPR_TYPE_SUB_PATH 3

#define DDL_VERTEX_CREATE 111
#define DDL_VERTEX_DROP 112
#define DDL_VERTEX_QUERY 113
#define DDL_VERTEX_TRUNCATE 114
#define DDL_VERTEX_STATIC_COUNT 115
#define DDL_VERTEX_ALTER_COUNT 116
#define DDL_EDGE_CREATE 121
#define DDL_EDGE_DROP 122
#define DDL_EDGE_QUERY 123
#define DDL_KV_CREATE 131
#define DDL_KV_DROP 132
#define DDL_KV_QUERY 133
#define DDL_KV_TRUNCATE 134
#define DDL_KV_STATIC_COUNT 135
#define DDL_KV_ALTER_COUNT 136
#define DDL_NAMESPACE_CREATE 141
#define DDL_NAMESPACE_DROP 142
#define DDL_NAMESPACE_QUERY 143
#define DDL_RESPOOL_CREATE 151
#define DDL_RESPOOL_DROP 152
#define DDL_RESPOOL_QUERY 153
#define DDL_RESPOOL_BIND 154
#define DDL_RESPOOL_UNBIND 155
#define DDL_CHECKBILL_START 161
#define DDL_CHECKBILL_STOP 162
#define DDL_CHECKBILL_QUERY 163
#define DDL_CHECKBILL_UPDATE_VERSION 164
#define DDL_TABLESPACE_CREATE 171
#define DDL_TABLESPACE_DROP   172
#define DDL_ALARM_QUERY 173
#define DML_VERTEX_INSERT 201
#define DML_VERTEX_INSERT_RES 202
#define DML_VERTEX_MERGE 203
#define DML_VERTEX_REPLACE 204
#define DML_VERTEX_REPLACE_RES 205
#define DML_VERTEX_DELETE 206
#define DML_VERTEX_UPDATE 207
#define DML_VERTEX_UPDATE_VERSION 208
#define DML_VERTEX_SELECT 209
#define DDL_EDGE_INSERT 301
#define DDL_EDGE_DELETE 302
#define DDL_EDGE_SELECT 303
#define DML_SUB_TABLE_START_ONCE 401
#define DML_SUB_TABLE_START_ALWAYS 402
#define DML_SUB_TABLE_STOP 403
#define DML_SUB_PATH_START_ONCE 404
#define DML_SUB_PATH_START_ALWAYS 405
#define DML_SUB_PATH_STOP 406
#define DML_KV_SET 501
#define DML_KV_GET 502
#define DML_KV_DEL 503
#define DML_KV_EXIST 504
#define DML_KV_SCAN 505
#define DML_KV_GETCOUNT 506

#define RUN_UNIT_ID_PRESHELL -1
#define RUN_UNIT_ID_AFTERSHELL -2

#define DR_FULL_KEY_ALLIDX 0
#define DR_FULL_ALL 1
#define DR_FULL_KEY_UNIQUEIDX 2
#define DR_SQL_KEY_ALLIDX 3
#define DR_SQL_ONLY 4

GmcOperationTypeE g_oprVertexType[] = {GMC_OPERATION_INSERT, GMC_OPERATION_INSERT_WITH_RESOURCE, GMC_OPERATION_MERGE,
    GMC_OPERATION_REPLACE, GMC_OPERATION_REPLACE_WITH_RESOURCE, GMC_OPERATION_DELETE, GMC_OPERATION_UPDATE,
    GMC_OPERATION_UPDATE_VERSION, GMC_OPERATION_SCAN};
const char g_oprVertexName[][16] = {
    "insert", "insertRes", "merge", "replace", "replaceRes", "delete", "update", "updateVersion", "select"};
const char g_oprKVName[][16] = {"set", "get", "del", "exist"};

struct asyncCtrl {
    int pos;
    int count;
    uint64_t sum;
};
struct asyncContex {
    bool used;
    int v;
    void *s;  // scene
    void *p;  // thread
    void *r;  // runUnit
    uint64_t t;
};
struct KVElement {
    int varType;  // 0:const   1:var+string  2:var+digit
    int varRule;
    int size1;
    int size2;
    int offset;
    int length;
    int64_t varBegin;
    int64_t varEnd;
};
struct resMember {
    char colName[32];
    uint16_t resId;
    uint16_t count;
    uint32_t startIndex;
    uint64_t resAttr;
};
struct labelConfig {
    int max_record_count;
    int max_record_count_check;
    int auto_increment;
    int push_age_record_batch;
    int defragmentation;
    int disable_sub_back_pressure;
    int isFastReadUncommitted;
    int isTableLockMode;
    void init()
    {
        max_record_count = -1;
        max_record_count_check = -1;
        auto_increment = -1;
        push_age_record_batch = -1;
        defragmentation = -1;
        disable_sub_back_pressure = -1;
        isFastReadUncommitted = -1;
        isTableLockMode = -1;
    }
};
struct rangeScan {
    uint8_t isDesc;  // 0:asc  1:desc
    uint8_t lFlag;   // 0:[ 1:(  2:NULL
    uint8_t rFlag;   // 0:[ 1:(  2:NULL
    uint8_t n;
    uint8_t *varField;
    uint64_t *valField;  // uchar、uint8、uint16、uint32、uint64,  warning: fixed type of field will run wrong
    GmcRangeItemT *rangeItem;
};
struct filterCtx {
    uint8_t n;
    uint16_t *offset;
    uint16_t *length;
    int64_t *value;
    char *str;
    char **filter;
};
struct sortCtx {
    uint32_t n;
    GmcOrderDirectionE order[25];
    const char *propNameArr[25];
};

struct runUnit {
    bool *isPrepare;
    bool isFilter;
    int isolation;  // 0:isolation + seq  1:seq, 2:isolaton +  random, 3: random
    int objType;    // 0:vertex,  1:edge,  2:x  3:kv
    int oprType;    // 0:dml,  1:ddl,  2:sub  3:dcl
    int exeSeq;     // 0:left->right   1:up->down
    int oprCmd;
    int dataRule;   // 0:full_object(const)+primary_key+sec_idx(unique/nonunique),
                    // 1:full_object(const)+primary_key+sec_idx(unique),  2:full_object(var),
                    // 3:sql+primary+sec_idx(unique/nonunique), 4:sql
    int connMode;   // 0:sync, 1:async,  2:sub
    int transNum;   // [0,10000):valid transaction num  [10000,max]:random
    int transMode;  // 0:commit  1:rollback  2:commit or rollback
    int batchNum;
    int beginCycle;
    int endCycle;
    int countCycle;
    int repeatNum;
    int ops;  // 0:unlimit
    int limitSize;
    int preFetchSize;
    int varMinSize;
    int varMaxSize;
    int varVLen;
    int varILen;
    int resNum;
    int asyncSize;
    int vecSize;
    int schVersion;
    uint8_t snCfg[6];  // [0]|0:default,1:full_table,2:file,3:param  [1]table/path  [2]:is_reliable  [3]:persist
                       // [4]:events  [5]:retry
    int *countBatch;
    GmcBatchT **ctxBatch;
    int *lastStrPos;
    char id[12];
    char metaName[40];
    char idxName[32];
    char edgeName[32];
    char *cacheName;
    char *keyStr;
    char *verStr;
    char *eventType;
    labelConfig *labelCfg;
    rangeScan *range;
    filterCtx *flt;
    sortCtx *sort;
    void *metadataE;
    void *metadataT;
    void *metadataI;
    char **inputV;
    char **inputI;
    void **firstV;
    void **firstI;
    void **varV;
    void **varI;
    GmcSubConfigT subCfg;
    GmcStmtT **stmt;
    asyncCtrl **acl;
    asyncContex **acx;
    resMember *resAddr;
    KVElement kvK;
    KVElement kvV;
    void init()
    {
        ops = 0;
        varVLen = 0;
        varILen = 0;
        resNum = 0;
        connMode = 0;
        batchNum = 0;
        transNum = 0;
        asyncSize = 0;
        transMode = 0;
        beginCycle = 0;
        endCycle = 1;
        dataRule = 0;
        repeatNum = 1;
        isolation = 0;
        limitSize = 0;
        preFetchSize = 0;
        varMinSize = 0;
        varMaxSize = 0;
        vecSize = 1;
        schVersion = -1;
        countBatch = NULL;
        ctxBatch = NULL;
        lastStrPos = NULL;
        edgeName[0] = '\0';
        metaName[0] = '\0';
        idxName[0] = '\0';
        keyStr = NULL;
        verStr = NULL;
        eventType = NULL;
        cacheName = NULL;
        labelCfg = NULL;
        range = NULL;
        flt = NULL;
        sort = NULL;
        acl = NULL;
        acx = NULL;
        stmt = NULL;
        inputV = NULL;
        inputI = NULL;
        firstV = NULL;
        firstI = NULL;
        varV = NULL;
        varI = NULL;
        metadataE = NULL;
        metadataT = NULL;
        metadataI = NULL;
        memset(snCfg, 0, sizeof(snCfg));
        memset(&kvK, 0, sizeof(KVElement));
        memset(&kvV, 0, sizeof(KVElement));
    }
};
struct runGroup {
    int numAll;
    int numExe;
    bool *isSubGroup;
    void **runList;
};
struct sceneEntry {
    bool isTime;
    bool connFlag[4];  // 0:sync  1:async  2:sub
    int threadCount;
    int runTime;
    int numPre;
    int numRun;
    int numAfter;
    int startUid;
    int stopUid;
    int startTid;
    runUnit *preShell;
    runUnit *afterShell;
    char nameSpace[32];
    char snName[64];
    runGroup r;
    void init()
    {
        isTime = false;
        connFlag[0] = true;
        numPre = 0;
        numRun = 0;
        numAfter = 0;
        startUid = 0;
        stopUid = 0;
        startTid = 0;
        threadCount = 0;
        preShell = NULL;
        afterShell = NULL;
        snprintf(nameSpace, sizeof(nameSpace), "default");
        memset(snName, 0, sizeof(snName));
    }
};
sceneEntry *g_sceneEntry = NULL;
int g_sceneNum = 0;
runUnit *g_runUnit = NULL;
int g_unitNum = 0;

sceneEntry g_perfEntry;
runUnit g_perfUnit;

pthread_t g_asyncTmPt;
int g_asyncTmFd = 0;
bool g_asyncTmRun = false;
struct snLatency {
    int productId;
    int consumeId;
    int recvNumSucc;
    uint64_t sum;
    uint64_t vMin;
    uint64_t vMax;
    uint64_t *latency;
};
struct perfContent {
    bool snFlag;
    uint16_t connStat;
    int id;
    int asyncId;
    int sceneId;
    int offset;
    int ops;
    int status;
    int snCount;
    int countTrans;
    pthread_t hdlTS;
    pthread_t hdlSN;
    unsigned short *randomSeed;
    const char *errMsg[3];
    GmcConnT *conn[3];
    GmcStmtT *stmt[3];
    int inSize;
    int outSize;
    char *inStr;
    char *outStr;
    int speed;
    uint64_t latency;
    uint64_t requestNum;
    uint64_t repondNum;
    uint64_t countReq[2];
    uint64_t countSucc[2];
    uint64_t countLost[2];
    uint64_t countFail[2];
    uint64_t countSnI[2];
    uint64_t countSnR1[2];
    uint64_t countSnR2[2];
    uint64_t countSnR3[2];
    uint64_t countSnM1[2];
    uint64_t countSnM2[2];
    uint64_t countSnM3[2];
    uint64_t countSnU[2];
    uint64_t countSnD[2];
    uint64_t countSnLoad[2];
    uint64_t countSnAged[2];
    snLatency *sn;
    void init()
    {
        id = -1;
        sn = NULL;
        conn[0] = NULL;
        conn[1] = NULL;
        conn[2] = NULL;
        connStat = 0;
        countTrans = 0;
        snCount = 0;
    }
};
perfContent **g_testCtx = NULL;

#define MAX_EP_ASYNC_NUM 64
int g_epNum = 0;
struct epollAsync {
    bool run;
    int id;
    int fd;
    int num;
    pthread_t pt;
};
epollAsync g_epAsync[MAX_EP_ASYNC_NUM];
int g_epSubscribe[MAX_EP_ASYNC_NUM];
int epollEventCommon(int epfd, int fd, GmcEpollCtlTypeE type, uint32_t events = EPOLLIN)
{
    if (epfd == 0) {
        return 0;
    }
    int ret;
    if (type == GMC_EPOLL_DEL) {
        ret = epoll_ctl(epfd, EPOLL_CTL_DEL, fd, NULL);
        if (ret != 0) {
            printf(" EPOLL_CTL_DEL(%d, %d, %d) failed, ret = %d\n", epfd, fd, type, ret);
        }
        return ret;
    }
    struct epoll_event e;
    e.data.fd = fd;
    e.events = events;
    if (type == GMC_EPOLL_ADD) {
        ret = epoll_ctl(epfd, EPOLL_CTL_ADD, fd, &e);
        if (ret != 0) {
            printf(" EPOLL_CTL_ADD(%d, %d, %d) failed, errCode:%d, errNo:%d\n", epfd, fd, type, ret, errno);
            assert(0);
        }
        return ret;
    }
    if (type == GMC_EPOLL_MOD) {
        ret = epoll_ctl(epfd, EPOLL_CTL_MOD, fd, &e);
        if (ret != 0) {
            printf(" EPOLL_CTL_MOD(%d, %d, %d) failed, errCode:%d, errNo:%d\n", epfd, fd, type, ret, errno);
            assert(0);
        }
        return ret;
    }
}
int epollEventHeartBeat(int fd, GmcEpollCtlTypeE type)
{
    return epollEventCommon(g_asyncTmFd, fd, type);
}
int asyncEpollEvent(int32_t fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    if (userData == NULL) {
        assert(0);
    }
    epollAsync *e = (epollAsync *)userData;
    return epollEventCommon(e->fd, fd, type, events);
}
int snEpollEvent(int32_t fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    if (userData == NULL) {
        assert(0);
    }
    return epollEventCommon(*(int *)userData, fd, type, events);
}

void TestFlowCtrlLevelNoticeT(void *args, GmcDbFlowCtrlLevelE flowCtrlLevel)
{
    perfContent *p = (perfContent *)args;
    printf("[tid:%02d] [TestFlowCtrlLevelNoticeT] flowCtrlLevel = %d.\n", p->id, flowCtrlLevel);
}
void TestSubFailedCallbackT(GmcStmtT *stmt, void *userdata)
{
    perfContent *p = (perfContent *)userdata;
    printf("[tid:%02d] [TestSubFailedCallbackT] sub_data lost\n", p->id);
}

char g_strDataType[25][12] = {"char", "uchar", "int8", "uint8", "int16", "uint16", "int32", "uint32", "bool", "int64",
    "uint64", "float", "double", "resource", "time", "bit8", "bit16", "bit32", "bit64", "partiton", "string", "bytes",
    "fixed", "bitmap", "null"};
#define DATA_TYPE_OFF 20
int g_offDataType[DATA_TYPE_OFF] = {0, 1, 2, 3, 4, 6, 8, 12, 16, 20, 24, 32, 40, 48, 56, 0, 4, 8, 16, 64};
char *g_commFieldVal;
int g_commFieldLen = 1024;
pthread_mutex_t g_commFieldLock;

void AsyncFunUseNameSpace(void *userData, int32_t status, const char *errMsg)
{
    int *acx = (int *)userData;
    if (status != 0) {
        printf("GmcUseNamespaceAsync failed, status = %d|%s.\n", status, errMsg);
    }
    *acx = status;
}

struct randomVal {
    int64_t v0 : 1;
    int64_t v1 : 1;
    int64_t v2 : 6;
    int64_t v3 : 8;
    int64_t v4 : 16;
    int64_t v5 : 32;
};
struct perf_timespec {
    int64_t tv_nsec;
    time_t tv_sec;
};
unsigned short *perfInitalRandomSeed()
{
    struct perf_timespec *rev_tp;
    rev_tp = (struct perf_timespec *)malloc(sizeof(struct perf_timespec));
    if (NULL == rev_tp) {
        printf(" InitalRandomSeed, malloc memory failed.");
        return NULL;
    }
    struct timespec tp;
    clock_gettime(CLOCK_MONOTONIC, &tp);
    rev_tp->tv_nsec = tp.tv_nsec;
    rev_tp->tv_sec = tp.tv_sec;
#if defined ENV_RTOSV2X
    srand(tp.tv_nsec);
#endif
    return (unsigned short *)rev_tp;
}

int initTestContext()
{
    int threadCount = g_threadCount;
    if (threadCount < g_snThrdCount) {
        threadCount = g_snThrdCount;
    }
    int size = (sizeof(perfContent *) + sizeof(perfContent)) * threadCount;
    char *tmp = (char *)malloc(size);
    if (tmp == NULL) {
        printf("[initTestContext] malloc memory(size:%d) for g_testCtx failed.\n", size);
        return -1;
    }
    memset(tmp, 0, size);
    g_testCtx = (struct perfContent **)tmp;
    size = sizeof(perfContent *) * threadCount;
    int i, j, k;
    for (i = 0; i < threadCount; ++i) {
        g_testCtx[i] = (struct perfContent *)(tmp + size + i * sizeof(perfContent));
        g_testCtx[i]->init();
        g_testCtx[i]->randomSeed = perfInitalRandomSeed();
        g_testCtx[i]->id = i;
        g_testCtx[i]->inSize = 128;
        g_testCtx[i]->inStr = (char *)malloc(129);
        if (g_testCtx[i]->inStr == NULL) {
            printf("[initTestContext] malloc memory(size:128) for g_testCtx[%d]->inStr failed.\n", i);
            return -1;
        }
        for (j = 0; j < 128; ++j) {
            g_testCtx[i]->inStr[j] = 'a' + j % 26;
        }
        g_testCtx[i]->outSize = 128;
        g_testCtx[i]->outStr = (char *)malloc(129);
        if (g_testCtx[i]->outStr == NULL) {
            printf("[initTestContext] malloc memory(size:128) for g_testCtx[%d]->outStr failed.\n", i);
            return -1;
        }
        g_testCtx[i]->outStr[0] = '\0';
        if (g_snThrdCount > 0) {
            k = sizeof(snLatency);
            g_testCtx[i]->sn = (snLatency *)malloc(k);
            if (g_testCtx[i]->sn == NULL) {
                printf("[initTestContext] malloc memory(size:%d) for g_testCtx[%d]->sn failed.\n", k, i);
                return -1;
            }
            memset(g_testCtx[i]->sn, 0, k);
            if (g_snLatency) {
                g_testCtx[i]->sn->productId = g_perfUnit.endCycle - g_perfUnit.beginCycle;
                g_testCtx[i]->sn->vMin = 0x7FFFFFFF;
                k = sizeof(uint64_t) * (g_perfUnit.endCycle - g_perfUnit.beginCycle);
                g_testCtx[i]->sn->latency = (uint64_t *)malloc(k);
                if (g_testCtx[i]->sn->latency == NULL) {
                    printf("[initTestContext] malloc memory(size:%d) for g_testCtx[%d]->sn->latency failed.\n", k, i);
                    return -1;
                }
                memset(g_testCtx[i]->sn->latency, 0, k);
            }
        }
    }
    k = 0;
    for (i = 0; i < g_sceneNum; ++i) {
        g_sceneEntry[i].startTid = k;
        for (j = 0; j < g_sceneEntry[i].threadCount; ++j) {
            g_testCtx[k]->sceneId = i;
            g_testCtx[k]->offset = j;
            ++k;
        }
    }
    g_commFieldVal = (char *)malloc(g_commFieldLen + 64);
    if (g_commFieldVal == NULL) {
        printf("[initTestContext] malloc memory(size:%d) for g_commFieldVal failed.\n", g_commFieldLen);
        return -1;
    }
    memset(g_commFieldVal, 0, g_commFieldLen + 64);
    g_commFieldVal[0] = 'A';
    g_commFieldVal[1] = 'a';
    g_commFieldVal[2] = 1;
    g_commFieldVal[3] = 2;
    *(short int *)(g_commFieldVal + 4) = 101;
    *(unsigned short int *)(g_commFieldVal + 6) = 102;
    *(int *)(g_commFieldVal + 8) = 1001;
    *(unsigned int *)(g_commFieldVal + 12) = 1002;
    *(bool *)(g_commFieldVal + 16) = false;
    *(int *)(g_commFieldVal + 20) = 0;
    *(int64_t *)(g_commFieldVal + 24) = 10001;
    *(uint64_t *)(g_commFieldVal + 32) = 10002;
    *(float *)(g_commFieldVal + 40) = 3.14159;
    *(double *)(g_commFieldVal + 48) = 3.1415926;
    *(int64_t *)(g_commFieldVal + 56) = 123456789;
    for (i = 0; i < g_commFieldLen; ++i) {
        g_commFieldVal[64 + i] = 'a' + i % 26;
    }
    g_commFieldVal[64 + g_commFieldLen - 1] = '\0';
    return 0;
}

int expandFieldBuf(int size)
{
    int ret = 0;
    if (g_commFieldLen >= size) {
        return ret;
    }
    pthread_mutex_lock(&g_commFieldLock);
    do {
        char *pos = (char *)malloc(size + 68);
        if (pos == NULL) {
            printf("[expandFieldBuf] malloc memory(size:%d) for g_commFieldVal failed.\n", size + 68);
            ret = 1;
            break;
        }
        memcpy(pos, g_commFieldVal, g_commFieldLen + 64);
        int i;
        for (i = g_commFieldLen + 64; i < size + 64; ++i) {
            pos[i] = 'A' + i % 26;
        }
        pos[i] = '\0';
        free(g_commFieldVal);
        g_commFieldVal = pos;
        g_commFieldLen = size;
    } while (0);
    pthread_mutex_unlock(&g_commFieldLock);
    return ret;
}

void destroyTestContext()
{
    for (int i = 0; i < g_threadCount; ++i) {
        free(g_testCtx[i]->randomSeed);
    }
    free(g_testCtx[0]);
    free(g_testCtx);
    g_testCtx = NULL;
}

int perfEnvInit(char *cfgPath = NULL)
{
    return GmcInit();
}

void perfEnvUnInit()
{
    (void)GmcUnInit();
}

void *threadSubscribeMain(void *arg)
{
    perfContent *p = (perfContent *)arg;
    char tmpBuf[24];
    snprintf(tmpBuf, sizeof(tmpBuf), "epollSub%04d", p->id);
    prctl(PR_SET_NAME, tmpBuf);
    int n = g_threadCount / MAX_EP_ASYNC_NUM + 2;
    if (g_threadCount < g_snThrdCount) {
        n = g_snThrdCount / MAX_EP_ASYNC_NUM + 2;
    }
    struct epoll_event *etOut = (struct epoll_event *)malloc(sizeof(struct epoll_event) * n);
    if (etOut == NULL) {
        printf("[tid:%d] [threadSubscribeMain] malloc memory:%u for etOut failed.\n", p->id,
            sizeof(struct epoll_event) * n);
        return NULL;
    }
    memset(etOut, 0, sizeof(struct epoll_event) * n);
    int i, nfds;
    p->snFlag = true;
    while (p->snFlag) {
        nfds = epoll_wait(g_epSubscribe[p->id], etOut, n, 0);
        if (nfds == 0) {
            usleep(1000);
            continue;
        } else if (nfds < 0) {
            printf("[tid:%d] [threadSubscribeMain] epoll_wait(fd:%d,recv) socket failed, ret = %d, errno = %d.\n",
                p->id, g_epSubscribe[p->id], nfds, errno);
            usleep(1000);
            continue;
        }
        for (i = 0; i < nfds; ++i) {
            GmcHandleRWEvent(etOut[i].data.fd, etOut[i].events);
        }
    }
    close(g_epSubscribe[p->id]);
    g_epSubscribe[p->id] = 0;
    free(etOut);
    printf("[tid:%02d] [threadSubscribeMain] insert:%lu, replace:%lu:%lu:%lu, merge:%lu:%lu:%lu, update:%lu, "
           "delete:%lu, age:%lu\n",
        p->id, p->countSnI[0], p->countSnR1[0], p->countSnR2[0], p->countSnR3[0], p->countSnM1[0], p->countSnM2[0],
        p->countSnM3[0], p->countSnU[0], p->countSnD[0], p->countSnAged[0]);
    p->ops = -1;
}

int perfGmcConnect(int threadId, GmcConnTypeE connMode = GMC_CONN_TYPE_SYNC, char *connName = NULL, int ringLen = 256)
{
    int ret;
    GmcConnOptionsT *connOptions = NULL;
    do {
        ret = GmcConnOptionsCreate(&connOptions);
        if (ret != GMERR_OK) {
            printf("[tid:%02d] [perfGmcConnect] GmcConnOptionsCreate failed, ret = %d.\n", threadId, ret);
            break;
        }
        ret = GmcConnOptionsSetServerLocator(connOptions, g_perfServer[g_perfMode]);
        if (ret != GMERR_OK) {
            printf("[tid:%02d] [perfGmcConnect] GmcConnOptionsSetServerLocator(%s) failed, ret = %d.\n", threadId,
                g_perfServer[g_perfMode], ret);
            break;
        }

        ret = GmcConnOptionsSetFlowCtrlCallback(connOptions, TestFlowCtrlLevelNoticeT, g_testCtx[threadId]);
        if (ret != GMERR_OK) {
            printf("[tid:%02d] [perfGmcConnect] GmcConnOptionsSetFlowCtrlCallback failed, ret = %d.\n", threadId, ret);
            break;
        }
        if (connMode == GMC_CONN_TYPE_SYNC) {
            break;
        }
        if (connMode == GMC_CONN_TYPE_ASYNC) {
            ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptions, asyncEpollEvent,
                g_epAsync + threadId % MAX_EP_ASYNC_NUM);
            if (ret != 0) {
                printf("[tid:%02d] [perfGmcConnect] GmcConnOptionsSetEpollRegFuncWithUserData failed, ret = %d.\n",
                    threadId, ret);
            }
            break;
        }
        char thdName[64];
        if (connName && connName[0] != '\0') {
            snprintf(thdName, sizeof(thdName), "%s_%02d", connName, threadId);
        } else {
            snprintf(thdName, sizeof(thdName), "sn_%d_%02d", getpid(), threadId);
        }
        ret = GmcConnOptionsSetConnName(connOptions, thdName);
        if (ret != 0) {
            printf("[tid:%02d] [perfGmcConnect] GmcConnOptionsSetConnName(%s) failed, ret = %d.\n", threadId, thdName,
                ret);
            break;
        }
        ret = GmcConnOptionsSetFlowCtrlCallback(connOptions, TestFlowCtrlLevelNoticeT, g_testCtx[threadId]);
        if (ret != GMERR_OK) {
            printf("[tid:%02d] [perfGmcConnect] GmcConnOptionsSetFlowCtrlCallback failed, ret = %d.\n", threadId, ret);
            break;
        }
        if (g_perfMode == 1) {
            ret = GmcConnOptionsSetSubFailedCallback(connOptions, TestSubFailedCallbackT, g_testCtx[threadId]);
            if (ret != 0) {
                printf("[tid:%02d] [perfGmcConnect] GmcConnOptionsSetSubFailedCallback failed, errCode:%d.\n", threadId,
                    ret);
                break;
            }
        }
        if (threadId < MAX_EP_ASYNC_NUM) {
            if (g_snThrdCount > g_threadCount) {
                g_epSubscribe[threadId] = epoll_create(g_snThrdCount / MAX_EP_ASYNC_NUM + 2);
            } else {
                g_epSubscribe[threadId] = epoll_create(g_threadCount / MAX_EP_ASYNC_NUM + 2);
            }
        }
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptions, snEpollEvent,
            g_epSubscribe + threadId % MAX_EP_ASYNC_NUM);
        if (ret != 0) {
            printf("[tid:%02d] [perfGmcConnect] GmcConnOptionsSetEpollRegFunc failed, ret = %d.\n", threadId, ret);
            break;
        }
        if (threadId < MAX_EP_ASYNC_NUM) {
            ret = pthread_create(&g_testCtx[threadId]->hdlSN, NULL, threadSubscribeMain, g_testCtx[threadId]);
            if (ret != 0) {
                printf("[tid:%02d] [perfGmcConnect] pthread_create.threadSubscribeMain failed, ret = %d.\n", threadId,
                    ret);
                break;
            }
            usleep(100000);
        }
    } while (0);
    if (ret != 0) {
        if (connOptions) {
            GmcConnOptionsDestroy(connOptions);
        }
        ++g_testCtx[threadId]->connStat;
        return ret;
    }
    GmcConnT *conn;
    int threadCount = g_threadCount;
    if (threadCount < g_snThrdCount) {
        threadCount = g_snThrdCount;
    }
    if (g_envType == 0 || g_envType == 3) {  // only for euler env
        int i, threadSucc;
        pthread_mutex_lock(&g_connConcurrentLock);
        while (g_envType == 0 || g_envType == 3) {
            threadSucc = 0;
            for (i = 0; i < threadCount; ++i) {
                threadSucc += g_testCtx[i]->connStat;
            }
            if (g_connConcurrentSize > threadSucc && g_connConcurrentSize - threadSucc > 120) {
                usleep(100000);
                continue;
            }
            ++g_connConcurrentSize;
            break;
        }
        pthread_mutex_unlock(&g_connConcurrentLock);
    }
    ret = GmcConnect(connMode, connOptions, &conn);
    GmcConnOptionsDestroy(connOptions);
    ++g_testCtx[threadId]->connStat;
    if (ret != 0) {
        printf("[tid:%02d] perfGmcConnect.GmcConnect(%s, %s, %s) failed, ret = %d.\n", threadId,
            g_perfServer[g_perfMode], g_perfUser, g_perfPasswd, ret);
        return -1;
    }
    g_testCtx[threadId]->errMsg[connMode]= GmcGetLastError();
    g_testCtx[threadId]->conn[connMode] = conn;
    return ret;
}
void perfGmcDisConnect(int threadId, GmcConnTypeE connMode = GMC_CONN_TYPE_BUTT)
{
    int i, ret;
    for (i = 0; i < 3; ++i) {
        if (connMode != GMC_CONN_TYPE_BUTT && connMode != (GmcConnTypeE)i) {
            continue;
        }
        if (g_testCtx[threadId]->conn[i]) {
            ret = GmcDisconnect(g_testCtx[threadId]->conn[i]);
            if (ret != 0) {
                printf("[testGmcDisconnect] GmcDisconnect(%d) failed, ret = %d.\n", i, ret);
            }
            g_testCtx[threadId]->conn[i] = NULL;
        }
    }
}
int perfSetNameSpace(int threadId, GmcConnTypeE connMode, char *nameSpace)
{
    if (nameSpace == NULL || nameSpace[0] == '\0' || 0 == strcmp(nameSpace, "default")) {
        return 0;
    }
    perfContent *p = g_testCtx[threadId];
    GmcStmtT *stmt;
    int ctxStatus;
    int ret = GmcAllocStmt(p->conn[connMode], &stmt);
    if (ret != 0) {
        printf("[tid:%02d] perfSetNameSpace.GmcAllocStmt failed, ret = %d.\n", threadId, ret);
        return -1;
    }
    do {
        if (connMode == GMC_CONN_TYPE_SYNC) {
            ret = GmcUseNamespace(stmt, nameSpace);
            if (ret != 0) {
                printf("[tid:%02d] perfSetNameSpace.GmcUseNamespace(%s) failed, errCode:%d, errMsg:%s\n", threadId,
                    nameSpace, ret, p->errMsg[GMC_CONN_TYPE_SYNC]);
            }
            break;
        }
        ctxStatus = 2;
        ret = GmcUseNamespaceAsync(stmt, nameSpace, AsyncFunUseNameSpace, &ctxStatus);
        if (ret != 0) {
            printf("[tid:%02d] perfSetNameSpace.GmcUseNamespaceAsync(%s) failed, errCode:%d, errMsg:%s\n", threadId,
                nameSpace, ret, p->errMsg[GMC_CONN_TYPE_SYNC]);
            return ret;
        }
        int i = 300;
        while (ctxStatus == 2 && i > 0) {
            usleep(10000);
            --i;
        }
        if (i == 0) {
            ret = 1;
            printf("[tid:%02d] perfSetNameSpace.GmcUseNamespaceAsync(%s) timeout(3s).\n", threadId, nameSpace);
            break;
        }
        ret = ctxStatus;
        if (ctxStatus != 0) {
            printf("[tid:%02d] perfSetNameSpace.GmcUseNamespaceAsync(%s) failed, ret = %d.\n", threadId, nameSpace,
                ctxStatus);
        }
    } while (0);
    GmcFreeStmt(stmt);
    return ret;
}

int64_t perfReadJsonFile(const char *path, char **buf)
{
    FILE *fp;
    fp = fopen(path, "rb");
    if (NULL == fp) {
        printf("[perfReadJsonFile] open file:%s fail.\n", path);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        printf("[perfReadJsonFile] fseek file:%s to end failed.\n", path);
        fclose(fp);
        return -1;
    }

    int64_t size = ftell(fp);
    if (size < 0) {
        printf("[perfReadJsonFile] read file size:%ld failed.\n", size);
        fclose(fp);
        return -1;
    }

    char *pBuffer = (char *)malloc(size + 4);
    if (pBuffer == NULL) {
        printf("[perfReadJsonFile] malloc memory:%ld for file:%s failed.\n", size + 4, path);
        fclose(fp);
        return -1;
    }
    (void)fseek(fp, 0L, SEEK_SET);
    int64_t readSize = fread(pBuffer, 1, size, fp);
    if (readSize != size) {
        printf("[perfReadJsonFile] read file:%s failed, expectSize:%ld, actualSize:%ld.\n", path, size, readSize);
        free(pBuffer);
        fclose(fp);
        return -1;
    }
    fclose(fp);
    pBuffer[size] = '\0';
    *buf = pBuffer;
    return size;
}

int executeCmd(char *cmd, char *v1 = NULL)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("[executeCmd] popen(%s) error.\n", cmd);
        return -1;
    }
    int len = 0;
    char cmdOutput[4096] = {0};
    while (NULL != fgets(cmdOutput + len, 4000 - len, pf)) {
        len = strlen(cmdOutput);
    }
    pclose(pf);
    if (v1) {
        snprintf(v1, strlen(cmdOutput), "%s", cmdOutput);
    }
    return 0;
}

//  S6730   1400MHz    taishan 2600MHz
double g_cpuMHz = 1.0;
double g_usInterval = 0;

inline uint64_t getCpuCycles()
{
    uint64_t v = 0;
#if defined X86
    uint32_t lo = 0;
    uint32_t hi = 0;
    asm volatile("rdtsc" : "=a"(lo), "=d"(hi));
    v = (((uint64_t)hi << DB_32BIT) | lo);
#elif defined ENV_RTOSV2X
    asm volatile("isb" : : : "memory");
    asm volatile("mrs %0, cntvct_el0" : "=r"(v));
#elif defined ENV_RTOSV2
    asm volatile("isb" : : : "memory");
    asm volatile("mrs %0, PMCCNTR_EL0" : "+r"(v));

#else
    asm volatile("isb" : : : "memory");
    asm volatile("mrs %0, cntvct_el0" : "=r"(v));
#endif
    return v;
}

inline uint64_t getAndCalCpuCycles()
{
    uint64_t cval = getCpuCycles();
    double dval = (double)cval * g_cpuMHz;
    cval = dval;
    return cval;
}

inline uint64_t getCyclesUS()
{
    uint64_t v = getCpuCycles();
    uint64_t usV = (double)v / g_usInterval;
    return usV;
}
inline uint64_t getCyclesMS()
{
    uint64_t v = getCpuCycles();
    uint64_t msV = (double)v / g_usInterval / 1000;
    return msV;
}
inline uint64_t getCyclesS()
{
    uint64_t v = getCpuCycles();
    uint64_t sV = (double)v / g_usInterval / 1000000;
    return sV;
}
void getCpuMHz()
{
    char cmd[] = "cat /proc/cpuinfo | grep 'cpu MHz' | head -n 1 | awk '{print $4}'";
    char strResult[64];
    int ret = executeCmd(cmd, strResult);
    if (ret != 0) {
        return;
    }
    uint64_t cpuMHz = atoi(strResult);
    if (cpuMHz != 0) {
        cpuMHz = cpuMHz * 1000 * 1000;
    } else {
        cpuMHz = 1400000000;
    }
    uint64_t v1 = getCpuCycles();
    sleep(1);
    uint64_t v2 = getCpuCycles();
    g_cpuMHz = (double)cpuMHz / (v2 - v1);
    g_usInterval = (double)(v2 - v1) / 1000000;
}

bool checkCompleteName(char *name)
{
    if (g_renameTable == false) {
        return true;
    }
    int len = strlen(name);
    if (len < 5) {
        return false;
    }
    if ((name[0] == 'v' || name[0] == 'V') && name[1] == '$') {
        return true;
    }
    int i = len - 5;
    while (i < len && name[i] >= '0' && name[i] <= '9') {
        ++i;
    }
    if (i == len) {
        return true;
    }
    return false;
}

void fixCompleteName(char *name)
{
    if (checkCompleteName(name)) {
        return;
    }
    int len = strlen(name);
    for (int i = 0; i < 5; ++i) {
        name[len + i] = '0';
    }
    name[len + 5] = '\0';
}

// xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx data model tree
/*
#define DM_MAX_NAME_LENGTH          128
#define DM_MAX_COMMENT_LENGTH       512
#define DM_MAX_SUPERFIELD_NUM       5
#define DM_MAX_LABEL_WRITERS_NAME_LENGTH  256
#define MAX_TABLE_NAME_LEN  128
#define DB_SCHEMA_MAX_DEPTH 32
#define DB_MAX_INDEX_NAME_LENGTH ((DB_SCHEMA_MAX_DEPTH + 1) * MAX_TABLE_NAME_LEN)

enum DbDataTypeE{
    DB_DATATYPE_CHAR,
    DB_DATATYPE_UCHAR,
    DB_DATATYPE_INT8,
    DB_DATATYPE_UINT8,
    DB_DATATYPE_INT16,
    DB_DATATYPE_UINT16,
    DB_DATATYPE_INT32,
    DB_DATATYPE_UINT32,
    DB_DATATYPE_BOOL,
    DB_DATATYPE_INT64,
    DB_DATATYPE_UINT64,
    DB_DATATYPE_FLOAT,
    DB_DATATYPE_DOUBLE,
    DB_DATATYPE_RESOURCE,
    DB_DATATYPE_TIME,
    DB_DATATYPE_BITFIELD8,
    DB_DATATYPE_BITFIELD16,
    DB_DATATYPE_BITFIELD32,
    DB_DATATYPE_BITFIELD64,
    DB_DATATYPE_PARTITION,
    DB_DATATYPE_STRING,
    DB_DATATYPE_BYTES,
    DB_DATATYPE_FIXED,
    DB_DATATYPE_BITMAP,
    DB_DATATYPE_NULL // 表明当前Value是一个空值，并不是严格意义的数据类型
};
*/

int getFieldOffsetFromCommStr(DbDataTypeE e)
{
    if (e == DB_DATATYPE_CHAR) {
        return 0;
    } else if (e == DB_DATATYPE_UCHAR) {
        return 1;
    } else if (e == DB_DATATYPE_INT8) {
        return 2;
    } else if (e == DB_DATATYPE_UINT8) {
        return 3;
    } else if (e == DB_DATATYPE_INT16) {
        return 4;
    } else if (e == DB_DATATYPE_UINT16) {
        return 6;
    } else if (e == DB_DATATYPE_INT32) {
        return 8;
    } else if (e == DB_DATATYPE_UINT32) {
        return 12;
    } else if (e == DB_DATATYPE_BOOL) {
        return 16;
    } else if (e == DB_DATATYPE_PARTITION) {
        return 20;
    } else if (e == DB_DATATYPE_INT64) {
        return 24;
    } else if (e == DB_DATATYPE_UINT64) {
        return 32;
    } else if (e == DB_DATATYPE_FLOAT) {
        return 40;
    } else if (e == DB_DATATYPE_DOUBLE) {
        return 48;
    } else if (e == DB_DATATYPE_TIME) {
        return 56;
    } else if (e == DB_DATATYPE_BITFIELD8) {
        return 3;
    } else if (e == DB_DATATYPE_BITFIELD16) {
        return 6;
    } else if (e == DB_DATATYPE_BITFIELD32) {
        return 12;
    } else if (e == DB_DATATYPE_BITFIELD64) {
        return 32;
    }
    return 64;
}
char *GetDataType(DbDataTypeE e, int *v = NULL)
{
    int id = -1;
    if (e == DB_DATATYPE_CHAR) {
        id = 0;
    } else if (e == DB_DATATYPE_UCHAR) {
        id = 1;
    } else if (e == DB_DATATYPE_INT8) {
        id = 2;
    } else if (e == DB_DATATYPE_UINT8) {
        id = 3;
    } else if (e == DB_DATATYPE_INT16) {
        id = 4;
    } else if (e == DB_DATATYPE_UINT16) {
        id = 5;
    } else if (e == DB_DATATYPE_INT32) {
        id = 6;
    } else if (e == DB_DATATYPE_UINT32) {
        id = 7;
    } else if (e == DB_DATATYPE_BOOL) {
        id = 8;
    } else if (e == DB_DATATYPE_INT64) {
        id = 9;
    } else if (e == DB_DATATYPE_UINT64) {
        id = 10;
    } else if (e == DB_DATATYPE_FLOAT) {
        id = 11;
    } else if (e == DB_DATATYPE_DOUBLE) {
        id = 12;
    } else if (e == DB_DATATYPE_RESOURCE) {
        id = 13;
    } else if (e == DB_DATATYPE_TIME) {
        id = 14;
    } else if (e == DB_DATATYPE_BITFIELD8) {
        id = 15;
    } else if (e == DB_DATATYPE_BITFIELD16) {
        id = 16;
    } else if (e == DB_DATATYPE_BITFIELD32) {
        id = 17;
    } else if (e == DB_DATATYPE_BITFIELD64) {
        id = 18;
    } else if (e == DB_DATATYPE_PARTITION) {
        id = 19;
    } else if (e == DB_DATATYPE_STRING) {
        id = 20;
    } else if (e == DB_DATATYPE_BYTES) {
        id = 21;
    } else if (e == DB_DATATYPE_FIXED) {
        id = 22;
    } else if (e == DB_DATATYPE_BITMAP) {
        id = 23;
    } else if (e == DB_DATATYPE_NULL) {
        id = 24;
    }
    if (v) {
        *v = id;
    }
    return g_strDataType[id];
}

// -------------------------------------------------------------------------------------------------------------------------------------------------------

struct subTabDef {
    DmNodeTypeE nodeType;
    int level;
    int arraySize;  // only for fixed_array
    int fieldNum;
    DmPropertySchemaT *fs;
    char nodeName[32];
    int subTabNum;
    subTabDef *subTabAddr;
};

struct metaDataLabelVertex {
    int metaId;
    int fullLen;
    int keyLen;
    int fieldNum;
    int secIdxNum;
    int smallMemNum;
    int smallMemOff;
    int subTabNum;
    int *secIdxLen;
    int *secIsUnique;
    subTabDef *subTabAddr;
    DmPropertySchemaT *fs;
    DmAutoIncrPropInfoT autoIncrPropInfo;
    DmVlIndexLabelT *pkIdx;
    DmVlIndexLabelT *secIdx;
    char *smallMemAddr[10];
    char metaName[64];
    void init()
    {
        fieldNum = 0;
        secIdxNum = 0;
        smallMemNum = 0;
        smallMemOff = 0;
        subTabNum = 0;
        secIsUnique = NULL;
        secIdxLen = NULL;
        fs = NULL;
        pkIdx = NULL;
        secIdx = NULL;
        subTabAddr = NULL;
        for (int i = 0; i < 10; ++i) {
            smallMemAddr[i] = NULL;
        }
    }
};

struct fieldDefine {
    bool isSet;
    bool useCommon;
    int varType;
    int varBegin;
    int varInc;
    int varOffset;
    int varLast;
    int64_t varVal;
    char *varPreText;
    void *next;
    DmPropertySchemaT *f;
};

void releaseMetaDataVertex(metaDataLabelVertex *t)
{
    if (t == NULL) {
        return;
    }
    if (t->secIdxLen) {
        free(t->secIdxLen);
    }
    if (t->pkIdx) {
        free(t->pkIdx);
    }
    if (t->secIdx) {
        free(t->secIdx);
    }
    for (int i = 0; i < 10; ++i) {
        if (t->smallMemAddr[i]) {
            free(t->smallMemAddr[i]);
        }
    }
    free(t->fs);
    free(t);
    t = NULL;
}

struct metaDataLabelEdge {
    int metaId;
    DmVlIndexLabelT *si;
    DmVlIndexLabelT *di;
    metaDataLabelVertex *sv;
    metaDataLabelVertex *dv;
    char edgeName[CUSTOM_LEN32];
    char sourceName[CUSTOM_LEN32];
    char destName[CUSTOM_LEN32];
};

metaDataLabelVertex **g_metaVertexEntry = NULL;
int g_metaVertexNum = 0;
int g_metaVertexMax = 0;
metaDataLabelEdge **g_metaEdgeEntry = NULL;
int g_metaEdgeNum = 0;
int g_metaEdgeMax = 0;
pthread_mutex_t g_memadataLock;

void addMetaDataVertex(metaDataLabelVertex *t, int id)
{
    if (t == NULL) {
        printf("[addMetaDataVertex] t is null.\n");
        return;
    }
    if (g_metaVertexNum == g_metaVertexMax) {
        g_metaVertexMax += 10;
        metaDataLabelVertex **l = (metaDataLabelVertex **)malloc(sizeof(metaDataLabelVertex *) * g_metaVertexMax);
        if (l == NULL) {
            printf("[addMetaDataVertex] malloc memory(%ld).\n", sizeof(metaDataLabelVertex *) * g_metaVertexMax);
            return;
        }
        if (g_metaVertexNum > 0) {
            memcpy(l, g_metaVertexEntry, sizeof(metaDataLabelVertex *) * g_metaVertexNum);
            free(g_metaVertexEntry);
        }
        g_metaVertexEntry = l;
    }
    t->metaId = g_metaVertexNum;
    g_metaVertexEntry[g_metaVertexNum] = t;
    ++g_metaVertexNum;
}

void addMetaDataEdge(metaDataLabelEdge *t, int id)
{
    if (t == NULL) {
        printf("[addMetaDataEdge] t is null.\n");
        return;
    }
    if (id != -1 && id < 0 && id >= g_metaEdgeNum) {
        printf("[addMetaDataEdge] invalid id(%d), request.\n", id);
        return;
    }
    if (id != -1) {
        t->metaId = id;
        g_metaEdgeEntry[id] = t;
        return;
    }
    if (g_metaEdgeNum == g_metaEdgeMax) {
        g_metaEdgeMax += 10;
        metaDataLabelEdge **l = (metaDataLabelEdge **)malloc(sizeof(metaDataLabelEdge *) * g_metaEdgeMax);
        if (l == NULL) {
            printf("[addMetaDataEdge] malloc memory(%ld) for .\n", sizeof(metaDataLabelEdge *) * g_metaEdgeMax);
            return;
        }
        if (g_metaEdgeNum > 0) {
            memcpy(l, g_metaEdgeEntry, sizeof(metaDataLabelEdge *) * g_metaEdgeNum);
            free(g_metaEdgeEntry);
        }
        g_metaEdgeEntry = l;
    }
    t->metaId = g_metaEdgeNum;
    g_metaEdgeEntry[g_metaEdgeNum] = t;
    ++g_metaEdgeNum;
}

char *getSmallMemAddr(metaDataLabelVertex *t, int64_t len)
{
    int actualLen = len + 1;
    if (t->smallMemAddr[t->smallMemNum] && t->smallMemOff + actualLen >= 512) {
        ++t->smallMemNum;
        t->smallMemOff = 0;
    }
    if (t->smallMemAddr[t->smallMemNum] == NULL) {
        t->smallMemAddr[t->smallMemNum] = (char *)malloc(512);
        if (t->smallMemAddr[t->smallMemNum] == NULL) {
            return NULL;
        }
    }
    t->smallMemOff += actualLen;
    return (t->smallMemAddr[t->smallMemNum] + t->smallMemOff - actualLen);
}

void *GetMetaDataFromCache(char *metaName)
{
    void *vertexLabel = NULL;
    pthread_mutex_lock(&g_memadataLock);
    for (int i = 0; i < g_metaVertexNum; ++i) {
        if (g_metaVertexEntry[i] && 0 == strcmp(metaName, g_metaVertexEntry[i]->metaName)) {
            vertexLabel = g_metaVertexEntry[i];
            break;
        }
    }
    pthread_mutex_unlock(&g_memadataLock);
    return vertexLabel;
}

void generateSubTable(metaDataLabelVertex *t, subTabDef *s, DmNodeSchemaT *n)
{
    char *orgName = MEMBER_PTR(n, name);
    (void)snprintf(s->nodeName, sizeof(s->nodeName), orgName);
    s->nodeType = n->nodeType;
    s->arraySize = n->arraySize;
    DmSchemaT *schema = MEMBER_PTR(n, schema);
    s->level = schema->level;
    s->fieldNum = 0;
    int i;
    DmPropertySchemaT *prop = MEMBER_PTR(schema, properties);
    for (i = 0; i < schema->propeNum; ++i) {
        if (prop[i].isValid == true) {
            ++s->fieldNum;
        }
    }
    s->fs = (DmPropertySchemaT *)malloc(sizeof(DmPropertySchemaT) * s->fieldNum);
    if (s->fs == NULL) {
        printf("[generateSubTable] malloc memory(size:%u) for s->fs failed, subTable = %s.\n",
            sizeof(DmPropertySchemaT) * s->fieldNum, s->nodeName);
        exit(1);
    }
    char *colName;
    s->fieldNum = 0;
    for (i = 0; i < schema->propeNum; ++i) {
        if (prop[i].isValid == false) {
            continue;
        }
        s->fs[s->fieldNum] = prop[i];
        orgName = MEMBER_PTR(prop + i, name);
        colName = getSmallMemAddr(t, strlen(orgName));
        if (colName == NULL) {
            break;
        }
        (void)snprintf(colName, strlen(orgName) + 1, "%s", orgName);
        s->fs[s->fieldNum].name = colName;
        if (s->fs[s->fieldNum].dataType == DB_DATATYPE_BITFIELD8 ||
            s->fs[s->fieldNum].dataType == DB_DATATYPE_BITFIELD16 ||
            s->fs[s->fieldNum].dataType == DB_DATATYPE_BITFIELD32 ||
            s->fs[s->fieldNum].dataType == DB_DATATYPE_BITFIELD64) {
            --s->fs[s->fieldNum].size;
            s->fs[s->fieldNum].bitfieldOffset = 1;
            while (s->fs[s->fieldNum].size > 0) {
                s->fs[s->fieldNum].bitfieldOffset = (s->fs[s->fieldNum].bitfieldOffset << 1) + 1;
                --s->fs[s->fieldNum].size;
            }
        }
        if (s->fs[s->fieldNum].dataType == DB_DATATYPE_BITFIELD8) {
            s->fs[s->fieldNum].size = 1;
        } else if (s->fs[s->fieldNum].dataType == DB_DATATYPE_BITFIELD16) {
            s->fs[s->fieldNum].size = 2;
        } else if (s->fs[s->fieldNum].dataType == DB_DATATYPE_BITFIELD32) {
            s->fs[s->fieldNum].size = 4;
        } else if (s->fs[s->fieldNum].dataType == DB_DATATYPE_BITFIELD64) {
            s->fs[s->fieldNum].size = 8;
        } else if (s->fs[s->fieldNum].dataType == DB_DATATYPE_BITMAP) {
            s->fs[s->fieldNum].size -=
                64;  // property->size += sizeof(uint32_t) * BYTE_LENGTH + sizeof(uint32_t) * BYTE_LENGTH;
        }
        ++s->fieldNum;
    }
    s->subTabNum = schema->nodeNum;
    if (s->subTabNum == 0) {
        return;
    }
    s->subTabAddr = (subTabDef *)malloc(sizeof(subTabDef) * s->subTabNum);
    if (s->subTabAddr == NULL) {
        printf("[generateSubTable] malloc memory(size:%u) for s->subTabAddr failed, subTable = %s.\n",
            sizeof(subTabDef) * s->subTabNum, s->nodeName);
        exit(1);
    }
    memset(s->subTabAddr, 0, sizeof(subTabDef) * s->subTabNum);
    DmNodeSchemaT *nodes = MEMBER_PTR(schema, nodes);
    for (int i = 0; i < s->subTabNum; ++i) {
        generateSubTable(t, s->subTabAddr + i, nodes + i);
    }
}

void *genMetaDataVertex(char *metaName, void *vLabel)
{
    int i, j, k;
    pthread_mutex_lock(&g_memadataLock);
    DmVertexLabelT *dvl = (DmVertexLabelT *)vLabel;
    DmSchemaT *ds = MEMBER_PTR(dvl->metaVertexLabel, schema);
    metaDataLabelVertex *d = NULL;
    metaDataLabelVertex *t = (metaDataLabelVertex *)malloc(sizeof(metaDataLabelVertex));
    if (t == NULL) {
        printf("[genMetaDataVertex] malloc memory(%ld).\n", sizeof(metaDataLabelVertex));
        pthread_mutex_unlock(&g_memadataLock);
        return NULL;
    }
    t->init();
    (void)snprintf(t->metaName, sizeof(t->metaName), "%s", MEMBER_PTR(&(dvl->metaCommon), metaName));
    if (dvl->commonInfo->autoIncrPropInfo) {
        t->autoIncrPropInfo = *(dvl->commonInfo->autoIncrPropInfo);
    } else {
        t->autoIncrPropInfo.autoIncrPropId = 0xFFFFFFFF;
    }
    char *colName;
    char *orgName;
    DmPropertySchemaT *prop = MEMBER_PTR(ds, properties);
    do {
        t->fieldNum = ds->propeNum;
        orgName = MEMBER_PTR(prop + t->fieldNum - 1, name);
        if (strcmp(orgName, "sys_check_version") == 0) {
            --t->fieldNum;
        }
        k = 0;
        for (i = 0; i < t->fieldNum; ++i) {
            if (prop[i].isValid) {
                ++k;
            }
        }
        t->fs = (DmPropertySchemaT *)malloc(sizeof(DmPropertySchemaT) * k);
        if (t->fs == NULL) {
            printf("[genMetaDataVertex] malloc memory(%ld) for t->fs failed.\n", sizeof(DmPropertySchemaT) * k);
            break;
        }
        i = 0;
        t->fullLen = 0;
        for (k = 0; k < t->fieldNum; ++k) {
            if (prop[k].isValid == false) {
                continue;
            }
            t->fs[i] = prop[k];
            orgName = MEMBER_PTR(prop + k, name);
            colName = getSmallMemAddr(t, strlen(orgName));
            if (colName == NULL) {
                break;
            }
            (void)snprintf(colName, strlen(orgName) + 1, "%s", orgName);
            t->fs[i].name = colName;
            if (t->fs[i].dataType == DB_DATATYPE_BITFIELD8 || t->fs[i].dataType == DB_DATATYPE_BITFIELD16 ||
                t->fs[i].dataType == DB_DATATYPE_BITFIELD32 || t->fs[i].dataType == DB_DATATYPE_BITFIELD64) {
                --t->fs[i].size;
                t->fs[i].bitfieldOffset = 1;
                while (t->fs[i].size > 0) {
                    t->fs[i].bitfieldOffset = (t->fs[i].bitfieldOffset << 1) + 1;
                    --t->fs[i].size;
                }
            }
            if (t->fs[i].dataType == DB_DATATYPE_BITFIELD8) {
                t->fs[i].size = 1;
            } else if (t->fs[i].dataType == DB_DATATYPE_BITFIELD16) {
                t->fs[i].size = 2;
            } else if (t->fs[i].dataType == DB_DATATYPE_BITFIELD32) {
                t->fs[i].size = 4;
            } else if (t->fs[i].dataType == DB_DATATYPE_BITFIELD64) {
                t->fs[i].size = 8;
            } else if (t->fs[i].dataType == DB_DATATYPE_BITMAP) {
                t->fs[i].size -=
                    64;  // property->size += sizeof(uint32_t) * BYTE_LENGTH + sizeof(uint32_t) * BYTE_LENGTH;
            }
            t->fullLen += prop[k].size;
            ++i;
        }
        t->fieldNum = i;
        t->pkIdx = (DmVlIndexLabelT *)malloc(sizeof(DmVlIndexLabelT));
        if (t->pkIdx == NULL) {
            printf("[genMetaDataVertex] malloc memory(%ld) for t->pkIdx failed.\n", sizeof(DmVlIndexLabelT));
            break;
        }
        DmVlIndexLabelT *pk = MEMBER_PTR(dvl->metaVertexLabel, pkIndex);
        *t->pkIdx = *pk;
        orgName = MEMBER_PTR(pk, indexName);
        colName = getSmallMemAddr(t, strlen(orgName));
        if (colName == NULL) {
            break;
        }
        (void)snprintf(colName, strlen(orgName) + 1, "%s", orgName);
        t->pkIdx->indexName = colName;
        t->pkIdx->properties = (DmPropertySchemaT *)malloc(sizeof(DmPropertySchemaT) * t->pkIdx->propeNum);
        if (t->pkIdx->properties == NULL) {
            printf("[genMetaDataVertex] malloc memory(%ld) for t->pkIdx->properties failed.\n",
                sizeof(DmPropertySchemaT) * t->pkIdx->propeNum);
            break;
        }
        t->keyLen = 0;
        uint32_t *propIds = MEMBER_PTR(pk, propIds);
        for (i = 0; i < t->pkIdx->propeNum; ++i) {
            memcpy(t->pkIdx->properties + i, t->fs + propIds[i], sizeof(DmPropertySchemaT));
            t->keyLen += t->pkIdx->properties[i].size;
        }
        t->secIdxNum = dvl->metaVertexLabel->secIndexNum;
        if (t->secIdxNum > 0) {
            t->secIdx = (DmVlIndexLabelT *)malloc(sizeof(DmVlIndexLabelT) * t->secIdxNum);
            if (t->secIdx == NULL) {
                printf("[genMetaDataVertex] malloc memory(%ld) for t->secIdx failed.\n",
                    sizeof(DmVlIndexLabelT) * t->secIdxNum);
                break;
            }
            t->secIdxLen = (int *)malloc(sizeof(int) * t->secIdxNum * 2);
            if (t->secIdxLen == NULL) {
                printf("[genMetaDataVertex] malloc memory(%ld) for t->secIdxLen failed.\n",
                    sizeof(int) * t->secIdxNum);
                break;
            }
            t->secIsUnique = t->secIdxLen + t->secIdxNum;
            for (i = 0; i < t->secIdxNum; ++i) {
                pk = MEMBER_PTR(dvl->metaVertexLabel, secIndexes) + i;
                t->secIdx[i] = *pk;
                orgName = MEMBER_PTR(pk, indexName);
                colName = getSmallMemAddr(t, strlen(orgName));
                if (colName == NULL) {
                    break;
                }
                (void)snprintf(colName, strlen(orgName) + 1, "%s", orgName);
                t->secIdx[i].indexName = colName;
                t->secIdx[i].properties =
                    (DmPropertySchemaT *)malloc(sizeof(DmPropertySchemaT) * t->secIdx[i].propeNum);
                if (t->secIdx[i].properties == NULL) {
                    printf("[genMetaDataVertex] malloc memory(%ld) for t->secIdx[i].properties failed.\n",
                        sizeof(DmPropertySchemaT) * t->secIdx[i].propeNum);
                    break;
                }
                t->secIsUnique[i] = (pk->idxLabelBase.indexConstraint == UNIQUE);
                t->secIdxLen[i] = 0;
                propIds = MEMBER_PTR(pk, propIds);
                for (j = 0; j < t->secIdx[i].propeNum; ++j) {
                    memcpy(t->secIdx[i].properties + j, t->fs + propIds[j], sizeof(DmPropertySchemaT));
                    t->secIdxLen[i] += t->secIdx[i].properties[j].size;
                }
            }
        }
        t->subTabNum = ds->nodeNum;
        if (t->subTabNum > 0) {
            t->subTabAddr = (subTabDef *)malloc(sizeof(subTabDef) * t->subTabNum);
            if (t->subTabAddr == NULL) {
                break;
            }
            memset(t->subTabAddr, 0, sizeof(subTabDef) * t->subTabNum);
            DmNodeSchemaT *nodes = MEMBER_PTR(ds, nodes);
            for (i = 0; i < t->subTabNum; ++i) {
                generateSubTable(t, t->subTabAddr + i, nodes + i);
            }
        }
        d = t;
    } while (0);
    if (d == NULL) {
        releaseMetaDataVertex(t);
        pthread_mutex_unlock(&g_memadataLock);
        return NULL;
    }
    addMetaDataVertex(t, g_metaVertexNum);
    DmVlIndexLabelT *x;
    for (i = 0; i < t->fieldNum; ++i) {
        t->fs[i].nameLen = 0;
        x = t->pkIdx;
        for (j = 0; j < x->propeNum; ++j) {
            if (0 == strcmp(t->fs[i].name, x->properties[j].name)) {
                t->fs[i].nameLen = 1;
                break;
            }
        }
        if (t->fs[i].nameLen > 0) {
            continue;
        }
        for (j = 0; j < t->secIdxNum; ++j) {
            x = t->secIdx + j;
            for (int k = 0; k < x->propeNum; ++k) {
                if (0 == strcmp(t->fs[i].name, x->properties[k].name)) {
                    if (t->secIsUnique[j] == 1) {
                        t->fs[i].nameLen = 2;
                    } else {
                        t->fs[i].nameLen = 3;
                    }
                    break;
                }
            }
        }
    }
    pthread_mutex_unlock(&g_memadataLock);
    return d;
}

void setRangeDataIntoBuff(void *value, DbDataTypeE dataType, uint8_t varType, uint64_t base, int v)
{
    uint64_t setV;
    if (v >= 0) {
        if (varType == 1) {
            setV = (uint64_t)v - base;
        } else {
            setV = base + v;
        }
    } else {
        setV = base;
    }
    switch (dataType) {
        case DB_DATATYPE_UINT32:
            *(uint32_t *)value = setV;
            break;
        case DB_DATATYPE_UINT64:
            *(uint64_t *)value = setV;
            break;
        case DB_DATATYPE_UINT16:
            *(uint16_t *)value = setV;
            break;
        default:
            *(uint8_t *)value = setV;
    }
}

void attachVertexIndexCtx(perfContent *p, runUnit *r)
{
    metaDataLabelVertex *t = (metaDataLabelVertex *)r->metadataT;
    if (t == NULL) {
        return;
    }
    if (r->idxName[0] == '\0' || 0 == strcmp(r->idxName, t->pkIdx->indexName)) {
        r->metadataI = t->pkIdx;
        return;
    }
    int i;
    for (i = 0; i < t->secIdxNum; ++i) {
        if (0 == strcmp(r->idxName, t->secIdx[i].indexName)) {
            r->metadataI = t->secIdx + i;
            break;
        }
    }
    if (r->metadataI == NULL) {
        printf("[tid:%02d] [attachVertexIndexCtx] get index(%s) from metadata failed.\n", p->id, r->idxName);
        exit(1);
    }
    DmVlIndexLabelT *x = (DmVlIndexLabelT *)r->metadataI;
    if (x->idxLabelBase.indexType != ART_INDEX_LOCAL || r->range == NULL) {
        return;
    }

    if (r->range->n != x->propeNum) {
        printf("[tid:%02d] [attachVertexIndexCtx] size of index column: actual:%u, your input:%d.\n", p->id,
            x->propeNum, r->range->n);
        if (r->range->n < x->propeNum) {
            exit(1);
        }
        r->range->n = x->propeNum;
    }
    GmcRangeItemT *pr = r->range->rangeItem + p->offset * r->range->n;
    for (i = 0; i < r->range->n; ++i) {
        if (r->range->lFlag != 2) {
            snprintf(pr[i].lValue->propertyName, sizeof(pr[i].lValue->propertyName), "%s", x->properties[i].name);
            pr[i].lValue->type = (GmcDataTypeE)x->properties[i].dataType;
            pr[i].lValue->size = (GmcDataTypeE)x->properties[i].size;
            setRangeDataIntoBuff(
                pr[i].lValue->value, x->properties[i].dataType, r->range->varField[i], r->range->valField[i], -1);
        }
        if (r->range->rFlag != 2) {
            snprintf(pr[i].rValue->propertyName, sizeof(pr[i].rValue->propertyName), "%s", x->properties[i].name);
            pr[i].rValue->type = (GmcDataTypeE)x->properties[i].dataType;
            pr[i].rValue->size = (GmcDataTypeE)x->properties[i].size;
            setRangeDataIntoBuff(pr[i].rValue->value, x->properties[i].dataType, r->range->varField[i + r->range->n],
                r->range->valField[i + r->range->n], -1);
        }
    }
}

metaDataLabelEdge *generateMetaDataEdge(char *edgeName, void *vLabel)
{
    int i, j, reUseId = -1;
    ;
    pthread_mutex_lock(&g_memadataLock);
    for (i = 0; i < g_metaEdgeNum; ++i) {
        if (g_metaEdgeEntry[i] == NULL) {
            if (reUseId == -1) {
                reUseId = i;
            }
            continue;
        }
        if (0 == strcmp(edgeName, g_metaEdgeEntry[i]->edgeName)) {
            pthread_mutex_unlock(&g_memadataLock);
            return g_metaEdgeEntry[i];
        }
    }
    metaDataLabelEdge *e = (metaDataLabelEdge *)malloc(sizeof(metaDataLabelEdge));
    if (e == NULL) {
        printf("[generateMetaDataEdge] malloc memory(%ld) for metaDataLabelEdge failed.\n", sizeof(metaDataLabelEdge));
        pthread_mutex_unlock(&g_memadataLock);
        return NULL;
    }
    DmEdgeLabelT *del = (DmEdgeLabelT *)vLabel;
    e->metaId = del->metaCommon.metaId;
    snprintf(e->edgeName, sizeof(e->edgeName), "%s", edgeName);
    snprintf(e->sourceName, sizeof(e->sourceName), "%s", del->sourceVertexName);
    snprintf(e->destName, sizeof(e->destName), "%s", del->destVertexName);
    e->si = NULL;
    e->sv = NULL;
    e->di = NULL;
    e->dv = NULL;
    addMetaDataEdge(e, reUseId);
    pthread_mutex_unlock(&g_memadataLock);
    return e;
}

inline void setintoFieldValue(char *addr, fieldDefine *f, int val, bool isFirst = false)
{
    int64_t newVal;
    char *s = addr + f->varOffset;
    if (f->varType == FIELD_VAR_SERIAL) {
        newVal = val + f->varVal;
    } else if (f->varType == FIELD_VAR_RANGE) {
        newVal = val % f->varInc + f->varVal;
    } else if (f->varType == FIELD_VAR_RANDOM) {
        newVal = f->varVal + TEST_RANDOM(g_testCtx[0]->randomSeed) % f->varInc;
    } else {
        newVal = f->varVal;
    }
    switch (f->f->dataType) {
        case DB_DATATYPE_UINT32: {
            *(unsigned int *)s = newVal;
            break;
        }
        case DB_DATATYPE_UINT16: {
            *(unsigned short int *)s = newVal & 0x0FFFF;
            break;
        }
        case DB_DATATYPE_UINT8:
        case DB_DATATYPE_PARTITION: {
            s[0] = newVal & 0x0FF;
            break;
        }
        case DB_DATATYPE_UINT64: {
            *(uint64_t *)s = newVal;
            break;
        }
        case DB_DATATYPE_CHAR: {
            s[0] = 'a' + (newVal & 0x1A);
            break;
        }
        case DB_DATATYPE_UCHAR: {
            s[0] = 'A' + (newVal & 0x1A);
            break;
        }
        case DB_DATATYPE_INT8: {
            s[0] = newVal & 0x7F;
            break;
        }
        case DB_DATATYPE_INT16: {
            *(short int *)s = newVal & 0x7FFF;
            break;
        }
        case DB_DATATYPE_INT32: {
            *(int *)s = newVal;
            break;
        }
        case DB_DATATYPE_INT64: {
            *(int64_t *)s = newVal;
            break;
        }
        case DB_DATATYPE_BOOL: {
            s[0] = newVal & 0x02;
            break;
        }
        case DB_DATATYPE_FLOAT: {
            *(float *)s = 3.1415926 + newVal;
            break;
        }
        case DB_DATATYPE_DOUBLE: {
            *(double *)s = 3.333333333333333 + newVal;
            break;
        }
        case DB_DATATYPE_TIME: {
            *(int64_t *)s = newVal;
            break;
        }
        case DB_DATATYPE_FIXED: {
            int i, len;
            if (isFirst == false) {
                char serialStr[12];
                snprintf(serialStr, sizeof(serialStr), "%08d", newVal);
                for (i = 0; i < 8; ++i) {
                    if (f->f->size < i + 1) {
                        break;
                    }
                    s[f->f->size - 1 - i] = serialStr[7 - i];
                }
            } else {
                if (f->varPreText) {
                    len = strlen(f->varPreText);
                }
                for (i = 0; i < f->f->size; ++i) {
                    if (f->varPreText == NULL) {
                        s[i] = 'a' + i % 26;
                    } else {
                        s[i] = f->varPreText[i % len];
                    }
                }
                s[i] = '\0';
            }
            break;
        }
        case DB_DATATYPE_STRING:
        case DB_DATATYPE_BYTES:
        case DB_DATATYPE_BITMAP: {
            int i, len, start;
            len = f->varBegin + f->varInc;
            if (isFirst == false) {
                char serialStr[12];
                snprintf(serialStr, sizeof(serialStr), "%08d", newVal);
                start = 0;
                if (f->varPreText) {
                    start = strlen(f->varPreText);
                }
                for (i = 0; i < 8; ++i) {
                    if (start + i >= len) {
                        break;
                    }
                    s[start + i] = serialStr[i];
                }
            } else {
                if (f->varPreText) {
                    start = strlen(f->varPreText);
                }
                for (i = 0; i < len; ++i) {
                    if (f->varPreText == NULL) {
                        s[i] = 'a' + i % 26;
                    } else {
                        s[i] = f->varPreText[i % start];
                    }
                }
                if (f->f->dataType == DB_DATATYPE_STRING) {
                    s[len] = '\0';
                }
            }
            break;
        }
        case DB_DATATYPE_BITFIELD32: {
            *(unsigned int *)s = newVal & f->f->bitfieldOffset;
            break;
        }
        case DB_DATATYPE_BITFIELD16: {
            *(unsigned short int *)s = newVal & f->f->bitfieldOffset;
            break;
        }
        case DB_DATATYPE_BITFIELD8: {
            s[0] = newVal & f->f->bitfieldOffset;
            break;
        }
        case DB_DATATYPE_BITFIELD64: {
            *(uint64_t *)s = newVal & f->f->bitfieldOffset;
            break;
        }
        default: {
            printf("unknow dataType\n");
            assert(0);
            break;
        }
    }
}

inline void setintoTreeField(char *s, DmPropertySchemaT *f, int val)
{
    int64_t newVal = val;
    switch (f->dataType) {
        case DB_DATATYPE_UINT32: {
            *(unsigned int *)s = newVal;
            break;
        }
        case DB_DATATYPE_UINT16: {
            *(unsigned short int *)s = newVal & 0x0FFFF;
            break;
        }
        case DB_DATATYPE_UINT8:
        case DB_DATATYPE_PARTITION: {
            s[0] = newVal & 0x0FF;
            break;
        }
        case DB_DATATYPE_UINT64: {
            *(uint64_t *)s = newVal;
            break;
        }
        case DB_DATATYPE_CHAR: {
            s[0] = 'a' + (newVal & 0x1A);
            break;
        }
        case DB_DATATYPE_UCHAR: {
            s[0] = 'A' + (newVal & 0x1A);
            break;
        }
        case DB_DATATYPE_INT8: {
            s[0] = newVal & 0x7F;
            break;
        }
        case DB_DATATYPE_INT16: {
            *(short int *)s = newVal & 0x7FFF;
            break;
        }
        case DB_DATATYPE_INT32: {
            *(int *)s = newVal;
            break;
        }
        case DB_DATATYPE_INT64: {
            *(int64_t *)s = newVal;
            break;
        }
        case DB_DATATYPE_BOOL: {
            s[0] = newVal & 0x02;
            break;
        }
        case DB_DATATYPE_FLOAT: {
            *(float *)s = 3.1415926 + newVal;
            break;
        }
        case DB_DATATYPE_DOUBLE: {
            *(double *)s = 3.333333333333333 + newVal;
            break;
        }
        case DB_DATATYPE_TIME: {
            *(int64_t *)s = newVal;
            break;
        }
        case DB_DATATYPE_STRING:
        case DB_DATATYPE_FIXED:
        case DB_DATATYPE_BYTES: {
            int i, len;
            char serialStr[12];
            snprintf(serialStr, sizeof(serialStr), "%08d", newVal);
            for (i = 0; i < 8; ++i) {
                s[i] = serialStr[i];
            }
            break;
        }
        case DB_DATATYPE_BITFIELD32: {
            *(unsigned int *)s = newVal & f->bitfieldOffset;
            break;
        }
        case DB_DATATYPE_BITFIELD16: {
            *(unsigned short int *)s = newVal & f->bitfieldOffset;
            break;
        }
        case DB_DATATYPE_BITFIELD8: {
            s[0] = newVal & f->bitfieldOffset;
            break;
        }
        case DB_DATATYPE_BITFIELD64: {
            *(uint64_t *)s = newVal & f->bitfieldOffset;
            break;
        }
        default: {
            printf("unknow dataType\n");
            assert(0);
            break;
        }
    }
}

void printSubTable(int n, subTabDef *s, int offset, int serial)
{
    if (n == 0 || s == NULL) {
        return;
    }
    int i, j;
    char buf1[128];
    char buf2[128];
    for (i = 0; i < offset; ++i) {
        buf1[i] = ' ';
        buf2[i] = ' ';
    }
    buf1[i] = '\0';
    buf2[i] = ' ';
    buf2[i + 1] = ' ';
    buf2[i + 2] = '\0';
    subTabDef *sp;
    DmPropertySchemaT *dp;
    for (i = 0; i < n; ++i) {
        sp = s + i;
        printf("%s%02d subTable:%s, level:%d, fieldNum:%d, subTableNum:%d\n", buf1, serial + i, sp->nodeName, sp->level,
            sp->fieldNum, sp->subTabNum);
        for (j = 0; j < sp->fieldNum; ++j) {
            dp = sp->fs + j;
            printf("%s%02d name:%-24s type:%-6s, size:%u\n", buf2, j, dp->name, GetDataType(dp->dataType), dp->size);
        }
        printSubTable(sp->subTabNum, sp->subTabAddr, offset + 2, sp->fieldNum);
    }
}

void printMetaDataVertex(metaDataLabelVertex *t)
{
    if (t == NULL) {
        printf("printMetaDataVertex is null.\n");
        return;
    }
    printf("metaId = %d, metaName = %s\n", t->metaId, t->metaName);
    printf("field:\n");
    int i, j;
    DmPropertySchemaT *dp;
    for (i = 0; i < t->fieldNum; ++i) {
        dp = t->fs + i;
        printf("  %02d name:%-24s type:%-6s, size:%u\n", i, dp->name, GetDataType(dp->dataType), dp->size);
    }
    printSubTable(t->subTabNum, t->subTabAddr, 2, t->fieldNum);
    DmSuperFieldT *ds;
    DmVlIndexLabelT *di = t->pkIdx;
    printf("key:  name:%s,  fieldList =", di->indexName);
    for (i = 0; i < di->propeNum; ++i) {
        printf(" %s", di->properties[i].name);
    }
    printf("\n");
    for (i = 0; i < t->secIdxNum; ++i) {
        di = t->secIdx + i;
        printf("sec key[%d]:  name:%s,  fieldList =", i, di->indexName);
        for (j = 0; j < di->propeNum; ++j) {
            printf(" %s", di->properties[j].name);
        }
        printf("\n");
    }
}

void printMetaDataEdge(metaDataLabelEdge *e)
{
    if (e == NULL) {
        printf("printMetaDataEdge is null.\n");
        return;
    }
    printf("metaId = %d, metaName = %s, sourceName = %s, destName = %s.\n", e->metaId, e->edgeName, e->sourceName,
        e->destName);
}

void printVertextHead(metaDataLabelVertex *t)
{
    if (g_logLevel == 0 || t == NULL) {
        return;
    }
    for (int i = 0; i < t->fieldNum; ++i) {
        printf("%s|", (t->fs + i)->name);
    }
    printf("\n");
    printf("--------------------------------------------------------------------------------------\n");
}

void printFieldValue(char *s, DbDataTypeE type, int len)
{
    switch (type) {
        case DB_DATATYPE_CHAR:
        case DB_DATATYPE_UCHAR: {
            printf("%c|", s[0]);
            break;
        }
        case DB_DATATYPE_INT8: {
            printf("%d|", s[0]);
            break;
        }
        case DB_DATATYPE_UINT8:
        case DB_DATATYPE_BITFIELD8:
        case DB_DATATYPE_PARTITION: {
            printf("%u|", s[0]);
            break;
        }
        case DB_DATATYPE_INT16: {
            printf("%d|", *(short int *)s);
            break;
        }
        case DB_DATATYPE_UINT16:
        case DB_DATATYPE_BITFIELD16: {
            printf("%u|", *(unsigned short int *)s);
            break;
        }
        case DB_DATATYPE_INT32: {
            printf("%d|", *(int *)s);
            break;
        }
        case DB_DATATYPE_UINT32:
        case DB_DATATYPE_BITFIELD32: {
            printf("%u|", *(unsigned int *)s);
            break;
        }
        case DB_DATATYPE_INT64: {
            printf("%d|", *(int64_t *)s);
            break;
        }
        case DB_DATATYPE_UINT64:
        case DB_DATATYPE_BITFIELD64: {
            printf("%u|", *(uint64_t *)s);
            break;
        }
        case DB_DATATYPE_BOOL: {
            printf("%s|", (*(bool *)s) ? "true" : "false");
            break;
        }
        case DB_DATATYPE_FLOAT: {
            printf("%f|", *(float *)s);
            break;
        }
        case DB_DATATYPE_DOUBLE: {
            printf("%f|", *(double *)s);
            break;
        }
        case DB_DATATYPE_TIME: {
            printf("%ld|", *(int64_t *)s);
            break;
        }
        case DB_DATATYPE_STRING: {
            printf("%d:", len);
            printf("%s|", s);
            break;
        }
        case DB_DATATYPE_FIXED:
        case DB_DATATYPE_BYTES:
        case DB_DATATYPE_BITMAP: {
            printf("%d:", len);
            for (int i = 0; i < len; ++i) {
                printf("%c", s[i]);
            }
            printf("|");
            break;
        }
        default: {
            printf("unknow dataType\n");
            assert(0);
            break;
        }
    }
}

int setKeyValueForEdge(perfContent *p, int id, int val, bool isSource)
{
    /*GmcStmtT *stmt = p->dmlStmt[id];
    metaDataLabelVertex *t;
    if (isSource) {
        t = g_oprAddr[id]->e->s;
    } else {
        t = g_oprAddr[id]->e->d;
    }
    FieldElement *f;
    int i, ret;
    for (i = 0; i < t->pkFieldNum; ++i) {
        f = t->pkFieldEntry[i];
        if (f->isSet == false) {
            continue;
        }
        setintoFieldValue(p->strInput[id], f, val);
        if (isSource) {
            ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, i, (GmcDataTypeE)f->f->dataType, p->strInput[id] +
    f->offsetAll, f->f->size); if (ret != 0) { printf("setKeyValueForEdge.GmcSetEdgeSrcVertexIndexKeyValue failed,
    fieldName:%s, ret = %d\n", f->f->name, ret); return -1;
            }
        } else {
            ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, i, (GmcDataTypeE)f->f->dataType, p->strInput[id] +
    f->offsetAll, f->f->size); if (ret != 0) { printf("setKeyValueForEdge.GmcSetEdgeDstVertexIndexKeyValue failed,
    fieldName:%s, ret = %d\n", f->f->name, ret); return -1;
            }
        }
    }*/
    return 0;
}

int setSimpleVertexAll(perfContent *p, runUnit *r, int val, GmcNodeT *node = NULL)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    GmcStmtT *stmt = r->stmt[p->offset];
    DmAutoIncrPropInfoT *autoCol = &(((metaDataLabelVertex *)r->metadataT)->autoIncrPropInfo);
    char *addr = r->inputV[p->offset];
    fieldDefine *dp1 = (fieldDefine *)r->firstV[p->offset];
    fieldDefine *dp2 = (fieldDefine *)r->varV[p->offset];
    while (dp1 != NULL) {
        setintoFieldValue(addr, dp1, val);
        dp1 = (fieldDefine *)dp1->next;
    }
    int i, j, size, ret;
    GmcBitMapT bitmap = {0, 0, NULL};
    for (i = 0; i < r->varVLen; ++i) {
        dp1 = dp2 + i;
        if (node == NULL && autoCol && autoCol->autoIncrPropId == dp1->f->propeId) {
            continue;
        }
        if (dp1->varType == FIELD_VAR_NULL) {
            if (node == NULL) {
                ret = GmcSetVertexProperty(stmt, dp1->f->name, GMC_DATATYPE_NULL, NULL, dp1->f->size);
            } else {
                ret = GmcNodeSetPropertyByName(node, dp1->f->name, GMC_DATATYPE_NULL, NULL, dp1->f->size);
            }
            continue;
        }
        if (dp1->useCommon == false) {
            if (dp1->f->dataType == DB_DATATYPE_BITMAP) {
                bitmap.endPos = dp1->f->size - 1;
                bitmap.bits = (uint8_t *)(addr + dp1->varOffset);
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &bitmap, sizeof(bitmap));
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &bitmap, sizeof(bitmap));
                }
            } else if (dp1->f->dataType == DB_DATATYPE_STRING || dp1->f->dataType == DB_DATATYPE_BYTES) {
                size = dp1->varBegin;
                if (dp1->varInc > 0) {
                    size = dp1->varBegin + TEST_RANDOM(p->randomSeed) % dp1->varInc;
                }
                if (dp1->f->dataType == DB_DATATYPE_STRING && dp1->varInc > 0) {
                    addr[dp1->varOffset + dp1->varLast] = 'a' + dp1->varLast % 26;
                    addr[dp1->varOffset + size] = '\0';
                    dp1->varLast = size;
                    ret = strlen(addr + dp1->varOffset);
                }
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, size);
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, size);
                }
            } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD32 || dp1->f->dataType == DB_DATATYPE_BITFIELD16 ||
                       dp1->f->dataType == DB_DATATYPE_BITFIELD8 || dp1->f->dataType == DB_DATATYPE_BITFIELD64) {
                if (dp1->f->dataType == DB_DATATYPE_BITFIELD32) {
                    uint32_t v32 = *(uint32_t *)(addr + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v32, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v32, dp1->f->size);
                    }
                } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD64) {
                    uint64_t v64 = *(uint64_t *)(addr + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v64, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v64, dp1->f->size);
                    }
                } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD16) {
                    uint16_t v16 = *(uint16_t *)(addr + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v16, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v16, dp1->f->size);
                    }
                } else {
                    uint8_t v08 = *(uint8_t *)(addr + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v08, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v08, dp1->f->size);
                    }
                }
            } else {
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, dp1->f->size);
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, dp1->f->size);
                }
            }
        } else {
            if (dp1->f->dataType == DB_DATATYPE_BITMAP) {
                bitmap.endPos = dp1->f->size - 1;
                bitmap.bits = (uint8_t *)(g_commFieldVal + dp1->varOffset);
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &bitmap, sizeof(bitmap));
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &bitmap, sizeof(bitmap));
                }
            } else if (dp1->f->dataType == DB_DATATYPE_RESOURCE) {
                for (j = 0; j < r->resNum; ++j) {
                    if (0 != strcmp(dp1->f->name, r->resAddr[j].colName)) {
                        continue;
                    }
                    uint64_t resId;
                    (void)GmcSetPoolIdResource(r->resAddr[j].resId, &resId);
                    (void)GmcSetCountResource(r->resAddr[j].count, &resId);
                    (void)GmcSetStartIdxResource(r->resAddr[j].startIndex + val, &resId);
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(stmt, dp1->f->name, GMC_DATATYPE_RESOURCE, &resId, sizeof(resId));
                    } else {
                        ret =
                            GmcNodeSetPropertyByName(node, dp1->f->name, GMC_DATATYPE_RESOURCE, &resId, sizeof(resId));
                    }
                    break;
                }
            } else if (dp1->f->dataType == DB_DATATYPE_BYTES) {
                size = dp1->varBegin;
                if (dp1->varInc > 0) {
                    size = dp1->varBegin + TEST_RANDOM(p->randomSeed) % dp1->varInc;
                }
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, g_commFieldVal + dp1->varOffset, size);
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, g_commFieldVal + dp1->varOffset, size);
                }
            } else if (dp1->f->dataType == DB_DATATYPE_STRING) {
                size = dp1->varBegin;
                if (dp1->varInc > 0) {
                    size = dp1->varBegin + TEST_RANDOM(p->randomSeed) % dp1->varInc;
                }
                addr[dp1->varOffset + r->lastStrPos[p->offset]] = 'a' + r->lastStrPos[p->offset] % 26;
                addr[dp1->varOffset + size] = '\0';
                r->lastStrPos[p->offset] = size;
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, size);
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, size);
                }
            } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD32 || dp1->f->dataType == DB_DATATYPE_BITFIELD16 ||
                       dp1->f->dataType == DB_DATATYPE_BITFIELD8 || dp1->f->dataType == DB_DATATYPE_BITFIELD64) {
                if (dp1->f->dataType == DB_DATATYPE_BITFIELD32) {
                    uint32_t v32 = *(uint32_t *)(g_commFieldVal + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v32, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v32, dp1->f->size);
                    }
                } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD64) {
                    uint64_t v64 = *(uint64_t *)(g_commFieldVal + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v64, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v64, dp1->f->size);
                    }
                } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD16) {
                    uint16_t v16 = *(uint16_t *)(g_commFieldVal + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v16, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v16, dp1->f->size);
                    }
                } else {
                    uint8_t v08 = *(uint8_t *)(g_commFieldVal + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v08, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v08, dp1->f->size);
                    }
                }
            } else {
                if (node == NULL) {
                    ret = GmcSetVertexProperty(stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType,
                        g_commFieldVal + dp1->varOffset, dp1->f->size);
                } else {
                    ret = GmcNodeSetPropertyByName(node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType,
                        g_commFieldVal + dp1->varOffset, dp1->f->size);
                }
            }
        }
        if (ret != 0) {
            printf("[tid:%02d] setSimpleVertexAll.GmcSetVertexProperty failed, fieldName:%s, value:%d errCode:%d, "
                   "errMsg:%s.\n",
                p->id, dp1->f->name, val, ret, p->errMsg[r->connMode]);
            return -1;
        }
    }
    return 0;
}

int setSimpleVertexInc(perfContent *p, runUnit *r, int val, GmcNodeT *node = NULL)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    GmcStmtT *stmt = r->stmt[p->offset];
    DmAutoIncrPropInfoT *autoCol = &(((metaDataLabelVertex *)r->metadataT)->autoIncrPropInfo);
    char *addr = r->inputV[p->offset];
    fieldDefine *dp1 = (fieldDefine *)r->firstV[p->offset];
    fieldDefine *dp2 = (fieldDefine *)r->varV[p->offset];
    while (dp1 != NULL) {
        setintoFieldValue(addr, dp1, val);
        dp1 = (fieldDefine *)dp1->next;
    }
    int i, j, size, ret;
    GmcBitMapT bitmap = {0, 0, NULL};
    for (i = 0; i < r->varVLen; ++i) {
        dp1 = dp2 + i;
        if (node == NULL && autoCol && autoCol->autoIncrPropId == dp1->f->propeId) {
            continue;
        }
        if (node == NULL && r->oprCmd == DML_VERTEX_MERGE) {
            fieldDefine *dpK = (fieldDefine *)r->varI[p->offset];
            for (j = 0; j < r->varILen; ++j) {
                if (strcmp(dp1->f->name, dpK[j].f->name) == 0) {
                    break;
                }
            }
            if (j < r->varILen) {
                continue;
            }
        }
        if (dp1->varType == FIELD_VAR_NULL) {
            if (node == NULL) {
                ret = GmcSetVertexProperty(stmt, dp1->f->name, GMC_DATATYPE_NULL, NULL, dp1->f->size);
            } else {
                ret = GmcNodeSetPropertyByName(node, dp1->f->name, GMC_DATATYPE_NULL, NULL, dp1->f->size);
            }
            continue;
        }
        if (dp1->useCommon == false) {
            if (dp1->f->dataType == DB_DATATYPE_BITMAP) {
                bitmap.endPos = dp1->f->size - 1;
                bitmap.bits = (uint8_t *)(addr + dp1->varOffset);
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &bitmap, sizeof(bitmap));
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &bitmap, sizeof(bitmap));
                }
            } else if (dp1->f->dataType == DB_DATATYPE_STRING || dp1->f->dataType == DB_DATATYPE_BYTES) {
                size = dp1->varBegin;
                if (dp1->varInc > 0) {
                    size = dp1->varBegin + TEST_RANDOM(p->randomSeed) % dp1->varInc;
                }
                if (dp1->f->dataType == DB_DATATYPE_STRING && dp1->varInc > 0) {
                    addr[dp1->varOffset + dp1->varLast] = 'a' + dp1->varLast % 26;
                    addr[dp1->varOffset + size] = '\0';
                    dp1->varLast = size;
                }
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, size);
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, size);
                }
            } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD32 || dp1->f->dataType == DB_DATATYPE_BITFIELD16 ||
                       dp1->f->dataType == DB_DATATYPE_BITFIELD8 || dp1->f->dataType == DB_DATATYPE_BITFIELD64) {
                if (dp1->f->dataType == DB_DATATYPE_BITFIELD32) {
                    uint32_t v32 = *(uint32_t *)(addr + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v32, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v32, dp1->f->size);
                    }
                } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD64) {
                    uint64_t v64 = *(uint64_t *)(addr + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v64, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v64, dp1->f->size);
                    }
                } else if (dp1->f->dataType == DB_DATATYPE_BITFIELD16) {
                    uint16_t v16 = *(uint16_t *)(addr + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v16, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v16, dp1->f->size);
                    }
                } else {
                    uint8_t v08 = *(uint8_t *)(addr + dp1->varOffset) & dp1->f->bitfieldOffset;
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(
                            stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v08, dp1->f->size);
                    } else {
                        ret = GmcNodeSetPropertyByName(
                            node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, &v08, dp1->f->size);
                    }
                }
            } else {
                if (node == NULL) {
                    ret = GmcSetVertexProperty(
                        stmt, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, dp1->f->size);
                } else {
                    ret = GmcNodeSetPropertyByName(
                        node, dp1->f->name, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, dp1->f->size);
                }
            }
        } else {
            if (dp1->f->dataType == DB_DATATYPE_RESOURCE) {
                for (j = 0; j < r->resNum; ++j) {
                    if (0 != strcmp(dp1->f->name, r->resAddr[j].colName)) {
                        continue;
                    }
                    uint64_t resId;
                    (void)GmcSetPoolIdResource(r->resAddr[j].resId, &resId);
                    (void)GmcSetCountResource(r->resAddr[j].count, &resId);
                    (void)GmcSetStartIdxResource(r->resAddr[j].startIndex + val, &resId);
                    if (node == NULL) {
                        ret = GmcSetVertexProperty(stmt, dp1->f->name, GMC_DATATYPE_RESOURCE, &resId, sizeof(resId));
                    } else {
                        ret =
                            GmcNodeSetPropertyByName(node, dp1->f->name, GMC_DATATYPE_RESOURCE, &resId, sizeof(resId));
                    }
                }
            } else {
                ret = 0;
            }
        }
        if (ret != 0) {
            printf("[tid:%02d] setSimpleVertexInc.GmcSetVertexProperty failed, fieldName:%s, value:%d, errCode:%d, "
                   "errMsg:%s.\n",
                p->id, dp1->f->name, val, ret, p->errMsg[r->connMode]);
            return -1;
        }
    }
    return 0;
}

void extendBufSize(int reqSize, int *orgSize, char **str, bool isReset = true)
{
    int newSize = *orgSize;
    while (newSize < reqSize) {
        if (reqSize > 1000000) {
            newSize = reqSize + 10;
        } else {
            newSize = newSize * 10;
        }
    }
    char *newStr = (char *)malloc(newSize + 1);
    if (newStr == NULL) {
        printf("extendBufSize[from %d to %d] failed.\n", *orgSize, newSize + 1);
        exit(1);
    }
    free(*str);
    *str = newStr;
    *orgSize = newSize;
    if (isReset == false) {
        return;
    }
    for (int i = 0; i < newSize; ++i) {
        newStr[i] = 'a' + i % 26;
    }
    newStr[newSize] = '\0';
}

int setFullVertexTree(perfContent *p, runUnit *r, GmcNodeT *rootNode, int subNum, subTabDef *subAddr, int v)
{
    if (subNum == 0) {
        return 0;
    }
    int i, j, k, ret;
    char val[12];
    subTabDef *s;
    DmPropertySchemaT *f;
    GmcNodeT *node, *subNode;
    int size;
    for (i = 0; i < subNum; ++i) {
        s = subAddr + i;
        ret = GmcNodeGetChild(rootNode, s->nodeName, &node);
        if (ret != 0) {
            printf("[tid:%02d] setFullVertexTree.GmcNodeGetChild failed, nodeName:%s, errCode:%d, errMsg:%s.\n", p->id,
                s->nodeName, ret, p->errMsg[r->connMode]);
            return -1;
        }
        for (j = 0; j < r->vecSize; ++j) {
            if (s->nodeType == DM_NODE_RECORD) {
                subNode = node;
            } else if (s->nodeType == DM_NODE_VECTOR) {
                ret = GmcNodeAppendElement(node, &subNode);
                if (ret != 0) {
                    printf("[tid:%02d] setFullVertexTree.GmcNodeAppendElement failed, nodeName:%s, errCode:%d, "
                           "errMsg:%s.\n",
                        p->id, s->nodeName, ret, p->errMsg[r->connMode]);
                    return -1;
                }
            } else {
                ret = GmcNodeGetElementByIndex(node, j, &subNode);
                if (ret != 0) {
                    printf("[tid:%02d] setFullVertexTree.GmcNodeGetElementByIndex failed, nodeName:%s, errCode:%d, "
                           "errMsg:%s.\n",
                        p->id, s->nodeName, ret, p->errMsg[r->connMode]);
                    return -1;
                }
            }
            for (k = 0; k < s->fieldNum; ++k) {
                f = s->fs + k;
                if ((GmcDataTypeE)f->dataType == GMC_DATATYPE_FIXED) {
                    if (f->size > p->inSize) {
                        extendBufSize(f->size, &p->inSize, &p->inStr);
                    }
                    size = f->size;
                    if (size >= 8) {
                        setintoTreeField(p->inStr, f, v + j * 100 + k);
                    }
                    ret = GmcNodeSetPropertyByName(subNode, f->name, (GmcDataTypeE)f->dataType, p->inStr, size);
                } else if ((GmcDataTypeE)f->dataType == GMC_DATATYPE_STRING ||
                           (GmcDataTypeE)f->dataType == GMC_DATATYPE_BYTES) {
                    if (r->varMinSize == r->varMaxSize) {
                        if (r->varMinSize == 0) {
                            size = 32;
                        } else {
                            size = r->varMaxSize;
                        }
                        if (size > f->size) {
                            size = f->size - 1;
                        }
                    } else {
                        if (r->varMaxSize < f->size) {
                            size = r->varMinSize + TEST_RANDOM(p->randomSeed) % (r->varMaxSize - r->varMinSize);
                        } else {
                            size = TEST_RANDOM(p->randomSeed) % (f->size - 1) + 1;
                        }
                    }
                    if (size >= 8) {
                        setintoTreeField(p->inStr, f, v + j * 100 + k);
                    }
                    if (size > p->inSize) {
                        extendBufSize(size, &p->inSize, &p->inStr);
                    }
                    p->inStr[size] = '\0';
                    ret = GmcNodeSetPropertyByName(subNode, f->name, (GmcDataTypeE)f->dataType, p->inStr, size);
                    p->inStr[size] = 'a' + size % 26;
                } else {
                    setintoTreeField(val, f, v + j * 100 + k);
                    ret = GmcNodeSetPropertyByName(subNode, f->name, (GmcDataTypeE)f->dataType, val, f->size);
                }
                if (ret != 0) {
                    printf("[tid:%02d] setFullVertexTree.GmcNodeSetPropertyByName failed, nodeName:%s, fieldName:%s, "
                           "errCode:%d, errMsg:%s.\n",
                        p->id, s->nodeName, f->name, ret, p->errMsg[r->connMode]);
                    return -1;
                }
            }
            if (v < 10000000) {
                ret = setFullVertexTree(p, r, subNode, s->subTabNum, s->subTabAddr, (v + j) * 10);
            } else {
                ret = setFullVertexTree(p, r, subNode, s->subTabNum, s->subTabAddr, v + j);
            }
            if (s->nodeType == DM_NODE_RECORD) {
                break;
            }
        }
    }
    return 0;
}

int setFullVertexMain(perfContent *p, runUnit *r)
{
    metaDataLabelVertex *t = (metaDataLabelVertex *)r->metadataT;
    if (t->subTabNum == 0) {
        return setSimpleVertexAll(p, r, 0);
    }
    GmcNodeT *rootNode;
    int ret = GmcGetRootNode(r->stmt[p->offset], &rootNode);
    if (ret != 0) {
        printf("[tid:%02d] setFullVertexMain.GmcGetRootNode failed, errCode:%d, errMsg:%s.\n", p->id, ret,
            p->errMsg[r->connMode]);
        return ret;
    }
    ret = setSimpleVertexAll(p, r, 0, rootNode);
    if (ret != 0) {
        printf(
            "[tid:%02d] setFullVertexMain.setSimpleVertexAll failed, ret = %d.\n", p->id, ret, p->errMsg[r->connMode]);
        return ret;
    }
    return setFullVertexTree(p, r, rootNode, t->subTabNum, t->subTabAddr, 1);
}

int setDeltaVertexMain(perfContent *p, runUnit *r)
{
    // delta
    return 0;
}

int setVertexKeyRange(perfContent *p, runUnit *r, int val)
{
    int i, ret;
    DmVlIndexLabelT *x = (DmVlIndexLabelT *)r->metadataI;
    GmcRangeItemT *item = r->range->rangeItem + r->range->n * p->offset;
    for (i = 0; i < r->range->n; ++i) {
        if (r->range->lFlag != 2 && r->range->varField[i] > 0) {
            setRangeDataIntoBuff(
                item[i].lValue->value, x->properties[i].dataType, r->range->varField[i], r->range->valField[i], val);
        }
        if (r->range->rFlag != 2 && r->range->varField[i + r->range->n] > 0) {
            setRangeDataIntoBuff(item[i].rValue->value, x->properties[i].dataType, r->range->varField[i + r->range->n],
                r->range->valField[i + r->range->n], val);
        }
    }
    ret = GmcSetKeyRange(r->stmt[p->offset], item, x->propeNum);
    if (ret != 0) {
        printf("[tid:%02d] setVertexKeyRange.GmcSetKeyRange failed, errCode:%d, errMsg:%s\n", p->id, ret,
            p->errMsg[r->connMode]);
        return -1;
    }
    return 0;
}

int setVertexKeyField(perfContent *p, runUnit *r, int val)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    GmcStmtT *stmt = r->stmt[p->offset];
    DmAutoIncrPropInfoT *autoCol = &(((metaDataLabelVertex *)r->metadataT)->autoIncrPropInfo);
    char *addr = r->inputI[p->offset];
    fieldDefine *dp1 = (fieldDefine *)r->firstI[p->offset];
    fieldDefine *dp2 = (fieldDefine *)r->varI[p->offset];
    while (dp1 != NULL) {
        setintoFieldValue(addr, dp1, val);
        dp1 = (fieldDefine *)dp1->next;
    }
    int i, size, ret;
    for (i = 0; i < r->varILen; ++i) {
        dp1 = dp2 + i;
        if (autoCol && autoCol->autoIncrPropId == dp1->f->propeId) {
            continue;
        }
        if (dp1->varType == FIELD_VAR_NULL) {
            ret = GmcSetIndexKeyValue(stmt, i, GMC_DATATYPE_NULL, addr + dp1->varOffset, dp1->f->size);
            if (ret != 0) {
                printf("[tid:%02d] setVertexKeyField.GmcSetIndexKeyValue(id=%d,null) failed, fieldName:%s, errCode:%d, "
                       "errMsg:%s\n",
                    p->id, i, dp1->f->name, ret, p->errMsg[r->connMode]);
                return ret;
            }
            continue;
        }
        if (dp1->useCommon == false) {
            if (dp1->f->isFixed) {
                ret = GmcSetIndexKeyValue(stmt, i, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, dp1->f->size);
            } else {
                size = dp1->varBegin;
                if (dp1->varInc > 0) {
                    size = dp1->varBegin + TEST_RANDOM(p->randomSeed) % dp1->varInc;
                }
                if (dp1->f->dataType == DB_DATATYPE_STRING && dp1->varInc > 0) {
                    addr[dp1->varOffset + dp1->varLast] = 'a' + dp1->varLast % 26;
                    addr[dp1->varOffset + size] = '\0';
                    dp1->varLast = size;
                }
                ret = GmcSetIndexKeyValue(stmt, i, (GmcDataTypeE)dp1->f->dataType, addr + dp1->varOffset, size);
            }
        } else {
            if (dp1->f->isFixed) {
                ret = GmcSetIndexKeyValue(
                    stmt, i, (GmcDataTypeE)dp1->f->dataType, g_commFieldVal + dp1->varOffset, dp1->f->size);
            } else if (dp1->f->dataType == DB_DATATYPE_BYTES) {
                size = dp1->varBegin;
                if (dp1->varInc > 0) {
                    size = dp1->varBegin + TEST_RANDOM(p->randomSeed) % dp1->varInc;
                }
                ret =
                    GmcSetIndexKeyValue(stmt, i, (GmcDataTypeE)dp1->f->dataType, g_commFieldVal + dp1->varOffset, size);
            } else if (dp1->f->dataType == DB_DATATYPE_STRING) {
                size = dp1->varBegin;
                if (dp1->varInc > 0) {
                    size = dp1->varBegin + TEST_RANDOM(p->randomSeed) % dp1->varInc;
                }
                addr[dp1->varOffset + r->lastStrPos[p->offset]] = 'a' + r->lastStrPos[p->offset] % 26;
                addr[dp1->varOffset + size] = '\0';
                r->lastStrPos[p->offset] = size;
                ret =
                    GmcSetIndexKeyValue(stmt, i, (GmcDataTypeE)dp1->f->dataType, g_commFieldVal + dp1->varOffset, size);
            }
        }
        if (ret != 0) {
            printf("[tid:%02d] setVertexKeyField.GmcSetIndexKeyValue failed, fieldName:%s, errCode:%d, errMsg:%s\n",
                p->id, dp1->f->name, ret, p->errMsg[r->connMode]);
            return ret;
        }
    }
    return 0;
}

int setVertexFilter(perfContent *p, runUnit *r, int val)
{
    char *str = NULL;
    do {
        if (r->flt == NULL) {
            break;
        }
        if (r->flt->n == 0) {
            str = r->flt->str;
            break;
        }
        str = r->flt->filter[p->offset];
        int64_t v1;
        char v2[32];
        for (uint8_t i = 0; i < r->flt->n; ++i) {
            v1 = r->flt->value[i] + val;
            snprintf(v2, sizeof(v2), "%ld", v1);
            uint16_t j = 0;
            while (j < 32 && j < r->flt->length[i] && v2[j] != '\0') {
                str[r->flt->offset[i] + j] = v2[j];
                ++j;
            }
            for (; j < r->flt->length[i]; ++j) {
                str[r->flt->offset[i] + j] = ' ';
            }
        }
    } while (0);
    int ret = GmcSetFilter(r->stmt[p->offset], str);
    if (ret != 0) {
        printf("[tid:%02d] setVertexFilter.GmcSetFilter failed, filter:%s, errCode:%d, errMsg:%s\n", p->id, str, ret,
            p->errMsg[r->connMode]);
        return ret;
    }
    return 0;
}

void perfDumpVertex(perfContent *p, GmcStmtT *stmt, metaDataLabelVertex *t = NULL)
{
    if (g_logLevel == 0) {
        return;
    }
    int i, ret;
    if (g_logLevel == 1 || t == NULL) {
        char *vertexStr;
        int ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &vertexStr);
        if (ret != 0) {
            printf("[tid:%02d] [perfDumpVertex] GmcDumpVertexToJson failed, ret = %d.\n", p->id, ret);
        } else {
            ret = strlen(vertexStr);
            for (i = 0; i < ret; ++i) {
                printf("%c", vertexStr[i]);
                if (vertexStr[i] == ',') {
                    printf("\n");
                }
            }
            printf("\n");
            GmcFreeJsonStr(stmt, vertexStr);
        }
        return;
    }
    if (t == NULL) {
        printf("[tid:%02d] [perfDumpVertex] metadata is null, parse vertex failed.\n", p->id);
        return;
    }
    // get rootNode only
    uint32_t size;
    DmPropertySchemaT *f;
    bool isNull;
    for (i = 0; i < t->fieldNum; ++i) {
        f = t->fs + i;
        ret = GmcGetVertexPropertySizeByName(stmt, f->name, &size);
        if (ret != 0) {
            printf("[tid:%02d] [perfDumpVertex] GmcGetVertexPropertySizeByName failed, fieldName:%s, errCode:%d.\n",
                p->id, f->name, ret);
            break;
        }
        if (size > p->outSize) {
            extendBufSize(size, &p->outSize, &p->outStr, false);
        }
        isNull = false;
        memset(p->outStr, 0, size + 1);
        ret = GmcGetVertexPropertyByName(stmt, f->name, p->outStr, size, &isNull);
        if (ret != 0) {
            printf("\ngetFullVertexObject.GmcGetVertexPropertyByName failed, fieldName:%s, fieldSize:%u, errCode:%d.\n",
                f->name, size, ret);
            break;
        }
        if (isNull) {
            printf("null|");
        } else {
            if (f->isResource == false) {
                p->outStr[size] = '\0';
                printFieldValue(p->outStr, f->dataType, size);
            } else {
                uint16_t tmpPoolId, tmpCount;
                uint32_t tmpStartIndex;
                uint64_t tmpResId = *(uint64_t *)p->outStr;
                (void)GmcGetPoolIdResource(tmpResId, &tmpPoolId);
                (void)GmcGetCountResource(tmpResId, &tmpCount);
                (void)GmcGetStartIdxResource(tmpResId, &tmpStartIndex);
                printf("%u-%u-%u|", tmpPoolId, tmpCount, tmpStartIndex);
            }
        }
    }
    printf("\n");
}

void getDataArea(runUnit *r, int tid, int *t1, int *t2)
{
    if (t1 == NULL || t2 == NULL) {
        return;
    }
    if (r == NULL || tid < 0 || tid >= g_threadCount) {
        *t1 = 0;
        *t2 = 1;
        return;
    }
    if (r->isolation == 1 || r->isolation == 3) {
        *t1 = r->beginCycle;
        *t2 = r->endCycle;
        return;
    }
    perfContent *p = g_testCtx[tid];
    sceneEntry *s = g_sceneEntry + p->sceneId;
    int n = r->endCycle - r->beginCycle;
    if (n >= s->threadCount) {
        n = n / s->threadCount;
        *t1 = r->beginCycle + n * (tid - s->startTid);
        *t2 = r->beginCycle + n * (tid - s->startTid + 1);
        if (tid - s->startTid + 1 == s->threadCount) {
            *t2 = r->endCycle;
        }
        return;
    }
    *t1 = p->id;
    *t2 = p->id + 1;
    if (p->id >= n) {
        *t2 = p->id;
    }
}

asyncContex *newAsyncContex(perfContent *p, runUnit *r, int v)
{
    asyncCtrl *acl = r->acl[p->offset];
    asyncContex *acx = r->acx[p->offset] + acl->pos;
    uint64_t t = getCyclesUS();
    while (acx->used) {
        if (t - acx->t >= 3000000) {
            printf("[tid:%02d] newAsyncContex timeout(3s), vertexName:%s, oprCmd:%d, value:%d.\n", p->id, r->metaName,
                r->oprCmd, acx->v);
            break;
        }
        usleep(200);
        t = getCyclesUS();
    }
    acx->v = v;
    acx->t = t;
    ++acl->pos;
    if (acl->pos >= r->asyncSize) {
        acl->pos = 0;
    }
    acx->p = p;
    acx->r = r;
    ++p->requestNum;
    acx->used = true;
    return acx;
}
void resetAsyncContex(asyncContex *acx)
{
    acx->used = false;
    perfContent *p = (perfContent *)acx->p;
    ++p->repondNum;
}
void freeAsyncContex(asyncContex *acx)
{
    perfContent *p = (perfContent *)acx->p;
    runUnit *r = (runUnit *)acx->r;
    ++p->repondNum;
    if (acx->used) {
        asyncCtrl *acl = r->acl[p->offset];
        uint64_t t = getCyclesUS();
        if (t <= acx->t) {
            printf("[tid:%02d] freeAsyncContex, startTime:%lu >= endTime:%lu.\n", p->id, acx->t, t);
        } else {
            acl->sum += (t - acx->t);
            ++acl->count;
        }
        acx->used = false;
    } else {
        printf("[tid:%02d] freeAsyncContex, disorder msg, vertexName:%s, oprCmd:%d, value:%d.\n", p->id, r->metaName,
            r->oprCmd, acx->v);
    }
}

void AsyncFunCreateNameSpace(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [mgrNameSpace] GmcCreateNamespaceAsync(%s%05d) failed, status = %d|%s.\n", p->id, g_strText,
            acx->v, status, errMsg);
    } else {
        printf("[tid:%02d] [mgrNameSpace] create %s%05d successful.\n", p->id, g_strText, acx->v);
    }
}
void AsyncFunDropNameSpace(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [mgrNameSpace] GmcDropNamespaceAsync(%s%05d) failed, status = %d|%s.\n", p->id, g_strText,
            acx->v, status, errMsg);
    } else {
        printf("[tid:%02d] [mgrNameSpace] drop %s%05d successful.\n", p->id, g_strText, acx->v);
    }
}

void AsyncFunDropKv(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [mgrLabelKV] GmcDropKvTableAsync(%s%05d) failed, status = %d|%s.\n", p->id, r->metaName,
            acx->v, status, errMsg);
    } else {
        printf("[tid:%02d] [mgrLabelKV] drop %s%05d successful.\n", p->id, r->metaName, acx->v);
    }
}
void AsyncFunTruncateKv(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [mgrLabelKV] GmcTruncateKvTableAsync(%s%05d) failed, status = %d|%s.\n", p->id, r->metaName,
            acx->v, status, errMsg);
    } else {
        printf("[tid:%02d] [mgrLabelKV] truncate %s%05d successful.\n", p->id, r->metaName, acx->v);
    }
}
void AsyncFunCreateKv(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [mgrLabelKV] GmcCreateKvTableAsync(%s%05d) failed, status = %d|%s.\n", p->id, r->metaName,
            acx->v, status, errMsg);
    } else {
        printf("[tid:%02d] [mgrLabelKV] create %s%05d successful.\n", p->id, r->metaName, acx->v);
    }
}

void AsyncFunDropVertex(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [mgrLabelVertex] GmcDropVertexLabelAsync(%s%05d) failed, status = %d|%s.\n", p->id,
            r->metaName, acx->v, status, errMsg);
    } else {
        printf("[tid:%02d] [mgrLabelVertex] drop %s%05d successful.\n", p->id, r->metaName, acx->v);
    }
}
void AsyncFunTruncateVertex(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [mgrLabelVertex] GmcTruncateVertexLabelAsync(%s%05d) failed, status = %d|%s.\n", p->id,
            r->metaName, acx->v, status, errMsg);
    } else {
        printf("[tid:%02d] [mgrLabelVertex] truncate %s%05d successful.\n", p->id, r->metaName, acx->v);
    }
}
void AsyncFunOpenVertex(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0 && status != GMERR_DUPLICATE_TABLE) {
        printf("[tid:%02d] [mgrLabelVertex] GmcStoreVertexLabelToCacheAsync(%s) failed, status = %d|%s.\n", p->id,
            r->metaName, status, errMsg);
    }
    acx->v = status;
}
void AsyncFunCreateVertex(void *userData, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [mgrLabelVertex] GmcCreateVertexLabelAsync(%s%05d) failed, status = %d|%s.\n", p->id,
            r->metaName, acx->v, status, errMsg);
    } else {
        printf("[tid:%02d] [mgrLabelVertex] create %s%05d successful.\n", p->id, r->metaName, acx->v);
    }
}

void AsyncFunVertexBatchExe(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status != 0) {
        printf("[tid:%02d] [exeDMLVertex] AsyncFunVertexBatchExe failed, tableName:%s, value:%d, status = %d|%s.\n",
            p->id, r->metaName, acx->v, status, errMsg);
        p->countReq[0] += 1;
        p->countFail[0] += 1;
        return;
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    int ret = GmcBatchDeparseRet(batchRet, &totalNum, &successNum);
    if (ret != 0) {
        printf("[tid:%02d] [exeDMLVertex] AsyncFunVertexBatchExe.GmcBatchDeparseRet failed, tableName:%s, value:%d, "
               "ret = %d|%s.\n",
            p->id, r->metaName, acx->v, status, errMsg);
        p->countReq[0] += 1;
        p->countFail[0] += 1;
        return;
    }
    p->countReq[0] += totalNum;
    p->countSucc[0] += successNum;
    p->countFail[0] += (totalNum - successNum);
}
void AsyncFunVertexReplace(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status == 0) {
        if (affectedRows == 0) {
            ++p->countLost[0];
        } else {
            p->countSucc[0] += affectedRows;
        }
    } else {
        ++p->countFail[0];
        printf("[tid:%02d] [exeDMLVertex] GmcReplaceVertexAsync failed, tableName:%s, value:%d, status = %d|%s.\n",
            p->id, r->metaName, acx->v, status, errMsg);
    }
}
void AsyncFunVertexInsert(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status == 0) {
        if (affectedRows == 0) {
            ++p->countLost[0];
        } else {
            p->countSucc[0] += affectedRows;
        }
    } else {
        ++p->countFail[0];
        printf("[tid:%02d] [exeDMLVertex] GmcInsertVertexAsync failed, tableName:%s, value:%d, status = %d|%s.\n",
            p->id, r->metaName, acx->v, status, errMsg);
    }
}
void AsyncFunVertexInsertWithRes(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status == 0) {
        if (affectedRows == 0) {
            ++p->countLost[0];
        } else {
            p->countSucc[0] += affectedRows;
        }
    } else {
        ++p->countFail[0];
        printf(
            "[tid:%02d] [exeDMLVertex] GmcInsertVertexAsyncWithRes failed, tableName:%s, value:%d, status = %d|%s.\n",
            p->id, r->metaName, acx->v, status, errMsg);
    }
}
void AsyncFunVertexMerge(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status == 0) {
        if (affectedRows == 0) {
            ++p->countLost[0];
        } else {
            p->countSucc[0] += affectedRows;
        }
    } else if (status == GMERR_UNIQUE_VIOLATION) {
        ++p->countLost[0];
    } else {
        ++p->countFail[0];
        printf("[tid:%02d] [exeDMLVertex] GmcMergeVertexAsync failed, tableName:%s, value:%d, status = %d|%s.\n", p->id,
            r->metaName, acx->v, status, errMsg);
    }
}
void AsyncFunVertexUpdate(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status == 0) {
        if (affectedRows == 0) {
            ++p->countLost[0];
        } else {
            p->countSucc[0] += affectedRows;
        }
    } else {
        ++p->countFail[0];
        printf("[tid:%02d] [exeDMLVertex] GmcUpdateVertexByIndexKeyAsync failed, tableName:%s, value:%d, status = "
               "%d|%s.\n",
            p->id, r->metaName, acx->v, status, errMsg);
    }
}
void AsyncFunVertexDelete(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    if (status == 0) {
        if (affectedRows == 0) {
            ++p->countLost[0];
        } else {
            p->countSucc[0] += affectedRows;
        }
    } else {
        ++p->countFail[0];
        printf("[tid:%02d] [exeDMLVertex] GmcDeleteVertexByIndexKeyAsync failed, tableName:%s, value:%d, status = "
               "%d|%s.\n",
            p->id, r->metaName, acx->v, status, errMsg);
    }
}

void AsyncFunKvSet(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    ++p->countSucc[0];
    if (status != 0) {
        --p->countSucc[0];
        ++p->countFail[0];
        printf("[tid:%02d] [exeDMLKV] GmcKvSetAsync failed, tableName:%s, value:%d, status = %d|%s.\n", p->id,
            r->metaName, acx->v, status, errMsg);
    }
}
void AsyncFunKvDel(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    asyncContex *acx = (asyncContex *)userData;
    freeAsyncContex(acx);
    runUnit *r = (runUnit *)acx->r;
    perfContent *p = (perfContent *)acx->p;
    ++p->countSucc[0];
    if (status != 0) {
        --p->countSucc[0];
        ++p->countFail[0];
        printf("[tid:%02d] [exeDMLKV] GmcKvDelete failed, tableName:%s, value:%d, status = %d|%s.\n", p->id,
            r->metaName, acx->v, status, errMsg);
    }
}

void snPrintVertext(GmcStmtT *stmt, const GmcSubMsgInfoT *info, perfContent *p, int id)
{
    // info->msgType  0:path,  1:old object,  2:new object,  3:old object + new object,  4:key,  5:key + old object,
    // 6:key + new object,  7:key + old object + new object
    if (info->msgType < 1 || info->msgType > 7) {
        printf("[tid:%02d] [snPrintVertext] invalid msgType:%u, serial = %d|%u.\n", p->id, info->msgType, id,
            info->labelCount);
        return;
    }
    unsigned int metaNameLen = DM_MAX_NAME_LENGTH;
    char metaName[DM_MAX_NAME_LENGTH];
    memset(metaName, 0, DM_MAX_NAME_LENGTH);
    int ret = GmcSubGetLabelName(stmt, id, metaName, &metaNameLen);
    if (ret != 0) {
        printf("[tid:%02d] [snPrintVertext] GmcSubGetLabelName failed, serial = %d|%u, ret = %d.\n", p->id, id,
            info->labelCount, ret);
        return;
    }
    uint32_t msgType;
    ret = GmcSubGetMsgType(stmt, &msgType);
    if (ret != 0) {
        printf("[tid:%02d] [snPrintVertext] GmcSubGetMsgType failed, serial = %d|%u, ret = %d.\n", p->id, id,
            info->labelCount, ret);
        return;
    }
    char *kvKey;
    char *kvVal;
    uint32_t lenKey, lenVal;
    if (info->msgType == 1 || info->msgType == 3 || info->msgType == 5 || info->msgType == 7) {
        if (msgType & GMC_SUB_MSG_OLD_DATA) {
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            if (ret != 0) {
                printf("[tid:%02d] [snPrintVertext] GmcSubSetFetchMode failed, serial = %d|%u, ret = %d.\n", p->id, id,
                    info->labelCount, ret);
                return;
            }
            if (info->eventType == GMC_SUB_EVENT_KV_SET || info->eventType == GMC_SUB_EVENT_KV_REMOVE) {
                ret = GmcKvGetFromStmt(stmt, (void **)&kvKey, &lenKey, (void **)&kvVal, &lenVal);
                if (ret == 0) {
                    printf("[tid:%02d] [kv] key:%s, val:%s.\n", p->id, kvKey, kvVal);
                } else if (ret == GMERR_WRONG_STMT_OBJECT) {  // vertex obj
                    perfDumpVertex(p, stmt);
                } else {
                    printf("[tid:%02d] [snPrintVertext] GmcGetKvFromStmt(old) failed, serial = %d|%u, ret = %d.\n",
                        p->id, id, info->labelCount, ret);
                }
            } else {
                perfDumpVertex(p, stmt);
            }
        } else {
            if (g_logLevel > 0) {
                printf("[tid:%02d] [snPrintVertext] old data is null, info->msgType = %u, stmt->msgType=%u.\n", p->id,
                    info->msgType, msgType);
            }
        }
    }
    if (info->msgType == 2 || info->msgType == 3 || info->msgType == 6 || info->msgType == 7) {
        if (msgType & GMC_SUB_MSG_NEW_DATA) {
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            if (ret != 0) {
                printf("[tid:%02d] [snPrintVertext] GmcSubSetFetchMode(new) failed, serial = %d|%u, ret = %d.\n", p->id,
                    id, info->labelCount, ret);
                return;
            }
            if (info->eventType == GMC_SUB_EVENT_KV_SET) {
                ret = GmcKvGetFromStmt(stmt, (void **)&kvKey, &lenKey, (void **)&kvVal, &lenVal);
                if (ret != 0) {
                    printf("[tid:%02d] [snPrintVertext] GmcGetKvFromStmt failed, serial = %d|%u, ret = %d.\n", p->id,
                        id, info->labelCount, ret);
                } else {
                    printf("[tid:%02d] [kv] key:%s, val:%s.\n", kvKey, kvVal);
                }
            } else {
                perfDumpVertex(p, stmt);
            }
        } else {
            if (g_logLevel > 0) {
                printf("[tid:%02d] [snPrintVertext] new data is null, info->msgType = %u, stmt->msgType=%u.\n", p->id,
                    info->msgType, msgType);
            }
        }
    }
    if (info->msgType == 4 || info->msgType == 5 || info->msgType == 6 || info->msgType == 7) {
        if (g_logLevel > 0) {
            if (msgType & GMC_SUB_MSG_KEY_DATA) {
                printf("[tid:%02d] [snPrintVertext] msgType:%u, eventType:%u, this is key, serial = %d|%u.\n", p->id,
                    info->msgType, info->eventType, id, info->labelCount);
            } else {
                printf("[tid:%02d] [snPrintVertext] key is null, info->msgType = %u, stmt->msgType=%u.\n", p->id,
                    info->msgType, msgType);
            }
        }
    }
}

void snAsyncFunTable(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    if (userData == NULL) {
        assert(0);
    }
    snContex *acx = (snContex *)userData;
    snObject *s = acx->s;
    perfContent *p = acx->p;
    ++p->snCount;
    if (p->snCount > 200000000) {
        p->snCount = 1;
    }
    switch (info->eventType) {
        case GMC_SUB_EVENT_INSERT: {
            ++p->countSnI[0];
            break;
        }
        case GMC_SUB_EVENT_KV_REMOVE: {  // GMC_SUB_EVENT_DELETE = GMC_SUB_EVENT_KV_REMOVE
            ++p->countSnD[0];
            break;
        }
        case GMC_SUB_EVENT_UPDATE: {
            ++p->countSnU[0];
            break;
        }
        case GMC_SUB_EVENT_REPLACE:
        case GMC_SUB_EVENT_KV_SET: {
            ++p->countSnR1[0];
            break;
        }
        case GMC_SUB_EVENT_REPLACE_INSERT: {
            ++p->countSnR2[0];
            break;
        }
        case GMC_SUB_EVENT_REPLACE_UPDATE: {
            ++p->countSnR3[0];
            break;
        }
        case GMC_SUB_EVENT_MERGE: {
            ++p->countSnM1[0];
            break;
        }
        case GMC_SUB_EVENT_MERGE_INSERT: {
            ++p->countSnM2[0];
            break;
        }
        case GMC_SUB_EVENT_MERGE_UPDATE: {
            ++p->countSnM3[0];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            ++p->countSnLoad[0];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            return;
        }
        case GMC_SUB_EVENT_AGED: {
            ++p->countSnAged[0];
            break;
        }
        default: {
            printf("[tid:%02d] [snAsyncFunTable] invalid eventType:%u.\n", p->id, info->eventType);
            break;
        }
    }
    if (g_snLatency && p->sn && p->sn->latency) {
        if (p->sn->consumeId >= p->sn->productId) {
            printf("[tid:%02d] [snAsyncFunTable] dismatch msg, p->sn->productId=%d, p->sn->consumeId=%d.\n", p->id,
                p->sn->productId, p->sn->consumeId);
        } else {
            if (p->sn->latency[p->sn->consumeId] == 0) {
                printf("[tid:%02d] [snAsyncFunTable] p->sn->latency[%d] = 0, product is not marker the start_time.\n",
                    p->id, p->sn->consumeId);
                assert(0);
            } else {
                uint64_t t = getCyclesUS();
                if (t <= p->sn->latency[p->sn->consumeId]) {
                    printf("[tid:%02d] [snAsyncFunTable] startTime:%lu >= endTime:%d.\n", p->id,
                        p->sn->latency[p->sn->consumeId], t);
                } else {
                    t -= p->sn->latency[p->sn->consumeId];
                    p->sn->sum += t;
                    if (t > p->sn->vMax) {
                        p->sn->vMax = t;
                    }
                    if (t < p->sn->vMin) {
                        p->sn->vMin = t;
                    }
                    ++p->sn->recvNumSucc;
                }
            }
            ++p->sn->consumeId;
        }
    }
    if (g_logLevel == 0) {
        return;
    }
    unsigned int i;
    int ret;
    bool isEnd;
    while (true) {
        ret = GmcFetch(stmt, &isEnd);
        if (ret != 0) {
            printf("[tid:%02d] [snAsyncFunTable] GmcFetch failed, i = %d, ret = %d.\n", p->id, i, ret);
            break;
        }
        if (isEnd) {
            break;
        }
        for (i = 0; i < info->labelCount; ++i) {
            snPrintVertext(stmt, info, p, i);
        }
    }
}

void snAsyncStaTable(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    if (userData == NULL) {
        assert(0);
    }
    perfContent *p = (perfContent *)userData;
    ++p->snCount;
    if (p->snCount > 200000000) {
        p->snCount = 1;
    }
    switch (info->eventType) {
        case GMC_SUB_EVENT_INSERT: {
            ++p->countSnI[0];
            break;
        }
        case GMC_SUB_EVENT_KV_REMOVE: {  // GMC_SUB_EVENT_DELETE = GMC_SUB_EVENT_KV_REMOVE
            ++p->countSnD[0];
            break;
        }
        case GMC_SUB_EVENT_UPDATE: {
            ++p->countSnU[0];
            break;
        }
        case GMC_SUB_EVENT_REPLACE:
        case GMC_SUB_EVENT_KV_SET: {
            ++p->countSnR1[0];
            break;
        }
        case GMC_SUB_EVENT_REPLACE_INSERT: {
            ++p->countSnR2[0];
            break;
        }
        case GMC_SUB_EVENT_REPLACE_UPDATE: {
            ++p->countSnR3[0];
            break;
        }
        case GMC_SUB_EVENT_MERGE: {
            ++p->countSnM1[0];
            break;
        }
        case GMC_SUB_EVENT_MERGE_INSERT: {
            ++p->countSnM2[0];
            break;
        }
        case GMC_SUB_EVENT_MERGE_UPDATE: {
            ++p->countSnM3[0];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {  // initial load
            ++p->countSnLoad[0];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            return;
        }
        case GMC_SUB_EVENT_AGED: {
            ++p->countSnAged[0];
            break;
        }
        default: {
            printf("[tid:%02d] [snAsyncStaTable] invalid eventType:%u.\n", p->id, info->eventType);
            break;
        }
    }
    if (g_logLevel == 0) {
        return;
    }
    unsigned int i;
    int ret;
    bool isEnd;
    while (true) {
        ret = GmcFetch(stmt, &isEnd);
        if (ret != 0) {
            printf("[tid:%02d] [snAsyncStaTable] GmcFetch failed, i = %d, ret = %d.\n", p->id, i, ret);
            break;
        }
        if (isEnd) {
            break;
        }
        for (i = 0; i < info->labelCount; ++i) {
            snPrintVertext(stmt, info, p, i);
        }
    }
}

int mgrNameSpace(perfContent *p, runUnit *r, int v1, int v2 = -1)
{
    if (r->oprCmd != DDL_NAMESPACE_CREATE && r->oprCmd != DDL_NAMESPACE_DROP) {
        printf("[mgrNameSpace] invalid action, r->oprCmd = %d, create/drop is request.\n", r->oprCmd);
        return -1;
    }
    GmcStmtT *stmt = r->stmt[p->offset];
    int v3 = v2;
    if (v3 == -1) {
        v3 = v1 + 1;
    }
    int i, ret;
    char nameSpace[128];
    for (i = v1; i < v3; ++i) {
        snprintf(nameSpace, sizeof(nameSpace), "%s%05d", g_strText, i);
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            if (r->oprCmd == DDL_NAMESPACE_DROP) {
                ret = GmcDropNamespace(stmt, nameSpace);
                if (ret != 0) {
                    printf("[mgrNameSpace] GmcDropNamespace(%s) failed, ret = %d.\n", nameSpace, ret);
                } else {
                    printf("drop namespace successful, name = %s.\n", nameSpace);
                }
            } else {
                ret = GmcCreateNamespace(stmt, nameSpace, g_perfUser);
                if (ret != 0) {
                    printf("[mgrNameSpace] GmcCreateNamespace(%s) failed, ret = %d.\n", nameSpace, ret);
                } else {
                    printf("create namespace successful, name = %s.\n", nameSpace);
                }
            }
        } else {
            asyncContex *ctx = newAsyncContex(p, r, i);
            if (r->oprCmd == DDL_NAMESPACE_DROP) {
                ret = GmcDropNamespaceAsync(stmt, nameSpace, AsyncFunDropNameSpace, ctx);
                if (ret != 0) {
                    resetAsyncContex(ctx);
                    printf("[mgrNameSpace] GmcDropNamespaceAsync(%s) failed, ret = %d.\n", nameSpace, ret);
                }
            } else {
                ret = GmcCreateNamespaceAsync(stmt, nameSpace, g_perfUser, AsyncFunCreateNameSpace, ctx);
                if (ret != 0) {
                    resetAsyncContex(ctx);
                    printf("[mgrNameSpace] GmcCreateNamespace(%s) failed, ret = %d.\n", nameSpace, ret);
                }
            }
        }
    }
    return 0;
}

int mgrRespool(perfContent *p, runUnit *r, int v1, int v2 = -1)
{
    if (r->oprCmd != DDL_RESPOOL_CREATE && r->oprCmd != DDL_RESPOOL_DROP && r->oprCmd != DDL_RESPOOL_QUERY &&
        r->oprCmd != DDL_RESPOOL_BIND && r->oprCmd != DDL_RESPOOL_UNBIND) {
        printf("[mgrRespool] invalid action, r->oprCmd = %d, create/drop is request.\n", r->oprCmd);
        return -1;
    }
    GmcStmtT *stmt = r->stmt[p->offset];
    int v3 = v2;
    if (v3 == -1) {
        v3 = v1 + 1;
    }
    int i, len, ret;
    len = strlen(r->metaName);
    char *fieldAddr = NULL;
    for (i = 0; i < len; ++i) {
        if (r->metaName[i] == ',') {
            fieldAddr = r->metaName + i + 1;
            r->metaName[i] = '\0';
        }
    }
    char resName[32];
    char metaName[32];
    // pool_id, start_id, capacity, order, allac_type
    int resDef[5] = {0, 0, 2000000000, 0, 0};
    char resStr[256];
    snprintf(resStr, sizeof(resStr), "%s", g_strText);
    char *pos, *left;
    pos = strtok_r(resStr, ",", &left);
    snprintf(resName, sizeof(resName), "%s", pos);
    pos = strtok_r(NULL, ",", &left);
    i = 0;
    while (pos) {
        resDef[i++] = atoi(pos);
        pos = strtok_r(NULL, ",", &left);
    }
    for (i = v1; i < v3; ++i) {
        if (r->oprCmd == DDL_RESPOOL_CREATE) {
            if (checkCompleteName(resName)) {
                snprintf(resStr, sizeof(resStr),
                    "{ \"name\": \"%s\", \"pool_id\": %d, \"start_id\": %d, \"capacity\": %d, \"order\": %d, "
                    "\"alloc_type\" : %d }",
                    resName, resDef[0] + i, resDef[1], resDef[2], resDef[3], resDef[4]);
            } else {
                snprintf(resStr, sizeof(resStr),
                    "{ \"name\": \"%s%05d\", \"pool_id\": %d, \"start_id\": %d, \"capacity\": %d, \"order\": %d, "
                    "\"alloc_type\" : %d }",
                    resName, i, resDef[0] + i, resDef[1], resDef[2], resDef[3], resDef[4]);
            }
        } else {
            if (checkCompleteName(g_strText)) {
                snprintf(resStr, sizeof(resStr), "%s", g_strText);
            } else {
                snprintf(resStr, sizeof(resStr), "%s%05d", g_strText, i);
            }
            if (checkCompleteName(r->metaName)) {
                snprintf(metaName, sizeof(metaName), "%s", r->metaName);
            } else {
                snprintf(metaName, sizeof(metaName), "%s%05d", r->metaName, i);
            }
        }
        if (r->oprCmd == DDL_RESPOOL_CREATE) {
            ret = GmcCreateResPool(stmt, resStr);
            if (ret != 0) {
                printf("[mgrRespool] GmcCreateResPool(%s%05d) failed, errCode:%d, errMsg:%s = %d.\n", resName, i, ret,
                    p->errMsg[0]);
            } else {
                printf("create ResPool successful, name = %s%05d.\n", resName, i);
            }
            if (g_logLevel == 1) {
                printf("the schema of ResPool: %s\n", resStr);
            }
        } else if (r->oprCmd == DDL_RESPOOL_DROP) {
            ret = GmcDestroyResPool(stmt, resStr);
            if (ret != 0) {
                printf("[mgrRespool] GmcDestroyResPool(%s) failed, ret = %d.\n", resStr, ret);
            } else {
                printf("drop ResPool successful, name = %s.\n", resStr);
            }
        } else if (r->oprCmd == DDL_RESPOOL_QUERY) {
            char *str;
            ret = GmcGetResPool(stmt, resStr, &str);
            if (ret != 0) {
                printf("[mgrRespool] GmcGetResPool(%s) failed, ret = %d.\n", resStr, ret);
            } else {
                printf("ResPool:%s, json:%s.\n", resStr, str);
            }
        } else if (r->oprCmd == DDL_RESPOOL_BIND) {
            if (fieldAddr == NULL) {
                ret = GmcBindResPoolToLabel(stmt, resStr, metaName);
                if (ret != 0) {
                    printf("[mgrRespool] GmcBindResPoolToLabel(%s-%s) failed, errCode:%d, errMsg:%s.\n", resStr,
                        metaName, ret, p->errMsg[0]);
                } else {
                    printf("bind %s to %s successful.\n", resStr, metaName);
                }
            } else {
                printf("bind %s to %s.%s successful.\n", resStr, metaName, fieldAddr);
            }
        } else {  // DDL_RESPOOL_UNBIND
            if (fieldAddr == NULL) {
                ret = GmcUnbindResPoolFromLabel(stmt, metaName);
                if (ret != 0) {
                    printf("[mgrRespool] GmcUnbindResPoolFromLabel(%s) failed, ret = %d.\n", metaName, ret);
                } else {
                    printf("unbind respool from %s successful.\n", metaName);
                }
            } else {
                    printf("unbind respool from %s.%s successful.\n", metaName, fieldAddr);
            }
        }
    }
    return 0;
}

GmcAlarmIdE g_alarmId[11] = {GMC_ALARM_SHM_USED_INFO, GMC_ALARM_ASYNC_CONN_RING, GMC_ALARM_SUB_CONN_RING,
    GMC_ALARM_CONNECTION_NUMBER, GMC_ALARM_CLIENT_STMT, GMC_ALARM_VETEX_EDGE_DATA, GMC_ALARM_SUB_MSG_POOL,
    GMC_ALARM_DYM_USED_INFO, GMC_ALARM_HUNG_WORKER, GMC_ALARM_TABLE_SPACE_USED_INFO, GMC_ALARM_ID_UNDEFINED};

int mgrTableSpace(perfContent *p, runUnit *r, int v1, int v2 = -1)
{
    if (r->oprCmd != DDL_TABLESPACE_CREATE && r->oprCmd != DDL_TABLESPACE_DROP && r->oprCmd != DDL_ALARM_QUERY) {
        printf("[mgrTableSpace] invalid action, r->oprCmd = %d, create/drop/query is request.\n", r->oprCmd);
        return -1;
    }
    sceneEntry *s = g_sceneEntry + p->sceneId;
    GmcStmtT *stmt = r->stmt[p->offset];
    int v3 = v2;
    if (v3 == -1) {
        v3 = v1 + 1;
    }
    int i, j, len, ret;
    if (r->oprCmd == DDL_ALARM_QUERY) {
        GmcAlarmDataT data;
        for (i = v1; i < v3; ++i) {
            if (i < 0 || i >= 9) {
                continue;
            }
            ret = GmcGetAlarmData(stmt, g_alarmId[i], &data);
            if (ret != 0) {
                printf("[GmcGetAlarmData] get alarm msg failed, alarmId:%d, resultCode:%d.\n", i, ret);
                continue;
            }
            printf("%d %d %d %d %3.2f %3.2f %3.2f %3.2f %lu %lu\n", i, data.srcType, data.status, data.alarmStatus,
                data.activeValue * 100, data.clearedValue * 100, data.activeThreshold * 100,
                data.clearedThreshold * 100, data.succTimes, data.failTimes);
        }
        return 0;
    }
    char tableSpaceName[32];
    char cacheName[1024];
    GmcTspCfgT tsp = {cacheName, 4, 4, 4};
    (void)snprintf(cacheName, sizeof(cacheName), "%s", r->cacheName);
    char *pos, *left;
    pos = strtok_r(cacheName, ",", &left);
    (void)snprintf(tableSpaceName, sizeof(tableSpaceName), "%s", pos);
    do {
        pos = strtok_r(NULL, ",", &left);
        if (!pos) {
            break;
        }
        tsp.initSize = atoi(pos);
        pos = strtok_r(NULL, ",", &left);
        if (!pos) {
            break;
        }
        tsp.stepSize = atoi(pos);
        pos = strtok_r(NULL, ",", &left);
        if (!pos) {
            break;
        }
        tsp.maxSize = atoi(pos);
    } while (0);
    for (i = v1; i < v3; ++i) {
        len = strlen(tableSpaceName) - 1;
        if (len < 5) {
            len = -1;
        } else {
            while (len >= 0 && tableSpaceName[len] >= '0' && tableSpaceName[len] <= '9') {
                --len;
            }
        }
        if (g_renameTable && len == -1) {
            (void)snprintf(cacheName, sizeof(cacheName), "%s%05d", tableSpaceName, i);
        }
        if (r->oprCmd == DDL_TABLESPACE_DROP) {
            ret = GmcDropTablespace(stmt, cacheName);
            if (ret != 0) {
                printf("[mgrTableSpace] GmcDropTablespace failed, ret = %d, tableSpaceName = %s\n", ret, cacheName);
            } else {
                printf("drop tableSpace successful, name = %s.\n", cacheName);
            }
        } else {
            ret = GmcCreateTablespace(stmt, &tsp);
            if (ret != 0) {
                printf("[mgrTableSpace] GmcCreateTablespace failed, ret = %d, tableSpaceName = %s\n", ret, cacheName);
            } else {
                printf("create tableSpace successful, tableSpaceName = %s\n", cacheName);
            }
        }
    }
    return 0;
}

int mgrLabelKV(perfContent *p, runUnit *r, int v1, int v2 = -1)
{
    if (r->oprCmd != DDL_KV_CREATE && r->oprCmd != DDL_KV_DROP && r->oprCmd != DDL_KV_QUERY &&
        r->oprCmd != DDL_KV_TRUNCATE && r->oprCmd != DDL_KV_STATIC_COUNT && r->oprCmd != DDL_KV_ALTER_COUNT) {
        printf("[mgrLabelKV] invalid action, r->oprCmd = %d, create/drop/truncate is request.\n", r->oprCmd);
        return 1;
    }
    if (r->oprCmd == DDL_KV_QUERY) {
        printf("[mgrLabelKV] useless interface in kv scene.\n", r->metaName);
        return 0;
    }
    int i;
    char metaName[128];
    int v3 = v2;
    if (v3 == -1) {
        v3 = v1 + 1;
    }
    char labelConfig[64] = {0};
    int n = strlen(r->metaName);
    if (r->oprCmd == DDL_VERTEX_CREATE && r->verStr) {
        char *strConfig;
        int64_t size = perfReadJsonFile(r->verStr, &strConfig);
        if (size < 0) {
            return -1;
        }
        snprintf(labelConfig, sizeof(labelConfig), "%s", strConfig);
        free(strConfig);
    };
    int ret;
    asyncContex *ctx;
    for (i = v1; i < v3; ++i) {
        snprintf(metaName, sizeof(metaName), "%s%05d", r->metaName, i);
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            if (r->oprCmd == DDL_KV_DROP) {
                ret = GmcKvDropTable(r->stmt[p->offset], metaName);
                if (ret != 0 && ret != GMERR_UNDEFINED_TABLE) {
                    printf("[mgrLabelKV] GmcDropVertexLabel(metaName=%s) failed, ret = %d.\n", metaName, ret);
                } else {
                    printf("drop lable(kv.%s) successful.\n", metaName);
                }
                continue;
            }
            if (r->oprCmd == DDL_KV_TRUNCATE) {
                ret = GmcKvTruncateTable(r->stmt[p->offset], metaName);
                if (ret != 0) {
                    printf("[mgrLabelKV] GmcTruncateKvTable(metaName=%s) failed, ret = %d.\n", metaName, ret);
                } else {
                    printf("truncate label(kv.%s) successful.\n", metaName);
                }
                continue;
            }
            if (r->oprCmd == DDL_KV_STATIC_COUNT) {
                fixCompleteName(r->metaName);
                ret = GmcKvPrepareStmtByLabelName(r->stmt[p->offset], r->metaName);
                if (ret != 0) {
                    printf("[mgrLabelKV] GmcKvPrepareStmtByLabelName failed, ret = %d.\n", ret);
                }
                unsigned int tableCount;
                ret = GmcKvTableRecordCount(r->stmt[p->offset], &tableCount);
                if (ret != 0) {
                    printf("[mgrLabelKV] GmcKvTableRecordCount(metaName=%s) failed, ret = %d.\n", metaName, ret);
                } else {
                    printf("table_name:kv.%s, count:%u.\n", metaName, tableCount);
                }
                continue;
            }
            if (r->oprCmd == DDL_KV_ALTER_COUNT) {
                do {
                    const char *labelArray[1];
                    labelArray[0] = metaName;
                    uint64_t alterCount[2];
                    ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_SET, alterCount, 1);
                    if (ret != 0) {
                        printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                        return 5;
                    }
                    ret = GmcGetOperStatsCnt(
                        r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_SET_FAILED, alterCount + 1, 1);
                    if (ret != 0) {
                        printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                        return 5;
                    }
                    printf("  set:   %06lu  %06lu\n", alterCount[0], alterCount[1]);
                    ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_REMOVE, alterCount, 1);
                    if (ret != 0) {
                        printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                        return 5;
                    }
                    ret = GmcGetOperStatsCnt(
                        r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_REMOVE_FAILED, alterCount + 1, 1);
                    if (ret != 0) {
                        printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                        return 5;
                    }
                    printf("  del:  %06lu  %06lu\n", alterCount[0], alterCount[1]);
                    ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_TOTAL, alterCount, 1);
                    if (ret != 0) {
                        printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                        return 5;
                    }
                    printf("  total:    %06lu\n", alterCount[0]);
                } while (0);
                continue;
            }
            if (labelConfig[0] == '\0') {
                ret = GmcKvCreateTable(r->stmt[p->offset], metaName, NULL);
            } else {
                ret = GmcKvCreateTable(r->stmt[p->offset], metaName, labelConfig);
            }
            if (ret != 0) {
                printf("[mgrLabelKV] GmcCreateVertexLabel(%s), ret = %d.\n", metaName, ret);
            } else {
                printf("create lable(kv.%s) successful.\n", metaName);
            }
        } else {
            ctx = newAsyncContex(p, r, i);
            if (r->oprCmd == DDL_KV_DROP) {
                ret = GmcKvDropTableAsync(r->stmt[p->offset], metaName, AsyncFunDropKv, ctx);
                if (ret != 0 && ret != GMERR_UNDEFINED_TABLE) {
                    resetAsyncContex(ctx);
                    printf("[mgrLabelKV] GmcDropKvTableAsync(metaName=%s) failed, ret = %d.\n", metaName, ret);
                }
                continue;
            }
            if (r->oprCmd == DDL_KV_TRUNCATE) {
                ret = GmcKvTruncateTableAsync(r->stmt[p->offset], metaName, AsyncFunTruncateKv, ctx);
                if (ret != 0) {
                    resetAsyncContex(ctx);
                    printf("[mgrLabelKV] GmcTruncateKvTableAsync(metaName=%s) failed, ret = %d.\n", metaName, ret);
                }
                continue;
            }
            if (r->oprCmd == DDL_KV_STATIC_COUNT) {
                printf("[tid:%02d] [exeDMLKV] GmcKvTableRecordCount in async mode, uncertain function.\n", p->id);
                resetAsyncContex(ctx);
                continue;
            }
            if (r->oprCmd == DDL_KV_ALTER_COUNT) {
                printf("[tid:%02d] [exeDMLKV] GmcGetOperStatsCnt in async mode, uncertain function.\n", p->id);
                resetAsyncContex(ctx);
                continue;
            }
            if (labelConfig[0] == '\0') {
                ret = GmcKvCreateTableAsync(r->stmt[p->offset], metaName, NULL, AsyncFunCreateKv, ctx);
            } else {
                ret = GmcKvCreateTableAsync(r->stmt[p->offset], metaName, labelConfig, AsyncFunCreateKv, ctx);
            }
            if (ret != 0) {
                resetAsyncContex(ctx);
                printf("[mgrLabelKV] GmcCreateKvTableAsync(%s), ret = %d.\n", metaName, ret);
            }
        }
    }
    return 0;
}

int mgrLabelVertex(perfContent *p, runUnit *r, int v1, int v2 = -1)
{
    if (r->oprCmd != DDL_VERTEX_CREATE && r->oprCmd != DDL_VERTEX_DROP && r->oprCmd != DDL_VERTEX_QUERY &&
        r->oprCmd != DDL_VERTEX_TRUNCATE && r->oprCmd != DDL_VERTEX_STATIC_COUNT &&
        r->oprCmd != DDL_VERTEX_ALTER_COUNT) {
        printf("[mgrLabelVertex] invalid action, r->oprCmd = %d, create/drop/query/truncate is request.\n", r->oprCmd);
        return 1;
    }
    int i;
    char metaName[128];
    int v3 = v2;
    if (v3 == -1) {
        v3 = v1 + 1;
    }
    if (r->oprCmd == DDL_VERTEX_QUERY) {
        for (i = v1; i < v3; ++i) {
            if (checkCompleteName(r->metaName)) {
                snprintf(metaName, sizeof(metaName), "%s", r->metaName);
            } else {
                snprintf(metaName, sizeof(metaName), "%s%05d", r->metaName, i);
            }
            metaDataLabelVertex *t = (metaDataLabelVertex *)r->metadataT;
            if (t == NULL) {
                printf("[mgrLabelVertex] GetVertexDefine(metaName=%s) failed.\n", metaName);
                continue;
            }
            printMetaDataVertex(t);
            releaseMetaDataVertex(t);
        }
        return 0;
    }
    char *labCfgParam = NULL;
    char labCfg[256] = {0};
    char *labelSchemaNorm = NULL;
    char *labelSchemaText = NULL;
    int j, len, ret;
    int offsetAddr[50];
    int offsetNum = 0;
    int n = strlen(r->metaName);
    do {
        if (r->oprCmd != DDL_VERTEX_CREATE) {
            break;
        }
        int64_t size = perfReadJsonFile(r->keyStr, &labelSchemaNorm);
        if (size < 0) {
            printf("[mgrLabelVertex] perfReadJsonFile(file=%s), labelSchema=%s\n", r->keyStr, labelSchemaNorm);
            return 4;
        }
        if (g_renameTable == false) {
            labelSchemaText = labelSchemaNorm;
        } else {
            labelSchemaText = (char *)malloc(size + 256);
            if (labelSchemaText == NULL) {
                printf("[mgrLabelVertex] malloc memory(size:%d) for labelSchemaText failed, schemFile:%s.\n",
                    size + 256, r->keyStr);
                return 5;
            }
            memset(labelSchemaText, 0, size + 256);
            len = size;
            size += 256;
            j = 0;
            for (i = 0; i < len; ++i) {
                if (i + 5 > len) {
                    labelSchemaText[j++] = labelSchemaNorm[i];
                    continue;
                }
                if ((0 != strncmp(labelSchemaNorm + i, r->metaName, n)) || (i > 0 && labelSchemaNorm[i - 1] != '"') ||
                    (labelSchemaNorm[i + n] != '"')) {
                    labelSchemaText[j++] = labelSchemaNorm[i];
                    continue;
                }
                offsetAddr[offsetNum++] = j + n;
                snprintf(labelSchemaText + j, size - j, "%s00000", r->metaName);
                j = strlen(labelSchemaText);
                i = i + n - 1;
            }
            free(labelSchemaNorm);
        }
        len = strlen(r->keyStr);
        if (0 != strncmp(r->keyStr + len - 7, ".gmjson", 7)) {
            break;
        }
        snprintf(metaName, sizeof(metaName), "%s", r->keyStr);
        snprintf(metaName + len - 7, sizeof(metaName) - len, ".gmconfig");
        if (0 == access(metaName, F_OK)) {
            size = perfReadJsonFile(metaName, &labelSchemaNorm);
            if (size > 0) {
                labCfgParam = labelSchemaNorm;
                break;
            }
        }
        if (r->labelCfg != NULL) {
            labCfgParam = labCfg;
        }
    } while (0);
    char serial[8];
    asyncContex *ctx;
    for (i = v1; i < v3; ++i) {
        if (checkCompleteName(r->metaName)) {
            snprintf(metaName, sizeof(metaName), "%s", r->metaName);
        } else {
            snprintf(metaName, sizeof(metaName), "%s%05d", r->metaName, i);
        }
        if (r->connMode == GMC_CONN_TYPE_ASYNC) {
            ctx = newAsyncContex(p, r, i);
        }
        if (r->oprCmd == DDL_VERTEX_DROP) {
            if (r->connMode == GMC_CONN_TYPE_SYNC) {
                if (r->schVersion == -1) {
                    ret = GmcDropVertexLabel(r->stmt[p->offset], metaName);
                } else {
                    ret = GmcDegradeVertexLabel(r->stmt[p->offset], metaName, r->schVersion);
                }
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcDropVertexLabel(metaName=%s) failed, ret = %d.\n", metaName, ret);
                } else {
                    printf("drop lable(vertex) successful, name = %s\n", metaName);
                }
            } else {
                ret = GmcDropVertexLabelAsync(r->stmt[p->offset], metaName, AsyncFunDropVertex, ctx);
                if (ret != 0) {
                    resetAsyncContex(ctx);
                    printf(
                        "[mgrLabelVertex] GmcDropVertexLabelAsync(metaName=%s) failed, ret = %d.\n", metaName, ret);
                }
            }
            continue;
        }
        if (r->oprCmd == DDL_VERTEX_TRUNCATE) {
            if (r->connMode == GMC_CONN_TYPE_SYNC) {
                ret = GmcTruncateVertexLabel(r->stmt[p->offset], metaName);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcTruncateVertexLabel(metaName=%s) failed, ret = %d.\n", metaName, ret);
                } else {
                    printf("truncate lable(vertex) successful, name = %s\n", metaName);
                }
            } else {
                ret = GmcTruncateVertexLabelAsync(r->stmt[p->offset], metaName, AsyncFunTruncateVertex, ctx);
                if (ret != 0) {
                    resetAsyncContex(ctx);
                    printf("[mgrLabelVertex] GmcTruncateVertexLabelAsync(metaName=%s) failed, ret = %d.\n", metaName,
                        ret);
                }
            }
            continue;
        }
        if (r->oprCmd == DDL_VERTEX_STATIC_COUNT) {
            if (r->connMode == GMC_CONN_TYPE_SYNC) {
                uint64_t tableCount;
                ret = GmcGetVertexCount(r->stmt[p->offset], metaName, NULL, &tableCount);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetVertexCount(metaName=%s) failed, ret = %d.\n", metaName, ret);
                } else {
                    printf("table_name:%s, count:%lu.\n", metaName, tableCount);
                }
            } else {
                resetAsyncContex(ctx);
                printf("[mgrLabelVertex] GmcGetVertexCount(metaName=%s) unsupported funcation in async mode.\n",
                    metaName, ret);
            }
            continue;
        }
        if (r->oprCmd == DDL_VERTEX_ALTER_COUNT) {
            if (r->connMode == GMC_CONN_TYPE_ASYNC) {
                resetAsyncContex(ctx);
                printf("[mgrLabelVertex] GmcGetOperStatsCnt(metaName=%s) unsupported funcation in async mode.\n",
                    metaName, ret);
                continue;
            }
            do {
                const char *labelArray[1];
                labelArray[0] = metaName;
                uint64_t alterCount[2];
                ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_INSERT, alterCount, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                ret = GmcGetOperStatsCnt(
                    r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_INSERT_FAILED, alterCount + 1, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                printf("  insert:   %06lu  %06lu\n", alterCount[0], alterCount[1]);
                ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_REPLACE, alterCount, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                ret = GmcGetOperStatsCnt(
                    r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_REPLACE_FAILED, alterCount + 1, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                printf("  replace:  %06lu  %06lu\n", alterCount[0], alterCount[1]);
                ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_UPDATE, alterCount, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                ret = GmcGetOperStatsCnt(
                    r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_UPDATE_FAILED, alterCount + 1, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                printf("  update:   %06lu  %06lu\n", alterCount[0], alterCount[1]);
                ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_DELETE, alterCount, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                ret = GmcGetOperStatsCnt(
                    r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_DELETE_FAILED, alterCount + 1, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                printf("  delete:   %06lu  %06lu\n", alterCount[0], alterCount[1]);
                ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_MERGE, alterCount, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                ret = GmcGetOperStatsCnt(
                    r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_MERGE_FAILED, alterCount + 1, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                printf("  merge:   %06lu  %06lu\n", alterCount[0], alterCount[1]);
                ret = GmcGetOperStatsCnt(r->stmt[p->offset], labelArray, GMC_STATISTICS_TYPE_TOTAL, alterCount, 1);
                if (ret != 0) {
                    printf("[mgrLabelVertex] GmcGetOperStatsCnt failed, metaName=%s, ret = %d.\n", metaName, ret);
                    return 5;
                }
                printf("  total:    %06lu\n", alterCount[0]);
            } while (0);
            continue;
        }
        if (r->labelCfg != NULL) {
            snprintf(labCfg, sizeof(labCfg), "{");
            if (r->labelCfg->max_record_count != -1) {
                len = strlen(labCfg);
                snprintf(
                    labCfg + len, sizeof(labCfg) - len, " \"max_record_count\": %d,", r->labelCfg->max_record_count);
            }
            if (r->labelCfg->max_record_count_check != -1) {
                len = strlen(labCfg);
                if (r->labelCfg->max_record_count_check == 0) {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"max_record_count_check\": false,");
                } else {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"max_record_count_check\": true,");
                }
            }
            if (r->labelCfg->defragmentation != -1) {
                len = strlen(labCfg);
                if (r->labelCfg->defragmentation == 0) {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"defragmentation\": false,");
                } else {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"defragmentation\": true,");
                }
            }
            if (r->labelCfg->isFastReadUncommitted != -1) {
                len = strlen(labCfg);
                if (r->labelCfg->isFastReadUncommitted == 0) {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"isFastReadUncommitted\": false,");
                } else {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"isFastReadUncommitted\": true,");
                }
            }
            if (r->labelCfg->isTableLockMode != -1) {
                len = strlen(labCfg);
                if (r->labelCfg->isTableLockMode == 0) {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"isTableLockMode\": false,");
                } else {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"isTableLockMode\": true,");
                }
            }
            if (r->labelCfg->disable_sub_back_pressure != -1) {
                len = strlen(labCfg);
                if (r->labelCfg->disable_sub_back_pressure == 0) {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"disable_sub_back_pressure\": false,");
                } else {
                    snprintf(labCfg + len, sizeof(labCfg) - len, " \"disable_sub_back_pressure\": true,");
                }
            }
            if (r->labelCfg->auto_increment != -1) {
                len = strlen(labCfg);
                snprintf(labCfg + len, sizeof(labCfg) - len, " \"auto_increment\": %d,", r->labelCfg->auto_increment);
            }
            if (r->labelCfg->push_age_record_batch != -1) {
                len = strlen(labCfg);
                snprintf(labCfg + len, sizeof(labCfg) - len, " \"push_age_record_batch\": %d,",
                    r->labelCfg->push_age_record_batch);
            }
            len = strlen(labCfg);
            if (labCfg[len - 1] == ',') {
                snprintf(labCfg + len - 1, sizeof(labCfg) - len, " }");
            } else {
                labCfgParam = NULL;
            }
        }
        if (g_renameTable) {
            snprintf(serial, sizeof(serial), "%05d", i);
            for (j = 0; j < offsetNum; ++j) {
                memcpy(labelSchemaText + offsetAddr[j], serial, 5);
            }
        }
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            ret = GmcCreateVertexLabel(r->stmt[p->offset], labelSchemaText, labCfgParam);
            if (ret != 0) {
                printf("[mgrLabelVertex] GmcCreateVertexLabel(%s), errCode:%d, errMsg:%s\n", metaName, ret,
                    p->errMsg[r->connMode]);
            } else {
                printf("create lable(vertex) successful, name = %s.\n", metaName);
            }
        } else {
            ret =
                GmcCreateVertexLabelAsync(r->stmt[p->offset], labelSchemaText, labCfgParam, AsyncFunCreateVertex, ctx);
            if (ret != 0) {
                resetAsyncContex(ctx);
                printf("[mgrLabelVertex] GmcCreateVertexLabel(%s), errCode:%d, errMsg:%s\n", metaName, ret,
                    p->errMsg[r->connMode]);
            }
        }
        if (g_logLevel == 1) {
            printf("label config: %s\n", labCfgParam);
            printf("label schema: %s\n", labelSchemaText);
        }
    }
    if (labelSchemaText) {
        free(labelSchemaText);
    }
    return 0;
}

int mgrLabelEdge(perfContent *p, runUnit *r, int v1, int v2 = -1)
{
    if (r->oprCmd != DDL_EDGE_CREATE && r->oprCmd != DDL_EDGE_DROP && r->oprCmd != DDL_EDGE_QUERY) {
        printf("[mgrLabelEdge] invalid action, r->oprCmd = %d, create/drop/query is request.\n", r->oprCmd);
        return -1;
    }
    int ret;
    if (r->oprCmd == DDL_EDGE_QUERY) {
        metaDataLabelEdge *e = (metaDataLabelEdge *)r->metadataE;
        if (e == NULL) {
            printf("[mgrLabelEdge] GetEdgeDefine(edgeName=%s) failed.\n", r->idxName);
            return 1;
        }
        printMetaDataEdge(e);
        return 0;
    }
    if (r->oprCmd == DDL_EDGE_DROP) {
        ret = GmcDropEdgeLabel(r->stmt[p->offset], r->edgeName);
        if (ret != 0) {
            printf("[mgrLabelEdge] GmcDropEdgeLabel(edgeName=%s) failed, ret = %d.\n", r->edgeName, ret);
        } else {
            printf("drop lable(Edge) successful, name = %s\n", r->edgeName);
        }
        return -1;
    }
    char *labelSchema;
    int64_t size = perfReadJsonFile(r->keyStr, &labelSchema);
    if (size < 0) {
        printf("[mgrLabelEdge] perfReadJsonFile(file=%s), labelSchema=%s\n", r->keyStr, labelSchema);
        return -1;
    }
    ret = GmcCreateEdgeLabel(r->stmt[p->offset], labelSchema, NULL);
    if (ret != 0) {
        printf("[mgrLabelEdge] labelSchema:%s, ret = %d.\n", labelSchema, ret);
    } else {
        printf("create lable(edge) successful\n");
    }
    if (g_logLevel == 1) {
        printf("label schema: %s\n", labelSchema);
    }
    free(labelSchema);
    return ret;
}

int exeScanKV(perfContent *p, runUnit *r, int val1, int val2)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    GmcStmtT *stmt = r->stmt[p->offset];
    int i, ret;
    fixCompleteName(r->metaName);
    ret = GmcKvPrepareStmtByLabelName(stmt, r->metaName);
    if (ret != 0) {
        printf("[tid:%02d] [exeScanKV] GmcKvPrepareStmtByLabelName(%s) failed, errCode:%d, errMsg:%s\n", p->id,
            r->metaName, ret, p->errMsg[r->connMode]);
        return ret;
    }
    if (r->vecSize != 1) {
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &r->vecSize, sizeof(r->vecSize));
        if (ret != 0) {
            printf("[tid:%02d] [exeScanKV] GmcSetStmtAttr(GMC_STMT_ATTR_PRE_FETCH_ROWS:%s:%d) failed, errCode:%d, "
                   "errMsg:%s\n",
                p->id, r->metaName, r->vecSize, ret, p->errMsg[r->connMode]);
            return ret;
        }
    }
    bool isEOF;
    uint64_t t1;
    char *bufKey;
    char *bufVal;
    uint32_t sizeKey, sizeVal;
    for (i = val1; i < val2; ++i) {
        t1 = getCyclesMS();
        ret = GmcKvScan(stmt, r->limitSize);
        if (ret != 0) {
            printf("[tid:%02d] [exeScanKV] exeScanKV(%s) failed, errCode:%d, errMsg:%s\n", p->id, r->metaName, ret,
                p->errMsg[r->connMode]);
            assert(0);
            return ret;
        }
        isEOF = false;
        while (true) {
            ret = GmcFetch(stmt, &isEOF);
            if (isEOF == true || ret != 0) {
                break;
            }
            ++p->countSucc[0];
            ret = GmcKvGetFromStmt(stmt, (void **)&bufKey, &sizeKey, (void **)&bufVal, &sizeVal);
            if (ret != 0) {
                printf("[exeScanKV] GmcGetKvFromStmt(%s) failed, errCode:%d, errMsg:%s\n", r->metaName, ret,
                    p->errMsg[r->connMode]);
                return ret;
            }
            if (g_logLevel > 0 && isEOF == false) {
                printf("%04d  key:%s  value:%u|%s\n", p->countSucc[0], bufKey, sizeVal, bufVal);
            }
        }
        if (ret != 0) {
            printf(
                "[exeScanKV] GmcFetch(%s) failed, errCode:%d, errMsg:%s\n", r->metaName, ret, p->errMsg[r->connMode]);
        } else {
            if (p->countSucc[0] == 0 && g_logLevel > 0) {
                printf("null\n");
            }
        }
        printf("[tid:%02d] [exeScanKV] scanID:%d, kvName:%s, count:%d, elapse:%lums\n", p->id, i, r->metaName,
            p->countSucc[0], getCyclesMS() - t1);
    }
    GmcResetStmt(stmt);
    return 0;
}

int exeDMLVertex(perfContent *p, runUnit *r, int v, bool isLast = false)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    GmcStmtT *stmt = r->stmt[p->offset];
    GmcConnT *conn = p->conn[r->connMode];
    DmVlIndexLabelT *d = (DmVlIndexLabelT *)r->metadataI;
    metaDataLabelVertex *t = (metaDataLabelVertex *)r->metadataT;
    int c, ret, t_ret;
    if (r->oprCmd == DML_VERTEX_UPDATE || (r->oprCmd == DML_VERTEX_MERGE)) {
        ret = GmcResetVertex(stmt, false);
        if (ret != 0) {
            printf("[tid:%02d] [exeDMLVertex] GmcResetVertex failed, ret = %d.\n", p->id, ret);
            return ret;
        }
    }
    bool isFinish;
    bool rebuildEdge = true;
    if (r->oprCmd == DML_VERTEX_UPDATE || r->oprCmd == DML_VERTEX_MERGE || r->oprCmd == DML_VERTEX_DELETE ||
        r->oprCmd == DML_VERTEX_SELECT) {
        if (r->isFilter == false) {
            ret = GmcSetIndexKeyName(stmt, d->indexName);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcSetIndexKeyName(keyName:%s) failed, ret = %d.\n", p->id,
                    d->indexName, ret);
                return ret;
            }
            if (r->range) {
                ret = setVertexKeyRange(p, r, v);
            } else {
                ret = setVertexKeyField(p, r, v);
            }
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] setVertexKeyRange/setVertexKeyField failed, ret = %d.\n", p->id, ret);
                return ret;
            }
        } else {
            ret = setVertexFilter(p, r, v);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] setVertexFilter failed, ret = %d.\n", p->id, ret);
                return ret;
            }
            if (r->oprCmd == DML_VERTEX_SELECT) {
                if (r->sort) {
                    ret = GmcSetOrderBy(stmt, r->sort->order, r->sort->propNameArr, r->sort->n);
                    if (ret != 0) {
                        printf("[tid:%02d] [exeDMLVertex] GmcSetOrderBy(%s) failed, errCode:%d, errMsg:%s\n", p->id,
                            r->metaName, ret, p->errMsg[r->connMode]);
                        return ret;
                    }
                }
            }
        }
        if (r->oprCmd == DML_VERTEX_SELECT && r->limitSize > 0) {
            ret = GmcSetScanLimit(stmt, r->limitSize);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcSetScanLimit(%s) failed, errCode:%d, errMsg:%s\n", p->id,
                    r->metaName, ret, p->errMsg[r->connMode]);
                return ret;
            }
        }
        if (r->oprCmd == DML_VERTEX_DELETE || r->oprCmd == DML_VERTEX_UPDATE || r->oprCmd == DML_VERTEX_MERGE) {
            (void)GmcSetStmtAttr(stmt, GMC_STMT_ATTR_AUTO_DELETE_EDGE, &rebuildEdge, sizeof(bool));
        }
    }
    if (r->oprCmd == DML_VERTEX_REPLACE || r->oprCmd == DML_VERTEX_INSERT) {
        setSimpleVertexInc(p, r, v);
    } else if (r->oprCmd == DML_VERTEX_UPDATE || r->oprCmd == DML_VERTEX_MERGE) {
        setSimpleVertexInc(p, r, v);
    }
    asyncContex *ctx;
    if (r->batchNum > 0) {
    LOOP_BATCH:
        ret = GmcBatchAddDML(r->ctxBatch[p->offset], stmt);
        if ((ret != 0) && (ret != GMERR_PROGRAM_LIMIT_EXCEEDED)) {
            printf("[tid:%02d] [exeDMLVertex] GmcBatchAddDML failed, errCode:%d, errMsg:%s\n", p->id, ret,
                p->errMsg[r->connMode]);
            return ret;
        }
        t_ret = ret;
        ++r->countBatch[p->offset];
        if (r->countBatch[p->offset] < r->batchNum && isLast == false) {
            return ret;
            ;
        }
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            GmcBatchRetT retBatch;
            uint32_t vt = 0;
            uint32_t vs = 0;
            ret = GmcBatchExecute(r->ctxBatch[p->offset], &retBatch);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcBatchExecute failed, errCode:%d, errMsg:%s\n", p->id, ret,
                    p->errMsg[r->connMode]);
                return ret;
            }
            ret = GmcBatchDeparseRet(&retBatch, &vt, &vs);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcBatchDeparseRet failed, errCode:%d, errMsg:%s\n", p->id, ret,
                    p->errMsg[r->connMode]);
                return ret;
            }
            p->countReq[0] += vt;
            p->countSucc[0] += vs;
            p->countFail[0] += (vt - vs);
        } else {
            ctx = newAsyncContex(p, r, v);
            ret = GmcBatchExecuteAsync(r->ctxBatch[p->offset], AsyncFunVertexBatchExe, ctx);
            if (ret != 0) {
                resetAsyncContex(ctx);
                printf("[tid:%02d] [DAVertexAsync-batch] GmcBatchExecuteAsync fail, key:%d resultCode:%d, total:%u, "
                       "success:%u.\n",
                    p->id, v, ret, p->errMsg[r->connMode]);
            }
        }
        if (t_ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            t_ret = 0;
            goto LOOP_BATCH;
        }
        r->countBatch[p->offset] = 0;
        return ret;
    }
    GmcAsyncRequestDoneContextT asyncReq;
    if (r->connMode == GMC_CONN_TYPE_ASYNC) {
        ctx = newAsyncContex(p, r, v);
        asyncReq.userData = ctx;
    }
    if (g_snLatency) {
        p->sn->latency[v - r->beginCycle] = getCyclesUS();
    }
    if (g_logLevel > 0 && (r->oprCmd == DML_VERTEX_REPLACE || r->oprCmd == DML_VERTEX_INSERT)) {
        perfDumpVertex(p, stmt, t);
    }
    ++p->countReq[0];
    if (r->connMode == GMC_CONN_TYPE_ASYNC) {
        if (r->oprCmd == DML_VERTEX_REPLACE) {
            asyncReq.replaceCb = AsyncFunVertexReplace;
        } else if (r->oprCmd == DML_VERTEX_INSERT) {
            asyncReq.insertCb = AsyncFunVertexInsert;
        } else if (r->oprCmd == DML_VERTEX_MERGE) {
            asyncReq.mergeCb = AsyncFunVertexMerge;
        } else if (r->oprCmd == DML_VERTEX_DELETE) {
            asyncReq.deleteCb = AsyncFunVertexDelete;
        } else if (r->oprCmd == DML_VERTEX_UPDATE) {
            asyncReq.updateCb = AsyncFunVertexUpdate;
        } else if (r->oprCmd == DML_VERTEX_SELECT) {
            resetAsyncContex(ctx);
            printf("[tid:%02d] [exeDMLVertex] read %s in async mode.\n", p->id, r->metaName);
            return -1;
        } else {
            resetAsyncContex(ctx);
            printf("[tid:%02d] [exeDMLVertex] invalid action:%d.\n", p->id, r->oprCmd);
            return -1;
        }
        ret = GmcExecuteAsync(stmt, &asyncReq);
    } else {
        ret = GmcExecute(stmt);
    }
    if (r->connMode == GMC_CONN_TYPE_ASYNC) {
        if (ret != 0) {
            printf("[tid:%02d] [exeDMLVertex] tableName:%s, oprCmd:%d, ret:%d.\n", p->id, r->metaName, r->oprCmd, ret);
            resetAsyncContex(ctx);
        }
        return ret;
    }
    if (ret == 0) {
        if (r->oprCmd != DML_VERTEX_SELECT) {
            unsigned int affectRows = 0;
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcGetStmtAttr failed, tableName:%s, oprCmd:%d, ret:%d.\n", p->id,
                    r->metaName, r->oprCmd, ret);
            } else {
                if (affectRows == 0) {
                    ++p->countLost[0];
                } else {
                    p->countSucc[0] += affectRows;
                }
            }
        } else {
            isFinish = false;
            c = 0;
            while (ret == 0) {
                ret = GmcFetch(stmt, &isFinish);
                if (isFinish) {
                    break;
                }
                ++c;
                if (g_logLevel > 0 && ret == 0) {
                    perfDumpVertex(p, stmt, t);
                }
            }
            if (c == 0) {
                ++p->countLost[0];
            } else {
                p->countSucc[0] += c;
            }
        }
    } else if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_UNIQUE_VIOLATION || ret == GMERR_RESTRICT_VIOLATION ||
               ret == GMERR_DUPLICATE_OBJECT) {
        ++p->countLost[0];
        if (g_logLevel > 0) {
            printf("[tid:%02d] [DAVertexSync]  duplicate key:%d, errCode:%d, errMsg:%s\n", p->id, v, ret,
                p->errMsg[r->connMode]);
        }
        ret = 0;
    } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        // retry
        --p->countReq[0];
    } else {
        ++p->countFail[0];
        printf("[tid:%02d] [DAVertexSync] key:%d, errCode:%d, errMsg:%s\n", p->id, v, ret, p->errMsg[r->connMode]);
    }
    return ret;
}

inline unsigned int setValIntoKV(perfContent *p, char *s, KVElement *el, int v)
{
    if (el->varType == 0) {
        return el->size1;
    }
    if (el->varType == 1) {
        if (el->varRule == 0) {
            return el->size1;
        } else if (el->varRule == 1) {
            return TEST_RANDOM(p->randomSeed) % el->size1;
        } else if (el->varRule == 2) {
            return TEST_RANDOM(p->randomSeed) % (el->size2 - el->size1) + el->size1;
        } else {
            assert(0);
        }
    }
    int64_t v1;
    if (el->varRule == 0) {
        v1 = v;
    } else if (el->varRule == 1) {
        v1 = v + el->varBegin;
    } else if (el->varRule == 2) {
        v1 = TEST_RANDOM(p->randomSeed) % el->varBegin;
    } else if (el->varRule == 3) {
        v1 = TEST_RANDOM(p->randomSeed) % (el->varEnd - el->varBegin) + el->varBegin;
    } else if (el->varRule == 4) {
        v1 = v % (el->varEnd - el->varBegin) + el->varBegin;
    } else {
        assert(0);
    }
    char serial[24];
    if (el->varType == 2) {
        snprintf(serial, sizeof(serial), "%08ld", v1);
    } else {
        snprintf(serial, sizeof(serial), "%012ld", v1);
    }
    for (int i = 0; i < el->length; ++i) {
        s[el->offset + i] = serial[i];
    }
    return el->size1;
}

int exeDMLKV(perfContent *p, runUnit *r, int v, bool isLast = false)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    GmcStmtT *stmt = r->stmt[p->offset];
    int ret;
    uint32_t sizeK, sizeV;
    int k = 0;
    sizeK = setValIntoKV(p, r->inputI[p->offset], &r->kvK, v);
    ++p->countReq[0];
    asyncContex *ctx;
    if (r->connMode == GMC_CONN_TYPE_ASYNC) {
        ctx = newAsyncContex(p, r, v);
    }
    if (g_snLatency) {
        p->sn->latency[v - r->beginCycle] = getCyclesUS();
    }
    GmcKvTupleT tuple;
    if (r->batchNum > 0) {
        if (r->oprCmd == DML_KV_SET) {
            sizeV = setValIntoKV(p, r->inputV[p->offset], &r->kvV, v);
            ret = GmcKvInputToStmt(stmt, r->inputI[p->offset], sizeK, r->inputV[p->offset], sizeV);
        } else {
            ret = GmcKvInputToStmt(stmt, r->inputI[p->offset], sizeK, NULL, 0);
        }
        if (ret != 0) {
            printf("[tid:%02d] [exeDMLKV] GmcKvInputToStmt failed, ret = %d.\n", p->id, ret);
            return ret;
        }
        if (r->oprCmd == DML_KV_SET) {
            ret = GmcBatchAddKvDML(r->ctxBatch[p->offset], stmt, GMC_OPERATION_INSERT);
        } else if (r->oprCmd == DML_KV_DEL) {
            ret = GmcBatchAddKvDML(r->ctxBatch[p->offset], stmt, GMC_OPERATION_DELETE);
        } else {
            printf("[tid:%02d] [exeDMLKV] invalid action:%d in batch operation.\n", p->id, r->oprCmd);
            return 0;
        }
        ++r->countBatch[p->offset];
        if (r->countBatch[p->offset] < r->batchNum && isLast == false) {
            return ret;
            ;
        }
        unsigned int vt = 1;
        unsigned int vs = 1;
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            GmcBatchRetT retBatch;
            uint32_t vt = 0;
            uint32_t vs = 0;
            ret = GmcBatchExecute(r->ctxBatch[p->offset], &retBatch);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcBatchExecute failed, errCode:%d, errMsg:%s\n", p->id, ret,
                    p->errMsg[r->connMode]);
                return ret;
            }
            ret = GmcBatchDeparseRet(&retBatch, &vt, &vs);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcBatchDeparseRet failed, errCode:%d, errMsg:%s\n", p->id, ret,
                    p->errMsg[r->connMode]);
                return ret;
            }
            p->countReq[0] += vt;
            p->countSucc[0] += vs;
            p->countFail[0] += (vt - vs);

        } else {
            ctx = newAsyncContex(p, r, v);
            ret = GmcBatchExecuteAsync(r->ctxBatch[p->offset], AsyncFunVertexBatchExe, ctx);
            if (ret != 0) {
                resetAsyncContex(ctx);
                printf("[tid:%02d] [exeDMLKV] key:%d resultCode:%d, total:%u, success:%u.\n", p->id, v, ret, vt, vs);
            }
        }
        r->countBatch[p->offset] = 0;
        return ret;
    }
    if (r->oprCmd == DML_KV_SET) {
        sizeV = setValIntoKV(p, r->inputV[p->offset], &r->kvV, v);
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            ret = GmcKvSet(stmt, r->inputI[p->offset], sizeK, r->inputV[p->offset], sizeV);
        } else {
            tuple.key = r->inputI[p->offset];
            tuple.keyLen = sizeK;
            tuple.value = r->inputV[p->offset];
            tuple.valueLen = sizeV;
            ret = GmcKvSetAsync(stmt, &tuple, AsyncFunKvSet, ctx);
        }
    } else if (r->oprCmd == DML_KV_GET) {
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            ret = GmcKvGetValueSize(stmt, r->inputI[p->offset], sizeK, &sizeV);
            if (sizeV > p->outSize) {
                extendBufSize(sizeV, &p->outSize, &p->outStr, false);
            }
            ret = GmcKvGet(stmt, r->inputI[p->offset], sizeK, p->outStr, &sizeV);
            if (ret == 0 && g_logLevel > 0) {
                p->outStr[sizeV] = '\0';
                printf("[kv] key:%s, value:%u|%s\n", r->inputI[p->offset], sizeV, p->outStr);
            }
        } else {
            resetAsyncContex(ctx);
            ret = 0;
            printf("[tid:%02d] [exeDMLKV] read %s in async mode.\n", p->id, r->metaName);
        }
    } else if (r->oprCmd == DML_KV_DEL) {
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            ret = GmcKvRemove(stmt, r->inputI[p->offset], sizeK);
        } else {
            ret = GmcKvRemoveAsync(stmt, r->inputI[p->offset], sizeK, AsyncFunKvDel, ctx);
        }
    } else if (r->oprCmd == DML_KV_EXIST) {
        if (r->connMode == GMC_CONN_TYPE_SYNC) {
            bool isExist;
            ret = GmcKvIsExist(stmt, r->inputI[p->offset], sizeK, &isExist);
            if (ret == 0 && g_logLevel > 0) {
                printf("[kv] key:%s, isExist:%u\n", r->inputI[p->offset], isExist);
            }
        } else {
            resetAsyncContex(ctx);
            ret = 0;
            printf("[tid:%02d] [exeDMLKV] GmcIsKvExist in async mode, uncertain function.\n", p->id);
        }
    } else {
        ret = -1;
        printf("[tid:%02d] [exeDMLKV] invalid action:%d.\n", p->id, r->oprCmd);
    }
    if (r->connMode == GMC_CONN_TYPE_ASYNC) {
        if (ret != 0) {
            printf("[tid:%02d] [exeDMLKV] tableName:%s, oprCmd:%d, ret:%d.\n", p->id, r->metaName, r->oprCmd, ret);
            resetAsyncContex(ctx);
        }
        return ret;
    }
    if (ret == 0) {
        ++p->countSucc[0];
    } else if (ret == GMERR_DATA_EXCEPTION) {
        ++p->countLost[0];
        if (g_logLevel > 0) {
            printf("[tid:%02d] [exeDMLKV] key:%s is not existant.\n", p->id, r->inputI[p->offset]);
        }
        ret = 0;
    } else if (ret == GMERR_UNIQUE_VIOLATION || ret == GMERR_DUPLICATE_OBJECT) {
        ++p->countLost[0];
        if (g_logLevel > 0) {
            printf("[tid:%02d] [exeDMLKV]  duplicate key:%s, errCode:%d, errMsg:%s\n", p->id, r->inputI[p->offset], ret,
                p->errMsg[r->connMode]);
        }
        ret = 0;
    } else if (ret == GMERR_INTERNAL_ERROR || ret == GMERR_LOCK_NOT_AVAILABLE) {
        --p->countReq[0];
    } else {
        ++p->countFail[0];
        printf("[tid:%02d] [exeDMLKV] key:%s resultCode:%d.\n", p->id, r->inputI[p->offset], ret);
    }
    return ret;
}

int exeDMLEntry(perfContent *p, runUnit *r, int v1, int v2)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    GmcStmtT *stmt = r->stmt[p->offset];
    GmcConnT *conn = p->conn[r->connMode];
    int i, v, transMode, ret;
    int batchNum = r->batchNum;
    int transNum = r->transNum;
    int transCount = 0;
    int retryNum = 0;
    int ops = r->ops;
    int64_t lVal;
    randomVal *rval = (randomVal *)&lVal;
    GmcTxConfigT txCfg;
    txCfg.readOnly = false;
    txCfg.type = GMC_TX_ISOLATION_COMMITTED;
    txCfg.transMode = GMC_TRANS_USED_IN_CS;
    metaDataLabelVertex *t = (metaDataLabelVertex *)r->metadataT;

    if (r->isPrepare[p->offset] == false) {
        if (r->objType == OBJ_TYPE_VERTEX) {
            if (r->schVersion == -1) {
                ret = GmcPrepareStmtByLabelName(stmt, r->metaName, g_oprVertexType[r->oprCmd - DML_VERTEX_INSERT]);
            } else {
                ret = GmcPrepareStmtByLabelNameWithVersion(stmt, r->metaName, r->schVersion,
                    g_oprVertexType[r->oprCmd - DML_VERTEX_INSERT]);
            }
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLEntry] GmcPrepareStmtByLabelName(%s) failed, ret = %d\n", p->id, r->metaName,
                    ret);
                return ret;
            }
            if (r->oprCmd == DML_VERTEX_INSERT || r->oprCmd == DML_VERTEX_INSERT_RES ||
                r->oprCmd == DML_VERTEX_REPLACE || r->oprCmd == DML_VERTEX_REPLACE_RES) {
                ret = setFullVertexMain(p, r);
                if (ret != 0) {
                    printf("[tid:%02d] [exeDMLEntry] setFullVertexMain(%s) failed.\n", p->id, r->metaName);
                    return ret;
                }
            } else if (r->oprCmd == DML_VERTEX_UPDATE || r->oprCmd == DML_VERTEX_MERGE) {
                ret = setDeltaVertexMain(p, r);
                if (ret != 0) {
                    printf("[tid:%02d] [exeDMLEntry] setDeltaVertexMain(%s) failed.\n", p->id, r->metaName);
                    return ret;
                }
            }
            if (r->oprCmd == DML_VERTEX_SELECT && r->vecSize != 1) {
                ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &r->vecSize, sizeof(r->vecSize));
                if (ret != 0) {
                    printf("[tid:%02d] [exeDMLEntry] GmcSetStmtAttr(GMC_STMT_ATTR_PRE_FETCH_ROWS:%s:%d) failed, "
                           "errCode:%d, errMsg:%s\n",
                        p->id, r->metaName, r->vecSize, ret, p->errMsg[r->connMode]);
                    return ret;
                }
            }
            if (r->oprCmd == DML_VERTEX_SELECT && r->limitSize > 0) {
                ret = GmcSetScanLimit(stmt, r->limitSize);
                if (ret != 0) {
                    printf("[tid:%02d] [exeDMLEntry] GmcSetScanLimit(%s:%d) failed, errCode:%d, errMsg:%s\n", p->id,
                        r->metaName, r->limitSize, ret, p->errMsg[r->connMode]);
                    return ret;
                }
            }
        } else {
            ret = GmcKvPrepareStmtByLabelName(stmt, r->metaName);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLEntry] GmcKvPrepareStmtByLabelName(%s) failed, errCode:%d, errMsg:%s\n",
                    p->id, r->metaName, ret, p->errMsg[r->connMode]);
                return ret;
            }
        }
        r->isPrepare[p->offset] == true;
    }
    struct timeval tmBegin, tmEnd;
    uint64_t opsBegin, opsEnd, cycleBegin, cycleEnd;
    gettimeofday(&tmBegin, NULL);
    opsBegin = getCyclesUS();
    cycleBegin = getAndCalCpuCycles();
    uint64_t opsBegin1 = opsBegin;
    for (i = v1; i < v2; ++i) {
        if (r->ops > 0) {
            if (ops >= r->ops) {
                opsEnd = getCyclesUS();
                if (opsEnd - opsBegin < CYCLES_PER_S) {
                    --i;
                    usleep(200);
                    continue;
                }
                ops = 1;
                opsBegin = opsEnd;
            } else {
                ++ops;
            }
        }
        if (r->isolation == 2 || r->isolation == 3) {
            if (retryNum == 0) {
                v = TEST_RANDOM(p->randomSeed) % (v2 - v1) + v1;
            }
        } else {
            v = i;
        }
        if (r->transNum > 0) {
            if (transCount == 0) {
                ret = GmcTransStart(conn, &txCfg);
                if (ret != 0) {
                    printf("[tid:%02d] [exeDMLEntry] GmcTransStart failed, ret = %d\n", p->id, ret);
                    break;
                }
            }
            ++transCount;
        }
        if (i + 1 == v2) {
            if (r->objType == OBJ_TYPE_VERTEX) {
                ret = exeDMLVertex(p, r, v, true);
            } else {
                ret = exeDMLKV(p, r, v, true);
            }
        } else {
            if (r->objType == OBJ_TYPE_VERTEX) {
                ret = exeDMLVertex(p, r, v);
            } else {
                ret = exeDMLKV(p, r, v);
            }
        }
        if (ret != 0) {
            if (ret == GMERR_INTERNAL_ERROR || ret == GMERR_LOCK_NOT_AVAILABLE) {
                if (ret == GMERR_INTERNAL_ERROR &&
                    (r->oprCmd == DML_VERTEX_INSERT || r->oprCmd == DML_VERTEX_INSERT_RES ||
                        r->oprCmd == DML_VERTEX_REPLACE || r->oprCmd == DML_VERTEX_REPLACE_RES)) {
                    ret = GmcResetVertex(stmt, false);
                    if (ret != 0) {
                        printf("[tid:%02d] [exeDMLEntry] GmcResetVertex failed, ret = %d.\n", p->id, ret);
                        return ret;
                    }
                    ret = setFullVertexMain(p, r);
                    if (ret != 0) {
                        printf("[tid:%02d] [exeDMLEntry] setFullVertexMain(%s) failed.\n", p->id, r->metaName);
                        return ret;
                    }
                }
                ++retryNum;
                if (retryNum < 5) {
                    usleep(1000);
                    --i;
                    if (r->ops > 0) {
                        --ops;
                    }
                    continue;
                }
            }
        }
        retryNum = 0;
        if (r->transNum > 0) {
            if (ret == GMERR_TRANSACTION_ROLLBACK || (transCount == transNum || (i + 1 == v2))) {
                lVal = 0;
                transMode = r->transMode % 10;
                if (transMode == 2) {
                    lVal = TEST_RANDOM(p->randomSeed);
                    transMode = rval->v3 % 2;
                }
                if (ret == GMERR_TRANSACTION_ROLLBACK) {
                    transMode = 0;
                }
                if (transMode == 1) {
                    ret = GmcTransCommit(conn);
                } else {
                    ret = GmcTransRollBack(conn);
                }
                if (ret != 0) {
                    printf("[tid:%02d] [exeDMLEntry] do transaction failed, transMode = %d[0:rollback, 1:commit], ret "
                           "= %d.\n",
                        p->id, transMode, ret);
                    break;
                }
                transNum = r->transNum;
                if (r->transMode >= 10) {
                    transNum = rval->v4 % r->transNum + 1;
                }
                transCount = 0;
            }
        }
    }
    i = 0;
    while (r->connMode == GMC_CONN_TYPE_ASYNC && p->requestNum > p->repondNum && i < 500) {
        ++i;
        usleep(10000);
    }
    cycleEnd = getAndCalCpuCycles();
    gettimeofday(&tmEnd, NULL);
    uint64_t s1, s2, s3;
    s1 = (tmEnd.tv_sec - tmBegin.tv_sec) * 1000000 + (tmEnd.tv_usec - tmBegin.tv_usec);
    if (s1 > 0) {
        s2 = ((double)(v2 - v1) / s1) * 1000000;
        s3 = (cycleEnd - cycleBegin) / (v2 - v1);
    } else {
        s2 = 0;
        s3 = 0;
    }
    p->speed = s2;
    p->latency = s1 / 1000;
    if (r->objType == OBJ_TYPE_VERTEX) {
        printf("[tid:%02d] [%s] request:%llu, success:%llu, duplicate/lost:%llu, failed:%llu, cost_time:%lds|%06ldus, "
               "ops:%ld, cpu_cycles:%ld, sn:%llu|%llu:%lu:%lu|%llu:%lu:%lu|%llu|%llu|%llu\n",
            p->id, g_oprVertexName[r->oprCmd - DML_VERTEX_INSERT], p->countReq[0], p->countSucc[0], p->countLost[0],
            p->countFail[0], s1 / 1000000, s1 % 1000000, s2, s3, p->countSnI[0], p->countSnR1[0], p->countSnR2[0],
            p->countSnR3[0], p->countSnM1[0], p->countSnM2[0], p->countSnM3[0], p->countSnU[0], p->countSnD[0],
            p->countSnAged[0]);
    } else {
        printf("[tid:%02d] [%s] request:%llu, success:%llu, duplicate/lost:%llu, failed:%llu, cost_time:%lds|%06ldus, "
               "ops:%ld, cpu_cycles:%ld, sn(set/del):%llu|%llu\n",
            p->id, g_oprKVName[r->oprCmd - DML_KV_SET], p->countReq[0], p->countSucc[0], p->countLost[0],
            p->countFail[0], s1 / 1000000, s1 % 1000000, s2, s3, p->countSnR1[0], p->countSnD[0]);
    }
    return ret;
}

int getNewStmt(perfContent *p, runUnit *r)
{
    int ret;
    GmcConnTypeE connMode = (GmcConnTypeE)r->connMode;
    if (connMode == GMC_CONN_TYPE_SUB) {
        connMode = GMC_CONN_TYPE_SYNC;
    }
    if (p->conn[connMode] == NULL) {
        ret = perfGmcConnect(p->id, connMode);
        if (ret != 0) {
            printf("[tid:%02d] [getNewStmt] perfGmcConnect failed, ret = %d\n", p->id, ret);
            return ret;
        }
        sceneEntry *s = g_sceneEntry + p->sceneId;
        ret = perfSetNameSpace(p->id, connMode, s->nameSpace);
        if (ret != 0) {
            printf(
                "[tid:%02d] [getNewStmt] perfSetNameSpace failed, namespace:%d, ret = %d\n", p->id, s->nameSpace, ret);
            return ret;
        }
    }
    ret = GmcAllocStmt(p->conn[connMode], &r->stmt[p->offset]);
    if (ret != 0) {
        printf("[tid:%02d] [getNewStmt] GmcAllocStmt failed, ret = %d\n", p->id, ret);
        return ret;
    }
    return 0;
}

int resetTheDataFeature(runUnit *r, int offset, char *str, fieldDefine *f, int n)
{
    char strText[COMM_STR_SIZE];
    snprintf(strText, sizeof(strText), "%s", str);
    metaDataLabelVertex *t = (metaDataLabelVertex *)r->metadataT;
    char *p1, *p2, *left;
    fieldDefine *v;
    int i, j, len;
    p1 = strtok_r(strText, ",", &left);
    while (p1) {
        len = strlen(p1);
        for (j = 0; j < len; ++j) {
            if (p1[j] == '=') {
                p1[j] = '\0';
                p2 = p1 + j + 1;
                break;
            }
        }
        if (j == len) {
            printf("[resetTheDataFeature] invalid format, character(=) is request, str:%s\n", str);
            return -1;
        }
        for (i = 0; i < n; ++i) {
            if (f[i].f == NULL) {
                for (j = 0; j < t->fieldNum; ++j) {
                    if (0 == strcmp(t->fs[j].name, p1)) {
                        f[i].f = t->fs + j;
                        break;
                    }
                }
                if (j == t->fieldNum) {
                    printf("[resetTheDataFeature] invalid field:%s, str:%s\n", p1, str);
                    exit(1);
                }
            }
            if (0 == strcmp(f[i].f->name, p1)) {
                v = f + i;
                f[i].isSet = true;
                break;
            }
        }
        if (i == n) {
            printf("[resetTheDataFeature] wrong field:%s, str:%s\n", p1, str);
            exit(1);
        }
        if (p2[0] == '*') {
            v->varType = FIELD_VAR_SERIAL;
            v->varVal = atol(p2 + 1);
            p1 = strtok_r(NULL, ",", &left);
            continue;
        }
        if (p2[0] == '[') {
            v->varType = FIELD_VAR_SERIAL;
            v->varBegin = atoi(p2 + 1);
            len = strlen(p2);
            for (j = 1; j < len; ++j) {
                if (p2[j] == '-') {
                    v->varInc = atoi(p2 + j + 1) - v->varBegin;
                    break;
                }
            }
            if (j == len) {
                printf("[resetTheDataFeature] invalid field:%s, expect [min-max]:charPre:digital str:%s\n", p2, str);
                exit(1);
            }
            while (j < len && p2[j] != ':') {
                ++j;
            }
            if (j == len) {
                printf("[resetTheDataFeature] invalid field:%s, expect [min-max]:charPre:digital str:%s\n", p2, str);
                exit(1);
            }
            p2 = p2 + j + 1;
            len = strlen(p2);
            j = 0;
            while (j < len && p2[j] != ':') {
                ++j;
            }
            if (j == len) {
                printf("[resetTheDataFeature] invalid field:%s, expect [min-max]:charPre:digital str:%s\n", p2, str);
                exit(1);
            }
            v->varPreText = (char *)malloc(j + 1);
            if (v->varPreText == NULL) {
                printf("[resetTheDataFeature] malloc memory(size:%d) failed, str:%s\n", j + 1, str);
                exit(1);
            }
            p2[j] = '\0';
            snprintf(v->varPreText, j + 1, "%s", p2);
            v->varVal = atol(p2 + j + 1);
            p1 = strtok_r(NULL, ",", &left);
            continue;
        }
        if (0 == strcmp(p2, "null")) {
            v->varType = FIELD_VAR_NULL;
            p1 = strtok_r(NULL, ",", &left);
            continue;
        }
        if (0 == strncmp(p2, "random(", 7)) {
            v->varType = FIELD_VAR_RANDOM;
            v->varInc = atoi(p2 + 7);
            p2 = p2 + 8;
            len = strlen(p2);
            for (j = 0; j < len; ++j) {
                if (p2[j] == ':') {
                    break;
                }
            }
            if (j != len) {
                v->varVal = atol(p2 + j + 1);
            }
            p1 = strtok_r(NULL, ",", &left);
            continue;
        }
        // field=min-max  field=abcd:1234356
        len = strlen(p2);
        for (j = 0; j < len; ++j) {
            if (p2[j] != '-' && p2[j] != ':') {
                continue;
            }
            if (p2[j] == '-') {
                v->varType = FIELD_VAR_RANGE;
                v->varVal = atol(p2);
                v->varInc = atol(p2 + j + 1) - v->varVal;
                if (v->varInc <= 0) {
                    v->varInc = 1;
                }
            } else {
                v->varType = FIELD_VAR_SERIAL;
                v->varPreText = (char *)malloc(j + 1);
                if (v->varPreText == NULL) {
                    printf("[resetTheDataFeature] malloc memory(size:%d) failed, str:%s\n", j + 1, str);
                    return -1;
                }
                p2[j] = '\0';
                snprintf(v->varPreText, j + 1, "%s", p2);
                v->varVal = atol(p2 + j + 1);
                v->varBegin = strlen(v->varPreText) + 8;
            }
            break;
        }
        if (j == len) {
            v->varType = FIELD_VAR_CONST;
            if (p2[0] >= '0' && p2[0] <= '9') {
                v->varVal = atol(p2);
            } else {
                v->varPreText = (char *)malloc(len + 1);
                if (v->varPreText == NULL) {
                    printf("[resetTheDataFeature] malloc memory(size:%d) failed, str:%s\n", len + 1, str);
                    return -1;
                }
                snprintf(v->varPreText, len + 1, "%s", p2);
                v->varBegin = strlen(v->varPreText);
            }
        }
        p1 = strtok_r(NULL, ",", &left);
    }
    return 0;
}

char *prepareInputBuf(runUnit *r, fieldDefine *f, int n, int *position)
{
    *position = -1;
    int i, j, ret;
    int sum = 0;
    fieldDefine *prev;
    // var
    for (i = 0; i < n; ++i) {
        if (f[i].varType <= FIELD_VAR_NULL) {
            continue;
        }
        f[i].varOffset = sum;
        if (f[i].f->isFixed) {
            sum += f[i].f->size;
            if (f[i].f->dataType == DB_DATATYPE_FIXED) {
                sum += 1;
            }
        } else {
            if (f[i].varBegin == 0) {
                if (r->varMinSize == r->varMaxSize) {
                    if (r->varMinSize == 0) {
                        f[i].varBegin = 32;
                    } else {
                        f[i].varBegin = r->varMinSize;
                    }
                    if (f[i].varBegin >= f[i].f->size) {
                        f[i].varBegin = f[i].f->size - 1;
                    }
                    f[i].varInc = 0;
                } else {
                    f[i].varBegin = r->varMinSize;
                    f[i].varInc = r->varMaxSize - r->varMinSize;
                    if (f[i].f->size - 1 < r->varMaxSize) {
                        if (f[i].f->size > 8) {
                            f[i].varBegin = f[i].f->size - 8 - 1;
                            f[i].varInc = 8;
                        } else {
                            f[i].varBegin = 1;
                            f[i].varInc = f[i].f->size - 1 - 1;
                        }
                    }
                }
            }
            sum += (f[i].varBegin + f[i].varInc + 1);
        }
        if (*position == -1) {
            *position = i;
        } else {
            prev->next = f + i;
        }
        prev = f + i;
    }
    // set field
    for (i = 0; i < n; ++i) {
        if (f[i].varType >= FIELD_VAR_NULL || f[i].isSet == false) {
            continue;
        }
        f[i].varOffset = sum;
        if (f[i].f->isFixed) {
            sum += f[i].f->size;
            if (f[i].f->dataType == DB_DATATYPE_FIXED) {
                sum += 1;
            }
        } else {
            sum += (f[i].varBegin + 1);
        }
    }
    // not set, use default
    int maxVal = 0;
    int actualVal = 0;
    int sLen = 0;
    for (i = 0; i < n; ++i) {
        if (f[i].varType >= FIELD_VAR_NULL || f[i].isSet == true) {
            continue;
        }
        f[i].useCommon = true;
        if (f[i].f->dataType == DB_DATATYPE_STRING) {
            f[i].varOffset = sum;
            if (f[i].varBegin == 0) {
                if (r->varMinSize == r->varMaxSize) {
                    if (r->varMinSize == 0) {
                        f[i].varBegin = 32;
                    } else {
                        f[i].varBegin = r->varMinSize;
                    }
                    if (f[i].varBegin >= f[i].f->size) {
                        f[i].varBegin = f[i].f->size - 1;
                    }
                    f[i].varInc = 0;
                } else {
                    f[i].varBegin = r->varMinSize;
                    f[i].varInc = r->varMaxSize - r->varMinSize;
                    if (f[i].f->size - 1 < r->varMaxSize) {
                        if (f[i].f->size > 8) {
                            f[i].varBegin = f[i].f->size - 8 - 1;
                            f[i].varInc = 8;
                        } else {
                            f[i].varBegin = 1;
                            f[i].varInc = f[i].f->size - 1 - 1;
                        }
                    }
                }
            }
            if (sLen < f[i].varBegin + f[i].varInc) {
                sLen = f[i].varBegin + f[i].varInc + 1;
            }
            continue;
        }
        f[i].varOffset = getFieldOffsetFromCommStr(f[i].f->dataType);
        if (f[i].f->isFixed) {
            actualVal = f[i].f->size;
        } else {
            if (f[i].varBegin == 0) {
                if (r->varMinSize == r->varMaxSize) {
                    if (r->varMinSize == 0) {
                        f[i].varBegin = 32;
                    } else {
                        f[i].varBegin = r->varMinSize;
                    }
                    if (f[i].varBegin >= f[i].f->size) {
                        f[i].varBegin = f[i].f->size - 1;
                    }
                    f[i].varInc = 0;
                } else {
                    f[i].varBegin = r->varMinSize;
                    f[i].varInc = r->varMaxSize - r->varMinSize;
                    if (f[i].f->size - 1 < r->varMaxSize) {
                        if (f[i].f->size > 8) {
                            f[i].varBegin = f[i].f->size - 8 - 1;
                            f[i].varInc = 8;
                        } else {
                            f[i].varBegin = 1;
                            f[i].varInc = f[i].f->size - 1 - 1;
                        }
                    }
                }
            }
            actualVal = f[i].varBegin + f[i].varInc;
        }
        actualVal += 1;
        if (actualVal > maxVal) {
            maxVal = actualVal;
            ret = expandFieldBuf(maxVal);
            if (ret != 0) {
                printf(
                    "[prepareInputBuf] expandFieldBuf failed, oldSize:%d, newSize:%d.\n", g_commFieldLen, maxVal);
                return NULL;
            }
        }
    }
    sum = sum + sLen + 4;
    char *str = (char *)malloc(sum);
    if (str == NULL) {
        printf("[prepareInputBuf] malloc memory(%d) for input_str failed.\n", sum + 4);
        return NULL;
    }
    memset(str, 0, sum);
    // set field
    for (i = 0; i < n; ++i) {
        if (f[i].useCommon == true) {
            continue;
        }
        setintoFieldValue(str, f + i, 0, true);
    }
    for (i = 0; i < n; ++i) {
        if (f[i].useCommon == true && f[i].f->dataType == DB_DATATYPE_STRING) {
            for (j = 0; j < sLen; ++j) {
                str[f[i].varOffset + j] = 'A' + j % 26;
            }
            str[f[i].varOffset + sLen] = '\0';
            break;
        }
    }
    return str;
}

int generateVarAndData(perfContent *p, runUnit *r)
{
    int i, len, sum, ret;
    fieldDefine *f;
    int offset = p->offset;
    DmVlIndexLabelT *x = (DmVlIndexLabelT *)r->metadataI;
    metaDataLabelVertex *t = (metaDataLabelVertex *)r->metadataT;
    if (x->idxLabelBase.indexType == ART_INDEX_LOCAL && r->range) {
        return 0;
    }
    int indexType = 0;  // 1:LPMV4,  2:LPMV6
    do {
        if (x->idxLabelBase.indexType == LPM4_INDEX) {
            indexType = 1;
            break;
        }
        if (x->idxLabelBase.indexType == LPM6_INDEX) {
            indexType = 2;
            break;
        }
        if (x->idxLabelBase.indexConstraint != PRIMARY) {
            break;
        }
        for (i = 0; i < t->secIdxNum; ++i) {
            if (t->secIdx[i].idxLabelBase.indexType == LPM4_INDEX) {
                indexType = 1;
                break;
            }
            if (t->secIdx[i].idxLabelBase.indexType == LPM6_INDEX) {
                indexType = 2;
                break;
            }
        }
    } while (0);
    if (r->oprCmd == DML_VERTEX_MERGE || r->oprCmd == DML_VERTEX_UPDATE || r->oprCmd == DML_VERTEX_DELETE ||
        r->oprCmd == DML_VERTEX_SELECT || r->oprCmd == DDL_EDGE_SELECT || r->oprCmd == DDL_CHECKBILL_UPDATE_VERSION) {
        if (r->isFilter == false) {
            f = (fieldDefine *)malloc(sizeof(fieldDefine) * x->propeNum);
            if (f == NULL) {
                printf("generateVarAndDat malloc mem(%ld) for f(index) failed.\n", sizeof(fieldDefine) * x->propeNum);
                return -1;
            }
            r->varI[offset] = f;
            r->varILen = x->propeNum;
            memset(f, 0, sizeof(fieldDefine) * x->propeNum);
            for (i = 0; i < r->varILen; ++i) {
                if (r->dataRule != DR_SQL_ONLY) {
                    f[i].varType = FIELD_VAR_SERIAL;
                }
                if (f[i].f->isFixed == false) {
                }
                if (indexType > 0) {
                    if (i != 2) {
                        f[i].varType = FIELD_VAR_CONST;
                        f[i].varVal = i;
                        f[i].isSet = true;
                    }
                    if (i == 3) {
                        if (indexType == 1) {
                            f[i].varVal = 32;
                        } else {
                            f[i].varVal = 128;
                        }
                    }
                }
                f[i].f = x->properties + i;
                f[i].next = NULL;
            }
            if (r->keyStr != NULL) {
                ret = resetTheDataFeature(r, offset, r->keyStr, f, r->varILen);
                if (ret != 0) {
                    printf("generateVarAndData.resetTheDataFeature failed, ret = %d.\n", ret);
                    return ret;
                }
            }
            r->inputI[offset] = prepareInputBuf(r, f, r->varILen, &sum);
            if (r->inputI[offset] == NULL) {
                printf("generateVarAndData.prepareInputBuf failed, sum = %d.\n", sum);
                return -1;
            }
            if (sum >= 0 && sum < r->varILen) {
                r->firstI[offset] = f + sum;
            }
        }
        if (r->oprCmd == DML_VERTEX_DELETE || r->oprCmd == DML_VERTEX_SELECT || r->oprCmd == DDL_EDGE_SELECT ||
            r->oprCmd == DDL_CHECKBILL_UPDATE_VERSION) {
            return 0;
        }
    }
    r->varV[offset] = NULL;
    sum = 1;
    if (r->verStr) {
        len = strlen(r->verStr);
        for (i = 0; i < len; ++i) {
            if (r->verStr[i] == ',') {
                ++sum;
            }
        }
    }
    if (r->oprCmd == DML_VERTEX_UPDATE || (r->oprCmd == DML_VERTEX_MERGE && r->verStr)) {
        f = (fieldDefine *)malloc(sizeof(fieldDefine) * sum);
        if (f == NULL) {
            printf("generateVarAndDat malloc mem(%ld) for f(update-vertex) failed.\n", sizeof(fieldDefine) * sum);
            return -1;
        }
        r->varV[offset] = f;
        r->varVLen = sum;
        memset(f, 0, sizeof(fieldDefine) * sum);
        if (r->verStr) {
            ret = resetTheDataFeature(r, offset, r->verStr, f, sum);
            if (ret != 0) {
                printf("generateVarAndData.resetTheDataFeature failed, ret = %d.\n", ret);
                return ret;
            }
        } else {
            f->isSet = true;
            f->varVal = 1999;
            f->f = t->fs + t->fieldNum - 1;
        }
        r->inputV[offset] = prepareInputBuf(r, f, sum, &len);
        if (r->inputV[offset] == NULL) {
            printf("generateVarAndData.prepareInputBuf[inputV] failed, sum = %d.\n", len);
            return -1;
        }
        if (len >= 0 && len < sum) {
            r->firstV[offset] = f + len;
        }
        return 0;
    }
    if (r->dataRule == DR_FULL_KEY_ALLIDX || r->dataRule == DR_FULL_KEY_UNIQUEIDX || r->dataRule == DR_FULL_ALL) {
        f = (fieldDefine *)malloc(sizeof(fieldDefine) * t->fieldNum);
        if (f == NULL) {
            printf("generateVarAndDat malloc mem(%ld) for f(vertex) failed.\n", sizeof(fieldDefine) * t->fieldNum);
            return -1;
        }
        memset(f, 0, sizeof(fieldDefine) * t->fieldNum);
        r->varV[offset] = f;
        r->varVLen = t->fieldNum;
        for (i = 0; i < t->fieldNum; ++i) {
            f[i].f = t->fs + i;
            if ((r->dataRule == DR_FULL_KEY_ALLIDX && f[i].f->nameLen > 0) ||
                (r->dataRule == DR_FULL_KEY_UNIQUEIDX && (f[i].f->nameLen == 1 || f[i].f->nameLen == 2)) ||
                r->dataRule == DR_FULL_ALL) {
                f[i].varType = FIELD_VAR_SERIAL;
            }
        }
        sum = t->fieldNum;
        if (indexType > 0) {
            for (i = 0; i < x->propeNum; ++i) {
                for (int j = 0; j < t->fieldNum; ++j) {
                    if (0 == strcmp(x->properties[i].name, f[j].f->name)) {
                        if (i != 2) {
                            f[i].varType = FIELD_VAR_CONST;
                            f[i].varVal = i;
                            f[i].isSet = true;
                        }
                        if (i == 3) {
                            if (indexType == 1) {
                                f[i].varVal = 32;
                            } else {
                                f[i].varVal = 128;
                            }
                        }
                    }
                }
            }
        }
    } else {
        if (r->dataRule == DR_SQL_KEY_ALLIDX) {
            for (i = 0; i < t->fieldNum; ++i) {
                if (t->fs[i].nameLen > 0) {
                    ++sum;
                }
            }
        }
        f = (fieldDefine *)malloc(sizeof(fieldDefine) * sum);
        if (f == NULL) {
            printf("generateVarAndDat malloc mem(%ld) for f(vertex) failed.\n", sizeof(fieldDefine) * sum);
            return -1;
        }
        sum = 0;
        if (r->dataRule == DR_SQL_KEY_ALLIDX) {
            for (i = 0; i < t->fieldNum; ++i) {
                if (t->fs[i].nameLen > 0) {
                    f[sum].f = t->fs + i;
                    f[sum].varType = FIELD_VAR_SERIAL;
                    ++sum;
                }
            }
        }
        if (indexType > 0) {
            for (i = 0; i < x->propeNum; ++i) {
                for (int j = 0; j < t->fieldNum; ++j) {
                    if (0 == strcmp(x->properties[i].name, f[j].f->name)) {
                        if (i != 2) {
                            f[i].varType = FIELD_VAR_CONST;
                            f[i].varVal = i;
                            f[i].isSet = true;
                        }
                        if (i == 3) {
                            if (indexType == 1) {
                                f[i].varVal = 32;
                            } else {
                                f[i].varVal = 128;
                            }
                        }
                    }
                }
            }
        }
        if (r->verStr) {
            int j, k;
            char ch;
            char *pos = r->verStr;
            len = strlen(r->verStr);
            for (i = 0; i < len; ++i) {
                if (r->verStr[i] != '=' && r->verStr[i] != ',') {
                    continue;
                }
                if (r->verStr[i] == ',') {
                    pos = r->verStr + i + 1;
                    continue;
                }
                ch = r->verStr[i];
                r->verStr[i] = '\0';
                for (j = 0; j < sum; ++j) {
                    if (0 == strcmp(pos, f[j].f->name)) {
                        break;
                    }
                }
                if (j < sum) {
                    r->verStr[i] = ch;
                    continue;
                }
                for (j = 0; j < t->fieldNum; ++j) {
                    if (0 == strcmp(pos, t->fs[j].name)) {
                        f[sum].f = t->fs + j;
                        ++sum;
                        break;
                    }
                }
                r->verStr[i] = ch;
            }
        }
        r->varV[offset] = f;
        r->varVLen = sum;
    }
    if (r->verStr) {
        ret = resetTheDataFeature(r, offset, r->verStr, f, sum);
        if (ret != 0) {
            printf("generateVarAndData.resetTheDataFeature failed, ret = %d.\n", ret);
            return ret;
        }
    }
    r->inputV[offset] = prepareInputBuf(r, f, sum, &len);
    if (r->inputV[offset] == NULL) {
        printf("generateVarAndData.prepareInputBuf[inputV] failed, sum = %d.\n", len);
        return -1;
    }
    if (len >= 0 && len < sum) {
        r->firstV[offset] = f + len;
    }
    return 0;
}

int getMetadataVertex(perfContent *p, runUnit *r)
{
    GmcStmtT *stmt;
    CltCataLabelT *cltCata;
    bool releaseFlag[2] = {0};
    bool isView = false;
    if (0 == strncmp(r->metaName, "V$", 2)) {
        isView = true;
    }
    fixCompleteName(r->metaName);
    int ret = -1;
    do {
        r->metadataT = GetMetaDataFromCache(r->metaName);
        if (r->metadataT) {
            ret = 0;
            break;
        }
        if (p->conn[0]) {
            stmt = r->stmt[p->offset];
        } else {
            if (p->conn[0] == NULL) {
                ret = perfGmcConnect(p->id, GMC_CONN_TYPE_SYNC);
                if (ret != 0) {
                    printf("[tid:%02d] [getMetadataVertex] perfGmcConnect(sync) failed, ret = %d.\n", p->id, ret);
                    break;
                }
            }
            releaseFlag[0] = true;
            sceneEntry *s = g_sceneEntry + p->sceneId;
            ret = perfSetNameSpace(p->id, GMC_CONN_TYPE_SYNC, s->nameSpace);
            if (ret != 0) {
                printf("[tid:%02d] [getMetadataVertex] perfSetNameSpace(%s) failed, ret = %d.\n", p->id, s->nameSpace,
                    ret);
                break;
            }
            ret = GmcAllocStmt(p->conn[0], &stmt);
            if (ret != 0) {
                printf("[tid:%02d] [getMetadataVertex] GmcAllocStmt failed, ret = %d.\n", p->id, ret);
                break;
            }
            releaseFlag[1] = true;
        }
        if (r->schVersion == -1) {
            ret = GmcPrepareStmtByLabelName(stmt, r->metaName, GMC_OPERATION_SCAN);
        } else {
            ret = GmcPrepareStmtByLabelNameWithVersion(stmt, r->metaName, r->schVersion, GMC_OPERATION_SCAN);
        }
        if (ret != 0) {
            printf(
                "[tid:%02d] [getMetadataVertex] CltOpenVertexLabel(%s) failed, ret = %d.\n", p->id, r->metaName, ret);
            break;
        }
        if (isView == false) {
            cltCata = ((CltOperVertexT *)(stmt->operationContext))->cltCataLabel;
            r->metadataT = genMetaDataVertex(r->metaName, cltCata->vertexLabel);
            if (r->metadataT == NULL) {
                printf("[tid:%02d] [getMetadataVertex] genMetaDataVertex(%s) failed.\n", p->id, r->metaName);
                ret = -1;
            }
        }
        // avoid close meta more than open. GmcCloseVertexLabel(cltCata);
    } while (0);
    if (releaseFlag[1]) {
        GmcFreeStmt(stmt);
    }
    if (releaseFlag[0]) {
        perfGmcDisConnect(p->id, GMC_CONN_TYPE_SYNC);
    }
    if (ret != 0 || isView) {
        return ret;
    }
    attachVertexIndexCtx(p, r);
    if (r->objType == OBJ_TYPE_EDGE || r->oprCmd == DDL_VERTEX_QUERY) {
        return 0;
    }
    return generateVarAndData(p, r);
}

int mgrCheckBill(perfContent *p, runUnit *r, int v1, int v2 = -1)
{
    if (r->oprCmd != DDL_CHECKBILL_START && r->oprCmd != DDL_CHECKBILL_STOP && r->oprCmd != DDL_CHECKBILL_QUERY &&
        r->oprCmd != DDL_CHECKBILL_UPDATE_VERSION) {
        printf("[tid:%02d] [mgrCheckBill] invalid action, r->oprCmd = %d, create/drop is request.\n", p->id, r->oprCmd);
        return -1;
    }
    GmcStmtT *stmt = r->stmt[p->offset];
    int v3 = v2;
    if (v3 == -1) {
        v3 = v1 + 1;
    }
    int i, ret;
    int partitonId = r->ops;
    if (r->ops == -1) {
        partitonId = 0xff;
    }
    DmVlIndexLabelT *d;
    if (r->oprCmd == DDL_CHECKBILL_UPDATE_VERSION) {
        ret = getMetadataVertex(p, r);
        if (ret != 0) {
            printf("[tid:%02d] [mgrCheckBill] getMetadataVertex(%s) failed, ret = %d.\n", p->id, r->metaName, ret);
            return -1;
        }
        d = (DmVlIndexLabelT *)r->metadataI;
    }
    char metaName[128];
    int idx = 0;
    uint32_t totalNum, successNum;
    for (i = v1; i < v3; ++i) {
        if (checkCompleteName(r->metaName)) {
            snprintf(metaName, sizeof(metaName), "%s", r->metaName);
        } else {
            snprintf(metaName, sizeof(metaName), "%s%05d", r->metaName, i);
        }
        if (r->oprCmd == DDL_CHECKBILL_START) {
            ret = GmcBeginCheck(stmt, metaName, partitonId);
            if (ret != 0) {
                printf("[tid:%02d] [mgrCheckBill] GmcBeginCheck(%s) failed, ret = %d.\n", p->id, metaName, ret);
            } else {
                printf("[tid:%02d] [mgrCheckBill] start check_bill successful, name = %s.\n", p->id, metaName);
            }
        } else if (r->oprCmd == DDL_CHECKBILL_STOP) {
            ret = GmcEndCheck(stmt, metaName, partitonId, false);
            if (ret != 0) {
                printf("[tid:%02d] [mgrCheckBill] GmcEndCheck(%s) failed, ret = %d.\n", p->id, metaName, ret);
            } else {
                printf("[tid:%02d] [mgrCheckBill] stop check_bill successful, name = %s.\n", p->id, metaName);
            }
        } else if (r->oprCmd == DDL_CHECKBILL_QUERY) {
            GmcCheckInfoT *info;
            ret = GmcGetCheckInfo(stmt, metaName, partitonId, &info);
            if (ret != 0) {
                printf("[tid:%02d] [mgrCheckBill] GmcGetCheckInfo(%s) failed, ret = %d.\n", p->id, metaName, ret);
            } else {
                GmcCheckStatusE status;
                ret = GmcGetCheckStatus(info, &status);
                if (ret != 0) {
                    printf(
                        "[tid:%02d] [mgrCheckBill] GmcGetCheckStatus(%s) failed, ret = %d.\n", p->id, metaName, ret);
                } else {
                    if (status == GMC_CHECK_STATUS_NORMAL) {
                        printf("[tid:%02d] [mgrCheckBill] tableName:%s, partitionId:%d, status:normal.\n", p->id,
                            metaName, partitonId);
                    } else if (status == GMC_CHECK_STATUS_CHECKING) {
                        printf("[tid:%02d] [mgrCheckBill] tableName:%s, partitionId:%d, status:checking.\n", p->id,
                            metaName, partitonId);
                    } else if (status == GMC_CHECK_STATUS_ABNORMAL) {
                        printf("[tid:%02d] [mgrCheckBill] tableName:%s, partitionId:%d, status:abnormal.\n", p->id,
                            metaName, partitonId);
                    } else {
                        printf("[tid:%02d] [mgrCheckBill] tableName:%s, partitionId:%d, status:%d.\n", p->id, metaName,
                            partitonId, status);
                    }
                }
            }
        } else {
            if (r->schVersion == -1) {
                ret = GmcPrepareStmtByLabelName(stmt, metaName, GMC_OPERATION_UPDATE_VERSION);
            } else {
                ret = GmcPrepareStmtByLabelNameWithVersion(stmt, metaName, r->schVersion, GMC_OPERATION_UPDATE_VERSION);
            }
            if (ret != 0) {
                printf("[tid:%02d] [mgrCheckBill] GmcPrepareStmtByLabelName(%s) failed, ret = %d.\n", p->id, metaName,
                    ret);
                return -1;
            }
            ret = setVertexKeyField(p, r, i);
            if (ret != 0) {
                printf("[tid:%02d] [mgrCheckBill] setVertexKeyField failed, ret = %d.\n", p->id, ret);
                return ret;
            }
            ret = GmcBatchAddDML(r->ctxBatch[p->offset], stmt);
            if (ret != 0) {
                printf("[tid:%02d] [mgrCheckBill] GmcBatchAddDML(metaName=%s,keyName=%s) failed, errCode:%d, "
                       "errMsg:%s.\n",
                    p->id, metaName, d->indexName, ret, p->errMsg[r->connMode]);
                return ret;
            }
            ++idx;
            if (idx < r->batchNum && i + 1 < v3) {
                continue;
            }
            idx = 0;
            GmcBatchRetT retBatch;
            uint32_t vt = 0;
            uint32_t vs = 0;
            ret = GmcBatchExecute(r->ctxBatch[p->offset], &retBatch);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcBatchExecute failed, errCode:%d, errMsg:%s\n", p->id, ret,
                    p->errMsg[r->connMode]);
                return ret;
            }
            ret = GmcBatchDeparseRet(&retBatch, &vt, &vs);
            if (ret != 0) {
                printf("[tid:%02d] [exeDMLVertex] GmcBatchDeparseRet failed, errCode:%d, errMsg:%s\n", p->id, ret,
                    p->errMsg[r->connMode]);
                return ret;
            }
            p->countReq[0] += vt;
            p->countSucc[0] += vs;
            p->countFail[0] += (vt - vs);
        }
    }
    if (r->oprCmd == DDL_CHECKBILL_UPDATE_VERSION) {
        printf("[tid:%02d] [mgrCheckBill] update version completely, metaName:%s, data_range:[%d,%d].\n", p->id,
            metaName, v1, v3);
    }
    return 0;
}

int getMetadataEdge(perfContent *p, runUnit *r)
{
    int ret;
    GmcResetStmt(r->stmt[p->offset]);
    GmcEdgeLabelHandle vLabel;
    if (r->connMode == GMC_CONN_TYPE_SYNC) {
        ret = GmcOpenEdgeLabelByName(r->stmt[p->offset], r->edgeName, &vLabel);
        if (ret != 0) {
            printf("[getMetadataEdge] GmcOpenVertexLabelByName failed, ret = %d.\n", ret);
            return ret;
        }
    } else {
        printf("[getMetadataEdge] uncompleted function, edge(open) + async.\n");
        exit(1);
    }
    metaDataLabelEdge *e = generateMetaDataEdge(r->edgeName, vLabel);
    if (e == NULL) {
        printf("[getMetadataEdge] generateMetaDataEdge(%s) failed.\n", r->edgeName);
        return ret;
    }
    r->metadataE = e;
    if (r->metaName[0] != '\0') {
        fixCompleteName(r->metaName);
        if (0 != strcmp(r->metaName, e->sourceName)) {
            snprintf(e->destName, sizeof(e->destName), "%s", e->sourceName);
            snprintf(e->sourceName, sizeof(e->sourceName), "%s", r->metaName);
        }
    }
    ret = GmcCloseEdgeLabel(r->stmt[p->offset], vLabel);
    if (ret != 0) {
        printf("[getMetadataEdge] GmcCloseEdgeLabel failed, ret = %d.\n", ret);
        return ret;
    }
    snprintf(r->metaName, sizeof(r->metaName), "%s", e->destName);
    ret = getMetadataVertex(p, r);
    if (ret != 0) {
        printf("[getMetadataEdge] getMetadataVertex(destLable:%s) failed.\n", e->destName);
        return ret;
    }
    e->dv = (metaDataLabelVertex *)r->metadataT;
    e->di = (DmVlIndexLabelT *)r->metadataI;
    snprintf(r->metaName, sizeof(r->metaName), "%s", e->sourceName);
    ret = getMetadataVertex(p, r);
    if (ret != 0) {
        printf("[getMetadataEdge] getMetadataVertex(sourceLable:%s) failed.\n", e->sourceName);
        return ret;
    }
    e->sv = (metaDataLabelVertex *)r->metadataT;
    e->si = (DmVlIndexLabelT *)r->metadataI;
    if (r->oprCmd == DDL_EDGE_QUERY) {
        return 0;
    }
    return generateVarAndData(p, r);
}

void AsyncFunCreateLabel(void *userData, int32_t status, const char *errMsg)
{
    if (status != 0) {
        printf("[AsyncFunCreateLabel] status = %d\n", status);
    }
}

int testDoTransaction(perfContent *p, runUnit *r)
{
    int ret = 0;
    if (p->countTrans == 0) {
        GmcTxConfigT txCfg;
        txCfg.type = GMC_TX_ISOLATION_COMMITTED;
        txCfg.transMode = GMC_TRANS_USED_IN_CS;
        metaDataLabelVertex *t = (metaDataLabelVertex *)r->metadataT;
        ret = GmcTransStart(p->conn[r->connMode], &txCfg);
    }
    ++p->countTrans;
    return ret;
}

int testEndTransaction(perfContent *p, runUnit *r, int *newTransNum = NULL)
{
    int64_t lVal = 0;
    randomVal *rval = (randomVal *)&lVal;
    int transMode = r->transMode % 10;
    if (transMode == 2) {
        lVal = TEST_RANDOM(p->randomSeed);
        transMode = rval->v3 % 2;
    }
    int ret;
    if (transMode == 1) {
        ret = GmcTransCommit(p->conn[r->connMode]);
    } else {
        ret = GmcTransRollBack(p->conn[r->connMode]);
    }
    if (newTransNum != NULL) {
        if (r->transMode >= 10) {
            if (lVal == 0) {
                lVal = TEST_RANDOM(p->randomSeed);
            }
            *newTransNum = rval->v4 % r->transNum + 1;
        } else {
            *newTransNum = r->transNum;
        }
    }
    p->countTrans = 0;
    return ret;
}

int mgrSubTable(perfContent *p, runUnit *r, int v1, int v2)
{
    if (r->oprCmd == DML_SUB_TABLE_START_ONCE && r->ops > 0) {
        // sleep(1);
        return 0;
    }
    sceneEntry *s = g_sceneEntry + p->sceneId;
    int i, j, ret, len;
    int64_t size;
    char *str;
    do {
        if (r->verStr || r->snCfg[0] == 1) {
            break;
        }
        if (r->snCfg[0] == 2) {  // read subInfo from file
            size = perfReadJsonFile(r->keyStr, &str);
            if (size < 0) {
                printf("[tid:%02d] [mgrSubTable] perfReadJsonFile(%s) failed.\n", p->id, r->keyStr);
                p->ops = 2;
                return 3;
            }
            for (i = 0; i < size; ++i) {
                if (i + 16 < size && 0 == strncmp(str + i, "\"label_name\"", 16)) {
                    i = i + 16;
                    break;
                }
            }
            if (i == size) {
                printf("[tid:%02d] [mgrSubTable] get key(label_name) from (%s) failed.\n", p->id, r->keyStr);
                p->ops = 2;
                free(str);
                return 4;
            }
            while (i < size && str[i] != '"') {
                ++i;
            }
            ++i;
            if (i == size) {
                printf("[tid:%02d] [mgrSubTable] get key(\"-left) from (%s) failed.\n", p->id, r->keyStr);
                free(str);
                p->ops = 2;
                return 5;
            }
            j = 0;
            while (i < size && str[i] != '"' && j < sizeof(r->idxName)) {
                r->idxName[j++] = str[i];
                ++i;
            }
            if (i == size) {
                printf("[tid:%02d] [mgrSubTable] get key(\"-right) from (%s) failed.\n", p->id, r->keyStr);
                free(str);
                p->ops = 2;
                return 6;
            }
            if (s->snName[0] == '\0') {
                snprintf(r->idxName + j, sizeof(r->idxName) - j, "_%05d", p->id);
            } else {
                snprintf(r->idxName + j, sizeof(r->idxName) - j, "%s_%05d", s->snName, p->id);
            }
            break;
        }
        fixCompleteName(r->metaName);
        if (s->snName[0] == '\0') {
            snprintf(r->idxName, sizeof(r->idxName), "%s_%05d", r->metaName, p->id);
        } else {
            snprintf(r->idxName, sizeof(r->idxName), "%s%s_%05d", r->metaName, s->snName, p->id);
        }
        r->subCfg.subsName = r->idxName;
        i = 512;
        str = (char *)malloc(i);
        if (str == NULL) {
            printf("[tid:%02d] [mgrSubTable] malloc memory(%d) for r->verStr(tmp) failed.\n", p->id, i);
            return 1;
        }
        if (r->snCfg[0] == 0) {  // use default
            if (r->objType == OBJ_TYPE_VERTEX) {
                snprintf(str, i,
                    "{ \"label_name\":\"%s\", \"is_reliable\":false, \"persist\":false, \"events\": [ {\"type\":"
                    "\"initial_load\",\"msgTypes\":[\"new object\"]},{\"type\":\"insert\"},"
                    "{\"type\":\"replace\", \"msgTypes\":[\"new object\"]},  {\"type\":\"merge\", \"msgTypes\":[\"new "
                    "object\"]}, {\"type\":\"update\", \"msgTypes\":[\"new object\", \"old object\"]}, "
                    "{\"type\":\"delete\", \"msgTypes\":[\"old object\"]}, {\"type\":\"age\", \"msgTypes\":[\"old "
                    "object\"]} ], \"retry\":true }",
                    r->metaName);
            } else {
                snprintf(str, i,
                    "{ \"label_name\":\"%s\", \"is_reliable\":false, \"persist\":false, \"events\": [ {\"type\":"
                    "\"initial_load\", \"msgTypes\":[\"new object\"] }, {\"type\":\"set\"},"
                    "{\"type\":\"delete\", \"msgTypes\":[\"old object\"] } ], \"retry\":true }",
                    r->metaName);
            }
            break;
        }
        snprintf(str, i, "{ \"label_name\":\"%s\", ", r->metaName);
        len = strlen(str);
        if (r->snCfg[2]) {
            snprintf(str + len, i - len, " \"is_reliable\":true,");
        } else {
            snprintf(str + len, i - len, " \"is_reliable\":false,");
        }
        len = strlen(str);
        if (r->snCfg[3]) {
            snprintf(str + len, i - len, " \"persist\":true,");
        } else {
            snprintf(str + len, i - len, " \"persist\":false,");
        }
        len = strlen(str);
        if (r->snCfg[4] && r->eventType) {
            snprintf(str + len, i - len, " %s", r->eventType);
        } else {
            if (r->objType == OBJ_TYPE_KV) {
                snprintf(str + len, i - len,
                    "\"events\": [ {\"type\":\"initial_load\", \"msgTypes\":[\"new object\"] },"
                    "{\"type\":\"set\", \"msgTypes\":[\"new object\"] }, "
                    "{\"type\":\"delete\", \"msgTypes\":[\"old object\"] }, "
                    "{\"type\":\"age\", \"msgTypes\":[\"old object\"] } ] ");
            } else {
                snprintf(str + len, i - len,
                    "\"events\": [ {\"type\":\"initial_load\", "
                    "\"msgTypes\":[\"new object\"]}, {\"type\":\"insert\", \"msgTypes\":[\"new object\"]}, "
                    "{\"type\":\"replace\", \"msgTypes\":[\"new object\"]}, {\"type\":\"merge\", \"msgTypes\":[\"new "
                    "object\"]}, {\"type\":\"update\", \"msgTypes\":[\"new object\", \"old object\"]}, "
                    "{\"type\":\"delete\", \"msgTypes\":[\"old object\"]}, {\"type\":\"age\", \"msgTypes\":[\"old "
                    "object\"] } ] ");
            }
        }
        len = strlen(str);
        snprintf(str + len - 1, i - len, " }");
    } while (0);
    if (r->verStr == NULL && r->snCfg[0] != 1) {
        len = strlen(str);
        r->verStr = (char *)malloc(len + 1);
        if (r->verStr == NULL) {
            printf("[tid:%02d] [mgrSubTable] malloc memory(%d) for r->verStr failed.\n", p->id, len + 1);
            p->ops = 2;
            return 1;
        }
        snprintf(r->verStr, len + 1, "%s", str);
        free(str);
    }
    if (r->snCfg[0] == 1) {
        // Todo: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    } else {
        ret = GmcUnSubscribe(r->stmt[p->offset], r->idxName);
        if (ret != 0) {
            printf("[tid:%02d] [mgrSubTable] GmcUnSubscribe(%s) fail, ret = %d.\n", p->id, r->idxName, ret);
        }
        if (r->oprCmd == DML_SUB_TABLE_STOP) {
            p->ops = 0;
            return 0;
        }
        if (g_logLevel > 0) {
            printf(" subName:%s\n", r->idxName);
            printf(" sunInfo:%s\n", r->verStr);
        }
        r->subCfg.configJson = r->verStr;
        ret = GmcSubscribe(r->stmt[p->offset], &r->subCfg, p->conn[GMC_CONN_TYPE_SUB], snAsyncStaTable, p);
        if (ret != 0) {
            printf("[tid:%02d] [mgrSubTable] GmcSubscribe(%s) fail, ret = %d.\n", p->id, r->idxName, ret);
            printf("[tid:%02d] [mgrSubTable] jsonText:%s\n", p->id, r->verStr);
            p->ops = 2;
            return 2;
        }
        if (r->objType == OBJ_TYPE_VERTEX) {
            if (r->schVersion == -1) {
                ret = GmcPrepareStmtByLabelName(r->stmt[p->offset], r->metaName, GMC_OPERATION_REPLACE);
            } else {
                ret = GmcPrepareStmtByLabelNameWithVersion(r->stmt[p->offset], r->metaName, r->schVersion,
                    GMC_OPERATION_REPLACE);
            }
            if (ret != 0) {
                printf("[tid:%d] [mgrSubTable] GmcPrepareStmtByLabelName fail, ret = %d, metaName:%s\n", p->id, ret,
                    r->metaName);
                return ret;
            }
        } else {
            ret = GmcKvPrepareStmtByLabelName(r->stmt[p->offset], r->metaName);
            if (ret != 0) {
                printf("[tid:%02d] [mgrSubTable] GmcKvPrepareStmtByLabelName(%s) fail, ret = %d.\n", p->id,
                    r->metaName, ret);
                return ret;
            }
        }
    }
    p->ops = 1;
    return 0;
}

int mgrSubTablePath(perfContent *p, runUnit *r, int v1, int v2)
{
    assert(0);
    return 0;
}

int exeEdgeSelect(perfContent *p, runUnit *r, int v1, int v2)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    int i, j, v, num, ret;
    int ops = r->ops;
    int64_t lVal;
    randomVal *rval = (randomVal *)&lVal;
    metaDataLabelEdge *e = (metaDataLabelEdge *)r->metadataE;
    metaDataLabelVertex *t = e->dv;
    struct timeval tmBegin, tmEnd;
    uint64_t opsBegin, opsEnd;
    if (g_perfType == 1) {
        gettimeofday(&tmBegin, NULL);
    }
    bool isEOF;
    opsBegin = getCyclesUS();
    uint64_t opsBegin1 = opsBegin;
    for (i = v1; i < v2; ++i) {
        if (r->ops > 0) {
            if (ops >= r->ops) {
                opsEnd = getCyclesUS();
                if (opsEnd - opsBegin < CYCLES_PER_S) {
                    --i;
                    usleep(200);
                    continue;
                }
                ops = 1;
                opsBegin = opsEnd;
            } else {
                ++ops;
            }
        }
        if (r->isolation == 2 || r->isolation == 3) {
            v = TEST_RANDOM(p->randomSeed) % (v2 - v1) + v1;
        } else {
            v = i;
        }
        GmcFreeStmt(r->stmt[p->offset]);
        ret = GmcAllocStmt(p->conn[r->connMode], &r->stmt[p->offset]);
        if (ret != 0) {
            printf(
                "[tid:%02d] [exeEdgeSelect] GmcAllocStmt failed, i=%d, cycl=%d-%d, ret = %d.\n", p->id, i, v1, v2, ret);
            return ret;
        }
        if (r->schVersion == -1) {
            ret = GmcPrepareStmtByLabelName(r->stmt[p->offset], e->sourceName, GMC_OPERATION_SCAN);
        } else {
            ret = GmcPrepareStmtByLabelNameWithVersion(r->stmt[p->offset], e->sourceName, r->schVersion,
                GMC_OPERATION_SCAN);
        }
        if (ret != 0) {
            printf("[tid:%02d] [exeEdgeSelect] GmcPrepareStmtByLabelName failed, ret = %d.\n", p->id, ret);
            return ret;
        }
        ret = setVertexKeyField(p, r, v);
        if (ret != 0) {
            printf("[tid:%02d] [exeEdgeSelect] setVertexKeyField failed, ret = %d.\n", p->id, ret);
            return ret;
        }
        ++p->countReq[0];
        ret = GmcDirectFetchNeighborBegin(r->stmt[p->offset], r->edgeName);
        if (ret != 0) {
            printf("[exeEdgeSelect] GmcDirectFetchNeighborBegin failed, ret = %d.\n", ret);
            return ret;
        }
        num = 0;
        isEOF = false;
        while (isEOF == false && ret == 0) {
            ret = GmcFetch(r->stmt[p->offset], &isEOF);
            if (ret == 0 && isEOF == false) {
                perfDumpVertex(p, r->stmt[p->offset], t);
                ++p->countSucc[0];
                ++num;
            }
        }
        if (ret != 0) {
            printf("[ScanEdge] GmcFetch(%s) failed, ret = %d.\n", r->edgeName, ret);
            (void)GmcDirectFetchNeighborEnd(r->stmt[p->offset]);
            return ret;
        }
        if (p->countSucc[0] == 0 && g_logLevel > 0) {
            printf("null\n");
        }
        if (num == 0) {
            ++p->countLost[0];
        }
        ret = GmcDirectFetchNeighborEnd(r->stmt[p->offset]);
        if (ret != 0) {
            printf("[exeEdgeSelect] GmcDirectFetchNeighborEnd(%s) failed, ret = %d.\n", r->edgeName, ret);
            return 0;
        }
    }
    if (g_perfType == 1) {
        gettimeofday(&tmEnd, NULL);
        uint64_t s1, s2;
        s1 = (tmEnd.tv_sec - tmBegin.tv_sec) * 1000000 + (tmEnd.tv_usec - tmBegin.tv_usec);
        if (s1 > 0) {
            s2 = ((double)(v2 - v1) / s1) * 1000000;
        } else {
            s2 = 0;
        }
        printf("[tid:%02d] [exeEdgeSelect] request:%llu, success:%llu, duplicate/lost:0, failed:%llu, "
               "cost_time:%lds|%06ldus, ops:%ld, cpu_cycles:0\n",
            p->id, p->countReq[0], p->countSucc[0], p->countFail[0], s1 / 1000000, s1 % 1000000, s2);
    }
    return ret;
}

int testPrepareStmt(perfContent *p, runUnit *r)
{
    int ret;
    if (r->stmt[p->offset] == NULL) {
        ret = getNewStmt(p, r);
        if (ret != 0) {
            printf("[tid:%02d] testPrepareStmt.getNewStmt failed, ret = %d\n", p->id, ret);
            return ret;
        }
    }
    if (r->objType == OBJ_TYPE_VERTEX) {
        if (r->oprType == OPR_TYPE_DML || r->oprCmd == DDL_VERTEX_QUERY) {
            ret = getMetadataVertex(p, r);
            if (ret != 0) {
                printf("[tid:%02d] testPrepareStmt.getMetadataVertex failed, ret = %d\n", p->id, ret);
                return ret;
            }
        }
    } else if (r->objType == OBJ_TYPE_EDGE) {
        if (r->oprCmd == DDL_EDGE_QUERY || r->oprCmd == DDL_EDGE_SELECT) {
            ret = getMetadataEdge(p, r);
            if (ret != 0) {
                printf("[tid:%02d] testPrepareStmt.getMetadataEdge failed, ret = %d\n", p->id, ret);
                return ret;
            }
        }
    } else if (r->objType == OBJ_TYPE_KV) {
        if (r->oprCmd >= DML_KV_SET && r->oprCmd <= DML_KV_GETCOUNT) {
            fixCompleteName(r->metaName);
        }
    }
    return 0;
}

int testExeOneUnit(perfContent *p, runUnit *r, int v1, int v2 = -1)
{
    if (v1 == v2) {
        return 0;
    }
    sceneEntry *s = g_sceneEntry + p->sceneId;
    int ret = testPrepareStmt(p, r);
    if (ret != 0) {
        printf("[tid:%02d] testExeOneUnit.testPrepareStmt failed, ret = %d.\n", p->id, ret);
        return ret;
    }
    if (r->objType == OBJ_TYPE_VERTEX) {
        if (r->oprType == OPR_TYPE_DML) {
            if (v2 > 0) {
                return exeDMLEntry(p, r, v1, v2);
            } else if (v2 == -1) {
                return exeDMLVertex(p, r, v1);
            } else {  // v2 == 0
                return exeDMLVertex(p, r, v1, true);
            }
        } else if (r->oprType == OPR_TYPE_DDL) {
            return mgrLabelVertex(p, r, v1, v2);
        } else if (r->oprType == OPR_TYPE_SUB_TAB) {
            return mgrSubTable(p, r, v1, v2);
        } else if (r->oprType == OPR_TYPE_SUB_PATH) {
            return mgrSubTablePath(p, r, v1, v2);
        } else {
            printf("[tid:%02d] testExeOneUnit invalid oprType:%d.\n", p->id, r->oprType);
            exit(1);
        }
    } else if (r->objType == OBJ_TYPE_EDGE) {
        if (r->oprType == OPR_TYPE_DDL) {
            return mgrLabelEdge(p, r, v1, v2);
        } else if (r->oprType == OPR_TYPE_DML) {
            if (r->oprCmd == DDL_EDGE_SELECT) {
                return exeEdgeSelect(p, r, v1, v2);
            } else {
                printf("[tid:%02d] testExeOneUnit uncompleted function, only select is available.\n", p->id);
                exit(1);
            }
        } else {
            printf("[tid:%02d] testExeOneUnit uncompleted function, edge + dml.\n", p->id);
            exit(1);
        }
    } else if (r->objType == OBJ_TYPE_KV) {
        if (r->oprType == OPR_TYPE_DML) {
            if (r->oprCmd == DML_KV_SCAN) {
                return exeScanKV(p, r, v1, v2);
            } else {
                if (v2 > 0) {
                    return exeDMLEntry(p, r, v1, v2);
                } else if (v2 == -1) {
                    return exeDMLKV(p, r, v1);
                } else {  // v2 == 0
                    return exeDMLKV(p, r, v1, true);
                }
            }
        } else if (r->oprType == OPR_TYPE_DDL) {
            return mgrLabelKV(p, r, v1, v2);
        } else if (r->oprType == OPR_TYPE_SUB_TAB) {
            return mgrSubTable(p, r, v1, v2);
        } else {
            printf("[tid:%02d] testExeOneUnit invalid oprType:%d.\n", p->id, r->oprType);
            exit(1);
        }
    } else if (r->objType == OBJ_TYPE_TABLESPACE) {
        return mgrTableSpace(p, r, v1, v2);
    } else if (r->objType == OBJ_TYPE_RES) {
        return mgrRespool(p, r, v1, v2);
    } else if (r->objType == OBJ_TYPE_NAMESPACE) {
        return mgrNameSpace(p, r, v1, v2);
    } else {
        return mgrCheckBill(p, r, v1, v2);
    }
}

int testExeOneGroup(perfContent *p, runGroup *g)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    int i, j, k, m, num, countTrans, ops, repeatNum, executeNum;
    int v1, v2, v, ret;
    runUnit *r, *curr;
    int *val1 = NULL;
    int *val2 = NULL;
    struct timeval tmBegin, tmEnd;
    uint64_t opsBegin, opsEnd;
    for (i = 0; i < g->numAll; ++i) {
        if (g->isSubGroup[i]) {
            testExeOneGroup(p, (runGroup *)(g->runList[i]));
            continue;
        }
        r = (runUnit *)(g->runList[i]);
        if (r->batchNum > 0 && r->ctxBatch[p->offset] == NULL) {
            GmcBatchOptionT batchOption;
            ret = GmcBatchOptionInit(&batchOption);
            if (ret != 0) {
                printf("[tid:%02d] [testExeOneGroup] fail, GmcBatchOptionSetExecOrder ret = %d.\n", p->id, ret);
                return -1;
            }
            ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);  // GMC_BATCH_ORDER_SEMI
            if (ret != 0) {
                printf("[tid:%02d] [testExeOneGroup] fail, GmcBatchOptionSetExecOrder ret = %d.\n", p->id, ret);
                return -1;
            }
            ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
            if (ret != 0) {
                printf("[tid:%02d] [testExeOneGroup] fail, GmcBatchOptionSetBufLimitSize ret = %d.\n", p->id, ret);
                return -1;
            }
            ret = GmcBatchPrepare(p->conn[r->connMode], &batchOption, &r->ctxBatch[p->offset]);
            if (ret != 0) {
                printf("[tid:%02d] [testExeOneGroup] fail, GmcBatchPrepare ret = %d.\n", p->id, r->id, ret);
                return -1;
            }
        }
        getDataArea(r, p->id, &v1, &v2);
        if (r->exeSeq == 0) {
            gettimeofday(&tmBegin, NULL);
            for (j = 0; j < r->repeatNum; ++j) {
                testExeOneUnit(p, r, v1, v2);
                p->countTrans = 0;
            }
            gettimeofday(&tmEnd, NULL);
            if (g_logLevel > 0) {
                printf("[tid:%02d]   execute unit(%s), objType:%d, oprType:%d, oprCmd:%d, elapse:%d.\n", p->id, r->id,
                    r->objType, r->oprType, r->oprCmd, tmEnd.tv_sec - tmBegin.tv_sec);
            }
            continue;
        }
        // printf("[tid:%02d]   execute unit(%s), objType:%d, oprType:%d, oprCmd:%d.\n", p->id, r->id, r->objType,
        // r->oprType, r->oprCmd);
        repeatNum = r->repeatNum;
        for (j = i + 1; j < g->numAll; ++j) {
            curr = (runUnit *)(g->runList[j]);
            if (curr->exeSeq == 0) {
                break;
            }
            printf("[tid:%02d]   execute unit(%s), objType:%d, oprType:%d, oprCmd:%d.\n", p->id, curr->id,
                curr->objType, curr->oprType, curr->oprCmd);
            if (repeatNum < curr->repeatNum) {
                repeatNum = curr->repeatNum;
            }
        }
        num = j;
        val1 = (int *)malloc(sizeof(int) * (num - i) * 2);
        if (val1 == NULL) {
            printf("[tid:%02d] malloc memory(%ld) for val1 failed.\n", p->id, sizeof(int) * (num - i) * 2);
            break;
        }
        val2 = val1 + (num - i);
        val1[0] = v1;
        val2[0] = v2 - v1;
        executeNum = val2[0];
        for (j = i + 1; j < g->numAll; ++j) {
            curr = (runUnit *)(g->runList[j]);
            if (curr->exeSeq == 0) {
                break;
            }
            getDataArea(curr, p->id, &v1, &v2);
            val1[j - i] = v1;
            val2[j - i] = v2 - v1;
            if (executeNum < val2[j - i]) {
                executeNum = val2[j - i];
            }
        }
        ops = 0;
        num = j;
        countTrans = r->transNum;
        opsBegin = getCyclesUS();
        for (j = 0; j < repeatNum; ++j) {
            for (k = 0; k < executeNum; ++k) {
                for (m = i; m < num; ++m) {
                    curr = (runUnit *)(g->runList[m]);
                    if (j >= curr->repeatNum) {
                        continue;
                    }
                    if (k >= val2[m - i]) {
                        continue;
                    }
                    if (r->ops > 0) {
                        if (ops >= r->ops) {
                            opsEnd = getCyclesUS();
                            if (opsEnd - opsBegin < CYCLES_PER_S) {
                                --m;
                                usleep(200);
                                continue;
                            }
                            ops = 1;
                            opsBegin = opsEnd;
                        } else {
                            ++ops;
                        }
                    }
                    if (r->transNum > 0) {
                        ret = testDoTransaction(p, r);
                        if (ret != 0) {
                            printf("[tid:%02d] testExeOneGroup.testEndTransaction failed, ret = %d\n", p->id, ret);
                        }
                    }
                    if (curr->isolation == 2 || curr->isolation == 3) {
                        v = TEST_RANDOM(p->randomSeed) % val2[m - i] + val1[m - i];
                    } else {
                        v = k + val1[m - i];
                    }
                    if (k + 1 == val2[m - i]) {
                        ret = testExeOneUnit(p, curr, v, 0);
                    } else {
                        ret = testExeOneUnit(p, curr, v, -1);
                    }
                    if (ret != 0) {
                        printf("[tid:%02d] testExeOneGroup.testExeOneUnit failed, ret = %d\n", p->id, ret);
                    }
                    if (r->transNum > 0) {
                        if (p->countTrans == countTrans) {
                            ret = testEndTransaction(p, r, &countTrans);
                            if (ret != 0) {
                                printf("[tid:%02d] testExeOneGroup.testEndTransaction failed, ret = %d\n", p->id, ret);
                            }
                        }
                    }
                }
            }
        }
        if (r->transNum > 0 && p->countTrans > 0) {
            ret = testEndTransaction(p, r);
            if (ret != 0) {
                printf("[tid:%02d] testExeOneGroup.testEndTransaction failed, ret = %d\n", p->id, ret);
            }
        }
        i = num - 1;
    }
    if (val1) {
        free(val1);
    }
    return 0;
}

void testPrepareOneGroup(perfContent *p, runGroup *g)
{
    sceneEntry *s = g_sceneEntry + p->sceneId;
    int i, ret;
    runUnit *r, *curr;
    for (i = 0; i < g->numAll; ++i) {
        if (g->isSubGroup[i]) {
            testPrepareOneGroup(p, (runGroup *)(g->runList[i]));
            continue;
        }
        r = (runUnit *)(g->runList[i]);
        ret = testPrepareStmt(p, r);
        if (ret != 0) {
            printf("[tid:%02d] testExeOneUnit.testPrepareStmt failed, ret = %d.\n", p->id, ret);
            continue;
        }
    }
}

void *threadAsyncMain(void *arg)
{
    epollAsync *e = (epollAsync *)arg;
    char tmpBuf[24];
    snprintf(tmpBuf, sizeof(tmpBuf), "epollAsync%04d", e->id);
    prctl(PR_SET_NAME, tmpBuf);
    int i, nfds;
    int nums = e->num * 2 + 1;
    e->fd = epoll_create(nums);
    e->run = true;
    struct epoll_event etOut[nums];
    while (e->run) {
        nfds = epoll_wait(e->fd, etOut, nums, 0);
        if (nfds == 0) {
            usleep(1000);
            continue;
        } else if (nfds < 0) {
            printf("[tid:%d] [threadAsyncMain] epoll_wait(fd:%d,recv) socket failed, ret = %d, errno = %d.\n", e->id,
                e->fd, nfds, errno);
            usleep(1000);
            continue;
        }
        for (i = 0; i < nfds; ++i) {
            GmcHandleRWEvent(etOut[i].data.fd, etOut[i].events);
        }
    }
    close(e->fd);
    e->fd = 0;
}

void *heartBeatCallback(void *arg)
{
    int epollNum = 5;
    char tmpBuf[24];
    snprintf(tmpBuf, sizeof(tmpBuf), "heartBeatCB");
    prctl(PR_SET_NAME, tmpBuf);
    int i, nfds;
    g_asyncTmFd = epoll_create(epollNum);
    g_asyncTmRun = true;
    struct epoll_event etOut[epollNum];
    while (g_asyncTmRun) {
        nfds = epoll_wait(g_asyncTmFd, etOut, epollNum, 0);
        if (nfds == 0) {
            usleep(1000);
            continue;
        } else if (nfds < 0) {
            printf("[tid:xx] [heartBeatCallback] epoll_wait(fd:%d,recv) socket failed, ret = %d, errno = %d.\n",
                g_asyncTmFd, nfds, errno);
            usleep(1000);
            continue;
        }
        for (i = 0; i < nfds; ++i) {
            GmcHandleEvent(etOut[i].data.fd);
        }
    }
    close(g_asyncTmFd);
    g_asyncTmFd = 0;
}

void StartEpollAsync()
{
    int i, ret;
    perfContent *p;
    sceneEntry *s;
    g_epNum = 0;
    memset(g_epAsync, 0, sizeof(g_epAsync));
    int start = 0;
    for (i = 0; i < g_threadCount; ++i) {
        p = g_testCtx[i];
        s = g_sceneEntry + p->sceneId;
        if (s->connFlag[1] == false) {
            continue;
        }
        p->asyncId = start;
        g_epAsync[start].id = start;
        ++g_epAsync[start].num;
        ++g_epNum;
        ++start;
        start = start % MAX_EP_ASYNC_NUM;
    }
    if (g_epNum == 0) {
        return;
    }
    if (g_asyncTmRun) {
        ret = GmcRegTimeoutEpollFunc(epollEventHeartBeat);
        if (ret != 0) {
            printf("[startEpollAsync] GmcRegTimeoutEpollFunc failed, ret = %d.\n", ret);
            exit(1);
        }
    }
    if (g_epNum > MAX_EP_ASYNC_NUM) {
        g_epNum = MAX_EP_ASYNC_NUM;
    }
    epollAsync *e;
    int id[MAX_EP_ASYNC_NUM];
    for (i = 0; i < g_epNum; ++i) {
        e = g_epAsync + i;
        ret = pthread_create(&e->pt, NULL, threadAsyncMain, e);
        if (ret != 0) {
            printf("[startEpollAsync] start threadAsyncMain(id=%d) failed, ret = %d\n", i, ret);
            exit(1);
        }
    }
    usleep(500000);
}

void startEpollHB()
{
    int ret = pthread_create(&g_asyncTmPt, NULL, heartBeatCallback, NULL);
    if (ret != 0) {
        printf("[startEpollHB] pthread_create.heartBeatCallback failed, ret = %d\n", ret);
        exit(1);
    }
    int sum = 0;
    while (!g_asyncTmRun && ++sum < 200) {
        usleep(10000);
    }
    if (!g_asyncTmRun) {
        return;
    }
    ret = GmcRegHeartBeatEpollFunc(epollEventHeartBeat);
    if (ret != 0) {
        printf("[startEpollHB] GmcRegHeartBeatEpollFunc failed, ret = %d.\n", ret);
    }
}

void *testMainRequest(void *arg)
{
    perfContent *p = (perfContent *)arg;
    sceneEntry *s = g_sceneEntry + p->sceneId;
    int ret;
    char tmpBuf[24];
    snprintf(tmpBuf, sizeof(tmpBuf), "testMain%04d", p->id);
    prctl(PR_SET_NAME, tmpBuf);
    p->status = TH_STAT_INIT;
    if (s->connFlag[2] && p->conn[2] == NULL) {
        ret = perfGmcConnect(p->id, GMC_CONN_TYPE_SUB, s->snName);
        defInThreadStr(ret, p->id, p->status, "testMainRequest.perfGmcConnect(GMC_CONN_TYPE_SUB)", NULL);
        ret = perfSetNameSpace(p->id, GMC_CONN_TYPE_SUB, s->nameSpace);
        defInThreadStr(ret, p->id, p->status, "testMainRequest.perfSetNameSpace(GMC_CONN_TYPE_SUB)", NULL);
    }
    if (s->connFlag[1] && p->conn[1] == NULL) {
        ret = perfGmcConnect(p->id, GMC_CONN_TYPE_ASYNC);
        defInThreadStr(ret, p->id, p->status, "testMainRequest.perfGmcConnect(GMC_CONN_TYPE_ASYNC)", NULL);
        ret = perfSetNameSpace(p->id, GMC_CONN_TYPE_ASYNC, s->nameSpace);
        defInThreadStr(ret, p->id, p->status, "testMainRequest.perfSetNameSpace(GMC_CONN_TYPE_ASYNC)", NULL);
    }
    if (s->connFlag[0] && p->conn[0] == NULL) {
        ret = perfGmcConnect(p->id, GMC_CONN_TYPE_SYNC);
        defInThreadStr(ret, p->id, p->status, "testMainRequest.perfGmcConnect(GMC_CONN_TYPE_SYNC)", NULL);
        ret = perfSetNameSpace(p->id, GMC_CONN_TYPE_SYNC, s->nameSpace);
        defInThreadStr(ret, p->id, p->status, "testMainRequest.perfSetNameSpace(GMC_CONN_TYPE_SYNC)", NULL);
    }
    int i;
    runUnit *curr;
    p->status = TH_STAT_PRESHELL;
    if (p->id == s->startTid) {
        if (s->preShell != NULL) {
            for (i = 0; i < s->numPre; ++i) {
                curr = s->preShell + i;
                testExeOneUnit(p, curr, curr->beginCycle, curr->endCycle);
            }
        }
        p->status = TH_STAT_PREPARE;
        testPrepareOneGroup(p, &s->r);
        for (i = 1; i < s->threadCount; ++i) {
            if (p[i].status == TH_STAT_INIT) {
                usleep(1000);
                --i;
                continue;
            }
            p[i].status = TH_STAT_PREPARE;
        }
    } else {
        while (p->status == TH_STAT_PRESHELL) {
            usleep(10000);
        }
        testPrepareOneGroup(p, &s->r);
    }
    p->status = TH_STAT_WAIT;
    while (p->status == TH_STAT_WAIT) {
        usleep(10000);
    }
    struct timeval tm1, tm2;
    gettimeofday(&tm1, NULL);
    int runCycle = 0;
    while (p->status == TH_STAT_RUN) {
        if (s->isTime) {
            gettimeofday(&tm2, NULL);
            if (s->runTime > 0 && tm2.tv_sec - tm1.tv_sec > s->runTime) {
                break;
            }
        } else {
            ++runCycle;
            if (s->runTime > 0 && runCycle > s->runTime) {
                break;
            }
            if (s->runTime == 0 && runCycle >= 1000000000) {
                --runCycle;
            }
        }
        ret = testExeOneGroup(p, &s->r);
        if (ret != 0) {
            printf("[tid:%02d] testMainRequest.testExeOneGroup failed, ret = %d.\n", p->id, ret);
            p->status = TH_STAT_ERR;
        }
        i = 0;
        while (p->requestNum > p->repondNum && i < 180000) {
            ++i;
            usleep(1000);
        }
        if (p->requestNum > p->repondNum) {
            printf("[tid:%02d] [testMainRequest] some async message have been lost, requestNum:%lu, repondNum:%lu.\n",
                p->id, p->requestNum, p->repondNum);
            p->repondNum = p->requestNum;
        }
    }
    if (p->id == s->startTid && s->afterShell != NULL) {
        if (p->status == TH_STAT_RUN) {
            p->status = TH_STAT_AFTER;
        }
        for (i = 0; i < s->numAfter; ++i) {
            curr = s->afterShell + i;
            testExeOneUnit(p, curr, curr->beginCycle, curr->endCycle);
        }
    }
    if (p->requestNum > 0) {
        i = 1000;
        while (p->requestNum > p->repondNum && i > 0) {
            --i;
            usleep(10000);
        }
        g_epAsync[p->asyncId].run = false;
        usleep(200000);
    }
    if (s->connFlag[2]) {
        if (p->id < MAX_EP_ASYNC_NUM) {
            i = 30;
            do {
                --i;
                ret = p->snCount;
                usleep(100000);
            } while (ret != p->snCount && i >= 0);
            p->snFlag = false;
            usleep(10000);
        } else {
            usleep(100000);
        }
    }
    perfGmcDisConnect(p->id);
    if (p->status == TH_STAT_RUN || p->status == TH_STAT_AFTER) {
        p->status = TH_STAT_SUC;
    }
    return NULL;
}

//   ------------ read data file

int getOperationCommand(char *name1, char *name2, int *oprType)
{
    *oprType = OPR_TYPE_DML;
    if (0 == strcmp(name1, "edge")) {
        if (0 == strcmp(name2, "create")) {
            *oprType = OPR_TYPE_DDL;
            return DDL_EDGE_CREATE;
        }
        if (0 == strcmp(name2, "drop")) {
            *oprType = OPR_TYPE_DDL;
            return DDL_EDGE_DROP;
        }
        if (0 == strcmp(name2, "query")) {
            *oprType = OPR_TYPE_DDL;
            return DDL_EDGE_QUERY;
        }
        if (0 == strcmp(name2, "insert")) {
            return DDL_EDGE_INSERT;
        }
        if (0 == strcmp(name2, "delete")) {
            return DDL_EDGE_DELETE;
        }
        if (0 == strcmp(name2, "select")) {
            return DDL_EDGE_SELECT;
        }
        return -1;
    }
    if (0 == strcmp(name1, "kv")) {
        *oprType = OPR_TYPE_DDL;
        if (0 == strcmp(name2, "create")) {
            return DDL_KV_CREATE;
        }
        if (0 == strcmp(name2, "drop")) {
            return DDL_KV_DROP;
        }
        if (0 == strcmp(name2, "query")) {
            return DDL_KV_QUERY;
        }
        if (0 == strcmp(name2, "truncate")) {
            return DDL_KV_TRUNCATE;
        }
        *oprType = OPR_TYPE_DML;
        if (0 == strcmp(name2, "set")) {
            return DML_KV_SET;
        }
        if (0 == strcmp(name2, "get")) {
            return DML_KV_GET;
        }
        if (0 == strcmp(name2, "del")) {
            return DML_KV_DEL;
        }
        if (0 == strcmp(name2, "exist")) {
            return DML_KV_EXIST;
        }
        if (0 == strcmp(name2, "scan")) {
            return DML_KV_SCAN;
        }
        if (0 == strcmp(name2, "getCount")) {
            return DDL_VERTEX_STATIC_COUNT;
        }
        if (0 == strcmp(name2, "getCountAlter")) {
            return DDL_VERTEX_ALTER_COUNT;
        }
    }
    if (0 == strcmp(name1, "namespace")) {
        *oprType = OPR_TYPE_DDL;
        if (0 == strcmp(name2, "create")) {
            return DDL_NAMESPACE_CREATE;
        }
        if (0 == strcmp(name2, "drop")) {
            return DDL_NAMESPACE_DROP;
        }
        return -1;
    }
    if (0 == strcmp(name1, "respool")) {
        *oprType = OPR_TYPE_DDL;
        if (0 == strcmp(name2, "create")) {
            return DDL_RESPOOL_CREATE;
        }
        if (0 == strcmp(name2, "drop")) {
            return DDL_RESPOOL_DROP;
        }
        if (0 == strcmp(name2, "query")) {
            return DDL_RESPOOL_QUERY;
        }
        return -1;
    }
    if (0 == strcmp(name1, "checkbill")) {
        *oprType = OPR_TYPE_DDL;
        if (0 == strcmp(name2, "start")) {
            return DDL_CHECKBILL_START;
        }
        if (0 == strcmp(name2, "stop")) {
            return DDL_CHECKBILL_STOP;
        }
        if (0 == strcmp(name2, "query")) {
            return DDL_CHECKBILL_QUERY;
        }
        if (0 == strcmp(name2, "update")) {
            return DDL_CHECKBILL_UPDATE_VERSION;
        }
        return -1;
    }
    if (0 == strcmp(name2, "tableStartOnce")) {
        *oprType = OPR_TYPE_SUB_TAB;
        return DML_SUB_TABLE_START_ONCE;
    }
    if (0 == strcmp(name2, "tableStart")) {
        *oprType = OPR_TYPE_SUB_TAB;
        return DML_SUB_TABLE_START_ALWAYS;
    }
    if (0 == strcmp(name2, "tableStop")) {
        *oprType = OPR_TYPE_SUB_TAB;
        return DML_SUB_TABLE_STOP;
    }
    if (0 == strcmp(name2, "pathStartOnce")) {
        *oprType = OPR_TYPE_SUB_PATH;
        return DML_SUB_PATH_START_ONCE;
    }
    if (0 == strcmp(name2, "pathStart")) {
        *oprType = OPR_TYPE_SUB_PATH;
        return DML_SUB_PATH_START_ALWAYS;
    }
    if (0 == strcmp(name2, "pathStop")) {
        *oprType = OPR_TYPE_SUB_PATH;
        return DML_SUB_PATH_STOP;
    }
    if (0 == strcmp(name2, "create")) {
        *oprType = OPR_TYPE_DDL;
        return DDL_VERTEX_CREATE;
    }
    if (0 == strcmp(name2, "drop")) {
        *oprType = OPR_TYPE_DDL;
        return DDL_VERTEX_DROP;
    }
    if (0 == strcmp(name2, "query")) {
        *oprType = OPR_TYPE_DDL;
        return DDL_VERTEX_QUERY;
    }
    if (0 == strcmp(name2, "truncate")) {
        *oprType = OPR_TYPE_DDL;
        return DDL_VERTEX_TRUNCATE;
    }
    if (0 == strcmp(name2, "insert")) {
        return DML_VERTEX_INSERT;
    }
    if (0 == strcmp(name2, "replace")) {
        return DML_VERTEX_REPLACE;
    }
    if (0 == strcmp(name2, "merge")) {
        return DML_VERTEX_MERGE;
    }
    if (0 == strcmp(name2, "delete")) {
        return DML_VERTEX_DELETE;
    }
    if (0 == strcmp(name2, "update")) {
        return DML_VERTEX_UPDATE;
    }
    if (0 == strcmp(name2, "select")) {
        return DML_VERTEX_SELECT;
    }
    if (0 == strcmp(name2, "scan")) {
        return DML_VERTEX_SELECT;
    }
    if (0 == strcmp(name2, "updateF")) {
        return DML_VERTEX_UPDATE;
    }
    if (0 == strcmp(name2, "deleteF")) {
        return DML_VERTEX_DELETE;
    }
    if (0 == strcmp(name2, "getCount")) {
        return DDL_VERTEX_STATIC_COUNT;
    }
    if (0 == strcmp(name2, "getCountAlter")) {
        return DDL_VERTEX_ALTER_COUNT;
    }
    return -1;
}

void getKvVarNum(char *s, int *var1, int *var2)
{
    *var1 = 0;
    *var2 = 0;
    if (s == NULL) {
        return;
    }
    int len = strlen(s) - 1;
    for (int i = 0; i < len; ++i) {
        if (s[i] == '%') {
            if (s[i + 1] == 's') {
                *var1 += 1;
            } else if (s[i + 1] == 'd') {
                *var2 += 1;
            } else {
                printf("[getKvVarNum] invalid configuration:%s.\n", s);
                exit(1);
            }
        }
    }
}

void generateKvCondArray(runUnit *u, int threadCount, int varS, int varD, bool isKey)
{
    KVElement *c;
    char *s;
    char **input;
    if (isKey) {
        c = &(u->kvK);
        s = u->keyStr;
        input = u->inputI;
    } else {
        c = &(u->kvV);
        s = u->verStr;
        input = u->inputV;
    }
    int len1 = strlen(s);
    if (s[len1 - 1] != ':') {
        printf("[generateKvCondArray] invalid configuration:%s, the character in the end is not :\n", s);
        exit(1);
    }
    int i, j = 0;
    for (i = 0; i < len1; ++i) {
        if (s[i] == '-') {
            j = i;
        }
        if (s[i] == ',') {
            break;
        }
    }
    if (i < len1) {
        c->size1 = atol(s);
        c->size2 = atol(s + j + 1);
        s = s + i + 1;
        len1 = strlen(s);
    }
    uint64_t k, m;
    int sum = strlen(s) + 4;
    for (i = 0; i < len1; ++i) {
        if (i + 3 < len1 && 0 == strncmp(s + i, "%s:", 3)) {
            c->varType = 1;
            if (c->size2 > 0) {
                sum = i + 4 + c->size2;
            } else if (c->size1 > 0) {
                sum = i + 4 + c->size1;
            } else {
                c->size1 = 128;
                sum = 4 + c->size1;
            }
            break;
        } else if (i + 3 < len1 && 0 == strncmp(s + i, "%d:", 3)) {
            c->varType = 2;
            sum = i + 12;
            break;
        } else if (i + 3 < len1 && 0 == strncmp(s + i, "%u:", 3)) {
            c->varType = 3;
            sum += 16;
            break;
        }
    }
    char *o = (char *)malloc(sum * threadCount);
    if (o == NULL) {
        printf("malloc memory(%d) for kv_buf failed.\n", sum * threadCount);
        exit(1);
    }
    memset(o, 0, sum * threadCount);
    input[0] = o;
    int n;
    int len2 = 0;
    int len3 = 0;
    for (i = 0; i < len1; ++i) {
        if (i + 3 < len1 && 0 == strncmp(s + i, "%s:", 3)) {
            i += 3;
        } else if (i + 3 < len1 && 0 == strncmp(s + i, "%d:", 3)) {
            i += 3;
        } else if (i + 3 < len1 && 0 == strncmp(s + i, "%u:", 3)) {
            i += 3;
        } else {
            o[len2++] = s[i];
            continue;
        }
        c->varRule = atoi(s + i);
        while (i < len1 && s[i] >= '0' && s[i] <= '9') {
            ++i;
        }
        if (i == len1 || s[i] != ':') {
            printf("[generateKvCondArray] invalid configuration:%s, get varRule failed.\n", s);
            exit(1);
        }
        ++i;
        if (c->varType == 1) {
            s = s + i;
            n = strlen(s) - 1;
            if (c->size1 < c->size2) {
                j = c->size2;
            } else {
                j = c->size1;
            }
            while (len2 < j) {
                o[len2] = s[len2 % n];
                ++len2;
            }
            break;
        }
        n = i;
        while (n < len1 && s[n] != '-') {
            ++n;
        }
        c->varBegin = atol(s + i);
        if (n < len1) {
            c->varEnd = atol(s + n + 1);
        }
        c->offset = len2;
        n = 8;
        if (c->varType == 3) {
            n = 12;
        }
        c->offset = len2;
        c->length = n;
        for (j = 0; j < n; ++j) {
            o[len2++] = '0';
        }
    }
    for (i = 1; i < threadCount; ++i) {
        input[i] = o + sum * i;
        memcpy(input[i], input[0], sum);
    }
    if (c->size1 == 0 && c->size2 == 0) {
        c->size1 = len2;
    }
}

void generateKvCond(runUnit *u, int threadCount)
{
    if (u->objType != OBJ_TYPE_KV || u->oprType != OPR_TYPE_DML || u->oprCmd == DML_KV_SCAN ||
        u->oprCmd == DML_KV_SCAN) {
        return;
    }
    int i, varS, varD;
    getKvVarNum(u->keyStr, &varS, &varD);
    if (varS > 0 || varD > 0) {
        generateKvCondArray(u, threadCount, varS, varD, true);
    } else {
        for (i = 0; i < threadCount; ++i) {
            u->inputI[i] = u->keyStr;
        }
        u->kvK.size1 = strlen(u->keyStr);
    }
    getKvVarNum(u->verStr, &varS, &varD);
    if (varS > 0 || varD > 0) {
        generateKvCondArray(u, threadCount, varS, varD, false);
    } else {
        for (i = 0; i < threadCount; ++i) {
            u->inputV[i] = u->verStr;
        }
        if (u->verStr == NULL) {
            u->kvV.size1 = 0;
        } else {
            u->kvV.size1 = strlen(u->verStr);
        }
    }
}

void generateVertexCond(runUnit *r, int threadCount)
{
    if (r->isFilter == false) {
        return;
    }
    if (r->flt == NULL || r->flt->n == 0) {
        return;
    }
    r->flt->filter = (char **)malloc(sizeof(char *) * threadCount);
    if (r->flt->filter == NULL) {
        printf("generateVertexCon malloc mem(%ld) for r->flt->filter failed.\n", sizeof(char *) * threadCount);
        exit(1);
    }
    r->flt->filter[0] = r->flt->str;
    if (threadCount == 1) {
        return;
    }
    int len = strlen(r->flt->str) + 8;
    r->flt->filter[1] = (char *)malloc(len * (threadCount - 1));
    if (r->flt->filter[1] == NULL) {
        printf("generateVertexCon malloc mem(%ld) for r->flt->filter[1] failed.\n", len * (threadCount - 1));
        exit(1);
    }
    snprintf(r->flt->filter[1], len, "%s", r->flt->str);
    for (int i = 2; i < threadCount; ++i) {
        r->flt->filter[i] = r->flt->filter[1] + (i - 1) * len;
        snprintf(r->flt->filter[i], len, "%s", r->flt->str);
    }
}

void generateRangeCond(runUnit *u, int threadCount)
{
    if (u->range == NULL || u->objType != OBJ_TYPE_VERTEX || u->oprType != OPR_TYPE_DML) {
        return;
    }
    if (u->range->n == 0) {
        u->range->n = 16;
    }
    u->range->rangeItem = (GmcRangeItemT *)malloc(sizeof(GmcRangeItemT) * threadCount * u->range->n);
    if (u->range->rangeItem == NULL) {
        printf("[generateRangeCond] malloc memory(size:%u) for u->range->rangeItem failed.\n",
            sizeof(GmcRangeItemT) * threadCount * u->range->n);
        exit(1);
    }
    memset(u->range->rangeItem, 0, sizeof(GmcRangeItemT) * threadCount * u->range->n);
    int sum = 0;
    if (u->range->lFlag != 2) {
        ++sum;
    }
    if (u->range->rFlag != 2) {
        ++sum;
    }
    int size = threadCount * u->range->n * sum;
    GmcPropValueT *p = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * size);
    if (p == NULL) {
        printf("[generateRangeCond] malloc memory(size:%u) for u->range->rangeItem->lvalue/rvalue failed.\n",
            sizeof(GmcPropValueT) * size);
        exit(1);
    }
    memset(p, 0, sizeof(GmcPropValueT) * size);
    int64_t *v = (int64_t *)malloc(sizeof(int64_t) * size);
    if (v == NULL) {
        printf("[generateRangeCond] malloc memory(size:%u) for u->range->rangeItem->lvalue/rvalue->value failed.\n",
            sizeof(int64_t) * size);
        exit(1);
    }
    memset(v, 0, sizeof(int64_t) * size);
    int i, j;
    for (i = 0; i < size; ++i) {
        p[i].value = v + i;
    }
    sum = 0;
    GmcRangeItemT *pr;
    for (i = 0; i < threadCount; ++i) {
        pr = u->range->rangeItem + u->range->n * i;
        for (j = 0; j < u->range->n; ++j) {
            if (u->range->isDesc) {
                pr[j].order = GMC_ORDER_DESC;
            } else {
                pr[j].order = GMC_ORDER_ASC;
            }
            if (u->range->lFlag == 2) {
                pr[j].lFlag = GMC_COMPARE_RANGE_OPEN;
                pr[j].lValue = NULL;
            } else {
                pr[j].lFlag = (GmcCompareFlagE)u->range->lFlag;
                pr[j].lValue = p + sum++;
            }
            if (u->range->rFlag == 2) {
                pr[j].rFlag = GMC_COMPARE_RANGE_OPEN;
                pr[j].rValue = NULL;
            } else {
                pr[j].rFlag = (GmcCompareFlagE)u->range->rFlag;
                pr[j].rValue = p + sum++;
            }
        }
    }
}

void parseParamVarSize(char *str, runUnit *r)
{
    r->varMinSize = atoi(str);
    r->varMaxSize = r->varMinSize;
    int len = strlen(str);
    for (int i = 0; i < len; ++i) {
        if (str[i] == ',') {
            r->varMaxSize = atoi(str + i + 1);
            return;
        }
    }
}

void parseSortCondition(char *str, runUnit *r)
{
    if (str == NULL || 0 == strcmp(str, "NULL") || 0 == strcmp(str, "null")) {
        return;
    }
    int len = strlen(str);
    char *outStr = (char *)malloc(len + 1);
    if (outStr == NULL) {
        printf("[parseSortCondition] malloc memory(size:%d) for outStr failed.\n", len + 1);
        exit(1);
    }
    r->sort = (sortCtx *)malloc(sizeof(sortCtx));
    if (r->sort == NULL) {
        printf("[parseSortCondition] malloc memory(size:%u) for r->sort failed.\n", sizeof(sortCtx));
        free(outStr);
        exit(1);
    }
    snprintf(outStr, len + 1, "%s", str);
    int i = 0;
    int n = 0;
    while (i < len) {
        if (0 == strncmp(outStr + i, "asc,", 4)) {
            r->sort->order[n] = GMC_ORDER_ASC;
            i += 4;
            r->sort->propNameArr[n] = outStr + i;
        } else if (0 == strncmp(outStr + i, "desc,", 5)) {
            r->sort->order[n] = GMC_ORDER_DESC;
            i += 5;
            r->sort->propNameArr[n] = outStr + i;
        } else {
            printf(
                "[parseSortCondition] invalid configuration. expect asc or desc, but %s, str = %s \n", outStr + i, str);
            exit(1);
        }
        while (i < len && outStr[i] != ':') {
            ++i;
        }
        if (i < len && outStr[i] == ':') {
            outStr[i] = '\0';
            ++i;
        }
        ++n;
    }
    r->sort->n = n;
}

int parseParamRespool(char *str, runUnit *r)
{
    int i, j, len, sum = 1;
    char buf[1024];
    snprintf(buf, sizeof(buf), "%s", str);
    len = strlen(buf);
    for (i = 0; i < len; ++i) {
        if (buf[i] == ',') {
            ++sum;
        }
    }
    r->resNum = sum;
    r->resAddr = (resMember *)malloc(sizeof(resMember) * sum);
    if (r->resAddr == NULL) {
        printf("[parseRespoolMsg] malloc memory(size:%u) for r->resAddr failed.\n", sizeof(resMember) * sum);
        exit(1);
    }
    memset(r->resAddr, 0, sizeof(resMember) * sum);
    sum = 0;
    char *pos, *left;
    pos = strtok_r(buf, ",", &left);
    while (pos) {
        do {
            len = strlen(pos);
            j = 0;
            while (j < len && pos[j] != ':') {
                r->resAddr[sum].colName[j] = pos[j];
                ++j;
            }
            ++j;
            if (j >= len) {
                break;
            }
            r->resAddr[sum].resId = atoi(pos + j);
            while (j < len && buf[j] != ':') {
                ++j;
            }
            ++j;
            if (j >= len) {
                break;
            }
            r->resAddr[sum].count = atoi(pos + j);
            while (j < len && buf[j] != ':') {
                ++j;
            }
            ++j;
            if (j >= len) {
                break;
            }
            r->resAddr[sum].startIndex = atoi(pos + j);
        } while (0);
        ++sum;
        pos = strtok_r(NULL, ",", &left);
    }
    return 0;
}

int parseParamRangeField(char *str, runUnit *r)
{
    r->range = (rangeScan *)malloc(sizeof(rangeScan));
    if (r->range == NULL) {
        printf("[parseParamRangeField] malloc memory(size:%u) for r->range failed.\n", sizeof(rangeScan));
        exit(1);
    }
    memset(r->range, 0, sizeof(rangeScan));
    int n = 0;
    if (0 == strncmp(str, "asc:", 4)) {
        n = 4;
        r->range->isDesc = false;
    } else if (0 == strncmp(str, "desc:", 5)) {
        n = 5;
        r->range->isDesc = true;
    }
    int len = strlen(str);
    while (n < len && str[n] >= '0' && str[n] <= '9') {
        r->range->n = r->range->n * 10 + (str[n] - '0');
        ++n;
    }
    if (n == len || str[n] != ':') {
        printf("[parseParamRangeField] get fieldNum failed, str:%s.\n", str);
        exit(1);
    }
    ++n;
    if (str[n] == '[') {
        r->range->lFlag = 1;
    } else if (str[n] == '(') {
        r->range->lFlag = 0;
    } else {
        printf("[parseParamRangeField] expect [ or ( , but it is %c.\n", str[n]);
        return 1;
    }
    char buf[128];
    snprintf(buf, sizeof(buf), "%s", str + n + 1);
    len = strlen(buf);
    if (buf[len - 1] == ']') {
        r->range->rFlag = 1;
    } else if (buf[len - 1] == ')') {
        r->range->rFlag = 0;
    } else {
        printf("[parseParamRangeField] expect ] or ) , but it is %c.\n", buf[len - 1]);
        return 1;
    }
    buf[len - 1] = '\0';
    --len;
    char val[2][128];
    int i, j;
    for (i = 0; i < len; ++i) {
        if (buf[i] != '~') {
            continue;
        }
        buf[i] = '\0';
        snprintf(val[0], sizeof(val[0]), "%s", buf);
        snprintf(val[1], sizeof(val[1]), "%s", buf + i + 1);
        buf[i] = '~';
        break;
    }
    if (i == len) {
        printf("[parseParamRangeField] the condition miss ~\n");
        return 1;
    }
    if (0 == strcmp(val[0], "null")) {
        r->range->lFlag = 2;
        val[0][0] = '\0';
    }
    if (0 == strcmp(val[1], "null")) {
        r->range->rFlag = 2;
        val[1][0] = '\0';
    }
    if (r->range->lFlag == 2 && r->range->rFlag == 2) {
        return 0;
    }
    r->range->varField = (uint8_t *)malloc(sizeof(uint8_t) * r->range->n * 2);
    if (r->range->varField == NULL) {
        printf("[parseParamRangeField] malloc memory(size:%u) for r->range->varField failed.\n",
            sizeof(uint8_t) * r->range->n * 2);
        return 1;
    }
    memset(r->range->varField, 0, sizeof(uint8_t) * r->range->n * 2);
    r->range->valField = (uint64_t *)malloc(sizeof(uint64_t) * r->range->n * 2);
    if (r->range->valField == NULL) {
        printf("[parseParamRangeField] malloc memory(size:%u) for r->range->varField failed.\n",
            sizeof(uint64_t) * r->range->n * 2);
        return 1;
    }
    memset(r->range->valField, 0, sizeof(uint64_t) * r->range->n * 2);

    char *pos, *left;
    for (i = 0; i < 2; ++i) {
        if (val[i][0] == '\0') {
            continue;
        }
        j = 0;
        pos = strtok_r(val[i], ",", &left);
        while (pos) {
            if (pos[0] == '-') {
                r->range->varField[j + i * r->range->n] = 1;
                r->range->valField[j + i * r->range->n] = atol(pos + 1);
            } else if (pos[0] == '+') {
                r->range->varField[j + i * r->range->n] = 2;
                r->range->valField[j + i * r->range->n] = atol(pos + 1);
            } else {
                r->range->valField[j + i * r->range->n] = atol(pos);
            }
            ++j;
            pos = strtok_r(NULL, ",", &left);
        }
    }
    return 0;
}

int parseParamFilter(char *str, runUnit *r)
{
    r->flt = (filterCtx *)malloc(sizeof(filterCtx));
    if (r->flt == NULL) {
        printf("[parseParamFilter] malloc memory(size:%u) for r->flt failed.\n", sizeof(filterCtx));
        return 1;
    }
    memset(r->flt, 0, sizeof(filterCtx));
    int n1 = 0;
    int n2 = 0;
    int len = strlen(str);
    int i, j;
    for (i = 0; i < len; ++i) {
        if (str[i] != '%') {
            continue;
        }
        ++n1;
        j = atoi(str + i + 1);
        if (j == 0) {
            n2 += 8;
        } else {
            n2 += j;
        }
    }
    r->flt->n = n1;
    n1 = 12 * r->flt->n;
    char *v = (char *)malloc(n1);
    if (v == NULL) {
        printf("[parseParamFilter] malloc memory(size:%u) for r->flt->member failed.\n", n1);
        return 1;
    }
    memset(v, 0, n1);
    r->flt->offset = (uint16_t *)v;
    r->flt->length = (uint16_t *)(v + r->flt->n * 2);
    r->flt->value = (int64_t *)(v + r->flt->n * 4);
    r->flt->str = (char *)malloc(len + n2 + 8);
    if (r->flt->str == NULL) {
        printf("[parseParamFilter] malloc memory(size:%u) for r->flt->str failed.\n", len + n2 + 8);
        return 1;
    }
    memset(r->flt->str, 0, len + n2 + 8);
    n1 = 0;
    n2 = 0;
    int64_t base;
    for (i = 0; i < len; ++i) {
        if (str[i] != '%') {
            r->flt->str[n1++] = str[i];
            continue;
        }
        r->flt->offset[n2] = n1;
        ++i;
        while (i < len && str[i] >= '0' && str[i] <= '9') {
            r->flt->length[n2] = r->flt->length[n2] * 10 + (str[i] - '0');
            ++i;
        }
        if (r->flt->length[n2] == 0) {
            r->flt->length[n2] = 8;
        }
        for (j = 0; j < r->flt->length[n2]; ++j) {
            r->flt->str[n1++] = ' ';
        }
        if (str[i] != 'd') {
            printf("[parseParamFilter] invalid configuration, expect %cxxxd.\n", '%');
            return 1;
        }
        ++i;
        if (str[i] == ':') {
            ++i;
            base = 0;
            while (i < len && str[i] >= '0' && str[i] <= '9') {
                base = base * 10 + (str[i] - '0');
                ++i;
            }
            r->flt->value[n2] = base;
        }
        ++n2;
        --i;
    }
    return 0;
}

int parseParamLabelConfig(char *str, runUnit *r)
{
    r->labelCfg = (labelConfig *)malloc(sizeof(labelConfig));
    if (r->labelCfg == NULL) {
        printf("[parseParamLabelConfig] malloc memory(size:%lu) for r->labelCfg failed.\n", sizeof(labelConfig));
        exit(1);
    }
    r->labelCfg->init();
    char *pos, *left;
    char buf[1024];
    snprintf(buf, sizeof(buf), "%s", str);
    pos = strtok_r(buf, ",", &left);
    while (pos) {
        if (0 == strncmp(pos, "max_record_count=", 17)) {
            r->labelCfg->max_record_count = atol(pos + 27);
        } else if (0 == strncmp(pos, "max_record_count_check=", 21)) {
            r->labelCfg->max_record_count_check = atol(pos + 21);
        } else if (0 == strncmp(pos, "auto_increment=", 15)) {
            r->labelCfg->auto_increment = atol(pos + 15);
        } else if (0 == strncmp(pos, "disable_sub_back_pressure=", 26)) {
            r->labelCfg->disable_sub_back_pressure = atol(pos + 26);
        } else if (0 == strncmp(pos, "isFastReadUncommitted=", 22)) {
            r->labelCfg->isFastReadUncommitted = atol(pos + 22);
        } else if (0 == strncmp(pos, "isTableLockMode=", 16)) {
            r->labelCfg->isTableLockMode = atol(pos + 16);
        } else if (0 == strncmp(pos, "push_age_record_batch=", 22)) {
            r->labelCfg->push_age_record_batch = atol(pos + 22);
        } else if (0 == strncmp(pos, "defragmentation=", 16)) {
            r->labelCfg->defragmentation = atol(pos + 16);
        } else {
            printf("[parseParamLabelConfig] invalid configuration:%s, input:%s.\n", pos, str);
            exit(1);
        }
        pos = strtok_r(NULL, ",", &left);
    }
    return 0;
}

void parseParamSubinfo(runUnit *u, char *buf)
{
    if (buf == NULL || u == NULL) {
        return;
    }
    int i, j, k, len;
    char *p1, *p2;
    char dmlType[32];
    bool msgType[3];
    char eventType[300];
    k = 0;
    char *pos, *left;
    pos = strtok_r(buf, ",", &left);
    while (pos) {
        do {
            if (0 == strncmp(pos, "latency=", 8)) {
                g_snLatency = atoi(pos + 8);
                break;
            }
            if (0 == strncmp(pos, "connName=", 9)) {
                len = strlen(pos + 9);
                u->cacheName = (char *)malloc(len + 1);
                if (u->cacheName == NULL) {
                    printf("malloc memory(%d) for u->cacheName failed.\n", len + 1);
                    exit(1);
                }
                snprintf(u->cacheName, len + 1, "%s", pos + 9);
                break;
            }
            if (0 == strncmp(pos, "is_full_sync=", 13)) {
                u->snCfg[0] = atoi(pos + 13);
                break;
            }
            u->snCfg[0] = 3;
            if (0 == strncmp(pos, "is_reliable=", 12)) {
                u->snCfg[2] = atoi(pos + 12);
                break;
            }
            if (0 == strncmp(pos, "persist=", 8)) {
                u->snCfg[3] = atoi(pos + 8);
                break;
            }
            if (0 != strncmp(pos, "events=", 7)) {
                printf("key(%s) is not expected in subInfo\n", pos);
                exit(1);
            }
            u->snCfg[4] = 1;
            snprintf(eventType, sizeof(eventType), "\"events\": [");
            pos += 7;
            len = strlen(pos);
            i = 0;
            while (i < len) {
                p1 = pos + i;
                memset(msgType, 0, sizeof(msgType));
                while (i < len && pos[i] != '+' && pos[i] != ':') {
                    ++i;
                }
                if (i < len && pos[i] == '+') {
                    pos[i] = '\0';
                    do {
                        ++i;
                        p2 = pos + i;
                        if (0 == strncmp(p2, "new", 3)) {
                            msgType[0] = 1;
                        } else if (0 == strncmp(p2, "old", 3)) {
                            msgType[1] = 1;
                        } else if (0 == strncmp(p2, "key", 3)) {
                            msgType[2] = 1;
                        } else {
                            printf("invalid configuration:%s, sn_obj must been in [new, old, key].\n", pos);
                            exit(0);
                        }
                        i += 3;
                    } while (i < len && pos[i] == '-');
                }
                if (i >= len || pos[i] == ':') {
                    if (i < len && pos[i] == ':') {
                        pos[i] = '\0';
                    }
                    snprintf(dmlType, sizeof(dmlType), "%s", p1);
                    if (0 == strcmp(dmlType, "replaceI")) {
                        snprintf(dmlType, sizeof(dmlType), "replace insert");
                    } else if (0 == strcmp(dmlType, "replaceU")) {
                        snprintf(dmlType, sizeof(dmlType), "replace update");
                    } else if (0 == strcmp(dmlType, "mergeI")) {
                        snprintf(dmlType, sizeof(dmlType), "merge insert");
                    } else if (0 == strcmp(dmlType, "mergeU")) {
                        snprintf(dmlType, sizeof(dmlType), "merge update");
                    }
                    k = strlen(eventType);
                    if (msgType[0] == 0 && msgType[1] == 0 && msgType[2] == 0) {
                        snprintf(eventType + k, sizeof(eventType) - k, " {\"type\":\"%s\"},", dmlType);
                    } else if (msgType[0] == 0 && msgType[1] == 0 && msgType[2] == 1) {
                        snprintf(eventType + k, sizeof(eventType) - k, " {\"type\":\"%s\", \"msgTypes\": [\"key\"]},",
                            dmlType);
                    } else if (msgType[0] == 0 && msgType[1] == 1 && msgType[2] == 0) {
                        snprintf(eventType + k, sizeof(eventType) - k,
                            " {\"type\":\"%s\", \"msgTypes\": [\"old object\"]},", dmlType);
                    } else if (msgType[0] == 1 && msgType[1] == 0 && msgType[2] == 0) {
                        snprintf(eventType + k, sizeof(eventType) - k,
                            " {\"type\":\"%s\", \"msgTypes\": [\"new object\"]},", dmlType);
                    } else if (msgType[0] == 1 && msgType[1] == 1 && msgType[2] == 0) {
                        snprintf(eventType + k, sizeof(eventType) - k,
                            " {\"type\":\"%s\", \"msgTypes\": [\"new object\", \"old object\"]},", dmlType);
                    } else if (msgType[0] == 1 && msgType[1] == 0 && msgType[2] == 1) {
                        snprintf(eventType + k, sizeof(eventType) - k,
                            " {\"type\":\"%s\", \"msgTypes\": [\"new object\", \"key\"]},", dmlType);
                    } else if (msgType[0] == 0 && msgType[1] == 1 && msgType[2] == 1) {
                        snprintf(eventType + k, sizeof(eventType) - k,
                            " {\"type\":\"%s\", \"msgTypes\": [\"old object\", \"key\"]},", dmlType);
                    } else {
                        snprintf(eventType + k, sizeof(eventType) - k,
                            " {\"type\":\"%s\", \"msgTypes\": [\"old object\", \"new object\", \"key\"]},", dmlType);
                    }
                    k = strlen(eventType);
                }
                ++i;
            }
            k = strlen(eventType);
            snprintf(eventType + k - 1, sizeof(eventType) - k, " ],");
        } while (0);
        pos = strtok_r(NULL, ",", &left);
    }
    if (k != 0) {
        k = strlen(eventType);
        u->eventType = (char *)malloc(k + 1);
        if (u->eventType == NULL) {
            printf("malloc memory(%d) for u->eventType failed.\n", k);
            exit(1);
        }
        snprintf(u->eventType, k + 1, "%s", eventType);
    }
}

void generateExeUnit(runUnit *u, char *buf)
{
    u->init();
    char s[4][1024];
    sscanf(
        buf, "%s %d %s %s %s %d %d %s", s[0], &u->exeSeq, s[1], u->metaName, s[2], &u->beginCycle, &u->endCycle, s[3]);
    int i;
    int j = 0;
    int len = strlen(s[0]);
    for (i = 0; i < len; ++i) {
        if (s[0][i] != '.') {
            u->id[j++] = s[0][i];
        }
    }
    u->id[j] = '\0';
    if (u->exeSeq != 0 && u->exeSeq != 1) {
        printf("invalid configuration[1], value:%d, expect:[0,1].\n");
        exit(2);
    }
    if (0 == strcmp(s[1], "vertex")) {
        u->objType = OBJ_TYPE_VERTEX;
    } else if (0 == strcmp(s[1], "edge")) {
        u->objType = OBJ_TYPE_EDGE;
    } else if (0 == strcmp(s[1], "kv")) {
        u->objType = OBJ_TYPE_KV;
    } else if (0 == strcmp(s[1], "namespace")) {
        u->objType = OBJ_TYPE_NAMESPACE;
    } else if (0 == strcmp(s[1], "respool")) {
        u->objType = OBJ_TYPE_RES;
    } else if (0 == strcmp(s[1], "checkbill")) {
        u->objType = OBJ_TYPE_BILL;
    } else {
        printf("invalid configuration, value:%s, expect:vertex/edge/ds/kv\n", s[1]);
        exit(2);
    }
    u->oprCmd = getOperationCommand(s[1], s[2], &u->oprType);
    if (u->oprCmd == -1) {
        printf("invalid configuration[4], value:%s.\n", s[2]);
        exit(2);
    }
    if (0 == strcmp(s[1], "vertex") &&
        (0 == strcmp(s[2], "scan") || 0 == strcmp(s[2], "updateF") || 0 == strcmp(s[2], "deleteF"))) {
        u->isFilter = true;
    }
    if (u->beginCycle < 0) {
        printf("invalid configuration[5,6], value:%d-%d.\n", u->beginCycle, u->endCycle);
        exit(2);
    }
    if (u->beginCycle >= u->endCycle &&
        (0 != strcmp(s[2], "create") && 0 != strcmp(s[2], "tab_sub") && 0 != strcmp(s[2], "path_sub"))) {
        printf("invalid configuration[5,6], value:%d-%d.\n", u->beginCycle, u->endCycle);
        exit(2);
    }
    u->countCycle = u->endCycle - u->beginCycle;
    len = strlen(buf);
    i = 0;
    while (i < len && buf[i] != '=') {
        ++i;
    }
    if (i == len) {
        return;
    }
    while (i > 0 && buf[i] != ' ') {
        --i;
    }
    ++i;
    int k;
    char ch;
    int *v;
    while (i < len) {
        while (i < len && buf[i] == ' ') {
            ++i;
        }
        j = i;
        while (i < len && (buf[i] != ' ' && buf[i] != '=')) {
            ++i;
        }
        if (i == len) {
            break;
        }
        ch = buf[i];
        buf[i] = '\0';
        k = 0;
        if (0 == strcmp(buf + j, "ops")) {
            ++k;
            v = &u->ops;
        } else if (0 == strcmp(buf + j, "batchNum")) {
            ++k;
            v = &u->batchNum;
        } else if (0 == strcmp(buf + j, "transNum")) {
            ++k;
            v = &u->transNum;
        } else if (0 == strcmp(buf + j, "transMode")) {
            ++k;
            v = &u->transMode;
        } else if (0 == strcmp(buf + j, "repeatNum")) {
            ++k;
            v = &u->repeatNum;
        } else if (0 == strcmp(buf + j, "isolation")) {
            ++k;
            v = &u->isolation;
        } else if (0 == strcmp(buf + j, "dataRule")) {
            ++k;
            v = &u->dataRule;
        } else if (0 == strcmp(buf + j, "asyncSize")) {
            ++k;
            v = &u->asyncSize;
        }
        buf[i] = ch;
        while (i < len && (buf[i] == ' ' || buf[i] == '=')) {
            ++i;
        }
        if (i == len) {
            printf("invalid configuration(extend msg), 2 value:%s.\n", buf);
            exit(2);
        }
        if (k == 1) {
            *v = atoi(buf + i);
            i += 1;
        } else if (0 == strncmp(buf + j, "connMode", 8)) {
            if (0 == strncmp(buf + i, "sync", 4)) {
                u->connMode = 0;
            } else if (0 == strncmp(buf + i, "async", 5)) {
                u->connMode = 1;
                i += 5;
                if (u->asyncSize <= 0) {
                    u->asyncSize = 32;
                }
            } else if (0 == strncmp(buf + i, "pub", 3)) {
                u->connMode = 2;
                i += 3;
            } else {
                printf("invalid configuration(connMode), value:%s.\n", buf);
                exit(2);
            }
        } else if (0 == strncmp(buf + j, "idxName", 7)) {
            k = i;
            while (i < len && buf[i] != ' ' && buf[i] != ':') {
                u->idxName[i - k] = buf[i];
                ++i;
            }
            u->idxName[i - k] = '\0';
            if (buf[i] == ':') {
                k = i + 1;
                while (k < len && (buf[k] != ' ' && buf[k] != '\"')) {
                    ++k;
                }
                if (k > len) {
                    printf("invalid configuration(idxName), value:%s.\n", buf);
                    exit(1);
                }
                ch = buf[k];
                buf[k] = '\0';
                if (parseParamRangeField(buf + i + 1, u) != 0) {
                    printf("[generateExeUnit] parseParamRangeField(%s) failed.\n", buf + i + 1);
                    exit(1);
                }
                buf[k] = ch;
                i = k;
            }
        } else if (0 == strncmp(buf + j, "filter", 6)) {
            if (buf[i] != '\'' && buf[i] != '\"') {
                printf("invalid configuration(filter), expect quote as begin, value:%s.\n", buf);
                exit(1);
            }
            ch = buf[i];
            ++i;
            k = i;
            while (i < len && buf[i - 1] != '\\' && buf[i] != ch) {
                ++i;
            }
            if (i == len) {
                printf("invalid configuration(filter), expect quote as end, value:%s.\n", buf);
                exit(1);
            }
            buf[i] = '\0';
            if (parseParamFilter(buf + k, u) != 0) {
                printf("[generateExeUnit] parseParamFilter(%s) failed.\n", buf + k);
                exit(1);
            }
            buf[i] = ch;
        } else if (0 == strncmp(buf + j, "file", 4) || 0 == strncmp(buf + j, "key", 3) ||
                   0 == strncmp(buf + j, "file2", 5) || 0 == strncmp(buf + j, "text", 4) ||
                   0 == strncmp(buf + j, "value", 5) || 0 == strncmp(buf + j, "name", 4) ||
                   0 == strncmp(buf + j, "subInfo", 7)) {
            while (i < len && buf[i] != '\'' && buf[i] != '"') {
                ++i;
            }
            ++i;
            k = i;
            while (i < len && buf[i - 1] != '\\' && buf[i] != '\'' && buf[i] != '"') {
                ++i;
            }
            char *strTmp = (char *)malloc(i - k + 4);
            if (strTmp == NULL) {
                printf("malloc memory(%d) for strTmp failed.\n", i - k + 4);
                exit(2);
            }
            if (0 == strncmp(buf + j, "file", 4) || 0 == strncmp(buf + j, "key", 3)) {
                u->keyStr = strTmp;
            } else if (0 == strncmp(buf + j, "file2", 5) || 0 == strncmp(buf + j, "text", 4) ||
                       0 == strncmp(buf + j, "value", 5)) {
                u->verStr = strTmp;
            } else if (0 == strncmp(buf + j, "name", 4)) {
                u->cacheName = strTmp;
            } else if (0 == strncmp(buf + j, "subInfo", 4)) {
                u->eventType = strTmp;
            } else {
                printf("invalid configuration, info:%s.\n", buf);
                exit(1);
            }
            for (j = k; j < i; ++j) {
                strTmp[j - k] = buf[j];
            }
            strTmp[i - k] = '\0';
            if (u->eventType) {
                if (0 == access(u->eventType, F_OK)) {
                    u->snCfg[0] = 2;
                    u->keyStr = u->eventType;
                    u->eventType = NULL;
                } else {
                    parseParamSubinfo(u, strTmp);
                    free(strTmp);
                }
            }
        } else {
            printf("invalid configuration(extend msg), 4 value:%s.\n", buf);
            exit(2);
        }
        while (i < len && buf[i] != ' ') {
            ++i;
        }
    }
}

void generateGroup2(runGroup *r, bool *connFlag, int startUid, int stopUid, int pos)
{
    runUnit *r1, *r2;
    int i, j, len;
    int sum1 = 0;
    int sum2 = 0;
    for (i = startUid; i <= stopUid; ++i) {
        len = strlen(g_runUnit[i].id);
        if (len <= pos) {
            continue;
        }
        ++sum1;
        if (pos + 1 == len) {
            ++sum2;
            continue;
        }
        for (j = i + 1; j <= stopUid; ++j) {
            if (0 != strncmp(g_runUnit[i].id, g_runUnit[j].id, pos + 1)) {
                break;
            }
        }
        i = j - 1;
    }
    r->numAll = sum1;
    r->numExe = sum2;
    r->isSubGroup = (bool *)malloc(sizeof(sum1 + 1));
    if (r->isSubGroup == NULL) {
        printf("malloc memory(%d) for r->isSubGroup failed.\n", sum1 + 1);
        exit(1);
    }
    memset(r->isSubGroup, 0, sum1 + 1);
    r->runList = (void **)malloc(sizeof(void *) * sum1);
    if (r->runList == NULL) {
        printf("malloc memory(%ld) for r->runList failed.\n", sizeof(void *) * sum1);
        exit(1);
    }
    runGroup *subR = (runGroup *)malloc(sizeof(runGroup) * (sum1 - sum2));
    if (subR == NULL) {
        printf("malloc memory(%ld) for subR failed.\n", sizeof(runGroup) * (sum1 - sum2));
        exit(1);
    }
    sum1 = 0;
    sum2 = 0;
    for (i = startUid; i <= stopUid; ++i) {
        len = strlen(g_runUnit[i].id);
        if (len <= pos) {
            continue;
        }
        if (pos + 1 == len) {
            connFlag[g_runUnit[i].connMode] = true;
            if (g_runUnit[i].connMode == 2) {
                connFlag[0] = true;
            }
            r->runList[sum1 + sum2] = g_runUnit + i;
            ++sum1;
            continue;
        }
        r->runList[sum1 + sum2] = subR + sum2;
        r->isSubGroup[sum1 + sum2] = true;
        for (j = i + 1; j <= stopUid; ++j) {
            if (0 != strncmp(g_runUnit[i].id, g_runUnit[j].id, pos + 1)) {
                break;
            }
        }
        generateGroup2((runGroup *)r->runList[sum1 + sum2], connFlag, i, j - 1, pos + 1);
        ++sum2;
        i = j - 1;
    }
}

void generateGroup1(sceneEntry *sE)
{
    if (sE == NULL || sE->startUid == -1) {
        printf("[generateGroup] invalid sceneEntry.\n");
        exit(3);
    }
    int v1 = sE->startUid;
    sE->preShell = NULL;
    if (atoi(g_runUnit[v1].id) == RUN_UNIT_ID_PRESHELL) {
        sE->numPre = 1;
        sE->preShell = g_runUnit + v1;
        ++v1;
    }
    while (v1 <= sE->stopUid && atoi(g_runUnit[v1].id) == RUN_UNIT_ID_PRESHELL) {
        ++v1;
        ++sE->numPre;
    }
    int v2 = v1 + 1;
    while (v2 <= sE->stopUid && atoi(g_runUnit[v2].id) != RUN_UNIT_ID_AFTERSHELL) {
        ++v2;
    }
    sE->afterShell = NULL;
    if (v2 <= sE->stopUid) {
        sE->numAfter = sE->stopUid - v2 + 1;
        sE->afterShell = g_runUnit + v2;
        --v2;
    } else {
        v2 = sE->stopUid;
    }
    sE->numRun = v2 - v1 + 1;
    generateGroup2(&sE->r, sE->connFlag, v1, v2, 0);
}

void generateTestScene(char *file)
{
    if (file == NULL) {
        printf("scene_file is null\n");
        exit(1);
    }
    if (0 != access(file, F_OK)) {
        printf("scene_file:%s is not existant.\n", file);
        exit(1);
    }
    FILE *pf = fopen(file, "r");
    if (pf == NULL) {
        printf("open scene_file:%s failed.\n", file);
        exit(1);
    }
    g_sceneNum = 0;
    g_unitNum = 0;
    char strBuf[1024];
    while (NULL != fgets(strBuf, sizeof(strBuf), pf)) {
        if (strBuf[0] == '#' || strlen(strBuf) < 10) {
            continue;
        }
        if (0 == strncmp(strBuf, "[scene ", 7)) {
            ++g_sceneNum;
        } else {
            ++g_unitNum;
        }
    }
    if (g_sceneNum == 0 || g_unitNum == 0) {
        fclose(pf);
        printf("invalid scene_file:%s.\n", file);
        exit(1);
    }
    g_sceneEntry = (sceneEntry *)malloc(sizeof(sceneEntry) * g_sceneNum);
    if (g_sceneEntry == NULL) {
        printf("malloc memory(%lu) for g_sceneEntry failed.\n", sizeof(sceneEntry) * g_sceneNum);
        exit(1);
    }
    g_runUnit = (runUnit *)malloc(sizeof(runUnit) * g_unitNum);
    if (g_runUnit == NULL) {
        printf("malloc memory(%lu) for g_runUnit failed.\n", sizeof(runUnit) * g_unitNum);
        exit(1);
    }
    if (0 != fseek(pf, 0L, SEEK_SET)) {
        fclose(pf);
        printf("reset file pos to head failed,  scene_file:%s.\n", file);
        exit(1);
    }
    int i, len;
    sceneEntry *sE = NULL;
    runUnit *rU = NULL;
    char s[2][128];
    int id = 0;
    g_threadCount = 0;
    GmcStmtT **stmt;
    while (NULL != fgets(strBuf, sizeof(strBuf), pf)) {
        if (strBuf[0] == '#' || strlen(strBuf) < 10) {
            continue;
        }
        len = strlen(strBuf);
        while (strBuf[len - 1] == '\r' || strBuf[len - 1] == '\n') {
            strBuf[len - 1] = '\0';
            len -= 1;
        }
        if (0 == strncmp(strBuf, "[scene ", 7)) {
            if (sE == NULL) {
                sE = g_sceneEntry;
            } else {
                generateGroup1(sE);
                sE += 1;
            }
            memset(sE, 0, sizeof(sceneEntry));
            sscanf(strBuf, "%s %s %d %s", s[0], sE->nameSpace, &sE->threadCount, s[1]);
            sE->isTime = false;
            sE->runTime = atoi(s[1]);
            sE->startUid = -1;
            sE->startTid = g_threadCount;
            g_threadCount += sE->threadCount;
            len = strlen(s[1]) - 1;
            while (len >= 0 && s[1][0] != 's') {
                --len;
            }
            if (len >= 0) {
                sE->isTime = true;
            }
        } else {
            if (rU == NULL) {
                rU = g_runUnit;
            } else {
                ++rU;
            }
            generateExeUnit(rU, strBuf);
            if (rU->transNum > 10000) {
                rU->transNum -= 10000;
                rU->transMode += 10;
            }
            if (rU->asyncSize > 0) {
                rU->acl = (asyncCtrl **)malloc(sizeof(asyncCtrl *) * sE->threadCount * 2);
                if (rU->acl == NULL) {
                    printf("malloc memory(%lu) for rU->acl failed.\n", sizeof(void *) * sE->threadCount * 2);
                    exit(1);
                }
                rU->acx = (asyncContex **)(rU->acl + sE->threadCount);
                rU->acl[0] = (asyncCtrl *)malloc(sizeof(asyncCtrl) * sE->threadCount);
                if (rU->acl[0] == NULL) {
                    printf("malloc memory(%lu) for rU->acl[0] failed.\n", sizeof(asyncCtrl) * sE->threadCount);
                    exit(1);
                }
                rU->acx[0] = (asyncContex *)malloc(sizeof(asyncContex) * sE->threadCount * rU->asyncSize);
                if (rU->acx[0] == NULL) {
                    printf("malloc memory(%lu) for rU->acx[0] failed.\n",
                        sizeof(asyncContex) * sE->threadCount * rU->asyncSize);
                    exit(1);
                }
                for (i = 1; i < sE->threadCount; ++i) {
                    rU->acl[i] = rU->acl[0] + i;
                    rU->acx[i] = rU->acx[0] + i * rU->asyncSize;
                }
                memset(rU->acl[0], 0, sizeof(asyncCtrl) * sE->threadCount);
                memset(rU->acx[0], 0, sizeof(asyncContex) * sE->threadCount * rU->asyncSize);
            }
            rU->lastStrPos = (int *)malloc(sizeof(int) * sE->threadCount);
            if (rU->lastStrPos == NULL) {
                printf("malloc memory(%lu) for rU->lastStrPos failed.\n", sizeof(int) * sE->threadCount);
                exit(1);
            }
            memset(rU->lastStrPos, 0, sizeof(int) * sE->threadCount);
            if (rU->batchNum > 0) {
                rU->countBatch = (int *)malloc(sizeof(int) * sE->threadCount);
                if (rU->countBatch == NULL) {
                    printf("malloc memory(%lu) for rU->countBatch failed.\n", sizeof(int) * sE->threadCount);
                    exit(1);
                }
                memset(rU->countBatch, 0, sizeof(int) * sE->threadCount);
                rU->ctxBatch = (GmcBatchT **)malloc(sizeof(GmcBatchT *) * sE->threadCount);
                if (rU->ctxBatch == NULL) {
                    printf("malloc memory(%lu) for rU->ctxBatch failed.\n", sizeof(GmcBatchT *) * sE->threadCount);
                    exit(1);
                }
                memset(rU->ctxBatch, 0, sizeof(GmcBatchT *) * sE->threadCount);
            }
            if (rU->oprCmd >= DML_SUB_TABLE_START_ONCE && rU->oprCmd <= DML_SUB_PATH_STOP) {
                rU->connMode = 2;
                if (rU->cacheName) {
                    snprintf(sE->snName, sizeof(sE->snName), "%s", rU->cacheName);
                    free(rU->cacheName);
                    rU->cacheName = NULL;
                }
            }
            rU->isPrepare = (bool *)malloc(sE->threadCount);
            if (rU->isPrepare == NULL) {
                printf("malloc memory(%d) for rU->isPrepare failed.\n", sE->threadCount);
                exit(1);
            }
            memset(rU->isPrepare, 0, sE->threadCount);
            stmt = (GmcStmtT **)malloc(sizeof(GmcStmtT *) * sE->threadCount * 7);
            if (stmt == NULL) {
                printf("malloc memory(%lu) for stmt failed.\n", sizeof(GmcStmtT *) * sE->threadCount);
                exit(1);
            }
            memset(stmt, 0, sizeof(void *) * sE->threadCount * 7);
            rU->stmt = stmt;
            rU->inputV = (char **)(stmt + sE->threadCount);
            rU->inputI = (char **)(stmt + sE->threadCount * 2);
            rU->firstV = (void **)stmt + sE->threadCount * 3;
            rU->firstI = (void **)stmt + sE->threadCount * 4;
            rU->varV = (void **)stmt + sE->threadCount * 5;
            rU->varI = (void **)stmt + sE->threadCount * 6;
            for (i = 0; i < sE->threadCount; ++i) {
                rU->inputV[i] = NULL;
                rU->inputI[i] = NULL;
                rU->firstV[i] = NULL;
                rU->firstI[i] = NULL;
                rU->varV[i] = NULL;
                rU->varI[i] = NULL;
            }
            if (sE->startUid == -1) {
                sE->startUid = id;
            }
            sE->stopUid = id;
            generateRangeCond(rU, sE->threadCount);
            generateVertexCond(rU, sE->threadCount);
            generateKvCond(rU, sE->threadCount);
            ++id;
        }
    }
    generateGroup1(sE);
    fclose(pf);
}

void printGroupMsg(runGroup *r, int offset)
{
    char str[128];
    int i;
    for (i = 0; i < offset; ++i) {
        str[i] = ' ';
    }
    str[i] = '\0';
    printf("%s[group]  num:%d, exe:%d.\n", str, r->numAll, r->numExe);
    runUnit *u;
    for (i = 0; i < r->numAll; ++i) {
        if (r->isSubGroup[i]) {
            printGroupMsg((runGroup *)r->runList[i], offset + 1);
        } else {
            u = (runUnit *)r->runList[i];
            printf(" %s[unit] talbeName:%s, command:%d\n", str, u->metaName, u->oprCmd);
        }
    }
}

void printSceneMsg()
{
    if (g_sceneNum == 0) {
        return;
    }
    printf("scene_num:%d, exe_unit:%d \n", g_sceneNum, g_unitNum);
    for (int i = 0; i < g_sceneNum; ++i) {
        printf("---------------------------------------------------------------------------\n");
        printf("%02d threadCount=%d, runTime=%d, numPre=%d, numRun=%d, numAfter=%d\n", i, g_sceneEntry[i].threadCount,
            g_sceneEntry[i].runTime, g_sceneEntry[i].numPre, g_sceneEntry[i].numRun, g_sceneEntry[i].numAfter);
        printGroupMsg(&g_sceneEntry[i].r, 1);
    }
}

#endif // #define  __PERF_COMMON_H__
