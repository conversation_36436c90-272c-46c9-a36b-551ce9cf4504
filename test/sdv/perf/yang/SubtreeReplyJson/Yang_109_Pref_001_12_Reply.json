{"yang": {"ds": [{"name": "running", "huawei-vlan": [{"huawei-vlan:vlan": {"vlans": {"vlan": [{"id": 4030, "member-ports": {"member-port": [{"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 12, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 34, "forwarded-report-count": 34, "forwarded-leave-count": 0, "forwarded-query-count": 12}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4031, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 26, "forwarded-report-count": 26, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4032, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 11, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 30, "forwarded-report-count": 30, "forwarded-leave-count": 0, "forwarded-query-count": 11}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4033, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 22, "forwarded-report-count": 22, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4034, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 15, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 30, "forwarded-report-count": 30, "forwarded-leave-count": 0, "forwarded-query-count": 15}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4035, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 19, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 31, "forwarded-report-count": 31, "forwarded-leave-count": 0, "forwarded-query-count": 19}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4036, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 20, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 17, "forwarded-report-count": 17, "forwarded-leave-count": 0, "forwarded-query-count": 20}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4037, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 11, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 32, "forwarded-report-count": 32, "forwarded-leave-count": 0, "forwarded-query-count": 11}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4038, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4039, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 19, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 29, "forwarded-report-count": 29, "forwarded-leave-count": 0, "forwarded-query-count": 19}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4040, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 25, "forwarded-report-count": 25, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4041, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 30, "forwarded-report-count": 30, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4042, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 25, "forwarded-report-count": 25, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4043, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 26, "forwarded-report-count": 26, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4044, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 14, "forwarded-report-count": 14, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4045, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 19, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 33, "forwarded-report-count": 33, "forwarded-leave-count": 0, "forwarded-query-count": 19}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4046, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 29, "forwarded-report-count": 29, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4047, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 20, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 20}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4048, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 25, "forwarded-report-count": 25, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4049, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 32, "forwarded-report-count": 32, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4050, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 13, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 29, "forwarded-report-count": 29, "forwarded-leave-count": 0, "forwarded-query-count": 13}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4051, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "router-ports": {"router-port": [{"interface": "10GE0/0/1", "up-time": 116, "expire-time": 64, "flag": "dynamic"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 29, "forwarded-report-count": 29, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4052, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 20, "forwarded-report-count": 20, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4053, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 12, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 21, "forwarded-report-count": 21, "forwarded-leave-count": 0, "forwarded-query-count": 12}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4054, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 15, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 15}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4055, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4056, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 13, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 26, "forwarded-report-count": 26, "forwarded-leave-count": 0, "forwarded-query-count": 13}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4057, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 29, "forwarded-report-count": 29, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4058, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 15, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 22, "forwarded-report-count": 22, "forwarded-leave-count": 0, "forwarded-query-count": 15}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4059, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 31, "forwarded-report-count": 31, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4060, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 25, "forwarded-report-count": 25, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4061, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 11, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 27, "forwarded-report-count": 27, "forwarded-leave-count": 0, "forwarded-query-count": 11}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4062, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 22, "forwarded-report-count": 22, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4063, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 21, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 21, "forwarded-report-count": 21, "forwarded-leave-count": 0, "forwarded-query-count": 21}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4064, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 13, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 37, "forwarded-report-count": 37, "forwarded-leave-count": 0, "forwarded-query-count": 13}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4065, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4066, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 9, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 20, "forwarded-report-count": 20, "forwarded-leave-count": 0, "forwarded-query-count": 9}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4067, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "router-ports": {"router-port": [{"interface": "10GE0/0/1", "up-time": 114, "expire-time": 66, "flag": "dynamic"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4068, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 34, "forwarded-report-count": 34, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4069, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 11, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 27, "forwarded-report-count": 27, "forwarded-leave-count": 0, "forwarded-query-count": 11}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4070, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 12, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 29, "forwarded-report-count": 29, "forwarded-leave-count": 0, "forwarded-query-count": 12}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4071, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 15, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 15}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4072, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 19, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 20, "forwarded-report-count": 20, "forwarded-leave-count": 0, "forwarded-query-count": 19}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4073, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "router-ports": {"router-port": [{"interface": "10GE0/0/1", "up-time": 114, "expire-time": 66, "flag": "dynamic"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 23, "forwarded-report-count": 23, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4074, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 13, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 23, "forwarded-report-count": 23, "forwarded-leave-count": 0, "forwarded-query-count": 13}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4075, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 11, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 26, "forwarded-report-count": 26, "forwarded-leave-count": 0, "forwarded-query-count": 11}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4076, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 20, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 29, "forwarded-report-count": 29, "forwarded-leave-count": 0, "forwarded-query-count": 20}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4077, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4078, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 13, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 13}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4079, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 22, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 22}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4080, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 34, "forwarded-report-count": 34, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4081, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 15, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 23, "forwarded-report-count": 23, "forwarded-leave-count": 0, "forwarded-query-count": 15}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4082, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "router-ports": {"router-port": [{"interface": "10GE0/0/1", "up-time": 114, "expire-time": 66, "flag": "dynamic"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 27, "forwarded-report-count": 27, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4083, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "router-ports": {"router-port": [{"interface": "10GE0/0/1", "up-time": 114, "expire-time": 66, "flag": "dynamic"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 20, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 25, "forwarded-report-count": 25, "forwarded-leave-count": 0, "forwarded-query-count": 20}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4084, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 30, "forwarded-report-count": 30, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4085, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4086, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 27, "forwarded-report-count": 27, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4087, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "router-ports": {"router-port": [{"interface": "10GE0/0/1", "up-time": 114, "expire-time": 66, "flag": "dynamic"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4088, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 8, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 21, "forwarded-report-count": 21, "forwarded-leave-count": 0, "forwarded-query-count": 8}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4089, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 9, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 9}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4090, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "router-ports": {"router-port": [{"interface": "10GE0/0/1", "up-time": 114, "expire-time": 66, "flag": "dynamic"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 19, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 25, "forwarded-report-count": 25, "forwarded-leave-count": 0, "forwarded-query-count": 19}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4091, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 23, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 37, "forwarded-report-count": 37, "forwarded-leave-count": 0, "forwarded-query-count": 23}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4092, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 25, "forwarded-report-count": 25, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4093, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 13, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 20, "forwarded-report-count": 20, "forwarded-leave-count": 0, "forwarded-query-count": 13}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4094, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 21, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 12, "type": "common", "mac-learning": "enable", "@mac-learning": {"ietf-netconf-with-defaults:default": "true"}, "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "alarm-reply-enable": false, "@alarm-reply-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-mac-enable": false, "@check-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-mac-enable": false, "@alarm-mac-enable": {"ietf-netconf-with-defaults:default": "true"}, "check-user-bind-enable": false, "@check-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}, "alarm-user-bind-enable": false, "@alarm-user-bind-enable": {"ietf-netconf-with-defaults:default": "true"}}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "router-port-discard": false, "@router-port-discard": {"ietf-netconf-with-defaults:default": "true"}, "immediately-leave": false, "@immediately-leave": {"ietf-netconf-with-defaults:default": "true"}, "proxy-enable": false, "@proxy-enable": {"ietf-netconf-with-defaults:default": "true"}, "querier-enable": false, "@querier-enable": {"ietf-netconf-with-defaults:default": "true"}, "query-interval": 60, "@query-interval": {"ietf-netconf-with-defaults:default": "true"}, "router-aging-time": 180, "@router-aging-time": {"ietf-netconf-with-defaults:default": "true"}, "querier": {"querier-state": false, "querier-address": ""}}, "interface-groups": {"interface-group": [{"interface": "10GE0/0/2", "groups": {"group": [{"group-address": "*********", "up-time": 113, "expire-time": 17, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}]}}]}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 25, "forwarded-report-count": 25, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}]}}}]}]}}