[{"name": "ds", "source_vertex_label": "yang", "dest_vertex_label": "ds", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library", "source_vertex_label": "ds", "dest_vertex_label": "ietf-yang-library", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::module-set", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::feature", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::feature", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::deviation", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::deviation", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::schema", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema::module-set", "source_vertex_label": "ietf-yang-library:yang-library::schema", "dest_vertex_label": "ietf-yang-library:yang-library::schema::module-set", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::datastore", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::datastore", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "notifications", "source_vertex_label": "ds", "dest_vertex_label": "notifications", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications", "source_vertex_label": "ds", "dest_vertex_label": "nc-notifications", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications:netconf::streams::stream", "source_vertex_label": "nc-notifications", "dest_vertex_label": "nc-notifications:netconf::streams::stream", "source_node_path": "/nc-notifications:netconf/streams", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry", "source_vertex_label": "ds", "dest_vertex_label": "openconfig-telemetry", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "source_node_path": "/openconfig-telemetry:telemetry-system/sensor-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "dest_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_node_path": "/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "source_node_path": "/openconfig-telemetry:telemetry-system/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "source_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "dest_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "source_node_path": "/destinations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "source_node_path": "/openconfig-telemetry:telemetry-system/subscriptions/persistent", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "source_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "source_node_path": "/sensor-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "source_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ztp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ztp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "<PERSON><PERSON><PERSON>-web-manager", "source_vertex_label": "ds", "dest_vertex_label": "<PERSON><PERSON><PERSON>-web-manager", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-web-manager:web-manager::source-interface::interfaces::interface", "source_vertex_label": "<PERSON><PERSON><PERSON>-web-manager", "dest_vertex_label": "huawei-web-manager:web-manager::source-interface::interfaces::interface", "source_node_path": "/huawei-web-manager:web-manager/source-interface/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ifm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface", "source_vertex_label": "huawei-ifm", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface", "source_node_path": "/huawei-ifm:ifm/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:dhcp-client-if::request-option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:dhcp-client-if::request-option", "source_node_path": "/huawei-dhcp:dhcp-client-if", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/dns-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/excluded-ip-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/static-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "source_node_path": "/option-format/ip-format", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "source_node_path": "/option-format/sub-options-format/sub-options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "source_node_path": "/option-format/sub-ip-format", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::huawei-erps:erps-attribute::rings::ring", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::huawei-erps:erps-attribute::rings::ring", "source_node_path": "/huawei-ethernet:ethernet/main-interface/l2-attribute/huawei-erps:erps-attribute/rings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "source_node_path": "/huawei-ethernet:ethernet/main-interface/port-isolate-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ifm-trunk:trunk::members::member", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ifm-trunk:trunk::members::member", "source_node_path": "/huawei-ifm-trunk:trunk/members", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-igmp-mld-snooping:igmp-snooping::static-groups::static-group", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-igmp-mld-snooping:igmp-snooping::static-groups::static-group", "source_node_path": "/huawei-igmp-mld-snooping:igmp-snooping/static-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-igmp-mld-snooping:igmp-snooping::proxy-uplink-ports::proxy-uplink-port", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-igmp-mld-snooping:igmp-snooping::proxy-uplink-ports::proxy-uplink-port", "source_node_path": "/huawei-igmp-mld-snooping:igmp-snooping/proxy-uplink-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses::address", "source_node_path": "/huawei-ip:ipv4/address/common-address/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses::address", "source_node_path": "/huawei-ip:ipv4/state/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps::static-arp", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps::static-arp", "source_node_path": "/huawei-ip:ipv4/huawei-arp:static-arps", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "source_node_path": "/huawei-lldp:lldp/session/neighbors", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "source_node_path": "/management-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "source_node_path": "/protocol-vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "source_node_path": "/vlan-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "source_node_path": "/unknown-tlvs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "source_node_path": "/unknown-organizationally-defined-tlvs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "source_node_path": "/med-tlv/capability", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "source_node_path": "/med-tlv/network-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "source_node_path": "/legacy-power-capability", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-mirror:mirror::observe-ports::observe-port", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-mirror:mirror::observe-ports::observe-port", "source_node_path": "/huawei-mirror:mirror/observe-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-qos:qos::car-templates::car-template", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-qos:qos::car-templates::car-template", "source_node_path": "/huawei-qos:qos/car-templates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "source_node_path": "/huawei-sacl:traffic-filter-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-statistics-applys::traffic-statistics-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-statistics-applys::traffic-statistics-apply", "source_node_path": "/huawei-sacl:traffic-statistics-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-statistics-applys::traffic-statistics-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "source_node_path": "/huawei-sacl:traffic-remark-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-limit-applys::traffic-limit-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-limit-applys::traffic-limit-apply", "source_node_path": "/huawei-sacl:traffic-limit-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-limit-applys::traffic-limit-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "source_node_path": "/huawei-sacl:traffic-redirect-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "source_node_path": "/huawei-sacl:traffic-mirror-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-storm-control:storm-control::storm-rates::storm-rate", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-storm-control:storm-control::storm-rates::storm-rate", "source_node_path": "/huawei-storm-control:storm-control/storm-rates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-arp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp:arp::query-entries::query-entry", "source_vertex_label": "huawei-arp", "dest_vertex_label": "huawei-arp:arp::query-entries::query-entry", "source_node_path": "/huawei-arp:arp/query-entries", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp:arp::statistics::statistic", "source_vertex_label": "huawei-arp", "dest_vertex_label": "huawei-arp:arp::statistics::statistic", "source_node_path": "/huawei-arp:arp/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-dhcp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "source_node_path": "/huawei-dhcp:dhcp/server/ip-pool-querys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::snooping::static-binds::static-bind", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::snooping::static-binds::static-bind", "source_node_path": "/huawei-dhcp:dhcp/snooping/static-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::snooping::dynamic-binds::dynamic-bind", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::snooping::dynamic-binds::dynamic-bind", "source_node_path": "/huawei-dhcp:dhcp/snooping/dynamic-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::client::client-querys::client-query", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::client::client-querys::client-query", "source_node_path": "/huawei-dhcp:dhcp/client/client-querys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::client::client-dns-server-querys::client-dns-server-query", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::client::client-dns-server-querys::client-dns-server-query", "source_node_path": "/huawei-dhcp:dhcp/client/client-dns-server-querys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-erps", "source_vertex_label": "ds", "dest_vertex_label": "huawei-erps", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-erps:erps::rings::ring", "source_vertex_label": "huawei-erps", "dest_vertex_label": "huawei-erps:erps::rings::ring", "source_node_path": "/huawei-erps:erps/rings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-erps:erps::rings::ring::erps-port-infos::erps-port-info", "source_vertex_label": "huawei-erps:erps::rings::ring", "dest_vertex_label": "huawei-erps:erps::rings::ring::erps-port-infos::erps-port-info", "source_node_path": "/erps-port-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-mstp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp:mstp::default-process::cist-info::cist-port-infos::cist-port-info", "source_vertex_label": "huawei-mstp", "dest_vertex_label": "huawei-mstp:mstp::default-process::cist-info::cist-port-infos::cist-port-info", "source_node_path": "/huawei-mstp:mstp/default-process/cist-info/cist-port-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp:mstp::default-process::interface-bpdu-statistics::interface-bpdu-statistic", "source_vertex_label": "huawei-mstp", "dest_vertex_label": "huawei-mstp:mstp::default-process::interface-bpdu-statistics::interface-bpdu-statistic", "source_node_path": "/huawei-mstp:mstp/default-process/interface-bpdu-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm-trunk", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ifm-trunk", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-igmp-mld-snooping", "source_vertex_label": "ds", "dest_vertex_label": "huawei-igmp-mld-snooping", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-lldp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-supported", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::system-capabilities-supported", "source_node_path": "/huawei-lldp:lldp/local-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "source_node_path": "/huawei-lldp:lldp/local-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::management-addresss::management-address", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::management-addresss::management-address", "source_node_path": "/huawei-lldp:lldp/local-info/management-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mirror", "source_vertex_label": "ds", "dest_vertex_label": "huawei-mirror", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mirror:mirror::global-observers::global-observer", "source_vertex_label": "huawei-mirror", "dest_vertex_label": "huawei-mirror:mirror::global-observers::global-observer", "source_node_path": "/huawei-mirror:mirror/global-observers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos", "source_vertex_label": "ds", "dest_vertex_label": "huawei-qos", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos:qos::car-template::templates::template", "source_vertex_label": "huawei-qos", "dest_vertex_label": "huawei-qos:qos::car-template::templates::template", "source_node_path": "/huawei-qos:qos/car-template/templates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos:qos::global-query::acl-resource-usages::acl-resource-usage", "source_vertex_label": "huawei-qos", "dest_vertex_label": "huawei-qos:qos::global-query::acl-resource-usages::acl-resource-usage", "source_node_path": "/huawei-qos:qos/global-query/acl-resource-usages", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sacl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply", "source_node_path": "/huawei-sacl:sacl/traffic-filter-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply", "source_node_path": "/huawei-sacl:sacl/traffic-remark-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply", "source_node_path": "/huawei-sacl:sacl/traffic-limit-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply", "source_node_path": "/huawei-sacl:sacl/traffic-statistics-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply", "source_node_path": "/huawei-sacl:sacl/traffic-redirect-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance", "source_vertex_label": "ds", "dest_vertex_label": "huawei-network-instance", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance", "source_vertex_label": "huawei-network-instance", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance", "source_node_path": "/huawei-network-instance:network-instance/instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "source_node_path": "/huawei-l3vpn:afs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "source_node_path": "/huawei-routing:routing/routing-manage/topologys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "source_node_path": "/routes/ipv4-unicast-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "source_node_path": "/routes/ipv4-route-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "source_node_path": "/huawei-routing:routing/static-routing/unicast-route2s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "source_node_path": "/nexthop-interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "source_node_path": "/nexthop-interface-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "source_node_path": "/nexthop-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "source_node_path": "/huawei-routing:routing/static-routing/ipv4-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-routing", "source_vertex_label": "ds", "dest_vertex_label": "huawei-routing", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-vlan", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-vlan", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan", "source_vertex_label": "hua<PERSON>-vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan", "source_node_path": "/huawei-vlan:vlan/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "source_node_path": "/member-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-dhcp:dhcp-snooping-vlan::vlan-trusts::vlan-trust", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-dhcp:dhcp-snooping-vlan::vlan-trusts::vlan-trust", "source_node_path": "/huawei-dhcp:dhcp-snooping-vlan/vlan-trusts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::router-ports::router-port", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::router-ports::router-port", "source_node_path": "/huawei-igmp-mld-snooping:igmp-snooping/router-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group", "source_node_path": "/huawei-igmp-mld-snooping:igmp-snooping/interface-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group", "source_node_path": "/groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group::sources::source", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group::sources::source", "source_node_path": "/sources", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::group-ports::group-port", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::group-ports::group-port", "source_node_path": "/huawei-igmp-mld-snooping:igmp-snooping/group-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::group-ports::group-port::ports::port", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::group-ports::group-port", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::group-ports::group-port::ports::port", "source_node_path": "/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-mac:mac-addresss::mac-address", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-mac:mac-addresss::mac-address", "source_node_path": "/huawei-mac:mac-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::instances::instance", "source_vertex_label": "hua<PERSON>-vlan", "dest_vertex_label": "huawei-vlan:vlan::instances::instance", "source_node_path": "/huawei-vlan:vlan/instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::huawei-vlan-pool:vlan-pools::vlan-pool", "source_vertex_label": "hua<PERSON>-vlan", "dest_vertex_label": "huawei-vlan:vlan::huawei-vlan-pool:vlan-pools::vlan-pool", "source_node_path": "/huawei-vlan:vlan/huawei-vlan-pool:vlan-pools", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-mac", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::global-blackhole-macs::global-blackhole-mac", "source_vertex_label": "hua<PERSON>-mac", "dest_vertex_label": "huawei-mac:mac::global-blackhole-macs::global-blackhole-mac", "source_node_path": "/huawei-mac:mac/global-blackhole-macs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "source_vertex_label": "hua<PERSON>-mac", "dest_vertex_label": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "source_node_path": "/huawei-mac:mac/vlan-dynamic-macs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::mac-statistics::mac-statistic", "source_vertex_label": "hua<PERSON>-mac", "dest_vertex_label": "huawei-mac:mac::mac-statistics::mac-statistic", "source_node_path": "/huawei-mac:mac/mac-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-tm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-tm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range", "source_vertex_label": "ds", "dest_vertex_label": "huawei-time-range", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance", "source_vertex_label": "huawei-time-range", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "source_node_path": "/huawei-time-range:time-range/time-range-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "source_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "source_node_path": "/absolute-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "source_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "source_node_path": "/period-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify", "source_vertex_label": "ds", "dest_vertex_label": "huawei-terminal-identify", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::monitoring-scan::vlans::vlan", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::monitoring-scan::vlans::vlan", "source_node_path": "/huawei-terminal-identify:terminal-identify/monitoring-scan/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::periodic-scan::vlans::vlan", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::periodic-scan::vlans::vlan", "source_node_path": "/huawei-terminal-identify:terminal-identify/periodic-scan/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::identified-terminals::identified-terminal", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::identified-terminals::identified-terminal", "source_node_path": "/huawei-terminal-identify:terminal-identify/identified-terminals", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:start-immediate-scan::scopes::scope", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:start-immediate-scan::scopes::scope", "source_node_path": "/huawei-terminal-identify:start-immediate-scan/scopes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system", "source_vertex_label": "ds", "dest_vertex_label": "huawei-system", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::system-info::huawei-system-controller:upstream-info::if-name", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::system-info::huawei-system-controller:upstream-info::if-name", "source_node_path": "/huawei-system:system/system-info/huawei-system-controller:upstream-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::security-risks::security-risk", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::security-risks::security-risk", "source_node_path": "/huawei-system:system/security-risks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::weak-passwords::weak-password", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::weak-passwords::weak-password", "source_node_path": "/huawei-system:system/weak-passwords", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller", "source_vertex_label": "ds", "dest_vertex_label": "huawei-system-controller", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller:system-controller::offline-records::offline-record", "source_vertex_label": "huawei-system-controller", "dest_vertex_label": "huawei-system-controller:system-controller::offline-records::offline-record", "source_node_path": "/huawei-system-controller:system-controller/offline-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "source_vertex_label": "huawei-system-controller", "dest_vertex_label": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "source_node_path": "/huawei-system-controller:system-controller/register-fail-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog", "source_vertex_label": "ds", "dest_vertex_label": "huawei-syslog", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::log-switch-list::log-switch", "source_vertex_label": "huawei-syslog", "dest_vertex_label": "huawei-syslog:syslog::log-switch-list::log-switch", "source_node_path": "/huawei-syslog:syslog/log-switch-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::logfiles::logfile", "source_vertex_label": "huawei-syslog", "dest_vertex_label": "huawei-syslog:syslog::logfiles::logfile", "source_node_path": "/huawei-syslog:syslog/logfiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "source_vertex_label": "huawei-syslog:syslog::logfiles::logfile", "dest_vertex_label": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "source_node_path": "/latest-logs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ssl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::ssl-policys::ssl-policy", "source_vertex_label": "huawei-ssl", "dest_vertex_label": "huawei-ssl:ssl::ssl-policys::ssl-policy", "source_node_path": "/huawei-ssl:ssl/ssl-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sshs", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::users::user", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::users::user", "source_node_path": "/huawei-sshs:sshs/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "source_node_path": "/huawei-sshs:sshs/ipv4-server-sources", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::call-homes::call-home", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::call-homes::call-home", "source_node_path": "/huawei-sshs:sshs/call-homes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "source_vertex_label": "huawei-sshs:sshs::call-homes::call-home", "dest_vertex_label": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "source_node_path": "/end-points", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa", "source_vertex_label": "ds", "dest_vertex_label": "huawei-aaa", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "source_node_path": "/huawei-aaa:aaa/alive-user-qrys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::lam::users::user", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::lam::users::user", "source_node_path": "/huawei-aaa:aaa/lam/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sshc", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc:sshc::transfer-results::transfer-result", "source_vertex_label": "huawei-sshc", "dest_vertex_label": "huawei-sshc:sshc::transfer-results::transfer-result", "source_node_path": "/huawei-sshc:sshc/transfer-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc:sshc::transfer-tasks::transfer-task", "source_vertex_label": "huawei-sshc", "dest_vertex_label": "huawei-sshc:sshc::transfer-tasks::transfer-task", "source_node_path": "/huawei-sshc:sshc/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software", "source_vertex_label": "ds", "dest_vertex_label": "huawei-software", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::versions::version", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::versions::version", "source_node_path": "/huawei-software:software/versions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::startup-packages::startup-package", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::startup-packages::startup-package", "source_node_path": "/huawei-software:software/startup-packages", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::packages::package", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::packages::package", "source_node_path": "/huawei-software:software/packages", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::operation-schedules::operation-schedule", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::operation-schedules::operation-schedule", "source_node_path": "/huawei-software:software/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software-fwd", "source_vertex_label": "ds", "dest_vertex_label": "huawei-software-fwd", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software-fwd:software-fwd::fwd-statistics-infos::fwd-statistics-info", "source_vertex_label": "huawei-software-fwd", "dest_vertex_label": "huawei-software-fwd:software-fwd::fwd-statistics-infos::fwd-statistics-info", "source_node_path": "/huawei-software-fwd:software-fwd/fwd-statistics-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-socket", "source_vertex_label": "ds", "dest_vertex_label": "huawei-socket", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade", "source_vertex_label": "ds", "dest_vertex_label": "huawei-smart-upgrade", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "source_vertex_label": "huawei-smart-upgrade", "dest_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "source_node_path": "/huawei-smart-upgrade:smart-upgrade/smart-upgrade-info/download-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "source_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "dest_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "source_node_path": "/download-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:download-software::type::specify-software::version-lists::version-list", "source_vertex_label": "huawei-smart-upgrade", "dest_vertex_label": "huawei-smart-upgrade:download-software::type::specify-software::version-lists::version-list", "source_node_path": "/huawei-smart-upgrade:download-software/type/specify-software/version-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sec-session-mgmt", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "source_vertex_label": "huawei-sec-session-mgmt", "dest_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "source_node_path": "/huawei-sec-session-mgmt:sec-session-mgmt/protocol-ttls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "source_vertex_label": "huawei-sec-session-mgmt", "dest_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "source_node_path": "/huawei-sec-session-mgmt:sec-session-mgmt/application-ttls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-acl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group", "source_vertex_label": "huawei-acl", "dest_vertex_label": "huawei-acl:acl::groups::group", "source_node_path": "/huawei-acl:acl/groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "source_node_path": "/rule-basics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "source_node_path": "/rule-advances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "source_node_path": "/rule-ethernets", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki", "source_vertex_label": "ds", "dest_vertex_label": "huawei-pki", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::entitys::entity", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::entitys::entity", "source_node_path": "/huawei-pki:pki/entitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::domains::domain", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::domains::domain", "source_node_path": "/huawei-pki:pki/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::certificate-infos::certificate-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info", "source_node_path": "/huawei-pki:pki/certificate-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "source_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info", "dest_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "source_node_path": "/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "source_node_path": "/huawei-pki:pki/preset-certificate-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "source_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "dest_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "source_node_path": "/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::crl-infos::crl-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::crl-infos::crl-info", "source_node_path": "/huawei-pki:pki/crl-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::key-pair-infos::key-pair-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::key-pair-infos::key-pair-info", "source_node_path": "/huawei-pki:pki/key-pair-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "source_node_path": "/huawei-pki:pki/cert-key-pair-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "source_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "dest_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "source_node_path": "/cert-key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-create::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-create::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-create/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-destroy::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-destroy::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-destroy/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-import::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-import::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-import/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-import::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-import::certificates::certificate", "source_node_path": "/huawei-pki:certificate-import/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-delete::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-delete::certificates::certificate", "source_node_path": "/huawei-pki:certificate-delete/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-delete-by-domain::domain-certificates::domain-certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-delete-by-domain::domain-certificates::domain-certificate", "source_node_path": "/huawei-pki:certificate-delete-by-domain/domain-certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-replace::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-replace::certificates::certificate", "source_node_path": "/huawei-pki:certificate-replace/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:csr-generate::csrs::csr", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:csr-generate::csrs::csr", "source_node_path": "/huawei-pki:csr-generate/csrs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:crl-import::crls::crl", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:crl-import::crls::crl", "source_node_path": "/huawei-pki:crl-import/crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:crl-delete::crls::crl", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:crl-delete::crls::crl", "source_node_path": "/huawei-pki:crl-delete/crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:crl-delete-by-domain::crls::crl", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:crl-delete-by-domain::crls::crl", "source_node_path": "/huawei-pki:crl-delete-by-domain/crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-devm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::physical-entitys::physical-entity", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::physical-entitys::physical-entity", "source_node_path": "/huawei-devm:devm/physical-entitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::mpu-boards::mpu-board", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::mpu-boards::mpu-board", "source_node_path": "/huawei-devm:devm/mpu-boards", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::ports::port", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::ports::port", "source_node_path": "/huawei-devm:devm/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "<PERSON><PERSON><PERSON>-driver", "source_vertex_label": "ds", "dest_vertex_label": "<PERSON><PERSON><PERSON>-driver", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::temperature2s::temperature2", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::temperature2s::temperature2", "source_node_path": "/huawei-driver:driver/temperature2s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::device-health-checks::device-health-check", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::device-health-checks::device-health-check", "source_node_path": "/huawei-driver:driver/device-health-checks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::electronic-labels::electronic-label", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::electronic-labels::electronic-label", "source_node_path": "/huawei-driver:driver/electronic-labels", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch", "source_vertex_label": "ds", "dest_vertex_label": "huawei-patch", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::patch-infos::patch-info", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::patch-infos::patch-info", "source_node_path": "/huawei-patch:patch/patch-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "source_vertex_label": "huawei-patch:patch::patch-infos::patch-info", "dest_vertex_label": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "source_node_path": "/operations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "source_node_path": "/huawei-patch:patch/next-startup-patchs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::operation-schedules::operation-schedule", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::operation-schedules::operation-schedule", "source_node_path": "/huawei-patch:patch/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-file-operation", "source_vertex_label": "ds", "dest_vertex_label": "huawei-file-operation", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-file-operation:file-operation::dirs::dir", "source_vertex_label": "huawei-file-operation", "dest_vertex_label": "huawei-file-operation:file-operation::dirs::dir", "source_node_path": "/huawei-file-operation:file-operation/dirs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-openconfig-telemetry-ext", "source_vertex_label": "ds", "dest_vertex_label": "huawei-openconfig-telemetry-ext", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-openconfig-telemetry-ext:resync-subscription::sensor-profile", "source_vertex_label": "huawei-openconfig-telemetry-ext", "dest_vertex_label": "huawei-openconfig-telemetry-ext:resync-subscription::sensor-profile", "source_node_path": "/huawei-openconfig-telemetry-ext:resync-subscription", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ntp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::unicasts::unicast", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::unicasts::unicast", "source_node_path": "/huawei-ntp:ntp/unicasts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::authentications::authentication", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::authentications::authentication", "source_node_path": "/huawei-ntp:ntp/authentications", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::full-sessions::full-session", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::full-sessions::full-session", "source_node_path": "/huawei-ntp:ntp/full-sessions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-netconf-sync", "source_vertex_label": "ds", "dest_vertex_label": "huawei-netconf-sync", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-multicast", "source_vertex_label": "ds", "dest_vertex_label": "huawei-multicast", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management", "source_vertex_label": "ds", "dest_vertex_label": "huawei-module-management", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management:module-management::module-infos::module-info", "source_vertex_label": "huawei-module-management", "dest_vertex_label": "huawei-module-management:module-management::module-infos::module-info", "source_node_path": "/huawei-module-management:module-management/module-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management:module-management::operation-schedules::operation-schedule", "source_vertex_label": "huawei-module-management", "dest_vertex_label": "huawei-module-management:module-management::operation-schedules::operation-schedule", "source_node_path": "/huawei-module-management:module-management/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-masterkey", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-masterkey", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-layer2-loadbalance", "source_vertex_label": "ds", "dest_vertex_label": "huawei-layer2-loadbalance", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http", "source_vertex_label": "ds", "dest_vertex_label": "huawei-http", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http:http::httpc-transfer-results::httpc-transfer-result", "source_vertex_label": "huawei-http", "dest_vertex_label": "huawei-http:http::httpc-transfer-results::httpc-transfer-result", "source_node_path": "/huawei-http:http/httpc-transfer-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http:http::transfer-tasks::transfer-task", "source_vertex_label": "huawei-http", "dest_vertex_label": "huawei-http:http::transfer-tasks::transfer-task", "source_node_path": "/huawei-http:http/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-host-security", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-host-security", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::anti-attacks::anti-attack", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::anti-attacks::anti-attack", "source_node_path": "/huawei-host-security:host-security/anti-attacks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::host-cars::host-protocol-type", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::host-cars::host-protocol-type", "source_node_path": "/huawei-host-security:host-security/host-cars", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::packet-statistics::packet-statistic", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::packet-statistics::packet-statistic", "source_node_path": "/huawei-host-security:host-security/packet-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "source_node_path": "/huawei-host-security:host-security/adjust-car", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::policys::policy", "source_node_path": "/huawei-host-security:host-security/policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::filters::filter", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::filters::filter", "source_node_path": "/filters", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "source_node_path": "/cpcars", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "source_node_path": "/auto-defend", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "source_node_path": "/applied-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fwm-resource", "source_vertex_label": "ds", "dest_vertex_label": "huawei-fwm-resource", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ftpc", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ftpc", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "source_vertex_label": "huawei-ftpc", "dest_vertex_label": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "source_node_path": "/huawei-ftpc:ftpc/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-fm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::alarms::alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::alarms::alarm", "source_node_path": "/huawei-fm:fm/alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::active-alarms::active-alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::active-alarms::active-alarm", "source_node_path": "/huawei-fm:fm/active-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::history-alarms::history-alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::history-alarms::history-alarm", "source_node_path": "/huawei-fm:fm/history-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns", "source_vertex_label": "ds", "dest_vertex_label": "huawei-dns", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::domains::domain", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::domains::domain", "source_node_path": "/huawei-dns:dns/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv4-hosts::ipv4-host", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::ipv4-hosts::ipv4-host", "source_node_path": "/huawei-dns:dns/ipv4-hosts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv4-servers::ipv4-server", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::ipv4-servers::ipv4-server", "source_node_path": "/huawei-dns:dns/ipv4-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::query-host-ips::query-host-ip", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::query-host-ips::query-host-ip", "source_node_path": "/huawei-dns:dns/query-host-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnostic-tools", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "source_vertex_label": "huawei-diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "source_node_path": "/huawei-diagnostic-tools:diagnostic-tools/ipv4/ping-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "source_node_path": "/details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "source_vertex_label": "huawei-diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "source_node_path": "/huawei-diagnostic-tools:diagnostic-tools/ipv4/trace-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "source_node_path": "/details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-iotbus:iotbus::discovery-devices::discovery-device", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-iotbus:iotbus::discovery-devices::discovery-device", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-iotbus:iotbus/discovery-devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses::address", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses::address", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-ip:ipv4/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/mac-learnings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/vlan-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/car-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/acl-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "source_node_path": "/acl-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-nctl:nctl::innertables::innertable", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-nctl:nctl::innertables::innertable", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-nctl:nctl/innertables", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-nfc:nfc::processes::process", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-nfc:nfc::processes::process", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-nfc:nfc/processes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path::path", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path::path", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/subscriptions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "source_node_path": "/sensor-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_node_path": "/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "source_node_path": "/indicators", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::collect-tasks::collect-task", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::collect-tasks::collect-task", "source_node_path": "/collect-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "source_node_path": "/destinations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::collect-tasks::collect-task", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::collect-tasks::collect-task", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/collect-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/clients", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client::subscribe-paths::subscribe-path", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client::subscribe-paths::subscribe-path", "source_node_path": "/subscribe-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::notification-subscriptions::notification-subscription", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::notification-subscriptions::notification-subscription", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/notification-subscriptions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-patch:patch::patch-unit-infos::patch-unit-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-patch:patch::patch-unit-infos::patch-unit-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-patch:patch/patch-unit-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/routing-manage/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/direct-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/static-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/user-network-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-system:sysdiag::service-infos::service-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-system:sysdiag::service-infos::service-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-system:sysdiag/service-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::return-packets::return-packet", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::return-packets::return-packet", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/return-packets", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::monitoring-scan-records::monitoring-scan-record", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::monitoring-scan-records::monitoring-scan-record", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/monitoring-scan-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::statistics::statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::statistics::statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::running-status::modules::module", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::running-status::modules::module", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/running-status/modules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/configuration-statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status::infos::info", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status::infos::info", "source_node_path": "/infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose-omu", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose-omu", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe", "source_vertex_label": "ds", "dest_vertex_label": "huawei-devm-poe", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe:devm-poe::poes::poe", "source_vertex_label": "huawei-devm-poe", "dest_vertex_label": "huawei-devm-poe:devm-poe::poes::poe", "source_node_path": "/huawei-devm-poe:devm-poe/poes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "source_vertex_label": "huawei-devm-poe:devm-poe::poes::poe", "dest_vertex_label": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "source_node_path": "/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cpu-memory", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-cpu-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-memory-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-storage-partition-infos::board-storage-partition-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-storage-partition-infos::board-storage-partition-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-storage-partition-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-top-infos::board-memory-top-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-memory-top-infos::board-memory-top-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-memory-top-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-top-infos::board-cpu-top-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-cpu-top-infos::board-cpu-top-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-cpu-top-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign", "source_vertex_label": "ds", "dest_vertex_label": "huawei-codesign", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign:codesign::software-crls::software-crl", "source_vertex_label": "huawei-codesign", "dest_vertex_label": "huawei-codesign:codesign::software-crls::software-crl", "source_node_path": "/huawei-codesign:codesign/software-crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign:codesign::crl-names::crl-name", "source_vertex_label": "huawei-codesign", "dest_vertex_label": "huawei-codesign:codesign::crl-names::crl-name", "source_node_path": "/huawei-codesign:codesign/crl-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cli", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cli", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cfg", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg:cfg::startup-infos::startup-info", "source_vertex_label": "huawei-cfg", "dest_vertex_label": "huawei-cfg:cfg::startup-infos::startup-info", "source_node_path": "/huawei-cfg:cfg/startup-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg:cfg::cfg-files::cfg-file", "source_vertex_label": "huawei-cfg", "dest_vertex_label": "huawei-cfg:cfg::cfg-files::cfg-file", "source_node_path": "/huawei-cfg:cfg/cfg-files", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-capture", "source_vertex_label": "ds", "dest_vertex_label": "huawei-capture", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-capture:capture-packet::interface-name", "source_vertex_label": "huawei-capture", "dest_vertex_label": "huawei-capture:capture-packet::interface-name", "source_node_path": "/huawei-capture:capture-packet", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-aspf", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-aspf", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aspf:aspf::protocol", "source_vertex_label": "hua<PERSON>-aspf", "dest_vertex_label": "huawei-aspf:aspf::protocol", "source_node_path": "/huawei-aspf:aspf", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}]