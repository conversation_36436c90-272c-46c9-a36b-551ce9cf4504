[{"name": "ds", "source_vertex_label": "yang", "dest_vertex_label": "ds", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library", "source_vertex_label": "ds", "dest_vertex_label": "ietf-yang-library", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::module-set", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::feature", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::feature", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::deviation", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::deviation", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::schema", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema::module-set", "source_vertex_label": "ietf-yang-library:yang-library::schema", "dest_vertex_label": "ietf-yang-library:yang-library::schema::module-set", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::datastore", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::datastore", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "notifications", "source_vertex_label": "ds", "dest_vertex_label": "notifications", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications", "source_vertex_label": "ds", "dest_vertex_label": "nc-notifications", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications:netconf::streams::stream", "source_vertex_label": "nc-notifications", "dest_vertex_label": "nc-notifications:netconf::streams::stream", "source_node_path": "/nc-notifications:netconf/streams", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-validate", "source_vertex_label": "ds", "dest_vertex_label": "yangdb-st-validate", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-validate:c1::l1", "source_vertex_label": "yangdb-st-validate", "dest_vertex_label": "yangdb-st-validate:c1::l1", "source_node_path": "/yangdb-st-validate:c1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-validate:c5::l1", "source_vertex_label": "yangdb-st-validate", "dest_vertex_label": "yangdb-st-validate:c5::l1", "source_node_path": "/yangdb-st-validate:c5", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-validate:c5::l2", "source_vertex_label": "yangdb-st-validate", "dest_vertex_label": "yangdb-st-validate:c5::l2", "source_node_path": "/yangdb-st-validate:c5", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-status", "source_vertex_label": "ds", "dest_vertex_label": "yangdb-st-status", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-status:c1::l1", "source_vertex_label": "yangdb-st-status", "dest_vertex_label": "yangdb-st-status:c1::l1", "source_node_path": "/yangdb-st-status:c1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-status:c2::l2", "source_vertex_label": "yangdb-st-status", "dest_vertex_label": "yangdb-st-status:c2::l2", "source_node_path": "/yangdb-st-status:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config", "source_vertex_label": "ds", "dest_vertex_label": "yangdb-st-config", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c1::l1", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c1::l1", "source_node_path": "/yangdb-st-config:c1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c1::l1::l1_l2", "source_vertex_label": "yangdb-st-config:c1::l1", "dest_vertex_label": "yangdb-st-config:c1::l1::l1_l2", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c1::ll1", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c1::ll1", "source_node_path": "/yangdb-st-config:c1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c1::ordered_list", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c1::ordered_list", "source_node_path": "/yangdb-st-config:c1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c1::c4::l1", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c1::c4::l1", "source_node_path": "/yangdb-st-config:c1/c4", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c1::c4::l1::c4_l2", "source_vertex_label": "yangdb-st-config:c1::c4::l1", "dest_vertex_label": "yangdb-st-config:c1::c4::l1::c4_l2", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::l2", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::l2", "source_node_path": "/yangdb-st-config:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::insert-list", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::insert-list", "source_node_path": "/yangdb-st-config:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::diff-type-key-list", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::diff-type-key-list", "source_node_path": "/yangdb-st-config:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::lengthed-leaf-list", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::lengthed-leaf-list", "source_node_path": "/yangdb-st-config:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::leafl2", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::leafl2", "source_node_path": "/yangdb-st-config:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::uniqued-list", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::uniqued-list", "source_node_path": "/yangdb-st-config:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::maxed-list", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::maxed-list", "source_node_path": "/yangdb-st-config:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::mined-list-cont::mined-list", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::mined-list-cont::mined-list", "source_node_path": "/yangdb-st-config:c2/mined-list-cont", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::maxed-leaflist", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::maxed-leaflist", "source_node_path": "/yangdb-st-config:c2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c2::mined-leaflist-cont::mined-leaflist", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c2::mined-leaflist-cont::mined-leaflist", "source_node_path": "/yangdb-st-config:c2/mined-leaflist-cont", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c3::l3", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c3::l3", "source_node_path": "/yangdb-st-config:c3", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c7::c7_l1", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c7::c7_l1", "source_node_path": "/yangdb-st-config:c7", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c7::c7_l2_with_mand_leaf", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c7::c7_l2_with_mand_leaf", "source_node_path": "/yangdb-st-config:c7", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c8::ch1::ca1::ca1_ll1", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c8::ch1::ca1::ca1_ll1", "source_node_path": "/yangdb-st-config:c8/ch1/ca1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c8::c8_l1", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c8::c8_l1", "source_node_path": "/yangdb-st-config:c8", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c8::l1", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c8::l1", "source_node_path": "/yangdb-st-config:c8", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c8::vv1", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c8::vv1", "source_node_path": "/yangdb-st-config:c8", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c8::ch2::ca1::vv2", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c8::ch2::ca1::vv2", "source_node_path": "/yangdb-st-config:c8/ch2/ca1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-config:c8::ch2::ca2::vv3", "source_vertex_label": "yangdb-st-config", "dest_vertex_label": "yangdb-st-config:c8::ch2::ca2::vv3", "source_node_path": "/yangdb-st-config:c8/ch2/ca2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-benchmark", "source_vertex_label": "ds", "dest_vertex_label": "yangdb-st-benchmark", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-benchmark:security-policy::zones", "source_vertex_label": "yangdb-st-benchmark", "dest_vertex_label": "yangdb-st-benchmark:security-policy::zones", "source_node_path": "/yangdb-st-benchmark:security-policy", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-benchmark:security-policy::rules::rule", "source_vertex_label": "yangdb-st-benchmark", "dest_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule", "source_node_path": "/yangdb-st-benchmark:security-policy/rules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-benchmark:security-policy::rules::rule::source-zone", "source_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule", "dest_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule::source-zone", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-benchmark:security-policy::rules::rule::destination-zone", "source_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule", "dest_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule::destination-zone", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-benchmark:security-policy::rules::rule::source-address::address-item::address-ipv4s::address-ipv4s::address-ipv4", "source_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule", "dest_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule::source-address::address-item::address-ipv4s::address-ipv4s::address-ipv4", "source_node_path": "/source-address/address-item/address-ipv4s/address-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-benchmark:security-policy::rules::rule::destination-address::address-item::address-ipv4s::address-ipv4s::address-ipv4", "source_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule", "dest_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule::destination-address::address-item::address-ipv4s::address-ipv4s::address-ipv4", "source_node_path": "/destination-address/address-item/address-ipv4s/address-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-benchmark:security-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "source_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule", "dest_vertex_label": "yangdb-st-benchmark:security-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "source_node_path": "/service/service-items/protocol-and-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-action", "source_vertex_label": "ds", "dest_vertex_label": "yangdb-st-action", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-action:c1::l1", "source_vertex_label": "yangdb-st-action", "dest_vertex_label": "yangdb-st-action:c1::l1", "source_node_path": "/yangdb-st-action:c1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-action:rpc-duplicate-instance::lst", "source_vertex_label": "yangdb-st-action", "dest_vertex_label": "yangdb-st-action:rpc-duplicate-instance::lst", "source_node_path": "/yangdb-st-action:rpc-duplicate-instance", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "yangdb-st-action:rpc-duplicate-instance::leaf-lst", "source_vertex_label": "yangdb-st-action", "dest_vertex_label": "yangdb-st-action:rpc-duplicate-instance::leaf-lst", "source_node_path": "/yangdb-st-action:rpc-duplicate-instance", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}]