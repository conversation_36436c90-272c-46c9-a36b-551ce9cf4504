/*****************************************************************************
版权所有 (c) 华为技术有限公司 2016-2020
Description  : arp场景化操作
Notes        :
History      :
Author       : xzm
Modification :
Date         : 2022/11/03
*****************************************************************************/
#ifndef GMDBV5_PERF_SCENE_OPER_H
#define GMDBV5_PERF_SCENE_OPER_H

#include "gmc_errno.h"
#include "../../../../src/common/include/utils/db_perf_stat.h"
#include "../testperf/perf_config.h"
#include "../testperf/perf_product.h"
#include "../../../../src/common/include/log/common_log.h"
#include "../testperf/perf_vertex.h"
#include "../../../../src/datamodel/include/dm_data_basic.h"
#define DB_ARP_IP_ADDRESS_LEN 4
#define DB_VPN_ACROSS_IP_ADDRESS_LEN 4
#define DB_ARP_MAC_ADDRESS_LEN 6

struct connObj {
    int stat;
    int id;
    GmcConnT *connS;  // sync connect
    GmcConnT *connA;  // async connect
    GmcStmtT *stmtS[5];
    GmcStmtT *stmtA[5];
    char threadName[16];
    void initObj(int idx)
    {
        id = idx;
        stat = 0;
        connS = NULL;
        connA = NULL;
        for (int i = 0; i < 5; ++i) {
            stmtS[i] = NULL;
            stmtA[i] = NULL;
        }
    }
};

/* All length macro define as following */
#define DB_VXLAN_TUNNEL_ENTRY_SRC_ADDR_LEN 16
#define DB_VXLAN_TUNNEL_ENTRY_DST_ADDR_LEN 16

#pragma pack(1)
typedef struct tagdb_vxlan_tunnel_entry_ip_addr {
    uint32_t vrid;
    int8_t src_addr[DB_VXLAN_TUNNEL_ENTRY_SRC_ADDR_LEN];
    int8_t dst_addr[DB_VXLAN_TUNNEL_ENTRY_DST_ADDR_LEN];
} db_vxlan_tunnel_entry_ip_addr_t;
#pragma pack()

/* All fields struct define as following */
/* Struct definition of vxlan_tunnel_entry */
#pragma pack(1)
typedef struct tagvxlan_tunnel_entry_struct_t {
    uint32_t vrid;
    uint32_t vrf_id;
    uint32_t tunnel_id;
    uint8_t tunnel_type;
    uint8_t tunnel_subtype;
    uint8_t split_group;
    uint8_t rsv1;
    int8_t src_addr[DB_VXLAN_TUNNEL_ENTRY_SRC_ADDR_LEN];
    int8_t dst_addr[DB_VXLAN_TUNNEL_ENTRY_DST_ADDR_LEN];
    uint32_t aib_id;
    uint32_t vp_id;
    uint8_t complete_data;
    uint8_t complete_res;
    uint8_t is_bud;
    uint8_t rsv2;
    uint64_t aib_version;
    uint64_t vp_version;
    uint32_t mc_aib_id;
    uint64_t mc_aib_version;
    uint32_t ver_no;
} vxlan_tunnel_entry_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_if_vlan_k_vlan_id {
    uint16_t vlan_id;
    uint32_t vr_id;
} db_if_vlan_k_vlan_id_t;
#pragma pack()

/* All fields struct define as following */
/* Struct definition of if_vlan */
#pragma pack(1)
typedef struct tagif_vlan_struct_t {
    uint32_t if_index;
    uint16_t vlan_id;
    uint32_t vr_id;
    uint32_t app_source_id;
    uint32_t app_serial_id;
    uint64_t app_obj_id;
} if_vlan_struct_t;
#pragma pack()

/* Key struct definition of l3mc_elb. */
#pragma pack(1)
typedef struct tagdb_l3mc_elb {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t grpAddr;
    uint32_t srcAddr;
    uint32_t outIfIndex;
    uint8_t srcType;
} db_l3mc_elb_key_t;
#pragma pack()

/* Key struct definition of l3mcvlan_elb. */
#pragma pack(1)
typedef struct tagdb_l3mcvlan_elb {
    uint32_t vrIndex;
    uint32_t vrfIndex;
    uint32_t grpAddr;
    uint32_t srcAddr;
    uint32_t ifIndex;
    uint16_t vlanId;
} db_l3mcvlan_elb_key_t;
#pragma pack()

/* Key struct definition of mlag_peer_link. */
#pragma pack(1)
typedef struct tagdb_mlag_peer_link {
    uint32_t vrid;
} db_mlag_peer_link_key_t;
#pragma pack()

/* All length macro define as following */
#define DB_MLAG_PEER_LINK_SRC_IPV6_ADDR_LEN 16
#define DB_MLAG_PEER_LINK_DST_IPV6_ADDR_LEN 16

/* All fields struct define as following */
/* Struct definition of mlag_peer_link */
#pragma pack(1)
typedef struct tagmlag_peer_link_struct_t {
    uint32_t vrid;
    uint32_t if_index;
    uint32_t link_id;
    uint16_t link_type;
    uint16_t tunnel_type;
    uint32_t src_ipv4_addr;
    uint32_t dst_ipv4_addr;
    int8_t src_ipv6_addr[DB_MLAG_PEER_LINK_SRC_IPV6_ADDR_LEN];
    int8_t dst_ipv6_addr[DB_MLAG_PEER_LINK_DST_IPV6_ADDR_LEN];
    uint32_t reserved;
} mlag_peer_link_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_l3mcvlan_elb_mfib_refresh_key {
    uint32_t vrIndex;
    uint32_t vrfIndex;
    uint32_t grpAddr;
    uint32_t srcAddr;
} db_l3mcvlan_elb_mfib_refresh_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_l3mc_elb_sub_MFIB_refresh_key {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t grpAddr;
    uint32_t srcAddr;
    uint32_t outIfIndex;
    uint8_t srcType;
} db_l3mc_elb_sub_MFIB_refresh_key_t;
#pragma pack()

/* Key struct definition of mlag_port_pair. */
#pragma pack(1)
typedef struct tagdb_mlag_port_pair {
    uint32_t if_index;
} db_mlag_port_pair_key_t;
#pragma pack()

/* All fields struct define as following */
/* Struct definition of mlag_port_pair */
#pragma pack(1)
typedef struct tagmlag_port_pair_struct_t {
    uint32_t if_index;
    uint32_t vrid;
    uint32_t mlag_id;
    uint32_t dfs_group_id;
    uint8_t state;
    uint8_t loc_link_state;
    uint8_t peer_link_state;
    uint8_t reserve;
    uint32_t cfg_delete_num;
    uint32_t port_down_num;
    uint32_t reserved;
} mlag_port_pair_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_l3mc_elb_MFIB_refresh_key {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t grpAddr;
    uint32_t srcAddr;
    uint8_t srcType;
} db_l3mc_elb_MFIB_refresh_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_l2mc_elb4_vlanId_key {
    uint32_t vrIndex;
    uint16_t vlanId;
} db_l2mc_elb4_vlanId_key_t;
#pragma pack()

/* All length macro define as following */
#define DB_L2MC_ELB4_RESV_LEN 3
#define DB_L2MC_ELB4_SVCCTXHIGHPRIO_LEN 28
#define DB_L2MC_ELB4_SVCCTXNORMALPRIO_LEN 12
#define DB_L2MC_ELB4_SERVICESTATUS_LEN 2
#define DB_L2MC_ELB4_ERRCODE_LEN 2

/* All fields struct define as following */
/* Struct definition of l2mc_elb4 */
#pragma pack(1)
typedef struct tagl2mc_elb4_struct_t {
    uint32_t vrIndex;
    uint16_t vlanId;
    uint16_t userVlanId;
    uint32_t groupAddr;
    uint32_t sourceAddr;
    uint32_t outIfIndex;
    uint32_t vrfIndex;
    uint16_t fwdIfExtType;
    uint16_t fwdIfType;
    uint8_t isSelfBoard;
    int8_t resv[DB_L2MC_ELB4_RESV_LEN];
    uint32_t pathFlags;
    uint32_t appSrcPid;
    uint64_t mcidVersion;
    uint32_t mfibVersion;
    uint32_t mcId;
    uint32_t smoothVersion;
    int8_t svcCtxHighPrio[DB_L2MC_ELB4_SVCCTXHIGHPRIO_LEN];
    int8_t svcCtxNormalPrio[DB_L2MC_ELB4_SVCCTXNORMALPRIO_LEN];
    int8_t serviceStatus[DB_L2MC_ELB4_SERVICESTATUS_LEN];
    int8_t errCode[DB_L2MC_ELB4_ERRCODE_LEN];
    int64_t timeStampCreate;
    int64_t timeStampSmooth;
} l2mc_elb4_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_l3mcv4_loop_elb_MFIB_refresh_key {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t groupAddr;
    uint32_t sourceAddr;
} db_l3mcv4_loop_elb_MFIB_refresh_key_t;
#pragma pack()

/* All length macro define as following */
#define DB_L3MCV4_LOOP_ELB_SVCCTXHIGHPRIO_LEN 12
#define DB_L3MCV4_LOOP_ELB_SVCCTXNORMALPRIO_LEN 12
#define DB_L3MCV4_LOOP_ELB_SERVICESTATUS_LEN 2
#define DB_L3MCV4_LOOP_ELB_ERRCODE_LEN 2
#define DB_L3MCV4_LOOP_ELB_RESV_LEN 3

/* All fields struct define as following */
/* Struct definition of l3mcv4_loop_elb */
#pragma pack(1)
typedef struct tagl3mcv4_loop_elb_struct_t {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t groupAddr;
    uint32_t sourceAddr;
    uint32_t outIfIndex;
    uint32_t vlanId;
    uint32_t fwdIfType;
    uint32_t ifType;
    uint32_t pathFlags;
    int8_t svcCtxHighPrio[DB_L3MCV4_LOOP_ELB_SVCCTXHIGHPRIO_LEN];
    int8_t svcCtxNormalPrio[DB_L3MCV4_LOOP_ELB_SVCCTXNORMALPRIO_LEN];
    uint64_t mcidVersion;
    uint32_t mfibVersion;
    uint32_t mcId;
    uint32_t smoothVersion;
    int8_t serviceStatus[DB_L3MCV4_LOOP_ELB_SERVICESTATUS_LEN];
    int8_t errCode[DB_L3MCV4_LOOP_ELB_ERRCODE_LEN];
    int64_t timeStampCreate;
    int64_t timeStampSmooth;
    uint32_t flag;
    uint8_t isSelfBoard;
    int8_t resv[DB_L3MCV4_LOOP_ELB_RESV_LEN];
} l3mcv4_loop_elb_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tag_arp_re_struct_t {
    uint32_t ip_address;
    uint32_t vrid;
    uint32_t vrfindex;
    uint32_t ifindex;
    uint32_t user_group_id;
    uint8_t status_high_prio;
    uint8_t status_normal_prio;
    uint8_t errcode_high_prio;
    uint8_t errcode_normal_prio;
    char svc_ctx_high_prio[16];
    char svc_ctx_normal_prio[16];
    uint32_t app_source_id;
    uint32_t app_serial_id;
    uint64_t app_obj_id;
    uint32_t app_version;
    int64_t time_stamp_create;
} arp_re_struct_t;

typedef struct tag_arp_re_key_t {
    uint32_t ip_address;
    uint32_t vrid;
    uint32_t vrfindex;
} arp_re_key_t;

typedef struct tag_arp_fake_struct_t {
    char ip_address[4];
    uint32_t ifindex;
    uint32_t vrid;
    uint32_t vrfindex;
    uint32_t fake_time;
    uint32_t fake_type;
    uint32_t module_bitmap;
    int64_t time_stamp_create;
} arp_fake_struct_t;

typedef struct tag_arp_fake_key_t {
    char ip_address[4];
    uint32_t ifindex;
} arp_fake_key_t;

typedef struct tag_if_fwd_struct_t {
    uint32_t ifindex;
    uint32_t ifkey_type;
    char ifkey[20];
    uint32_t vrid;
    uint32_t resource_vsi;
    char resource[24];
    uint32_t ready_flags;
    uint32_t attr_flags;
    int64_t update_time;
    char down_status[8];
    char error_code[8];
    char svc_ctx[96];
    uint32_t vrf_index;
    uint32_t globallif;
    uint64_t globallif_ver;
} if_fwd_struct_t;

typedef struct tag_if_fwd_key_t {
    uint32_t ifindex;
} if_fwd_key_t;

typedef struct tag_hpp_ifindex_ctrl2fwd_key_t {
    uint32_t ctrlifindex;
} hpp_ifindex_ctrl2fwd_key_t;

typedef struct tag_vpn_across_vpn_across_nhp_index_t {
    char ip_address[4];
    uint32_t dst_fvrf_index;
} vpn_across_vpn_across_nhp_index_t;

typedef struct tag_nhp_std_ip_ifindex_localhash_key_t {
    uint32_t next_hop;
    uint32_t out_if_index;
} nhp_std_ip_ifindex_localhash_key_t;

typedef struct tag_vpn_across_vpn_across_re_index_t {
    uint32_t dst_ip;
    uint32_t mask;
    uint32_t src_fvrf_index;
} vpn_across_vpn_across_re_index_t;
#pragma pack()

/* Key struct definition of l3mc_mfib. */
#pragma pack(1)
typedef struct tagdb_l3mc_mfib {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t grpAddr;
    uint32_t srcAddr;
} db_l3mc_mfib_key_t;
#pragma pack()

/* All the Index ID define as following */
#define DB_L3MCVLAN_ELB_PRIMARY_KEY_ID 0
#define DB_L3MCVLAN_ELB_MFIB_REFRESH_KEY_ID 257
#define DB_L3MCVLAN_ELB_OUTIF_REFRESH_KEY_ID 514
#define DB_L3MCVLAN_ELB_CMD_SCAN_KEY_ID 771
#define DB_L3MCVLAN_ELB_SRCPID_KEY_ID 1028

/* All length macro define as following */
#define DB_L3MCVLAN_ELB_SVCCTXHIGHPRIO_LEN 28
#define DB_L3MCVLAN_ELB_SVCCTXNORMALPRIO_LEN 12
#define DB_L3MCVLAN_ELB_SERVICESTATUS_LEN 2
#define DB_L3MCVLAN_ELB_ERRCODE_LEN 2
#define DB_L3MCVLAN_ELB_RESV_LEN 2

/* All fields struct define as following */
/* Struct definition of l3mcvlan_elb */
#pragma pack(1)
typedef struct tagl3mcvlan_elb_struct_t {
    uint32_t vrIndex;
    uint32_t vrfIndex;
    uint32_t grpAddr;
    uint32_t srcAddr;
    uint32_t ifIndex;
    uint16_t vlanId;
    uint16_t ifType;
    uint32_t mcid;
    uint64_t mcidVersion;
    uint32_t mfibVersion;
    uint16_t mlagRole;
    uint8_t isMlagDual;
    uint8_t isInIfElb;
    uint32_t inIfhasMlag;
    uint32_t appSrcPid;
    uint32_t smoothVersion;
    uint32_t pathFlags;
    int8_t svcCtxHighPrio[DB_L3MCVLAN_ELB_SVCCTXHIGHPRIO_LEN];
    int8_t svcCtxNormalPrio[DB_L3MCVLAN_ELB_SVCCTXNORMALPRIO_LEN];
    int8_t serviceStatus[DB_L3MCVLAN_ELB_SERVICESTATUS_LEN];
    int8_t errCode[DB_L3MCVLAN_ELB_ERRCODE_LEN];
    int64_t timeStampCreate;
    int64_t timeStampSmooth;
    uint8_t isOutVxlan;
    uint8_t isSelfBoard;
    int8_t resv[DB_L3MCVLAN_ELB_RESV_LEN];
} l3mcvlan_elb_struct_t;
#pragma pack()

/* All the Index ID define as following */
#define DB_L3MC_ELB_PRIMARY_KEY_ID 0
#define DB_L3MC_ELB_MFIB_REFRESH_KEY_ID 257
#define DB_L3MC_ELB_L3IF_REFRESH_KEY_ID 514
#define DB_L3MC_ELB_VLAN_MEMBER_CHANGE_KEY_ID 771
#define DB_L3MC_ELB_VLAN_IGMP_REFRESH_KEY_ID 1028
#define DB_L3MC_ELB_SRCPID_KEY_ID 1285
#define DB_L3MC_ELB_CMD_KEY_ID 1542

/* All length macro define as following */
#define DB_L3MC_ELB_RESV1_LEN 3
#define DB_L3MC_ELB_SVCCTXHIGHPRIO_LEN 28
#define DB_L3MC_ELB_SVCCTXNORMALPRIO_LEN 12
#define DB_L3MC_ELB_SERVICESTATUS_LEN 2
#define DB_L3MC_ELB_ERRCODE_LEN 2
#define DB_L3MC_ELB_RESV2_LEN 1

/* All the Index ID define as following */
#define DB_L2MC_VLAN_MFIBV4_PRIMARY_KEY_ID 0
#define DB_L2MC_VLAN_MFIBV4_VLANID_LOCAL_KEY_ID 257
#define DB_L2MC_VLAN_MFIBV4_VLANID_KEY_ID 514
#define DB_L2MC_VLAN_MFIBV4_GROUPADDR_KEY_ID 771
#define DB_L2MC_VLAN_MFIBV4_ERRCODE_KEY_ID 1028
#define DB_L2MC_VLAN_MFIBV4_SRCPID_KEY_ID 1285

/* All length macro define as following */
#define DB_L2MC_VLAN_MFIBV4_SVCCTXHIGHPRIO_LEN 4
#define DB_L2MC_VLAN_MFIBV4_SVCCTXNORMALPRIO_LEN 4
#define DB_L2MC_VLAN_MFIBV4_SERVICESTATUS_LEN 2

/* All fields struct define as following */
/* Struct definition of l2mc_vlan_mfibv4 */
#pragma pack(1)
typedef struct tagl2mc_vlan_mfibv4_struct_t {
    uint32_t vrIndex;
    uint32_t groupAddr;
    uint32_t sourceAddr;
    uint32_t vlanId;
    uint32_t vrfIndex;
    uint8_t groupMaskLen;
    uint8_t sourceMaskLen;
    uint32_t pathFlags;
    uint32_t mcId;
    uint64_t mcidVersion;
    uint32_t mfibVersion;
    int8_t svcCtxHighPrio[DB_L2MC_VLAN_MFIBV4_SVCCTXHIGHPRIO_LEN];
    int8_t svcCtxNormalPrio[DB_L2MC_VLAN_MFIBV4_SVCCTXNORMALPRIO_LEN];
    uint32_t appSrcPid;
    uint32_t smoothVersion;
    uint32_t appVersion;
    int8_t serviceStatus[DB_L2MC_VLAN_MFIBV4_SERVICESTATUS_LEN];
    uint32_t hppErrCode;
    uint32_t enpErrCode;
    int64_t timeStampCreate;
    int64_t timeStampSmooth;
} l2mc_vlan_mfibv4_struct_t;
#pragma pack()

/* All the Index ID define as following */
#define DB_L3MC_ELB_SUB_PRIMARY_KEY_ID 0
#define DB_L3MC_ELB_SUB_MFIB_REFRESH_KEY_ID 257
#define DB_L3MC_ELB_SUB_VLAN_MEMBER_CHANGE_KEY_ID 514
#define DB_L3MC_ELB_SUB_PORTIFINDEX_KEY_ID 771
#define DB_L3MC_ELB_SUB_PV_KEY_ID 1028

/* All length macro define as following */
#define DB_L3MC_ELB_SUB_RESV_LEN 2
#define DB_L3MC_ELB_SUB_SVCCTXHIGHPRIO_LEN 28
#define DB_L3MC_ELB_SUB_SVCCTXNORMALPRIO_LEN 12
#define DB_L3MC_ELB_SUB_SERVICESTATUS_LEN 2
#define DB_L3MC_ELB_SUB_ERRCODE_LEN 2

/* All fields struct define as following */
/* Struct definition of l3mc_elb_sub */
#pragma pack(1)
typedef struct tagl3mc_elb_sub_struct_t {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t grpAddr;
    uint32_t srcAddr;
    uint32_t vlanId;
    uint32_t portIfIndex;
    uint8_t srcType;
    uint8_t isSelfBoard;
    int8_t resv[DB_L3MC_ELB_SUB_RESV_LEN];
    uint32_t outIfIndex;
    uint32_t pathFlags;
    int8_t svcCtxHighPrio[DB_L3MC_ELB_SUB_SVCCTXHIGHPRIO_LEN];
    int8_t svcCtxNormalPrio[DB_L3MC_ELB_SUB_SVCCTXNORMALPRIO_LEN];
    uint64_t mcidVersion;
    uint32_t mfibVersion;
    uint32_t mcId;
    uint32_t ifType;
    uint32_t appVersion;
    uint32_t smoothVersion;
    int8_t serviceStatus[DB_L3MC_ELB_SUB_SERVICESTATUS_LEN];
    int8_t errCode[DB_L3MC_ELB_SUB_ERRCODE_LEN];
    int64_t timeStampCreate;
    int64_t timeStampSmooth;
} l3mc_elb_sub_struct_t;
#pragma pack()

/* Key struct definition of l3mc_elb_sub. */
#pragma pack(1)
typedef struct tagdb_l3mc_elb_sub {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t grpAddr;
    uint32_t srcAddr;
    uint32_t vlanId;
    uint32_t portIfIndex;
    uint8_t srcType;
} db_l3mc_elb_sub_key_t;
#pragma pack()

typedef struct tagspec_execute_user_data_t {
    volatile uint64_t expect_total_record_count;
    volatile uint64_t curr_total_record_count;
    struct timeval start_time;
    struct timeval end_time;
} special_batch_execute_user_data_t;

/* Key struct definition of l2mc_vlan_mfibv4. */
#pragma pack(1)
typedef struct tagdb_l2mc_vlan_mfibv4 {
    uint32_t vrIndex;
    uint32_t groupAddr;
    uint32_t sourceAddr;
    uint32_t vlanId;
} db_l2mc_vlan_mfibv4_key_t;
#pragma pack()

/* Key struct definition of l3mc_register_oif. */
#pragma pack(1)
typedef struct tagdb_l3mc_register_oif {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t grpAddr;
    uint32_t srcAddr;
} db_l3mc_register_oif_key_t;
#pragma pack()

/* All the Index ID define as following */
#define DB_L3MC_REGISTER_OIF_PRIMARY_KEY_ID 0
#define DB_L3MC_REGISTER_OIF_IFINDEX_KEY_ID 257
#define DB_L3MC_REGISTER_OIF_VLANID_KEY_ID 514

/* All length macro define as following */
#define DB_L3MC_REGISTER_OIF_SERVICESTATUS_LEN 2
#define DB_L3MC_REGISTER_OIF_ERRCODE_LEN 2
#define DB_L3MC_REGISTER_OIF_SVCCTXHIGHPRIO_LEN 12
#define DB_L3MC_REGISTER_OIF_SVCCTXNORMALPRIO_LEN 12
#define DB_L3MC_REGISTER_OIF_RESV_LEN 3

/* All fields struct define as following */
/* Struct definition of l3mc_register_oif */
#pragma pack(1)
typedef struct tagl3mc_register_oif_struct_t {
    uint32_t vrId;
    uint32_t vrfId;
    uint32_t grpAddr;
    uint32_t srcAddr;
    uint32_t outIfIndex;
    int8_t serviceStatus[DB_L3MC_REGISTER_OIF_SERVICESTATUS_LEN];
    int8_t errCode[DB_L3MC_REGISTER_OIF_ERRCODE_LEN];
    int64_t timeStampCreate;
    int64_t timeStampSmooth;
    int8_t svcCtxHighPrio[DB_L3MC_REGISTER_OIF_SVCCTXHIGHPRIO_LEN];
    int8_t svcCtxNormalPrio[DB_L3MC_REGISTER_OIF_SVCCTXNORMALPRIO_LEN];
    uint32_t mcId;
    uint32_t mcIdVersion;
    uint32_t vlanId;
    uint8_t isSelfBoard;
    int8_t resv[DB_L3MC_REGISTER_OIF_RESV_LEN];
} l3mc_register_oif_struct_t;
#pragma pack()

#define DB_IP4FORWARD_SVC_CTX_HIGH_PRIO_LEN 16
#define DB_IP4FORWARD_SVC_CTX_NORMAL_PRIO_LEN 16

#pragma pack(1)
typedef struct tagip4forward_struct_t {
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t dest_ip_addr;
    uint8_t mask_len;
    uint8_t nhp_group_flag;
    uint16_t qos_profile_id;
    uint32_t primary_label;
    uint32_t attribute_id;
    uint32_t nhp_group_id;
    uint32_t path_flags;
    uint32_t flags;
    uint8_t status_high_prio;
    uint8_t status_normal_prio;
    uint8_t errcode_high_prio;
    uint8_t errcode_normal_prio;
    int8_t svc_ctx_high_prio[DB_IP4FORWARD_SVC_CTX_HIGH_PRIO_LEN];
    int8_t svc_ctx_normal_prio[DB_IP4FORWARD_SVC_CTX_NORMAL_PRIO_LEN];
    uint32_t app_source_id;
    uint32_t table_smooth_id;
    uint64_t app_obj_id;
    uint32_t app_version;
    uint64_t trace;
    uint16_t route_flags;
    uint8_t next_type;
    uint8_t reserved;
    int64_t time_stamp_create;
    int64_t time_stamp_smooth;
} ip4forward_struct_t;
#pragma pack()

void set_batch_write_stop_flag(bool value);

GmcStmtT *objDel_arp_fake = NULL;
GmcStmtT *objW_arp_fake = NULL;
GmcStmtT *objScan_arp_fake = NULL;
GmcStmtT *objR_l3mc_mfib = NULL;
GmcStmtT *objBw_l3mc_mfib = NULL;
GmcStmtT *objR_l2mc_vlan_mfibv4 = NULL;
GmcStmtT *objR_C_l2mc_vlan_mfibv4 = NULL;
GmcStmtT *objW1_l2mc_vlan_mfibv4 = NULL;
GmcStmtT *objW2_l2mc_vlan_mfibv4 = NULL;
GmcStmtT *l3mc_mfib_stat_obj = NULL;
GmcStmtT *objR_l3mc_elb = NULL;
GmcStmtT *objR_l3mcvlan_elb = NULL;
GmcStmtT *objBw_l3mcvlan_elb = NULL;
GmcStmtT *objBw_l3mc_elb_sub = NULL;
GmcStmtT *objBw_l3mc_elb = NULL;
GmcStmtT *objBdel_l3mc_mfib = NULL;
GmcStmtT *objR_l3mc_elb_sub = NULL;
GmcStmtT *objQry_l3mcvlan_elb = NULL;
GmcStmtT *objQry_l3mc_elb = NULL;
GmcStmtT *objQry_C_l2mc_elb4 = NULL;
GmcStmtT *objCnt_l3mc_mfib = NULL;
GmcStmtT *objCnt_l3mc_bd_untag_elb = NULL;
GmcStmtT *objCnt_l3mc_bd_tag_elb = NULL;
GmcStmtT *objCnt_l3mc_bd_tnl_elb = NULL;
GmcStmtT *objCnt_l3mc_mvpn_tnl_elb = NULL;
GmcStmtT *l3mc_bd_untag_elb_stat_obj = NULL;

void mfib_release_obj()
{
    if (objQry_C_l2mc_elb4) {
        GmcFreeStmt(objQry_C_l2mc_elb4);
        objQry_C_l2mc_elb4 = NULL;
    }
    if (objQry_l3mc_elb) {
        GmcFreeStmt(objQry_l3mc_elb);
        objQry_l3mc_elb = NULL;
    }
    if (objDel_arp_fake) {
        GmcFreeStmt(objDel_arp_fake);
        objDel_arp_fake = NULL;
    }
    if (objW_arp_fake) {
        GmcFreeStmt(objW_arp_fake);
        objW_arp_fake = NULL;
    }
    if (objScan_arp_fake) {
        GmcFreeStmt(objScan_arp_fake);
        objScan_arp_fake = NULL;
    }
    if (objR_l3mc_mfib) {
        GmcFreeStmt(objR_l3mc_mfib);
        objR_l3mc_mfib = NULL;
    }
    if (objBw_l3mc_mfib) {
        GmcFreeStmt(objBw_l3mc_mfib);
        objBw_l3mc_mfib = NULL;
    }
    if (objR_l2mc_vlan_mfibv4) {
        GmcFreeStmt(objR_l2mc_vlan_mfibv4);
        objR_l2mc_vlan_mfibv4 = NULL;
    }
    if (objR_C_l2mc_vlan_mfibv4) {
        GmcFreeStmt(objR_C_l2mc_vlan_mfibv4);
        objR_C_l2mc_vlan_mfibv4 = NULL;
    }
    if (objW1_l2mc_vlan_mfibv4) {
        GmcFreeStmt(objW1_l2mc_vlan_mfibv4);
        objW1_l2mc_vlan_mfibv4 = NULL;
    }
    if (objW2_l2mc_vlan_mfibv4) {
        GmcFreeStmt(objW2_l2mc_vlan_mfibv4);
        objW2_l2mc_vlan_mfibv4 = NULL;
    }
    if (l3mc_mfib_stat_obj) {
        GmcFreeStmt(l3mc_mfib_stat_obj);
        l3mc_mfib_stat_obj = NULL;
    }
    if (objR_l3mc_elb) {
        GmcFreeStmt(objR_l3mc_elb);
        objR_l3mc_elb = NULL;
    }
    if (objR_l3mcvlan_elb) {
        GmcFreeStmt(objR_l3mcvlan_elb);
        objR_l3mcvlan_elb = NULL;
    }
    if (objBw_l3mcvlan_elb) {
        GmcFreeStmt(objBw_l3mcvlan_elb);
        objBw_l3mcvlan_elb = NULL;
    }
    if (objBw_l3mc_elb_sub) {
        GmcFreeStmt(objBw_l3mc_elb_sub);
        objBw_l3mc_elb_sub = NULL;
    }
    if (objBw_l3mc_elb) {
        GmcFreeStmt(objBw_l3mc_elb);
        objBw_l3mc_elb = NULL;
    }
    if (objBdel_l3mc_mfib) {
        GmcFreeStmt(objBdel_l3mc_mfib);
        objBdel_l3mc_mfib = NULL;
    }
    if (objR_l3mc_elb_sub) {
        GmcFreeStmt(objR_l3mc_elb_sub);
        objR_l3mc_elb_sub = NULL;
    }
    if (objQry_l3mcvlan_elb) {
        GmcFreeStmt(objQry_l3mcvlan_elb);
        objQry_l3mcvlan_elb = NULL;
    }
    if (objCnt_l3mc_mfib) {
        GmcFreeStmt(objCnt_l3mc_mfib);
        objCnt_l3mc_mfib = NULL;
    }
    if (objCnt_l3mc_bd_untag_elb) {
        GmcFreeStmt(objCnt_l3mc_bd_untag_elb);
        objCnt_l3mc_bd_untag_elb = NULL;
    }
    if (objCnt_l3mc_bd_tag_elb) {
        GmcFreeStmt(objCnt_l3mc_bd_tag_elb);
        objCnt_l3mc_bd_tag_elb = NULL;
    }
    if (objCnt_l3mc_bd_tnl_elb) {
        GmcFreeStmt(objCnt_l3mc_bd_tnl_elb);
        objCnt_l3mc_bd_tnl_elb = NULL;
    }
    if (objCnt_l3mc_mvpn_tnl_elb) {
        GmcFreeStmt(objCnt_l3mc_mvpn_tnl_elb);
        objCnt_l3mc_mvpn_tnl_elb = NULL;
    }
    if (l3mc_bd_untag_elb_stat_obj) {
        GmcFreeStmt(l3mc_bd_untag_elb_stat_obj);
        l3mc_bd_untag_elb_stat_obj = NULL;
    }
}

int32_t BenchMarkAllocStmt(GmcConnT **t_conn, GmcStmtT **t_stmt)
{
    int32_t ret = GmcAllocStmt(*t_conn, t_stmt);
    if (ret != GMERR_OK) {
        mfib_release_obj();
        ret = GmcDisconnect(*t_conn);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = perfConnect(0, t_conn, t_stmt);
    }
    return ret;
}

int32_t benchMark_arp_read_by_key(struct connObj *t_conn, char *pIp_address, uint32_t nIp_addressLen, uint32_t if_index)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(arp_read_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(arp_read_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_arp_obj");

    arp_product_key_t key_data = {0};
    memcpy_s(key_data.ip_address, DB_ARP_IP_ADDRESS_LEN, pIp_address, nIp_addressLen);
    key_data.if_index = if_index;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<arp_product_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    arp_product_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(arp_product_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    DB_START_TEST_CPU_CYCLES(arp_read_by_key_db_read_obj_with_buf);
    ret = GmcGetVertexBuf(t_stmt, 0, &s, &inputBufInfo);
    DB_STOP_TEST_CPU_CYCLES(arp_read_by_key_db_read_obj_with_buf);
    if (ret != GMERR_OK && ret != GMERR_UNDEFINED_OBJECT) {
        DB_LOG_ERROR(ret, "db_read_obj_with_buf arp_struct_t failed");
    }

    DB_START_TEST_CPU_CYCLES(arp_read_by_key_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_read_by_key_release_obj);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t if_read_root_dev_l2_fields_by_key(struct connObj *t_conn, uint32_t uInputIndex)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(if_read_root_dev_l2_by_key_db_create_if_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "if", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(if_read_root_dev_l2_by_key_db_create_if_obj);
    CHECK_OK_RET(ret, "db_create_if_obj");

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &uInputIndex, sizeof(uInputIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue ifindex.");

    ret = GmcSetIndexKeyName(t_stmt, "if_pk");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    DB_START_TEST_CPU_CYCLES(if_read_root_dev_l2_by_key_db_read_obj);
    ret = GmcExecute(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(if_read_root_dev_l2_by_key_db_read_obj);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(t_stmt, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch failed");
            return ret;
        }
        if (isFinish == true) {
            break;
        }
        GmcNodeT *root;
        ret = GmcGetRootNode(t_stmt, &root);
        CHECK_OK_RET(ret, "GmcGetRootNode.");

        bool isNull;
        char str_pro[64] = {0};
        unsigned int proSize = sizeof(str_pro);

        uint16_t value_u16;
        uint32_t value_u32;
        uint64_t value_u64;
        ret = GmcNodeGetPropertyByName(root, (char *)"ifindex", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ifindex.");

        ret = GmcNodeGetPropertyByName(root, (char *)"name", str_pro, proSize, &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName name.");

        ret = GmcNodeGetPropertyByName(root, (char *)"vrid", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrid.");

        ret = GmcNodeGetPropertyByName(root, (char *)"if_type", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_type.");

        ret = GmcNodeGetPropertyByName(root, (char *)"shutdown", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName shutdown.");

        ret = GmcNodeGetPropertyByName(root, (char *)"linkup", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName linkup.");

        ret = GmcNodeGetPropertyByName(root, (char *)"tbtp", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tbtp.");

        ret = GmcNodeGetPropertyByName(root, (char *)"tb", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tb.");

        ret = GmcNodeGetPropertyByName(root, (char *)"tp", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tp.");

        ret = GmcNodeGetPropertyByName(root, (char *)"port_switch", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_switch.");

        ret = GmcNodeGetPropertyByName(root, (char *)"forwardType", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName forwardType.");

        ret = GmcNodeGetPropertyByName(root, (char *)"macAddress", str_pro, proSize, &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName macAddress.");

        ret = GmcNodeGetPropertyByName(root, (char *)"ipv4_mtu", &value_u16, sizeof(value_u16), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4_mtu.");

        ret = GmcNodeGetPropertyByName(root, (char *)"ipv4_enable", &value_u16, sizeof(value_u16), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4_enable.");

        ret = GmcNodeGetPropertyByName(root, (char *)"ipv6_mtu", &value_u16, sizeof(value_u16), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6_mtu.");

        ret = GmcNodeGetPropertyByName(root, (char *)"ipv6_enable", &value_u16, sizeof(value_u16), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6_enable.");

        ret = GmcNodeGetPropertyByName(root, (char *)"on_board", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName on_board.");

        ret = GmcNodeGetPropertyByName(root, (char *)"lagid", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName lagid.");

        ret = GmcNodeGetPropertyByName(root, (char *)"hppsvcflg", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName hppsvcflg.");

        ret = GmcNodeGetPropertyByName(root, (char *)"error_down", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName error_down.");

        ret = GmcNodeGetPropertyByName(root, (char *)"speed", &value_u64, sizeof(value_u64), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName speed.");

        ret = GmcNodeGetPropertyByName(root, (char *)"link_protocol", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName link_protocol.");

        ret = GmcNodeGetPropertyByName(root, (char *)"vrf_index", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrf_index.");

        ret = GmcNodeGetPropertyByName(root, (char *)"port_group_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_group_id.");

        ret = GmcNodeGetPropertyByName(root, (char *)"if_group_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_group_id.");

        ret = GmcNodeGetPropertyByName(root, (char *)"if_df", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_df.");

        ret = GmcNodeGetPropertyByName(root, (char *)"encap_type", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName encap_type.");

        ret = GmcNodeGetPropertyByName(root, (char *)"is_subif", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName is_subif.");

        ret = GmcNodeGetPropertyByName(root, (char *)"mainifindex", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName mainifindex.");

        ret = GmcNodeGetPropertyByName(root, (char *)"logicTB", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName logicTB.");

        ret = GmcNodeGetPropertyByName(root, (char *)"logicTP", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName logicTP.");

        ret = GmcNodeGetPropertyByName(root, (char *)"vlandomain", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vlandomain.");

        ret = GmcNodeGetPropertyByName(root, (char *)"coreId", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName coreId.");
        if (ret != GMERR_OK && ret != GMERR_DATA_EXCEPTION) {
            CHECK_OK_RET(ret, "db_get_failed");
        }

        ret = GmcNodeGetPropertyByName(root, (char *)"ipv4mss", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4mss.");

        ret = GmcNodeGetPropertyByName(root, (char *)"ipv6mss", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6mss.");

        // 插入record节点 dev
        GmcNodeT *node_dev;
        ret = GmcNodeGetChild(root, "dev", &node_dev);
        CHECK_OK_RET(ret, "GmcNodeGetChild dev.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"dev_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName dev_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"chassis_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName chassis_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"slot_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName slot_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"card_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName card_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"unit_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName unit_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"port_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"port_num", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_num.");

        // 插入record节点 l2
        GmcNodeT *node_l2;
        DB_START_TEST_CPU_CYCLES(if_read_root_dev_l2_by_key_db_create_child_node);
        ret = GmcNodeGetChild(root, "l2", &node_l2);
        DB_STOP_TEST_CPU_CYCLES(if_read_root_dev_l2_by_key_db_create_child_node);
        CHECK_OK_RET(ret, "GmcNodeGetChild l2.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"trunk_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName trunk_id.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"vlan_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vlan_id.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"l2_portindex", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName l2_portindex.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"vsi", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vsi.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"tunnel_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tunnel_id.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"bd_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName bd_id.");
    }

    DB_START_TEST_CPU_CYCLES(if_read_root_dev_l2_by_key_db_release_if_object);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(if_read_root_dev_l2_by_key_db_release_if_object);
    return GMERR_OK;
}

int32_t if_fwd_read_by_key(struct connObj *t_conn, uint32_t uInputIndex)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(if_fwd_read_by_key_db_create_if_fwd_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "if_fwd", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if_fwd");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(if_fwd_read_by_key_db_create_if_fwd_obj);

    if_fwd_key_t key_data = {0};
    key_data.ifindex = uInputIndex;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<if_fwd_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    if_fwd_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(if_fwd_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    DB_START_TEST_CPU_CYCLES(if_fwd_read_by_key_if_fwd_db_read_obj_with_buf);
    ret = GmcGetVertexBuf(t_stmt, 0, &s, &inputBufInfo);
    DB_STOP_TEST_CPU_CYCLES(if_fwd_read_by_key_if_fwd_db_read_obj_with_buf);
    CHECK_OK_RET(ret, "db_read_obj_with_buf of if_fwd_struct_t failed.");

    DB_START_TEST_CPU_CYCLES(if_fwd_read_by_key_db_release_if_fwd_object);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(if_fwd_read_by_key_db_release_if_fwd_object);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t hpp_ifindex_ctrl2fwd_read_by_key(struct connObj *t_conn, uint32_t ctrlifindex)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(hpp_ifindex_ctrl2fwd_read_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "hpp_ifindex_ctrl2fwd", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "hpp_ifindex_ctrl2fwd");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(hpp_ifindex_ctrl2fwd_read_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_hpp_ifindex_ctrl2fwd_obj");

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &ctrlifindex, sizeof(ctrlifindex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue ctrlifindex.");

    ret = GmcSetIndexKeyName(t_stmt, "primerykey");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    DB_START_TEST_CPU_CYCLES(hpp_ifindex_ctrl2fwd_read_by_key_db_read_obj);
    ret = GmcExecute(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(hpp_ifindex_ctrl2fwd_read_by_key_db_read_obj);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "db_read_obj objR_hpp_ifindex_ctrl2fwd failed");
        GmcFreeStmt(t_stmt);
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(t_stmt, &isFinish);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcFetch failed");
            return ret;
        }
        if (isFinish == true) {
            break;
        }
        GmcNodeT *root;
        ret = GmcGetRootNode(t_stmt, &root);
        CHECK_OK_RET(ret, "GmcGetRootNode.");

        bool isNull;
        uint32_t value_u32;
        uint64_t value_u64;
        ret = GmcNodeGetPropertyByName(root, (char *)"ctrlifindex", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ctrlifindex.");

        ret = GmcNodeGetPropertyByName(root, (char *)"fwdifindex", &value_u64, sizeof(uint64_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName fwdifindex.");
    }

    DB_START_TEST_CPU_CYCLES(hpp_ifindex_ctrl2fwd_read_by_key_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(hpp_ifindex_ctrl2fwd_read_by_key_release_obj);
    return ret;
}

int32_t arp_batch_insert_sync(struct connObj *t_conn, arp_product_struct_t *pData, uint32_t uDataCnt)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }

    uint32_t j = 0;
    uint32_t total_num, success_num;
    DB_START_TEST_CPU_CYCLES(arp_batch_insert_sync_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp", GMC_OPERATION_REPLACE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(arp_batch_insert_sync_create_obj);
    CHECK_OK_RET(ret, "db_create_arp_obj.");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    arp_product_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connS, &batchOption, &batch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcBatchPrepare:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    ret = GmcBatchBindStmt(batch, t_stmt);
    if (ret != 0) {
        DB_LOG_ERROR(ret, "GmcBatchBindStmt:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        DB_START_TEST_CPU_CYCLES(arp_batch_insert_sync_reset_batch_object);
        GmcResetStmt(t_stmt);
        DB_STOP_TEST_CPU_CYCLES(arp_batch_insert_sync_reset_batch_object);

        memcpy_s(obj_struct.ip_address, DB_ARP_IP_ADDRESS_LEN, pData[i].ip_address, DB_ARP_IP_ADDRESS_LEN);
        obj_struct.if_index = pData[i].if_index;
        obj_struct.vr_id = pData[i].vr_id;
        obj_struct.vrf_index = pData[i].vrf_index;
        memcpy_s(obj_struct.mac_address, DB_ARP_MAC_ADDRESS_LEN, pData[i].mac_address, DB_ARP_MAC_ADDRESS_LEN);
        obj_struct.link_type = pData[i].link_type;
        obj_struct.work_if_index = pData[i].work_if_index;
        obj_struct.atm_if_index = pData[i].atm_if_index;
        obj_struct.target_blade = pData[i].target_blade;
        obj_struct.target_port = pData[i].target_port;
        obj_struct.pe_vid = pData[i].pe_vid;
        obj_struct.ce_vid = pData[i].ce_vid;
        obj_struct.vcd = pData[i].vcd;
        obj_struct.fwd_if_type = pData[i].fwd_if_type;
        obj_struct.tunnel_type = pData[i].tunnel_type;
        obj_struct.path_flag = pData[i].path_flag;
        obj_struct.if_phy_type = pData[i].if_phy_type;
        obj_struct.main_if_phy_type = pData[i].main_if_phy_type;
        obj_struct.if_link_type = pData[i].if_link_type;
        obj_struct.if_encap_type = pData[i].if_encap_type;
        obj_struct.flow_id = pData[i].flow_id;
        obj_struct.tunnel_vrf_id = pData[i].tunnel_vrf_id;
        obj_struct.tunnel_id = pData[i].tunnel_id;
        obj_struct.tunnel_encap_id = pData[i].tunnel_encap_id;
        obj_struct.tunnel_sip = pData[i].tunnel_sip;
        obj_struct.tunnel_dip = pData[i].tunnel_dip;
        obj_struct.vsi_index = pData[i].vsi_index;
        obj_struct.smooth_id = pData[i].smooth_id;
        obj_struct.app_source_id = pData[i].app_source_id;
        obj_struct.app_obj_id = pData[i].app_obj_id;
        obj_struct.app_version = pData[i].app_version;
        memcpy_s(obj_struct.adj_service_status, 2, pData[i].adj_service_status, 2);
        memcpy_s(obj_struct.adj_service_errcode, 2, pData[i].adj_service_errcode, 2);
        memcpy_s(obj_struct.adj_svc_context_high_prio, 32, pData[i].adj_svc_context_high_prio, 32);
        memcpy_s(obj_struct.adj_svc_context_normal_prio, 32, pData[i].adj_svc_context_normal_prio, 32);
        memcpy_s(obj_struct.nhp_service_status, 2, pData[i].nhp_service_status, 2);
        memcpy_s(obj_struct.nhp_service_errcode, 2, pData[i].nhp_service_errcode, 2);
        memcpy_s(obj_struct.nhp_svc_context_high_prio, 16, pData[i].nhp_svc_context_high_prio, 16);
        memcpy_s(obj_struct.nhp_svc_context_normal_prio, 16, pData[i].nhp_svc_context_normal_prio, 16);
        obj_struct.vlanid = pData[i].vlanid;
        obj_struct.bridge_vlanid = pData[i].bridge_vlanid;
        obj_struct.is_trunk = pData[i].is_trunk;
        obj_struct.mlag_flag = pData[i].mlag_flag;
        obj_struct.state = pData[i].state;
        obj_struct.sub_if_index = pData[i].sub_if_index;
        obj_struct.time_stamp_create = pData[i].is_trunk;
        obj_struct.time_stamp_smooth = pData[i].time_stamp_smooth;
        DB_START_TEST_CPU_CYCLES(arp_batch_insert_sync_set_all_fields);
        ret = GmcSetVertexWithBuf(t_stmt, &s);
        DB_STOP_TEST_CPU_CYCLES(arp_batch_insert_sync_set_all_fields);
        CHECK_OK_RET(ret, "GmcSetVertexWithBuf.");

        DB_START_TEST_CPU_CYCLES(arp_batch_insert_sync_db_batch_add_oper);
        ret = GmcBatchAddDML(batch, t_stmt);
        DB_STOP_TEST_CPU_CYCLES(arp_batch_insert_sync_db_batch_add_oper);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_batch_add_oper");
            j = 0;
            break;
        }
        if (j >= wr_loop || GMERR_DATA_EXCEPTION == ret || i >= end) {
            unsigned int totalNum = 0;
            unsigned int successNum = 0;
            GmcBatchRetT batchRet;
            DB_START_TEST_CPU_CYCLES(arp_batch_insert_sync_db_batch_exec);
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "GmcBatchExecute index:%u .", i);
                break;
            }
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "GmcBatchDeparseRet index:%u .", i);
                break;
            }
            DB_STOP_TEST_CPU_CYCLES(arp_batch_insert_sync_db_batch_exec);
            j = 0;
            total_num += totalNum;
            success_num += successNum;
        }
        if (ret != GMERR_OK || i >= end) {
            break;
        }
    }

    DB_START_TEST_CPU_CYCLES(arp_batch_insert_sync_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_batch_insert_sync_release_obj);
    return ret;
}

int32_t vpn_across_get_count_by_nhp_index(
    struct connObj *t_conn, char *pIp_address, uint32_t nIp_addressLen, uint32_t dst_fvrf_index)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(vpn_across_get_count_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "vpn_across", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "vpn_across");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(vpn_across_get_count_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_vpn_across_obj.");

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_FIXED, &pIp_address, nIp_addressLen);
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue Ip_address.");

    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &dst_fvrf_index, sizeof(dst_fvrf_index));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue dst_fvrf_index.");

    ret = GmcSetIndexKeyName(t_stmt, "vpn_across_nhp_index");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    ret = GmcExecute(t_stmt);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    uint64_t count;
    DB_START_TEST_CPU_CYCLES(vpn_across_get_count_by_key_get_table_count_by_key);
    ret = GmcGetVertexCount(t_stmt, "vpn_across", "vpn_across_nhp_index", &count);
    DB_STOP_TEST_CPU_CYCLES(vpn_across_get_count_by_key_get_table_count_by_key);

    DB_START_TEST_CPU_CYCLES(vpn_across_get_count_by_key_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(vpn_across_get_count_by_key_release_obj);

    return ret;
}

int32_t arp_re_read_by_key(struct connObj *t_conn, uint32_t ip_address, uint32_t vrid, uint32_t vrfindex)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(arp_re_read_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp_re", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp_re");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(arp_re_read_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_arp_re_obj");

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &ip_address, sizeof(ip_address));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue ip_address.");

    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue vrid.");

    ret = GmcSetIndexKeyValue(t_stmt, 2, GMC_DATATYPE_UINT32, &vrfindex, sizeof(vrfindex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue vrfindex.");

    ret = GmcSetIndexKeyName(t_stmt, "arp_re_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    DB_START_TEST_CPU_CYCLES(arp_re_read_by_key_db_read_obj);
    ret = GmcExecute(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_re_read_by_key_db_read_obj);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        GmcFreeStmt(t_stmt);
        return ret;
    }

    bool isFinish = false;
    bool isFirst = true;
    while (!isFinish) {
        ret = GmcFetch(t_stmt, &isFinish);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcFetch failed");
            return ret;
        }
        if (isFinish && !isFirst) {
            break;
        }
        GmcNodeT *root;
        ret = GmcGetRootNode(t_stmt, &root);
        if (ret == GMERR_NO_DATA) {
            return ret;
        }
        CHECK_OK_RET(ret, "GmcGetRootNode.");

        bool isNull;
        uint8_t value_u8;
        uint32_t value_u32;
        uint64_t value_u64;
        int64_t time;
        char str_pro[64] = {0};
        unsigned int proSize = sizeof(str_pro);
        ret = GmcNodeGetPropertyByName(root, (char *)"ip_address", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ip_address.");

        ret = GmcNodeGetPropertyByName(root, (char *)"vrid", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrid.");

        ret = GmcNodeGetPropertyByName(root, (char *)"vrfindex", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrfindex.");

        ret = GmcNodeGetPropertyByName(root, (char *)"ifindex", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ifindex.");

        ret = GmcNodeGetPropertyByName(root, (char *)"user_group_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName user_group_id.");

        ret = GmcNodeGetPropertyByName(root, (char *)"status_high_prio", &value_u8, sizeof(uint8_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName status_high_prio.");

        ret = GmcNodeGetPropertyByName(root, (char *)"status_normal_prio", &value_u8, sizeof(uint8_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName status_normal_prio.");

        ret = GmcNodeGetPropertyByName(root, (char *)"errcode_high_prio", &value_u8, sizeof(uint8_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName errcode_high_prio.");

        ret = GmcNodeGetPropertyByName(root, (char *)"errcode_normal_prio", &value_u8, sizeof(uint8_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName errcode_normal_prio.");

        ret = GmcNodeGetPropertyByName(root, (char *)"svc_ctx_high_prio", str_pro, proSize, &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName svc_ctx_high_prio.");

        ret = GmcNodeGetPropertyByName(root, (char *)"svc_ctx_normal_prio", str_pro, proSize, &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName svc_ctx_normal_prio.");

        ret = GmcNodeGetPropertyByName(root, (char *)"app_source_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName app_source_id.");

        ret = GmcNodeGetPropertyByName(root, (char *)"app_serial_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName app_serial_id.");

        ret = GmcNodeGetPropertyByName(root, (char *)"app_obj_id", &value_u64, sizeof(uint64_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName app_obj_id.");

        ret = GmcNodeGetPropertyByName(root, (char *)"app_version", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName app_version.");

        ret = GmcNodeGetPropertyByName(root, (char *)"time_stamp_create", &time, sizeof(int64_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName time_stamp_create.");

        isFirst = false;
    }

    DB_START_TEST_CPU_CYCLES(arp_re_read_by_key_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_re_read_by_key_release_obj);
    return ret;
}

int32_t ip4forward_read_by_key(
    struct connObj *t_conn, uint32_t vr_id, uint32_t vrf_index, uint32_t dest_ip_addr, uint8_t mask_len)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(ip4forward_read_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "ip4forward", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "ip4forward");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(ip4forward_read_by_key_create_obj);

    ip4forward_product_key_t key_data = {0};
    key_data.vr_id = vr_id;
    key_data.vrf_index = vrf_index;
    key_data.dest_ip_addr = dest_ip_addr;
    key_data.mask_len = mask_len;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<ip4forward_product_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    ip4forward_product_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(ip4forward_product_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    DB_START_TEST_CPU_CYCLES(ip4forward_read_by_key_db_read_obj_with_buf);
    ret = GmcGetVertexBuf(t_stmt, 0, &s, &inputBufInfo);
    DB_STOP_TEST_CPU_CYCLES(ip4forward_read_by_key_db_read_obj_with_buf);
    if (ret != GMERR_OK && ret != GMERR_UNDEFINED_OBJECT) {
        DB_LOG_ERROR(ret, "db_read_obj_with_buf ip4forward_struct_t failed");
    }

    DB_START_TEST_CPU_CYCLES(ip4forward_read_by_key_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(ip4forward_read_by_key_release_obj);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t arp_re_batch_insert_sync(struct connObj *t_conn, arp_re_struct_t *pData, uint32_t uDataCnt)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }

    uint32_t j = 0;
    uint32_t total_num, success_num;
    DB_START_TEST_CPU_CYCLES(arp_re_batch_insert_sync_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp_re", GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp_re");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(arp_re_batch_insert_sync_create_obj);
    CHECK_OK_RET(ret, "db_create_arp_re_obj.");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    arp_re_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connS, &batchOption, &batch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcBatchPrepare:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    ret = GmcBatchBindStmt(batch, t_stmt);
    if (ret != 0) {
        DB_LOG_ERROR(ret, "GmcBatchBindStmt:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        DB_START_TEST_CPU_CYCLES(arp_re_batch_insert_sync_reset_batch_object);
        GmcResetStmt(t_stmt);
        DB_STOP_TEST_CPU_CYCLES(arp_re_batch_insert_sync_reset_batch_object);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_reset_arp_re_batch_object");
            break;
        }
        obj_struct.ip_address = pData[i].ip_address;
        obj_struct.vrid = pData[i].vrid;
        obj_struct.vrfindex = pData[i].vrfindex;
        obj_struct.ifindex = pData[i].ifindex;
        obj_struct.user_group_id = pData[i].user_group_id;
        obj_struct.status_high_prio = pData[i].status_high_prio;
        obj_struct.status_normal_prio = pData[i].status_normal_prio;
        obj_struct.errcode_high_prio = pData[i].errcode_high_prio;
        memcpy_s(obj_struct.svc_ctx_high_prio, 16, pData[i].svc_ctx_high_prio, 16);
        memcpy_s(obj_struct.svc_ctx_normal_prio, 16, pData[i].svc_ctx_normal_prio, 16);
        obj_struct.app_source_id = pData[i].app_source_id;
        obj_struct.app_serial_id = pData[i].app_serial_id;
        obj_struct.app_obj_id = pData[i].app_obj_id;
        obj_struct.app_version = pData[i].app_version;
        obj_struct.time_stamp_create = pData[i].time_stamp_create;

        DB_START_TEST_CPU_CYCLES(arp_re_batch_insert_sync_set_all_fields);
        ret = GmcSetVertexWithBuf(t_stmt, &s);
        DB_STOP_TEST_CPU_CYCLES(arp_re_batch_insert_sync_set_all_fields);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_set_arp_re_all_fields");
            break;
        }

        DB_START_TEST_CPU_CYCLES(arp_re_batch_insert_sync_db_batch_add_oper);
        ret = GmcBatchAddDML(batch, t_stmt);
        DB_STOP_TEST_CPU_CYCLES(arp_re_batch_insert_sync_db_batch_add_oper);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_batch_add_oper");
            j = 0;
            break;
        }
        if (j >= wr_loop || GMERR_DATA_EXCEPTION == ret || i >= end) {
            unsigned int totalNum = 0;
            unsigned int successNum = 0;
            GmcBatchRetT batchRet;
            DB_START_TEST_CPU_CYCLES(arp_re_batch_insert_sync_db_batch_exec);
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "GmcBatchExecute index:%u .", i);
                break;
            }
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "GmcBatchDeparseRet index:%u .", i);
                break;
            }
            DB_STOP_TEST_CPU_CYCLES(arp_re_batch_insert_sync_db_batch_exec);
            j = 0;
            total_num += totalNum;
            success_num += successNum;
        }
        if (ret != GMERR_OK || i >= end) {
            break;
        }
    }
    DB_START_TEST_CPU_CYCLES(arp_re_batch_insert_sync_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_re_batch_insert_sync_release_obj);

    return ret;
}

int32_t nhp_std_get_count_by_ip_ifindex_localhash_key(struct connObj *t_conn, uint32_t next_hop, uint32_t out_if_index)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(nhp_std_get_count_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "nhp_std", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "nhp_std");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(nhp_std_get_count_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_nhp_std_obj.");

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &next_hop, sizeof(next_hop));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue next_hop.");

    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &out_if_index, sizeof(out_if_index));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue out_if_index.");

    ret = GmcSetIndexKeyName(t_stmt, "ip_ifindex_localhash_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    ret = GmcExecute(t_stmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    uint64_t count;
    DB_START_TEST_CPU_CYCLES(nhp_std_get_count_by_key_get_table_count_by_key);
    ret = GmcGetVertexCount(t_stmt, "nhp_std", "ip_ifindex_localhash_key", &count);
    DB_STOP_TEST_CPU_CYCLES(nhp_std_get_count_by_key_get_table_count_by_key);

    DB_START_TEST_CPU_CYCLES(nhp_std_get_count_by_key_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(nhp_std_get_count_by_key_release_obj);

    return ret;
}

int32_t arp_insert_sync(struct connObj *t_conn, arp_product_struct_t *pData, uint32_t uDataCnt)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(arp_insert_sync_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp", GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(arp_insert_sync_create_obj);
    CHECK_OK_RET(ret, "db_create_arp_obj.");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    arp_product_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);
    for (uint32_t i = 0; i < uDataCnt; i++) {
        memcpy_s(obj_struct.ip_address, DB_ARP_IP_ADDRESS_LEN, pData[i].ip_address, DB_ARP_IP_ADDRESS_LEN);
        obj_struct.if_index = pData[i].if_index;
        obj_struct.vr_id = pData[i].vr_id;
        obj_struct.vrf_index = pData[i].vrf_index;
        memcpy_s(obj_struct.mac_address, DB_ARP_MAC_ADDRESS_LEN, pData[i].mac_address, DB_ARP_MAC_ADDRESS_LEN);
        obj_struct.link_type = pData[i].link_type;
        obj_struct.work_if_index = pData[i].work_if_index;
        obj_struct.atm_if_index = pData[i].atm_if_index;
        obj_struct.target_blade = pData[i].target_blade;
        obj_struct.target_port = pData[i].target_port;
        obj_struct.pe_vid = pData[i].pe_vid;
        obj_struct.ce_vid = pData[i].ce_vid;
        obj_struct.vcd = pData[i].vcd;
        obj_struct.fwd_if_type = pData[i].fwd_if_type;
        obj_struct.tunnel_type = pData[i].tunnel_type;
        obj_struct.path_flag = pData[i].path_flag;
        obj_struct.if_phy_type = pData[i].if_phy_type;
        obj_struct.main_if_phy_type = pData[i].main_if_phy_type;
        obj_struct.if_link_type = pData[i].if_link_type;
        obj_struct.if_encap_type = pData[i].if_encap_type;
        obj_struct.flow_id = pData[i].flow_id;
        obj_struct.tunnel_vrf_id = pData[i].tunnel_vrf_id;
        obj_struct.tunnel_id = pData[i].tunnel_id;
        obj_struct.tunnel_encap_id = pData[i].tunnel_encap_id;
        obj_struct.tunnel_sip = pData[i].tunnel_sip;
        obj_struct.tunnel_dip = pData[i].tunnel_dip;
        obj_struct.vsi_index = pData[i].vsi_index;
        obj_struct.smooth_id = pData[i].smooth_id;
        obj_struct.app_source_id = pData[i].app_source_id;
        obj_struct.app_obj_id = pData[i].app_obj_id;
        obj_struct.app_version = pData[i].app_version;
        memcpy_s(obj_struct.adj_service_status, 2, pData[i].adj_service_status, 2);
        memcpy_s(obj_struct.adj_service_errcode, 2, pData[i].adj_service_errcode, 2);
        memcpy_s(obj_struct.adj_svc_context_high_prio, 32, pData[i].adj_svc_context_high_prio, 32);
        memcpy_s(obj_struct.adj_svc_context_normal_prio, 32, pData[i].adj_svc_context_normal_prio, 32);
        memcpy_s(obj_struct.nhp_service_status, 2, pData[i].nhp_service_status, 2);
        memcpy_s(obj_struct.nhp_service_errcode, 2, pData[i].nhp_service_errcode, 2);
        memcpy_s(obj_struct.nhp_svc_context_high_prio, 16, pData[i].nhp_svc_context_high_prio, 16);
        memcpy_s(obj_struct.nhp_svc_context_normal_prio, 16, pData[i].nhp_svc_context_normal_prio, 16);
        obj_struct.vlanid = pData[i].vlanid;
        obj_struct.bridge_vlanid = pData[i].bridge_vlanid;
        obj_struct.is_trunk = pData[i].is_trunk;
        obj_struct.mlag_flag = pData[i].mlag_flag;
        obj_struct.state = pData[i].state;
        obj_struct.sub_if_index = pData[i].sub_if_index;
        obj_struct.time_stamp_create = pData[i].is_trunk;
        obj_struct.time_stamp_smooth = pData[i].time_stamp_smooth;

        DB_START_TEST_CPU_CYCLES(arp_insert_sync_set_all_fields);
        ret = GmcSetVertexWithBuf(t_stmt, &s);
        DB_STOP_TEST_CPU_CYCLES(arp_insert_sync_set_all_fields);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_set_arp_all_fields");
            break;
        }

        DB_START_TEST_CPU_CYCLES(arp_insert_sync_db_write_obj);
        ret = GmcExecute(t_stmt);
        DB_STOP_TEST_CPU_CYCLES(arp_insert_sync_db_write_obj);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_write_obj");
            break;
        }
        GmcResetStmt(t_stmt);
    }

    DB_START_TEST_CPU_CYCLES(arp_insert_sync_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_insert_sync_release_obj);
    return ret;
}

int32_t arp_fake_insert_nc_sync(struct connObj *t_conn, arp_fake_struct_t *pData, uint32_t uDataCnt)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(arp_fake_insert_nc_sync_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp_fake", GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp_fake");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(arp_fake_insert_nc_sync_create_obj);
    CHECK_OK_RET(ret, "db_create_arp_fake_obj.");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;
    GmcSeriT s;
    arp_fake_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);
    for (uint32_t i = 0; i < uDataCnt; i++) {
        memcpy_s(obj_struct.ip_address, 4, pData[i].ip_address, 4);
        obj_struct.ifindex = pData[i].ifindex;
        obj_struct.vrid = pData[i].vrid;
        obj_struct.vrfindex = pData[i].vrfindex;
        obj_struct.fake_time = pData[i].fake_time;
        obj_struct.fake_type = pData[i].fake_type;
        obj_struct.module_bitmap = pData[i].module_bitmap;
        obj_struct.time_stamp_create = pData[i].time_stamp_create;
        DB_START_TEST_CPU_CYCLES(arp_fake_insert_nc_sync_set_all_fields);
        ret = GmcSetVertexWithBuf(t_stmt, &s);
        DB_STOP_TEST_CPU_CYCLES(arp_fake_insert_nc_sync_set_all_fields);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_set_arp_fake_all_fields");
            break;
        }
        DB_START_TEST_CPU_CYCLES(arp_fake_insert_nc_sync_db_write_obj);
        ret = GmcExecute(t_stmt);
        DB_STOP_TEST_CPU_CYCLES(arp_fake_insert_nc_sync_db_write_obj);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_write_obj");
            break;
        }
        GmcResetStmt(t_stmt);
    }

    DB_START_TEST_CPU_CYCLES(arp_fake_insert_nc_sync_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_fake_insert_nc_sync_release_obj);
    return ret;
}

int32_t vpn_across_get_count_by_vpn_across_re_index(
    struct connObj *t_conn, uint32_t dst_ip, uint32_t mask, uint32_t src_fvrf_index)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(t_stmt, "vpn_across", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "vpn_across");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_vpn_across_obj.");

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &dst_ip, sizeof(dst_ip));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue dst_ip.");

    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &mask, sizeof(mask));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue mask.");

    ret = GmcSetIndexKeyValue(t_stmt, 2, GMC_DATATYPE_UINT32, &src_fvrf_index, sizeof(src_fvrf_index));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue src_fvrf_index.");

    ret = GmcSetIndexKeyName(t_stmt, "vpn_across_re_index");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    ret = GmcExecute(t_stmt);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    uint64_t count;
    ret = GmcGetVertexCount(t_stmt, "vpn_across", "vpn_across_re_index", &count);

    GmcFreeStmt(t_stmt);

    return ret;
}

int32_t arp_fake_delete_nc_sync(struct connObj *t_conn, char *pIp_address, uint32_t nIp_addressLen, uint32_t ifindex)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(arp_fake_delete_nc_sync_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp_fake", GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp_fake");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(arp_fake_delete_nc_sync_create_obj);
    CHECK_OK_RET(ret, "db_create_arp_fake_obj.");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;
    GmcSeriT s;
    arp_fake_key_t obj_struct;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialKeyLength(&s);
    memcpy_s(obj_struct.ip_address, nIp_addressLen, pIp_address, nIp_addressLen);
    obj_struct.ifindex = ifindex;

    ret = GmcSetIndexKeyWithBuf(t_stmt, 0, &s);
    CHECK_OK_RET(ret, "db_set_obj");

    DB_START_TEST_CPU_CYCLES(arp_fake_delete_nc_sync_db_remove_obj);
    ret = GmcExecute(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_fake_delete_nc_sync_db_remove_obj);
    CHECK_OK_RET(ret, "db_remove_obj");

    DB_START_TEST_CPU_CYCLES(arp_fake_delete_nc_sync_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_fake_delete_nc_sync_release_obj);
    return ret;
}

int32_t arp_re_batch_delete_sync(struct connObj *t_conn, arp_re_struct_t *pData, uint32_t uDataCnt)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }

    uint32_t j = 0;
    uint32_t total_num, success_num;

    DB_START_TEST_CPU_CYCLES(arp_re_batch_delete_sync_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp_re", GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp_re");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(arp_re_batch_delete_sync_create_obj);
    CHECK_OK_RET(ret, "db_create_arp_re_obj.");

    arp_re_key_t key_data = {0};
    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connS, &batchOption, &batch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcBatchPrepare:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    ret = GmcBatchBindStmt(batch, t_stmt);
    if (ret != 0) {
        DB_LOG_ERROR(ret, "GmcBatchBindStmt:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        DB_START_TEST_CPU_CYCLES(arp_re_batch_delete_sync_reset_batch_object);
        GmcResetStmt(t_stmt);
        DB_STOP_TEST_CPU_CYCLES(arp_re_batch_delete_sync_reset_batch_object);

        ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &pData[i].ip_address, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue ip_address.");

        ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &pData[i].vrid, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue ifindex.");

        ret = GmcSetIndexKeyValue(t_stmt, 2, GMC_DATATYPE_UINT32, &pData[i].vrfindex, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue ifindex.");

        ret = GmcSetIndexKeyName(t_stmt, "arp_re_key");
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcSetIndexKeyName index:%u .", i);
            break;
        }

        DB_START_TEST_CPU_CYCLES(arp_re_batch_delete_sync_db_batch_add_oper);
        ret = GmcBatchAddDML(batch, t_stmt);
        DB_STOP_TEST_CPU_CYCLES(arp_re_batch_delete_sync_db_batch_add_oper);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_batch_add_oper");
            j = 0;
            break;
        }
        if (j >= wr_loop || GMERR_DATA_EXCEPTION == ret || i >= end) {
            unsigned int totalNum = 0;
            unsigned int successNum = 0;
            GmcBatchRetT batchRet;
            DB_START_TEST_CPU_CYCLES(arp_re_batch_delete_sync_db_batch_exec);
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "GmcBatchExecute index:%u .", i);
                break;
            }
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "GmcBatchDeparseRet index:%u .", i);
                break;
            }
            DB_STOP_TEST_CPU_CYCLES(arp_re_batch_delete_sync_db_batch_exec);
            j = 0;
            total_num += totalNum;
            success_num += successNum;
        }
        if (ret != GMERR_OK || i >= end) {
            break;
        }
    }
    DB_START_TEST_CPU_CYCLES(arp_re_batch_delete_sync_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(arp_re_batch_delete_sync_release_obj);

    return ret;
}

int32_t arp_fake_insert_c_sync(struct connObj *t_conn, arp_fake_struct_t *pData, uint32_t uDataCnt)
{
    int32_t ret = GMERR_OK;
    if (!objW_arp_fake) {
        ret = BenchMarkAllocStmt(&t_conn->connS, &objW_arp_fake);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objW_arp_fake, "arp_fake", GMC_OPERATION_REPLACE);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp_fake");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_arp_fake_obj.");
    }

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objW_arp_fake;
    GmcSeriT s;
    arp_fake_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        memcpy_s(obj_struct.ip_address, 4, pData[i].ip_address, 4);
        obj_struct.ifindex = pData[i].ifindex;
        obj_struct.vrid = pData[i].vrid;
        obj_struct.vrfindex = pData[i].vrfindex;
        obj_struct.fake_time = pData[i].fake_time;
        obj_struct.fake_type = pData[i].fake_type;
        obj_struct.module_bitmap = pData[i].module_bitmap;
        obj_struct.time_stamp_create = pData[i].time_stamp_create;
        ret = GmcSetVertexWithBuf(objW_arp_fake, &s);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_set_arp_fake_all_fields");
            break;
        }
        ret = GmcExecute(objW_arp_fake);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "db_write_obj");
            break;
        }
        GmcResetStmt(objW_arp_fake);
    }
    return ret;
}

int32_t arp_delete_sync(struct connObj *t_conn, char *pIp_address, uint32_t nIp_addressLen, uint32_t if_index)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&t_conn->connS, &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(t_stmt, "arp", GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_arp_obj");

    arp_product_key_t key_data = {0};
    memcpy_s(key_data.ip_address, DB_ARP_IP_ADDRESS_LEN, pIp_address, nIp_addressLen);
    key_data.if_index = if_index;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&key_data;
    s.userData = &ctx;
    getSerialKeyLength(&s);

    ret = GmcSetIndexKeyWithBuf(t_stmt, 0, &s);

    ret = GmcExecute(t_stmt);
    CHECK_OK_RET(ret, "db_remove_obj");

    GmcFreeStmt(t_stmt);
    return ret;
}

int32_t arp_fake_delete_c_sync(struct connObj *t_conn, char *pIp_address, uint32_t nIp_addressLen, uint32_t ifindex)
{
    int32_t ret = GMERR_OK;
    if (!objDel_arp_fake) {
        ret = BenchMarkAllocStmt(&t_conn->connS, &objDel_arp_fake);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objDel_arp_fake, "arp_fake", GMC_OPERATION_DELETE);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp_fake");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_arp_fake_obj.");
    } else {
        GmcResetStmt(objDel_arp_fake);
        CHECK_OK_RET(ret, "db_reset_arp_fake_object");
    }

    arp_fake_key_t key_data = {0};
    memcpy_s(key_data.ip_address, 4, pIp_address, nIp_addressLen);
    key_data.ifindex = ifindex;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objDel_arp_fake;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&key_data;
    s.userData = &ctx;
    getSerialKeyLength(&s);

    ret = GmcSetIndexKeyWithBuf(objDel_arp_fake, 0, &s);
    ret = GmcExecute(objDel_arp_fake);
    CHECK_OK_RET(ret, "db_remove_obj");

    return ret;
}

int32_t arp_fake_scan_iter_process(struct connObj *t_conn)
{
    int32_t ret = GMERR_OK;
    if (!objScan_arp_fake) {
        ret = BenchMarkAllocStmt(&t_conn->connS, &objScan_arp_fake);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
    } else {
        DB_START_TEST_CPU_CYCLES(arpAged_db_reset_arp_fake_object);
        GmcResetStmt(objScan_arp_fake);
        DB_STOP_TEST_CPU_CYCLES(arpAged_db_reset_arp_fake_object);
        CHECK_OK_RET(ret, "db_reset_arp_fake_object.");
    }

    ret = GmcPrepareStmtByLabelName(objScan_arp_fake, "arp_fake", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "arp_fake");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_arp_fake_obj.");
    ret = GmcExecute(objScan_arp_fake);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcExecute.");
        return ret;
    }

    arp_fake_struct_t read_data;
    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objScan_arp_fake;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;
    uint32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        DB_START_TEST_CPU_CYCLES(arpAged_db_root_iterator_next);
        ret = GmcFetch(objScan_arp_fake, &isFinish);
        DB_STOP_TEST_CPU_CYCLES(arpAged_db_root_iterator_next);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcFetch index:%u .", tmpScanCnt);
            return ret;
        }
        if (isFinish == true) {
            break;
        }
        DB_START_TEST_CPU_CYCLES(arpAged_db_read_obj_with_buf);
        ret = GmcGetVertexDeseri(objScan_arp_fake, &d);
        DB_STOP_TEST_CPU_CYCLES(arpAged_db_read_obj_with_buf);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "perf_vertex_struct_getData index:%u .", tmpScanCnt);
            break;
        }
        if (read_data.fake_time > 0) {
            read_data.fake_time--;
            ret = arp_fake_insert_c_sync(t_conn, &read_data, 1);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "arp_fake_insert_sync failed");
                break;
            }
        } else {
            ret = benchMark_arp_read_by_key(t_conn, (char *)read_data.ip_address, 4, read_data.ifindex);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "arp_read_by_key failed");
                break;
            }
            ret = vpn_across_get_count_by_nhp_index(t_conn, (char *)read_data.ip_address, 4, read_data.ifindex);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "arp_fake_insert_sync failed");
                break;
            }
            ret = arp_delete_sync(t_conn, (char *)read_data.ip_address, 4, read_data.ifindex);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "arp_delete_sync failed");
                break;
            }
            ret = arp_fake_delete_c_sync(t_conn, (char *)read_data.ip_address, 4, read_data.ifindex);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "arp_fake_delete_sync failed");
                break;
            }
        }
        tmpScanCnt++;
        if (tmpScanCnt > 99) {
            break;
        }
    }
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// vrf_class_data 主键读 1   非结构化读所有字段
int32_t vrf_class_data_read_by_key(
    struct connObj *t_conn, uint32_t In_table_id, uint32_t In_producer_pid, uint32_t In_vr_id, uint32_t In_vrf_index)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(t_stmt, "vrf_class_data", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "vrf_class_data");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_vrf_class_data_obj");

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &In_table_id, sizeof(In_table_id));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue In_table_id.");
    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &In_producer_pid, sizeof(In_producer_pid));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue In_producer_pid.");
    ret = GmcSetIndexKeyValue(t_stmt, 2, GMC_DATATYPE_UINT32, &In_vr_id, sizeof(In_vr_id));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue In_vr_id.");
    ret = GmcSetIndexKeyValue(t_stmt, 3, GMC_DATATYPE_UINT32, &In_vrf_index, sizeof(In_vrf_index));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue In_vrf_index.");

    ret = GmcSetIndexKeyName(t_stmt, "vrf_class_data_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    ret = GmcExecute(t_stmt);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(t_stmt, &isFinish);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcFetch failed");
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        bool isNull;
        uint8_t value_u8;
        uint32_t value_u32;
        uint64_t value_u64;
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"table_id", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName table_id.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"producer_pid", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName producer_pid.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"vr_id", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vr_id.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"vrf_index", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vrf_index.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"topology_id", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName topology_id.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"vrf_state", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vrf_state.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"vrf_state_version", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vrf_state_version.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"vrf_data_version", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vrf_data_version.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"smooth_flag", &value_u32, sizeof(int32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName smooth_flag.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"vrf_data_machine", &value_u8, sizeof(int8_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vrf_data_machine.");
        ret =
            GmcGetVertexPropertyByName(t_stmt, (char *)"time_stamp_verify_begin", &value_u64, sizeof(int64_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName time_stamp_verify_begin.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"time_stamp_verify_end", &value_u64, sizeof(int64_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName time_stamp_verify_end.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"time_stamp_age_begin", &value_u64, sizeof(int64_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName time_stamp_age_begin.");
        ret = GmcGetVertexPropertyByName(t_stmt, (char *)"time_stamp_age_end", &value_u64, sizeof(int64_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName time_stamp_age_end.");
    }

    GmcFreeStmt(t_stmt);
    return ret;
}

status_t nhp_group_read_by_key(struct connObj *conn, uint32_t nhp_group_id, uint32_t vr_id)
{
    GmcStmtT *stmt;
    int32_t ret = BenchMarkAllocStmt(&(conn->connS), &stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    const char *labelName = "nhp_group";
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        printf("GmcPrepareStmtByLabelName: %s failed.", labelName);
        return ret;
    }
    CHECK_OK_RET(ret, "GmcPrepareStmtByLabelName");

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName primary_key.");
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &nhp_group_id, sizeof(nhp_group_id));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue nhp_group_id.");
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        printf("GmcExecute failed");
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            printf("GmcFetch failed");
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        bool isNull;
        uint8_t value_u8;
        uint16_t value_u16;
        uint32_t value_u32;
        uint64_t value_u64;
        uint8_t value_fix2[2];
        uint8_t value_fix32[32];
        ret = GmcGetVertexPropertyByName(stmt, (char *)"nhp_group_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName nhp_group_id.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"vr_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vr_id.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"vrf_index", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vrf_index.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"flags", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"ref_count", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName ref_count.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"new_vrf", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName new_vrf.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"nhp_number", &value_u16, sizeof(uint16_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName nhp_number.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"nhp_type_high_prio", &value_u8, sizeof(uint8_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName nhp_type_high_prio.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"nhp_type_normal_prio", &value_u8, sizeof(uint8_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName nhp_type_normal_prio.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"status_high_prio", &value_u8, sizeof(uint8_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName status_high_prio.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"status_normal_prio", &value_u8, sizeof(uint8_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName status_normal_prio.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"errcode_high_prio", &value_fix2, sizeof(value_fix2), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName errcode_high_prio.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"errcode_normal_prio", &value_fix2, sizeof(value_fix2), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName errcode_normal_prio.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"svc_ctx_high_prio", value_fix32, sizeof(value_fix32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName svc_ctx_high_prio.");
        ret =
            GmcGetVertexPropertyByName(stmt, (char *)"svc_ctx_normal_prio", value_fix32, sizeof(value_fix32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName svc_ctx_normal_prio.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"app_source_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName app_source_id.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"table_smooth_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName table_smooth_id.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"time_stamp_update", &value_u64, sizeof(uint64_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName time_stamp_update.");

        ret = GmcGetVertexPropertyByName(stmt, (char *)"oper_bitmap", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName oper_bitmap.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"attr_flag", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName attr_flag.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"time_stamp_app", &value_u64, sizeof(uint64_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName time_stamp_app.");
        ret = GmcGetVertexPropertyByName(stmt, (char *)"time_stamp_svc", &value_u64, sizeof(uint64_t), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName time_stamp_svc.");
    }

    GmcFreeStmt(stmt);

    return ret;
}

status_t ip4forward_batch_insert_async(
    struct connObj *t_conn, ip4forward_struct_t *pData, uint32_t uDataCnt, GmcBatchDoneT call_back, void *pUser_data)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! GmcAllocStmt failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    const char *labelName = "ip4forward";
    ret = GmcPrepareStmtByLabelName(t_stmt, labelName, GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        printf("GmcPrepareStmtByLabelName:%s failed.", labelName);
        return ret;
    }

    struct structTestCtx ctx = {0};
    ctx.stmt = t_stmt;

    GmcSeriT s;
    ip4forward_product_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;

    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connA, &batchOption, &batch);
    if (ret != GMERR_OK) {
        printf("GmcBatchPrepare:%s failed.", labelName);
        return GMERR_CONFIG_ERROR;
    }
    ret = GmcBatchBindStmt(batch, t_stmt);
    if (ret != GMERR_OK) {
        printf("GmcBatchBindStmt:%s failed.", labelName);
        return ret;
    }

    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    for (uint32_t i = 0, j = 0; i < uDataCnt; i++) {
        j++;

        obj_struct.vr_id = pData[i].vr_id;
        obj_struct.vrf_index = pData[i].vrf_index;
        obj_struct.dest_ip_addr = pData[i].dest_ip_addr;
        obj_struct.mask_len = pData[i].mask_len;
        obj_struct.nhp_group_flag = pData[i].nhp_group_flag;
        obj_struct.qos_profile_id = pData[i].qos_profile_id;
        obj_struct.primary_label = pData[i].primary_label;
        obj_struct.attribute_id = pData[i].attribute_id;
        obj_struct.nhp_group_id = pData[i].nhp_group_id;
        obj_struct.path_flags = pData[i].path_flags;
        obj_struct.flags = pData[i].flags;
        obj_struct.status_high_prio = pData[i].status_high_prio;
        obj_struct.status_normal_prio = pData[i].status_normal_prio;
        obj_struct.errcode_high_prio = pData[i].errcode_high_prio;
        obj_struct.errcode_normal_prio = pData[i].errcode_normal_prio;
        memcpy_s(obj_struct.svc_ctx_high_prio, DB_IP4FORWARD_SVC_CTX_HIGH_PRIO_LEN, pData[i].svc_ctx_high_prio,
            DB_IP4FORWARD_SVC_CTX_HIGH_PRIO_LEN);
        memcpy_s(obj_struct.svc_ctx_normal_prio, DB_IP4FORWARD_SVC_CTX_NORMAL_PRIO_LEN, pData[i].svc_ctx_normal_prio,
            DB_IP4FORWARD_SVC_CTX_NORMAL_PRIO_LEN);
        obj_struct.app_source_id = pData[i].app_source_id;
        obj_struct.table_smooth_id = pData[i].table_smooth_id;
        obj_struct.app_obj_id = pData[i].app_obj_id;
        obj_struct.app_version = pData[i].app_version;
        obj_struct.trace = pData[i].trace;
        obj_struct.route_flags = pData[i].route_flags;
        obj_struct.next_type = pData[i].next_type;
        obj_struct.reserved = pData[i].reserved;
        obj_struct.time_stamp_create = pData[i].time_stamp_create;
        obj_struct.time_stamp_smooth = pData[i].time_stamp_smooth;

        getSerialVertexLength(&s);
        ret = GmcSetVertexWithBuf(t_stmt, &s);
        if (ret != GMERR_OK) {
            printf("GmcSetVertexWithBuf failed");
            break;
        }

        ret = GmcBatchAddDML(batch, t_stmt);
        if (ret != GMERR_OK) {
            printf("GmcBatchAddDML failed");
            j = 0;
            break;
        }

        if (j >= wr_loop || i >= end) {
            do {
                ret = GmcBatchExecuteAsync(batch, call_back, pUser_data);
                if ((ret == GMERR_REQUEST_TIME_OUT) || (ret == GMERR_CONNECTION_SEND_BUFFER_FULL)) {
                    (void)usleep(300);
                } else {
                    break;
                }
            } while (1);

            if (ret) {
                printf("db_batch_exec_async");
                break;
            }

            if (i >= end) {
                break;
            }

            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }

    GmcFreeStmt(t_stmt);
    return ret;
}

static uint32_t g_nhp_group_ref_count = 0;

status_t nhp_group_update_by_key(struct connObj *t_conn, uint32_t nhp_group_id, uint32_t vr_id)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! GmcAllocStmt failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    const char *labelName = "nhp_group";
    ret = GmcPrepareStmtByLabelName(t_stmt, labelName, GMC_OPERATION_UPDATE);
    if (ret != GMERR_OK) {
        printf("GmcPrepareStmtByLabelName:%s failed.", labelName);
        return ret;
    }

    ret = GmcSetIndexKeyName(t_stmt, "primary_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName primary_key.");
    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &nhp_group_id, sizeof(nhp_group_id));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue nhp_group_id.");
    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

    uint32_t ref_count = ++g_nhp_group_ref_count;
    ret = GmcSetVertexProperty(t_stmt, "ref_count", GMC_DATATYPE_UINT32, &ref_count, sizeof(ref_count));
    CHECK_OK_RET(ret, "GmcSetVertexProperty ref_count.");

    uint32_t oper_bitmap = 100;
    ret = GmcSetVertexProperty(t_stmt, "oper_bitmap", GMC_DATATYPE_UINT32, &oper_bitmap, sizeof(oper_bitmap));
    CHECK_OK_RET(ret, "GmcSetVertexProperty oper_bitmap.");

    uint64_t time_stamp_update = 100;
    ret = GmcSetVertexProperty(
        t_stmt, "time_stamp_update", GMC_DATATYPE_TIME, &time_stamp_update, sizeof(time_stamp_update));
    CHECK_OK_RET(ret, "GmcSetVertexProperty time_stamp_update.");

    ret = GmcExecute(t_stmt);
    CHECK_OK_RET(ret, "GmcExecute unsuccessful.");

    GmcFreeStmt(t_stmt);
    return ret;
}

// l3mc_mfib 主键读 1
int32_t l3mc_mfib_read_by_key(struct connObj *t_conn, uint32_t vrId, uint32_t vrfId, uint32_t grpAddr, uint32_t srcAddr)
{
    int32_t ret = GMERR_OK;
    if (objR_l3mc_mfib == NULL) {
        ret = BenchMarkAllocStmt(&t_conn->connS, &objR_l3mc_mfib);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objR_l3mc_mfib, "l3mc_mfib", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_mfib");
            return ret;
        }
    }
    GmcResetStmt(objR_l3mc_mfib);

    db_l3mc_mfib_key_t key_data = {0};
    key_data.vrId = vrId;
    key_data.vrfId = vrfId;
    key_data.grpAddr = grpAddr;
    key_data.srcAddr = srcAddr;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objR_l3mc_mfib;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mc_mfib_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(l3mc_mfib_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};
    ret = GmcGetVertexBuf(objR_l3mc_mfib, 0, &s, &inputBufInfo);
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_ERROR(ret, "db_read_obj_with_buf of l3mc_mfib_struct_t :%s failed.", "l3mc_mfib");
        return ret;
    }
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// l2mc_vlan_mfibv4 主键读
int32_t l2mc_vlan_mfibv4_read_by_key(
    struct connObj *t_conn, uint32_t vrIndex, uint32_t groupAddr, uint32_t sourceAddr, uint32_t vlanId)
{
    int32_t ret = GMERR_OK;
    if (objR_l2mc_vlan_mfibv4 == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objR_l2mc_vlan_mfibv4);
        CHECK_OK_RET(ret, "db_create_l2mc_vlan_mfibv4_cache_object");
        ret = GmcPrepareStmtByLabelName(objR_l2mc_vlan_mfibv4, "l2mc_vlan_mfibv4", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_vlan_mfibv4");
            return ret;
        }
    } else {
        GmcResetStmt(objR_l2mc_vlan_mfibv4);
        CHECK_OK_RET(ret, "db_reset_l2mc_vlan_mfibv4_cache_object");
    }

    ret = GmcPrepareStmtByLabelName(objR_l2mc_vlan_mfibv4, "l2mc_vlan_mfibv4", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_vlan_mfibv4");
        return ret;
    }

    db_l2mc_vlan_mfibv4_key_t key_data = {0};
    key_data.vrIndex = vrIndex;
    key_data.groupAddr = groupAddr;
    key_data.sourceAddr = sourceAddr;
    key_data.vlanId = vlanId;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objR_l2mc_vlan_mfibv4;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l2mc_vlan_mfibv4_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(l2mc_vlan_mfibv4_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    ret = GmcGetVertexBuf(objR_l2mc_vlan_mfibv4, 0, &s, &inputBufInfo);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// l2mc_vlan_mfibv4 主键读  缓存obj
int32_t l2mc_vlan_mfibv4_c_read_by_key(
    struct connObj *t_conn, uint32_t vrIndex, uint32_t groupAddr, uint32_t sourceAddr, uint32_t vlanId)
{
    int32_t ret = GMERR_OK;
    if (objR_C_l2mc_vlan_mfibv4 == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objR_C_l2mc_vlan_mfibv4);
        CHECK_OK_RET(ret, "db_create_l2mc_vlan_mfibv4_cache_object");
        ret = GmcPrepareStmtByLabelName(objR_C_l2mc_vlan_mfibv4, "l2mc_vlan_mfibv4", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_vlan_mfibv4");
            return ret;
        }
    } else {
        GmcResetStmt(objR_C_l2mc_vlan_mfibv4);
        CHECK_OK_RET(ret, "db_reset_l2mc_vlan_mfibv4_cache_object");
    }

    ret = GmcPrepareStmtByLabelName(objR_C_l2mc_vlan_mfibv4, "l2mc_vlan_mfibv4", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_vlan_mfibv4");
        return ret;
    }

    db_l2mc_vlan_mfibv4_key_t key_data = {0};
    key_data.vrIndex = vrIndex;
    key_data.groupAddr = groupAddr;
    key_data.sourceAddr = sourceAddr;
    key_data.vlanId = vlanId;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objR_C_l2mc_vlan_mfibv4;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l2mc_vlan_mfibv4_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(l2mc_vlan_mfibv4_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};
    ret = GmcGetVertexBuf(objR_C_l2mc_vlan_mfibv4, 0, &s, &inputBufInfo);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// l2mc_vlan_mfibv4 主键读  不缓存obj
int32_t l2mc_vlan_mfibv4_nc_read_by_key(
    struct connObj *t_conn, uint32_t vrIndex, uint32_t groupAddr, uint32_t sourceAddr, uint32_t vlanId)
{
    GmcStmtT *t_stmt = NULL;
    int32_t ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    CHECK_OK_RET(ret, "db_create_l2mc_vlan_mfibv4_cache_object");
    ret = GmcPrepareStmtByLabelName(t_stmt, "l2mc_vlan_mfibv4", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_vlan_mfibv4");
        return ret;
    }

    db_l2mc_vlan_mfibv4_key_t key_data = {0};
    key_data.vrIndex = vrIndex;
    key_data.groupAddr = groupAddr;
    key_data.sourceAddr = sourceAddr;
    key_data.vlanId = vlanId;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l2mc_vlan_mfibv4_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(l2mc_vlan_mfibv4_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};
    ret = GmcGetVertexBuf(t_stmt, 0, &s, &inputBufInfo);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// l2mc_vlan_mfibv4 同步单写  第一次写
int32_t l2mc_vlan_mfibv4_insert1_sync(struct connObj *t_conn, l2mc_vlan_mfibv4_struct_t *pData)
{
    int32_t ret = GMERR_OK;
    if (objW1_l2mc_vlan_mfibv4 == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objW1_l2mc_vlan_mfibv4);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objW1_l2mc_vlan_mfibv4, "l2mc_vlan_mfibv4", GMC_OPERATION_INSERT);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_vlan_mfibv4");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l2mc_vlan_mfibv4_obj.");
    } else {
        GmcResetStmt(objW1_l2mc_vlan_mfibv4);
        if (ret) {
            DB_LOG_ERROR(ret, "db_reset_l2mc_vlan_mfibv4_object");
        }
    }

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objW1_l2mc_vlan_mfibv4;

    GmcSeriT s;
    l2mc_vlan_mfibv4_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    obj_struct.vrIndex = pData[0].vrIndex;
    obj_struct.groupAddr = pData[0].groupAddr;
    obj_struct.sourceAddr = pData[0].sourceAddr;
    obj_struct.vlanId = pData[0].vlanId;
    obj_struct.vrfIndex = pData[0].vrfIndex;
    obj_struct.groupMaskLen = pData[0].groupMaskLen;
    obj_struct.sourceMaskLen = pData[0].sourceMaskLen;
    obj_struct.pathFlags = pData[0].pathFlags;
    obj_struct.mcId = pData[0].mcId;
    obj_struct.mcidVersion = pData[0].mcidVersion;
    obj_struct.mfibVersion = pData[0].mfibVersion;
    memcpy_s(obj_struct.svcCtxHighPrio, DB_L2MC_VLAN_MFIBV4_SVCCTXHIGHPRIO_LEN, pData[0].svcCtxHighPrio,
        DB_L2MC_VLAN_MFIBV4_SVCCTXHIGHPRIO_LEN);
    memcpy_s(obj_struct.svcCtxNormalPrio, DB_L2MC_VLAN_MFIBV4_SVCCTXNORMALPRIO_LEN, pData[0].svcCtxNormalPrio,
        DB_L2MC_VLAN_MFIBV4_SVCCTXNORMALPRIO_LEN);
    obj_struct.appSrcPid = pData[0].appSrcPid;
    obj_struct.smoothVersion = pData[0].smoothVersion;
    obj_struct.appVersion = pData[0].appVersion;
    memcpy_s(obj_struct.serviceStatus, DB_L2MC_VLAN_MFIBV4_SERVICESTATUS_LEN, pData[0].serviceStatus,
        DB_L2MC_VLAN_MFIBV4_SERVICESTATUS_LEN);
    obj_struct.hppErrCode = pData[0].hppErrCode;
    obj_struct.enpErrCode = pData[0].enpErrCode;
    obj_struct.timeStampCreate = pData[0].timeStampCreate;
    obj_struct.timeStampSmooth = pData[0].timeStampSmooth;

    ret = GmcSetVertexWithBuf(objW1_l2mc_vlan_mfibv4, &s);
    if (ret) {
        DB_LOG_ERROR(ret, "db_set_arp_all_fields");
        return ret;
    }

    ret = GmcExecute(objW1_l2mc_vlan_mfibv4);
    if (ret) {
        DB_LOG_ERROR(ret, "db_write_obj");
        return ret;
    }

    return ret;
}

// l2mc_vlan_mfibv4 同步单写  第二次写
int32_t l2mc_vlan_mfibv4_insert2_sync(struct connObj *t_conn, l2mc_vlan_mfibv4_struct_t *pData)
{
    int32_t ret = GMERR_OK;
    if (objW2_l2mc_vlan_mfibv4 == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objW2_l2mc_vlan_mfibv4);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objW2_l2mc_vlan_mfibv4, "l2mc_vlan_mfibv4", GMC_OPERATION_REPLACE);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_vlan_mfibv4");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l2mc_vlan_mfibv4_obj.");
    } else {
        GmcResetStmt(objW2_l2mc_vlan_mfibv4);
        if (ret) {
            DB_LOG_ERROR(ret, "db_reset_l2mc_vlan_mfibv4_object");
        }
    }

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objW2_l2mc_vlan_mfibv4;

    GmcSeriT s;
    l2mc_vlan_mfibv4_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    obj_struct.vrIndex = pData[0].vrIndex;
    obj_struct.groupAddr = pData[0].groupAddr;
    obj_struct.sourceAddr = pData[0].sourceAddr;
    obj_struct.vlanId = pData[0].vlanId;
    obj_struct.vrfIndex = pData[0].vrfIndex;
    obj_struct.groupMaskLen = pData[0].groupMaskLen;
    obj_struct.sourceMaskLen = pData[0].sourceMaskLen;
    obj_struct.pathFlags = pData[0].pathFlags;
    obj_struct.mcId = pData[0].mcId;
    obj_struct.mcidVersion = pData[0].mcidVersion;
    obj_struct.mfibVersion = pData[0].mfibVersion;
    memcpy_s(obj_struct.svcCtxHighPrio, DB_L2MC_VLAN_MFIBV4_SVCCTXHIGHPRIO_LEN, pData[0].svcCtxHighPrio,
        DB_L2MC_VLAN_MFIBV4_SVCCTXHIGHPRIO_LEN);
    memcpy_s(obj_struct.svcCtxNormalPrio, DB_L2MC_VLAN_MFIBV4_SVCCTXNORMALPRIO_LEN, pData[0].svcCtxNormalPrio,
        DB_L2MC_VLAN_MFIBV4_SVCCTXNORMALPRIO_LEN);
    obj_struct.appSrcPid = pData[0].appSrcPid;
    obj_struct.smoothVersion = pData[0].smoothVersion;
    obj_struct.appVersion = pData[0].appVersion;
    memcpy_s(obj_struct.serviceStatus, DB_L2MC_VLAN_MFIBV4_SERVICESTATUS_LEN, pData[0].serviceStatus,
        DB_L2MC_VLAN_MFIBV4_SERVICESTATUS_LEN);
    obj_struct.hppErrCode = pData[0].hppErrCode;
    obj_struct.enpErrCode = pData[0].enpErrCode;
    obj_struct.timeStampCreate = pData[0].timeStampCreate;
    obj_struct.timeStampSmooth = pData[0].timeStampSmooth;

    ret = GmcSetVertexWithBuf(objW2_l2mc_vlan_mfibv4, &s);
    if (ret) {
        DB_LOG_ERROR(ret, "db_set_arp_all_fields");
        return ret;
    }

    ret = GmcExecute(objW2_l2mc_vlan_mfibv4);
    if (ret) {
        DB_LOG_ERROR(ret, "db_write_obj");
        return ret;
    }

    return ret;
}

// l3mc_register_oif 主键读
int32_t l3mc_register_oif_read_by_key(
    struct connObj *t_conn, uint32_t vrId, uint32_t vrfId, uint32_t grpAddr, uint32_t srcAddr)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(t_stmt, "l3mc_register_oif", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_register_oif");
        return ret;
    }

    db_l3mc_register_oif_key_t key_data = {0};
    key_data.vrId = vrId;
    key_data.vrfId = vrfId;
    key_data.grpAddr = grpAddr;
    key_data.srcAddr = srcAddr;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mc_register_oif_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(l3mc_register_oif_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    ret = GmcGetVertexBuf(t_stmt, 0, &s, &inputBufInfo);
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_ERROR(
            ret, "db_read_obj_with_buf of l3mc_register_oif_struct_t :%s failed.", "l3mc_register_oif");
        return ret;
    }

    GmcFreeStmt(t_stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t l3mc_mfib_c_get_count_by_key(
    struct connObj *t_conn, uint32_t uVrId, uint32_t uGrpAddr, uint32_t uSrcAddr, uint32_t uInIfIndex)
{
    int32_t ret = GMERR_OK;
    if (l3mc_mfib_stat_obj == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &l3mc_mfib_stat_obj);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(l3mc_mfib_stat_obj, "l3mc_mfib", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_mfib");
            return ret;
        }
    }

    CHECK_OK_RET(ret, "db_create_l3mc_mfib_obj.");

    DB_START_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);
    GmcResetStmt(l3mc_mfib_stat_obj);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);

    ret = GmcSetIndexKeyValue(l3mc_mfib_stat_obj, 0, GMC_DATATYPE_UINT32, &uVrId, sizeof(uVrId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uVrId.");
    ret = GmcSetIndexKeyValue(l3mc_mfib_stat_obj, 1, GMC_DATATYPE_UINT32, &uGrpAddr, sizeof(uGrpAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uGrpAddr.");
    ret = GmcSetIndexKeyValue(l3mc_mfib_stat_obj, 2, GMC_DATATYPE_UINT32, &uSrcAddr, sizeof(uSrcAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uSrcAddr.");
    ret = GmcSetIndexKeyValue(l3mc_mfib_stat_obj, 3, GMC_DATATYPE_UINT32, &uInIfIndex, sizeof(uInIfIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uInIfIndex.");

    ret = GmcSetIndexKeyName(l3mc_mfib_stat_obj, "sg_intf_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    ret = GmcExecute(l3mc_mfib_stat_obj);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    uint64_t count;
    DB_START_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);
    ret = GmcGetVertexCount(l3mc_mfib_stat_obj, "l3mc_mfib", "sg_intf_key", &count);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);

    return ret;
}

// no cache obj: db_get_table_record_count_by_key
int32_t l3mc_mfib_nc_get_count_by_key(
    struct connObj *t_conn, uint32_t uVrId, uint32_t uGrpAddr, uint32_t uSrcAddr, uint32_t uInIfIndex)
{
    GmcStmtT *t_stmt;
    int32_t ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(t_stmt, "l3mc_mfib", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_mfib");
        return ret;
    }

    CHECK_OK_RET(ret, "db_create_l3mc_mfib_obj.");

    DB_START_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);
    GmcResetStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &uVrId, sizeof(uVrId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uVrId.");
    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &uGrpAddr, sizeof(uGrpAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uGrpAddr.");
    ret = GmcSetIndexKeyValue(t_stmt, 2, GMC_DATATYPE_UINT32, &uSrcAddr, sizeof(uSrcAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uSrcAddr.");
    ret = GmcSetIndexKeyValue(t_stmt, 3, GMC_DATATYPE_UINT32, &uInIfIndex, sizeof(uInIfIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uInIfIndex.");

    ret = GmcSetIndexKeyName(t_stmt, "sg_intf_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    ret = GmcExecute(t_stmt);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    uint64_t count;
    DB_START_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);
    ret = GmcGetVertexCount(t_stmt, "l3mc_mfib", "sg_intf_key", &count);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);

    GmcFreeStmt(t_stmt);

    return ret;
}

// l3mc_mfib 根据索引 SG_INTF_KEY 统计记录数
int32_t l3mc_mfib_get_count_by_sg_intf_key(
    struct connObj *t_conn, uint32_t uVrId, uint32_t uGrpAddr, uint32_t uSrcAddr, uint32_t uInIfIndex)
{
    int32_t ret = GMERR_OK;
    if (objCnt_l3mc_mfib == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objCnt_l3mc_mfib);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objCnt_l3mc_mfib, "l3mc_mfib", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_mfib");
            return ret;
        }
    }

    CHECK_OK_RET(ret, "db_create_l3mc_mfib_obj.");

    DB_START_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);
    GmcResetStmt(objCnt_l3mc_mfib);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);

    ret = GmcSetIndexKeyValue(objCnt_l3mc_mfib, 0, GMC_DATATYPE_UINT32, &uVrId, sizeof(uVrId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uVrId.");
    ret = GmcSetIndexKeyValue(objCnt_l3mc_mfib, 1, GMC_DATATYPE_UINT32, &uGrpAddr, sizeof(uGrpAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uGrpAddr.");
    ret = GmcSetIndexKeyValue(objCnt_l3mc_mfib, 2, GMC_DATATYPE_UINT32, &uSrcAddr, sizeof(uSrcAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uSrcAddr.");
    ret = GmcSetIndexKeyValue(objCnt_l3mc_mfib, 3, GMC_DATATYPE_UINT32, &uInIfIndex, sizeof(uInIfIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue uInIfIndex.");

    ret = GmcSetIndexKeyName(objCnt_l3mc_mfib, "sg_intf_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    ret = GmcExecute(objCnt_l3mc_mfib);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    uint64_t count;
    DB_START_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);
    ret = GmcGetVertexCount(objCnt_l3mc_mfib, "l3mc_mfib", "sg_intf_key", &count);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mfib_get_count_by_key_reset_cache_object);

    return ret;
}

int32_t mfib_get_table_count(struct connObj *t_conn)
{
    int32_t ret = GMERR_OK;
    // l3mc_bd_untag_elb
    if (objCnt_l3mc_bd_untag_elb == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objCnt_l3mc_bd_untag_elb);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objCnt_l3mc_bd_untag_elb, "l3mc_bd_untag_elb", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_bd_untag_elb");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mc_bd_untag_elb_cache_object.");
    }

    DB_START_TEST_CPU_CYCLES(get_table_count_l3mc_bd_untag_elb);
    GmcResetStmt(objCnt_l3mc_bd_untag_elb);
    DB_STOP_TEST_CPU_CYCLES(get_table_count_l3mc_bd_untag_elb);

    // l3mc_bd_tag_elb
    if (objCnt_l3mc_bd_tag_elb == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objCnt_l3mc_bd_tag_elb);
    }
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(objCnt_l3mc_bd_tag_elb, "l3mc_bd_tag_elb", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_bd_tag_elb");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_l3mc_bd_tag_elb_obj.");

    DB_START_TEST_CPU_CYCLES(get_table_count_l3mc_bd_tag_elb);
    GmcResetStmt(objCnt_l3mc_bd_tag_elb);
    DB_STOP_TEST_CPU_CYCLES(get_table_count_l3mc_bd_tag_elb);

    // l3mc_bd_tnl_elb
    if (objCnt_l3mc_bd_tnl_elb == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objCnt_l3mc_bd_tnl_elb);
    }
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(objCnt_l3mc_bd_tnl_elb, "l3mc_bd_tnl_elb", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_bd_tnl_elb");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_l3mc_bd_tnl_elb_obj.");

    DB_START_TEST_CPU_CYCLES(get_table_count_l3mc_bd_tnl_elb);
    GmcResetStmt(objCnt_l3mc_bd_tnl_elb);
    DB_STOP_TEST_CPU_CYCLES(get_table_count_l3mc_bd_tnl_elb);
    // l3mc_mvpn_tnl_elb
    if (objCnt_l3mc_mvpn_tnl_elb == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objCnt_l3mc_mvpn_tnl_elb);
    }
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(objCnt_l3mc_mvpn_tnl_elb, "l3mc_mvpn_tnl_elb", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_mvpn_tnl_elb");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_l3mc_mvpn_tnl_elb_obj.");

    DB_START_TEST_CPU_CYCLES(get_table_count_l3mc_mvpn_tnl_elb);
    GmcResetStmt(objCnt_l3mc_mvpn_tnl_elb);
    DB_STOP_TEST_CPU_CYCLES(get_table_count_l3mc_mvpn_tnl_elb);
    return ret;
}

int32_t l3mc_bd_untag_elb_c_get_table_count(struct connObj *t_conn)
{
    int32_t ret = GMERR_OK;

    // l3mc_bd_untag_elb
    if (l3mc_bd_untag_elb_stat_obj == NULL) {
        ret = GmcAllocStmt(t_conn->connS, &l3mc_bd_untag_elb_stat_obj);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(l3mc_bd_untag_elb_stat_obj, "l3mc_bd_untag_elb", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_bd_untag_elb");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mc_bd_untag_elb_obj.");
    }

    DB_START_TEST_CPU_CYCLES(cache_l3mc_bd_untag_elb_get_table_count);
    GmcResetStmt(l3mc_bd_untag_elb_stat_obj);
    DB_STOP_TEST_CPU_CYCLES(cache_l3mc_bd_untag_elb_get_table_count);
    return ret;
}

int32_t l3mc_bd_untag_elb_nc_get_table_count(struct connObj *t_conn)
{
    DB_START_TEST_CPU_CYCLES(no_cache_l3mc_bd_untag_elb_get_table_count);
    int32_t ret = GMERR_OK;
    GmcStmtT *object = NULL;

    // l3mc_bd_untag_elb
    ret = GmcAllocStmt(t_conn->connS, &object);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(object, "l3mc_bd_untag_elb", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_bd_untag_elb");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_l3mc_bd_untag_elb_obj.");

    DB_START_TEST_CPU_CYCLES(no_cache_l3mc_bd_untag_elb_get_table_count);
    GmcResetStmt(object);
    DB_STOP_TEST_CPU_CYCLES(no_cache_l3mc_bd_untag_elb_get_table_count);
    DB_START_TEST_CPU_CYCLES(no_cache_l3mc_bd_untag_elb_get_table_count);
    GmcFreeStmt(object);
    DB_STOP_TEST_CPU_CYCLES(no_cache_l3mc_bd_untag_elb_get_table_count);

    return ret;
}

int32_t if_read_by_key(struct connObj *t_conn, uint32_t uInputIndex)
{
    int32_t ret = GMERR_OK;
    GmcStmtT *t_stmt;
    ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return ret;
    }
    DB_START_TEST_CPU_CYCLES(if_read_by_key_db_create_if_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "if", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(if_read_by_key_db_create_if_obj);

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &uInputIndex, sizeof(uInputIndex));
    CHECK_OK_RET(ret, "set index key value");
    ret = GmcSetIndexKeyName(t_stmt, "if_pk");
    CHECK_OK_RET(ret, "set index key name");

    DB_START_TEST_CPU_CYCLES(if_read_by_key_db_read_obj);
    ret = GmcExecute(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(if_read_by_key_db_read_obj);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "db_read_obj if failed");
        GmcFreeStmt(t_stmt);
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(t_stmt, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch failed");
            return ret;
        }
        if (isFinish == true) {
            break;
        }
        GmcNodeT *root;
        ret = GmcGetRootNode(t_stmt, &root);
        CHECK_OK_RET(ret, "GmcGetRootNode.");

        bool isNull;
        uint32_t ifindex;
        char if_name[64] = {0};
        uint32_t vrid;
        uint32_t if_type;
        uint32_t shutdown;
        uint32_t linkup;
        uint32_t tbtp;
        uint32_t tb;
        uint32_t tp;
        uint32_t port_switch;
        uint32_t forwardType;
        char macAddress[6] = {0};
        uint16_t ipv4_mtu;
        uint16_t ipv4_enable;
        uint16_t ipv6_mtu;
        uint16_t ipv6_enable;
        uint32_t on_board;
        uint32_t lagid;
        uint32_t hppsvcflg;
        uint32_t error_down;
        uint64_t speed;
        uint32_t link_protocol;
        uint32_t vrf_index;
        uint32_t port_group_id;
        uint32_t if_group_id;
        uint32_t if_df;
        uint32_t encap_type;
        uint32_t is_subif;
        uint32_t mainifindex;
        uint32_t logicTB;
        uint32_t logicTP;
        uint32_t vlandomain;
        uint32_t coreId;
        uint32_t ipv4mss;
        uint32_t ipv6mss;

        uint32_t get_field_uint32;

        uint32_t if_macaddress_len = 0;
        uint32_t if_name_len = 0;
        uint32_t trunk_id;
        uint32_t vlan_id;
        uint32_t l2_portindex;
        uint32_t tunnel_id;
        uint32_t vsi;
        uint32_t id;
        ret = GmcNodeGetPropertyByName(root, (char *)"ifindex", &ifindex, sizeof(ifindex), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ifindex.");
        ret = GmcNodeGetPropertyByName(root, (char *)"name", &if_name, sizeof(if_name), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName name.");
        ret = GmcNodeGetPropertyByName(root, (char *)"vrid", &vrid, sizeof(vrid), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrid.");
        ret = GmcNodeGetPropertyByName(root, (char *)"if_type", &if_type, sizeof(if_type), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_type.");
        ret = GmcNodeGetPropertyByName(root, (char *)"shutdown", &shutdown, sizeof(shutdown), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName shutdown.");
        ret = GmcNodeGetPropertyByName(root, (char *)"linkup", &linkup, sizeof(linkup), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName linkup.");
        ret = GmcNodeGetPropertyByName(root, (char *)"tbtp", &tbtp, sizeof(tbtp), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tbtp.");
        ret = GmcNodeGetPropertyByName(root, (char *)"tb", &tb, sizeof(tb), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tb.");
        ret = GmcNodeGetPropertyByName(root, (char *)"tp", &tp, sizeof(tp), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tp.");
        ret = GmcNodeGetPropertyByName(root, (char *)"port_switch", &port_switch, sizeof(port_switch), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_switch.");
        ret = GmcNodeGetPropertyByName(root, (char *)"forwardType", &forwardType, sizeof(forwardType), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName forwardType.");
        ret = GmcNodeGetPropertyByName(root, (char *)"macAddress", &macAddress, sizeof(macAddress), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName macAddress.");
        ret = GmcNodeGetPropertyByName(root, (char *)"ipv4_mtu", &ipv4_mtu, sizeof(ipv4_mtu), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4_mtu.");
        ret = GmcNodeGetPropertyByName(root, (char *)"ipv4_enable", &ipv4_enable, sizeof(ipv4_enable), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4_enable.");
        ret = GmcNodeGetPropertyByName(root, (char *)"ipv6_mtu", &ipv6_mtu, sizeof(ipv6_mtu), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6_mtu.");
        ret = GmcNodeGetPropertyByName(root, (char *)"ipv6_enable", &ipv6_enable, sizeof(ipv6_enable), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6_enable.");
        ret = GmcNodeGetPropertyByName(root, (char *)"on_board", &on_board, sizeof(on_board), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName on_board.");
        ret = GmcNodeGetPropertyByName(root, (char *)"lagid", &lagid, sizeof(lagid), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName lagid.");
        ret = GmcNodeGetPropertyByName(root, (char *)"hppsvcflg", &hppsvcflg, sizeof(hppsvcflg), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName hppsvcflg.");
        ret = GmcNodeGetPropertyByName(root, (char *)"error_down", &error_down, sizeof(error_down), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName error_down.");
        ret = GmcNodeGetPropertyByName(root, (char *)"speed", &speed, sizeof(speed), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName speed.");
        ret = GmcNodeGetPropertyByName(root, (char *)"link_protocol", &link_protocol, sizeof(link_protocol), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName link_protocol.");
        ret = GmcNodeGetPropertyByName(root, (char *)"vrf_index", &vrf_index, sizeof(vrf_index), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrf_index.");
        ret = GmcNodeGetPropertyByName(root, (char *)"port_group_id", &port_group_id, sizeof(port_group_id), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_group_id.");
        ret = GmcNodeGetPropertyByName(root, (char *)"if_group_id", &if_group_id, sizeof(if_group_id), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_group_id.");
        ret = GmcNodeGetPropertyByName(root, (char *)"if_df", &if_df, sizeof(if_df), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_df.");
        ret = GmcNodeGetPropertyByName(root, (char *)"encap_type", &encap_type, sizeof(encap_type), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName encap_type.");
        ret = GmcNodeGetPropertyByName(root, (char *)"is_subif", &is_subif, sizeof(is_subif), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName is_subif.");
        ret = GmcNodeGetPropertyByName(root, (char *)"mainifindex", &mainifindex, sizeof(mainifindex), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName mainifindex.");
        ret = GmcNodeGetPropertyByName(root, (char *)"logicTB", &logicTB, sizeof(logicTB), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName logicTB.");
        ret = GmcNodeGetPropertyByName(root, (char *)"logicTP", &logicTP, sizeof(logicTP), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName logicTP.");
        ret = GmcNodeGetPropertyByName(root, (char *)"vlandomain", &vlandomain, sizeof(vlandomain), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vlandomain.");
        ret = GmcNodeGetPropertyByName(root, (char *)"coreId", &coreId, sizeof(coreId), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName coreId.");
        ret = GmcNodeGetPropertyByName(root, (char *)"ipv4mss", &ipv4mss, sizeof(ipv4mss), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4mss.");
        ret = GmcNodeGetPropertyByName(root, (char *)"ipv6mss", &ipv6mss, sizeof(ipv6mss), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6mss.");

        char str_pro[64] = {0};
        unsigned int proSize = sizeof(str_pro);

        uint16_t value_u16;
        uint32_t value_u32;
        uint64_t value_u64;

        GmcNodeT *node_dev;
        ret = GmcNodeGetChild(root, "dev", &node_dev);
        CHECK_OK_RET(ret, "GmcNodeGetChild dev.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"dev_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName dev_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"chassis_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName chassis_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"slot_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName slot_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"card_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName card_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"unit_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName unit_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"port_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_id.");

        ret = GmcNodeGetPropertyByName(node_dev, (char *)"port_num", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_num.");

        GmcNodeT *node_l2;
        ret = GmcNodeGetChild(root, "l2", &node_l2);
        CHECK_OK_RET(ret, "GmcNodeGetChild l2.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"trunk_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName trunk_id.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"vlan_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vlan_id.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"l2_portindex", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName l2_portindex.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"vsi", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vsi.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"tunnel_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tunnel_id.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"bd_id", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName bd_id.");
    }

    DB_START_TEST_CPU_CYCLES(if_read_by_key_db_release_if_object);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(if_read_by_key_db_release_if_object);
    return 0;
}

// l3mc_elb 主键读       缓存了object
int32_t l3mc_elb_read_by_key(struct connObj *t_conn, uint32_t vrId, uint32_t vrfId, uint32_t grpAddr, uint32_t srcAddr,
    uint32_t outIfIndex, uint8_t srcType)
{
    int32_t ret;
    if (!objR_l3mc_elb) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objR_l3mc_elb);
        CHECK_OK_RET(ret, "db_create_l3mc_elb_obj");
        ret = GmcPrepareStmtByLabelName(objR_l3mc_elb, "l3mc_elb", GMC_OPERATION_SCAN);
        CHECK_OK_RET(ret, "db_create_l3mc_elb_obj");
    }

    DB_START_TEST_CPU_CYCLES(step1_l3mc_elb_read_by_key_reset_cache_object);
    GmcResetStmt(objR_l3mc_elb);
    DB_STOP_TEST_CPU_CYCLES(step1_l3mc_elb_read_by_key_reset_cache_object);

    db_l3mc_elb_key_t key_data = {0};
    key_data.vrId = vrId;
    key_data.vrfId = vrfId;
    key_data.grpAddr = grpAddr;
    key_data.srcAddr = srcAddr;
    key_data.outIfIndex = outIfIndex;
    key_data.srcType = srcType;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objR_l3mc_elb;
    GmcSeriT s;

    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&key_data;
    s.userData = &ctx;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mc_elb_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(l3mc_elb_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    DB_START_TEST_CPU_CYCLES(step1_l3mc_elb_read_by_key_db_read_obj_with_buf);
    ret = GmcGetVertexBuf(objR_l3mc_elb, 0, &s, &inputBufInfo);
    DB_STOP_TEST_CPU_CYCLES(step1_l3mc_elb_read_by_key_db_read_obj_with_buf);
    if (ret != GMERR_OK && ret != GMERR_UNDEFINED_OBJECT) {
        DB_LOG_ERROR(ret, "db_read_obj_with_buf l3mc_elb_struct_t failed");
    }
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// l3mcvlan_elb 主键读
int32_t l3mcvlan_elb_read_by_key(struct connObj *t_conn, uint32_t vrIndex, uint32_t vrfIndex, uint32_t grpAddr,
    uint32_t srcAddr, uint32_t ifIndex, uint16_t vlanId)
{
    int32_t ret;
    if (!objR_l3mcvlan_elb) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objR_l3mcvlan_elb);
        CHECK_OK_RET(ret, "db_create_l3mcvlan_elb_cache_object");
        ret = GmcPrepareStmtByLabelName(objR_l3mcvlan_elb, "l3mcvlan_elb", GMC_OPERATION_SCAN);
        CHECK_OK_RET(ret, "db_create_l3mcvlan_elb_cache_object");
    }

    DB_START_TEST_CPU_CYCLES(l3mcvlan_elb_read_by_key_reset_cache_object);
    GmcResetStmt(objR_l3mcvlan_elb);
    DB_STOP_TEST_CPU_CYCLES(l3mcvlan_elb_read_by_key_reset_cache_object);

    db_l3mcvlan_elb_key_t key_data = {0};
    key_data.vrIndex = vrIndex;
    key_data.vrfIndex = vrfIndex;
    key_data.grpAddr = grpAddr;
    key_data.srcAddr = srcAddr;
    key_data.ifIndex = ifIndex;
    key_data.vlanId = vlanId;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objR_l3mcvlan_elb;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mcvlan_elb_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(l3mcvlan_elb_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    DB_START_TEST_CPU_CYCLES(l3mcvlan_elb_read_by_key_db_read_obj_with_buf);
    ret = GmcGetVertexBuf(objR_l3mcvlan_elb, 0, &s, &inputBufInfo);
    DB_STOP_TEST_CPU_CYCLES(l3mcvlan_elb_read_by_key_db_read_obj_with_buf);
    if (ret != GMERR_OK && ret != GMERR_UNDEFINED_OBJECT) {
        DB_LOG_ERROR(ret, "db_read_obj_with_buf l3mcvlan_elb_struct_t failed");
    }
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

//  l3mc_elb_sub 主键读   缓存了object, 使用了缓存新接口
int32_t l3mc_elb_sub_read_by_key(struct connObj *t_conn, uint32_t vrId, uint32_t vrfId, uint32_t grpAddr,
    uint32_t srcAddr, uint32_t vlanId, uint32_t portIfIndex, uint8_t srcType)
{
    int32_t ret;
    if (!objR_l3mc_elb_sub) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objR_l3mc_elb_sub);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objR_l3mc_elb_sub, "l3mc_elb_sub", GMC_OPERATION_SCAN);
        CHECK_OK_RET(ret, "db_create_l3mc_elb_sub_cache_object");
    }

    DB_START_TEST_CPU_CYCLES(l3mc_elb_sub_read_by_key_reset_cache_object);
    GmcResetStmt(objR_l3mc_elb_sub);
    DB_STOP_TEST_CPU_CYCLES(l3mc_elb_sub_read_by_key_reset_cache_object);

    db_l3mc_elb_sub_key_t key_data = {0};
    key_data.vrId = vrId;
    key_data.vrfId = vrfId;
    key_data.grpAddr = grpAddr;
    key_data.srcAddr = srcAddr;
    key_data.vlanId = vlanId;
    key_data.portIfIndex = portIfIndex;
    key_data.srcType = srcType;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objR_l3mc_elb_sub;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mc_elb_sub_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(l3mc_elb_sub_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    DB_START_TEST_CPU_CYCLES(l3mc_elb_sub_read_by_key_db_read_obj_with_buf);
    ret = GmcGetVertexBuf(objR_l3mc_elb_sub, 0, &s, &inputBufInfo);
    DB_STOP_TEST_CPU_CYCLES(l3mc_elb_sub_read_by_key_db_read_obj_with_buf);
    if (ret != GMERR_OK && ret != GMERR_UNDEFINED_OBJECT) {
        DB_LOG_ERROR(ret, "db_read_obj_with_buf l3mc_elb_sub_struct_t failed");
    }
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// mlag_peer_link 主键读
int32_t mlag_peer_link_read_by_key(struct connObj *t_conn, uint32_t vrid)
{
    int32_t ret;
    GmcStmtT *t_stmt;
    ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }

    DB_START_TEST_CPU_CYCLES(mlag_peer_link_read_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "mlag_peer_link", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "mlag_peer_link");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(mlag_peer_link_read_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_mlag_peer_link_obj");

    db_mlag_peer_link_key_t key_data = {0};
    key_data.vrid = vrid;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = t_stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    mlag_peer_link_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(mlag_peer_link_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    ret = GmcGetVertexBuf(t_stmt, 0, &s, &inputBufInfo);
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_ERROR(ret, "db_read_obj_with_buf mlag_peer_link_struct_t failed");
    }

    DB_START_TEST_CPU_CYCLES(mlag_peer_link_read_by_key_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(mlag_peer_link_read_by_key_release_obj);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// l3mc_mvpn_tnl_elb 根据索引 tnl_vsg_key 获取count
int32_t l3mc_mvpn_tnl_elb_get_count_by_tnl_vsg_key(
    struct connObj *t_conn, uint32_t tnlVrId, uint32_t tnlVrfId, uint32_t tnlGroupAddr, uint32_t tnlSourceAddr)
{
    int32_t ret;
    GmcStmtT *t_stmt;
    ret = BenchMarkAllocStmt(&(t_conn->connS), &t_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(l3mc_mvpn_tnl_elb_get_count_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "l3mc_mvpn_tnl_elb", GMC_OPERATION_SCAN);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mvpn_tnl_elb_get_count_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_l3mc_mvpn_tnl_elb_obj.");

    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &tnlVrId, sizeof(tnlVrId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue tnlVrId.");

    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &tnlVrfId, sizeof(tnlVrfId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue tnlVrfId.");

    ret = GmcSetIndexKeyValue(t_stmt, 2, GMC_DATATYPE_UINT32, &tnlGroupAddr, sizeof(tnlGroupAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue tnlGroupAddr.");

    ret = GmcSetIndexKeyValue(t_stmt, 3, GMC_DATATYPE_UINT32, &tnlSourceAddr, sizeof(tnlSourceAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue tnlSourceAddr.");

    ret = GmcSetIndexKeyName(t_stmt, "tnl_vsg_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

    ret = GmcExecute(t_stmt);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    uint64_t count;
    DB_START_TEST_CPU_CYCLES(l3mc_mvpn_tnl_elb_get_count_by_key_get_record_count);
    ret = GmcGetVertexCount(t_stmt, "l3mc_mvpn_tnl_elb", "tnl_vsg_key", &count);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mvpn_tnl_elb_get_count_by_key_get_record_count);
    // printf(">>> count by_key tnl_vsg_key of l3mc_mvpn_tnl_elb is : %llu\n", count);

    DB_START_TEST_CPU_CYCLES(l3mc_mvpn_tnl_elb_get_count_by_key_release_obj);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(l3mc_mvpn_tnl_elb_get_count_by_key_release_obj);

    return ret;
}

// l3mcvlan_elb  索引：MFIB_REFRESH_KEY 扫描
int32_t l3mcvlan_elb_scan_by_mfib_refresh_key(
    struct connObj *t_conn, uint32_t vrIndex, uint32_t vrfIndex, uint32_t grpAddr, uint32_t srcAddr)
{
    int32_t ret;
    if (!objQry_l3mcvlan_elb) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_l3mcvlan_elb);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objQry_l3mcvlan_elb, "l3mcvlan_elb", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mcvlan_elb");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mcvlan_elb_cache_object");
    }
    DB_START_TEST_CPU_CYCLES(l3mcvlan_elb_scan_by_key_reset_cache_object);
    GmcResetStmt(objQry_l3mcvlan_elb);
    DB_STOP_TEST_CPU_CYCLES(l3mcvlan_elb_scan_by_key_reset_cache_object);

    ret = GmcSetIndexKeyValue(objQry_l3mcvlan_elb, 0, GMC_DATATYPE_UINT32, &vrIndex, sizeof(vrIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrIndex");
    ret = GmcSetIndexKeyValue(objQry_l3mcvlan_elb, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrfIndex");
    ret = GmcSetIndexKeyValue(objQry_l3mcvlan_elb, 2, GMC_DATATYPE_UINT32, &grpAddr, sizeof(grpAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: grpAddr");
    ret = GmcSetIndexKeyValue(objQry_l3mcvlan_elb, 3, GMC_DATATYPE_UINT32, &srcAddr, sizeof(srcAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: srcAddr");
    ret = GmcSetIndexKeyName(objQry_l3mcvlan_elb, "mfib_refresh_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: mfib_refresh_key");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_l3mcvlan_elb;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mcvlan_elb_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_l3mcvlan_elb);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        return ret;
    }

    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        DB_START_TEST_CPU_CYCLES(l3mcvlan_elb_scan_by_key_db_root_iterator_next);
        ret = GmcFetch(objQry_l3mcvlan_elb, &isFinish);
        DB_STOP_TEST_CPU_CYCLES(l3mcvlan_elb_scan_by_key_db_root_iterator_next);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_l3mcvlan_elb, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            break;
        }
        tmpScanCnt++;
    }
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// l3mc_elb_sub  二级索引扫描 MFIB_refresh_key
int32_t l3mc_elb_sub_scan_by_MFIB_refresh_key(struct connObj *t_conn, uint32_t vrId, uint32_t vrfId, uint32_t grpAddr,
    uint32_t srcAddr, uint32_t outIfIndex, uint8_t srcType)
{
    int32_t ret;
    GmcStmtT *objQry_l3mc_elb_sub = NULL;

    ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_l3mc_elb_sub);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return GMERR_CONNECTION_FAILURE;
    }

    DB_START_TEST_CPU_CYCLES(l3mc_elb_sub_scan_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(objQry_l3mc_elb_sub, "l3mc_elb_sub", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_elb_sub");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(l3mc_elb_sub_scan_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_l3mc_elb_sub_cache_object");

    ret = GmcSetIndexKeyValue(objQry_l3mc_elb_sub, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(vrId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrId");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb_sub, 1, GMC_DATATYPE_UINT32, &vrfId, sizeof(vrfId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrfId");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb_sub, 2, GMC_DATATYPE_UINT32, &grpAddr, sizeof(grpAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: grpAddr");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb_sub, 3, GMC_DATATYPE_UINT32, &srcAddr, sizeof(srcAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: srcAddr");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb_sub, 4, GMC_DATATYPE_UINT32, &outIfIndex, sizeof(outIfIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: outIfIndex");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb_sub, 5, GMC_DATATYPE_UINT8, &srcType, sizeof(srcType));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: srcType");
    ret = GmcSetIndexKeyName(objQry_l3mc_elb_sub, "MFIB_refresh_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: MFIB_refresh_key");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_l3mc_elb_sub;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mc_elb_sub_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_l3mc_elb_sub);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        GmcFreeStmt(objQry_l3mc_elb_sub);
        return ret;
    }
    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        DB_START_TEST_CPU_CYCLES(l3mc_elb_sub_scan_by_key_db_root_iterator_next);
        ret = GmcFetch(objQry_l3mc_elb_sub, &isFinish);
        DB_STOP_TEST_CPU_CYCLES(l3mc_elb_sub_scan_by_key_db_root_iterator_next);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_l3mc_elb_sub);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_l3mc_elb_sub, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_l3mc_elb_sub);
            break;
        }
        tmpScanCnt++;
    }

    DB_START_TEST_CPU_CYCLES(l3mc_elb_sub_scan_by_key_release_obj);
    GmcFreeStmt(objQry_l3mc_elb_sub);
    DB_STOP_TEST_CPU_CYCLES(l3mc_elb_sub_scan_by_key_release_obj);
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

int32_t mlag_port_pair_scan_by_pk(struct connObj *t_conn, uint32_t if_index)
{
    int32_t ret;
    GmcStmtT *objQry_mlag_port_pair = NULL;

    ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_mlag_port_pair);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return GMERR_CONNECTION_FAILURE;
    }

    DB_START_TEST_CPU_CYCLES(mlag_port_pair_scan_by_pk_create_obj);
    ret = GmcPrepareStmtByLabelName(objQry_mlag_port_pair, "mlag_port_pair", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "mlag_port_pair");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(mlag_port_pair_scan_by_pk_create_obj);
    CHECK_OK_RET(ret, "db_create_mlag_port_pair_cache_object");

    ret = GmcSetIndexKeyValue(objQry_mlag_port_pair, 0, GMC_DATATYPE_UINT32, &if_index, sizeof(if_index));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: if_index");
    ret = GmcSetIndexKeyName(objQry_mlag_port_pair, "pk");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: pk");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_mlag_port_pair;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    mlag_port_pair_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_mlag_port_pair);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        GmcFreeStmt(objQry_mlag_port_pair);
        return ret;
    }

    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        DB_START_TEST_CPU_CYCLES(mlag_port_pair_scan_by_pk_db_root_iterator_next);
        ret = GmcFetch(objQry_mlag_port_pair, &isFinish);
        DB_STOP_TEST_CPU_CYCLES(mlag_port_pair_scan_by_pk_db_root_iterator_next);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_mlag_port_pair);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_mlag_port_pair, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_mlag_port_pair);
            break;
        }
        tmpScanCnt++;
    }

    DB_START_TEST_CPU_CYCLES(mlag_port_pair_scan_by_pk_release_obj);
    GmcFreeStmt(objQry_mlag_port_pair);
    DB_STOP_TEST_CPU_CYCLES(mlag_port_pair_scan_by_pk_release_obj);
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// l3mc_elb 二级索引扫描 mfib_refresh_key
int32_t l3mc_elb_scan_by_mfib_refresh_key(
    struct connObj *t_conn, uint32_t vrId, uint32_t vrfId, uint32_t grpAddr, uint32_t srcAddr, uint8_t srcType)
{
    int32_t ret;
    if (!objQry_l3mc_elb) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_l3mc_elb);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
            return GMERR_CONNECTION_FAILURE;
        }

        ret = GmcPrepareStmtByLabelName(objQry_l3mc_elb, "l3mc_elb", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_elb");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mc_elb_cache_object");
    }

    DB_START_TEST_CPU_CYCLES(l3mc_elb_scan_by_key_reset_cache_object);
    GmcResetStmt(objQry_l3mc_elb);
    DB_STOP_TEST_CPU_CYCLES(l3mc_elb_scan_by_key_reset_cache_object);

    ret = GmcSetIndexKeyValue(objQry_l3mc_elb, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(vrId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrId");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb, 1, GMC_DATATYPE_UINT32, &vrfId, sizeof(vrfId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrfId");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb, 2, GMC_DATATYPE_UINT32, &grpAddr, sizeof(grpAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: grpAddr");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb, 3, GMC_DATATYPE_UINT32, &srcAddr, sizeof(srcAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: srcAddr");
    ret = GmcSetIndexKeyValue(objQry_l3mc_elb, 4, GMC_DATATYPE_UINT8, &srcType, sizeof(srcType));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: srcType");
    ret = GmcSetIndexKeyName(objQry_l3mc_elb, "MFIB_refresh_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: MFIB_refresh_key");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_l3mc_elb;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mc_elb_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_l3mc_elb);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        return ret;
    }

    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        DB_START_TEST_CPU_CYCLES(l3mc_elb_scan_by_key_db_root_iterator_next);
        ret = GmcFetch(objQry_l3mc_elb, &isFinish);
        DB_STOP_TEST_CPU_CYCLES(l3mc_elb_scan_by_key_db_root_iterator_next);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_l3mc_elb, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            break;
        }
        tmpScanCnt++;
    }
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// l2mc_elb4 二级索引扫描  vlanId_key   缓存obj
int32_t l2mc_elb4_c_scan_by_vlanId_key(struct connObj *t_conn, uint32_t vrIndex, uint16_t vlanId)
{
    int32_t ret;
    if (!objQry_C_l2mc_elb4) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_C_l2mc_elb4);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objQry_C_l2mc_elb4, "l2mc_elb4", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_elb4");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l2mc_elb4_cache_object");
    }

    DB_START_TEST_CPU_CYCLES(l2mc_elb4_c_scan_by_vlanId_key);
    GmcResetStmt(objQry_C_l2mc_elb4);

    ret = GmcSetIndexKeyValue(objQry_C_l2mc_elb4, 0, GMC_DATATYPE_UINT32, &vrIndex, sizeof(vrIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrIndex");
    ret = GmcSetIndexKeyValue(objQry_C_l2mc_elb4, 1, GMC_DATATYPE_UINT16, &vlanId, sizeof(vlanId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vlanId");
    ret = GmcSetIndexKeyName(objQry_C_l2mc_elb4, "vlanId_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: vlanId_key");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_C_l2mc_elb4;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l2mc_elb4_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_C_l2mc_elb4);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        return ret;
    }

    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(objQry_C_l2mc_elb4, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_C_l2mc_elb4, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            break;
        }
        tmpScanCnt++;
    }

    DB_STOP_TEST_CPU_CYCLES(l2mc_elb4_c_scan_by_vlanId_key);
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// l2mc_elb4 二级索引扫描  vlanId_key   不缓存obj
int32_t l2mc_elb4_nc_scan_by_vlanId_key(struct connObj *t_conn, uint32_t vrIndex, uint16_t vlanId)
{
    DB_START_TEST_CPU_CYCLES(l2mc_elb4_nc_scan_by_vlanId_key);
    int32_t ret;
    GmcStmtT *objQry_NC_l2mc_elb4 = NULL;

    ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_NC_l2mc_elb4);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return GMERR_CONNECTION_FAILURE;
    }

    ret = GmcPrepareStmtByLabelName(objQry_NC_l2mc_elb4, "l2mc_elb4", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l2mc_elb4");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_l2mc_elb4_obj");

    db_l2mc_elb4_vlanId_key_t key_data;
    key_data.vrIndex = vrIndex;
    key_data.vlanId = vlanId;

    ret = GmcSetIndexKeyValue(objQry_NC_l2mc_elb4, 0, GMC_DATATYPE_UINT32, &vrIndex, sizeof(vrIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrIndex");
    ret = GmcSetIndexKeyValue(objQry_NC_l2mc_elb4, 1, GMC_DATATYPE_UINT16, &vlanId, sizeof(vlanId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vlanId");
    ret = GmcSetIndexKeyName(objQry_NC_l2mc_elb4, "vlanId_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: vlanId_key");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_NC_l2mc_elb4;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l2mc_elb4_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_NC_l2mc_elb4);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        GmcFreeStmt(objQry_NC_l2mc_elb4);
        return ret;
    }

    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(objQry_NC_l2mc_elb4, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_NC_l2mc_elb4);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_NC_l2mc_elb4, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_NC_l2mc_elb4);
            break;
        }
        tmpScanCnt++;
    }

    GmcFreeStmt(objQry_NC_l2mc_elb4);
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// l3mcv4_loop_elb 二级索引扫描 MFIB_refresh_key
int32_t l3mcv4_loop_elb_scan_by_mfib_refresh_key(
    struct connObj *t_conn, uint32_t vrId, uint32_t vrfId, uint32_t groupAddr, uint32_t sourceAddr)
{
    int32_t ret;
    GmcStmtT *objQry_l3mcv4_loop_elb = NULL;
    ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_l3mcv4_loop_elb);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(objQry_l3mcv4_loop_elb, "l3mcv4_loop_elb", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mcv4_loop_elb");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_l3mcv4_loop_elb_obj");

    ret = GmcSetIndexKeyValue(objQry_l3mcv4_loop_elb, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(vrId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrId");
    ret = GmcSetIndexKeyValue(objQry_l3mcv4_loop_elb, 1, GMC_DATATYPE_UINT32, &vrfId, sizeof(vrfId));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrfId");
    ret = GmcSetIndexKeyValue(objQry_l3mcv4_loop_elb, 2, GMC_DATATYPE_UINT32, &groupAddr, sizeof(groupAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: groupAddr");
    ret = GmcSetIndexKeyValue(objQry_l3mcv4_loop_elb, 3, GMC_DATATYPE_UINT32, &sourceAddr, sizeof(sourceAddr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: sourceAddr");
    ret = GmcSetIndexKeyName(objQry_l3mcv4_loop_elb, "MFIB_refresh_key");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: MFIB_refresh_key");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_l3mcv4_loop_elb;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    l3mcv4_loop_elb_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_l3mcv4_loop_elb);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        GmcFreeStmt(objQry_l3mcv4_loop_elb);
        return ret;
    }

    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(objQry_l3mcv4_loop_elb, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_l3mcv4_loop_elb);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_l3mcv4_loop_elb, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_l3mcv4_loop_elb);
            break;
        }
        tmpScanCnt++;
    }

    GmcFreeStmt(objQry_l3mcv4_loop_elb);
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// if 主键读2个字段tb/tp
int32_t if_read_2fields_by_key(struct connObj *t_conn, uint32_t uInputIndex)
{
    int32_t ret = GMERR_OK;
    GmcStmtT *objR2f_if = NULL;
    ret = BenchMarkAllocStmt(&(t_conn->connS), &objR2f_if);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return ret;
    }
    ret = GmcPrepareStmtByLabelName(objR2f_if, "if", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_if_obj");

    GmcResetStmt(objR2f_if);

    ret = GmcSetIndexKeyValue(objR2f_if, 0, GMC_DATATYPE_UINT32, &uInputIndex, sizeof(uInputIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: uInputIndex");
    ret = GmcSetIndexKeyName(objR2f_if, "if_pk");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: if_pk");

    ret = GmcExecute(objR2f_if);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "db_read_obj if failed");
        GmcFreeStmt(objR2f_if);
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(objR2f_if, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch failed");
            return ret;
        }
        if (isFinish == true) {
            break;
        }
        GmcNodeT *root;
        ret = GmcGetRootNode(objR2f_if, &root);
        CHECK_OK_RET(ret, "GmcGetRootNode.");

        bool isNull;
        uint32_t tb;
        uint32_t tp;
        ret = GmcNodeGetPropertyByName(root, (char *)"tb", &tb, sizeof(tb), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tb.");
        ret = GmcNodeGetPropertyByName(root, (char *)"tp", &tp, sizeof(tp), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tp.");
    }

    GmcFreeStmt(objR2f_if);
    return ret;
}

// if_port 主键读2个字段unit_id/port_id
int32_t if_port_read_2fields_by_key(struct connObj *t_conn, uint32_t Input_tb, uint32_t Input_tp)
{
    int32_t ret = GMERR_OK;
    GmcStmtT *objR2f_if_port = NULL;
    ret = BenchMarkAllocStmt(&(t_conn->connS), &objR2f_if_port);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return ret;
    }
    ret = GmcPrepareStmtByLabelName(objR2f_if_port, "if_port", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if_port");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_if_port_obj");

    ret = GmcSetIndexKeyValue(objR2f_if_port, 0, GMC_DATATYPE_UINT32, &Input_tb, sizeof(Input_tb));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: Input_tb");
    ret = GmcSetIndexKeyValue(objR2f_if_port, 1, GMC_DATATYPE_UINT32, &Input_tp, sizeof(Input_tp));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: Input_tp");
    ret = GmcSetIndexKeyName(objR2f_if_port, "if_port_pk");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: if_port_pk");

    ret = GmcExecute(objR2f_if_port);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "db_read_obj if failed");
        GmcFreeStmt(objR2f_if_port);
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(objR2f_if_port, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch failed");
            return ret;
        }
        if (isFinish == true) {
            break;
        }
        GmcNodeT *root;
        ret = GmcGetRootNode(objR2f_if_port, &root);
        CHECK_OK_RET(ret, "GmcGetRootNode.");

        bool isNull;
        uint32_t unit_id;
        uint32_t port_id;
        ret = GmcNodeGetPropertyByName(root, (char *)"unit_id", &unit_id, sizeof(unit_id), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName unit_id.");
        ret = GmcNodeGetPropertyByName(root, (char *)"port_id", &port_id, sizeof(port_id), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_id.");
    }

    GmcFreeStmt(objR2f_if_port);
    return ret;
}

// vxlan_tunnel_entry 二级索引扫描 ip_addr
int32_t vxlan_tunnel_entry_scan_by_ip_addr(struct connObj *t_conn, uint32_t vrid, char *pSrc_addr,
    uint32_t nSrc_addrLen, char *pDst_addr, uint32_t nDst_addrLen)
{
    int32_t ret;

    GmcStmtT *objQry_vxlan_tunnel_entry = NULL;
    ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_vxlan_tunnel_entry);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(vxlan_tunnel_entry_scan_by_key_create_obj);
    ret = GmcPrepareStmtByLabelName(objQry_vxlan_tunnel_entry, "vxlan_tunnel_entry", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "vxlan_tunnel_entry");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(vxlan_tunnel_entry_scan_by_key_create_obj);
    CHECK_OK_RET(ret, "db_create_vxlan_tunnel_entry_obj");

    ret = GmcSetIndexKeyValue(objQry_vxlan_tunnel_entry, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vrid");
    ret = GmcSetIndexKeyValue(objQry_vxlan_tunnel_entry, 1, GMC_DATATYPE_FIXED, &pSrc_addr, sizeof(pSrc_addr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: pSrc_addr");
    ret = GmcSetIndexKeyValue(objQry_vxlan_tunnel_entry, 2, GMC_DATATYPE_FIXED, &pDst_addr, sizeof(pDst_addr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: pDst_addr");
    ret = GmcSetIndexKeyName(objQry_vxlan_tunnel_entry, "ip_addr");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: ip_addr");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_vxlan_tunnel_entry;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    vxlan_tunnel_entry_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_vxlan_tunnel_entry);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        GmcFreeStmt(objQry_vxlan_tunnel_entry);
        return ret;
    }

    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        DB_START_TEST_CPU_CYCLES(vxlan_tunnel_entry_scan_by_key_db_root_iterator_next);
        ret = GmcFetch(objQry_vxlan_tunnel_entry, &isFinish);
        DB_STOP_TEST_CPU_CYCLES(vxlan_tunnel_entry_scan_by_key_db_root_iterator_next);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_vxlan_tunnel_entry);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_vxlan_tunnel_entry, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_vxlan_tunnel_entry);
            break;
        }
        tmpScanCnt++;
    }
    DB_START_TEST_CPU_CYCLES(vxlan_tunnel_entry_scan_by_key_release_obj);
    GmcFreeStmt(objQry_vxlan_tunnel_entry);
    DB_STOP_TEST_CPU_CYCLES(vxlan_tunnel_entry_scan_by_key_release_obj);
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// if_vlan 二级索引扫描 k_vlan_id     只读第一条
int32_t if_vlan_scan_by_k_vlan_id(struct connObj *t_conn, uint16_t vlan_id, uint32_t vr_id)
{
    int32_t ret;

    GmcStmtT *objQry_if_vlan = NULL;
    ret = BenchMarkAllocStmt(&(t_conn->connS), &objQry_if_vlan);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(objQry_if_vlan, "if_vlan", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if_vlan");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_if_vlan_obj");

    ret = GmcSetIndexKeyValue(objQry_if_vlan, 0, GMC_DATATYPE_UINT16, &vlan_id, sizeof(vlan_id));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vlan_id");
    ret = GmcSetIndexKeyValue(objQry_if_vlan, 1, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue: vr_id");
    ret = GmcSetIndexKeyName(objQry_if_vlan, "k_vlan_id");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName: k_vlan_id");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objQry_if_vlan;
    ctx.keyId = 1;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    if_vlan_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    ret = GmcExecute(objQry_if_vlan);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute.");
        GmcFreeStmt(objQry_if_vlan);
        return ret;
    }

    int32_t tmpScanCnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(objQry_if_vlan, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch index, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_if_vlan);
            return ret;
        }
        if (isFinish == true) {
            break;
        }

        CHECK_OK_RET(ret, "Scan table by hashcluster failed");
        ret = GmcGetVertexDeseri(objQry_if_vlan, &d);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcGetVertexDeseri failed, tmpScanCnt:%u .", tmpScanCnt);
            GmcFreeStmt(objQry_if_vlan);
            break;
        }
        tmpScanCnt++;
    }

    GmcFreeStmt(objQry_if_vlan);
    deSeriFreeDynMem(&ctx, true);
    return (tmpScanCnt > 0) ? tmpScanCnt : ret;
}

// l3mc_mfib 异步批量写64个 等待回调
AsyncUserDataT g_user_l3mc_mfib = {0};
int32_t l3mc_mfib_batch_async_w(struct connObj *t_conn, l3mc_mfib_struct_t *pData, uint32_t uDataCnt)
{
    int32_t ret = GMERR_OK;
    uint32_t j = 0;
    uint64_t dml_add_count = g_user_l3mc_mfib.totalNum;
    int op_sleep_times = 0;
    uint32_t sleepCount = 0;
    if (objBw_l3mc_mfib == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objBw_l3mc_mfib);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objBw_l3mc_mfib, "l3mc_mfib", GMC_OPERATION_INSERT);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_mfib");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mc_mfib_obj_specific_conn.");
    }

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objBw_l3mc_mfib;

    GmcSeriT s;
    l3mc_mfib_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connA, &batchOption, &batch);
    ret = GmcBatchBindStmt(batch, objBw_l3mc_mfib);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcBatchPrepare:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(objBw_l3mc_mfib);
        if (ret) {
            DB_LOG_ERROR(ret, "db_reset_l3mc_mfib_batch_object");
            break;
        }

        obj_struct.vrId = pData[i].vrId;
        obj_struct.vrfId = pData[i].vrfId;
        obj_struct.grpAddr = pData[i].grpAddr;
        obj_struct.srcAddr = pData[i].srcAddr;
        obj_struct.srcMaskLen = pData[i].srcMaskLen;
        obj_struct.fvrfId = pData[i].fvrfId;
        obj_struct.grpMaskLen = pData[i].grpMaskLen;
        obj_struct.reserve = pData[i].reserve;
        obj_struct.inIfIndex = pData[i].inIfIndex;
        obj_struct.entryType = pData[i].entryType;
        obj_struct.pathFlags = pData[i].pathFlags;
        obj_struct.isWaiting = pData[i].isWaiting;
        obj_struct.isOldExist = pData[i].isOldExist;
        obj_struct.vlanId = pData[i].vlanId;
        obj_struct.oldInIfIndex = pData[i].oldInIfIndex;
        obj_struct.oldEntryType = pData[i].oldEntryType;
        obj_struct.mcId = pData[i].mcId;
        obj_struct.mcidVersion = pData[i].mcidVersion;
        obj_struct.mfibVersion = pData[i].mfibVersion;
        obj_struct.rootAddr = pData[i].rootAddr;
        obj_struct.originInIfIndex = pData[i].originInIfIndex;
        obj_struct.appSrcPid = pData[i].appSrcPid;
        obj_struct.appVersion = pData[i].appVersion;
        obj_struct.smoothVersion = pData[i].smoothVersion;
        obj_struct.hppErrCode = pData[i].hppErrCode;
        obj_struct.enpErrCode = pData[i].enpErrCode;
        obj_struct.timeStampCreate = pData[i].timeStampCreate;
        obj_struct.timeStampSmooth = pData[i].timeStampSmooth;

        ret = GmcSetVertexWithBuf(objBw_l3mc_mfib, &s);
        if (ret) {
            DB_LOG_ERROR(ret, "obj_set_func");
            break;
        }

        ret = GmcBatchAddDML(batch, objBw_l3mc_mfib);
        if (ret) {
            DB_LOG_ERROR(ret, "db_batch_add_oper");
            j = 0;
            break;
        }

        dml_add_count++;
        sleepCount = 0;
        if (j >= wr_loop || i >= end) {
        exe_batch_again:
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback_new, &g_user_l3mc_mfib);
            if (ret) {
                if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                    printf("### async quene full, usleep 300.");
                    (void)usleep(300);
                    op_sleep_times++;
                    sleepCount++;
                    if (sleepCount * 200 > 10000000) {
                        DB_LOG_ERROR(ret, "replace_async, sleep over 10 seconds");
                        return ret;
                    }
                    goto exe_batch_again;
                } else {
                    const char *lastErrInfo = NULL;
                    lastErrInfo = GmcGetLastError();
                    DB_LOG_ERROR(ret, "GmcReplaceVertexAsync index:%llu error:%s.", i, lastErrInfo);
                    return ret;
                }
            }

            if (i >= end) {
                break;
            }

            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }

    int waitCnt = 0;
    int old_sum = 0;
    int new_sum = 0;
    while (dml_add_count != g_user_l3mc_mfib.totalNum) {
        usleep(1000);
        waitCnt++;
        if (waitCnt == 1) {
            old_sum = g_user_l3mc_mfib.totalNum;
            new_sum = g_user_l3mc_mfib.totalNum;
        }
        if ((waitCnt * 100) % 2000000 == 0) {
            // printf("thrad index: %u batch_replace_async expect: %llu received: %d waited %d seconds.", 0,
            // dml_add_count, g_user_l3mc_mfib.totalNum, (waitCnt * 100) / 1000000);
        }
        if (waitCnt * 100 > 10000000) {
            DB_LOG_ERROR(ret,
                "thrad index: %u sleep 10s to wait, batch_replace_async expect: %llu received: %d sleep times: %d.", 0,
                dml_add_count, g_user_l3mc_mfib.totalNum, op_sleep_times);
            break;
        }
    }
    return ret;
}

// l3mcvlan_elb 异步批量写 缓存了object 等待
AsyncUserDataT g_user_vlan = {0};
int32_t l3mcvlan_elb_batch_async_w(struct connObj *t_conn, l3mcvlan_elb_struct_t *pData, uint32_t uDataCnt)
{
    int32_t ret = GMERR_OK;
    uint32_t j = 0;
    uint64_t dml_add_count = g_user_vlan.totalNum;
    int op_sleep_times = 0;
    uint32_t sleepCount = 0;
    if (objBw_l3mcvlan_elb == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objBw_l3mcvlan_elb);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objBw_l3mcvlan_elb, "l3mcvlan_elb", GMC_OPERATION_INSERT);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mcvlan_elb");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mcvlan_elb_obj_specific_conn.");
    }

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objBw_l3mcvlan_elb;

    GmcSeriT s;
    l3mcvlan_elb_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connA, &batchOption, &batch);
    ret = GmcBatchBindStmt(batch, objBw_l3mcvlan_elb);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcBatchPrepare:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(objBw_l3mcvlan_elb);
        if (ret) {
            DB_LOG_ERROR(ret, "db_reset_l3mcvlan_elb_batch_object");
            break;
        }

        obj_struct.vrIndex = pData[i].vrIndex;
        obj_struct.vrfIndex = pData[i].vrfIndex;
        obj_struct.grpAddr = pData[i].grpAddr;
        obj_struct.srcAddr = pData[i].srcAddr;
        obj_struct.ifIndex = pData[i].ifIndex;
        obj_struct.vlanId = pData[i].vlanId;
        obj_struct.ifType = pData[i].ifType;
        obj_struct.mcid = pData[i].mcid;
        obj_struct.mcidVersion = pData[i].mcidVersion;
        obj_struct.mfibVersion = pData[i].mfibVersion;
        obj_struct.mlagRole = pData[i].mlagRole;
        obj_struct.isMlagDual = pData[i].isMlagDual;
        obj_struct.isInIfElb = pData[i].isInIfElb;
        obj_struct.inIfhasMlag = pData[i].inIfhasMlag;
        obj_struct.appSrcPid = pData[i].appSrcPid;
        obj_struct.smoothVersion = pData[i].smoothVersion;
        obj_struct.pathFlags = pData[i].pathFlags;
        memcpy_s(obj_struct.svcCtxHighPrio, DB_L3MCVLAN_ELB_SVCCTXHIGHPRIO_LEN, pData[i].svcCtxHighPrio,
            DB_L3MCVLAN_ELB_SVCCTXHIGHPRIO_LEN);
        memcpy_s(obj_struct.svcCtxNormalPrio, DB_L3MCVLAN_ELB_SVCCTXNORMALPRIO_LEN, pData[i].svcCtxNormalPrio,
            DB_L3MCVLAN_ELB_SVCCTXNORMALPRIO_LEN);
        memcpy_s(obj_struct.serviceStatus, DB_L3MCVLAN_ELB_SERVICESTATUS_LEN, pData[i].serviceStatus,
            DB_L3MCVLAN_ELB_SERVICESTATUS_LEN);
        memcpy_s(obj_struct.errCode, DB_L3MCVLAN_ELB_ERRCODE_LEN, pData[i].errCode, DB_L3MCVLAN_ELB_ERRCODE_LEN);
        obj_struct.timeStampCreate = pData[i].timeStampCreate;
        obj_struct.timeStampSmooth = pData[i].timeStampSmooth;
        obj_struct.isOutVxlan = pData[i].isOutVxlan;
        obj_struct.isSelfBoard = pData[i].isSelfBoard;
        memcpy_s(obj_struct.resv, DB_L3MCVLAN_ELB_RESV_LEN, pData[i].resv, DB_L3MCVLAN_ELB_RESV_LEN);

        ret = GmcSetVertexWithBuf(objBw_l3mcvlan_elb, &s);
        if (ret) {
            DB_LOG_ERROR(ret, "obj_set_func");
            break;
        }

        ret = GmcBatchAddDML(batch, objBw_l3mcvlan_elb);
        if (ret) {
            DB_LOG_ERROR(ret, "db_batch_add_oper");
            j = 0;
            break;
        }

        dml_add_count++;
        sleepCount = 0;
        if (j >= wr_loop || i >= end) {
        exe_batch_again:
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback_new, &g_user_vlan);
            if (ret) {
                if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                    printf("### async quene full, usleep 300.");
                    (void)usleep(300);
                    op_sleep_times++;
                    sleepCount++;
                    if (sleepCount * 200 > 10000000) {
                        DB_LOG_ERROR(ret, "replace_async, sleep over 10 seconds");
                        return ret;
                    }
                    goto exe_batch_again;
                } else {
                    const char *lastErrInfo = NULL;
                    lastErrInfo = GmcGetLastError();
                    DB_LOG_ERROR(ret, "GmcReplaceVertexAsync index:%llu error:%s.", i, lastErrInfo);
                    return ret;
                }
            }

            if (i >= end) {
                break;
            }

            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }

    int waitCnt = 0;
    int old_sum = 0;
    int new_sum = 0;
    while (dml_add_count != g_user_vlan.totalNum) {
        usleep(1000);
        waitCnt++;
        if (waitCnt == 1) {
            old_sum = g_user_vlan.totalNum;
            new_sum = g_user_vlan.totalNum;
        }
        if ((waitCnt * 100) % 2000000 == 0) {
            // printf("thrad index: %u batch_replace_async expect: %llu received: %d waited %d seconds.", 0,
            // dml_add_count, g_user_vlan.totalNum, (waitCnt * 100) / 1000000);
        }
        if (waitCnt * 100 > 10000000) {
            DB_LOG_ERROR(ret,
                "thrad index: %u sleep 10s to wait, batch_replace_async expect: %llu received: %d sleep times: %d.", 0,
                dml_add_count, g_user_vlan.totalNum, op_sleep_times);
            break;
        }
    }
    return ret;
}

// l3mc_elb 异步批量批写100个 缓存了object
AsyncUserDataT g_user_elb = {0};
int32_t l3mc_elb_batch_insert_async_w(struct connObj *t_conn, l3mc_elb_struct_t *pData, uint32_t uDataCnt)
{
    int32_t ret = GMERR_OK;
    uint32_t j = 0;
    uint64_t dml_add_count = g_user_elb.totalNum;
    int op_sleep_times = 0;
    uint32_t sleepCount = 0;
    if (objBw_l3mc_elb == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objBw_l3mc_elb);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objBw_l3mc_elb, "l3mc_elb", GMC_OPERATION_INSERT);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_elb");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mc_elb_obj_specific_conn.");
    }

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objBw_l3mc_elb;

    GmcSeriT s;
    l3mc_elb_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connA, &batchOption, &batch);
    ret = GmcBatchBindStmt(batch, objBw_l3mc_elb);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcBatchPrepare:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(objBw_l3mc_elb);
        if (ret) {
            DB_LOG_ERROR(ret, "db_reset_l3mc_elb_batch_object");
            break;
        }

        obj_struct.vrId = pData[i].vrId;
        obj_struct.vrfId = pData[i].vrfId;
        obj_struct.grpAddr = pData[i].grpAddr;
        obj_struct.srcAddr = pData[i].srcAddr;
        obj_struct.outIfIndex = pData[i].outIfIndex;
        obj_struct.srcType = pData[i].srcType;
        memcpy_s(obj_struct.resv1, DB_L3MC_ELB_RESV1_LEN, pData[i].resv1, DB_L3MC_ELB_RESV1_LEN);
        obj_struct.vlanId = pData[i].vlanId;
        obj_struct.fwdIfType = pData[i].fwdIfType;
        obj_struct.ifType = pData[i].ifType;
        obj_struct.pathFlags = pData[i].pathFlags;
        memcpy_s(obj_struct.svcCtxHighPrio, DB_L3MC_ELB_SVCCTXHIGHPRIO_LEN, pData[i].svcCtxHighPrio,
            DB_L3MC_ELB_SVCCTXHIGHPRIO_LEN);
        memcpy_s(obj_struct.svcCtxNormalPrio, DB_L3MC_ELB_SVCCTXNORMALPRIO_LEN, pData[i].svcCtxNormalPrio,
            DB_L3MC_ELB_SVCCTXNORMALPRIO_LEN);
        obj_struct.appSrcPid = pData[i].appSrcPid;
        obj_struct.appVersion = pData[i].appVersion;
        obj_struct.mcidVersion = pData[i].mcidVersion;
        obj_struct.mfibVersion = pData[i].mfibVersion;
        obj_struct.mcId = pData[i].mcId;
        obj_struct.smoothVersion = pData[i].smoothVersion;
        memcpy_s(obj_struct.serviceStatus, DB_L3MC_ELB_SERVICESTATUS_LEN, pData[i].serviceStatus,
            DB_L3MC_ELB_SERVICESTATUS_LEN);
        memcpy_s(obj_struct.errCode, DB_L3MC_ELB_ERRCODE_LEN, pData[i].errCode, DB_L3MC_ELB_ERRCODE_LEN);
        obj_struct.timeStampCreate = pData[i].timeStampCreate;
        obj_struct.timeStampSmooth = pData[i].timeStampSmooth;
        obj_struct.igmpSnoopingEnable = pData[i].igmpSnoopingEnable;
        obj_struct.isOutVxlan = pData[i].isOutVxlan;
        obj_struct.isSelfBoard = pData[i].isSelfBoard;
        memcpy_s(obj_struct.resv2, DB_L3MC_ELB_RESV2_LEN, pData[i].resv2, DB_L3MC_ELB_RESV2_LEN);

        ret = GmcSetVertexWithBuf(objBw_l3mc_elb, &s);
        if (ret) {
            DB_LOG_ERROR(ret, "obj_set_func");
            break;
        }

        ret = GmcBatchAddDML(batch, objBw_l3mc_elb);
        if (ret) {
            DB_LOG_ERROR(ret, "db_batch_add_oper");
            j = 0;
            break;
        }

        dml_add_count++;
        sleepCount = 0;
        if (j >= wr_loop || i >= end) {
        exe_batch_again:
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback_new, &g_user_elb);
            if (ret) {
                if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                    printf("### async quene full, usleep 300.");
                    (void)usleep(300);
                    op_sleep_times++;
                    sleepCount++;
                    if (sleepCount * 200 > 10000000) {
                        DB_LOG_ERROR(ret, "replace_async, sleep over 10 seconds");
                        return ret;
                    }
                    goto exe_batch_again;
                } else {
                    const char *lastErrInfo = NULL;
                    lastErrInfo = GmcGetLastError();
                    DB_LOG_ERROR(ret, "GmcReplaceVertexAsync index:%llu error:%s.", i, lastErrInfo);
                    return ret;
                }
            }

            if (i >= end) {
                break;
            }

            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }

    int waitCnt = 0;
    int old_sum = 0;
    int new_sum = 0;
    while (dml_add_count != g_user_elb.totalNum) {
        usleep(1000);
        waitCnt++;
        if (waitCnt == 1) {
            old_sum = g_user_elb.totalNum;
            new_sum = g_user_elb.totalNum;
        }
        if ((waitCnt * 100) % 2000000 == 0) {
            // printf("thrad index: %u batch_replace_async expect: %llu received: %d waited %d seconds.", 0,
            // dml_add_count, g_user_elb.totalNum, (waitCnt * 100) / 1000000);
        }
        if (waitCnt * 100 > 10000000) {
            DB_LOG_ERROR(ret,
                "thrad index: %u sleep 10s to wait, batch_replace_async expect: %llu received: %d sleep times: %d.", 0,
                dml_add_count, g_user_elb.totalNum, op_sleep_times);
            break;
        }
    }
    return ret;
}

// l3mc_elb_sub 异步批量写 缓存了object 等待
AsyncUserDataT g_user_sub = {0};
int32_t l3mc_elb_sub_batch_insert_async_w(struct connObj *t_conn, l3mc_elb_sub_struct_t *pData, uint32_t uDataCnt)
{
    int32_t ret = GMERR_OK;
    uint32_t j = 0;
    uint64_t dml_add_count = g_user_sub.totalNum;
    int op_sleep_times = 0;
    uint32_t sleepCount = 0;
    if (objBw_l3mc_elb_sub == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objBw_l3mc_elb_sub);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objBw_l3mc_elb_sub, "l3mc_elb_sub", GMC_OPERATION_INSERT);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_elb_sub");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mc_elb_sub_obj_specific_conn.");
    }

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = objBw_l3mc_elb_sub;

    GmcSeriT s;
    l3mc_elb_sub_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connA, &batchOption, &batch);
    ret = GmcBatchBindStmt(batch, objBw_l3mc_elb_sub);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcBatchPrepare:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(objBw_l3mc_elb_sub);
        if (ret) {
            DB_LOG_ERROR(ret, "db_reset_l3mc_elb_sub_batch_object");
            break;
        }

        obj_struct.vrId = pData[i].vrId;
        obj_struct.vrfId = pData[i].vrfId;
        obj_struct.grpAddr = pData[i].grpAddr;
        obj_struct.srcAddr = pData[i].srcAddr;
        obj_struct.vlanId = pData[i].vlanId;
        obj_struct.portIfIndex = pData[i].portIfIndex;
        obj_struct.srcType = pData[i].srcType;
        obj_struct.isSelfBoard = pData[i].isSelfBoard;
        memcpy_s(obj_struct.resv, DB_L3MC_ELB_SUB_RESV_LEN, pData[i].resv, DB_L3MC_ELB_SUB_RESV_LEN);
        obj_struct.outIfIndex = pData[i].outIfIndex;
        obj_struct.pathFlags = pData[i].pathFlags;
        memcpy_s(obj_struct.svcCtxHighPrio, DB_L3MC_ELB_SUB_SVCCTXHIGHPRIO_LEN, pData[i].svcCtxHighPrio,
            DB_L3MC_ELB_SUB_SVCCTXHIGHPRIO_LEN);
        memcpy_s(obj_struct.svcCtxNormalPrio, DB_L3MC_ELB_SUB_SVCCTXNORMALPRIO_LEN, pData[i].svcCtxNormalPrio,
            DB_L3MC_ELB_SUB_SVCCTXNORMALPRIO_LEN);
        obj_struct.mcidVersion = pData[i].mcidVersion;
        obj_struct.mfibVersion = pData[i].mfibVersion;
        obj_struct.mcId = pData[i].mcId;
        obj_struct.ifType = pData[i].ifType;
        obj_struct.appVersion = pData[i].appVersion;
        obj_struct.smoothVersion = pData[i].smoothVersion;
        memcpy_s(obj_struct.serviceStatus, DB_L3MC_ELB_SUB_SERVICESTATUS_LEN, pData[i].serviceStatus,
            DB_L3MC_ELB_SUB_SERVICESTATUS_LEN);
        memcpy_s(obj_struct.errCode, DB_L3MC_ELB_SUB_ERRCODE_LEN, pData[i].errCode, DB_L3MC_ELB_SUB_ERRCODE_LEN);
        obj_struct.timeStampCreate = pData[i].timeStampCreate;
        obj_struct.timeStampSmooth = pData[i].timeStampSmooth;

        ret = GmcSetVertexWithBuf(objBw_l3mc_elb_sub, &s);
        if (ret) {
            DB_LOG_ERROR(ret, "obj_set_func");
            break;
        }

        ret = GmcBatchAddDML(batch, objBw_l3mc_elb_sub);
        if (ret) {
            DB_LOG_ERROR(ret, "db_batch_add_oper");
            j = 0;
            break;
        }

        dml_add_count++;
        sleepCount = 0;
        if (j >= wr_loop || i >= end) {
        exe_batch_again:
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback_new, &g_user_sub);
            if (ret) {
                if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                    printf("### async quene full, usleep 300.");
                    (void)usleep(300);
                    op_sleep_times++;
                    sleepCount++;
                    if (sleepCount * 200 > 10000000) {
                        DB_LOG_ERROR(ret, "replace_async, sleep over 10 seconds");
                        return ret;
                    }
                    goto exe_batch_again;
                } else {
                    const char *lastErrInfo = NULL;
                    lastErrInfo = GmcGetLastError();
                    DB_LOG_ERROR(ret, "GmcReplaceVertexAsync index:%llu error:%s.", i, lastErrInfo);
                    return ret;
                }
            }

            if (i >= end) {
                break;
            }

            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }

    int waitCnt = 0;
    int old_sum = 0;
    int new_sum = 0;
    while (dml_add_count != g_user_sub.totalNum) {
        usleep(1000);
        waitCnt++;
        if (waitCnt == 1) {
            old_sum = g_user_sub.totalNum;
            new_sum = g_user_sub.totalNum;
        }
        if ((waitCnt * 100) % 2000000 == 0) {
            // printf("thrad index: %u batch_replace_async expect: %llu received: %d waited %d seconds.", 0,
            // dml_add_count, g_user_sub.totalNum, (waitCnt * 100) / 1000000);
        }
        if (waitCnt * 100 > 10000000) {
            DB_LOG_ERROR(ret,
                "thrad index: %u sleep 10s to wait, batch_replace_async expect: %llu received: %d sleep times: %d.", 0,
                dml_add_count, g_user_sub.totalNum, op_sleep_times);
            break;
        }
    }
    return ret;
}

// l3mc_mfib 异步批量删除 64个 等回调
AsyncUserDataT g_l3mc_del = {0};
int32_t l3mc_mfib_batch_del_async_w(struct connObj *t_conn, l3mc_mfib_struct_t *pData, uint32_t uDataCnt)
{
    int32_t ret = GMERR_OK;
    uint32_t j = 0;
    uint64_t dml_add_count = g_l3mc_del.totalNum;
    int op_sleep_times = 0;
    uint32_t sleepCount = 0;
    if (objBdel_l3mc_mfib == NULL) {
        ret = BenchMarkAllocStmt(&(t_conn->connS), &objBdel_l3mc_mfib);
        if (ret != GMERR_OK) {
            printf("ERROR! gmserverConnect failed.\n");
            return GMERR_CONNECTION_FAILURE;
        }
        ret = GmcPrepareStmtByLabelName(objBdel_l3mc_mfib, "l3mc_mfib", GMC_OPERATION_DELETE);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "l3mc_mfib");
            return ret;
        }
        CHECK_OK_RET(ret, "db_create_l3mc_elb_sub_obj_specific_conn.");
    }

    uint32_t wr_loop = uDataCnt;
    uint32_t end = uDataCnt - 1;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ret = GmcBatchPrepare(t_conn->connA, &batchOption, &batch);
    ret = GmcBatchBindStmt(batch, objBdel_l3mc_mfib);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcBatchPrepare:%s failed.", "arp");
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(objBdel_l3mc_mfib);
        if (ret) {
            DB_LOG_ERROR(ret, "db_reset_l3mc_mfib_batch_object");
            break;
        }

        ret = GmcSetIndexKeyValue(objBdel_l3mc_mfib, 0, GMC_DATATYPE_UINT32, &(pData[i].vrId), sizeof(pData[i].vrId));
        CHECK_OK_RET(ret, "set index key value");
        ret = GmcSetIndexKeyValue(objBdel_l3mc_mfib, 1, GMC_DATATYPE_UINT32, &(pData[i].vrfId), sizeof(pData[i].vrfId));
        CHECK_OK_RET(ret, "set index key value");
        ret = GmcSetIndexKeyValue(
            objBdel_l3mc_mfib, 2, GMC_DATATYPE_UINT32, &(pData[i].grpAddr), sizeof(pData[i].grpAddr));
        CHECK_OK_RET(ret, "set index key value");
        ret = GmcSetIndexKeyValue(
            objBdel_l3mc_mfib, 3, GMC_DATATYPE_UINT32, &(pData[i].srcAddr), sizeof(pData[i].srcAddr));
        CHECK_OK_RET(ret, "set index key value");

        ret = GmcSetIndexKeyName(objBdel_l3mc_mfib, "primary_key");
        CHECK_OK_RET(ret, "set index key name");

        ret = GmcExecute(objBdel_l3mc_mfib);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcExecute failed");
            return ret;
        }

        ret = GmcBatchAddDML(batch, objBdel_l3mc_mfib);
        if (ret) {
            DB_LOG_ERROR(ret, "db_batch_add_oper");
            j = 0;
            break;
        }

        dml_add_count++;
        sleepCount = 0;
        if (j >= wr_loop || i >= end) {
        exe_batch_again:
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback_new, &g_l3mc_del);
            if (ret) {
                if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                    printf("### async quene full, usleep 300.");
                    (void)usleep(300);
                    op_sleep_times++;
                    sleepCount++;
                    if (sleepCount * 200 > 10000000) {
                        DB_LOG_ERROR(ret, "replace_async, sleep over 10 seconds");
                        return ret;
                    }
                    goto exe_batch_again;
                } else {
                    const char *lastErrInfo = NULL;
                    lastErrInfo = GmcGetLastError();
                    DB_LOG_ERROR(ret, "GmcReplaceVertexAsync index:%llu error:%s.", i, lastErrInfo);
                    return ret;
                }
            }

            if (i >= end) {
                break;
            }
            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }

    int waitCnt = 0;
    int old_sum = 0;
    int new_sum = 0;
    while (dml_add_count != g_l3mc_del.totalNum) {
        usleep(1000);
        waitCnt++;
        if (waitCnt == 1) {
            old_sum = g_l3mc_del.totalNum;
            new_sum = g_l3mc_del.totalNum;
        }
        if ((waitCnt * 100) % 2000000 == 0) {
            // printf("thrad index: %u batch_replace_async expect: %llu received: %d waited %d seconds.", 0,
            // dml_add_count,g_l3mc_del.totalNum, (waitCnt * 100) / 1000000);
        }
        if (waitCnt * 100 > 10000000) {
            DB_LOG_ERROR(ret,
                "thrad index: %u sleep 10s to wait, batch_replace_async expect: %llu received: %d sleep times: %d.", 0,
                dml_add_count, g_l3mc_del.totalNum, op_sleep_times);
            break;
        }
    }
    return ret;
}

#define DB_VLAN_CFG_VLAN_NAME_LEN 32
#define DB_VLAN_CFG_DESCRIPTION_LEN 64
#define DB_CFG_BD_BIND_VLAN_RSV_LEN 2

/* All length macro define as following */
#define DB_IF_CFG_NAME_LEN 64
#define DB_IF_CFG_MAC_ADDRESS_LEN 6

/* All length macro define as following */
#define DB_CIB_NAC_DATA_STRDSAUTHENSVR_LEN 16
#define DB_CIB_NAC_DATA_SZACCTSESSIONID_LEN 64
#define DB_CIB_NAC_DATA_SZUSERMAC_LEN 6
#define DB_CIB_NAC_DATA_SZLOCALMAC_LEN 6
#define DB_CIB_NAC_DATA_SZSYSMAC_LEN 6

#define DB_STCMASINFO_SZASMAC_LEN 6

#define DB_STWLANINFO_SZAPMAC_LEN 6
#define DB_STWLANINFO_STAUTHACIP_STIPADDR_LEN 16
#define DB_STWLANINFO_STPEERACIP_STIPADDR_LEN 16
#define DB_STDEVICEINFO_SZDEVICEMAC_LEN 6

// ip4forward
#define IP4_MAX_MASK_LEN_16 1000
#define IP4_MAX_MASK_LEN_24 2501000

#pragma pack(1)
typedef struct tagcfg_bd_bind_vlan_struct_t {
    uint32_t vr_id;
    uint32_t bd_id;
    uint16_t vlan_id;
    int8_t rsv[DB_CFG_BD_BIND_VLAN_RSV_LEN];
    uint32_t if_index;
    uint32_t ver_no;
} cfg_bd_bind_vlan_struct_t;
#pragma pack()

/* Struct definition of if_cfg */
#pragma pack(1)
typedef struct tagif_cfg_struct_t {
    uint32_t ifindex;
    int8_t name[DB_IF_CFG_NAME_LEN];
    uint32_t vrid;
    uint32_t if_type;
    uint32_t shutdown;
    uint32_t port_switch;
    uint32_t forward_type;
    int8_t mac_address[DB_IF_CFG_MAC_ADDRESS_LEN];
    uint16_t ipv4_mtu;
    uint16_t ipv6_mtu;
    uint32_t on_board;
    uint32_t lagid;
    uint32_t main_ifindex;
    uint32_t error_down;
    uint32_t dev_id;
    uint32_t chassis_id;
    uint32_t slot_id;
    uint32_t card_id;
    uint32_t unit_id;
    uint32_t port_id;
    uint32_t trunk_id;
    uint32_t vlan_id;
    uint32_t l2_portindex;
    uint32_t vsi;
    uint32_t tunnel_id;
    uint32_t field_flags;
} if_cfg_struct_t;
#pragma pack()

/* Struct definition of cib_nac_data */
#pragma pack(1)
typedef struct tagcib_nac_data_struct_t {
    uint32_t ulCID;
    uint32_t ulCidPrefix;
    uint32_t ulMagic;
    uint32_t ulVsysID;
    uint32_t ulVrID;
    uint32_t ulTaskID;
    uint8_t ucState;
    uint8_t ucSubState;
    uint8_t ucTableType;
    uint8_t ucAccessType;
    uint8_t ucTriggerType;
    uint8_t ucUserType;
    uint8_t ucIsV6User;
    uint8_t ucIfModifyAccNum;
    uint8_t ucSendLogInFlag;
    uint32_t ulAccessTime;
    uint32_t lAccessTime;
    uint16_t usPort;
    uint32_t ulPortType;
    uint32_t ulInterface;
    uint32_t ulLogicInterface;
    uint32_t ulPortIndex;
    uint32_t ulL2Interface;
    uint8_t ucSlot;
    uint8_t ucPic;
    uint16_t usMTU;
    uint32_t ulSrcNodeID;
    uint32_t ulSrcChnlID;
    uint32_t ulAccessModIndex;
    uint32_t ulAuthModIndex;
    uint32_t ulIpAddr;
    uint32_t ulVrfIndex;
    uint32_t ulGateWay;
    uint32_t ulMask;
    uint32_t ulAuthProfileIndex;
    uint8_t ucPortalLayerFlag;
    uint8_t ucWlanFlag;
    uint8_t ucIsETrunkAccess;
    uint8_t ucAuthFailAccessType;
    uint8_t waitMsgType;
    uint8_t ucResponeSuccessToEAPFlag;
    uint8_t ucResponeSuccessToWEBFlag;
    uint32_t ulRdsAuthenSvrVrf;
    uint16_t usDomainIndex;
    uint16_t usServiceProfile;
    uint16_t usRDSTemplateID;
    uint8_t ucAuthType;
    uint8_t ucAuthedPlace;
    uint32_t uiTACTemplateID;
    uint32_t uiLDAPTemplateID;
    uint32_t uiADTemplateID;
    uint8_t ucAuthoredPlace;
    uint8_t ucAuthenType;
    uint8_t ucAAASuccFlag;
    uint8_t ucSubMsgType;
    int8_t stRdsAuthenSvr[DB_CIB_NAC_DATA_STRDSAUTHENSVR_LEN];
    uint8_t ucSubAuthenCode;
    uint8_t ucPreAuthenCode;
    uint32_t ulAAAAuthType;
    int8_t szAcctSessionID[DB_CIB_NAC_DATA_SZACCTSESSIONID_LEN];
    uint32_t ulVlan;
    uint16_t usActionFlag;
    uint32_t ulAttrFlag;
    uint8_t bLeavingFlagAAA;
    uint8_t bLeavingFlagWEB;
    uint8_t bLeavingFlagFC;
    uint8_t bLeavingFlagDHCPR;
    uint8_t bLeavingFlagEAPOL;
    uint8_t bLeavingFlagWLAN;
    uint8_t bLeavingFlagAdmin;
    uint8_t bLeavingFlagPPP;
    uint8_t bLeavingFlagSrcMod;
    int8_t szUserMac[DB_CIB_NAC_DATA_SZUSERMAC_LEN];
    uint8_t ucDelRetryTimes;
    uint8_t ucGateWayFlag;
    uint8_t ucUpFlag;
    uint8_t ucPreAcctMethod;
    uint8_t ucPreAuthPlace;
    uint8_t ucBatchFlag;
    uint8_t ucBakModifyFlog;
    uint8_t ucBakState;
    uint32_t ulVlanifIndex;
    uint8_t is_per_user_hash_added;
    uint8_t ucLocalAuthorType;
    uint32_t original_ulVlan;
    uint32_t ulMagicNumber;
    uint8_t ucUserNameAccountFlag;
    uint16_t usSqId;
    uint8_t ucAccessMode;
    uint32_t ulModifyTriggerBmp;
    uint32_t ulAuthorModifyBitmap;
    uint8_t ucAuthVlanType;
    uint16_t usAuthVlan;
    uint8_t ucUserVlanSource;
    uint8_t ucWebMangerUserFlag;
    uint8_t ucIsForceDomainFlag;
    int32_t lIdleSec;
    uint8_t ucBakUserFlag;
    uint8_t ucBakNeedAddFlag;
    uint32_t ulBakRandom;
    uint32_t ulUserNameMaxNum;
    uint8_t ucAclOKV6;
    uint8_t ucIfHasProcAdpAuthor;
    uint8_t ucNotProcFcFlag;
    uint8_t isVoiceDev;
    uint8_t isdot1xmacReAuth;
    uint32_t ulAcctSessionTime;
    uint8_t ucIpConflictCheck;
    uint8_t ucPreAuthFlag;
    uint8_t ucIsPortBaseAuthenMac;
    uint8_t ucHaveUpdateCounter;
    uint32_t ulReauthTimeLen;
    uint32_t ulHacaServerIp;
    uint8_t ucOpenUser;
    uint8_t ucStaticUserFlag;
    uint8_t ucIsVoiceTerminal;
    uint8_t ucISPFlag;
    uint8_t ucLeaseFlag;
    uint32_t ulDnsIp1;
    uint32_t ulDnsIp2;
    uint32_t ulWinsIP1;
    uint32_t ulWinsIP2;
    uint8_t ucUserGroupChanged;
    uint8_t ucHttpFwdFlg;
    uint32_t ulSessionStartTime;
    uint8_t ucTermAct;
    uint8_t ucPushFlag;
    uint8_t ucAuthorizeType;
    uint8_t ucUnRestartTimerFlag;
    uint32_t ulEAPSessionTime;
    uint8_t ucVlanType;
    uint16_t usISPvlan;
    uint16_t usVLAN;
    uint16_t usGroupID;
    uint16_t usUclGroupId;
    uint8_t ucInnerIsolated;
    uint8_t ucInterIsolated;
    uint32_t ulMacLimit;
    uint32_t ulSessionTimeOut;
    uint8_t ucPriority;
    uint8_t ucDownPriority;
    uint8_t ucInDscpValue;
    uint8_t ucIn8021pValue;
    uint8_t ucInExpValue;
    uint8_t ucInLPValue;
    uint8_t ucOutDscpValue;
    uint8_t ucOut8021pValue;
    uint8_t ucOutExpValue;
    uint8_t ucOutLPValue;
    uint8_t ucIsSupportBandWidth;
    uint8_t ucBandWidthSlot;
    uint32_t ulUpLinkbandwidth;
    uint32_t ulDownLinkBWCir;
    uint32_t ulDownLinkBWCbs;
    uint8_t ucHttpToCpuFlag;
    uint8_t ucHttpsToCpuFlag;
    uint8_t ucProtocolTpye;
    uint16_t usPortNum;
    uint8_t ucBandWidthShareMode;
    uint32_t ulForwardInterface;
    uint8_t ucDaaStaticEnable;
    uint8_t ucStaticsUpMap;
    uint8_t ucStaticsDownMap;
    uint8_t ucAcctMap;
    uint8_t ucUserUpFlowStaticsFlag;
    uint8_t ucUserDownFlowStaticsFlag;
    uint8_t ucFlowStatEnableFlag;
    uint8_t ucDscp;
    uint8_t ucAccoutingSeparate;
    uint8_t ucV6StaticsUpMap;
    uint8_t ucV6StaticsDownMap;
    uint32_t ulLocalAuthorBitMap;
    uint32_t ulRemoteAuthorBitMap;
    uint32_t stAuthorizeInfo_ulIpAddr;
    uint32_t stAuthorizeInfo_ulGateWay;
    uint32_t stAuthorizeInfo_ulMask;
    uint8_t ucIfRdsAcl;
    uint8_t ucServiceSchemeVoiceVlan;
    uint32_t userAddrNetwork_ulIP;
    uint32_t userAddrNetwork_ulMask;
    uint64_t duRemainFlow;
    uint16_t usRedirectAclId;
    uint8_t ucSessionTimeoutSource;
    uint8_t ucDot1xUrlFlag;
    uint8_t ucUserGroupPriority;
    uint8_t ucUpRemarkOK;
    uint8_t ucDnRemarkOK;
    uint16_t usSesssionID;
    uint8_t ucOptionLen;
    uint8_t ucDownQosOK;
    uint8_t ucDhcpAckFlag;
    uint8_t ucIfNeedDHCPAck;
    uint8_t ucIsIpStaticUserEnable;
    int8_t szLocalMac[DB_CIB_NAC_DATA_SZLOCALMAC_LEN];
    uint8_t ucIfVpdn;
    uint16_t usUserGroup;
    uint16_t usUclGroup;
    uint8_t ucIsHacaUserFlag;
    uint32_t ulIPSecAcl;
    uint8_t ucIPSecFlag;
    uint8_t ucSetGroupOk;
    uint8_t ucUpDscpOK;
    uint8_t ucDnDscpOK;
    int8_t szSysMac[DB_CIB_NAC_DATA_SZSYSMAC_LEN];
    uint8_t ucIsV6Acl;
    uint8_t ucIsPortBased;
    uint32_t patch_reserved;
} cib_nac_data_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagcib_nac_ext_data_stCmAsInfo_t {
    uint32_t ulASIP;
    uint8_t ucSlotNo;
    uint8_t ucPicNo;
    uint16_t usPortNo;
    uint32_t ulSubIndex;
    uint32_t ulAsIfIndex;
    int8_t szASMac[DB_STCMASINFO_SZASMAC_LEN];
    uint8_t ucIsRemoteUser;
    uint8_t ucRemoteSyncTimeoutCount;
    uint16_t usAsIfPvid;
    uint16_t szASPortName_len;
    int8_t *szASPortName;
} cib_nac_ext_data_stCmAsInfo_t;
#pragma pack()

/* Struct definition of stAcctData */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_stAcctData_t {
    uint32_t ulRTAcctInterval;
    uint8_t ucAcctState;
    uint8_t ucAcctMethod;
    uint8_t ucSTAcctPolicy;
    uint8_t ucRTAcctPolicy;
    uint8_t ucRTAcctFailNumPermit;
    uint8_t ucRTAcctFailTimes;
    uint8_t ucFlowAcctMode;
    uint8_t ucAccStart;
    uint8_t ucSendRTAcctFlag;
    uint8_t ucIsNeedModifyRTAcctFlag;
    uint16_t usNormalRdsTempletID;
    uint16_t usTwoLevelAcctRdsTempletID;
    uint16_t usNormalTACTempletID;
    uint8_t curServer_ucType;
    uint8_t curServer_ucTwoLevelType;
    uint16_t usHacaTempletID;
    uint16_t curServer_stIPAddress_len;
    int8_t *curServer_stIPAddress;
    uint16_t curServer_stTwoLevelIPAddress_len;
    int8_t *curServer_stTwoLevelIPAddress;
} cib_nac_ext_data_stAcctData_t;
#pragma pack()

/* Struct definition of stDataFlow */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_stDataFlow_t {
    uint64_t duUpLinkForwardBytes;
    uint64_t duUpLinkForwardPackets;
    uint64_t duDownLinkForwardBytes;
    uint64_t duDownLinkForwardPackets;
    uint64_t duUpLinkDropBytes;
    uint64_t duUpLinkDropPackets;
    uint64_t duDownLinkDropBytes;
    uint64_t duDownLinkDropPackets;
    uint64_t duV6UpLinkForwardBytes;
    uint64_t duV6UpLinkForwardPackets;
    uint64_t duV6DownLinkForwardBytes;
    uint64_t duV6DownLinkForwardPackets;
    uint64_t duV6UpLinkDropBytes;
    uint64_t duV6UpLinkDropPackets;
    uint64_t duV6DownLinkDropBytes;
    uint64_t duV6DownLinkDropPackets;
} cib_nac_ext_data_stDataFlow_t;
#pragma pack()

/* Struct definition of stCar */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_stCar_t {
    uint8_t inbound_ucCarFlag;
    uint32_t inbound_ulCir;
    uint32_t inbound_ulCbs;
    uint32_t inbound_ulPir;
    uint32_t inbound_ulPbs;
    uint8_t outbound_ucCarFlag;
    uint32_t outbound_ulCir;
    uint32_t outbound_ulCbs;
    uint32_t outbound_ulPir;
    uint32_t outbound_ulPbs;
} cib_nac_ext_data_stCar_t;
#pragma pack()

/* Struct definition of stUserDetect */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_stUserDetect_t {
    uint32_t ulDetectTimeLen;
    uint16_t usDetectTimes;
    uint8_t ucDetectType;
    uint8_t ucDetectUserState;
    uint8_t ucEapolHandShakeType;
    uint8_t ucPortalLayerFlag;
    uint8_t ucNeedModTimer;
    uint8_t ucArpProxyFlag;
    uint8_t ucArpCheckFlag;
    uint8_t ucEapPktId;
    uint8_t ucEapPause;
    uint32_t ulReFreshIpTime;
    uint8_t ucDetectFlagForMacMove;
    uint8_t ucStaticUserKeepOnlineFlag;
    uint16_t usDetectTimesForMacMove;
    uint32_t ulDetectTimeLenForMacMove;
    uint32_t ulDetectDelayTimeLen;
} cib_nac_ext_data_stUserDetect_t;
#pragma pack()

#pragma pack(1)
typedef struct tagcib_nac_ext_data_stDaaQosProfile_t {
    uint32_t ulStatEn;
    uint32_t upCarItem_ulCarFlag;
    uint32_t upCarItem_ulCir;
    uint32_t upCarItem_ulCbs;
    uint32_t upCarItem_ulPir;
    uint32_t upCarItem_ulPbs;
    uint32_t downCarItem_ulCarFlag;
    uint32_t downCarItem_ulCir;
    uint32_t downCarItem_ulCbs;
    uint32_t downCarItem_ulPir;
    uint32_t downCarItem_ulPbs;
    uint8_t remark_ucRemark8021p;
    uint8_t remark_uc8021p;
    uint8_t remark_ucRemarkDscp;
    uint8_t remark_ucInDscp;
    uint8_t remark_ucOutDscp;
    uint8_t userQueue_ucValid;
    uint32_t userQueue_ulPir;
    uint32_t userQueue_ulCir;
    uint16_t usFlowQueueProfileIndex;
    uint16_t usFlowMappingProfileIndex;
} cib_nac_ext_data_stDaaQosProfile_t;
#pragma pack()

#pragma pack(1)
typedef struct tagcib_nac_ext_data_stProfileUserQueue_t {
    uint8_t ucValid;
    uint32_t ulPir;
    uint32_t ulCir;
    uint16_t usFlowQueueProfileIndex;
    uint16_t usFlowMappingProfileIndex;
} cib_nac_ext_data_stProfileUserQueue_t;
#pragma pack()

/* Struct definition of stTunnel */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_stTunnel_t {
    uint8_t ucUsed;
    uint32_t ulTunnelType;
    uint32_t ulTunnelMediumType;
    uint32_t ulTunnelPreference;
    uint16_t szTunnelPrivateGroupID_len;
    int8_t *szTunnelPrivateGroupID;
    uint16_t szTunnelPassword_len;
    int8_t *szTunnelPassword;
    uint16_t szTunnelGroupName_len;
    int8_t *szTunnelGroupName;
    uint16_t szTunnelClientAuthID_len;
    int8_t *szTunnelClientAuthID;
    uint16_t szTunnelServerAuthID_len;
    int8_t *szTunnelServerAuthID;
    uint16_t szTunnelClientEndpointIp_len;
    int8_t *szTunnelClientEndpointIp;
    uint16_t szTunnelServerEndpointIp_len;
    int8_t *szTunnelServerEndpointIp;
    uint16_t szTunnelAssignmentID_len;
    int8_t *szTunnelAssignmentID;
} cib_nac_ext_data_stTunnel_t;
#pragma pack()

/* Struct definition of stIncreaseDataFlow */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_stIncreaseDataFlow_t {
    uint64_t duUpLinkForwardBytes;
    uint64_t duUpLinkForwardPackets;
    uint64_t duDownLinkForwardBytes;
    uint64_t duDownLinkForwardPackets;
    uint64_t duUpLinkDropBytes;
    uint64_t duUpLinkDropPackets;
    uint64_t duDownLinkDropBytes;
    uint64_t duDownLinkDropPackets;
    uint64_t duV6UpLinkForwardBytes;
    uint64_t duV6UpLinkForwardPackets;
    uint64_t duV6DownLinkForwardBytes;
    uint64_t duV6DownLinkForwardPackets;
    uint64_t duV6UpLinkDropBytes;
    uint64_t duV6UpLinkDropPackets;
    uint64_t duV6DownLinkDropBytes;
    uint64_t duV6DownLinkDropPackets;
} cib_nac_ext_data_stIncreaseDataFlow_t;
#pragma pack()

/* Struct definition of stIdleCut */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_stIdleCut_t {
    uint32_t ulIdleCutTime;
    uint32_t ulIdleCutLeftTime;
    uint32_t ulIdleCutFlow;
    uint64_t duIdleCutFlow;
    uint64_t duIdleCutLeftFlow;
    uint8_t ucIdleCutFlowDirection;
    uint8_t ucIdleCutSecond;
} cib_nac_ext_data_stIdleCut_t;
#pragma pack()

/* Struct definition of stWlanInfo */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_stWlanInfo_t {
    uint8_t ucWorkGroupID;
    uint32_t ulVapID;
    uint32_t ulVACNodeID;
    uint8_t ucVapHaveUpdateCounter;
    int8_t szApMac[DB_STWLANINFO_SZAPMAC_LEN];
    uint8_t ucRadioId;
    uint32_t ulApId;
    uint8_t ucIsFastRoam;
    uint32_t stAuthACIP_enIpVersion;
    int8_t stAuthACIP_stIpAddr[DB_STWLANINFO_STAUTHACIP_STIPADDR_LEN];
    uint32_t stPeerACIP_enIpVersion;
    int8_t stPeerACIP_stIpAddr[DB_STWLANINFO_STPEERACIP_STIPADDR_LEN];
    uint32_t ulFwdMode;
    uint16_t usServiceVlan;
    uint8_t ucWirelessAccessType;
    char cNiRssi;
    int32_t lTerminalPosX;
    int32_t lTerminalPosY;
    uint8_t ucProcApFlag;
    uint8_t ucInterRoamFlag;
    uint8_t ucAuthPointFlag;
    uint8_t ucAPAuthorFlag;
    uint8_t ucIfL3Roam;
    uint8_t ucIfSameCentralAP;
    uint16_t szApName_len;
    int8_t *szApName;
    uint16_t szSsidName_len;
    int8_t *szSsidName;
    uint16_t szInterRoamPreDomainBuf_len;
    int8_t *szInterRoamPreDomainBuf;
    uint16_t logExtendInfo_len;
    int8_t *logExtendInfo;
    uint16_t szPMK_len;
    int8_t *szPMK;
} cib_nac_ext_data_stWlanInfo_t;
#pragma pack()

#pragma pack(1)
typedef struct tagcib_nac_ext_data_stNacPPPInfo_t {
    uint32_t ulVTIfIndex;
    uint8_t ucNeedCutPPP;
    uint32_t ulMss;
    uint32_t ulPhyTxIf;
    uint16_t szCallingNumber_len;
    int8_t *szCallingNumber;
    uint16_t szCalledNumber_len;
    int8_t *szCalledNumber;
} cib_nac_ext_data_stNacPPPInfo_t;
#pragma pack()

#pragma pack(1)
typedef struct tagcib_nac_ext_data_stDeviceInfo_t {
    int8_t szDeviceMac[DB_STDEVICEINFO_SZDEVICEMAC_LEN];
    uint16_t stDhcpOption_len;
    int8_t *stDhcpOption;
    uint16_t szUAInfo_len;
    int8_t *szUAInfo;
    uint16_t szDeviceType_len;
    int8_t *szDeviceType;
} cib_nac_ext_data_stDeviceInfo_t;
#pragma pack()

#pragma pack(1)
typedef struct tagcib_nac_ext_data_stWebAuth_t {
    uint16_t usPortal2PreFlag;
    uint8_t ucPortalVer;
    uint8_t ucSyncUserExitTimes;
    uint8_t ucPortalSvrIndex;
    uint8_t ucBakPortalSvrIndex;
    uint32_t ulWebAuthServerIP;
    uint32_t ulWebAuthVrf;
    uint32_t ulWebServerSrcIP;
    uint8_t ucWebUserIPType;
    uint8_t ucIfWeChatAuth;
    uint16_t stIPv6AddrWebAuth_len;
    int8_t *stIPv6AddrWebAuth;
} cib_nac_ext_data_stWebAuth_t;
#pragma pack()

#pragma pack(1)
typedef struct tagcib_nac_ext_data_astAuthTrack_t {
    uint8_t ucAccessType;
    uint8_t ucFailTimes;
    uint8_t ucSuccFlag;
} cib_nac_ext_data_astAuthTrack_t;
#pragma pack()

#pragma pack(1)
typedef struct tagcib_nac_ext_data_stAuthSeq_t {
    uint16_t astAuthTrack_count;
    cib_nac_ext_data_astAuthTrack_t *astAuthTrack;
    uint8_t ucActiveIndex;
    uint8_t ucTypeFailCount;
} cib_nac_ext_data_stAuthSeq_t;
#pragma pack()

/* Struct definition of cib_nac_ext_data */
#pragma pack(1)
typedef struct tagcib_nac_ext_data_struct_t {
    uint32_t ulCID;
    uint32_t ulCidPrefix;
    uint32_t patch_reserved;
    uint16_t stCmAsInfo_flag;
    cib_nac_ext_data_stCmAsInfo_t *stCmAsInfo;
    uint16_t stAcctData_flag;
    cib_nac_ext_data_stAcctData_t *stAcctData;
    uint16_t szAcctMultiSessionId_len;
    int8_t *szAcctMultiSessionId;
    uint16_t stIPv6Addr_len;
    int8_t *stIPv6Addr;
    uint16_t stDataFlow_count;
    cib_nac_ext_data_stDataFlow_t *stDataFlow;
    uint16_t stUserDetect_flag;
    cib_nac_ext_data_stUserDetect_t *stUserDetect;
    uint16_t szUserNameBuf_len;
    int8_t *szUserNameBuf;
    uint16_t stCar_flag;
    cib_nac_ext_data_stCar_t *stCar;
    uint16_t szServiceSchemeName_len;
    int8_t *szServiceSchemeName;
    uint16_t usPool_len;
    int8_t *usPool;
    uint16_t szUserGroupName_len;
    int8_t *szUserGroupName;
    uint16_t szUpdateURL_len;
    int8_t *szUpdateURL;
    uint16_t szDNSDomainName_len;
    int8_t *szDNSDomainName;
    uint16_t szDaaQosProfileName_len;
    int8_t *szDaaQosProfileName;
    uint16_t stDaaQosProfile_count;
    cib_nac_ext_data_stDaaQosProfile_t *stDaaQosProfile;
    uint16_t stProfileUserQueue_flag;
    cib_nac_ext_data_stProfileUserQueue_t *stProfileUserQueue;
    uint16_t stTunnel_flag;
    cib_nac_ext_data_stTunnel_t *stTunnel;
    uint16_t szState_len;
    int8_t *szState;
    uint16_t stIncreaseDataFlow_count;
    cib_nac_ext_data_stIncreaseDataFlow_t *stIncreaseDataFlow;
    uint16_t stIdleCut_flag;
    cib_nac_ext_data_stIdleCut_t *stIdleCut;
    uint16_t szRedirectURL_len;
    int8_t *szRedirectURL;
    uint16_t stWlanInfo_flag;
    cib_nac_ext_data_stWlanInfo_t *stWlanInfo;
    uint16_t stWebAuth_flag;
    cib_nac_ext_data_stWebAuth_t *stWebAuth;
    uint16_t szUserClass_len;
    int8_t *szUserClass;
    uint16_t stAuthSeq_flag;
    cib_nac_ext_data_stAuthSeq_t *stAuthSeq;
    uint16_t usAclID_len;
    int8_t *usAclID;
    uint16_t usAclV6ID_len;
    int8_t *usAclV6ID;
    uint16_t pszAcl1_len;
    int8_t *pszAcl1;
    uint16_t pszAcl2_len;
    int8_t *pszAcl2;
    uint16_t szPushUrl_len;
    int8_t *szPushUrl;
    uint16_t stNacPPPInfo_flag;
    cib_nac_ext_data_stNacPPPInfo_t *stNacPPPInfo;
    uint16_t szOption82Buf_len;
    int8_t *szOption82Buf;
    uint16_t szOption60Buf_len;
    int8_t *szOption60Buf;
    uint16_t stSlotFlag_aulBitMap_len;
    int8_t *stSlotFlag_aulBitMap;
    uint16_t stLastSendSlotFlag_aulBitMap_len;
    int8_t *stLastSendSlotFlag_aulBitMap;
    uint16_t stDeviceInfo_flag;
    cib_nac_ext_data_stDeviceInfo_t *stDeviceInfo;
    uint16_t szAccessUserName_len;
    int8_t *szAccessUserName;
    uint16_t szSendKeyPMK_len;
    int8_t *szSendKeyPMK;
    uint16_t szChargeableIdentity_len;
    int8_t *szChargeableIdentity;
} cib_nac_ext_data_struct_t;
#pragma pack()

/* All length macro define as following */
#define DB_NHP_GROUP_ERRCODE_HIGH_PRIO_LEN 2
#define DB_NHP_GROUP_ERRCODE_NORMAL_PRIO_LEN 2
#define DB_NHP_GROUP_SVC_CTX_HIGH_PRIO_LEN 32
#define DB_NHP_GROUP_SVC_CTX_NORMAL_PRIO_LEN 32

#pragma pack(1)
typedef struct tagnhp_group_struct_t {
    uint32_t nhp_group_id;
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t flags;
    uint32_t ref_count;
    uint32_t new_vrf;
    uint16_t nhp_number;
    uint8_t nhp_type_high_prio;
    uint8_t nhp_type_normal_prio;
    uint8_t status_high_prio;
    uint8_t status_normal_prio;
    int8_t errcode_high_prio[DB_NHP_GROUP_ERRCODE_HIGH_PRIO_LEN];
    int8_t errcode_normal_prio[DB_NHP_GROUP_ERRCODE_NORMAL_PRIO_LEN];
    int8_t svc_ctx_high_prio[DB_NHP_GROUP_SVC_CTX_HIGH_PRIO_LEN];
    int8_t svc_ctx_normal_prio[DB_NHP_GROUP_SVC_CTX_NORMAL_PRIO_LEN];
    uint32_t app_source_id;
    uint32_t table_smooth_id;
    int64_t time_stamp_update;
    uint32_t oper_bitmap;
    uint32_t attr_flag;
    int64_t time_stamp_app;
    int64_t time_stamp_svc;
} nhp_group_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagnhp_struct_t {
    uint32_t nhp_index;
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t origin_nhp;
    uint8_t nhp_flag;
    uint8_t nhp_num;
    uint32_t ref_cnt;
    uint32_t flags;
    uint32_t iid_flags;
    uint32_t app_source_id;
    uint32_t table_smooth_id;
    uint64_t app_obj_id;
    uint32_t app_version;
    int64_t time_stamp_app;
    int64_t time_stamp_svc;
} nhp_struct_t;
#pragma pack()

#define DB_NHP_STD_SVC_CTX_HIGH_PRIO_LEN 16
#define DB_NHP_STD_SVC_CTX_NORMAL_PRIO_LEN 16

#pragma pack(1)
typedef struct tagnhp_std_struct_t {
    uint32_t nhp_index;
    uint32_t next_hop;
    uint32_t out_if_index;
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t flags;
    uint32_t if_type;
    uint32_t iid_flags;
    uint32_t work_if_index;
    uint8_t status_high_prio;
    uint8_t status_normal_prio;
    uint8_t errcode_high_prio;
    uint8_t errcode_normal_prio;
    int8_t svc_ctx_high_prio[DB_NHP_STD_SVC_CTX_HIGH_PRIO_LEN];
    int8_t svc_ctx_normal_prio[DB_NHP_STD_SVC_CTX_NORMAL_PRIO_LEN];
    uint32_t app_source_id;
    uint32_t group_smooth_id;
    uint64_t app_obj_id;
    uint32_t app_version;
    uint32_t attr_flag;
    uint16_t fwd_if_type;
    uint16_t reserved;
    int64_t time_stamp_app;
    int64_t time_stamp_svc;
} nhp_std_struct_t;
#pragma pack()

#define DB_FWM_NAC_CIB_BASE_INFO_MAC_LEN 6

/* All fields struct define as following */
/* Struct definition of fwm_nac_cib_base_info */
#pragma pack(1)
typedef struct tagfwm_nac_cib_base_info_struct_t {
    uint32_t global_cid;
    uint32_t instance_id;
    uint32_t vr_id;
    uint32_t vsys_id;
    int8_t mac[DB_FWM_NAC_CIB_BASE_INFO_MAC_LEN];
    uint32_t user_type;
    uint32_t access_type;
    uint32_t auth_code_type;
    uint8_t remote_type;
    uint32_t if_index;
    uint16_t act_vlan;
    uint32_t bd_id;
    uint32_t vsi_id;
    uint64_t create_time;
    uint32_t ip_type;
    uint32_t ipv4_addr;
    uint8_t preauth_flag;
    uint8_t port_base;
    uint32_t auth_flag;
    uint32_t detect_type;
    uint32_t detect_period;
    uint32_t detect_timer;
    uint32_t is_eth_trunk_access;
} fwm_nac_cib_base_info_struct_t;
#pragma pack()

#define DB_FWM_NAC_CIB_AUTH_INFO_DACL_GROUP_NAME_LEN 65

/* All fields struct define as following */
/* Struct definition of fwm_nac_cib_auth_info */
#pragma pack(1)
typedef struct tagfwm_nac_cib_auth_info_struct_t {
    uint32_t global_cid;
    uint32_t instance_id;
    uint32_t acl_id;
    int8_t dacl_group_name[DB_FWM_NAC_CIB_AUTH_INFO_DACL_GROUP_NAME_LEN];
    uint8_t active_acl_type;
    uint32_t class_id;
    uint16_t original_vlan;
    uint16_t inner_vlan;
    uint16_t auth_vlan;
    uint32_t auth_vlan_type;
    uint8_t up_flow_statics_flag;
    uint8_t down_flow_statics_flag;
    uint8_t up_dscp;
    uint8_t down_dscp;
    uint8_t up_8021p;
    uint8_t down_8021p;
    uint16_t ucl_group_id;
    uint8_t inbound_car_flag;
    uint32_t inbound_cir;
    uint32_t inbound_cbs;
    uint32_t inbound_pir;
    uint32_t inbound_pbs;
    uint8_t outbound_car_flag;
    uint32_t outbound_cir;
    uint32_t outbound_cbs;
    uint32_t outbound_pir;
    uint32_t outbound_pbs;
} fwm_nac_cib_auth_info_struct_t;
#pragma pack()

/* All length macro define as following */
#define DB_IF_FWD_IFKEY_LEN 20
#define DB_IF_FWD_RESOURCE_LEN 24
#define DB_IF_FWD_DOWN_STATUS_LEN 8
#define DB_IF_FWD_ERROR_CODE_LEN 8
#define DB_IF_FWD_SVC_CTX_LEN 96

#pragma pack(1)
typedef struct tagstp_vlan_stg_mb_struct_t {
    uint32_t vrid;
    uint16_t vlanId;
    uint16_t instance_id;
    uint16_t instance_index;
} stp_vlan_stg_mb_struct_t;
#pragma pack()

/* Struct definition of stp_port_stg_status_mb */
#pragma pack(1)
typedef struct tagstp_port_stg_status_mb_struct_t {
    uint32_t vrid;
    uint32_t ifindex;
    uint16_t instId;
    uint8_t state;
} stp_port_stg_status_mb_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagnac_port_auth_struct_t {
    uint32_t ifindex;
    uint32_t iftype;
    uint32_t auth_methods;
    uint32_t port_based;
    uint32_t pkt_map;
    uint32_t down_ctl;
    uint8_t portbase_authened;
} nac_port_auth_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_vlan_cfg {
    uint32_t vrid;
    uint16_t vlan_id;
} db_vlan_cfg_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_if_cfg {
    uint32_t ifindex;
} db_if_cfg_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_ip4forward {
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t dest_ip_addr;
    uint8_t mask_len;
} db_ip4forward_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_nhp_group_vrfid_hashcluster_key {
    uint32_t app_source_id;
    uint32_t vr_id;
    uint32_t vrf_index;
} db_nhp_group_vrfid_hashcluster_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_nhp {
    uint32_t nhp_index;
} db_nhp_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_nhp_std_nhpindex_localhash_key {
    uint32_t nhp_index;
} db_nhp_std_nhpindex_localhash_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_fwm_nac_cib_base_info {
    uint32_t global_cid;
    uint32_t instance_id;
} db_fwm_nac_cib_base_info_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagdb_fwm_nac_cib_auth_info {
    uint32_t global_cid;
    uint32_t instance_id;
} db_fwm_nac_cib_auth_info_key_t;
#pragma pack()

#pragma pack(1)
typedef struct tagbd_bind_vlan_struct_t {
    uint32_t vr_id;
    uint32_t bd_id;
    uint16_t vlan_id;
    uint8_t bd_type;
    uint8_t complete_data;
    uint32_t if_index;
    uint32_t vsi_id;
    uint32_t ver_no;
} bd_bind_vlan_struct_t;
#pragma pack()

#pragma pack(1)
typedef struct tagvlan_cfg_struct_t {
    uint32_t vrid;
    uint16_t vlan_id;
    uint8_t admin_state;
    uint8_t static_enable;
    int8_t vlan_name[DB_VLAN_CFG_VLAN_NAME_LEN];
    int8_t description[DB_VLAN_CFG_DESCRIPTION_LEN];
    uint32_t transparent;
    uint8_t vlan_type;
    uint32_t app_source_id;
    uint32_t app_serial_id;
    uint32_t app_obj_id;
    uint32_t app_version;
} tagvlan_cfg_struct_t;
#pragma pack()

int32_t if_vlan_read_by_localhash_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj);
int32_t if_fwd_read_vrf_index_by_primary_func(uint32_t uDataCntm, connObj *connObj);
int32_t stp_vlan_stg_mb_read_field_instance_index_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t stp_port_stg_status_mb_read_field_state_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t if_read_field_name_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t if_read_node_dev_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t cfg_bd_bind_vlan_read_by_localhash_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj);
int32_t vlan_cfg_read_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t if_cfg_read_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t trunk_member_read_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t cib_nac_data_async_insert_once_func(
    uint32_t uDataCnt, uint32_t Isupdate, void *call_back, void *pUser_data, connObj *connObj);
int32_t cib_nac_ext_data_async_insert_once_func(
    uint32_t uDataCnt, uint32_t Isupdate, void *call_back, void *pUser_data, connObj *connObj);
int32_t ip4forward_read_by_lpm_func(uint32_t uDataCnt, connObj *connObj);
int32_t nhp_group_get_count_by_hashcluster_func(uint32_t uDataCnt, connObj *connObj);
int32_t nhp_group_read_by_hashcluster_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj);
int32_t nhp_read_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t nhp_std_read_by_hashcluster_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj);
int32_t ifm_ipv4_address_read_node_ipv4_address_list_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t fwm_nac_cib_base_info_read_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t if_read_field_lagid_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t if_read_field_forwardType_slot_id_chassis_id_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t nac_port_auth_read_field_down_ctl_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t fwm_nac_cib_auth_info_read_by_primary_func(uint32_t uDataCnt, connObj *connObj);
int32_t fwm_nac_cib_auth_info_sync_insert_once_func(uint32_t uDataCnt, uint32_t Isupdate, connObj *connObj);
int32_t fwm_nac_cib_base_info_sync_insert_once_func(uint32_t uDataCnt, uint32_t Isupdate, connObj *connObj);
int32_t if_fwd_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t stp_vlan_stg_mb_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t stp_port_stg_status_mb_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t if_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t if_vlan_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t cfg_bd_bind_vlan_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t vlan_cfg_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t if_cfg_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t trunk_member_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t ip4forward_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t nhp_group_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t nhp_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t nhp_std_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t ifm_ipv4_address_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t bd_bind_vlan_batch_insert_func(uint32_t uDataCnt, connObj *connObj);
int32_t nac_port_auth_batch_insert_func(uint32_t uDataCnt, connObj *connObj);

int32_t if_fwd_read_vrf_index_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    uint32_t get_field_u32;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_fwd", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "if_fwd_pk");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uIdx, sizeof(uIdx));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");

    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        CHECK_OK_RET(ret, "GmcFetch");
        if (ret != GMERR_OK) {
            break;
        }
        if (isFinish) {
            break;
        }

        bool isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
    }

    GmcFreeStmt(stmt);
    return ret;
}

int32_t stp_vlan_stg_mb_read_field_instance_index_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint16_t get_field_u16;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "stp_vlan_stg_mb", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "stp_vlan_stg_pk");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t vrid = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint16_t vlanId = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &vlanId, sizeof(vlanId));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");

    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        CHECK_OK_RET(ret, "GmcFetch");
        if (isFinish) {
            break;
        }
        bool isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "instance_index", &get_field_u16, sizeof(get_field_u16), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
    }

    GmcFreeStmt(stmt);
    return ret;
}

int32_t stp_port_stg_status_mb_read_field_state_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint8_t get_field_u8;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "stp_port_stg_status_mb", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "stp_port_stg_status_pk");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t vrid = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint32_t ifindex = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint16_t instId = 0;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT16, &instId, sizeof(instId));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");
    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        CHECK_OK_RET(ret, "GmcFetch");
        if (isFinish) {
            break;
        }

        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "state", &get_field_u8, sizeof(get_field_u8), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
    }
    GmcFreeStmt(stmt);

    return ret;
}

int32_t if_read_field_name_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    int8_t get_field_str[64];
    uint32_t get_field_str_len = 64;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "if_pk");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t ifindex = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");

    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        CHECK_OK_RET(ret, "GmcFetch");
        if (isFinish) {
            break;
        }
        bool isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "name", get_field_str, get_field_str_len, &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
    }
    GmcFreeStmt(stmt);
    return ret;
}

int32_t if_read_node_dev_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    uint32_t get_field_u32;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "if_pk");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t ifindex = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");
    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        CHECK_OK_RET(ret, "GmcFetch");
        if (isFinish) {
            break;
        }

        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "dev", &childNode);
        CHECK_OK_RET(ret, "db_create_child_node dev");

        bool isNull = false;
        ret = GmcNodeGetPropertyByName(childNode, "dev_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
        ret = GmcNodeGetPropertyByName(childNode, "chassis_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
        ret = GmcNodeGetPropertyByName(childNode, "slot_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
        ret = GmcNodeGetPropertyByName(childNode, "card_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
        ret = GmcNodeGetPropertyByName(childNode, "unit_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
        ret = GmcNodeGetPropertyByName(childNode, "port_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
        ret = GmcNodeGetPropertyByName(childNode, "port_num", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
    }

    GmcFreeStmt(stmt);

    return ret;
}

int32_t if_vlan_read_by_localhash_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t scan_times = 0;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_vlan", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "k_vlan_id");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint16_t vlan_id = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &vlan_id, sizeof(vlan_id));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint32_t vr_id = 0;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    if_vlan_struct_t obj;
    GmcDeseriT deserStr;
    deserStr.deseriFunc = deSeriStructVertex;
    deserStr.version = GMC_SERI_VERSION_DEFAULT;
    deserStr.obj = (uint8_t *)&obj;
    deserStr.objSize = sizeof(obj);
    deserStr.userData = &ctx;

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "if_vlan_read_by_localhash_func execute failed.");
    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "if_vlan_read_by_localhash_func gmcfetch failed.");
            GmcFreeStmt(stmt);
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &deserStr);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "Create root iterator failed.");
            GmcFreeStmt(stmt);
            return ret;
        }
        if_vlan_struct_t *get_data = (if_vlan_struct_t *)deserStr.obj;
        scan_times++;
        if (scan_times >= max_count) {
            break;
        }
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t cfg_bd_bind_vlan_read_by_localhash_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t scan_times = 0;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "cfg_bd_bind_vlan", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "vlan_index");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t vr_id = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint16_t vlan_id = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &vlan_id, sizeof(vlan_id));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    cfg_bd_bind_vlan_struct_t obj;
    GmcDeseriT deserStr;
    deserStr.deseriFunc = deSeriStructVertex;
    deserStr.version = GMC_SERI_VERSION_DEFAULT;
    deserStr.obj = (uint8_t *)&obj;
    deserStr.objSize = sizeof(obj);
    deserStr.userData = &ctx;

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "GmcExecute failed.");
    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "Create root iterator failed.");
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &deserStr);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "Create root iterator failed.");
            GmcFreeStmt(stmt);
            return ret;
        }
        scan_times++;
        if (scan_times >= max_count) {
            break;
        }
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t vlan_cfg_read_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t scan_times = 0;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "vlan_cfg", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    db_vlan_cfg_key_t key_data = {0};
    key_data.vrid = 0;
    key_data.vlan_id = uIdx;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<db_vlan_cfg_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    tagvlan_cfg_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(tagvlan_cfg_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    ret = GmcSetIndexKeyWithBuf(stmt, 0, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "GmcExecute");
    bool isFinish;
    while (1) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        CHECK_OK_RET(ret, "GmcFetch");
        if (isFinish || ret != GMERR_OK) {
            break;
        }

        ret = GmcGetVertexDeseri(stmt, &d);  // 结构化查询的结果
        CHECK_OK_RET(ret, "GmcGetVertexDeseri");
        ret = GmcGetVertexBuf(stmt, 0, NULL, &inputBufInfo);
        CHECK_OK_RET(ret, "GmcGetVertexBuf");
        if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
            CHECK_OK_RET(ret, "db_read_obj_with_buf tagvlan_cfg_struct_t failed");
            GmcFreeStmt(stmt);
            return ret;
        }
        scan_times++;
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t if_cfg_read_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_cfg", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    db_if_cfg_key_t key_data = {0};
    key_data.ifindex = uIdx;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<db_if_cfg_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    if_cfg_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(if_cfg_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    ret = GmcSetIndexKeyWithBuf(stmt, 0, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "GmcExecute");
    bool isFinish;
    uint32_t scan_times = 0;
    while (1) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        CHECK_OK_RET(ret, "GmcFetch");
        if (isFinish || ret != GMERR_OK) {
            break;
        }

        ret = GmcGetVertexDeseri(stmt, &d);  // 结构化查询的结果
        CHECK_OK_RET(ret, "GmcGetVertexDeseri");
        ret = GmcGetVertexBuf(stmt, 0, NULL, &inputBufInfo);
        CHECK_OK_RET(ret, "GmcGetVertexBuf\n");
        if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
            CHECK_OK_RET(ret, "db_read_obj_with_buf if_cfg_read_by_primary_func failed");
            GmcFreeStmt(stmt);
            return ret;
        }
        scan_times++;
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t trunk_member_read_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t get_field_u32;
    uint64_t get_field_u64;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "trunk_member", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "trunk_member_pk");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t vrid = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint32_t ifindex = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");

    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        CHECK_OK_RET(ret, "GmcFetch");
        if (isFinish) {
            break;
        }

        bool isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "vrid", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "ifindex", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "trunk_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "hash_mode", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "least_active_num", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "app_source_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "app_serial_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "app_obj_id", &get_field_u64, sizeof(get_field_u64), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "app_version", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "odaflag", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");

        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "member", &childNode);
        CHECK_OK_RET(ret, "db_create_child_node dev");
        uint32_t array_count;
        ret = GmcNodeGetElementCount(childNode, &array_count);
        CHECK_OK_RET(ret, "db_child_count member");

        for (uint32_t i = 0; i < array_count; i++) {
            ret = GmcNodeGetElementByIndex(childNode, i, &childNode);
            ret = GmcNodeGetPropertyByName(childNode, "member_ifindex", &get_field_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "db_get_trunk_member_member node");
            ret = GmcNodeGetPropertyByName(childNode, "member_active", &get_field_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "db_get_trunk_member_member node");
            ret = GmcNodeGetPropertyByName(childNode, "member_weight", &get_field_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "db_get_trunk_member_member node");
            ret = GmcNodeGetPropertyByName(childNode, "member_state", &get_field_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "db_get_trunk_member_member node");
            ret = GmcNodeGetPropertyByName(childNode, "trunk_state", &get_field_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "db_get_trunk_member_member node");
        }
    }
    GmcFreeStmt(stmt);
    return ret;
}

int32_t cib_nac_data_async_insert_once_func(
    uint32_t uDataCnt, uint32_t Isupdate, void *call_back, void *pUser_data, connObj *connObj)
{
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t uIdxup = uDataCnt + Isupdate;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connA, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "cib_nac_data", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    cib_nac_data_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    obj_struct.ulCID = uIdx;
    obj_struct.ulCidPrefix = uIdx;
    obj_struct.ulMagic = uIdx;
    obj_struct.ulVsysID = uIdx;
    obj_struct.ulVrID = uIdx;
    obj_struct.ulTaskID = uIdx;
    obj_struct.ucState = uIdx;
    obj_struct.ucSubState = uIdx;
    obj_struct.ucTableType = uIdx;
    obj_struct.ucAccessType = uIdx;
    obj_struct.ucTriggerType = uIdx;
    obj_struct.ucUserType = uIdxup;
    obj_struct.ucIsV6User = uIdxup;
    obj_struct.ucIfModifyAccNum = uIdx;
    obj_struct.ucSendLogInFlag = uIdx;
    obj_struct.ulAccessTime = uIdx;
    obj_struct.lAccessTime = uIdx;
    obj_struct.usPort = uIdx;
    obj_struct.ulPortType = uIdx;
    obj_struct.ulInterface = uIdx;
    obj_struct.ulLogicInterface = uIdx;
    obj_struct.ulPortIndex = uIdx;
    obj_struct.ulL2Interface = uIdx;
    obj_struct.ucSlot = uIdx;
    obj_struct.ucPic = uIdx;
    obj_struct.usMTU = uIdx;
    obj_struct.ulSrcNodeID = uIdx;
    obj_struct.ulSrcChnlID = uIdx;
    obj_struct.ulAccessModIndex = uIdx;
    obj_struct.ulAuthModIndex = uIdx;
    obj_struct.ulIpAddr = uIdx;
    obj_struct.ulVrfIndex = uIdx;
    obj_struct.ulGateWay = uIdx;
    obj_struct.ulMask = uIdx;
    obj_struct.ulAuthProfileIndex = uIdx;
    obj_struct.ucPortalLayerFlag = uIdx;
    obj_struct.ucWlanFlag = uIdx;
    obj_struct.ucIsETrunkAccess = uIdx;
    obj_struct.ucAuthFailAccessType = uIdx;
    obj_struct.waitMsgType = uIdx;
    obj_struct.ucResponeSuccessToEAPFlag = uIdx;
    obj_struct.ucResponeSuccessToWEBFlag = uIdx;
    obj_struct.ulRdsAuthenSvrVrf = uIdx;
    obj_struct.usDomainIndex = uIdx;
    obj_struct.usServiceProfile = uIdx;
    obj_struct.usRDSTemplateID = uIdx;
    obj_struct.ucAuthType = uIdx;
    obj_struct.ucAuthedPlace = uIdx;
    obj_struct.uiTACTemplateID = uIdx;
    obj_struct.uiLDAPTemplateID = uIdx;
    obj_struct.uiADTemplateID = uIdx;
    obj_struct.ucAuthoredPlace = uIdx;
    obj_struct.ucAuthenType = uIdx;
    obj_struct.ucAAASuccFlag = uIdx;
    obj_struct.ucSubMsgType = uIdx;
    obj_struct.ucSubAuthenCode = uIdx;
    obj_struct.ucPreAuthenCode = uIdx;
    obj_struct.ulAAAAuthType = uIdx;
    obj_struct.ulVlan = uIdx;
    obj_struct.usActionFlag = uIdx;
    obj_struct.ulAttrFlag = uIdx;
    obj_struct.bLeavingFlagAAA = uIdx;
    obj_struct.bLeavingFlagWEB = uIdx;
    obj_struct.bLeavingFlagFC = uIdx;
    obj_struct.bLeavingFlagDHCPR = uIdx;
    obj_struct.bLeavingFlagEAPOL = uIdx;
    obj_struct.bLeavingFlagWLAN = uIdx;
    obj_struct.bLeavingFlagAdmin = uIdx;
    obj_struct.bLeavingFlagPPP = uIdx;
    obj_struct.bLeavingFlagSrcMod = uIdx;
    obj_struct.ucDelRetryTimes = uIdx;
    obj_struct.ucGateWayFlag = uIdx;
    obj_struct.ucUpFlag = uIdx;
    obj_struct.ucPreAcctMethod = uIdx;
    obj_struct.ucPreAuthPlace = uIdx;
    obj_struct.ucBatchFlag = uIdx;
    obj_struct.ucBakModifyFlog = uIdx;
    obj_struct.ucBakState = uIdx;
    obj_struct.ulVlanifIndex = uIdx;
    obj_struct.is_per_user_hash_added = uIdx;
    obj_struct.ucLocalAuthorType = uIdx;
    obj_struct.original_ulVlan = uIdx;
    obj_struct.ulMagicNumber = uIdx;
    obj_struct.ucUserNameAccountFlag = uIdx;
    obj_struct.usSqId = uIdx;
    obj_struct.ucAccessMode = uIdx;
    obj_struct.ulModifyTriggerBmp = uIdx;
    obj_struct.ulAuthorModifyBitmap = uIdx;
    obj_struct.ucAuthVlanType = uIdx;
    obj_struct.usAuthVlan = uIdx;
    obj_struct.ucUserVlanSource = uIdx;
    obj_struct.ucWebMangerUserFlag = uIdx;
    obj_struct.ucIsForceDomainFlag = uIdx;
    obj_struct.lIdleSec = uIdx;
    obj_struct.ucBakUserFlag = uIdx;
    obj_struct.ucBakNeedAddFlag = uIdx;
    obj_struct.ulBakRandom = uIdx;
    obj_struct.ulUserNameMaxNum = uIdx;
    obj_struct.ucAclOKV6 = uIdx;
    obj_struct.ucIfHasProcAdpAuthor = uIdx;
    obj_struct.ucNotProcFcFlag = uIdx;
    obj_struct.isVoiceDev = uIdx;
    obj_struct.isdot1xmacReAuth = uIdx;
    obj_struct.ulAcctSessionTime = uIdx;
    obj_struct.ucIpConflictCheck = uIdx;
    obj_struct.ucPreAuthFlag = uIdx;
    obj_struct.ucIsPortBaseAuthenMac = uIdx;
    obj_struct.ucHaveUpdateCounter = uIdx;
    obj_struct.ulReauthTimeLen = uIdx;
    obj_struct.ulHacaServerIp = uIdx;
    obj_struct.ucOpenUser = uIdx;
    obj_struct.ucStaticUserFlag = uIdx;
    obj_struct.ucIsVoiceTerminal = uIdx;
    obj_struct.ucISPFlag = uIdx;
    obj_struct.ucLeaseFlag = uIdx;
    obj_struct.ulDnsIp1 = uIdx;
    obj_struct.ulDnsIp2 = uIdx;
    obj_struct.ulWinsIP1 = uIdx;
    obj_struct.ulWinsIP2 = uIdx;
    obj_struct.ucUserGroupChanged = uIdx;
    obj_struct.ucHttpFwdFlg = uIdx;
    obj_struct.ulSessionStartTime = uIdx;
    obj_struct.ucTermAct = uIdx;
    obj_struct.ucPushFlag = uIdx;
    obj_struct.ucAuthorizeType = uIdx;
    obj_struct.ucUnRestartTimerFlag = uIdx;
    obj_struct.ulEAPSessionTime = uIdx;
    obj_struct.ucVlanType = uIdx;
    obj_struct.usISPvlan = uIdx;
    obj_struct.usVLAN = uIdx;
    obj_struct.usGroupID = uIdx;
    obj_struct.usUclGroupId = uIdx;
    obj_struct.ucInnerIsolated = uIdx;
    obj_struct.ucInterIsolated = uIdx;
    obj_struct.ulMacLimit = uIdx;
    obj_struct.ulSessionTimeOut = uIdx;
    obj_struct.ucPriority = uIdx;
    obj_struct.ucDownPriority = uIdx;
    obj_struct.ucInDscpValue = uIdx;
    obj_struct.ucIn8021pValue = uIdx;
    obj_struct.ucInExpValue = uIdx;
    obj_struct.ucInLPValue = uIdx;
    obj_struct.ucOutDscpValue = uIdx;
    obj_struct.ucOut8021pValue = uIdx;
    obj_struct.ucOutExpValue = uIdx;
    obj_struct.ucOutLPValue = uIdx;
    obj_struct.ucIsSupportBandWidth = uIdx;
    obj_struct.ucBandWidthSlot = uIdx;
    obj_struct.ulUpLinkbandwidth = uIdx;
    obj_struct.ulDownLinkBWCir = uIdx;
    obj_struct.ulDownLinkBWCbs = uIdx;
    obj_struct.ucHttpToCpuFlag = uIdx;
    obj_struct.ucHttpsToCpuFlag = uIdx;
    obj_struct.ucProtocolTpye = uIdx;
    obj_struct.usPortNum = uIdx;
    obj_struct.ucBandWidthShareMode = uIdx;
    obj_struct.ulForwardInterface = uIdx;
    obj_struct.ucDaaStaticEnable = uIdx;
    obj_struct.ucStaticsUpMap = uIdx;
    obj_struct.ucStaticsDownMap = uIdx;
    obj_struct.ucAcctMap = uIdx;
    obj_struct.ucUserUpFlowStaticsFlag = uIdx;
    obj_struct.ucUserDownFlowStaticsFlag = uIdx;
    obj_struct.ucFlowStatEnableFlag = uIdx;
    obj_struct.ucDscp = uIdx;
    obj_struct.ucAccoutingSeparate = uIdx;
    obj_struct.ucV6StaticsUpMap = uIdx;
    obj_struct.ucV6StaticsDownMap = uIdx;
    obj_struct.ulLocalAuthorBitMap = uIdx;
    obj_struct.ulRemoteAuthorBitMap = uIdx;
    obj_struct.stAuthorizeInfo_ulIpAddr = uIdx;
    obj_struct.stAuthorizeInfo_ulGateWay = uIdx;
    obj_struct.stAuthorizeInfo_ulMask = uIdx;
    obj_struct.ucIfRdsAcl = uIdx;
    obj_struct.ucServiceSchemeVoiceVlan = uIdx;
    obj_struct.userAddrNetwork_ulIP = uIdx;
    obj_struct.userAddrNetwork_ulMask = uIdx;
    obj_struct.duRemainFlow = uIdx;
    obj_struct.usRedirectAclId = uIdx;
    obj_struct.ucSessionTimeoutSource = uIdx;
    obj_struct.ucDot1xUrlFlag = uIdx;
    obj_struct.ucUserGroupPriority = uIdx;
    obj_struct.ucUpRemarkOK = uIdx;
    obj_struct.ucDnRemarkOK = uIdx;
    obj_struct.usSesssionID = uIdx;
    obj_struct.ucOptionLen = uIdx;
    obj_struct.ucDownQosOK = uIdx;
    obj_struct.ucDhcpAckFlag = uIdx;
    obj_struct.ucIfNeedDHCPAck = uIdx;
    obj_struct.ucIsIpStaticUserEnable = uIdx;
    obj_struct.ucIfVpdn = uIdx;
    obj_struct.usUserGroup = uIdx;
    obj_struct.usUclGroup = uIdx;
    obj_struct.ucIsHacaUserFlag = uIdx;
    obj_struct.ulIPSecAcl = uIdx;
    obj_struct.ucIPSecFlag = uIdx;
    obj_struct.ucSetGroupOk = uIdx;
    obj_struct.ucUpDscpOK = uIdx;
    obj_struct.ucDnDscpOK = uIdx;
    obj_struct.ucIsV6Acl = uIdx;
    obj_struct.ucIsPortBased = uIdx;
    obj_struct.patch_reserved = uIdx;

    (void)memset_s(obj_struct.stRdsAuthenSvr, 16, 0, 16);
    (void)memset_s(obj_struct.szAcctSessionID, 64, 0, 64);
    (void)memset_s(obj_struct.szUserMac, 6, 0, 6);
    (void)memset_s(obj_struct.szLocalMac, 6, 0, 6);
    (void)memset_s(obj_struct.szSysMac, 6, 0, 6);

    ret = GmcSetVertexWithBuf(stmt, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set vertex with structure buffer.");
        GmcFreeStmt(stmt);
        return ret;
    }
    GmcAsyncRequestDoneContextT context;
    context.userData = pUser_data;

    // insert
    context.insertCb = (GmcInsertVertexDoneT)call_back;

    ret = GmcExecuteAsync(stmt, &context);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute db_write_obj_async.");
        GmcFreeStmt(stmt);
        return ret;
    }

    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

static GmcSeriT g_cib_nac_ext_data_seri;

int32_t cib_nac_ext_data_async_insert_once_func(
    uint32_t uDataCnt, uint32_t Isupdate, void *call_back, void *pUser_data, connObj *connObj)
{
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t uIdxup = uDataCnt + Isupdate;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connA, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "cib_nac_ext_data", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    cib_nac_ext_data_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;

    char testName[] = "none";
    uint16_t bytes_len = sizeof(testName);
    uint16_t array_size = 1;
    obj_struct.stDataFlow_count = array_size;
    obj_struct.stDaaQosProfile_count = array_size;
    obj_struct.stIncreaseDataFlow_count = array_size;
    obj_struct.ulCID = uIdx;
    obj_struct.ulCidPrefix = uIdx;
    obj_struct.patch_reserved = uIdxup;
    obj_struct.szAcctMultiSessionId_len = bytes_len;
    obj_struct.szAcctMultiSessionId = (int8_t *)testName;
    obj_struct.stIPv6Addr_len = bytes_len;
    obj_struct.stIPv6Addr = (int8_t *)testName;
    obj_struct.szUserNameBuf_len = bytes_len;
    obj_struct.szUserNameBuf = (int8_t *)testName;
    obj_struct.szServiceSchemeName_len = bytes_len;
    obj_struct.szServiceSchemeName = (int8_t *)testName;
    obj_struct.usPool_len = bytes_len;
    obj_struct.usPool = (int8_t *)testName;
    obj_struct.szUserGroupName_len = bytes_len;
    obj_struct.szUserGroupName = (int8_t *)testName;
    obj_struct.szUpdateURL_len = bytes_len;
    obj_struct.szUpdateURL = (int8_t *)testName;
    obj_struct.szDNSDomainName_len = bytes_len;
    obj_struct.szDNSDomainName = (int8_t *)testName;
    obj_struct.szDaaQosProfileName_len = bytes_len;
    obj_struct.szDaaQosProfileName = (int8_t *)testName;
    obj_struct.szState_len = bytes_len;
    obj_struct.szState = (int8_t *)testName;
    obj_struct.szRedirectURL_len = bytes_len;
    obj_struct.szRedirectURL = (int8_t *)testName;
    obj_struct.szUserClass_len = bytes_len;
    obj_struct.szUserClass = (int8_t *)testName;
    obj_struct.usAclID_len = bytes_len;
    obj_struct.usAclID = (int8_t *)testName;
    obj_struct.usAclV6ID_len = bytes_len;
    obj_struct.usAclV6ID = (int8_t *)testName;
    obj_struct.pszAcl1_len = bytes_len;
    obj_struct.pszAcl1 = (int8_t *)testName;
    obj_struct.pszAcl2_len = bytes_len;
    obj_struct.pszAcl2 = (int8_t *)testName;
    obj_struct.szPushUrl_len = bytes_len;
    obj_struct.szPushUrl = (int8_t *)testName;
    obj_struct.szOption82Buf_len = bytes_len;
    obj_struct.szOption82Buf = (int8_t *)testName;
    obj_struct.szOption60Buf_len = bytes_len;
    obj_struct.szOption60Buf = (int8_t *)testName;
    obj_struct.stSlotFlag_aulBitMap_len = bytes_len;
    obj_struct.stSlotFlag_aulBitMap = (int8_t *)testName;
    obj_struct.stLastSendSlotFlag_aulBitMap_len = bytes_len;
    obj_struct.stLastSendSlotFlag_aulBitMap = (int8_t *)testName;
    obj_struct.szAccessUserName_len = bytes_len;
    obj_struct.szAccessUserName = (int8_t *)testName;
    obj_struct.szSendKeyPMK_len = bytes_len;
    obj_struct.szSendKeyPMK = (int8_t *)testName;
    obj_struct.szChargeableIdentity_len = bytes_len;
    obj_struct.szChargeableIdentity = (int8_t *)testName;

    cib_nac_ext_data_stCmAsInfo_t obj_st_stCmAsInfo;
    obj_st_stCmAsInfo.ulASIP = uIdxup;
    obj_st_stCmAsInfo.ucSlotNo = uIdx;
    obj_st_stCmAsInfo.ucPicNo = uIdx;
    obj_st_stCmAsInfo.usPortNo = uIdx;
    obj_st_stCmAsInfo.ulSubIndex = uIdx;
    obj_st_stCmAsInfo.ulAsIfIndex = uIdx;
    obj_st_stCmAsInfo.ucIsRemoteUser = uIdx;
    obj_st_stCmAsInfo.ucRemoteSyncTimeoutCount = uIdx;
    obj_st_stCmAsInfo.usAsIfPvid = uIdx;
    obj_st_stCmAsInfo.szASPortName_len = bytes_len;
    obj_st_stCmAsInfo.szASPortName = (int8_t *)testName;
    (void)memset_s(obj_st_stCmAsInfo.szASMac, 6, 0, 6);
    obj_struct.stCmAsInfo = &obj_st_stCmAsInfo;
    obj_struct.stCmAsInfo_flag = 1;

    cib_nac_ext_data_stAcctData_t obj_st_stAcctData;
    obj_st_stAcctData.ucIsNeedModifyRTAcctFlag = 1;
    obj_st_stAcctData.ulRTAcctInterval = uIdx;
    obj_st_stAcctData.ucAcctState = uIdx;
    obj_st_stAcctData.ucAcctMethod = uIdx;
    obj_st_stAcctData.ucSTAcctPolicy = uIdx;
    obj_st_stAcctData.ucRTAcctPolicy = uIdx;
    obj_st_stAcctData.ucRTAcctFailNumPermit = uIdx;
    obj_st_stAcctData.ucRTAcctFailTimes = uIdx;
    obj_st_stAcctData.ucFlowAcctMode = uIdx;
    obj_st_stAcctData.ucAccStart = uIdx;
    obj_st_stAcctData.ucSendRTAcctFlag = uIdx;
    obj_st_stAcctData.ucIsNeedModifyRTAcctFlag = uIdx;
    obj_st_stAcctData.usNormalRdsTempletID = uIdx;
    obj_st_stAcctData.usTwoLevelAcctRdsTempletID = uIdx;
    obj_st_stAcctData.usNormalTACTempletID = uIdx;
    obj_st_stAcctData.curServer_ucType = uIdx;
    obj_st_stAcctData.curServer_ucTwoLevelType = uIdx;
    obj_st_stAcctData.usHacaTempletID = uIdx;
    obj_st_stAcctData.curServer_stIPAddress_len = bytes_len;
    obj_st_stAcctData.curServer_stIPAddress = (int8_t *)testName;
    obj_st_stAcctData.curServer_stTwoLevelIPAddress_len = bytes_len;
    obj_st_stAcctData.curServer_stTwoLevelIPAddress = (int8_t *)testName;
    obj_struct.stAcctData = &obj_st_stAcctData;
    obj_struct.stAcctData_flag = 1;

    cib_nac_ext_data_stDataFlow_t obj_st_stDataFlow;
    obj_st_stDataFlow.duUpLinkForwardBytes = uIdx;
    obj_st_stDataFlow.duUpLinkForwardPackets = uIdx;
    obj_st_stDataFlow.duDownLinkForwardBytes = uIdx;
    obj_st_stDataFlow.duDownLinkForwardPackets = uIdx;
    obj_st_stDataFlow.duUpLinkDropBytes = uIdx;
    obj_st_stDataFlow.duUpLinkDropPackets = uIdx;
    obj_st_stDataFlow.duDownLinkDropBytes = uIdx;
    obj_st_stDataFlow.duDownLinkDropPackets = uIdx;
    obj_st_stDataFlow.duV6UpLinkForwardBytes = uIdx;
    obj_st_stDataFlow.duV6UpLinkForwardPackets = uIdx;
    obj_st_stDataFlow.duV6DownLinkForwardBytes = uIdx;
    obj_st_stDataFlow.duV6DownLinkForwardPackets = uIdx;
    obj_st_stDataFlow.duV6UpLinkDropBytes = uIdx;
    obj_st_stDataFlow.duV6UpLinkDropPackets = uIdx;
    obj_st_stDataFlow.duV6DownLinkDropBytes = uIdx;
    obj_st_stDataFlow.duV6DownLinkDropPackets = uIdx;
    obj_struct.stDataFlow = &obj_st_stDataFlow;

    cib_nac_ext_data_stUserDetect_t obj_st_stUserDetect;
    obj_st_stUserDetect.ucPortalLayerFlag = 1;
    obj_st_stUserDetect.ucDetectFlagForMacMove = 1;
    obj_st_stUserDetect.ucStaticUserKeepOnlineFlag = 1;
    obj_st_stUserDetect.ulDetectTimeLen = uIdx;
    obj_st_stUserDetect.usDetectTimes = uIdx;
    obj_st_stUserDetect.ucDetectType = uIdx;
    obj_st_stUserDetect.ucDetectUserState = uIdx;
    obj_st_stUserDetect.ucEapolHandShakeType = uIdx;
    obj_st_stUserDetect.ucPortalLayerFlag = uIdx;
    obj_st_stUserDetect.ucNeedModTimer = uIdx;
    obj_st_stUserDetect.ucArpProxyFlag = uIdx;
    obj_st_stUserDetect.ucArpCheckFlag = uIdx;
    obj_st_stUserDetect.ucEapPktId = uIdx;
    obj_st_stUserDetect.ucEapPause = uIdx;
    obj_st_stUserDetect.ulReFreshIpTime = uIdx;
    obj_st_stUserDetect.ucDetectFlagForMacMove = uIdx;
    obj_st_stUserDetect.ucStaticUserKeepOnlineFlag = uIdx;
    obj_st_stUserDetect.usDetectTimesForMacMove = uIdx;
    obj_st_stUserDetect.ulDetectTimeLenForMacMove = uIdx;
    obj_st_stUserDetect.ulDetectDelayTimeLen = uIdx;
    obj_struct.stUserDetect = &obj_st_stUserDetect;
    obj_struct.stUserDetect_flag = 1;

    cib_nac_ext_data_stCar_t obj_st_stCar;
    obj_st_stCar.inbound_ucCarFlag = 1;
    obj_st_stCar.outbound_ucCarFlag = 1;
    obj_st_stCar.inbound_ulCir = uIdx;
    obj_st_stCar.inbound_ulCbs = uIdx;
    obj_st_stCar.inbound_ulPir = uIdx;
    obj_st_stCar.inbound_ulPbs = uIdx;
    obj_st_stCar.outbound_ucCarFlag = uIdx;
    obj_st_stCar.outbound_ulCir = uIdx;
    obj_st_stCar.outbound_ulCbs = uIdx;
    obj_st_stCar.outbound_ulPir = uIdx;
    obj_st_stCar.outbound_ulPbs = uIdx;
    obj_struct.stCar = &obj_st_stCar;
    obj_struct.stCar_flag = 1;

    cib_nac_ext_data_stDaaQosProfile_t obj_st_stDaaQosProfile;
    obj_st_stDaaQosProfile.upCarItem_ulCarFlag = 1;
    obj_st_stDaaQosProfile.downCarItem_ulCarFlag = 1;
    obj_st_stDaaQosProfile.ulStatEn = uIdx;
    obj_st_stDaaQosProfile.upCarItem_ulCarFlag = uIdx;
    obj_st_stDaaQosProfile.upCarItem_ulCir = uIdx;
    obj_st_stDaaQosProfile.upCarItem_ulCbs = uIdx;
    obj_st_stDaaQosProfile.upCarItem_ulPir = uIdx;
    obj_st_stDaaQosProfile.upCarItem_ulPbs = uIdx;
    obj_st_stDaaQosProfile.downCarItem_ulCarFlag = uIdx;
    obj_st_stDaaQosProfile.downCarItem_ulCir = uIdx;
    obj_st_stDaaQosProfile.downCarItem_ulCbs = uIdx;
    obj_st_stDaaQosProfile.downCarItem_ulPir = uIdx;
    obj_st_stDaaQosProfile.downCarItem_ulPbs = uIdx;
    obj_st_stDaaQosProfile.remark_ucRemark8021p = uIdx;
    obj_st_stDaaQosProfile.remark_uc8021p = uIdx;
    obj_st_stDaaQosProfile.remark_ucRemarkDscp = uIdx;
    obj_st_stDaaQosProfile.remark_ucInDscp = uIdx;
    obj_st_stDaaQosProfile.remark_ucOutDscp = uIdx;
    obj_st_stDaaQosProfile.userQueue_ucValid = uIdx;
    obj_st_stDaaQosProfile.userQueue_ulPir = uIdx;
    obj_st_stDaaQosProfile.userQueue_ulCir = uIdx;
    obj_st_stDaaQosProfile.usFlowQueueProfileIndex = uIdx;
    obj_st_stDaaQosProfile.usFlowMappingProfileIndex = uIdx;
    obj_struct.stDaaQosProfile = &obj_st_stDaaQosProfile;

    cib_nac_ext_data_stProfileUserQueue_t obj_st_stProfileUserQueue;
    obj_st_stProfileUserQueue.ucValid = uIdx;
    obj_st_stProfileUserQueue.ulPir = uIdx;
    obj_st_stProfileUserQueue.ulCir = uIdx;
    obj_st_stProfileUserQueue.usFlowQueueProfileIndex = uIdx;
    obj_st_stProfileUserQueue.usFlowMappingProfileIndex = uIdx;
    obj_struct.stProfileUserQueue = &obj_st_stProfileUserQueue;
    obj_struct.stProfileUserQueue_flag = 1;

    cib_nac_ext_data_stTunnel_t obj_st_stTunnel;
    obj_st_stTunnel.ucUsed = uIdx;
    obj_st_stTunnel.ulTunnelType = uIdx;
    obj_st_stTunnel.ulTunnelMediumType = uIdx;
    obj_st_stTunnel.ulTunnelPreference = uIdx;
    obj_st_stTunnel.szTunnelPrivateGroupID_len = bytes_len;
    obj_st_stTunnel.szTunnelPrivateGroupID = (int8_t *)testName;
    obj_st_stTunnel.szTunnelPassword_len = bytes_len;
    obj_st_stTunnel.szTunnelPassword = (int8_t *)testName;
    obj_st_stTunnel.szTunnelGroupName_len = bytes_len;
    obj_st_stTunnel.szTunnelGroupName = (int8_t *)testName;
    obj_st_stTunnel.szTunnelClientAuthID_len = bytes_len;
    obj_st_stTunnel.szTunnelClientAuthID = (int8_t *)testName;
    obj_st_stTunnel.szTunnelServerAuthID_len = bytes_len;
    obj_st_stTunnel.szTunnelServerAuthID = (int8_t *)testName;
    obj_st_stTunnel.szTunnelClientEndpointIp_len = bytes_len;
    obj_st_stTunnel.szTunnelClientEndpointIp = (int8_t *)testName;
    obj_st_stTunnel.szTunnelServerEndpointIp_len = bytes_len;
    obj_st_stTunnel.szTunnelServerEndpointIp = (int8_t *)testName;
    obj_st_stTunnel.szTunnelAssignmentID_len = bytes_len;
    obj_st_stTunnel.szTunnelAssignmentID = (int8_t *)testName;
    obj_struct.stTunnel = &obj_st_stTunnel;
    obj_struct.stTunnel_flag = 1;

    cib_nac_ext_data_stIncreaseDataFlow_t obj_st_stIncreaseDataFlow;
    obj_st_stIncreaseDataFlow.duUpLinkForwardBytes = uIdx;
    obj_st_stIncreaseDataFlow.duUpLinkForwardPackets = uIdx;
    obj_st_stIncreaseDataFlow.duDownLinkForwardBytes = uIdx;
    obj_st_stIncreaseDataFlow.duDownLinkForwardPackets = uIdx;
    obj_st_stIncreaseDataFlow.duUpLinkDropBytes = uIdx;
    obj_st_stIncreaseDataFlow.duUpLinkDropPackets = uIdx;
    obj_st_stIncreaseDataFlow.duDownLinkDropBytes = uIdx;
    obj_st_stIncreaseDataFlow.duDownLinkDropPackets = uIdx;
    obj_st_stIncreaseDataFlow.duV6UpLinkForwardBytes = uIdx;
    obj_st_stIncreaseDataFlow.duV6UpLinkForwardPackets = uIdx;
    obj_st_stIncreaseDataFlow.duV6DownLinkForwardBytes = uIdx;
    obj_st_stIncreaseDataFlow.duV6DownLinkForwardPackets = uIdx;
    obj_st_stIncreaseDataFlow.duV6UpLinkDropBytes = uIdx;
    obj_st_stIncreaseDataFlow.duV6UpLinkDropPackets = uIdx;
    obj_st_stIncreaseDataFlow.duV6DownLinkDropBytes = uIdx;
    obj_st_stIncreaseDataFlow.duV6DownLinkDropPackets = uIdx;
    obj_struct.stIncreaseDataFlow = &obj_st_stIncreaseDataFlow;

    cib_nac_ext_data_stIdleCut_t obj_st_stIdleCut;
    obj_st_stIdleCut.ulIdleCutTime = uIdx;
    obj_st_stIdleCut.ulIdleCutLeftTime = uIdx;
    obj_st_stIdleCut.ulIdleCutFlow = uIdx;
    obj_st_stIdleCut.duIdleCutFlow = uIdx;
    obj_st_stIdleCut.duIdleCutLeftFlow = uIdx;
    obj_st_stIdleCut.ucIdleCutFlowDirection = uIdx;
    obj_st_stIdleCut.ucIdleCutSecond = uIdx;
    obj_struct.stIdleCut = &obj_st_stIdleCut;
    obj_struct.stIdleCut_flag = 1;

    cib_nac_ext_data_stWlanInfo_t obj_st_stWlanInfo;
    obj_st_stWlanInfo.ucProcApFlag = 1;
    obj_st_stWlanInfo.ucWorkGroupID = uIdx;
    obj_st_stWlanInfo.ulVapID = uIdx;
    obj_st_stWlanInfo.ulVACNodeID = uIdx;
    obj_st_stWlanInfo.ucVapHaveUpdateCounter = uIdx;
    obj_st_stWlanInfo.ucRadioId = uIdx;
    obj_st_stWlanInfo.ulApId = uIdx;
    obj_st_stWlanInfo.ucIsFastRoam = uIdx;
    obj_st_stWlanInfo.stAuthACIP_enIpVersion = uIdx;
    obj_st_stWlanInfo.stPeerACIP_enIpVersion = uIdx;
    obj_st_stWlanInfo.ulFwdMode = uIdx;
    obj_st_stWlanInfo.usServiceVlan = uIdx;
    obj_st_stWlanInfo.ucWirelessAccessType = uIdx;
    obj_st_stWlanInfo.cNiRssi = uIdx;
    obj_st_stWlanInfo.lTerminalPosX = uIdx;
    obj_st_stWlanInfo.lTerminalPosY = uIdx;
    obj_st_stWlanInfo.ucProcApFlag = uIdx;
    obj_st_stWlanInfo.ucInterRoamFlag = uIdx;
    obj_st_stWlanInfo.ucAuthPointFlag = uIdx;
    obj_st_stWlanInfo.ucAPAuthorFlag = uIdx;
    obj_st_stWlanInfo.ucIfL3Roam = uIdx;
    obj_st_stWlanInfo.ucIfSameCentralAP = uIdx;
    obj_st_stWlanInfo.szApName_len = bytes_len;
    obj_st_stWlanInfo.szApName = (int8_t *)testName;
    obj_st_stWlanInfo.szSsidName_len = bytes_len;
    obj_st_stWlanInfo.szSsidName = (int8_t *)testName;
    obj_st_stWlanInfo.szInterRoamPreDomainBuf_len = bytes_len;
    obj_st_stWlanInfo.szInterRoamPreDomainBuf = (int8_t *)testName;
    obj_st_stWlanInfo.logExtendInfo_len = bytes_len;
    obj_st_stWlanInfo.logExtendInfo = (int8_t *)testName;
    obj_st_stWlanInfo.szPMK_len = bytes_len;
    obj_st_stWlanInfo.szPMK = (int8_t *)testName;
    (void)memset_s(obj_st_stWlanInfo.szApMac, 6, 0, 6);
    (void)memset_s(obj_st_stWlanInfo.stAuthACIP_stIpAddr, 16, 0, 16);
    (void)memset_s(obj_st_stWlanInfo.stPeerACIP_stIpAddr, 16, 0, 16);
    obj_struct.stWlanInfo = &obj_st_stWlanInfo;
    obj_struct.stWlanInfo_flag = 1;

    cib_nac_ext_data_stWebAuth_t obj_st_stWebAuth;
    obj_st_stWebAuth.usPortal2PreFlag = uIdx;
    obj_st_stWebAuth.ucPortalVer = uIdx;
    obj_st_stWebAuth.ucSyncUserExitTimes = uIdx;
    obj_st_stWebAuth.ucPortalSvrIndex = uIdx;
    obj_st_stWebAuth.ucBakPortalSvrIndex = uIdx;
    obj_st_stWebAuth.ulWebAuthServerIP = uIdx;
    obj_st_stWebAuth.ulWebAuthVrf = uIdx;
    obj_st_stWebAuth.ulWebServerSrcIP = uIdx;
    obj_st_stWebAuth.ucWebUserIPType = uIdx;
    obj_st_stWebAuth.ucIfWeChatAuth = uIdx;
    obj_st_stWebAuth.stIPv6AddrWebAuth_len = bytes_len;
    obj_st_stWebAuth.stIPv6AddrWebAuth = (int8_t *)testName;
    obj_struct.stWebAuth = &obj_st_stWebAuth;
    obj_struct.stWebAuth_flag = 1;

    cib_nac_ext_data_stAuthSeq_t obj_st_stAuthSeq;
    obj_st_stAuthSeq.ucActiveIndex = uIdx;
    obj_st_stAuthSeq.ucTypeFailCount = uIdx;
    obj_st_stAuthSeq.astAuthTrack_count = array_size;
    obj_struct.stAuthSeq = &obj_st_stAuthSeq;
    obj_struct.stAuthSeq_flag = 1;

    cib_nac_ext_data_astAuthTrack_t obj_st_astAuthTrack;
    obj_st_astAuthTrack.ucAccessType = uIdx;
    obj_st_astAuthTrack.ucFailTimes = uIdx;
    obj_st_astAuthTrack.ucSuccFlag = uIdx;
    obj_st_stAuthSeq.astAuthTrack = &obj_st_astAuthTrack;

    cib_nac_ext_data_stNacPPPInfo_t obj_st_stNacPPPInfo;
    obj_st_stNacPPPInfo.ulVTIfIndex = uIdx;
    obj_st_stNacPPPInfo.ucNeedCutPPP = uIdx;
    obj_st_stNacPPPInfo.ulMss = uIdx;
    obj_st_stNacPPPInfo.ulPhyTxIf = uIdx;
    obj_st_stNacPPPInfo.szCallingNumber_len = bytes_len;
    obj_st_stNacPPPInfo.szCallingNumber = (int8_t *)testName;
    obj_st_stNacPPPInfo.szCalledNumber_len = bytes_len;
    obj_st_stNacPPPInfo.szCalledNumber = (int8_t *)testName;
    obj_struct.stNacPPPInfo = &obj_st_stNacPPPInfo;
    obj_struct.stNacPPPInfo_flag = 1;

    cib_nac_ext_data_stDeviceInfo_t obj_st_stDeviceInfo;
    obj_st_stDeviceInfo.stDhcpOption_len = bytes_len;
    obj_st_stDeviceInfo.stDhcpOption = (int8_t *)testName;
    obj_st_stDeviceInfo.szUAInfo_len = bytes_len;
    obj_st_stDeviceInfo.szUAInfo = (int8_t *)testName;
    obj_st_stDeviceInfo.szDeviceType_len = bytes_len;
    obj_st_stDeviceInfo.szDeviceType = (int8_t *)testName;
    (void)memset_s(obj_st_stDeviceInfo.szDeviceMac, 6, 0, 6);
    obj_struct.stDeviceInfo = &obj_st_stDeviceInfo;
    obj_struct.stDeviceInfo_flag = 1;

    GmcAsyncRequestDoneContextT context;
    context.userData = pUser_data;

    // insert
    context.insertCb = (GmcInsertVertexDoneT)call_back;

    ret = GmcExecuteAsync(stmt, &context);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute db_write_obj_async.");
        GmcFreeStmt(stmt);
        return ret;
    }

    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t ip4forward_read_by_lpm_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_vlan", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    db_ip4forward_key_t key_data = {0};
    key_data.vr_id = 0;
    key_data.vrf_index = 0;
    if (uIdx <= IP4_MAX_MASK_LEN_16) {
        key_data.dest_ip_addr = ((uIdx + 2) << 16);
        key_data.mask_len = ((16) & 0xff);
    } else if (uIdx > IP4_MAX_MASK_LEN_16 && uIdx <= IP4_MAX_MASK_LEN_24) {
        key_data.dest_ip_addr = ((uIdx + 2) << 8);
        key_data.mask_len = ((24) & 0xff);
    } else {
        key_data.dest_ip_addr = ((uIdx + 2));
        key_data.mask_len = ((32) & 0xff);
    }

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<db_if_cfg_key_t>;
    getSerialKeyLength(&s);

    ip4forward_struct_t read_data;

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(ip4forward_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    bool isFinish;
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute.");
        GmcFreeStmt(stmt);
        return ret;
    }
    uint32_t scan_times = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            GmcFreeStmt(stmt);
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &d);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to get vertex.");
            GmcFreeStmt(stmt);
            return ret;
        }
        scan_times++;
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t nhp_group_get_count_by_hashcluster_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint64_t ret_count;
    uint32_t uIdx = uDataCnt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "nhp_group", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "vrfid_hashcluster_key");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t app_source_id = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &app_source_id, sizeof(app_source_id));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint32_t vr_id = 0;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint32_t vrf_index = 0;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcGetVertexRecordCount(stmt, &ret_count);
    CHECK_OK_RET(ret, "db_get_table_record_count_by_key");

    GmcFreeStmt(stmt);

    return ret;
}

int32_t nhp_group_read_by_hashcluster_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t scan_times = 0;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_vlan", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    db_nhp_group_vrfid_hashcluster_key_t key_data = {0};
    key_data.app_source_id = 7415578;
    key_data.vr_id = 0;
    key_data.vrf_index = uIdx;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<db_if_cfg_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    nhp_group_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(nhp_group_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute.");
        return ret;
    }
    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &d);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to get vertex.");
            return ret;
        }
        scan_times++;
        if (scan_times >= max_count) {
            break;
        }
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t nhp_read_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_vlan", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    db_nhp_key_t key_data = {0};
    key_data.nhp_index = uIdx;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<db_nhp_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    nhp_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(nhp_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    bool isFinish;
    uint32_t scan_times = 0;
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute.");
        return ret;
    }
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &d);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to get vertex.");
            return ret;
        }
        scan_times++;
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t nhp_std_read_by_hashcluster_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t scan_times = 0;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_vlan", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    db_nhp_std_nhpindex_localhash_key_t key_data = {0};
    key_data.nhp_index = uIdx;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<db_nhp_std_nhpindex_localhash_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    nhp_std_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(nhp_std_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    bool isFinish;
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute.");
        return ret;
    }
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &d);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to get vertex.");
            return ret;
        }
        scan_times++;
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t ifm_ipv4_address_read_node_ipv4_address_list_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    uint32_t get_field_u32;
    uint8_t get_field_u8;
    int64_t get_field_64;
    uint64_t get_field_u64;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "ifm_ipv4_address", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "ifIndex");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t ifIndex = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ifIndex, sizeof(ifIndex));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");

    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }

        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "ifIndex", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "vrfIndex", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
        ret = GmcGetVertexPropertyByName(stmt, "time_stamp_create", &get_field_u64, sizeof(get_field_u64), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");

        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "ipv4_address_list", &childNode);
        CHECK_OK_RET(ret, "db_create_child_node dev");
        uint32_t array_count;
        ret = GmcNodeGetElementCount(childNode, &array_count);
        CHECK_OK_RET(ret, "db_child_count member");

        for (uint32_t i = 0; i < array_count; i++) {
            ret = GmcNodeGetElementByIndex(childNode, i, &childNode);
            ret = GmcNodeGetPropertyByName(childNode, "address", &get_field_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "db_get_trunk_member_member node");
            ret = GmcNodeGetPropertyByName(childNode, "masklen", &get_field_u8, sizeof(uint8_t), &isNull);
            CHECK_OK_RET(ret, "db_get_trunk_member_member node");
            ret = GmcNodeGetPropertyByName(childNode, "type", &get_field_u8, sizeof(uint8_t), &isNull);
            CHECK_OK_RET(ret, "db_get_trunk_member_member node");
        }
    }
    GmcFreeStmt(stmt);

    return ret;
}

int32_t fwm_nac_cib_base_info_read_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "fwm_nac_cib_base_info", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    db_fwm_nac_cib_base_info_key_t key_data = {0};
    key_data.global_cid = uIdx;
    key_data.instance_id = uIdx;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<db_fwm_nac_cib_base_info_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    fwm_nac_cib_base_info_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(fwm_nac_cib_base_info_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    bool isFinish;
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute.");
        return ret;
    }
    uint32_t scan_times = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &d);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to get vertex.");
            return ret;
        }
        scan_times++;
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    if (ret == GMERR_OK && scan_times == 0) {
        return GMERR_NO_DATA;
    }
    return ret;
}

int32_t bd_bind_vlan_read_by_localhash_func(uint32_t uDataCnt, uint32_t max_count, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;
    uint32_t scan_times = 0;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "bd_bind_vlan", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "vlan_index");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t vr_id = uDataCnt;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    uint16_t vlan_id = uDataCnt;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &vlan_id, sizeof(vlan_id));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    bd_bind_vlan_struct_t obj;
    GmcDeseriT deserStr;
    deserStr.deseriFunc = deSeriStructVertex;
    deserStr.version = GMC_SERI_VERSION_DEFAULT;
    deserStr.obj = (uint8_t *)&obj;
    deserStr.objSize = sizeof(obj);
    deserStr.userData = &ctx;

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute.");
        return ret;
    }
    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &deserStr);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to get vertex.");
            return ret;
        }
        scan_times++;
        if (scan_times >= max_count) {
            break;
        }
    }

    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t if_read_field_lagid_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    uint32_t get_field_u32;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "if_pk");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t ifindex = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");

    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "lagid", &get_field_u32, sizeof(get_field_u32), &isNull);
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");

    GmcFreeStmt(stmt);

    return ret;
}

int32_t if_read_field_forwardType_slot_id_chassis_id_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    uint32_t get_field_u32;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "if_pk");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t ifindex = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");

    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }

        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "forwardType", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");

        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "dev", &childNode);
        CHECK_OK_RET(ret, "db_create_child_node dev");

        ret = GmcNodeGetPropertyByName(childNode, "chassis_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
        ret = GmcNodeGetPropertyByName(childNode, "slot_id", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "db_get_if_dev node");
    }

    GmcFreeStmt(stmt);

    return ret;
}

int32_t nac_port_auth_read_field_down_ctl_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    uint32_t get_field_u32;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "nac_port_auth", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    ret = GmcSetIndexKeyName(stmt, "primaryKey");
    CHECK_OK_RET(ret, "gmc Set Index Key Name");
    uint32_t ifindex = uIdx;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));
    CHECK_OK_RET(ret, "gmc prepare stmt");
    ret = GmcExecute(stmt);
    CHECK_OK_RET(ret, "gmc GmcExecute");

    bool isFinish;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "down_ctl", &get_field_u32, sizeof(get_field_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName");
    }
    GmcFreeStmt(stmt);

    return ret;
}

int32_t fwm_nac_cib_auth_info_read_by_primary_func(uint32_t uDataCnt, connObj *connObj)
{
    GmcStmtT *stmt;
    int32_t ret = GMERR_OK;
    uint32_t uIdx = uDataCnt;

    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "fwm_nac_cib_auth_info", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    db_fwm_nac_cib_auth_info_key_t key_data = {0};
    key_data.global_cid = uIdx;
    key_data.instance_id = uIdx;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<db_fwm_nac_cib_auth_info_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    fwm_nac_cib_auth_info_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(fwm_nac_cib_auth_info_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    bool isFinish;
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set indexkey.");
        return ret;
    }
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to execute.");
        return ret;
    }
    uint32_t scan_times = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to fetch.");
            return ret;
        }
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexDeseri(stmt, &d);
        if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
            CHECK_OK_RET(ret, "db_read_obj_with_buf tagvlan_cfg_struct_t failed");
            GmcFreeStmt(stmt);
            return ret;
        }
        scan_times++;
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    if (ret == GMERR_OK && scan_times == 0) {
        return GMERR_NO_DATA;
    }
    return ret;
}

int32_t fwm_nac_cib_auth_info_sync_insert_once_func(uint32_t uDataCnt, uint32_t Isupdate, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t uIdx = uDataCnt;
    uint32_t uIdxup = uIdx + Isupdate;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "fwm_nac_cib_auth_info", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    fwm_nac_cib_auth_info_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    obj_struct.global_cid = uIdx;
    obj_struct.instance_id = uIdxup;
    obj_struct.acl_id = uIdx;
    obj_struct.active_acl_type = uIdx;
    obj_struct.class_id = uIdx;
    obj_struct.original_vlan = uIdx;
    obj_struct.inner_vlan = uIdx;
    obj_struct.auth_vlan = uIdx;
    obj_struct.auth_vlan_type = uIdx;
    obj_struct.up_flow_statics_flag = uIdx;
    obj_struct.down_flow_statics_flag = uIdx;
    obj_struct.up_dscp = uIdx;
    obj_struct.down_dscp = uIdx;
    obj_struct.up_8021p = uIdx;
    obj_struct.down_8021p = uIdx;
    obj_struct.ucl_group_id = uIdx;
    obj_struct.inbound_car_flag = uIdx;
    obj_struct.inbound_cir = uIdxup;
    obj_struct.inbound_cbs = uIdxup;
    obj_struct.inbound_pir = uIdx;
    obj_struct.inbound_pbs = uIdx;
    obj_struct.outbound_car_flag = uIdx;
    obj_struct.outbound_cir = uIdx;
    obj_struct.outbound_cbs = uIdx;
    obj_struct.outbound_pir = uIdx;
    obj_struct.outbound_pbs = uIdx;
    (void)memset_s(obj_struct.dacl_group_name, 65, 2, 65);
    ret = GmcSetVertexWithBuf(stmt, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set vertex with structure buffer.");
        GmcFreeStmt(stmt);
        return ret;
    }
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK && ret != GMERR_PRIMARY_KEY_VIOLATION) {
        CHECK_OK_RET(ret, "failed to execute structure vertex set.");
        GmcFreeStmt(stmt);
        return ret;
    }

    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t fwm_nac_cib_base_info_sync_insert_once_func(uint32_t uDataCnt, uint32_t Isupdate, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t uIdx = uDataCnt;
    uint32_t uIdxup = uIdx + Isupdate;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "fwm_nac_cib_base_info", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    fwm_nac_cib_base_info_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    obj_struct.global_cid = uIdx;
    obj_struct.instance_id = uIdxup;
    obj_struct.vr_id = uIdx;
    obj_struct.vsys_id = uIdx;
    obj_struct.user_type = uIdx;
    obj_struct.access_type = uIdx;
    obj_struct.auth_code_type = uIdx;
    obj_struct.remote_type = uIdx;
    obj_struct.if_index = uIdx;
    obj_struct.act_vlan = uIdx;
    obj_struct.bd_id = uIdx;
    obj_struct.vsi_id = uIdx;
    obj_struct.create_time = uIdx;
    obj_struct.ip_type = uIdx;
    obj_struct.ipv4_addr = uIdx;
    obj_struct.preauth_flag = uIdx;
    obj_struct.port_base = uIdxup;
    obj_struct.auth_flag = uIdxup;
    obj_struct.detect_type = uIdx;
    obj_struct.detect_period = uIdx;
    obj_struct.detect_timer = uIdx;
    obj_struct.is_eth_trunk_access = uIdx;

    ret = GmcSetVertexWithBuf(stmt, &s);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to set vertex with structure buffer.");
        return ret;
    }
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK && ret != GMERR_PRIMARY_KEY_VIOLATION) {
        CHECK_OK_RET(ret, "failed to execute structure vertex set.");
        GmcFreeStmt(stmt);
        return ret;
    }

    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

static int32_t StructureTestBatchInit(GmcBatchT **batch, GmcStmtT *stmt, GmcConnT *conn)
{
    GmcBatchOptionT batchOption;
    int32_t ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to batch option init.");
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to batch option set order.");
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to batch option set buf limit.");
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to batch prepare.");
        return ret;
    }
    ret = GmcBatchBindStmt(*batch, stmt);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "failed to batch bind stmt.");
        return ret;
    }
    return GMERR_OK;
}

int32_t if_fwd_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_fwd", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    if_fwd_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        obj_struct.ifindex = i;
        obj_struct.ifkey_type = 2;
        obj_struct.vrid = 0;
        obj_struct.resource_vsi = 0;
        obj_struct.ready_flags = 16647175;
        obj_struct.attr_flags = 0;
        obj_struct.update_time = 0;
        obj_struct.globallif = 0;
        obj_struct.globallif_ver = 0;
        j++;
        GmcResetStmt(stmt);
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }

        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");
            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// stp_vlan_stg_mb
int32_t stp_vlan_stg_mb_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;
    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "stp_vlan_stg_mb", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    stp_vlan_stg_mb_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        GmcResetStmt(stmt);
        j++;
        obj_struct.vrid = i;
        obj_struct.vlanId = (uint16_t)i;
        obj_struct.instance_id = 0;
        obj_struct.instance_index = 0;
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");
            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// stp_port_stg_status_mb
int32_t stp_port_stg_status_mb_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "stp_port_stg_status_mb", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    stp_port_stg_status_mb_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        GmcResetStmt(stmt);
        j++;
        obj_struct.vrid = i;
        obj_struct.ifindex = i;
        obj_struct.instId = (uint16_t)i;
        obj_struct.state = 1;
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }

CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

int32_t static_if_set_field_func(GmcStmtT *stmt, uint64_t uiVrIndex)
{
    int32_t ret = GMERR_OK;
    int8_t arr[64] = {0};
    memcpy(arr, (int8_t *)&uiVrIndex, 8);
    (void)snprintf((char *)arr, 32, "if_name_%.23lu", uiVrIndex);
    ret = GmcSetVertexProperty(stmt, "name", GMC_DATATYPE_FIXED, arr, 64);
    CHECK_OK_RET(ret, "db_set_if_name");

    uint32_t ifindex = uiVrIndex & (uint32_t)(~0);
    ret = GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));

    uint32_t vrid = 0;
    ret = GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    CHECK_OK_RET(ret, "db_set_if_vrid");

    uint32_t if_type = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "if_type", GMC_DATATYPE_UINT32, &if_type, sizeof(if_type));
    CHECK_OK_RET(ret, "db_set_if_if_type");

    uint32_t shutdown = 1;
    ret = GmcSetVertexProperty(stmt, "shutdown", GMC_DATATYPE_UINT32, &shutdown, sizeof(shutdown));
    CHECK_OK_RET(ret, "db_set_if_shutdown");

    uint32_t linkup = (uiVrIndex % 2);
    ret = GmcSetVertexProperty(stmt, "linkup", GMC_DATATYPE_UINT32, &linkup, sizeof(linkup));
    CHECK_OK_RET(ret, "db_set_if_linkup");

    uint32_t tbtp = uiVrIndex + 75;
    ret = GmcSetVertexProperty(stmt, "tbtp", GMC_DATATYPE_UINT32, &tbtp, sizeof(tbtp));
    CHECK_OK_RET(ret, "db_set_if_tbtp");

    uint32_t tb = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT32, &tb, sizeof(tb));
    CHECK_OK_RET(ret, "db_set_if_tb");

    uint32_t tp = uiVrIndex + 75;
    ret = GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT32, &tp, sizeof(tp));
    CHECK_OK_RET(ret, "db_set_if_tp");

    uint32_t port_switch = 0;
    ret = GmcSetVertexProperty(stmt, "port_switch", GMC_DATATYPE_UINT32, &port_switch, sizeof(port_switch));
    CHECK_OK_RET(ret, "db_set_if_tp");

    uint32_t forwardType = 4294967295;
    ret = GmcSetVertexProperty(stmt, "forwardType", GMC_DATATYPE_UINT32, &forwardType, sizeof(forwardType));
    CHECK_OK_RET(ret, "db_set_if_tp");

    ret = GmcSetVertexProperty(stmt, "macAddress", GMC_DATATYPE_FIXED, arr, 6);
    CHECK_OK_RET(ret, "db_set_if_macAddress");

    uint16_t ipv4_mtu = 1500;
    ret = GmcSetVertexProperty(stmt, "ipv4_mtu", GMC_DATATYPE_UINT16, &ipv4_mtu, sizeof(ipv4_mtu));
    CHECK_OK_RET(ret, "db_set_if_ipv4_mtu");

    uint16_t ipv4_enable = 0;
    ret = GmcSetVertexProperty(stmt, "ipv4_enable", GMC_DATATYPE_UINT16, &ipv4_enable, sizeof(ipv4_enable));
    CHECK_OK_RET(ret, "db_set_if_ipv4_enable");

    uint16_t ipv6_mtu = 0;
    ret = GmcSetVertexProperty(stmt, "ipv6_mtu", GMC_DATATYPE_UINT16, &ipv6_mtu, sizeof(ipv6_mtu));
    CHECK_OK_RET(ret, "db_set_if_ipv6_mtu");

    uint16_t ipv6_enable = 0;
    ret = GmcSetVertexProperty(stmt, "ipv6_enable", GMC_DATATYPE_UINT16, &ipv6_enable, sizeof(ipv6_enable));
    CHECK_OK_RET(ret, "db_set_if_ipv6_enable");

    uint32_t on_board = 1;
    ret = GmcSetVertexProperty(stmt, "on_board", GMC_DATATYPE_UINT32, &on_board, sizeof(on_board));
    CHECK_OK_RET(ret, "set_if_on_board");

    uint32_t lagid = 4294967295;
    ret = GmcSetVertexProperty(stmt, "lagid", GMC_DATATYPE_UINT32, &lagid, sizeof(lagid));
    CHECK_OK_RET(ret, "set_if_lagid");

    uint32_t hppsvcflg = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "hppsvcflg", GMC_DATATYPE_UINT32, &hppsvcflg, sizeof(hppsvcflg));
    CHECK_OK_RET(ret, "set_if_hppsvcflg");

    uint32_t error_down = 1;
    ret = GmcSetVertexProperty(stmt, "error_down", GMC_DATATYPE_UINT32, &error_down, sizeof(error_down));
    CHECK_OK_RET(ret, "set_if_error_down");

    uint64_t speed = 25000000000;
    ret = GmcSetVertexProperty(stmt, "speed", GMC_DATATYPE_UINT64, &speed, sizeof(speed));
    CHECK_OK_RET(ret, "set_if_speed");

    uint32_t link_protocol = 0;
    ret = GmcSetVertexProperty(stmt, "link_protocol", GMC_DATATYPE_UINT32, &link_protocol, sizeof(link_protocol));
    CHECK_OK_RET(ret, "set_if_link_protocol");

    uint32_t vrf_index = 0;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
    CHECK_OK_RET(ret, "set_if_vrf_index");

    uint32_t port_group_id = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "port_group_id", GMC_DATATYPE_UINT32, &port_group_id, sizeof(port_group_id));
    CHECK_OK_RET(ret, "set_if_port_group_id");

    uint32_t if_group_id = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "if_group_id", GMC_DATATYPE_UINT32, &if_group_id, sizeof(if_group_id));
    CHECK_OK_RET(ret, "set_if_if_group_id");

    uint32_t if_df = 0;
    ret = GmcSetVertexProperty(stmt, "if_df", GMC_DATATYPE_UINT32, &if_df, sizeof(if_df));
    CHECK_OK_RET(ret, "set_if_if_df");

    uint32_t encap_type = 255;
    ret = GmcSetVertexProperty(stmt, "encap_type", GMC_DATATYPE_UINT32, &encap_type, sizeof(encap_type));
    CHECK_OK_RET(ret, "set_if_encap_type");

    uint32_t is_subif = 0;
    ret = GmcSetVertexProperty(stmt, "is_subif", GMC_DATATYPE_UINT32, &is_subif, sizeof(is_subif));
    CHECK_OK_RET(ret, "set_if_is_subif");

    uint32_t mainifindex = 4294967295;
    ret = GmcSetVertexProperty(stmt, "mainifindex", GMC_DATATYPE_UINT32, &mainifindex, sizeof(mainifindex));
    CHECK_OK_RET(ret, "set_if_mainifindex");

    uint32_t logicTB = 0;
    ret = GmcSetVertexProperty(stmt, "logicTB", GMC_DATATYPE_UINT32, &logicTB, sizeof(logicTB));
    CHECK_OK_RET(ret, "set_if_logicTB");

    uint32_t logicTP = uiVrIndex + 75;
    ret = GmcSetVertexProperty(stmt, "logicTP", GMC_DATATYPE_UINT32, &logicTP, sizeof(logicTP));
    CHECK_OK_RET(ret, "set_if_logicTP");

    uint32_t vlandomain = uiVrIndex + 75;
    ret = GmcSetVertexProperty(stmt, "vlandomain", GMC_DATATYPE_UINT32, &vlandomain, sizeof(vlandomain));
    CHECK_OK_RET(ret, "set_if_vlandomain");

    uint32_t coreId = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "coreId", GMC_DATATYPE_UINT32, &coreId, sizeof(coreId));
    CHECK_OK_RET(ret, "set_if_coreId");

    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "dev", &childNode);
    CHECK_OK_RET(ret, "set_if_coreId");

    uint32_t chassis_id = uiVrIndex;
    ret = GmcNodeSetPropertyByName(childNode, "chassis_id", GMC_DATATYPE_UINT32, &chassis_id, sizeof(chassis_id));
    CHECK_OK_RET(ret, "set_if_chassis_id");

    ret = GmcGetChildNode(stmt, "l2", &childNode);
    CHECK_OK_RET(ret, "set_if_coreId");

    uint32_t vlan_id = uiVrIndex;
    ret = GmcNodeSetPropertyByName(childNode, "vlan_id", GMC_DATATYPE_UINT32, &vlan_id, sizeof(vlan_id));
    CHECK_OK_RET(ret, "set_if_vlan_id");

    ret = GmcGetChildNode(stmt, "ifm", &childNode);
    CHECK_OK_RET(ret, "set_if_coreId");

    uint32_t simple_name = uiVrIndex;
    ret = GmcNodeSetPropertyByName(childNode, "simple_name", GMC_DATATYPE_UINT32, &simple_name, sizeof(simple_name));
    CHECK_OK_RET(ret, "set_if_vlan_id");

    ret = GmcGetChildNode(stmt, "port", &childNode);
    CHECK_OK_RET(ret, "set_if_coreId");

    uint32_t duplex = uiVrIndex;
    ret = GmcNodeSetPropertyByName(childNode, "duplex", GMC_DATATYPE_UINT32, &duplex, sizeof(duplex));
    CHECK_OK_RET(ret, "set_if_vlan_id");

    // set time in fields for sub delay test, ./bin -v1
    uint32_t ipv4mss = 4294967295;
    ret = GmcSetVertexProperty(stmt, "ipv4mss", GMC_DATATYPE_UINT32, &ipv4mss, sizeof(ipv4mss));
    CHECK_OK_RET(ret, "set_if_coreId");

    uint32_t ipv6mss = 4294967295;
    ret = GmcSetVertexProperty(stmt, "ipv6mss", GMC_DATATYPE_UINT32, &ipv6mss, sizeof(ipv6mss));
    CHECK_OK_RET(ret, "set_if_coreId");

    return ret;
}

int32_t if_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;

    ret = GmcPrepareStmtByLabelName(stmt, "if", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "GmcBatchOptionInit failed");
        return ret;
    }

    ret = GmcBatchPrepare(connObj->connS, &batchOption, &batch);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "batch prepare failed");
        return ret;
    }

    uint32_t wr_loop = 50;
    uint32_t end = uDataCnt - 1;
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(stmt);
        ret = GmcSetIndexKeyName(stmt, "if_pk");
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "GmcSetIndexKeyName failed");
            return ret;
        }
        uint32_t ifindex = i & (uint32_t)(~0);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifindex, sizeof(ifindex));
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "GmcSetIndexKeyValue failed");
            return ret;
        }
        ret = static_if_set_field_func(stmt, i);
        if (ret) {
            CHECK_OK_RET(ret, "static_if_set_field_func");
            break;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        return GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    return ret;
}

// if_vlan
int32_t if_vlan_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_vlan", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    if_vlan_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(stmt);
        obj_struct.if_index = i;
        obj_struct.vlan_id = i;
        obj_struct.vr_id = 0;
        obj_struct.app_source_id = 0;
        obj_struct.app_serial_id = 0;
        obj_struct.app_obj_id = 0;

        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        return GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// cfg_bd_bind_vlan
int32_t cfg_bd_bind_vlan_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "cfg_bd_bind_vlan", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    cfg_bd_bind_vlan_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(stmt);
        obj_struct.vr_id = i;
        obj_struct.bd_id = i;
        obj_struct.vlan_id = i;
        obj_struct.if_index = i;
        obj_struct.ver_no = 0;
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    deSeriFreeDynMem(&ctx, true);
    return ret;
}

// vlan_cfg
int32_t vlan_cfg_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int bool_type = rand() % 2;
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "vlan_cfg", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    tagvlan_cfg_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        GmcResetStmt(stmt);
        obj_struct.vrid = 0;
        obj_struct.vlan_id = i;
        obj_struct.admin_state = (bool_type == 0) ? 0 : 1;
        obj_struct.static_enable = 0;
        obj_struct.transparent = 0;
        obj_struct.vlan_type = 0;
        obj_struct.app_source_id = 0;
        obj_struct.app_serial_id = 0;
        obj_struct.app_obj_id = 0;
        obj_struct.app_version = 0;
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");
            sum_succ += success_num;
            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    deSeriFreeDynMem(&ctx, true);
    GmcFreeStmt(stmt);
    return ret;
}

// if_cfg
int32_t if_cfg_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_cfg", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    if_cfg_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        GmcResetStmt(stmt);
        j++;
        (void)snprintf((char *)obj_struct.name, 64, "if_cfg_name_%d", i);
        obj_struct.ifindex = i;
        obj_struct.vrid = 0;
        obj_struct.if_type = i;
        obj_struct.shutdown = 1;
        obj_struct.port_switch = 0;
        obj_struct.forward_type = 3;
        obj_struct.ipv4_mtu = 1500;
        obj_struct.ipv6_mtu = 0;
        obj_struct.on_board = 0;
        obj_struct.lagid = 4294967295;
        obj_struct.main_ifindex = 0;
        obj_struct.error_down = 0;
        obj_struct.dev_id = i;
        obj_struct.chassis_id = 1;
        obj_struct.slot_id = 1;
        obj_struct.card_id = 0;
        obj_struct.unit_id = 1;
        obj_struct.port_id = i;
        obj_struct.trunk_id = i;
        obj_struct.vlan_id = i;
        obj_struct.l2_portindex = i;
        obj_struct.vsi = 0;
        obj_struct.tunnel_id = 0;
        obj_struct.field_flags = 23;
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    deSeriFreeDynMem(&ctx, true);
    GmcFreeStmt(stmt);
    return ret;
}

status_t static_trunk_member_set_field_func(GmcStmtT *stmt, uint64_t uiVrIndex)
{
    status_t ret = GMERR_OK;
    uint32_t vrid = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "db_set_trunk_member_vrid");

    uint32_t ifindex = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &ifindex, sizeof(ifindex));
    CHECK_OK_RET(ret, "db_set_trunk_member_ifindex");

    uint32_t trunk_id = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "trunk_id", GMC_DATATYPE_UINT32, &trunk_id, sizeof(trunk_id));
    CHECK_OK_RET(ret, "db_set_trunk_member_trunk_id");

    uint32_t hash_mode = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "hash_mode", GMC_DATATYPE_UINT32, &hash_mode, sizeof(hash_mode));
    CHECK_OK_RET(ret, "db_set_trunk_member_hash_mode");

    uint32_t least_active_num = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(
        stmt, "least_active_num", GMC_DATATYPE_UINT32, &least_active_num, sizeof(least_active_num));
    CHECK_OK_RET(ret, "db_set_trunk_member_least_active_num");

    uint32_t app_source_id = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &app_source_id, sizeof(app_source_id));
    CHECK_OK_RET(ret, "db_set_trunk_member_app_source_id");

    uint32_t app_serial_id = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "app_serial_id", GMC_DATATYPE_UINT32, &app_serial_id, sizeof(app_serial_id));
    CHECK_OK_RET(ret, "db_set_trunk_member_app_serial_id");

    uint64_t app_obj_id = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &app_obj_id, sizeof(app_obj_id));
    CHECK_OK_RET(ret, "db_set_trunk_member_app_obj_id");

    uint32_t app_version = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &app_version, sizeof(app_version));
    CHECK_OK_RET(ret, "db_set_trunk_member_app_version");

    uint32_t odaflag = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "odaflag", GMC_DATATYPE_UINT32, &odaflag, sizeof(odaflag));
    CHECK_OK_RET(ret, "db_set_trunk_member_odaflag");

    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "member", &childNode);
    CHECK_OK_RET(ret, "set_if_coreId");
    return ret;
}

int32_t trunk_member_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "trunk_member", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "GmcBatchOptionInit failed");
        return ret;
    }

    ret = GmcBatchPrepare(connObj->connS, &batchOption, &batch);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "batch prepare failed");
        return ret;
    }

    uint32_t wr_loop = 50;
    uint32_t end = uDataCnt - 1;
    for (uint32_t i = 0; i < uDataCnt; i++) {
        GmcResetStmt(stmt);
        j++;
        ret = static_trunk_member_set_field_func(stmt, i);
        if (ret) {
            CHECK_OK_RET(ret, "static_trunk_member_set_field_func");
            break;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "db_batch_add_oper");
            j = 0;
            break;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        return GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    return ret;
}

int32_t ip4forward_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "ip4forward", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");
    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    ip4forward_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        obj_struct.vr_id = 0;
        obj_struct.vrf_index = 0;
        obj_struct.dest_ip_addr = 0;
        obj_struct.mask_len = 32;
        obj_struct.nhp_group_flag = 1;
        obj_struct.qos_profile_id = 0;
        obj_struct.primary_label = 4294967295;
        obj_struct.attribute_id = 0;
        obj_struct.nhp_group_id = (i & (uint8_t)(~0));
        obj_struct.path_flags = (i & (uint32_t)(~0));
        obj_struct.flags = 1;
        obj_struct.status_high_prio = 1;
        obj_struct.status_normal_prio = 1;
        obj_struct.errcode_high_prio = 0;
        obj_struct.errcode_normal_prio = 0;
        obj_struct.app_source_id = (i & (uint32_t)(~0));
        obj_struct.table_smooth_id = (i & (uint32_t)(~0));
        obj_struct.app_obj_id = 0;
        obj_struct.app_version = 0;
        obj_struct.trace = 0;
        obj_struct.route_flags = 2;
        obj_struct.next_type = 0;
        obj_struct.reserved = 0;
        obj_struct.time_stamp_create = (i & (int64_t)(~0));
        obj_struct.time_stamp_smooth = (i & (int64_t)(~0));
        if (i <= IP4_MAX_MASK_LEN_16) {
            obj_struct.dest_ip_addr = ((i + 2) << 16);
            obj_struct.mask_len = ((16) & 0xff);
        } else if (i > IP4_MAX_MASK_LEN_16 && i <= IP4_MAX_MASK_LEN_24) {
            obj_struct.dest_ip_addr = ((i + 2) << 8);
            obj_struct.mask_len = ((24) & 0xff);
        } else {
            obj_struct.dest_ip_addr = ((i + 2));
            obj_struct.mask_len = ((32) & 0xff);
        }
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    deSeriFreeDynMem(&ctx, true);
    GmcFreeStmt(stmt);
    return ret;
}

// nhp_group
int32_t nhp_group_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "nhp_group", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    nhp_group_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        obj_struct.nhp_group_id = i;
        obj_struct.vr_id = 0;
        obj_struct.vrf_index = 0;
        obj_struct.flags = 1;
        obj_struct.ref_count = i;
        obj_struct.new_vrf = 0;
        obj_struct.nhp_number = 1;
        obj_struct.nhp_type_high_prio = 0;
        obj_struct.nhp_type_normal_prio = 0;
        obj_struct.status_high_prio = 1;
        obj_struct.status_normal_prio = 1;
        obj_struct.app_source_id = i;
        obj_struct.table_smooth_id = i;
        obj_struct.time_stamp_update = i;
        obj_struct.oper_bitmap = 8;
        obj_struct.attr_flag = 0;
        obj_struct.time_stamp_app = i;
        obj_struct.time_stamp_svc = i;
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    deSeriFreeDynMem(&ctx, true);
    GmcFreeStmt(stmt);
    return ret;
}

// nhp
int32_t nhp_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "nhp", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    nhp_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        GmcResetStmt(stmt);
        j++;
        obj_struct.nhp_index = i;
        obj_struct.vr_id = 0;
        obj_struct.vrf_index = 0;
        obj_struct.origin_nhp = 0;
        obj_struct.nhp_flag = 0;
        obj_struct.nhp_num = 1;
        obj_struct.ref_cnt = i;
        obj_struct.flags = 1;
        obj_struct.iid_flags = 16;
        obj_struct.app_source_id = 7415578;
        obj_struct.table_smooth_id = 4973;
        obj_struct.app_obj_id = 0;
        obj_struct.app_version = 0;
        obj_struct.time_stamp_app = i;
        obj_struct.time_stamp_svc = i;
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }

        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");
            sum_succ += success_num;
            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    deSeriFreeDynMem(&ctx, true);
    GmcFreeStmt(stmt);
    return ret;
}

// nhp_std
int32_t nhp_std_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "nhp_std", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    nhp_std_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        GmcResetStmt(stmt);
        j++;
        obj_struct.nhp_index = i;
        obj_struct.next_hop = 2130706433;
        obj_struct.out_if_index = i;
        obj_struct.vr_id = 0;
        obj_struct.vrf_index = 0;
        obj_struct.flags = 1;
        obj_struct.if_type = 17;
        obj_struct.iid_flags = 16;
        obj_struct.work_if_index = 4294967295;
        obj_struct.status_high_prio = 1;
        obj_struct.status_normal_prio = 1;
        obj_struct.errcode_high_prio = 0;
        obj_struct.errcode_normal_prio = 0;
        obj_struct.app_source_id = 7415578;
        obj_struct.group_smooth_id = 0;
        obj_struct.app_obj_id = 0;
        obj_struct.app_version = 0;
        obj_struct.attr_flag = 0;
        obj_struct.fwd_if_type = 65535;
        obj_struct.reserved = i;
        obj_struct.time_stamp_app = i;
        obj_struct.time_stamp_svc = i;
        (void)memset_s(obj_struct.svc_ctx_high_prio, 16, 2, 16);
        (void)memset_s(obj_struct.svc_ctx_normal_prio, 16, 2, 16);
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    deSeriFreeDynMem(&ctx, true);
    GmcFreeStmt(stmt);
    return ret;
}

status_t static_ifm_ipv4_address_set_field_func(GmcStmtT *stmt, uint64_t uiVrIndex)
{
    status_t ret = GMERR_OK;

    uint32_t ifIndex = (uiVrIndex & (uint32_t)(~0));
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_UINT32, &ifIndex, sizeof(ifIndex));
    CHECK_OK_RET(ret, "db_set_ifm_ipv4_address_ifIndex");

    uint32_t vrfIndex = 0;
    ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
    CHECK_OK_RET(ret, "db_set_ifm_ipv4_address_vrfIndex");

    uint64_t time = (uiVrIndex & (int64_t)(~0));
    ret = GmcSetVertexProperty(stmt, "time_stamp_create", GMC_DATATYPE_TIME, &time, sizeof(time));
    CHECK_OK_RET(ret, "db_set_ifm_ipv4_address_time_stamp_create");
    return ret;
}

int32_t ifm_ipv4_address_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "ifm_ipv4_address", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "GmcBatchOptionInit failed");
        return ret;
    }

    ret = GmcBatchPrepare(connObj->connS, &batchOption, &batch);
    if (ret != GMERR_OK) {
        CHECK_OK_RET(ret, "batch prepare failed");
        return ret;
    }

    uint32_t wr_loop = 50;
    uint32_t end = uDataCnt - 1;
    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        ret = static_ifm_ipv4_address_set_field_func(stmt, i);
        if (ret) {
            CHECK_OK_RET(ret, "static_ifm_ipv4_address_set_field_func");
            break;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }
        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        return GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    return ret;
}

// bd_bind_vlan
int32_t bd_bind_vlan_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "bd_bind_vlan", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;
    bd_bind_vlan_struct_t obj_struct;
    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        j++;
        obj_struct.vr_id = i;
        obj_struct.bd_id = i;
        obj_struct.vlan_id = i;
        obj_struct.bd_type = i;
        obj_struct.complete_data = i;
        obj_struct.if_index = i;
        obj_struct.vsi_id = i;
        obj_struct.ver_no = 0;
        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }

        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");

            sum_succ += success_num;
            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    deSeriFreeDynMem(&ctx, true);
    GmcFreeStmt(stmt);
    return ret;
}

// nac_port_auth
int32_t nac_port_auth_batch_insert_func(uint32_t uDataCnt, connObj *connObj)
{
    int32_t ret = GMERR_OK;
    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(connObj->connS, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "nac_port_auth", GMC_OPERATION_INSERT);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, connObj->connS);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = stmt;

    GmcSeriT s;
    nac_port_auth_struct_t obj_struct;
    s.seriFunc = SeriStructVertex;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.obj = (uint8_t *)&obj_struct;
    s.userData = &ctx;
    getSerialVertexLength(&s);

    for (uint32_t i = 0; i < uDataCnt; i++) {
        GmcResetStmt(stmt);
        j++;
        obj_struct.ifindex = i;
        obj_struct.iftype = i;
        obj_struct.auth_methods = i;
        obj_struct.port_based = i;
        obj_struct.pkt_map = i;
        obj_struct.down_ctl = i;
        obj_struct.portbase_authened = i;

        ret = GmcSetVertexWithBuf(stmt, &s);
        if (ret != GMERR_OK) {
            CHECK_OK_RET(ret, "failed to set vertex with buf.");
            goto CLEAR2;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }

        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");
            sum_succ += success_num;
            j = 0;
        }

        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        GmcBatchDestroy(batch);
    }
    deSeriFreeDynMem(&ctx, true);
    GmcFreeStmt(stmt);
    return ret;
}

#endif  // GMDBV5_PERF_SCENE_OPER_H
