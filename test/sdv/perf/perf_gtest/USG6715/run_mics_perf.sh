# 新微观
cd /root/gmdb_benchmark_arm64/

#IFM业务IF表--DML：
# 建表if_r21_1
gmimport  -c vschema -f   /root/gmdb_benchmark_arm64/schema_file/if_r21_1.gmjson

echo "null null null null " > sub_1.txt
echo "if ipv6_enable null 0" >> sub_1.txt
echo "if ipv4_enable null 0" >> sub_1.txt

#主键读（读所有字段，包含根节点和子节点，不缓存object） 不带-R
for k in $(seq 3)
do
./gmdb_benchmark_static -p 1 -t 30 -a 8 -l 0 -L 128000 -m 2 -f51 -j 1  -b 50
gmsysview count
taskset 2 ./gmdb_benchmark_static_non_keep_obj -p 1 -t 15 -a 3 -l 0 -L 128000 -m 2 -f51 -v 1  | tee /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes.txt
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes.txt >> /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes_tmp.txt
done
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes_tmp.txt > /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes.txt
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes_tmp.txt |grep Ops_Read |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Read_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes_tmp.txt |grep Ops_Read |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Read_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes.txt
rm -rf /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes_tmp.txt
cat  /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_allNodes.txt

#FM业务IF表--订阅推送：
# 建表if_r21_1
wait_sub_process_exit()
{
        local sub_process_name=$1
        local sub_cli_pid=`pidof ${sub_process_name}`
        if [ "X${sub_cli_pid}" = "X" ];then
                return
        fi

    for i in $(seq 180)
        do
                if [ ! `pidof -s ${sub_process_name}` ];then
                        break
                fi
                sleep 1
        done
        kill -9  `pidof ${sub_process_name}`
}

gmimport  -c vschema -f   /root/gmdb_benchmark_arm64/schema_file/if_r21_1.gmjson

echo "null null null null " > tmpSub.txt
echo "if ipv6_enable null 0" >> tmpSub.txt
echo "if ipv4_enable null 0" >> tmpSub.txt

#8订阅吞吐（条件订阅，8通道）
for k in $(seq 3)
do
echo ">>>>>>>>> $k"
rm -f sub_*.txt
./gmdb_benchmark_static -p 1 -t 30 -a 10 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
gmsysview count
./static_sub_client_multithreading_epoll -s 51 -t 15 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f if_type -v 2 | tee sub_1.txt &
./static_sub_client_multithreading_epoll -s 51 -t 15 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null | tee sub_2.txt &
./static_sub_client_multithreading_epoll -s 51 -t 15 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null | tee sub_3.txt &
./static_sub_client_multithreading_epoll -s 51 -t 15 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_mtu -v null | tee sub_4.txt &
./static_sub_client_multithreading_epoll -s 51 -t 15 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f macAddress -v null | tee sub_5.txt &
./static_sub_client_multithreading_epoll -s 51 -t 15 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null | tee sub_6.txt &
./static_sub_client_multithreading_epoll -s 51 -t 15 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null | tee sub_7.txt &
./static_sub_client_multithreading_epoll -s 51 -t 15 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -F tmpSub.txt  | tee sub_8.txt  &
sleep 4
#批写
./gmdb_benchmark_static -p 1 -t 30 -a 8 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50  
wait_sub_process_exit static_sub_client_multithreading_epoll
sleep 3
cat sub_*.txt|grep ops > /opt/vrpv8/home/<USER>/if_8_ConditionSub_8_Channel_tmp_${k}.txt
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}'
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}' >> /opt/vrpv8/home/<USER>/if_8_ConditionSub_8_Channel_tmp_${k}.txt
echo ">>>>>>>>> if_8_ConditionSub_8_Channel_tmp_${k}.txt"
sleep 3
done
cat /opt/vrpv8/home/<USER>/if_8_ConditionSub_8_Channel_tmp_*.txt|grep sum_sub_ops |awk '{sum+=$2; count++} END {if (count>0) print "sum_sub_ops_Avg:", sum/count}' > /opt/vrpv8/home/<USER>/if_8_ConditionSub_8_Channel.txt
rm -rf /opt/vrpv8/home/<USER>/if_8_ConditionSub_8_Channel_tmp_*.txt
cat  /opt/vrpv8/home/<USER>/if_8_ConditionSub_8_Channel.txt

#8订阅吞吐（全量订阅，8通道）
for k in $(seq 3)
do
rm -f sub_*.txt
./gmdb_benchmark_static -p 1 -t 30 -a 10 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
gmsysview count
./static_sub_client_multithreading_epoll -s 51 -t 10 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 | tee sub_1.txt &
./static_sub_client_multithreading_epoll -s 51 -t 10 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 | tee sub_2.txt &
./static_sub_client_multithreading_epoll -s 51 -t 10 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 | tee sub_3.txt  &
./static_sub_client_multithreading_epoll -s 51 -t 10 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 | tee sub_4.txt  &
./static_sub_client_multithreading_epoll -s 51 -t 10 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 | tee sub_5.txt  & 
./static_sub_client_multithreading_epoll -s 51 -t 10 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 | tee sub_6.txt  &
./static_sub_client_multithreading_epoll -s 51 -t 10 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 | tee sub_7.txt  &
./static_sub_client_multithreading_epoll -s 51 -t 10 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 | tee sub_8.txt  &
sleep 4
./gmdb_benchmark_static -p 1 -t 30 -a 8 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
wait_sub_process_exit static_sub_client_multithreading_epoll
sleep 3
cat sub_*.txt|grep ops > /opt/vrpv8/home/<USER>/if_8_FullSub_8_Channel_tmp_${k}.txt
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}'
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}' >> /opt/vrpv8/home/<USER>/if_8_FullSub_8_Channel_tmp_${k}.txt
echo ">>>>>>>>> if_8_FullSub_8_Channel_tmp_${k}.txt"
sleep 3
done
cat /opt/vrpv8/home/<USER>/if_8_FullSub_8_Channel_tmp_*.txt|grep sum_sub_ops |awk '{sum+=$2; count++} END {if (count>0) print "sum_sub_ops_Avg:", sum/count}' > /opt/vrpv8/home/<USER>/if_8_FullSub_8_Channel.txt
rm -rf /opt/vrpv8/home/<USER>/if_8_FullSub_8_Channel_tmp_*.txt
cat  /opt/vrpv8/home/<USER>/if_8_FullSub_8_Channel.txt

#16订阅吞吐（条件订阅，16通道）
for k in $(seq 3)
do
./gmdb_benchmark_static -p 1 -t 30 -a 10 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
rm -f sub_*.txt
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f if_type -v 2  |tee sub_1.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  |tee sub_2.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null  |tee sub_3.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_mtu -v null  |tee sub_4.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f macAddress -v null  |tee sub_5.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null  |tee sub_6.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null  |tee sub_7.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -F tmpSub.txt   |tee sub_8.txt &

./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  |tee sub_9.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  |tee sub_10.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  |tee sub_11.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  |tee sub_12.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null  |tee sub_13.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null  |tee sub_14.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null  |tee sub_15.txt &
./static_sub_client_multithreading_epoll -s 51 -t 20 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  |tee sub_16.txt &
sleep 8
./gmdb_benchmark_static -p 1 -t 30 -a 8 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
wait_sub_process_exit static_sub_client_multithreading_epoll
sleep 3
cat sub_*.txt|grep ops > /opt/vrpv8/home/<USER>/if_16_ConditionSub_16_Channel_tmp_${k}.txt
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}'
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}' >> /opt/vrpv8/home/<USER>/if_16_ConditionSub_16_Channel_tmp_${k}.txt
echo ">>>>>>>>> if_16_ConditionSub_16_Channel_tmp_${k}.txt"
sleep 3
done
cat /opt/vrpv8/home/<USER>/if_16_ConditionSub_16_Channel_tmp_*.txt|grep sum_sub_ops |awk '{sum+=$2; count++} END {if (count>0) print "sum_sub_ops_Avg:", sum/count}' > /opt/vrpv8/home/<USER>/if_16_ConditionSub_16_Channel.txt
rm -rf /opt/vrpv8/home/<USER>/if_16_ConditionSub_16_Channel_tmp_*.txt
cat /opt/vrpv8/home/<USER>/if_16_ConditionSub_16_Channel.txt

#32订阅吞吐（32通道）
for k in $(seq 3)
do
./gmdb_benchmark_static -p 1 -t 30 -a 10 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
rm -f sub_*.txt
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f if_type -v 2  |tee sub_1.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  |tee sub_2.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null  |tee sub_3.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_mtu -v null  |tee sub_4.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f macAddress -v null  |tee sub_5.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null  |tee sub_6.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null  |tee sub_7.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -F tmpSub.txt   |tee sub_8.txt &

./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  |tee sub_9.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  |tee sub_10.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  |tee sub_11.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  |tee sub_12.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null  |tee sub_13.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null  |tee sub_14.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null  |tee sub_15.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  |tee sub_16.txt &

./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f if_type -v 2  |tee sub_17.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  |tee sub_18.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null  |tee sub_19.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_mtu -v null  |tee sub_20.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f macAddress -v null  |tee sub_21.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null  |tee sub_22.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null  |tee sub_23.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -F tmpSub.txt   |tee sub_24.txt &

./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  |tee sub_25.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  |tee sub_26.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  |tee sub_27.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  |tee sub_28.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null  |tee sub_29.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null  |tee sub_30.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null  |tee sub_31.txt &
./static_sub_client_multithreading_epoll -s 51 -t 30 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  |tee sub_32.txt &
sleep 15
./gmdb_benchmark_static -p 1 -t 30 -a 8 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
wait_sub_process_exit static_sub_client_multithreading_epoll
sleep 3
cat sub_*.txt|grep ops > /opt/vrpv8/home/<USER>/if_32_ConditionSub_32_Channel_tmp_${k}.txt
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}'
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}' >> /opt/vrpv8/home/<USER>/if_32_ConditionSub_32_Channel_tmp_${k}.txt
echo ">>>>>>>>> if_32_ConditionSub_32_Channel_tmp_${k}.txt"
done
cat /opt/vrpv8/home/<USER>/if_32_ConditionSub_32_Channel_tmp_*.txt|grep sum_sub_ops |awk '{sum+=$2; count++} END {if (count>0) print "sum_sub_ops_Avg:", sum/count}' > /opt/vrpv8/home/<USER>/if_32_ConditionSub_32_Channel.txt
cat  /opt/vrpv8/home/<USER>/if_32_ConditionSub_32_Channel.txt
rm -rf /opt/vrpv8/home/<USER>/if_32_ConditionSub_32_Channel_tmp_*.txt

# 64订阅 （32通道 64订阅）     （1个进程里2个订阅，并且在一个订阅连接上）
for k in $(seq 3)
do
./gmdb_benchmark_static -p 1 -t 30 -a 10 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
rm -f sub_*.txt
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f if_type -v 2 -N 2  -L 1  |tee sub_1.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  -N 2  -L 1  |tee sub_2.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null    -N 2  -L 1  |tee sub_3.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_mtu -v null    -N 2  -L 1  |tee sub_4.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f macAddress -v null    -N 2  -L 1  |tee sub_5.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null    -N 2  -L 1  |tee sub_6.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null    -N 2  -L 1  |tee sub_7.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -F tmpSub.txt  -N 2  -L 1   |tee sub_8.txt &

./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  -N 2  -L 1   |tee sub_9.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  -N 2  -L 1  |tee sub_10.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  -N 2  -L 1  |tee sub_11.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  -N 2  -L 1  |tee sub_12.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null  -N 2  -L 1  |tee sub_13.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null  -N 2  -L 1  |tee sub_14.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null  -N 2  -L 1  |tee sub_15.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  -N 2  -L 1  |tee sub_16.txt &

./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f if_type -v 2 -N 2  -L 1  |tee sub_17.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  -N 2  -L 1  |tee sub_18.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null    -N 2  -L 1  |tee sub_19.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_mtu -v null    -N 2  -L 1  |tee sub_20.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f macAddress -v null    -N 2  -L 1  |tee sub_21.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null    -N 2  -L 1  |tee sub_22.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null    -N 2  -L 1  |tee sub_23.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -F tmpSub.txt  -N 2  -L 1   |tee sub_24.txt &

./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  -N 2  -L 1   |tee sub_25.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f port_switch -v null  -N 2  -L 1  |tee sub_26.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  -N 2  -L 1  |tee sub_27.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f forwardType -v null  -N 2  -L 1  |tee sub_28.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f on_board -v null  -N 2  -L 1  |tee sub_29.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv6_enable -v null  -N 2  -L 1  |tee sub_30.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f ipv4_enable -v null  -N 2  -L 1  |tee sub_31.txt &
./static_sub_client_multithreading_epoll -s 51 -t 50 -g 64 -c 10000000 -m 2 -e 0x17 -p 0x1000 -n if -f linkup -v null  -N 2  -L 1  |tee sub_32.txt &
sleep 20
./gmdb_benchmark_static -p 1 -t 30 -a 8 -l 0 -L 128000 -m 2 -f51 -j 1  -s 10000000 -b 50
wait_sub_process_exit static_sub_client_multithreading_epoll
sleep 3
cat sub_*.txt|grep ops > /opt/vrpv8/home/<USER>/if_64_ConditionSub_32_Channel_tmp_${k}.txt
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}'
cat sub_*.txt|grep ops|awk '{sum+=$11} END {print "sum_sub_ops:", sum}' >> /opt/vrpv8/home/<USER>/if_64_ConditionSub_32_Channel_tmp_${k}.txt
echo ">>>>>>>>> if_64_ConditionSub_32_Channel_tmp_${k}.txt"
done
cat /opt/vrpv8/home/<USER>/if_64_ConditionSub_32_Channel_tmp_*.txt|grep sum_sub_ops |awk '{sum+=$2; count++} END {if (count>0) print "sum_sub_ops_Avg:", sum/count}' > /opt/vrpv8/home/<USER>/if_64_ConditionSub_32_Channel.txt
cat /opt/vrpv8/home/<USER>/if_64_ConditionSub_32_Channel.txt
rm -rf /opt/vrpv8/home/<USER>/if_64_ConditionSub_32_Channel_tmp_*.txt

#FIB业务ip4forward表--ip4forward：
# 建表
cp /opt/vrpv8/home/<USER>/root/gmdb_benchmark_arm64/schema_file/
gmimport -c vschema -f  /root/gmdb_benchmark_arm64/schema_file/ip4forward_r21_1.gmjson

#异步批写 Update （随机key更新，批量数120）
# ip4forward 异步批写 Update  (随机key)
for k in $(seq 3)
do
./gmdb_benchmark_static  -p 1 -t 120 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
./gmdb_benchmark_static_async -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
taskset 2 ./gmdb_benchmark_static_async_random -p 1 -t 120 -a 9 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120 | tee /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey.txt
cat /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey.txt >> /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey_tmp.txt
done
cat /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey_tmp.txt > /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey.txt
cat /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey_tmp.txt |grep Ops_batch_update |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_update_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey_tmp.txt |grep Ops_batch_update |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_update_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey.txt
cat /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey.txt
rm -rf /opt/vrpv8/home/<USER>/fib_async_batch_update_randomKey_tmp.txt

#同步批写 Update （随机key更新，批量数120）
# ip4forward 同步批写 Update  随机key
for k in $(seq 3)
do
./gmdb_benchmark_static  -p 1 -t 120 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
./gmdb_benchmark_static_async -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
taskset 2 ./gmdb_benchmark_static_random -p 1 -t 120 -a 9 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120 | tee /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey.txt
cat /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey.txt >> /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey_tmp.txt
done
cat /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey_tmp.txt > /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey.txt
cat /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey_tmp.txt |grep Ops_batch_update |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_update_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey_tmp.txt |grep Ops_batch_update |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_update_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey.txt
cat /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey.txt
rm -rf /opt/vrpv8/home/<USER>/fib_sync_batch_update_randomKey_tmp.txt

#同步单写 Update（不缓存object，随机key） 
# ip4forward 同步单 Update（不缓存object 随机key）
for k in $(seq 3)
do
echo ">>>>>>>>> $k"
./gmdb_benchmark_static  -p 1 -t 120 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
./gmdb_benchmark_static_async -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
taskset 2 ./gmdb_benchmark_static_random_non_keep_obj -p 1 -t 120 -a 41 -l 0 -L 1000000 -m 2 -f50 | tee /opt/vrpv8/home/<USER>/fib_sync_update_randomKey_noKeepObj_tmp_${k}.txt 
done
cat /opt/vrpv8/home/<USER>/fib_sync_update_randomKey_noKeepObj_tmp_*.txt > /opt/vrpv8/home/<USER>/fib_sync_update_randomKey_noKeepObj.txt
cat /opt/vrpv8/home/<USER>/fib_sync_update_randomKey_noKeepObj_tmp_*.txt |grep Ops_update_once |awk '{sum+=$3; count++} END {if (count>0) print "Ops_update_once_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/fib_sync_update_randomKey_noKeepObj_tmp_*.txt |grep Ops_update_once |awk '{sum+=$3; count++} END {if (count>0) print "Ops_update_once_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/fib_sync_update_randomKey_noKeepObj.txt
cat /opt/vrpv8/home/<USER>/fib_sync_update_randomKey_noKeepObj.txt
rm -rf /opt/vrpv8/home/<USER>/fib_sync_update_randomKey_noKeepObj_tmp_*.txt

# 同步单写 Delete（不缓存object） 
# ip4forward 同步单 Delete（不缓存object）
for k in $(seq 3)
do
./gmdb_benchmark_static_async -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
taskset 2 ./gmdb_benchmark_static_non_keep_obj -p 1 -t 120 -a  7 -l 0 -L 1000000 -m 2 -f50  | tee /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj.txt
gmsysview count
cat /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj.txt >> /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj_tmp.txt
done
cat /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj_tmp.txt > /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj.txt
cat /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj_tmp.txt |grep Ops_delete |awk '{sum+=$3; count++} END {if (count>0) print "Ops_delete_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj_tmp.txt |grep Ops_delete |awk '{sum+=$3; count++} END {if (count>0) print "Ops_delete_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj.txt
cat /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj.txt
rm -rf /opt/vrpv8/home/<USER>/fib_sync_delete_noKeepObj_tmp.txt

#NAC业务--trunk_member：
# 建表trunk_member
gmimport -c vschema -f  schema_file/trunk_member.gmjson

echo "# trunk_member"
echo "#同步单写 Replace（新增写，不缓存object,128个子节点）"
for k in $(seq 3)
do
# trunk_member同步单写 Replace（新增写，不缓存object）最大2048条数据
./gmdb_benchmark_static_non_keep_obj -p 1 -t 30 -a 40 -l 0 -L 2048 -m 2 -f75 -j 1  -s 10000000 -n 128
gmsysview count
./gmdb_benchmark_static  -p 1 -t 30 -a 7 -l 0 -L 2048 -m 2 -f75 -j 1  -s 10000000 -n 128
gmsysview count
taskset 2 ./gmdb_benchmark_static_non_keep_obj -p 1 -t 30 -a 40 -l 0 -L 2048 -m 2 -f75 -j 1  -s 10000000 -n 128 | tee /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj.txt
gmsysview count
./gmdb_benchmark_static  -p 1 -t 30 -a 7 -l 0 -L 2048 -m 2 -f75 -j 1  -s 10000000 -n 128
cat /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj.txt >> /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj_tmp.txt
done
cat /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj_tmp.txt > /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj.txt
cat /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj_tmp.txt |grep Ops_Write_once |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Write_once_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj_tmp.txt |grep Ops_Write_once |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Write_once_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj.txt
cat /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj.txt
rm -rf /opt/vrpv8/home/<USER>/trunk_member_replace_addNew_noKeepObj_tmp.txt

#IFM业务IF表--DML：
# 建表if_r21_1
gmimport  -c vschema -f   /root/gmdb_benchmark_arm64/schema_file/if_r21_1.gmjson

echo "null null null null " > sub_1.txt
echo "if ipv6_enable null 0" >> sub_1.txt
echo "if ipv4_enable null 0" >> sub_1.txt

echo "#主键读（读根节点单个字段，不缓存object）"
#主键读（读根节点单个字段，不缓存object）   -R 1
for k in $(seq 3)
do
taskset 2 ./gmdb_benchmark_static_non_keep_obj -p 1 -t 15 -a 3 -l 0 -L 128000 -m 2 -f51  -v 1 -R 1 | tee /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode.txt
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode.txt >> /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode_tmp.txt
done
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode_tmp.txt > /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode.txt
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode_tmp.txt |grep Ops_Read |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Read_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode_tmp.txt |grep Ops_Read |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Read_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode.txt
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode.txt
rm -rf /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_rootSglNode_tmp.txt

for k in $(seq 3)
do
echo "#主键读（读dev子节点下字段，不缓存object）"
#主键读（读dev子节点下字段，不缓存object）  -R 9
taskset 2 ./gmdb_benchmark_static_non_keep_obj -p 1 -t 15 -a 3 -l 0 -L 128000 -m 2 -f51  -v 1  -R 9 | tee /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes.txt
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes.txt >> /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes_tmp.txt
done
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes_tmp.txt > /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes.txt
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes_tmp.txt |grep Ops_Read |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Read_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes_tmp.txt |grep Ops_Read |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Read_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes.txt
cat /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes.txt
rm -rf /opt/vrpv8/home/<USER>/if_read_randomKey_noKeepObj_devNodes_tmp.txt

#老微观
#FIB业务 ip4forward表:
# 建表
cp /opt/vrpv8/home/<USER>/root/gmdb_benchmark_arm64/schema_file/
gmimport -c vschema -f  /root/gmdb_benchmark_arm64/schema_file/ip4forward_r21_1.gmjson

# ip4forward  异步批写 Insert
echo -e "\n >>> ip4forward Async batch_replace "
echo "# ./gmdb_benchmark_static_async -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120"
for k in $(seq 3)
do
./gmdb_benchmark_static_async -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
echo "# ./gmdb_benchmark_static -p 1 -t 50 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120"
./gmdb_benchmark_static -p 1 -t 50 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
echo "#./gmdb_benchmark_static_async -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120"
taskset 2 ./gmdb_benchmark_static_async -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120 | tee /opt/vrpv8/home/<USER>/fib_async_batch_replace.txt
gmsysview count
cat /opt/vrpv8/home/<USER>/fib_async_batch_replace.txt >> /opt/vrpv8/home/<USER>/fib_async_batch_replace_tmp.txt
done
cat /opt/vrpv8/home/<USER>/fib_async_batch_replace_tmp.txt > /opt/vrpv8/home/<USER>/fib_async_batch_replace.txt
cat /opt/vrpv8/home/<USER>/fib_async_batch_replace_tmp.txt |grep Ops_batch_write |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_write_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/fib_async_batch_replace_tmp.txt |grep Ops_batch_write |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_write_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/fib_async_batch_replace.txt
cat /opt/vrpv8/home/<USER>/fib_async_batch_replace.txt
rm -rf /opt/vrpv8/home/<USER>/fib_async_batch_replace_tmp.txt


# ip4forward  同步批写 Insert  gmsysview -q V\$STORAGE_CLUSTERED_HASH_LABEL_STAT |grep ip4forward_r -A 25 |grep -E "LABEL_NAME|ENTRY_USED"
echo -e "\n >>> ip4forward Sync batch_replace "
gmsysview count
echo "# ./gmdb_benchmark_static -p 1 -t 50 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120"
for k in $(seq 3)
do
./gmdb_benchmark_static -p 1 -t 50 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120
gmsysview count
echo "#./gmdb_benchmark_static -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120"
taskset 2 ./gmdb_benchmark_static -p 1 -t 120 -a 8 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120 | tee /opt/vrpv8/home/<USER>/fib_sync_batch_replace.txt
cat /opt/vrpv8/home/<USER>/fib_sync_batch_replace.txt >> /opt/vrpv8/home/<USER>/fib_sync_batch_replace_tmp.txt
done
cat /opt/vrpv8/home/<USER>/fib_sync_batch_replace_tmp.txt > /opt/vrpv8/home/<USER>/fib_sync_batch_replace.txt
cat /opt/vrpv8/home/<USER>/fib_sync_batch_replace_tmp.txt |grep Ops_batch_write |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_write_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/fib_sync_batch_replace_tmp.txt |grep Ops_batch_write |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_write_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/fib_sync_batch_replace.txt
cat /opt/vrpv8/home/<USER>/fib_sync_batch_replace.txt
rm -rf /opt/vrpv8/home/<USER>/fib_sync_batch_replace_tmp.txt

# ip4forward  同步批删 Delete
echo -e "\n >>> ip4forward Sync batch_delete "
gmsysview count
echo "#./gmdb_benchmark_static -p 1 -t 50 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120"
for k in $(seq 3)
do
taskset 2 ./gmdb_benchmark_static -p 1 -t 50 -a 10 -l 0 -L 1000000 -m 2 -f50 -j 0 -b 120 | tee /opt/vrpv8/home/<USER>/fib_sync_batch_delete.txt
gmsysview count
cat /opt/vrpv8/home/<USER>/fib_sync_batch_delete.txt >> /opt/vrpv8/home/<USER>/fib_sync_batch_delete_tmp.txt
done
cat /opt/vrpv8/home/<USER>/fib_sync_batch_delete_tmp.txt > /opt/vrpv8/home/<USER>/fib_sync_batch_delete.txt
cat /opt/vrpv8/home/<USER>/fib_sync_batch_delete_tmp.txt |grep Ops_batch_remove |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_remove_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/fib_sync_batch_delete_tmp.txt |grep Ops_batch_remove |awk '{sum+=$3; count++} END {if (count>0) print "Ops_batch_remove_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/fib_sync_batch_delete.txt
cat /opt/vrpv8/home/<USER>/fib_sync_batch_delete.txt
rm -rf /opt/vrpv8/home/<USER>/fib_sync_batch_delete_tmp.txt

# ip4forward  随机key读 ((普通obj + create/release obj)) 
echo -e "\n >>> ip4forward read_random  (commonObj + create/release obj)  "
gmsysview count
echo "#./gmdb_benchmark_static_non_keep_obj -p 1 -t 50 -a 3 -l 0 -L 1000000 -m 2 -f50 -j 0 -v 1"
for k in $(seq 3)
do
taskset 2 ./gmdb_benchmark_static_non_keep_obj -p 1 -t 50 -a 3 -l 0 -L 1000000 -m 2 -f50 -j 0 -v 1 | tee /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease.txt >> /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease_tmp.txt
done
cat /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease_tmp.txt > /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease_tmp.txt |grep Ops_Read |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Read_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease_tmp.txt |grep Ops_Read |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Read_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease.txt
rm -rf /opt/vrpv8/home/<USER>/fib_read_random_obj_createRelease_tmp.txt

#ARP业务 arp表:
# arp表测试项目 建表
cp /opt/vrpv8/home/<USER>/root/gmdb_benchmark_arm64/schema_file/
gmimport -c vschema -f  /root/gmdb_benchmark_arm64/schema_file/arp_r21_1.gmjson

rm -f /opt/vrpv8/home/<USER>/*

# ARP同步单写 Replace (普通obj + create/release obj)
echo -e "\n >>> arp replace (commonObj + create/release obj)  "
echo "# ./gmdb_benchmark_static -p 1 -t 50 -a 10 -l 0 -L512000 -m 2 -f52 -j 0 -b 300"
for k in $(seq 3)
do
./gmdb_benchmark_static -p 1 -t 50 -a 10 -l 0 -L512000 -m 2 -f52 -j 0 -b 300
gmsysview count
echo "#./gmdb_benchmark_static_non_keep_obj -p 1 -t 50 -a 40 -l 0 -L512000 -m 2 -f52 -j 0"
taskset 2 ./gmdb_benchmark_static_non_keep_obj -p 1 -t 50 -a 40 -l 0 -L512000 -m 2 -f52 -j 0 | tee /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease.txt >> /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease_tmp.txt
done
cat /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease_tmp.txt > /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease_tmp.txt |grep Ops_Write_once |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Write_once_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease_tmp.txt |grep Ops_Write_once |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Write_once_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease.txt
rm -rf /opt/vrpv8/home/<USER>/arp_sync_replace_obj_createRelease_tmp.txt

#MCAST业务 mfib/elb表:
# 建表
# elb二级索引读遍历  l3mc_elb表
gmimport -c cache -f  /root/gmdb_benchmark_arm64/business_schema_file

#  elb二级索引读遍历  l3mc_elb表
echo -e "\n >>> l3mc_elb   read_by_hashcluster "
echo "# ./fwm_test -m 51 -n 262144"
for k in $(seq 3)
do
./fwm_test -m 51 -n 262144
gmsysview -q V\$STORAGE_CLUSTERED_HASH_LABEL_STAT |grep -w l3mc_elb -A 20  |grep -E "LABEL_NAME|ENTRY_USED"
echo "#./fwm_test -m 55 -n 262144 -t 4"
taskset 2 ./fwm_test -m 55 -n 262144 -t 4 | tee /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster.txt
cat /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster.txt >> /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster_tmp.txt
done
cat /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster_tmp.txt > /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster.txt
cat /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster_tmp.txt |grep Ops |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster_tmp.txt |grep Ops |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster.txt
cat /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster.txt
rm -rf /opt/vrpv8/home/<USER>/l3mc_elb_read_by_hashcluster_tmp.txt

# mfib基于Key获取记录数 l3mc_mfib表  (create/release obj)
echo -e "\n >>> l3mc_mfib   get_count_of_table_by_key (create/release obj) "
echo "#./fwm_test -m 48 -n 65536 -t 25"
for k in $(seq 3)
do
taskset 2 ./fwm_test -m 48 -n 65536 -t 25 | tee /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease.txt 
cat /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease.txt >> /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease_tmp.txt
done
cat /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease_tmp.txt > /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease_tmp.txt |grep Ops |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease_tmp.txt |grep Ops |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease.txt
rm -rf /opt/vrpv8/home/<USER>/l3mc_mfib_getCountByHckey_obj_createRelease_tmp.txt

# mfib同步单写 Replace  l3mc_mfib (create/release obj) 
echo -e "\n >>> l3mc_mfib   replace (create/release obj) "
gmsysview -q V\$STORAGE_CLUSTERED_HASH_LABEL_STAT |grep -w l3mc_mfib -A 20 |grep -E "LABEL_NAME|ENTRY_USED"
echo "# ./fwm_test -m 42 -n 65536 "
for k in $(seq 3)
do
./fwm_test -m 42 -n 65536 
gmsysview count
echo "#./fwm_test -m 47 -n 65536"
taskset 2 ./fwm_test -m 47 -n 65536   | tee /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease.txt 
cat /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease.txt >> /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease_tmp.txt
done
cat /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease_tmp.txt > /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease_tmp.txt |grep Ops |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Avg:", sum/count}'
cat /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease_tmp.txt |grep Ops |awk '{sum+=$3; count++} END {if (count>0) print "Ops_Avg:", sum/count}' >> /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease.txt
cat /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease.txt
rm -rf /opt/vrpv8/home/<USER>/l3mc_mfib_sync_replace_obj_createRelease_tmp.txt
