/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 攻击函数
 * Author:
 * Create: 2022-4-16
 * History:
 */
#ifndef _ATTACK_H
#define _ATTACK_H 1

#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <stdarg.h>
#include <regex.h>
#include <signal.h>
#include <pthread.h>
#include <sys/sem.h>
#include <sys/wait.h>
#include <sys/time.h>

#include "gtest/gtest.h"
#include "resilience.h"

int exeCmd(char *cmd, uint32_t size)
{
    int ret = system(cmd);
    memset(cmd, 0, size);
    return ret;
}

void *detectAttackByConn(void *args)
{
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    int ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    RETURN_VOID_IFERR(ret);

    uint64_t timeout = MAX_DETECT_TIMES;
    char msg[MAX_CMD_SIZE] = {0};

    while (timeout > 0) {
        GmcAlarmIdE alarmId = GMC_ALARM_CONNECTION_NUMBER;
        GmcAlarmDataT alarmData;
        ret = GmcGetAlarmData(stmt, alarmId, &alarmData);
        RETURN_VOID_IFERR(ret);

        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld alarmData.alarmStatus : %d", timestamp, alarmData.alarmStatus);
        infoInFile(msg);

        timeout--;
        usleep(100000);
    }

    ret = testGmcDisconnect(conn, stmt);
    RETURN_VOID_IFERR(ret);
    return NULL;
}

void *thread_conn(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint64_t id = *((int *)args);

    uint64_t attckTimes = MAX_ATTACK_TIMES;
    while (attckTimes > 0) {
        TEST_INFO("SessionAttack", attckTimes, 1000, id);
        YangConnOptionT connOptions = {0};
        connOptions.epollFd = &g_epAsync[0].userEpollFd;
        int ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
        RETURN_VOID_IFERR(ret);
        ret = testGmcDisconnect(conn, stmt);
        RETURN_VOID_IFERR(ret);
        uint32_t waitTime = attckTimes;
        usleep(waitTime);
        attckTimes--;
    }
    return NULL;
}

int resSessionAttack()
{
    GmcConnT *conn[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt[MAX_CONN_SIZE] = {0};

    pthread_t thr_detect;
    int ret = pthread_create(&thr_detect, NULL, detectAttackByConn, NULL);

    const uint32_t threadNum = 20;
    uint32_t index[threadNum] = {0};
    pthread_t thr_arr[threadNum];

    char msg[MAX_CMD_SIZE] = {0};
    int64_t timestamp = getCurrentTime();
    (void)sprintf(msg, "%lld start crash", timestamp);
    infoInFile(msg);

    uint32_t waitTime = 30;
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    for (uint32_t i = 0; i < MAX_CONN_SIZE - 30; i++) {
        ret = TestYangGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_SYNC, &connOptions);
        RETURN_IFERR(ret);
        if (i == 0) {
            sleep(waitTime);
        }
    }

    sleep(waitTime);

    for (uint32_t i = 0; i < threadNum; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_conn, (void *)&index[i]);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    for (uint32_t i = 0; i < MAX_CONN_SIZE - 30; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        RETURN_IFERR(ret);
    }

    memset(msg, 0, sizeof(msg));
    timestamp = getCurrentTime();
    (void)sprintf(msg, "%lld stop crash", timestamp);
    infoInFile(msg);

    return GMERR_OK;
}

void *detectAttackByAccount(void *args)
{
    uint64_t timeout = MAX_DETECT_TIMES;
    char msg[MAX_CMD_SIZE] = {0};

    while (timeout > 0) {
        char logFile[MAX_CMD_SIZE];
        char *result = NULL;
        char *dir = getenv("TEST_HOME");
        (void)snprintf(logFile, sizeof(logFile), "%s/log/secure/sgmserver/sgmserver.log", dir);
        readJanssonFile(logFile, &result);
        if (result == NULL) {
            printf("read log file failed\n");
            RETURN_VOID_IFERR(FAILED);
        }

        if (strstr(result, "DROP USER") != NULL) {
            int64_t timestamp = getCurrentTime();
            (void)sprintf(msg, "%lld [detectAttackBySysPermission] \"DROP USER\" has been found", timestamp);
            infoInFile(msg);

            char rmCmd[MAX_CMD_SIZE] = {0};
            (void)snprintf(rmCmd, sizeof(rmCmd), "echo \"\">%s", logFile);
            exeCmd(rmCmd, sizeof(rmCmd));
        }
        free(result);

        timeout--;
        usleep(100000);
    }
    return NULL;
}

int resDeleteAccount()
{
    char msg[MAX_CMD_SIZE] = {0}, cmd[MAX_CMD_SIZE] = {0};
    pthread_t thr_detect;
    int32_t ret = pthread_create(&thr_detect, NULL, detectAttackByAccount, NULL);

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // remove trustlist
        memset(cmd, 0, sizeof(cmd));
        const char *allowListFile = "allow_list/allow_list_drop.gmuser";
        (void)snprintf(
            cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allowListFile, g_connServer);
        ret = executeCommand(cmd, "successfully");
        RETURN_IFERR(ret);

        printf("remove trustlist succ\n");

        sleep(30);

        // import trustlist
        memset(cmd, 0, sizeof(cmd));
        (void)snprintf(
            cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allowListFile, g_connServer);
        ret = executeCommand(cmd, "successfully");
        RETURN_IFERR(ret);
        printf("reimport trustlist succ\n");

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

void *detectAttackBySysPermission(void *args)
{
    uint64_t timeout = MAX_DETECT_TIMES;
    char msg[MAX_CMD_SIZE] = {0};

    while (timeout > 0) {
        char logFile[MAX_CMD_SIZE];
        char *result = NULL;
        char *dir = getenv("TEST_HOME");
        (void)snprintf(logFile, sizeof(logFile), "%s/log/secure/sgmserver/sgmserver.log", dir);
        readJanssonFile(logFile, &result);
        if (result == NULL) {
            printf("read log file failed\n");
            RETURN_VOID_IFERR(FAILED);
        }

        if (strstr(result, "REVOKE") != NULL) {
            int64_t timestamp = getCurrentTime();
            (void)sprintf(msg, "%lld [detectAttackBySysPermission] \"REVOKE\" has been found", timestamp);
            infoInFile(msg);

            char rmCmd[MAX_CMD_SIZE] = {0};
            (void)snprintf(rmCmd, sizeof(rmCmd), "echo \"\">%s", logFile);
            exeCmd(rmCmd, sizeof(rmCmd));
        }
        free(result);

        timeout--;
        usleep(100000);
    }
    return NULL;
}

int resModifySysPermission()
{
    char msg[MAX_CMD_SIZE] = {0}, cmd[MAX_CMD_SIZE] = {0};
    pthread_t thr_detect;
    int32_t ret = pthread_create(&thr_detect, NULL, detectAttackBySysPermission, NULL);

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // revoke create
        memset(cmd, 0, sizeof(cmd));
        const char *sysRevoke = "gmpolicy_file/createPolicy.gmpolicy";
        (void)snprintf(
            cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, sysRevoke, g_connServer);
        ret = executeCommand(cmd, "successfully");
        RETURN_IFERR(ret);
        printf("revoke create policy succ\n");

        sleep(30);

        // import create
        memset(cmd, 0, sizeof(cmd));
        (void)snprintf(
            cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, sysRevoke, g_connServer);
        ret = executeCommand(cmd, "successfully");
        RETURN_IFERR(ret);
        printf("reimport create policy succ\n");

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resModifyObjPermission()
{
    char msg[MAX_CMD_SIZE] = {0}, cmd[MAX_CMD_SIZE] = {0};
    pthread_t thr_detect;
    int32_t ret = pthread_create(&thr_detect, NULL, detectAttackBySysPermission, NULL);

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // revoke insert
        memset(cmd, 0, sizeof(cmd));
        const char *obj_policy_file = "gmpolicy_file/insertPolicy.gmpolicy";
        (void)snprintf(
            cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
        ret = executeCommand(cmd, "successfully");
        RETURN_IFERR(ret);

        printf("revoke insert policy succ\n");

        sleep(30);

        // import insert
        memset(cmd, 0, sizeof(cmd));
        (void)snprintf(
            cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
        ret = executeCommand(cmd, "successfully");
        RETURN_IFERR(ret);
        printf("reimport insert policy succ\n");

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int ResOverrideFile()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // 导出数据文件
        (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s", g_toolPath, gIp4ForwardName);
        int ret = executeCommand(cmd, "successfully");
        RETURN_IFERR(ret);

        sleep(30);

        // 导入数据文件
        (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s.gmdata -t %s -l 99999", g_toolPath,
            gIp4ForwardName, gIp4ForwardName);
        ret = executeCommand(cmd, "successfully");
        RETURN_IFERR(ret);

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resDeleteConfFile()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini_res_bk", g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));
    (void)snprintf(cmd, sizeof(cmd), "cp %s/gmserver.ini %s/gmserver.ini_res_bk", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // remove conf file
        (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini", g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));

        printf("remove conf file succ\n");

        sleep(30);

        // restore conf file
        (void)snprintf(
            cmd, sizeof(cmd), "cp %s/gmserver.ini_res_bk %s/gmserver.ini", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));
        printf("restore conf file succ\n");

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resOverrideConfFile()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini_res_bk", g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));
    (void)snprintf(cmd, sizeof(cmd), "cp %s/gmserver.ini %s/gmserver.ini_res_bk", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));
    (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini", g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // Override conf file
        (void)snprintf(
            cmd, sizeof(cmd), "cp %s/gmserver.ini_res_bk %s/gmserver.ini", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));

        printf("Override conf file succ\n");

        sleep(30);

        attckTimes--;
    }
    return GMERR_OK;
}

int resEncryptConfFile()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini_res_bk", g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));
    (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini~", g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));
    (void)snprintf(cmd, sizeof(cmd), "cp %s/gmserver.ini %s/gmserver.ini_res_bk", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // encrypt conf file
        (void)snprintf(cmd, sizeof(cmd), "gzexe %s/gmserver.ini", g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));

        printf("encrypt conf file succ\n");

        sleep(30);

        // restore conf file
        (void)snprintf(cmd, sizeof(cmd), "gzexe -d %s/gmserver.ini", g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));
        printf("restore conf file succ\n");

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resCreateConfData()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini_res_bk", g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));
    (void)snprintf(cmd, sizeof(cmd), "cp %s/gmserver.ini %s/gmserver.ini_res_bk", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // create conf data
        (void)snprintf(cmd, sizeof(cmd), "echo 'newData = 1'>>%s/gmserver.ini", g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));

        printf("create conf data succ\n");

        sleep(30);

        // restore conf data
        (void)snprintf(cmd, sizeof(cmd), "sed -i '$d' %s/gmserver.ini", g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));
        printf("restore conf data succ\n");

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resDeleteConfData()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini_res_bk", g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));
    (void)snprintf(cmd, sizeof(cmd), "cp %s/gmserver.ini %s/gmserver.ini_res_bk", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // delete conf data
        (void)snprintf(cmd, sizeof(cmd), "sed -i '$d' %s/gmserver.ini", g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));

        printf("delete conf data succ\n");

        sleep(30);

        // restore conf data
        (void)snprintf(
            cmd, sizeof(cmd), "cp -rf %s/gmserver.ini_res_bk %s/gmserver.ini", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
        exeCmd(cmd, sizeof(cmd));
        printf("restore conf data succ\n");

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resModifyConfData()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    (void)snprintf(cmd, sizeof(cmd), "rm -rf %s/gmserver.ini_res_bk", g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));
    (void)snprintf(cmd, sizeof(cmd), "cp %s/gmserver.ini %s/gmserver.ini_res_bk", g_sysGMDBCfgPath, g_sysGMDBCfgPath);
    exeCmd(cmd, sizeof(cmd));

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // modify conf data
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=9\"");
        printf("modify conf data succ\n");

        sleep(30);

        // restore conf data
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        printf("restore conf data succ\n");

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

char gCfeToolPath[256] =
    "4.19.36-vhulk1907.1.0.h619.eulerosv2r8.aarch64_EulerOS_Server_V200R008C00SPC300B630-2019-12-27-10-58-38/cfe";

int resMemoryHijack()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // 大量劫持OS可用内存
        (void)snprintf(
            cmd, sizeof(cmd), "./%s/cfe \"inject rmem_leak (total, block, gap) values(6G, 1G, 1000)\"", gCfeToolPath);
        exeCmd(cmd, sizeof(cmd));

        sleep(30);

        // 恢复
        (void)snprintf(cmd, sizeof(cmd), "./%s/cfe \"clean rmem_leak\"", gCfeToolPath);
        exeCmd(cmd, sizeof(cmd));

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resMemoryExhaustion()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // 内存资源耗尽
        (void)snprintf(
            cmd, sizeof(cmd), "./%s/cfe \"inject rmem_leak (total, block, gap) values(10G, 1G, 1000)\"", gCfeToolPath);
        exeCmd(cmd, sizeof(cmd));

        sleep(30);

        // 恢复
        (void)snprintf(cmd, sizeof(cmd), "./%s/cfe \"clean rmem_leak\"", gCfeToolPath);
        exeCmd(cmd, sizeof(cmd));

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resCpuHijack()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // 劫持CPU
        (void)snprintf(cmd, sizeof(cmd),
            "./%s/cfe \"inject rCPU_Overloadal (cpuid1, cpuid2, usage) values(0, 3, 100)\"", gCfeToolPath);
        exeCmd(cmd, sizeof(cmd));

        sleep(30);

        // 恢复
        (void)snprintf(cmd, sizeof(cmd), "./%s/cfe \"clean rCPU_Overloadal\"", gCfeToolPath);
        exeCmd(cmd, sizeof(cmd));

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resCpuExhaustion()
{
    char cmd[MAX_CMD_SIZE] = {0};
    char msg[MAX_CMD_SIZE] = {0};

    uint64_t attckTimes = 1;
    while (attckTimes > 0) {
        memset(msg, 0, sizeof(msg));
        int64_t timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld start crash", timestamp);
        infoInFile(msg);

        // CPU资源耗尽
        (void)snprintf(cmd, sizeof(cmd),
            "./%s/cfe \"inject rCPU_Overloadal (cpuid1, cpuid2, usage) values(0, 7, 100)\"", gCfeToolPath);
        exeCmd(cmd, sizeof(cmd));

        sleep(30);

        // 恢复
        (void)snprintf(cmd, sizeof(cmd), "./%s/cfe \"clean rCPU_Overloadal\"", gCfeToolPath);
        exeCmd(cmd, sizeof(cmd));

        memset(msg, 0, sizeof(msg));
        timestamp = getCurrentTime();
        (void)sprintf(msg, "%lld stop crash", timestamp);
        infoInFile(msg);

        attckTimes--;
    }
    return GMERR_OK;
}

int resInvalidAttack()
{
    printf("no attack mode\n");
    return GMERR_OK;
}

typedef int32_t (*AttackFunc)();

typedef struct {
    AttackFunc attackFunc;
} resAttackHandlerT;

resAttackHandlerT gResAttackHandler[] = {[TMODE_DFRs_IMPACT_APP_PROT_001] = {resSessionAttack},
    [TMODE_DFRs_IMPACT_APP_DATA_001] = {resDeleteAccount},
    [TMODE_DFRs_IMPACT_APP_DATA_004_SYS] = {resModifySysPermission},
    [TMODE_DFRs_IMPACT_APP_DATA_004_OBJ] = {resModifyObjPermission},
    [TMODE_DFRs_IMPACT_APP_DATA_006] = {ResOverrideFile},
    [TMODE_DFRs_IMPACT_APP_DATA_007] = {ResOverrideFile}, // ResEncryptFile
    [TMODE_DFRs_IMPACT_APP_DATA_008] = {ResOverrideFile}, // ResDeleteFile
    [TMODE_DFRs_IMPACT_APP_DATA_035] = {resDeleteConfFile},
    [TMODE_DFRs_IMPACT_APP_DATA_036] = {resOverrideConfFile},
    [TMODE_DFRs_IMPACT_APP_DATA_037] = {resEncryptConfFile},
    [TMODE_DFRs_IMPACT_APP_DATA_038] = {resCreateConfData},
    [TMODE_DFRs_IMPACT_APP_DATA_039] = {resDeleteConfData},
    [TMODE_DFRs_IMPACT_APP_DATA_040] = {resModifyConfData},
    [TMODE_DFRs_IMPACT_OS_RAM_001] = {resMemoryHijack},
    [TMODE_DFRs_IMPACT_OS_RAM_002] = {resMemoryExhaustion},
    [TMODE_DFRs_IMPACT_OS_CPU_001] = {resCpuHijack},
    [TMODE_DFRs_IMPACT_OS_CPU_002] = {resCpuExhaustion},
    [TMODE_MAX_INVALID] = {resInvalidAttack}};

#endif /* _ATTACK_H */
