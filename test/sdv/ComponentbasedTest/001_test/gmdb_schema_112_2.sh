#! /bin/bash
# ----------------------------------------------------------------------------
# Description:  异步删除主键中含有类型为string的变长表
# Author: qinjianhua wx620469
# Create: 2021-08-30
# History: 
# ----------------------------------------------------------------------------
ENV_TYPE=$1
tEnv=$(uname -a|grep Hongmeng|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="soho"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"
function check_gmserverId()
{
  gmserverId=`ps -ef | grep gmserver | grep -v grep | awk '{print $2}' | wc -l`
  hpkId=`ps | grep vm.elf | grep -v grep | awk '{print $2}' | wc -l`
  if [ $gmserverId -le 0 ] && [ $hpkId -le 0 ];then
    echo "----------The service does not exist!----------"
    kill -9 `pidof teststa`
    exit 1
  fi
}
kill -9 `pidof teststa`
if [ X"$ENV_TYPE" == "Xharmony" ] || [ X"$ENV_TYPE" == "Xsoho" ];then
    echo "X$ENV_TYPE"
else
    stop.sh -f
    start.sh -f
fi

../test_execute_all.sh

usr="user123"
pwd="password.123"
environ="RTOS"
server_locator="usocket:/run/verona/unix_emserver"
schema=if
loop=0
if [ X"$ENV_TYPE" == "Xharmony" ];then
  CYCLE_COUNT=1
  multi_process=1
  multi_thread=10
  thread_count=10
  let scan_count=1
elif [ X"$ENV_TYPE" == "Xsoho" ];then
  CYCLE_COUNT=1
  multi_process=1
  multi_thread=2
  thread_count=2
  let scan_count=1
else
  CYCLE_COUNT=10
  multi_process=1
  multi_thread=10
  thread_count=100
  let scan_count=1
fi

while [ ${loop} -lt ${CYCLE_COUNT} ]
do
    echo -e "\033[45m once operation start: $i ---- $schema \033[0m at:" `date "+%m-%d %H:%M:%S"`
    echo "loop=$loop"
    start_id=0
    end_id=10000
    echo "end_id=$end_id"
    ${TEST_HOME}/perf/stability/teststa -v -a  -m replace -n $schema   -b  $start_id -e $end_id
    ${TEST_HOME}/perf/stability/teststa -v -a  -m delete -n $schema   -b  $start_id -e $end_id
    gmsysview -q V\$QRY_DML_OPER_STATIS -f LABEL_NAME='if00000'
    echo -e "\033[45m once operation end: $i ---- $schema \033[0m at:" `date "+%m-%d %H:%M:%S"`
    let loop+=1
done
schema1=aaa_account_scheme
schema2=if
schema3=if_statistics
schema4=ip4forward
schema5=nhp_group
${TEST_HOME}/perf/stability/teststa -D -n $schema1 
${TEST_HOME}/perf/stability/teststa -D -n $schema2
${TEST_HOME}/perf/stability/teststa -D -n $schema3 
${TEST_HOME}/perf/stability/teststa -D -n $schema4 
${TEST_HOME}/perf/stability/teststa -D -n $schema5 
check_gmserverId
kill -9 `pidof teststa`
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ] || [ X"$ENV_TYPE" == "Xsoho" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
