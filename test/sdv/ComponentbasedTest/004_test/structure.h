/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef _STRUCTURE_H
#define _STRUCTURE_H
#include "syCommon.h"

int simpleStructureDelete(GmcStmtT *, int, int, const char *, uint32_t, uint32_t);
int simpleDelete(GmcStmtT *, int, int, const char *, const char *);

#pragma pack(1)
typedef struct TagTestT1Vertex {
    int64_t F0;
    uint64_t F1;
    int32_t F2;
    uint32_t F3;
    int16_t F4;
    uint16_t F5;
    int8_t F6;
    uint8_t F7;
    bool F8;
    float F9;
    double F10;
    uint64_t F11;
    char F12;
    unsigned char F13;
    uint8_t F14[16];
    uint8_t F15;
} GtTestT1VertexT;
#pragma pack()

void vertexSnCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    bool eof = false;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        if (info->eventType == GMC_SUB_EVENT_INSERT) {
            user_data->insertNum++;
        } else if (info->eventType == GMC_SUB_EVENT_DELETE) {
            user_data->deleteNum++;
        } else if (info->eventType == GMC_SUB_EVENT_UPDATE) {
            user_data->updateNum++;
        } else if (info->eventType == GMC_SUB_EVENT_REPLACE) {
            user_data->replaceNum++;
        } else if (info->eventType == GMC_SUB_EVENT_AGED) {
            user_data->agedNum++;
        } else if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD) {
        }
    }
}

void GtStructSetPK(GtTestT1VertexT *vertex, int32_t value)
{
    vertex->F0 = value;
}

void GtTestT1StructSetProperty(GtTestT1VertexT *vertex, int32_t value)
{
    vertex->F1 = value;
    vertex->F2 = value;
    vertex->F3 = value;
    vertex->F4 = value & 0x7fff;
    vertex->F5 = value & 0xffff;
    vertex->F6 = value & 0x7f;
    vertex->F7 = value & 0xff;
    vertex->F8 = false;
    vertex->F9 = value;
    vertex->F10 = value;
    vertex->F11 = value;
    vertex->F12 = 'a' + (value & 0x1A);
    vertex->F13 = 'A' + (value & 0x1A);
    int ret = 0;
    ret = snprintf((char *)vertex->F14, sizeof(vertex->F14), "aaaaaaa%08d", value);
    EXPECT_LE(0, ret);
    vertex->F15 = value & 0xf;
}

int GtCheckSimpleStruct(GtTestT1VertexT *vertex, int32_t value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)value;
    uint64_t F1Value = (uint64_t)value;
    int32_t F2Value = value;
    uint32_t F3Value = value;
    int16_t F4Value = value & 0x7FFF;
    uint16_t F5Value = value & 0xFFFF;
    int8_t F6Value = value & 0x7F;
    uint8_t F7Value = value & 0xFF;
    bool F8Value = false;
    float F9Value = value;
    double F10Value = value;
    uint64_t F11Value = value;
    char F12Value = 'a' + (value & 0x1A);
    unsigned char F13Value = 'A' + (value & 0x1A);
    char F14Value[16] = {0};
    ret = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", value);
    EXPECT_LE(0, ret);
    uint8_t F15Value = value & 0xF;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &vertex->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &vertex->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &vertex->F2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &vertex->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &vertex->F4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &vertex->F5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &vertex->F6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &vertex->F7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &vertex->F8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &vertex->F9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &vertex->F10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &vertex->F11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &vertex->F12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &vertex->F13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, vertex->F14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &vertex->F15, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int GtCheckSimpleTableKey(GmcStmtT *stmt, int value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)value;
    uint64_t F1Value = (uint64_t)value;
    int32_t F2Value = value;
    uint32_t F3Value = value;
    int16_t F4Value = value & 0x7FFF;
    uint16_t F5Value = value & 0xFFFF;
    int8_t F6Value = value & 0x7F;
    uint8_t F7Value = value & 0xFF;
    bool F8Value = false;
    float F9Value = value;
    double F10Value = value;
    uint64_t F11Value = value;
    char F12Value = 'a' + (value & 0x1A);
    unsigned char F13Value = 'A' + (value & 0x1A);
    char F14Value[16] = {0};
    ret = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", value);
    EXPECT_LE(0, ret);
    uint8_t F15Value = value & 0xF;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &F6Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &F11Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, F14Value);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    RETURN_IFERR(ret);
    return ret;
}

int GtSetPK(GmcStmtT *stmt, int value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)value;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int GtSetProperty(GmcStmtT *stmt, int32_t value, bool partition = 0)
{
    int ret = 0;
    uint64_t F1Value = (uint64_t)value;
    int32_t F2Value = value;
    uint32_t F3Value = value;
    int16_t F4Value = value & 0x7FFF;
    uint16_t F5Value = value & 0xFFFF;
    int8_t F6Value = value & 0x7F;
    uint8_t F7Value = value & 0xFF;
    bool F8Value = false;
    float F9Value = value;
    double F10Value = value;
    uint64_t F11Value = value;
    char F12Value = 'a' + (value & 0x1A);
    unsigned char F13Value = 'A' + (value & 0x1A);
    char F14Value[16] = {0};
    ret = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", value);
    EXPECT_LE(0, ret);
    uint8_t F15Value = value & 0xF;

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    if (partition) {
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
        EXPECT_EQ(GMERR_OK, ret);
    }

    return GMERR_OK;
}

int simpleWrite(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName)
{
    int ret = 0;
    uint32_t affectRows = 0;
    uint32_t expected_affectRows = 1;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        ret = GtSetPK(stmt, i);
        RETURN_IFERR(ret);
        ret = GtSetProperty(stmt, i, 1);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        RETURN_IFERR(ret);
        EXPECT_LE(expected_affectRows, affectRows);
    }
    return ret;
}

int simpleWriteAsync(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName)
{
    int ret = 0;
    uint32_t affectRows = 0;
    uint32_t expected_affectRows = 1;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        ret = GtSetPK(stmt, i);
        RETURN_IFERR(ret);
        ret = GtSetProperty(stmt, i, 1);
        RETURN_IFERR(ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_LE(expected_affectRows, data.affectRows);
    }
    return ret;
}

int simpleStructureWriteAsync(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    int waitFlag = 1)
{
    int ret = 0;
    uint32_t expected_affectRows = 1;
    GtTestT1VertexT obj = (GtTestT1VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        GtStructSetPK(&obj, i);
        GtTestT1StructSetProperty(&obj, i);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        RETURN_IFERR(ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        RETURN_IFERR(ret);
        if (waitFlag) {
            ret = testWaitAsyncRecv(&data);
            RETURN_IFERR(ret);
            EXPECT_EQ(GMERR_OK, data.status);
            EXPECT_LE(expected_affectRows, data.affectRows);
        }
    }
    return ret;
}

int simpleStructureWrite(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName)
{
    int ret = 0;
    uint32_t affectRows = 0;
    uint32_t expected_affectRows = 1;
    GtTestT1VertexT obj = (GtTestT1VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        GtStructSetPK(&obj, i);
        GtTestT1StructSetProperty(&obj, i);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        RETURN_IFERR(ret);
    }
    return ret;
}

int simpleStructureRead(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    uint32_t KeyID, uint32_t expectNum = 1)
{
    int ret = 0;
    GtTestT1VertexT obj = (GtTestT1VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    for (int i = recordCountStart; i < recordCountEnd; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        GtStructSetPK(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, KeyID, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            if (ret != 12000) {
                EXPECT_EQ(GMERR_OK, ret);
            }
            if (isFinish) {
                break;
            }
            cnt++;
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            const char *keyName = "PK";
            ret = simpleDelete(stmt, i, i + 1, labelName, keyName);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int simpleKeyRead(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    const char *keyName, uint32_t expectNum = 1)
{
    int ret = 0;
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            cnt++;
            ret = GtCheckSimpleTableKey(stmt, i);
            RETURN_IFERR(ret);
        }
        ret = simpleDelete(stmt, i, i +1, labelName, keyName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int simpleStructureUpdate(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    uint32_t KeyID, uint32_t expectNum = 1)
{
    int ret = 0;
    uint32_t affectRows = 0;
    uint32_t expected_affectRows = 0;
    GtTestT1VertexT obj = (GtTestT1VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        GtStructSetPK(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, KeyID, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GtSetProperty(stmt, i * 2);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_LE(expected_affectRows, affectRows);
    }
    return ret;
}

int simpleStructureUpdateAsync(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    uint32_t KeyID, uint32_t expectNum = 1)
{
    int ret = 0;
    uint32_t expected_affectRows = 0;
    GtTestT1VertexT obj = (GtTestT1VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        GtStructSetPK(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, KeyID, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GtSetProperty(stmt, i * 2);
        RETURN_IFERR(ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.deleteCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_LE(expected_affectRows, data.affectRows);
    }
    return ret;
}

int simpleUpdate(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    const char *keyName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        int64_t F0 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0, sizeof(F0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GtSetProperty(stmt, i);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }
    return ret;
}

int simpleUpdateAsync(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    const char *keyName)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        int64_t F0 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0, sizeof(F0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GtSetProperty(stmt, i);
        RETURN_IFERR(ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.deleteCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 批量更新
int simpleUpdateBatch(GmcStmtT *stmt, GmcConnT *conn, int recordCountStart, int recordCountEnd,
    const char *labelName, const char *keyName)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        int64_t F0 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0, sizeof(F0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int simpleStructureDelete(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    uint32_t KeyID, uint32_t expectNum = 1)
{
    int ret = 0;
    uint32_t affectRows = 0;
    uint32_t expected_affectRows = 1;
    GtTestT1VertexT obj = (GtTestT1VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        GtStructSetPK(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, KeyID, NULL, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
    return ret;
}

int simpleDelete(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    const char *keyName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        int64_t F0 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0, sizeof(F0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int simpleDeleteAsync(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    const char *keyName, int waitFlag = 1)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        int64_t F0 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0, sizeof(F0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        if (waitFlag) {
            ret = testWaitAsyncRecv(&data);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return ret;
}

// 异步结构化删除
int simpleStructureDeleteAsync(GmcStmtT *stmt, int recordCountStart, int recordCountEnd, const char *labelName,
    uint32_t KeyID, uint32_t expectNum = 1)
{
    int ret = 0;
    uint32_t expected_affectRows = 1;
    GtTestT1VertexT obj = (GtTestT1VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        GtStructSetPK(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, KeyID, NULL, &labelInfo);
        RETURN_IFERR(ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.deleteCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_LE(expected_affectRows, data.affectRows);
    }
    return ret;
}

int simpleDeleteBatch(GmcStmtT *stmt, GmcConnT *conn, int recordCountStart, int recordCountEnd,
    const char *labelName, const char *keyName)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        int64_t F0 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0, sizeof(F0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int simpleDeleteBatchAsync(GmcStmtT *stmt, GmcConnT *conn, int recordCountStart, int recordCountEnd,
    const char *labelName, const char *keyName)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        int64_t F0 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0, sizeof(F0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    RETURN_IFERR(ret);
    ret = testWaitAsyncRecv(&data);
    RETURN_IFERR(ret);
    if (data.status != GMERR_LOCK_NOT_AVAILABLE) {
        EXPECT_EQ(GMERR_OK, data.status);
    }
    return ret;
}

// 写大于32k数据的表
int vertexWrite32K(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    char stringValue[4096];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[4095] = '\0';
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < recordCountEnd; i++) {
        uint32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 全表扫描
int vertexFullScan(GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

// 32k表缓存最大空间扫描
int vertex32kFullScan(GmcStmtT *stmt, uint32_t recordCountStart, uint32_t recordCountEnd,
    const char *labelName, uint32_t preFetch)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &preFetch, sizeof(preFetch));
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    bool isFinish = false;
    int cnt = 0;
    uint32_t expCacheRows = 0;
    expCacheRows = preFetch - 1;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        cnt++;
        uint32_t cacheRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_CACHE_ROWS, &cacheRows, sizeof(cacheRows));
        RETURN_IFERR(ret);
        if (cacheRows != expCacheRows) {
            expCacheRows = cacheRows;
        }
        EXPECT_EQ(expCacheRows, cacheRows);
        expCacheRows--;
    }
    uint32_t tmpPreFetch;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &tmpPreFetch, sizeof(tmpPreFetch));
    RETURN_IFERR(ret);
    EXPECT_EQ(preFetch, tmpPreFetch);
    return GMERR_OK;
}

void *ThreadSimpleStructWrite(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    for (int i = 0; i < 100; i++) {
        ret = simpleStructureWrite(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleStructureWriteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadComplexWrite(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaComplex";
    for (int i = 0; i < 100; i++) {
        ret = simpleWrite(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleWriteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSCWrite(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    for (int i = 0; i < 100; i++) {
        ret = simpleStructureWrite(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleStructureWriteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSimpleUpdate(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    for (int i = 0; i < 100; i++) {
        ret = simpleUpdate(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleUpdateAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadComplexUpdate(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaComplex";
    for (int i = 0; i < 100; i++) {
        ret = simpleUpdate(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleUpdateAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSCUpdate(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    for (int i = 0; i < 100; i++) {
        ret = simpleUpdate(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleUpdateAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadHashclusterUpdate(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    for (int i = 0; i < 100; i++) {
        ret = simpleUpdate(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, "hashcluster");
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleUpdateAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, "hashcluster");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadStructureSimpleUpdate(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    for (int i = 0; i < 100; i++) {
        ret = simpleStructureUpdate(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, PK_ID);
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleStructureUpdateAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, PK_ID);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadStructureComplexUpdate(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaComplex";
    for (int i = 0; i < 100; i++) {
        ret = simpleStructureUpdate(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, PK_ID);
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleStructureUpdateAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, PK_ID);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadStructureSCUpdate(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    for (int i = 0; i < 100; i++) {
        ret = simpleStructureUpdate(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, PK_ID);
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleStructureUpdateAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, PK_ID);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSimpleDelete(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    for (int i = 0; i < 100; i++) {
        ret = simpleDelete(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleDeleteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadComplexDelete(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaComplex";
    for (int i = 0; i < 100; i++) {
        ret = simpleDelete(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleDeleteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSCDelete(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    for (int i = 0; i < 100; i++) {
        ret = simpleDelete(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleDeleteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadHashclusterDelete(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    for (int i = 0; i < 100; i++) {
        ret = simpleDelete(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName, "hashcluster");
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleDeleteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName, "hashcluster");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadFilterDelete(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = GmcSetFilter(stmt, "schema_datatype.F1<=10000");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        int affectRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_LE(abs(10000 - affectRows), 10000);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadComplexStructWriteAsync(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaComplex";
    ret = simpleStructureWrite(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = simpleStructureWriteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSPComplexStructWrite(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *conn = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    ret = simpleStructureWrite(stmt, RECORDCOUNTSTART, RECORDCOUNTEND * 7 / 10, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = simpleStructureWriteAsync(stmtAsync, RECORDCOUNTEND * 7 / 10, RECORDCOUNTEND, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSimpleStructRead(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    ret = simpleStructureRead(stmt, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, PK_ID);
    EXPECT_EQ(GMERR_OK, ret);
    const char *keyName = (char *)"PK";
    ret = simpleKeyRead(stmt, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = simpleStructureRead(stmt, RECORDCOUNTEND - 1, RECORDCOUNTEND, labelName, PK_ID);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadComplexStructRead(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaComplex";
    const char *keyName = (char *)"PK";
    ret = simpleKeyRead(stmt, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSCStructRead(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    const char *keyName = (char *)"PK";
    ret = simpleKeyRead(stmt, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadDeleteAllFast(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadConn(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    for (int i = 0; i < 100; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return (void *)(int64_t)ret;
}

void *ThreadComplexStructWrite(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaComplex";
    ret = simpleWrite(stmt, RECORDCOUNTSTART, RECORDCOUNTEND, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSComplexStructWrite(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    ret = simpleStructureWrite(stmt, RECORDCOUNTSTART, RECORDCOUNTEND, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadFullScan(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    ret = vertexFullScan(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *Thread32kFullScan(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    ret = vertexFullScan(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSPtWriteDelete(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    for (int i = 0; i < 1; i++) {
        ret = simpleStructureWrite(stmt, RECORDCOUNTSTART, RECORDCOUNTEND, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = simpleDelete(stmt, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, "PK");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadBatchDelete(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    ret = simpleDeleteBatch(stmt, conn, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadBatchUpdate(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"simple";
    ret = simpleUpdateBatch(stmt, conn, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadTimeUpdate(void *arg)
{
    int ret = 0;
    for (int i = 0; i < 10; i++) {
        time_t t1;
        t1 = time(0);
        (void)srand(t1);
        char date[128];
        int year = rand() % 2 + 2023;
        int month = rand() % 11 + 1;
        int day = rand() % 27 + 1;
        ret = snprintf(date, 128, "date -s %d/%d/%d", month, day, year);
        printf("date=%s\n", date);
        ret = system(date);

        int hour = rand() % 24;
        int min = rand() % 60;
        int sec = rand() % 60;
        char cmd[128];
        ret = snprintf(cmd, 128, "date -s %d:%d:%d", hour, min, sec);
        ret = system(cmd);
        EXPECT_EQ(GMERR_OK, ret);
        sleep(1);
    }
    return (void *)(int64_t)ret;
}

void *ThreadSPtWriteDeleteAsync(void *arg)
{
    int ret = 0;
    GmcStmtT *stmtAsync = NULL;
    GmcConnT *connAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    ret = simpleStructureWriteAsync(stmtAsync, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, 0);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(3);
    ret = simpleDeleteAsync(stmtAsync, RECORDCOUNTSTART, RECORDCOUNTEND, labelName, "PK", 0);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(3);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSPAllFast(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = (char *)"schemaSpecialComplex";
    ret = GmcDeleteAllFast(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadSubAll(void *arg)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subSimple = "subSimple";
    const char *subComplex = "subComplex";
    const char *subSPComplex = "subSP";
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    int chanRingLen = 256;
    char subConnName[20] = "";
    int count = *(int *)arg;
    ret = snprintf(subConnName, 20, "subConnName_%d", count);
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = createSubscribe((char *)"schema/sn_simple.gmjson", subSimple, stmt, connSub, vertexSnCallback);
        ret = createSubscribe((char *)"schema/sn_complex.gmjson", subComplex, stmt, connSub, vertexSnCallback);
        ret = createSubscribe((char *)"schema/sn_sp_complex.gmjson", subSPComplex, stmt, connSub, vertexSnCallback);
        ret = GmcUnSubscribe(stmt, subSimple);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subComplex);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subSPComplex);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testSubDisConnect(connSub, stmtSub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

void *ThreadScanAll(void *arg)
{
    int ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    const char *simpleName = (char *)"simple";
    const char *SCName = (char *)"schemaSpecialComplex";
    const char *complexName = (char *)"schemaComplex";
    for (int i = 0; i < 100; i++) {
        ret = vertexFullScan(stmt, simpleName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = vertexFullScan(stmt, SCName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = vertexFullScan(stmt, complexName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return (void *)(int64_t)ret;
}

#endif
