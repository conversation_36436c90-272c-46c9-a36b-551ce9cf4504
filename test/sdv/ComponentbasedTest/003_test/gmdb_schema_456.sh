#!/bin/bash
# ----------------------------------------------------------------------------
# Description:  告警项功能测试(最大表记录数过载告警)
# Author: huangchucan wx1007418
# Create: 2021-08-30
# History: 
# Note: 
# ----------------------------------------------------------------------------
#测试步骤：
#1、使用gmsysview，设置告警8的time_interval为：5；执行：alarm set 8 time_interval 5  ；执行alarm detail 8；查看到time_interval为5
#2、使用gmimport导入if.gmjson以及同目录下的if.gmconfig;其中if.gmconfig内容为：
# { 
#  ""max_record_count"" : 128000,    
#  ""auto_increment"" : 1,
#  ""max_record_count_check"" : true
# }
#3、在gmsysview执行： table if，查看到max_record_count：128000；max_record_count_check 为1.
#2、单线程写入if表120000条数据：使用gmsysview查询，执行alarm有结果1。。
#3、查询/opt/vrpv8/var/log/stlm/err/stlmlog_err.csv文件，有结果2
#4、删除30000条数据；执行alarm all 有结果3；
#5、查询/opt/vrpv8/var/log/stlm/err/stlmlog_err.csv文件，有结果4
#6、再次执行插入30000条数据，使用gmsysview查询，执行alarm有结果1。
#7、删除全部数据：使用gmsysview查询，执行alarm detail 8有结果3。
#8、恢复告警阀值：alarm set 8 time_interval 300； 
#预期结果：
#1、查询到active的告警信息。告警ID为8，标题为Table's max record count monitor
#2、查询到关键信息xx
#3、查询到告警恢复信息，显示为clear；
#4、查询到告警恢复的关键信息xx
# ----------------------------------------------------------------------------

ENV_TYPE=$1
tEnv=$(uname -a|grep Hongmeng|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="harmony"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"

if [ -z $ENV_TYPE ] || [ "x$ENV_TYPE" = "xeuler" ]; then
    envType=0
    runMode=0
    environ="RTOS"
    server_locator="usocket:/run/verona/unix_emserver"
elif [ "x$ENV_TYPE" = "xdopra" ]; then
    envType=1
    runMode=1
elif [ "x$ENV_TYPE" = "xharmony" ]; then
    envType=2
    runMode=1
else
    echo "usage: $0 <euler/dopra/harmony>"
    exit 1
fi

function clean_process()
{
        for processPid in `ps -aux|grep './tool/'|grep -v grep|awk '{print $2}'`
	do
		kill -9 $processPid
	done
}
function check_gmserverId()
{
    gmserverId=`ps -ef | grep gmserver | grep -v grep | awk '{print $2}' | wc -l`
    hpkId=`ps | grep vm.elf | grep -v grep | awk '{print $2}' | wc -l`
    if [ $gmserverId -le 0 ] && [ $hpkId -le 0 ];then
       echo "----------gmserver is exit!----------"
       kill -9 `pidof teststa` >/dev/null
       exit 1
    fi
}

usr="user123"
pwd="password.123"
environ="RTOS"
server_locator="usocket:/run/verona/unix_emserver"
teststa="${TEST_HOME}/perf/stability/teststa"
toolLog="log_tool"
runLog="log_teststa"

> ${runLog}

# $1 labelName
function toolCreateVertex
{
    TlabelName=$1
    TschemaPath="../../../schema_file/lite_ndb"
    rm -rf ${TlabelName}_bak.gmjson
    cp ${TschemaPath}/${TlabelName}.gmjson ${TlabelName}_bak.gmjson
    sed -i "s/\<${TlabelName}\>/${TlabelName}00000/" ${TlabelName}_bak.gmjson
    gmimport -c vschema -f ${TlabelName}_bak.gmjson
    rm -rf ${TlabelName}_bak.gmjson
}
kill -9 `pidof teststa` >/dev/null
clean_process
stop.sh -f
start.sh -f

#testcaseName: gmdb_schema_456
# 最大表记录数(V3告警项) V5对应视图
labelName="if"
labelNameTool="${labelName}00000"
if [ $envType -eq 0 ]; then
    threadCount=100
    processCount=5
    ViewCount=100
elif [ $envType -eq 1 ]; then
    threadCount=10
    processCount=2
    ViewCount=10
elif [ $envType -eq 2 ]; then
    threadCount=10
    processCount=2
    ViewCount=10
else
    exit 1
fi
if [ -z $1 ]; then
    echo "[gmdb_schema_456] test by signal vertex"
    rm -rf ${labelName}_bak.gmconfig
    cp config/autoIncrement.gmconfig ${labelName}_bak.gmconfig

    if [ $envType -eq 0 ]; then
        sh ./tool/createVertex.sh ${labelName}
    elif [ $envType -eq 1 ]; then
        sh ./tool/createVertexIot.sh ${labelName}
    elif [ $envType -eq 2 ]; then
        sh ./tool/createVertexIot.sh ${labelName}
    else
        exit 1
    fi
    rm -rf ${labelName}_bak.gmconfig
    sh ./tool/gmsysviewCata.sh "V\$CATA_VERTEX_LABEL_INFO" "VERTEX_LABEL_NAME" ${labelNameTool} > ${toolLog}
    cat ${toolLog} | grep "max_record_num"
    ${teststa} -v -m insert -n ${labelName} -b 0 -e 120000 -c ${threadCount} >> ${runLog}
    sh ./tool/gmsysviewCata.sh count ${labelNameTool} &
    ${teststa} -v -m delete -n ${labelName} -b 0 -e 30000 -c ${threadCount} >> ${runLog}
    sh ./tool/gmsysviewCata.sh count ${labelNameTool} &
    ${teststa} -v -m insert -n ${labelName} -b 0 -e 30000 -c ${threadCount} >> ${runLog}
    sh ./tool/gmsysviewCata.sh count ${labelNameTool} &
    ${teststa} -v -m delete -n ${labelName} -b 0 -e 120000 -c ${threadCount} >> ${runLog}
    sh ./tool/gmsysviewCata.sh count ${labelNameTool} &
elif [ "$1" == "all" ]; then
    echo "[gmdb_schema_456] test by all vertex"
else
    echo "[gmdb_schema_456]  parameter undefine."
fi
clean_process
check_gmserverId
kill -9 `pidof teststa` >/dev/null
    ${teststa} -v -D -n ${labelName} >/dev/null
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
