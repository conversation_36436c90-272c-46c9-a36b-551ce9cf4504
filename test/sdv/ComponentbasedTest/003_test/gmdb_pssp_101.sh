#!/bin/bash
# ----------------------------------------------------------------------------
# Description:  简单表的同步普通删时内存回收
# Author: huangchucan wx1007418
# Create: 2021-08-30
# History: 
# Note: 
# ----------------------------------------------------------------------------
#测试步骤：
#1.选取一张简单表A，进行反复写同步普通删（db_remove）操作，检查删除时内存是否正确回收，有预期结果1；
#预期结果：
#1.正确回收
# ----------------------------------------------------------------------------

ENV_TYPE=$1
tEnv=$(uname -a|grep Hongmeng|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="harmony"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"

if [ -z $ENV_TYPE ] || [ "x$ENV_TYPE" = "xeuler" ]; then
    envType=0
    runMode=0
    environ="RTOS"
    server_locator="usocket:/run/verona/unix_emserver"
elif [ "x$ENV_TYPE" = "xdopra" ]; then
    envType=1
    runMode=1
elif [ "x$ENV_TYPE" = "xharmony" ]; then
    envType=2
    runMode=1
else
    echo "usage: $0 <euler/dopra/harmony>"
    exit 1
fi

function clean_process()
{
        for processPid in `ps -aux|grep './tool/'|grep -v grep|awk '{print $2}'`
	do
		kill -9 $processPid
	done
}
function check_gmserverId()
{
    gmserverId=`ps -ef | grep gmserver | grep -v grep | awk '{print $2}' | wc -l`
    hpkId=`ps | grep vm.elf | grep -v grep | awk '{print $2}' | wc -l`
    if [ $gmserverId -le 0 ] && [ $hpkId -le 0 ];then
       echo "----------gmserver is exit!----------"
       kill -9 `pidof teststa` >/dev/null
       exit 1
    fi
}
kill -9 `pidof teststa` >/dev/null
clean_process
stop.sh -f

usr="user123"
pwd="password.123"
teststa="${TEST_HOME}/perf/stability/teststa"
toolLog="log_tool"
runLog="log_teststa"

> ${runLog}

# $1 labelName
function toolCreateVertex
{
    TlabelName=$1
    TschemaPath="../../../schema_file/lite_ndb"
    rm -rf ${TlabelName}_bak.gmjson
    cp ${TschemaPath}/${TlabelName}.gmjson ${TlabelName}_bak.gmjson
    sed -i "s/\<${TlabelName}\>/${TlabelName}00000/" ${TlabelName}_bak.gmjson
    gmimport -c vschema -f ${TlabelName}_bak.gmjson
    rm -rf ${TlabelName}_bak.gmjson
}
modifyCfg.sh "memCompactEnable=1"
kill -9 `pidof teststa` >/dev/null
clean_process
start.sh -f
modifyCfg.sh "recover"
sh ./tool/gmsysviewCata.sh "V\$CONFIG_PARAMETERS" "NAME" "memCompactEnable" |grep -e "NAME" -e "VALUE"

labelName="ip4forward"
labelNameTool="${labelName}00000"
if [ $envType -eq 0 ]; then
    threadCount=30
    ViewCount=10
    startId=0
    endId=50000
elif [ $envType -eq 1 ]; then
    threadCount=3
    ViewCount=2
    startId=0
    endId=5000
elif [ $envType -eq 2 ]; then
    threadCount=3
    ViewCount=2
    startId=0
    endId=5000
else
    exit 1
fi
#testcaseName: gmdb_pssp_101
function testcase_TESTF
{
    labelName=$1
    labelNameTool="${labelName}00000"

    if [ $envType -eq 0 ]; then
        sh ./tool/createVertex.sh -m ${labelName} 1 -c "defragmentation.gmconfig"
    elif [ $envType -eq 1 ]; then
        sh ./tool/createVertexIot.sh ${labelName} 1 "defragmentation.gmconfig"
    elif [ $envType -eq 2 ]; then
        sh ./tool/createVertexIot.sh ${labelName} 1 "defragmentation.gmconfig"
    else
        exit 1
    fi
    #replace and delete data
    ${teststa} -v -m replace -n ${labelName} -b $startId -e $endId -c ${threadCount} | \
        grep -e errCode -e errcode -e threadAggregate
    sh ./tool/gmsysviewCata.sh "V\$STORAGE_HEAP_VERTEX_LABEL_STAT" "VERTEXLABEL_NAME" ${labelNameTool} | \
        grep -e "HEAP_ROW_NUM" -e "DATA_PAGE_UNUSED_SIZE" -e "  TOTAL_PAGE_SIZE"
    ${teststa} -v -m delete -n ${labelName} -b $startId -e $endId -c ${threadCount} | \
        grep -e errCode -e errcode -e threadAggregate
    sh ./tool/gmsysviewCata.sh "V\$STORAGE_HEAP_VERTEX_LABEL_STAT" "VERTEXLABEL_NAME" ${labelNameTool} > ${toolLog}
    cat ${toolLog} |grep -e "HEAP_ROW_NUM" -e "DATA_PAGE_UNUSED_SIZE" -e "  TOTAL_PAGE_SIZE"
    DATA_PAGE_UNUSED_SIZE=`cat ${toolLog} |grep -e "DATA_PAGE_UNUSED_SIZE"| awk -F '[:]' '{print $2}'`
    MEMORY_TOTAL_SIZE=`cat ${toolLog} |grep -e "  TOTAL_PAGE_SIZE"| awk -F '[:]' '{print $2}'`
    i=1
    while [ $i -le $ViewCount ]
    do
        let i=i+1
        #replace and delete data
        ${teststa} -v -m replace -n ${labelName} -b $startId -e $endId -c ${threadCount} | \
            grep -e errCode -e errcode -e threadAggregate
        ${teststa} -v -m delete -n ${labelName} -b $startId -e $endId -c ${threadCount} | \
            grep -e errCode -e errcode -e threadAggregate
        sh ./tool/gmsysviewCata.sh "V\$STORAGE_HEAP_VERTEX_LABEL_STAT" "VERTEXLABEL_NAME" ${labelNameTool} > ${toolLog}
        #cat ${toolLog} |grep -e "HEAP_ROW_NUM" -e "DATA_PAGE_UNUSED_SIZE" -e "MEMORY_TOTAL_SIZE"
        TDATA_PAGE_UNUSED_SIZE=`cat ${toolLog} |grep -e "DATA_PAGE_UNUSED_SIZE"| awk -F '[:]' '{print $2}'`
        TMEMORY_TOTAL_SIZE=`cat ${toolLog} |grep -e "  TOTAL_PAGE_SIZE"| awk -F '[:]' '{print $2}'`
        #compare view field
        if [ "$TDATA_PAGE_UNUSED_SIZE" = "" ]; then
            cat ${toolLog}
        elif [ $TDATA_PAGE_UNUSED_SIZE -ne $DATA_PAGE_UNUSED_SIZE ]; then
            echo DATA_PAGE_UNUSED_SIZE $DATA_PAGE_UNUSED_SIZE TDATA_PAGE_UNUSED_SIZE $TDATA_PAGE_UNUSED_SIZE
            DATA_PAGE_UNUSED_SIZE=$TDATA_PAGE_UNUSED_SIZE
            cat ${toolLog}
        else
            echo i $i/$ViewCount
        fi
    done

    ${teststa} -v -D -n ${labelName} >/dev/null
}
if [ -z $2 ]; then
    echo "[gmdb_pssp_101] test by signal vertex"
    labelName="ip4forward"
    testcase_TESTF ${labelName}
elif [ "$2" == "all" ]; then
    echo "[gmdb_pssp_101] test by all vertex"
    for schemaFileName in `ls schema/*.gmjson`
    do
        echo [vertexName] [INFO] : $schemaFileName `date`
        > ${runLog}
        labelName=`echo $schemaFileName |awk -F '[/.]' '{print $2}'`
        lableConfig=`find -name $labelName.gmconfig`
        if [ "$lableConfig" != "" ]; then
            rm -rf ${labelName}_bak.gmconfig
            cp schema/$labelName.gmconfig ${labelName}_bak.gmconfig
        fi
        testcase_TESTF ${labelName}
        rm -rf ${labelName}_bak.gmconfig
    done

else
    echo "[gmdb_pssp_101] parameter undefine."
fi
clean_process
check_gmserverId
kill -9 `pidof teststa` >/dev/null
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
