#! /bin/bash
# ----------------------------------------------------------------------------
# Description:  多个消费者订阅多个生产者
# Author: wangweida wx994638
# Create: 2021-09-01
# History: 
# ----------------------------------------------------------------------------
ENV_TYPE=$1
tEnv=$(uname -a|grep Hongmeng|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="harmony"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"

if [ X"$ENV_TYPE" == "Xharmony" ];then
      gmserverId=`ps | grep /usr/bin/vm.elf | grep -v grep | awk '{print $1}' | wc -l`
else
      gmserverId=`ps | grep gmserver | grep -v grep | awk '{print $1}' | wc -l`
fi
function check_gmserverId()
{
    if [ $gmserverId -le 0 ];then
       echo "----------gmserver is exit!----------"
       # IOT上无法使用pidof xxx形式
       teststaId=`ps | grep teststa |grep -v grep|awk {'print $1'}`
       # IOT上无法使用exit 1 形式， 直接导致脚本无法继续执行
       kill -9 $teststaId
       # exit 1
    fi
}

if [ "X$ENV_TYPE" == "Xharmony" ];then
   echo "do nothing"
else
    stop.sh -f
    sh $TEST_HOME/tools/modifyCfg.sh "workerHungThreshold=6,200,300"
    start.sh -f
fi
../test_execute_all.sh

usr="user123"
pwd="password.123"
environ="RTOS"
server_locator="usocket:/run/verona/unix_emserver"
FILE_PATH=${TEST_HOME}/perf/stability/ComponentbasedTest/schema/ip4forward.gmjson
schema=ip4forward
thread_count=30
start_id=0
max_record_nums=400000
if [ X"$ENV_TYPE" == "Xharmony" ];then
   let end_id=max_record_nums/400
   CYCLE_COUNT=2
else
   let end_id=max_record_nums/8
   CYCLE_COUNT=25
fi



echo $end_id

# $1生产者数 
function op_sub_data(){
loop=0
echo $1

while [ ${loop} -lt $1 ]
do
      ${TEST_HOME}/perf/stability/teststa -v -p ip4forward &
      ${TEST_HOME}/perf/stability/teststa -v -m replace -n $schema -b $start_id -e $end_id 
      ${TEST_HOME}/perf/stability/teststa -v -p ip4forward &
      ${TEST_HOME}/perf/stability/teststa -v -m delete -n $schema -b  $start_id -e $end_id 
      ${TEST_HOME}/perf/stability/teststa -v -p ip4forward &
      ${TEST_HOME}/perf/stability/teststa -v -m update -n $schema -b  $start_id -e $end_id 
      ${TEST_HOME}/perf/stability/teststa -v -p ip4forward &
      ${TEST_HOME}/perf/stability/teststa -v -m merge -n $schema -b $start_id -e $end_id 
    let loop+=1
done

}
op_sub_data ${CYCLE_COUNT}
#${TEST_HOME}/perf/stability/teststa -v -L "is_full_sync=1,is_reliable=1,persist=1" -p:1024 ip4forward &
#CYCLE_COUNT1=256
#op_sub_data ${CYCLE_COUNT1}
check_gmserverId
kill -9 `pidof teststa`
schema1=aaa_account_scheme
schema2=if
schema3=if_statistics
schema4=ip4forward
schema5=nhp_group
${TEST_HOME}/perf/stability/teststa -D -n $schema1 
${TEST_HOME}/perf/stability/teststa -D -n $schema2
${TEST_HOME}/perf/stability/teststa -D -n $schema3 
${TEST_HOME}/perf/stability/teststa -D -n $schema4 
${TEST_HOME}/perf/stability/teststa -D -n $schema5 
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
