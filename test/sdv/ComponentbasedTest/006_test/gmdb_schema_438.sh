#!/bin/bash
# ----------------------------------------------------------------------------
# Description:  场景测试：使用lpm扫描，其中使用db_set_root_iterator_property接口设置单次扫描条数大于1，扫描正常，礽仍单次获取一条数据。
# Author: lia<PERSON>iang wx1036939
# Create: 2022-05-23
# History: 
# Note: 
# ----------------------------------------------------------------------------
#测试步骤：
#1、在ip4forward表中写入10条数据，使用gmsysview查看：count ip4forward.查看到10条数据。
#2、启动进程：线程A设置单次扫描参数每次获取5条数据，使用lpm扫描：db_create_ip4forward_obj->db_create_root_iter->
#db_set_root_iterator_property；db_root_iterator_next(root_iterator)-->db_get_root_iterator_cache_count；有结果1
#3、清理环境，删除10条数据
#预期结果：
#1、1、线程A获取10条数据，其中db_get_root_iterator_cache_count的输出参数值为0
# ----------------------------------------------------------------------------

ENV_TYPE=$1
tEnv=$(uname -a|grep Hongmeng|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="harmony"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"

if [ -z $ENV_TYPE ] || [ "x$ENV_TYPE" = "xeuler" ]; then
    envType=0
    runMode=0
    environ="RTOS"
    server_locator="usocket:/run/verona/unix_emserver"
elif [ "x$ENV_TYPE" = "xdopra" ]; then
    envType=1
    runMode=1
elif [ "x$ENV_TYPE" = "xharmony" ]; then
    envType=2
    runMode=1
else
    echo "usage: $0 <euler/dopra/harmony>"
    exit 1
fi

function clean_process()
{
        for processPid in `ps -aux|grep './tool/'|grep -v grep|awk '{print $2}'`
	do
		kill -9 $processPid
	done
}
function check_gmserverId()
{
    gmserverId=`ps -ef | grep gmserver | grep -v grep | awk '{print $2}' | wc -l`
    hpkId=`ps | grep vm.elf | grep -v grep | awk '{print $2}' | wc -l`
    if [ $gmserverId -le 0 ] && [ $hpkId -le 0 ];then
       echo "----------gmserver is exit!----------"
       kill -9 `pidof teststa` >/dev/null
       exit 1
    fi
}
#kill -9 `pidof teststa` >/dev/null
#clean_process
start.sh -f

usr="user123"
pwd="password.123"
environ="RTOS"
server_locator="usocket:/run/verona/unix_emserver"
teststa="${TEST_HOME}/perf/stability/teststa"
toolLog="log_tool"
runLog="log_teststa"

#testcaseName: gmdb_schema_438
labelName="ip4forward"
labelNameTool="${labelName}00000"
localIndexName="localhash_key"
scanCount=500
if [ $envType -eq 0 ]; then
    threadCount=50
elif [ $envType -eq 1 ]; then
    threadCount=5
elif [ $envType -eq 2 ]; then
    threadCount=5
else
    exit 1
fi
if [ -z $1 ]; then
    echo "[gmdb_schema_438] test by signal vertex"

    if [ $envType -eq 0 ]; then
        sh ./tool/createVertex.sh ${labelName}
    elif [ $envType -eq 1 ]; then
        sh ./tool/createVertexIot.sh ${labelName}
    elif [ $envType -eq 2 ]; then
        sh ./tool/createVertexIot.sh ${labelName}
    else
        exit 1
    fi
    ${teststa} -v -m replace -n ${labelName} -b 0 -e 50000 -c ${threadCount} >>${runLog}
    sh ./tool/gmsysviewCata.sh count ${labelNameTool} &
    ${teststa} -v -m scan -n ${labelName} -i ${lpmIndexName} -b 0 -e ${scanCount} -c ${threadCount} >>${runLog} &
    ${teststa} -v -D -n ${labelName} > /dev/null
elif [ "$1" == "all" ]; then
    echo "[gmdb_schema_438] test by all vertex"
else
    echo "[gmdb_schema_438] parameter undefine."
fi
clean_process
check_gmserverId
kill -9 `pidof teststa` >/dev/null
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
