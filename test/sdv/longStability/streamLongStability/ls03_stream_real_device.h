/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 业务场景，来源于DT用例RealDeviceTestcase
 * Author: guopanpan
 * Create: 2024-11-05
 */
#ifndef LS01_STREAM_REAL_DEVICE_H
#define LS01_STREAM_REAL_DEVICE_H 1
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <pthread.h>
#include "rd_process.h"
#include "ls_stat.h"
#include "ls_run.h"
#include "ls_config.h"
#include "ls_manager.h"
#include "ls_heartbeat.h"

#include "securec.h"
#include "gmc_sql.h"
#include "gmc_persist.h"
#include "rd_feature_stream.h"

#define LS_STREAM_REAL_DEVICE_MAX_SQL_LEN 1024
#define LS_STREAM_REAL_DEVICE_MAX_NAME_LEN 128
#define LS_STREAM_REAL_DEVICE_TEST_NAME "StreamRealDevice"

#define RD_STREAM_LS02_NAME_SIZE 50
#define RD_STREAM_LS02_CONTENT_SIZE 120
#define RD_STREAM_LS02_MAX_LEVEL 5
#define RD_STREAM_LS02_MAX_SQL_SIZE 512

typedef struct LsStreamRealDeviceThreadT {
    pthread_t thread;   // 线程句柄
    int32_t index;      // 标识第几条线程
    int32_t status;     // 执行状态
    RdVertexLabelT *vertexLabel;
    int64_t rowStart;
    int64_t rowNum;
    int64_t seed;
} LsStreamRealDeviceThreadT;

typedef struct LsStreamRealDeviceCtxT {
    uint32_t nThread;               // 线程数量
    LsStreamRealDeviceThreadT *threads;    // 线程上下文
} LsStreamRealDeviceCtxT;

int32_t LsStreamReadDeviceCreateNodes(GmcStmtT *stmt)
{
    // 定义 SQL 语句
    const char *commands[] = {
        // 创建表
        "CREATE TABLE IF NOT EXISTS im_log_tbl (log_time INTEGER, from_syslog INTEGER, use_id INTEGER, log_id INTEGER, "
        "log_type INTEGER, pid INTEGER, instance_id INTEGER, alm_state INTEGER, alm_level INTEGER, clear_type INTEGER, "
        "reason_idx INTEGER, begin_time INTEGER, end_time INTEGER, info_type INTEGER, param_num INTEGER, facility "
        "INTEGER, level INTEGER, name CHAR(32), service_name CHAR(32), host_name CHAR(32), log_params CHAR(512)) WITH "
        "(time_col = 'log_time', interval = '1 hour', disk_limit = '10 MB')",

        "CREATE TABLE IF NOT EXISTS syslog_tbl (log_time INTEGER, from_syslog INTEGER, host_name CHAR(32), "
        "service_name CHAR(32), pid INTEGER, facility INTEGER, level INTEGER, seq_times INTEGER, log_content "
        "CHAR(512)) WITH (time_col = 'log_time', interval = '1 hour', disk_limit = '1 MB')",

        "CREATE TABLE IF NOT EXISTS log_log_tbl (log_time INTEGER, from_syslog INTEGER, log_id INTEGER, pid INTEGER, "
        "facility INTEGER, level INTEGER, instance_id INTEGER, seq_times INTEGER, host_name CHAR(32), service_name "
        "CHAR(32), log_content CHAR(512)) WITH (time_col = 'log_time', interval = '1 hour', disk_limit = '1 MB')",

        "CREATE TABLE IF NOT EXISTS diag_log_tbl (log_time INTEGER, from_syslog INTEGER, log_id INTEGER, pid INTEGER, "
        "facility INTEGER, level INTEGER, instance_id INTEGER, seq_times INTEGER, host_name CHAR(32), service_name "
        "CHAR(32), log_content CHAR(512)) WITH (time_col = 'log_time', interval = '1 hour', disk_limit = '1 MB')",

        "CREATE TABLE IF NOT EXISTS event_log_tbl (log_time INTEGER, from_syslog INTEGER, log_id INTEGER, pid INTEGER, "
        "facility INTEGER, level INTEGER, instance_id INTEGER, seq_times INTEGER, host_name CHAR(32), service_name "
        "CHAR(32), log_content CHAR(512)) WITH (time_col = 'log_time', interval = '1 hour', disk_limit = '1 MB')",

        "CREATE TABLE IF NOT EXISTS oper_log_tbl (log_time INTEGER, from_syslog INTEGER, log_id INTEGER, pid INTEGER, "
        "facility INTEGER, level INTEGER, instance_id INTEGER, seq_times INTEGER, host_name CHAR(32), service_name "
        "CHAR(32), log_content CHAR(512)) WITH (time_col = 'log_time', interval = '1 hour', disk_limit = '1 MB')",

        "CREATE TABLE IF NOT EXISTS security_log_tbl (log_time INTEGER, from_syslog INTEGER, log_id INTEGER, pid "
        "INTEGER, facility INTEGER, level INTEGER, instance_id INTEGER, seq_times INTEGER, host_name CHAR(32), "
        "service_name CHAR(32), log_content CHAR(512)) WITH (time_col = 'log_time', interval = '1 hour', disk_limit = "
        "'1 MB')",

        "CREATE TABLE IF NOT EXISTS kernel_log_tbl (log_time INTEGER, facility INTEGER, level INTEGER, seq_times "
        "INTEGER, host_name CHAR(32), service_name CHAR(32), log_content CHAR(512)) WITH (time_col = 'log_time', "
        "interval = '1 hour', disk_limit = '1 MB')",

        "CREATE TABLE IF NOT EXISTS starry_log_tbl (log_time INTEGER, facility INTEGER, level INTEGER, seq_times "
        "INTEGER, host_name CHAR(32), service_name CHAR(32), log_content CHAR(512)) WITH (time_col = 'log_time', "
        "interval = '1 hour')",

        "CREATE TABLE IF NOT EXISTS im_log_schema_tbl (log_time INTEGER, log_id INTEGER, log_type INTEGER, facility "
        "INTEGER, level INTEGER, security_flag INTEGER, trap_flag INTEGER, suppress_period INTEGER, suppress_count "
        "INTEGER, notif_param_num INTEGER, param_num INTEGER, feature CHAR(32), log_name CHAR(32), notif_name "
        "CHAR(32), output_en CHAR(512), file_hash INTEGER, notif_params CHAR(512), params BLOB(512)) WITH (time_col = "
        "'log_time', interval = '1 hour', disk_limit = '5 MB')",

        "CREATE TABLE IF NOT EXISTS im_alarm_schema_tbl (log_time INTEGER, log_id INTEGER, log_type INTEGER, level "
        "INTEGER, event_type INTEGER, clear_type INTEGER, alarm_id INTEGER, alarm_suppress_time INTEGER, clear_id "
        "INTEGER, clear_suppress_time INTEGER, param_num INTEGER, name CHAR(32), feature CHAR(32), alarm_name "
        "CHAR(32), clear_name CHAR(32), alarm_notif_name CHAR(32), clear_notif_name CHAR(32), alarm_output_en "
        "CHAR(512), clear_output_en CHAR(512), file_hash INTEGER, alarm_notif_param_seq CHAR(512), "
        "clear_notif_param_seq CHAR(512), params BLOB(512)) WITH (time_col = 'log_time', interval = '1 hour', "
        "disk_limit = '5 MB')",

        // 创建流表
        "CREATE STREAM TABLE im_log_tbl_stream (log_time INTEGER, from_syslog INTEGER, use_id INTEGER, log_id INTEGER, "
        "log_type INTEGER, pid INTEGER, instance_id INTEGER, alm_state INTEGER, alm_level INTEGER, clear_type INTEGER, "
        "reason_idx INTEGER, begin_time INTEGER, end_time INTEGER, info_type INTEGER, param_num INTEGER, name "
        "CHAR(32), service_name CHAR(32), host_name CHAR(32), log_params CHAR(512), watermark for log_time as "
        "log_time)",

        "CREATE STREAM TABLE syslog_tbl_stream (log_time INTEGER, from_syslog INTEGER, host_name CHAR(32), "
        "service_name CHAR(32), pid INTEGER, facility INTEGER, level INTEGER, log_content CHAR(512), watermark for "
        "log_time as log_time)",

        // 创建Sink
        "CREATE STREAM SINK syslog_sink AS SELECT log_time, from_syslog, host_name, service_name, pid, facility, "
        "level, seq_distinct_count(pid, facility, level, log_content), log_content FROM syslog_tbl_stream INTO "
        "tsdb(syslog_tbl) WITH (batch_window_size = '200', timeout = 1)",

        // 创建引用
        "create stream reference facility(integer, integer);",
        "create stream reference level(integer, integer);",
        "create stream reference windowlimit(integer, integer);",
        "create stream reference windowlen(integer, integer);",

        "upsert into streamref facility values(0, 10000000);",
        "upsert into streamref level values(0, 10000000);",
        "upsert into streamref windowlimit values(0, 10000000); ",
        "upsert into streamref windowlen values(0, 10000000);",

        // 创建视图和Sink
        "CREATE STREAM VIEW im_log_w_ref_view AS SELECT log_time, from_syslog, use_id, log_id, log_type, pid, "
        "instance_id, alm_state, alm_level, clear_type, reason_idx, begin_time, end_time, info_type, param_num, "
        "ref['facility'][log_id], ref['level'][log_id], name, service_name, host_name, log_params FROM "
        "im_log_tbl_stream",

        "CREATE STREAM SINK im_log_sink AS SELECT log_time, from_syslog, use_id, log_id, log_type, pid, instance_id, "
        "alm_state, alm_level, clear_type, reason_idx, begin_time, end_time, info_type, param_num, "
        "ref_facility_log_id, ref_level_log_id, name, service_name, host_name, log_params FROM im_log_w_ref_view INTO "
        "tsdb(im_log_tbl) WITH (batch_window_size = '200', timeout = 1)",

        // 创建不同时间间隔的窗口视图和Sink
        // 3600秒窗口
        "CREATE STREAM VIEW im_log_window_view_3600 AS SELECT log_time, from_syslog, use_id, log_id, log_type, pid, "
        "instance_id, alm_state, alm_level, clear_type, reason_idx, begin_time, end_time, info_type, param_num, name, "
        "service_name, host_name, log_params, ROW_NUMBER() OVER (PARTITION BY window_start, window_end, log_id) FROM "
        "TABLE(TUMBLE(table im_log_tbl_stream, log_time, INTERVAL '3600' SECONDS))",
        "CREATE STREAM VIEW im_log_limit_view_3600 AS SELECT * FROM im_log_window_view_3600 WHERE row_number < "
        "ref['windowlimit'][log_id] AND ref['windowlen'][log_id] = 3600",
        "CREATE STREAM SINK sink_no_0 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_3600 WHERE ref['facility'][log_id] = 152 AND ref['level'][log_id] = 6 INTO "
        "tsdb(security_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_1 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_3600 WHERE ref['facility'][log_id] = 136 INTO tsdb(diag_log_tbl) WITH "
        "(batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_2 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_3600 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 5 INTO "
        "tsdb(event_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_3 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_3600 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 6 INTO "
        "tsdb(oper_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_4 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_3600 WHERE (ref['facility'][log_id] != 128 AND ref['facility'][log_id] != "
        "136 AND ref['facility'][log_id] != 0 AND ref['facility'][log_id] != 168) AND ((ref['facility'][log_id] = 144 "
        "AND ref['level'][log_id] != 6 AND ref['level'][log_id] != 5) OR (ref['facility'][log_id] = 152 AND "
        "ref['level'][log_id] != 6) OR (ref['facility'][log_id] = 160 AND ref['level'][log_id] != 7)) INTO "
        "tsdb(log_log_tbl) WITH (batch_window_size = '200', timeout = 1)",

        // 60秒窗口
        "CREATE STREAM VIEW im_log_window_view_60 AS SELECT log_time, from_syslog, use_id, log_id, log_type, pid, "
        "instance_id, alm_state, alm_level, clear_type, reason_idx, begin_time, end_time, info_type, param_num, name, "
        "service_name, host_name, log_params, ROW_NUMBER() OVER (PARTITION BY window_start, window_end, log_id) FROM "
        "TABLE(TUMBLE(table im_log_tbl_stream, log_time, INTERVAL '60' SECONDS))",
        "CREATE STREAM VIEW im_log_limit_view_60 AS SELECT * FROM im_log_window_view_60 WHERE row_number < "
        "ref['windowlimit'][log_id] AND ref['windowlen'][log_id] = 60",
        "CREATE STREAM SINK sink_no_5 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_60 WHERE ref['facility'][log_id] = 152 AND ref['level'][log_id] = 6 INTO "
        "tsdb(security_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_6 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_60 WHERE ref['facility'][log_id] = 136 INTO tsdb(diag_log_tbl) WITH "
        "(batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_7 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_60 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 5 INTO "
        "tsdb(event_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_8 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_60 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 6 INTO "
        "tsdb(oper_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_9 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_60 WHERE (ref['facility'][log_id] != 128 AND ref['facility'][log_id] != 136 "
        "AND ref['facility'][log_id] != 0 AND ref['facility'][log_id] != 168) AND ((ref['facility'][log_id] = 144 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 5) OR (ref['facility'][log_id] = 152 AND "
        "ref['level'][log_id] != 6) OR (ref['facility'][log_id] = 160 AND ref['level'][log_id] != 7)) INTO "
        "tsdb(log_log_tbl) WITH (batch_window_size = '200', timeout = 1)",

        // 300秒窗口
        "CREATE STREAM VIEW im_log_window_view_300 AS SELECT log_time, from_syslog, use_id, log_id, log_type, pid, "
        "instance_id, alm_state, alm_level, clear_type, reason_idx, begin_time, end_time, info_type, param_num, name, "
        "service_name, host_name, log_params, ROW_NUMBER() OVER (PARTITION BY window_start, window_end, log_id) FROM "
        "TABLE(TUMBLE(table im_log_tbl_stream, log_time, INTERVAL '300' SECONDS))",
        "CREATE STREAM VIEW im_log_limit_view_300 AS SELECT * FROM im_log_window_view_300 WHERE row_number < "
        "ref['windowlimit'][log_id] AND ref['windowlen'][log_id] = 300",
        "CREATE STREAM SINK sink_no_10 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_300 WHERE ref['facility'][log_id] = 152 AND ref['level'][log_id] = 6 INTO "
        "tsdb(security_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_11 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_300 WHERE ref['facility'][log_id] = 136 INTO tsdb(diag_log_tbl) WITH "
        "(batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_12 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_300 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 5 INTO "
        "tsdb(event_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_13 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_300 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 6 INTO "
        "tsdb(oper_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_14 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_300 WHERE (ref['facility'][log_id] != 128 AND ref['facility'][log_id] != "
        "136 AND ref['facility'][log_id] != 0 AND ref['facility'][log_id] != 168) AND ((ref['facility'][log_id] = 144 "
        "AND ref['level'][log_id] != 6 AND ref['level'][log_id] != 5) OR (ref['facility'][log_id] = 152 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 6) OR (ref['facility'][log_id] = 160 AND "
        "ref['level'][log_id] != 7)) INTO tsdb(log_log_tbl) WITH (batch_window_size = '200', timeout = 1)",

        // 1秒窗口
        "CREATE STREAM VIEW im_log_window_view_1 AS SELECT log_time, from_syslog, use_id, log_id, log_type, pid, "
        "instance_id, alm_state, alm_level, clear_type, reason_idx, begin_time, end_time, info_type, param_num, name, "
        "service_name, host_name, log_params, ROW_NUMBER() OVER (PARTITION BY window_start, window_end, log_id) FROM "
        "TABLE(TUMBLE(table im_log_tbl_stream, log_time, INTERVAL '1' SECONDS))",
        "CREATE STREAM VIEW im_log_limit_view_1 AS SELECT * FROM im_log_window_view_1 WHERE row_number < "
        "ref['windowlimit'][log_id] AND ref['windowlen'][log_id] = 1",
        "CREATE STREAM SINK sink_no_15 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_1 WHERE ref['facility'][log_id] = 152 AND ref['level'][log_id] = 6 INTO "
        "tsdb(security_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_16 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_1 WHERE ref['facility'][log_id] = 136 INTO tsdb(diag_log_tbl) WITH "
        "(batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_17 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_1 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 5 INTO "
        "tsdb(event_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_18 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_1 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 6 INTO "
        "tsdb(oper_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_19 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_1 WHERE (ref['facility'][log_id] != 128 AND ref['facility'][log_id] != 136 "
        "AND ref['facility'][log_id] != 0 AND ref['facility'][log_id] != 168) AND ((ref['facility'][log_id] = 144 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 5) OR (ref['facility'][log_id] = 152 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 6) OR (ref['facility'][log_id] = 160 AND "
        "ref['level'][log_id] != 7)) INTO tsdb(log_log_tbl) WITH (batch_window_size = '200', timeout = 1)",

        // 10秒窗口
        "CREATE STREAM VIEW im_log_window_view_10 AS SELECT log_time, from_syslog, use_id, log_id, log_type, pid, "
        "instance_id, alm_state, alm_level, clear_type, reason_idx, begin_time, end_time, info_type, param_num, name, "
        "service_name, host_name, log_params, ROW_NUMBER() OVER (PARTITION BY window_start, window_end, log_id) FROM "
        "TABLE(TUMBLE(table im_log_tbl_stream, log_time, INTERVAL '10' SECONDS))",
        "CREATE STREAM VIEW im_log_limit_view_10 AS SELECT * FROM im_log_window_view_10 WHERE row_number < "
        "ref['windowlimit'][log_id] AND ref['windowlen'][log_id] = 10",
        "CREATE STREAM SINK sink_no_20 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_10 WHERE ref['facility'][log_id] = 152 AND ref['level'][log_id] = 6 INTO "
        "tsdb(security_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_21 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_10 WHERE ref['facility'][log_id] = 136 INTO tsdb(diag_log_tbl) WITH "
        "(batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_22 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_10 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 5 INTO "
        "tsdb(event_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_23 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_10 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 5 INTO "
        "tsdb(event_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_24 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_10 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 6 INTO "
        "tsdb(oper_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_25 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_10 WHERE (ref['facility'][log_id] != 128 AND ref['facility'][log_id] != 136 "
        "AND ref['facility'][log_id] != 0 AND ref['facility'][log_id] != 168) AND ((ref['facility'][log_id] = 144 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 5) OR (ref['facility'][log_id] = 152 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 6) OR (ref['facility'][log_id] = 160 AND "
        "ref['level'][log_id] != 7)) INTO tsdb(log_log_tbl) WITH (batch_window_size = '200', timeout = 1)",

        // 900秒窗口
        "CREATE STREAM VIEW im_log_window_view_900 AS SELECT log_time, from_syslog, use_id, log_id, log_type, pid, "
        "instance_id, alm_state, alm_level, clear_type, reason_idx, begin_time, end_time, info_type, param_num, name, "
        "service_name, host_name, log_params, ROW_NUMBER() OVER (PARTITION BY window_start, window_end, log_id) FROM "
        "TABLE(TUMBLE(table im_log_tbl_stream, log_time, INTERVAL '900' SECONDS))",
        "CREATE STREAM VIEW im_log_limit_view_900 AS SELECT * FROM im_log_window_view_900 WHERE row_number < "
        "ref['windowlimit'][log_id] AND ref['windowlen'][log_id] = 900",
        "CREATE STREAM SINK sink_no_30 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_900 WHERE ref['facility'][log_id] = 152 AND ref['level'][log_id] = 6 INTO "
        "tsdb(security_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_31 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_900 WHERE ref['facility'][log_id] = 136 INTO tsdb(diag_log_tbl) WITH "
        "(batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_32 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_900 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 5 INTO "
        "tsdb(event_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_33 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_900 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 6 INTO "
        "tsdb(oper_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_34 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_900 WHERE (ref['facility'][log_id] != 128 AND ref['facility'][log_id] != "
        "136 AND ref['facility'][log_id] != 0 AND ref['facility'][log_id] != 168) AND ((ref['facility'][log_id] = 144 "
        "AND ref['level'][log_id] != 6 AND ref['level'][log_id] != 5) OR (ref['facility'][log_id] = 152 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 6) OR (ref['facility'][log_id] = 160 AND "
        "ref['level'][log_id] != 7)) INTO tsdb(log_log_tbl) WITH (batch_window_size = '200', timeout = 1)",

        // 0秒窗口
        "CREATE STREAM VIEW im_log_limit_view_0 AS SELECT * FROM im_log_tbl_stream WHERE ref['windowlen'][log_id] = 0",
        "CREATE STREAM SINK sink_no_35 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_0 WHERE ref['facility'][log_id] = 152 AND ref['level'][log_id] = 6 INTO "
        "tsdb(security_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_36 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_0 WHERE ref['facility'][log_id] = 136 INTO tsdb(diag_log_tbl) WITH "
        "(batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_37 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_0 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 5 INTO "
        "tsdb(event_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_38 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_0 WHERE ref['facility'][log_id] = 144 AND ref['level'][log_id] = 6 INTO "
        "tsdb(oper_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_39 AS SELECT log_time, from_syslog, log_id, pid, ref['facility'][log_id], "
        "ref['level'][log_id], instance_id, seq_distinct_count(pid, log_id, log_params), host_name, service_name, "
        "log_params FROM im_log_limit_view_0 WHERE (ref['facility'][log_id] != 128 AND ref['facility'][log_id] != 136 "
        "AND ref['facility'][log_id] != 0 AND ref['facility'][log_id] != 168) AND ((ref['facility'][log_id] = 144 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 5) OR (ref['facility'][log_id] = 152 AND "
        "ref['level'][log_id] != 6 AND ref['level'][log_id] != 6) OR (ref['facility'][log_id] = 160 AND "
        "ref['level'][log_id] != 7)) INTO tsdb(log_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_40 AS SELECT log_time, from_syslog, from_syslog, pid, facility, level, "
        "from_syslog, seq_distinct_count(pid, facility, level, log_content), host_name, service_name, log_content FROM "
        "syslog_tbl_stream WHERE facility = 136 INTO tsdb(diag_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
        "CREATE STREAM SINK sink_no_41 AS SELECT log_time, from_syslog, from_syslog, pid, facility, level, "
        "from_syslog, seq_distinct_count(pid, facility, level, log_content), host_name, service_name, log_content FROM "
        "syslog_tbl_stream WHERE facility = 144 AND level = 5 INTO tsdb(event_log_tbl) WITH (batch_window_size = "
        "'200', timeout = 1)",
        "CREATE STREAM SINK sink_no_42 AS SELECT log_time, from_syslog, from_syslog, pid, facility, level, "
        "from_syslog, seq_distinct_count(pid, facility, level, log_content), host_name, service_name, log_content FROM "
        "syslog_tbl_stream WHERE facility = 144 AND level = 6 INTO tsdb(oper_log_tbl) WITH (batch_window_size = '200', "
        "timeout = 1)",
        "CREATE STREAM SINK sink_no_43 AS SELECT log_time, from_syslog, from_syslog, pid, facility, level, "
        "from_syslog, seq_distinct_count(pid, facility, level, log_content), host_name, service_name, log_content FROM "
        "syslog_tbl_stream WHERE (facility != 128 AND facility != 136 AND facility != 0 AND facility != 168) AND "
        "(facility = 144 AND level != 6 AND level != 5) AND (facility = 152 AND level != 6 AND level != 6) AND "
        "(facility = 160 AND level != 7) INTO tsdb(log_log_tbl) WITH (batch_window_size = '200', timeout = 1)",
    };

    int32_t ret = GMERR_OK;
    for (int32_t i = 0; i < RD_ELEMENT_COUNT(commands); i++) {
        ret = GmcExecDirect(stmt, commands[i], strlen(commands[i]));
        if (ret != GMERR_OK) {
            RD_ERROR("Unable to execute commands[%d] (%s)", i, commands[i]);
            return ret;
        }
    }
    return ret;
}

int32_t LsStreamReadDeviceDropNodes(GmcStmtT *stmt)
{
    // 定义 DROP SQL 语句，按创建顺序的反序排列
    const char *dropCommands[] = {
        "DROP STREAM SINK sink_no_43", "DROP STREAM SINK sink_no_42",
        "DROP STREAM SINK sink_no_41", "DROP STREAM SINK sink_no_40", "DROP STREAM SINK sink_no_39",
        "DROP STREAM SINK sink_no_38", "DROP STREAM SINK sink_no_37", "DROP STREAM SINK sink_no_36",
        "DROP STREAM SINK sink_no_35", "DROP STREAM SINK sink_no_34", "DROP STREAM SINK sink_no_33",
        "DROP STREAM SINK sink_no_32", "DROP STREAM SINK sink_no_31", "DROP STREAM SINK sink_no_30",
        "DROP STREAM SINK sink_no_25", "DROP STREAM SINK sink_no_24", "DROP STREAM SINK sink_no_23",
        "DROP STREAM SINK sink_no_22", "DROP STREAM SINK sink_no_21", "DROP STREAM SINK sink_no_20",
        "DROP STREAM SINK sink_no_19", "DROP STREAM SINK sink_no_18", "DROP STREAM SINK sink_no_17",
        "DROP STREAM SINK sink_no_16", "DROP STREAM SINK sink_no_15", "DROP STREAM SINK sink_no_14",
        "DROP STREAM SINK sink_no_13", "DROP STREAM SINK sink_no_12", "DROP STREAM SINK sink_no_11",
        "DROP STREAM SINK sink_no_10", "DROP STREAM SINK sink_no_9", "DROP STREAM SINK sink_no_8",
        "DROP STREAM SINK sink_no_7", "DROP STREAM SINK sink_no_6", "DROP STREAM SINK sink_no_5",
        "DROP STREAM SINK sink_no_4", "DROP STREAM SINK sink_no_3", "DROP STREAM SINK sink_no_2",
        "DROP STREAM SINK sink_no_1", "DROP STREAM SINK sink_no_0", "DROP STREAM SINK im_log_sink",
        "DROP STREAM SINK syslog_sink", "DROP STREAM VIEW im_log_limit_view_900",
        "DROP STREAM VIEW im_log_window_view_900", "DROP STREAM VIEW im_log_w_ref_view",
        "DROP STREAM VIEW im_log_limit_view_1", "DROP STREAM VIEW im_log_window_view_1",
        "DROP STREAM VIEW im_log_limit_view_10", "DROP STREAM VIEW im_log_window_view_10",
        "DROP STREAM VIEW im_log_limit_view_300", "DROP STREAM VIEW im_log_window_view_300",
        "DROP STREAM VIEW im_log_limit_view_3600", "DROP STREAM VIEW im_log_window_view_3600",

        "DROP STREAM VIEW im_log_limit_view_0", "DROP STREAM VIEW im_log_limit_view_60",
        "DROP STREAM VIEW im_log_window_view_60",

        "DROP STREAM TABLE syslog_tbl_stream", "DROP STREAM TABLE im_log_tbl_stream", "DROP TABLE im_alarm_schema_tbl",
        "DROP TABLE im_log_schema_tbl", "DROP TABLE starry_log_tbl", "DROP TABLE kernel_log_tbl",
        "DROP TABLE security_log_tbl", "DROP TABLE oper_log_tbl", "DROP TABLE event_log_tbl", "DROP TABLE diag_log_tbl",
        "DROP TABLE log_log_tbl", "DROP TABLE syslog_tbl", "DROP TABLE im_log_tbl"};

    // 执行 DROP SQL 语句
    int32_t ret = GMERR_OK;
    for (int32_t i = 0; i < RD_ELEMENT_COUNT(dropCommands); i++) {
        ret = GmcExecDirect(stmt, dropCommands[i], strlen(dropCommands[i]));
        if (ret != GMERR_OK) {
            printf("Unable to execute drop commands[%d] (%s)\n", i, dropCommands[i]);
            return ret;
        }
    }
    return ret;
}

// 定义日志结构体 LsStreamImLogInfoT，与 im_log_tbl_stream 表结构对应
#pragma pack(1)
typedef struct {
    int64_t logTime;
    int64_t fromSyslog;
    int64_t useId;
    int64_t logId;
    int64_t logType;
    int64_t pid;
    int64_t instanceId;
    int64_t almState;
    int64_t almLevel;
    int64_t clearType;
    int64_t reasonIdx;
    int64_t beginTime;
    int64_t endTime;
    int64_t infoType;
    int64_t paramNum;
    char name[32];
    char serviceName[32];
    char hostName[32];
    char logParams[512];
} LsStreamImLogInfoT;
#pragma pack()

// DT中的数据模型
int32_t LsStreamReadDeviceWriteV1(GmcStmtT *stmt, int64_t index, uint32_t number)
{
    // 预处理语句
    int32_t ret;
    ret = GmcPrepareStmtByLabelName(stmt, "im_log_tbl_stream", GMC_OPERATION_SQL_INSERT);
    RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to prepare stmt.");

    // 定义日志结构体实例
    LsStreamImLogInfoT v1 = {0};
    v1.logTime = index;
    v1.fromSyslog = index;
    v1.useId = index;
    v1.logId = index;
    v1.logType = index;
    v1.pid = index;
    v1.instanceId = index;
    v1.almState = index;
    v1.almLevel = index;
    v1.clearType = index;
    v1.reasonIdx = index;
    v1.beginTime = index;
    v1.endTime = index;
    v1.infoType = index;
    v1.paramNum = index;

    // 字符串字段初始化
    (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s", "GMDB\0");
    (void)snprintf_s(
        (char *)v1.serviceName, sizeof(v1.serviceName), sizeof(v1.serviceName) - 1, "%s", "GMDB_Service\0");
    (void)snprintf_s((char *)v1.hostName, sizeof(v1.hostName), sizeof(v1.hostName) - 1, "%s", "GMDB_Host\0");
    (void)snprintf_s((char *)v1.logParams, sizeof(v1.logParams), sizeof(v1.logParams) - 1, "%s", "ExampleParams\0");

    // 该表定义必须和实际建表SQL保持一致
    const char *streamTable =
        "CREATE STREAM TABLE im_log_tbl_stream (log_time INTEGER, from_syslog INTEGER, use_id INTEGER, log_id INTEGER, "
        "log_type INTEGER, pid INTEGER, instance_id INTEGER, alm_state INTEGER, alm_level INTEGER, clear_type INTEGER, "
        "reason_idx INTEGER, begin_time INTEGER, end_time INTEGER, info_type INTEGER, param_num INTEGER, name "
        "CHAR(32), service_name CHAR(32), host_name CHAR(32), log_params CHAR(512), watermark for log_time as "
        "log_time)";
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(streamTable);
    if (vertexLabel == NULL) {
        RD_ERROR("Unable to parser table schema.");
        return RD_FAILED;
    }

    // 循环写入数据
    for (int64_t value = index; value < index + number; value++) {
        v1.logTime = value;  // 使用递增值以验证索引
        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, &v1);
        RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to set vertex with buf.");
        ret = GmcExecute(stmt);
        RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to execute.");
    }
    return ret;
}

// @TestcaseName 重复向流表写入数据，SDV长稳数据模型
int32_t LsStreamRealDeviceWriteV2(GmcStmtT *stmt, LsStreamRealDeviceThreadT *ctx, uint64_t repeatCnt)
{
    int32_t ret = GMERR_OK;
    uint32_t rowStart = ctx->rowStart * repeatCnt;
    uint32_t rowEnd = rowStart + ctx->rowNum;

    // 定义日志结构体实例
    int64_t index = rowStart;
    LsStreamImLogInfoT v1 = {0};
    v1.logTime = index;
    v1.fromSyslog = index;
    v1.useId = index;
    v1.logId = index;
    v1.logType = index;
    v1.pid = index;
    v1.instanceId = index;
    v1.almState = index;
    v1.almLevel = index;
    v1.clearType = index;
    v1.reasonIdx = index;
    v1.beginTime = index;
    v1.endTime = index;
    v1.infoType = index;
    v1.paramNum = index;

    // 字符串字段初始化
    (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s", "GMDB\0");
    (void)snprintf_s(
        (char *)v1.serviceName, sizeof(v1.serviceName), sizeof(v1.serviceName) - 1, "%s", "GMDB_Service\0");
    (void)snprintf_s((char *)v1.hostName, sizeof(v1.hostName), sizeof(v1.hostName) - 1, "%s", "GMDB_Host\0");
    (void)snprintf_s((char *)v1.logParams, sizeof(v1.logParams), sizeof(v1.logParams) - 1, "%s", "ExampleParams\0");

    // 该表定义必须和实际建表SQL保持一致
    const char *streamTable =
        "CREATE STREAM TABLE im_log_tbl_stream (log_time INTEGER, from_syslog INTEGER, use_id INTEGER, log_id INTEGER, "
        "log_type INTEGER, pid INTEGER, instance_id INTEGER, alm_state INTEGER, alm_level INTEGER, clear_type INTEGER, "
        "reason_idx INTEGER, begin_time INTEGER, end_time INTEGER, info_type INTEGER, param_num INTEGER, name "
        "CHAR(32), service_name CHAR(32), host_name CHAR(32), log_params CHAR(512), watermark for log_time as "
        "log_time);";
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(streamTable);
    if (vertexLabel == NULL) {
        RD_ERROR("Unable to parser table schema.");
        return RD_FAILED;
    }

    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to prepare stmt.");
    for (uint32_t i = rowStart; i < rowEnd; i++) {
        v1.logTime = i;  // 使用递增值以验证索引
        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, &v1);
        RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to set vertex with buf.");
        ret = GmcExecute(stmt);
        RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to execute.");
    }
    RdStreamFreeTableSchema(vertexLabel);
    return ret;
}

typedef int32_t (*LsStreamRealDeviceScenarioFuncT)(GmcStmtT *stmt, LsStreamRealDeviceThreadT *ctx, uint64_t repeatCnt);

typedef struct LsStreamRealDeviceScenarioMgrT {
    LsStreamRealDeviceScenarioFuncT scenarioFunc;
    bool enable;
} LsStreamRealDeviceScenarioMgrT;

LsStreamRealDeviceScenarioMgrT g_lsStreamRealDeviceScenariosMgr[] = {
    {LsStreamRealDeviceWriteV2, true},
};

// @TestImpl Worker 根据测试场景，按需编写线程回调，根据测试场景不同，可能有多个不同的回调
void *LsStreamRealDeviceWorker(void *args)
{
    RD_STEP("Enter thread %s, system lwp is %d, pthread id is %lu.", __FUNCTION__, LsGetTid(), pthread_self());
    RdSetThreadName(LS_STREAM_REAL_DEVICE_TEST_NAME);
    LsStreamRealDeviceThreadT *ctx = (LsStreamRealDeviceThreadT *)args;

    int32_t ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = RdGmcConnect(&conn, &stmt);
    if (ret != GMERR_OK) {
        RD_ERROR("Unable to connect to db.");
        ctx->status = ret;
        return NULL;
    }
    uint32_t modelType = GMC_MODEL_STREAM;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    if (ret != GMERR_OK) {
        RD_ERROR("Unable to set stmt model type.");
        ctx->status = ret;
        return NULL;
    }

    uint64_t repeatCnt = 0;
    do {
        // 发送心跳
        LsHeartBeatSend();

        // 遍历执行测试场景
        for (uint32_t i = 0; i < RD_ELEMENT_COUNT(g_lsStreamRealDeviceScenariosMgr); i++) {
            if (g_lsStreamRealDeviceScenariosMgr[i].enable &&
                g_lsStreamRealDeviceScenariosMgr[i].scenarioFunc != NULL) {
                ret = g_lsStreamRealDeviceScenariosMgr[i].scenarioFunc(stmt, ctx, repeatCnt);
                if (ret != RD_OK) {
                    RD_ERROR("Unable to test real device scenario %d, ret is %d", i, ret);
                    ret = ctx->status;
                    return NULL;
                }
            }
        }

        // 按需打印执行次数
        if (repeatCnt++ % 100000 == 0) {
            RD_INFO("[worker-%d] repeat count is %lu.", ctx->index, repeatCnt);
        }
    } while (!LsIsStop());

    ret = RdGmcDisconnect(conn, stmt);
    if (ret != GMERR_OK) {
        RD_ERROR("Unable to connect to db.");
        ctx->status = ret;
        return NULL;
    }
    LsHeartBeatDisable();
    return NULL;
}

// @TestImpl Prepare
int32_t LsStreamRealDevicePrepare(void *userCtx, void **args)
{
    LsStreamRealDeviceCtxT *ctx = (LsStreamRealDeviceCtxT *)malloc(sizeof(LsStreamRealDeviceCtxT));
    if (ctx == NULL) {
        RD_ERROR("Unable to malloc ctx.");
        return RD_FAILED;
    }
    (void)memset(ctx, 0x0, sizeof(LsStreamRealDeviceCtxT));

    uint32_t nThread = 1;
    LsStreamRealDeviceThreadT *threads =
        (LsStreamRealDeviceThreadT *)malloc(sizeof(LsStreamRealDeviceThreadT) * nThread);
    if (threads == NULL) {
        RD_ERROR("Unable to malloc threads.");
        free(ctx);
        return RD_FAILED;
    }
    (void)memset(threads, 0x0, sizeof(LsStreamRealDeviceThreadT) * nThread);
    for (uint32_t i = 0; i < nThread; i++) {
        threads[i].index = i;
        threads[i].rowStart = 0;
        threads[i].rowNum = 2000;
        threads[i].seed = 0;
    }

    *ctx = (LsStreamRealDeviceCtxT) {
        .nThread = nThread,
        .threads = threads,
    };

    int32_t ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = RdGmcConnect(&conn, &stmt);
    RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to connect to db.");
    ret = LsStreamReadDeviceCreateNodes(stmt);
    RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to create nodes.");
    ret = RdGmcDisconnect(conn, stmt);
    RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to connect to db.");

    *args = ctx;
    return RD_OK;
}

// @TestImpl Execute
int32_t LsStreamRealDeviceExecute(void *userCtx, void *args)
{
    RD_STEP("Start %s.", LS_STREAM_REAL_DEVICE_TEST_NAME);
    LsStreamRealDeviceCtxT *ctx = (LsStreamRealDeviceCtxT *)args;
    int32_t ret;

    for (uint32_t i = 0; i < ctx->nThread; i++) {
        ret = pthread_create(&(ctx->threads[i].thread), NULL, LsStreamRealDeviceWorker, &(ctx->threads[i]));
        if (ret != RD_OK) {
            RD_ERROR("Unable to create threads[%u].", i);
            return ret;
        }
    }

    return RD_OK;
}

// @TestImpl Cleanup
int32_t LsStreamRealDeviceCleanup(void *userCtx, void *args)
{
    int32_t ret;
    LsStreamRealDeviceCtxT *ctx = (LsStreamRealDeviceCtxT *)args;
    for (uint32_t i = 0; i < ctx->nThread; i++) {
        ret = pthread_join(ctx->threads[i].thread, NULL);
        if (ret != RD_OK) {
            return ret;
        }
        RdStreamFreeTableSchema(ctx->threads[i].vertexLabel);
    }
    for (uint32_t i = 0; i < ctx->nThread; i++) {
        if (ctx->threads[i].status != RD_OK) {
            return ctx->threads[i].status;
        }
    }

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = RdGmcConnect(&conn, &stmt);
    RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to connect to db.");
    ret = LsStreamReadDeviceDropNodes(stmt);
    RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to drop nodes.");
    ret = RdGmcDisconnect(conn, stmt);
    RD_LOG_AND_RETURN_IF_ERROR(ret, "Unable to connect to db.");

    free(ctx->threads);
    free(ctx);
    RD_STEP("Finish %s.", LS_STREAM_REAL_DEVICE_TEST_NAME);
    return ret;
}

#endif /* end of LS01_STREAM_REAL_DEVICE_H */
