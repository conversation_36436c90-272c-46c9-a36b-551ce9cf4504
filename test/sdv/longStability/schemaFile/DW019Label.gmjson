[{"comment": "Normal表", "type": "record", "name": "DW019Label", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F2", "type": "char", "default": "a"}, {"name": "F3", "type": "uchar", "default": "b"}, {"name": "F4", "type": "float", "nullable": true}, {"name": "F5", "type": "string", "nullable": true}, {"name": "F6", "type": "bytes", "nullable": true}, {"name": "F7", "type": "fixed", "size": 7, "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "string", "nullable": true, "size": 10240}], "keys": [{"name": "pk", "node": "DW019Label", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "hckey", "node": "DW019Label", "fields": ["F2"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}}, {"name": "lhkey", "node": "DW019Label", "fields": ["F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]