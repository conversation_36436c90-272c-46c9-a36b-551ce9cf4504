{"system_privilege_config": [{"users": [{"user": "root", "process": "longStabilityPr"}, {"user": "root", "process": "dtlStability"}, {"user": "root", "process": "DalogStab"}, {"user": "root", "process": "DalogCliTest"}, {"user": "root", "process": "idsStabTest"}, {"user": "root", "process": "YangStability"}, {"user": "root", "process": "YangSub1"}, {"user": "root", "process": "YangSub2"}, {"user": "root", "process": "Yang<PERSON><PERSON>"}, {"user": "root", "process": "YangRel01"}, {"user": "root", "process": "YangRel02"}, {"user": "root", "process": "YangRel03"}, {"user": "root", "process": "sub"}, {"user": "root", "process": "rel_yang"}, {"user": "root", "process": "rel_yang_006_01"}, {"user": "root", "process": "rel_yang_008_02"}, {"user": "root", "process": "rel_yang_007_01"}, {"user": "root", "process": "rel_yang_010_01"}, {"user": "root", "process": "rel_yang_010_02"}, {"user": "root", "process": "rel_yang_007_02"}, {"user": "root", "process": "rel_yang_006_03"}, {"user": "root", "process": "rel_yang_006_02"}, {"user": "root", "process": "rel_yang_sp"}, {"user": "root", "process": "rel_yang_operat"}, {"user": "root", "process": "conn_scan_merge"}, {"user": "root", "process": "reliability01"}, {"user": "root", "process": "reliability02"}, {"user": "root", "process": "TsdbLSReStart"}, {"user": "root", "process": "TsdbLS"}, {"user": "root", "process": "TsdbLSInit"}, {"user": "root", "process": "TsdbLSTruncate"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "EDGE_LABEL", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}, {"obj_type": "DATALOG_UDF", "privs_type": ["INVOKE_ANY"]}, {"obj_type": "TABLESPACE", "privs_type": ["CREATE", "DROP", "BIND"]}, {"obj_type": "BINARY_FILE", "privs_type": ["OPEN", "CLOSE"]}]}, {"users": [{"user": "root", "process": "gmimport"}, {"user": "root", "process": "gmddl"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "INSERT_ANY", "ALTER", "DROP"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "DATALOG_UDF", "privs_type": ["CREATE", "DROP", "INVOKE_ANY", "ALTER"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}, {"obj_type": "BINARY_FILE", "privs_type": ["OPEN", "CLOSE"]}]}, {"users": [{"user": "abc", "process": "gmimport"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}, {"obj_type": "BINARY_FILE", "privs_type": ["OPEN", "CLOSE"]}]}, {"users": [{"user": "root", "process": "longAbnomalTest"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "ip4MiniLpmTest"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "ip4forward8kTes"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "ip4fowardLSTest"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}]}, {"users": [{"user": "root", "process": "ip4fowardAsync"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}]}, {"users": [{"user": "root", "process": "connPoolScalout"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "chaosFault"}, {"user": "root", "process": "rel_sn"}, {"user": "root", "process": "rel_ddl"}, {"user": "root", "process": "rel_dml"}, {"user": "root", "process": "reliability_all"}, {"user": "root", "process": "reliability_sce"}, {"user": "root", "process": "reliability_02"}, {"user": "root", "process": "rel_sn_022"}, {"user": "root", "process": "rel_ddl_client"}, {"user": "root", "process": "g<PERSON><PERSON>"}, {"user": "root", "process": "gmcmd"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}]}, {"users": [{"user": "root", "process": "ifStabilityTest"}, {"user": "root", "process": "dlrStability"}, {"user": "root", "process": "ifClientTest"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "USE", "DROP"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}]}, {"users": [{"user": "root", "process": "subLongWriteClt"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "gmexport"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "SELECT_ANY"]}]}, {"users": [{"user": "root", "process": "gmsysview"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "SELECT_ANY"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}]}, {"users": [{"user": "abc", "process": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}]}, {"users": [{"user": "root", "process": "fibLongStab"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "if32kStabilTest"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "toolsLongStab"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}]}, {"users": [{"user": "root", "process": "spComplexTest"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}]}, {"users": [{"user": "root", "process": "scanComplexTest"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY"]}]}, {"users": [{"user": "root", "process": "ddlStabilityTes"}, {"user": "root", "process": "randomReuseStmt"}, {"user": "root", "process": "oneThreadAsync"}, {"user": "root", "process": "upAndDowngrade"}, {"user": "root", "process": "UpDowngradeNew"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE", "ALTER"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY", "REPLACE_ANY", "SELECT_ANY", "TRUNCATE"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}]}]}