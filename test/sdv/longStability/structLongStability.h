/* ****************************************************************************
 Description  :长稳用例结构化操作线程
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2021/11/21
**************************************************************************** */
#ifndef _STRUCT_LONG_STABILITY_H_
#define _STRUCT_LONG_STABILITY_H_

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "longStability.h"
#include "ip4foward_mini_lpm.h"
#include "ip4foward_8k_test.h"
#include "specific_complex_full_fields_stability_test.h"

#define LS_TEST_ERR 1

/****************************TEST_IPFORWARD_TABLE******************************************************/
#pragma pack(1)
typedef struct testforward_struct_t {
    int8_t F1[IP4FORWARD_F1_LEN];
    uint32_t F2;
    uint32_t F3;
    uint32_t F4;
    uint8_t F5;
    int8_t F6[IP4FORWARD_BITMAP_LEN / 8];
    int8_t F7[IP4FORWARD_F7_LEN];
    uint32_t F8;
    uint32_t F9;
    uint32_t F10;
    uint32_t F11;
    uint32_t F12;
    uint32_t F13;
    uint32_t F14;
} TEST_FORWARD_TSTRUCT;  // (60) B
#pragma pack()

// 设置vertex属性
int testForwardStructSetVertex(TEST_FORWARD_TSTRUCT *vertex, int32_t valueT)
{
    uint8_t bitmap[IP4FORWARD_BITMAP_LEN / 8] = {0xff};
    memcpy(vertex->F1, (int8_t *)"fixed", IP4FORWARD_F1_LEN);
    vertex->F2 = valueT % 5000;
    vertex->F3 = valueT;
    vertex->F4 = valueT;
    vertex->F5 = valueT % 256;
    // vertex->F6_begin_pos = 0;
    // vertex->F6_end_pos = IP4FORWARD_BITMAP_LEN-1;

    memcpy(vertex->F6, bitmap, sizeof(vertex->F6));
    memcpy(vertex->F7, (int8_t *)"fixed", IP4FORWARD_F7_LEN);
    vertex->F8 = valueT;
    vertex->F9 = valueT % 5000;
    vertex->F10 = valueT;
    vertex->F11 = valueT;
    vertex->F12 = valueT;
    vertex->F13 = valueT;
    vertex->F14 = valueT;
    return GMERR_OK;
}

// 结构化写
int testForwardStructReplace(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    TEST_FORWARD_TSTRUCT preVertex = {0};
    TestLabelInfoT labelInfo = {g_ip4forwardName, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_REPLACE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        memset(&preVertex, 0, sizeof(TEST_FORWARD_TSTRUCT));
        ret = testForwardStructSetVertex(&preVertex, i);
        ret = testStructSetVertexWithBuf(stmt, &preVertex, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 【  对【0,2w】间的数据进行结构化写操作】
void write_thread_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    testForwardStructReplace(stmt, 0, countN);  // 结构化replace
    sleep(ST);
}

void testForward_set_primary_key(TEST_FORWARD_TSTRUCT *d, int intV, void *fixedV)
{
    memcpy(d->F1, (int8_t *)fixedV, IP4FORWARD_F1_LEN);
    d->F2 = (uint32_t)intV % 5000;  // uint32  4
    d->F3 = (uint32_t)intV;         // uint32  4
    d->F4 = (uint32_t)intV;         // uint32  4
}

void testForward_struct_read_property(TEST_FORWARD_TSTRUCT *obj, int i, void *fixedV)
{
    int ret = 0;
    uint8_t bitmap[IP4FORWARD_BITMAP_LEN / 8] = {0xff};
    uint32_t f2Value = i % 5000;
    uint8_t f5_value = i % 256;
    uint32_t fValue = i;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedV, obj->F1, IP4FORWARD_F1_LEN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f2Value, &obj->F2, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F3, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F4, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &f5_value, &obj->F5, sizeof(uint8_t));//有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = memcmp(bitmap, obj->F6, IP4FORWARD_BITMAP_LEN / 8);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, &fValue, &obj->F6, 1);
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f2Value, &obj->F8, sizeof(uint32_t));//有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F9, sizeof(uint32_t));//有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F10, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F11, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F12, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F13, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F14, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 结构化读
void testForwardStructRead(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    bool isFinish = false;
    TEST_FORWARD_TSTRUCT obj;
    memset(&obj, 0, sizeof(obj));
    TestLabelInfoT labelInfo = {g_ip4forwardName, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    // ret = GmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        testForward_set_primary_key(&obj, i, (int8_t *)"fixed");
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testForward_struct_read_property(&obj, i, (int8_t *)"fixed");
            cnt++;
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

// 【  随机产生一个【0,4w】的数 a 进行主键读操作   】
void read_thread_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    char *f1_value = (char *)"fixed";

    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    srand((uint32_t)time(0));
    int loop = rand() % countN;

    set_pk_index(stmt, loop, f1_value);
    ret = GmcExecute(stmt);
    if (ret == GMERR_OK) {
        ret = GmcFetch(stmt, &isFinish);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            read_ip4foward_by_get(stmt);
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    } else if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    sleep(ST);
    // 结构化读
    testForwardStructRead(stmt, loop, 1);
    sleep(ST);
}

void testForward_set_local_key(TEST_FORWARD_TSTRUCT *d, int intV, void *fixedV)
{
    memcpy(d->F7, (int8_t *)fixedV, IP4FORWARD_F7_LEN);
    d->F8 = (uint32_t)intV;  // uint32  4
}

// 随机产生一个【0,4w】数值a，对 【a，a+2000】的区间进行 local范围降序扫描，GetVertexBuf进行结构化读
void testForwardStructLocalRead(GmcStmtT *stmt, int start, int end)
{
    int ret = 0, i = 0;
    uint32_t keyId = 2;
    unsigned int l_val = start;
    unsigned int r_val = end;
    unsigned int arrLen = 2;
    TEST_FORWARD_TSTRUCT lKey = (TEST_FORWARD_TSTRUCT){0};
    TEST_FORWARD_TSTRUCT rKey = (TEST_FORWARD_TSTRUCT){0};
    TestLabelInfoT labelInfo = {g_ip4forwardName, 0, g_testNameSpace};
    TEST_FORWARD_TSTRUCT *leftKey = NULL;
    TEST_FORWARD_TSTRUCT *rightKey = NULL;
    TEST_FORWARD_TSTRUCT obj = (TEST_FORWARD_TSTRUCT){0};
    GmcStructBufferT *outBufInfo = NULL;
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};

    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置左key
    leftKey = &lKey;
    testForward_set_local_key(leftKey, l_val, (char *)"fixed");

    // 设置右key
    rightKey = &rKey;
    testForward_set_local_key(rightKey, r_val, (char *)"fixed");

    GmcRangeItemFlagT items[arrLen];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_DESC;
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_OPEN;
    items[1].order = GMC_ORDER_DESC;

    ret = testStructSetKeyRangeStructure(stmt, leftKey, rightKey, items, arrLen, keyId, NULL, &labelInfo);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret == GMERR_OK) {
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexBuf(stmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            outBufInfo = &inputBufInfo;
            TEST_FORWARD_TSTRUCT *object = (TEST_FORWARD_TSTRUCT *)outBufInfo->buf;
            i = object->F3;
            testForward_struct_read_property(&obj, i, (int8_t *)"fixed");
            cnt++;
        }
    } else {
        TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
    }
}

// 【  随机产生一个【0,4w】数值a，对 【a，a+2000】的区间进行 local范围降序扫描】
void localkey_scan_read_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    // 适配GmcSetKeyRange接口
    GmcStmtT *stmtSetKey = NULL;
    ret = GmcAllocStmt(conn, &stmtSetKey);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (stmtSetKey == NULL) {
        return;
    }
    bool isFinish = false;
    unsigned int arrLen = 2;
    uint32_t l_val_scan = 0;
    uint32_t r_val_scan = 0;
    char *f1_value = (char *)"fixed";
    GmcPropValueT *leftKeyProps_scan = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (leftKeyProps_scan == NULL) {
        return;
    }
    GmcPropValueT *rightKeyProps_scan = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (rightKeyProps_scan == NULL) {
        free(leftKeyProps_scan);
        return;
    }
    GmcRangeItemT items_sc[arrLen];
    ret = testGmcPrepareStmtByLabelName(stmtSetKey, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    srand((uint32_t)time(0));
    uint32_t loop = rand() % countN;
    l_val_scan = loop;
    r_val_scan = loop + OP_NUM;
    // printf("[INFO] l_val_scan:%u  r_val_scan:%u\n",l_val_scan,r_val_scan);
    // 设置左值
    leftKeyProps_scan[0].type = GMC_DATATYPE_FIXED;
    leftKeyProps_scan[0].value = f1_value;
    leftKeyProps_scan[0].size = IP4FORWARD_F7_LEN;
    leftKeyProps_scan[1].type = GMC_DATATYPE_UINT32;
    leftKeyProps_scan[1].value = &l_val_scan;
    leftKeyProps_scan[1].size = sizeof(uint32_t);
    // 设置右值
    rightKeyProps_scan[0].type = GMC_DATATYPE_FIXED;
    rightKeyProps_scan[0].value = f1_value;
    rightKeyProps_scan[0].size = IP4FORWARD_F7_LEN;
    rightKeyProps_scan[1].type = GMC_DATATYPE_UINT32;
    rightKeyProps_scan[1].value = &r_val_scan;
    rightKeyProps_scan[1].size = sizeof(uint32_t);

    items_sc[0].lValue = &leftKeyProps_scan[0];
    items_sc[0].rValue = &rightKeyProps_scan[0];
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items_sc[0].order = GMC_ORDER_DESC;
    items_sc[1].lValue = &leftKeyProps_scan[1];
    items_sc[1].rValue = &rightKeyProps_scan[1];
    items_sc[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[1].rFlag = GMC_COMPARE_RANGE_OPEN;
    items_sc[1].order = GMC_ORDER_DESC;

    ret = GmcSetKeyRange(stmtSetKey, items_sc, arrLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmtSetKey, g_ip4forwardLocalKey);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmtSetKey);
    if (ret == GMERR_OK) {
        ret = GmcFetch(stmtSetKey, &isFinish);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            read_ip4foward_by_get(stmtSetKey);
            ret = GmcFetch(stmtSetKey, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    } else {
        TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
    }
    if (stmtSetKey) {
        GmcFreeStmt(stmtSetKey);
    }
    sleep(ST);
    // local结构化读
    testForwardStructLocalRead(stmt, loop, loop + OP_NUM);
    sleep(ST);
// 使用长稳用例来看护该视图的数组是否与资料描述一致
#if defined ENV_RTOSV2X
    char sysView[1024] = "gmsysview -q V\\$CLT_PROCESS_TIME_CONSUMPTION";
    executeCommand(sysView);
#endif
    free(leftKeyProps_scan);
    free(rightKeyProps_scan);
}

/****************************IPFORWARD_MINI_TABLE******************************************************/
#pragma pack(1)
typedef struct miniforward_struct_t {
    uint32_t ifindex;
    uint32_t vr_id;
    uint32_t vrf_index;
    int8_t dest_ip_addr[16];
    uint8_t maskLen;
    uint16_t vlanid;
    uint32_t vrid;
    uint16_t F1 : 16;
    uint16_t F2 : 16;
    uint8_t F3 : 8;
} MINI_FORWARD_TSTRUCT;
#pragma pack()

// mini table设置vertex属性
void testMiniStructSetVertex(MINI_FORWARD_TSTRUCT *vertex, int32_t valueT)
{
    uint8_t wr_fixed6[16] = {0};
    wr_fixed6[0] = 0xcd;
    wr_fixed6[1] = 0xcd;
    wr_fixed6[2] = 0x91;
    wr_fixed6[3] = 0x0a;
    wr_fixed6[4] = 0x22;
    wr_fixed6[5] = 0x22;
    wr_fixed6[6] = 0x54;
    wr_fixed6[7] = 0x98;
    wr_fixed6[8] = 0x84;
    wr_fixed6[9] = 0x75;
    wr_fixed6[10] = 0x11;
    wr_fixed6[11] = valueT % 256;
    wr_fixed6[12] = valueT % 255;
    wr_fixed6[13] = valueT % 254;
    wr_fixed6[14] = valueT % 253;
    wr_fixed6[15] = valueT % 252;

    vertex->ifindex = valueT;
    vertex->vr_id = 0;
    vertex->vrf_index = 0;
    memcpy(vertex->dest_ip_addr, wr_fixed6, 16);
    vertex->maskLen = 128;
    vertex->vlanid = valueT;
    vertex->vrid = valueT % 5000;
    vertex->F1 = (valueT % 3000) + 3;
    vertex->F2 = (valueT % 3000) + 1;
    vertex->F3 = ((valueT % 3000) + 2) % 256;
}

int testMiniStructReplace(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    MINI_FORWARD_TSTRUCT preVertex = {0};
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_mini_lpm, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_mini_lpm, GMC_OPERATION_REPLACE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        memset(&preVertex, 0, sizeof(MINI_FORWARD_TSTRUCT));
        testMiniStructSetVertex(&preVertex, i);
        ret = testStructSetVertexWithBuf(stmt, &preVertex, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE ||
            ret == GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

void testMini_set_primary_key(MINI_FORWARD_TSTRUCT *d, int intV)
{
    d->ifindex = (uint32_t)intV;  // uint32  4
}

void testMini_struct_read_property(MINI_FORWARD_TSTRUCT *obj, int i)
{
    int ret = 0;
    // printf("testMini_struct_read_property:i=%d line=%d\n",i,__LINE__);
    uint8_t wr_fixed6[16];
    wr_fixed6[0] = 0xcd;
    wr_fixed6[1] = 0xcd;
    wr_fixed6[2] = 0x91;
    wr_fixed6[3] = 0x0a;
    wr_fixed6[4] = 0x22;
    wr_fixed6[5] = 0x22;
    wr_fixed6[6] = 0x54;
    wr_fixed6[7] = 0x98;
    wr_fixed6[8] = 0x84;
    wr_fixed6[9] = 0x75;
    wr_fixed6[10] = 0x11;
    wr_fixed6[11] = i % 256;
    wr_fixed6[12] = i % 255;
    wr_fixed6[13] = i % 254;
    wr_fixed6[14] = i % 253;
    wr_fixed6[15] = i % 252;

    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint8_t maskLen = 128;
    uint32_t ifindex = i;
    uint16_t vlanid = i;
    uint32_t vrid = i % 5000;
    uint16_t F1Value = ((i % 3000) + 3) & 0xFFFF;
    uint16_t F2Value = ((i % 3000) + 1) & 0xFFFF;
    uint8_t F3Value = (((i % 3000) + 2) % 256) & 0xFF;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &vr_id, &obj->vr_id, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &vrf_index, &obj->vrf_index, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, wr_fixed6, obj->dest_ip_addr, 16);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &obj->maskLen, sizeof(uint8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &ifindex, &obj->ifindex, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &vlanid, &obj->vlanid, sizeof(uint16_t));//有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &vrid, &obj->vrid, sizeof(uint32_t));//有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
#if 0   
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITFIELD16, &F1Value, &obj->F1, sizeof(uint16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret); 
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITFIELD16, &F2Value, &obj->F2, sizeof(uint16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);   
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITFIELD8, &F3Value, &obj->F3, sizeof(uint8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
#endif
}

// 结构化读
void testMiniStructRead(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    bool isFinish = false;
    MINI_FORWARD_TSTRUCT obj;
    memset(&obj, 0, sizeof(obj));
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_mini_lpm, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_mini_lpm, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        testMini_set_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testMini_struct_read_property(&obj, i);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

// 【随机产生一个【0,4w】的数 a 进行主键读操作】结构化+非结构化
void read_thread_ip4foward_mini_lpm(GmcConnT *conn_read, GmcStmtT *stmt_read, void *args)
{
    int ret = 0;
    uint32_t countN = 0;
    longStabilitySetDataVolume(args, &countN, IP4FOWARD_MINI_NUM * 2);
    srand((uint32_t)time(0));
    int loop = rand() % countN;
    uint32_t pk = loop;
    ret = testGmcPrepareStmtByLabelName(stmt_read, label_name_ip4foward_mini_lpm, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 结构化读
    testMiniStructRead(stmt_read, pk, 1);
    // 非结构化读
    ret = GmcSetIndexKeyValue(stmt_read, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt_read, ip4foward_mini_lpm_pk_name);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt_read);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    sleep(1);
}

// 结构化更新
void testMiniStructUpdate(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    MINI_FORWARD_TSTRUCT obj;
    memset(&obj, 0, sizeof(obj));
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_mini_lpm, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_mini_lpm, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        testMini_set_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        lpm_set_VertexProperty_SK_vlanid(stmt, (666 + i) % 20000);
        lpm_set_VertexProperty_SK_vrid(stmt, (666 + i) % 5000);
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

// 结构化删除
void testMiniStructDelete(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    MINI_FORWARD_TSTRUCT obj;
    memset(&obj, 0, sizeof(obj));
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_mini_lpm, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_mini_lpm, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        testMini_set_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

//  结构化写--结构化更新--结构化删除操作
void write_thread_ip4foward_mini_lpm(GmcConnT *conn, GmcStmtT *stmt, void *args)
{
    int ret = 0;
    uint32_t countN = 0;
    longStabilitySetDataVolume(args, &countN, IP4FOWARD_MINI_NUM * 2);
    srand((uint32_t)time(0));
    int index = rand() % countN;
    testMiniStructReplace(stmt, index, OP_NUM);
    sleep(ST);
    // 结构化更新
    testMiniStructUpdate(stmt, index, OP_NUM);
    sleep(ST);
    // 结构化删除
    testMiniStructDelete(stmt, index, OP_NUM);
    sleep(ST);
    // 结构化写
    testMiniStructReplace(stmt, 0, countN / 2);
    sleep(ST);
}

// 异步的结构化写
int testMiniStructReplaceAsync(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    MINI_FORWARD_TSTRUCT preVertex = {0};
    AsyncUserDataT data = {0};
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_mini_lpm, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_mini_lpm, GMC_OPERATION_REPLACE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        memset(&preVertex, 0, sizeof(MINI_FORWARD_TSTRUCT));
        testMiniStructSetVertex(&preVertex, i);
        ret = testStructSetVertexWithBuf(stmt, &preVertex, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        data = {0};
        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.replaceCb = replace_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &RequestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (data.status == GMERR_OK || data.status == GMERR_OUT_OF_MEMORY || data.status == GMERR_LOCK_NOT_AVAILABLE ||
            data.status == GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    return ret;
}

// 异步的结构化更新
void testMiniStructUpdateAsync(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    bool isFinish = false;
    MINI_FORWARD_TSTRUCT obj;
    memset(&obj, 0, sizeof(obj));
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_mini_lpm, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_mini_lpm, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        testMini_set_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        lpm_set_VertexProperty_SK_vlanid(stmt, (666 + i) % 20000);
        lpm_set_VertexProperty_SK_vrid(stmt, (666 + i) % 5000);
        data = {0};
        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.updateCb = update_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &RequestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (data.status == GMERR_OK || data.status == GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
}

// 异步的结构化删除
void testMiniStructDeleteAsync(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    MINI_FORWARD_TSTRUCT obj;
    memset(&obj, 0, sizeof(obj));
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_mini_lpm, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_mini_lpm, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        testMini_set_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        data = {0};
        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.deleteCb = delete_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &RequestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (data.status == GMERR_OK || data.status == GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
}
// 异步的结构化写-结构化更新-结构化删除
void recyle_write_update_remove_ip4foward_mini_lpm_async(GmcConnT *g_conn_async, GmcStmtT *g_stmt_async, void *args)
{
    int ret = 0;
    uint32_t countN = 0;
    longStabilitySetDataVolume(args, &countN, IP4FOWARD_MINI_NUM * 2);

    srand((uint32_t)time(0));
    int index = rand() % countN;
    // 异步结构化写
    testMiniStructReplaceAsync(g_stmt_async, index, OP_NUM);
    sleep(ST);
    // 异步结构化更新
    testMiniStructUpdateAsync(g_stmt_async, index, OP_NUM);
    sleep(ST);
    // 异步结构化删除
    testMiniStructDeleteAsync(g_stmt_async, index, OP_NUM);
    sleep(ST);
    // 异步结构化写
    testMiniStructReplaceAsync(g_stmt_async, index, OP_NUM);
    sleep(ST);
}

/****************************IPFORWARD_8k_TABLE******************************************************/
#pragma pack(1)
typedef struct ipforward8k_struct_t {
    uint8_t F1[IP4FORWARD_8K_F1_LEN];
    uint32_t F2;
    uint32_t F3;
    uint32_t F4;
    uint8_t F5;
    // uint32_t F6_begin_pos;
    // uint32_t F6_end_pos;
    uint8_t F6[IP4FORWARD_8K_BITMAP_LEN / 8];
    uint8_t F7[IP4FORWARD_8K_F7_LEN];
    uint32_t F8;
    uint32_t F9;
    uint32_t F10;
    uint32_t F11;
    uint32_t F12;
    uint32_t F13;
    uint32_t F14;
    uint8_t F15[IP4FORWARD_8K_F15_LEN];
    uint8_t F16[IP4FORWARD_8K_F16_LEN];
} IPFORWARD8K_TSTRUCT;
#pragma pack()

// 设置vertex属性
void iporward8kStructSetVertex(IPFORWARD8K_TSTRUCT *vertex, int32_t valueT)
{
    uint8_t f6_bits[IP4FORWARD_8K_BITMAP_LEN / 8] = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07};
    memcpy(vertex->F1, (int8_t *)"fixed", IP4FORWARD_8K_F1_LEN);
    vertex->F2 = valueT % 5000;
    vertex->F3 = valueT;
    vertex->F4 = valueT;
    vertex->F5 = valueT % 256;
    // vertex->F6_begin_pos = 0;
    // vertex->F6_end_pos = IP4FORWARD_8K_BITMAP_LEN-1;
    memcpy(vertex->F6, f6_bits, sizeof(vertex->F6));
    memcpy(vertex->F7, (int8_t *)"fixed", IP4FORWARD_8K_F7_LEN);
    vertex->F8 = valueT;
    vertex->F9 = valueT % 5000;
    vertex->F10 = valueT;
    vertex->F11 = valueT;
    vertex->F12 = valueT;
    vertex->F13 = valueT;
    vertex->F14 = valueT;
    memset(vertex->F15, 0, IP4FORWARD_8K_F15_LEN);
    memset(vertex->F16, 0, IP4FORWARD_8K_F16_LEN);
    memset(vertex->F15, 'a', IP4FORWARD_8K_F15_LEN);
    memset(vertex->F16, 'a', IP4FORWARD_8K_F16_LEN);
}

// 结构化写
int iporward8kStructReplace(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;

    IPFORWARD8K_TSTRUCT preVertex = {0};
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_8k, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_8k, GMC_OPERATION_REPLACE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        memset(&preVertex, 0, sizeof(IPFORWARD8K_TSTRUCT));
        iporward8kStructSetVertex(&preVertex, i);
        ret = testStructSetVertexWithBuf(stmt, &preVertex, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 8k结构化写线程
void write_thread_ip4foward_8k(GmcConnT *conn, GmcStmtT *stmt, void *args)
{
    int ret = 0;
    uint32_t countN = 0;
    longStabilitySetDataVolume(args, &countN, IP4FORWARD_8K_NUM * 2);
    iporward8kStructReplace(stmt, 0, countN / 2);  // 结构化replace
    sleep(ST);
}

void iporward8k_set_primary_key(IPFORWARD8K_TSTRUCT *d, int intV, void *fixedV)
{
    memcpy(d->F1, (int8_t *)fixedV, IP4FORWARD_8K_F1_LEN);
    d->F2 = (uint32_t)intV % 5000;  // uint32  4
    d->F3 = (uint32_t)intV;         // uint32  4
    d->F4 = (uint32_t)intV;         // uint32  4
}

void iporward8k_struct_read_property(IPFORWARD8K_TSTRUCT *obj, int i, void *fixedV)
{
    int ret = 0;
    uint8_t f6_bits[IP4FORWARD_8K_BITMAP_LEN / 8] = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07};
    uint32_t f2Value = i % 5000;
    uint8_t f5_value = i % 256;
    uint32_t fValue = i;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedV, obj->F1, IP4FORWARD_8K_F1_LEN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f2Value, &obj->F2, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F3, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F4, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &f5_value, &obj->F5, sizeof(uint8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = memcmp(f6_bits, obj->F6, IP4FORWARD_8K_BITMAP_LEN/8); //有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedV, obj->F7, IP4FORWARD_8K_F7_LEN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F8, sizeof(uint32_t)); //有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F9, sizeof(uint32_t));//有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F10, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F11, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F12, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F13, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &fValue, &obj->F14, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedV, obj->F15, IP4FORWARD_8K_F15_LEN);//有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    // ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedV, obj->F16, IP4FORWARD_8K_F16_LEN);//有更新
    // TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 结构化读
void iporward8kStructRead(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    bool isFinish = false;
    IPFORWARD8K_TSTRUCT obj;
    memset(&obj, 0, sizeof(IPFORWARD8K_TSTRUCT));
    TestLabelInfoT labelInfo = {(char *)label_name_ip4foward_8k, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    // ret = GmcPrepareStmtByLabelName(stmt, label_name_ip4foward_8k, GMC_OPERATION_SCAN);
    // TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        iporward8k_set_primary_key(&obj, i, (int8_t *)"fixed");
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            iporward8k_struct_read_property(&obj, i, (int8_t *)"fixed");
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

// 随机产生一个【0,5k】的数 a 进行主键读操作
void read_thread_ip4foward_8k(GmcConnT *conn, GmcStmtT *stmt, void *args)
{
    int ret = 0;
    bool isFinish = false;
    uint32_t countN = 0;
    longStabilitySetDataVolume(args, &countN, IP4FORWARD_8K_NUM * 2);
    char *F1Value = (char *)"fixed";
    srand((uint32_t)time(0));
    int index = rand() % (countN / 8);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_ip4foward_8k, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    iporward8kStructRead(stmt, index, OP_NUM / 4);  // 结构化读
    sleep(ST);
    // 非结构化读
    for (int loop = index; loop < (index + OP_NUM / 4); loop++) {
        set_pk_index_8k(stmt, loop, F1Value);
        ret = GmcExecute(stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            read_ip4foward_by_get_8k(stmt);
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    sleep(ST);
}

/****************************SPECIFIC_COMPLEX_FULL_FIELDS_STABILITY_TABLE******************************************************/

#pragma pack(1)
typedef struct sp_complex_T3_struct {
    uint32_t A1;
    uint16_t A2_len;
    uint8_t *A2;
} SP_COMPLEX_T3_STRUCT;
#pragma pack()

#pragma pack(1)
typedef struct sp_complex_T2_struct {
    uint16_t A1;
    uint64_t A2;
    float A3;
    int8_t A4[SP_COMPLEX_BITMAP_LEN / 8];
    uint16_t A5_len;
    uint8_t *A5;
    uint16_t A6_len;
    uint8_t *A6;
    uint16_t T3_count;
    SP_COMPLEX_T3_STRUCT *T3;
} SP_COMPLEX_T2_STRUCT;
#pragma pack()

#pragma pack(1)
typedef struct sp_complex_T1_struct {
    uint64_t R1;
    int64_t R2;
    int8_t R3[SP_COMPLEX_FIXED_LEN];
    uint16_t T2_count;
    SP_COMPLEX_T2_STRUCT *T2;
} SP_COMPLEX_T1_STRUCT;
#pragma pack()

#pragma pack(1)
typedef struct sp_complex_T1_V_struct {
    uint32_t V1;
    uint32_t V2;
    int8_t V3[SP_COMPLEX_BITMAP_LEN / 8];
    uint16_t V4_len;
    uint8_t *V4;
} SP_COMPLEX_T1_V_STRUCT;
#pragma pack()

#pragma pack(1)
typedef struct sp_complex_root_struct {
    uint64_t F1;
    int8_t F2[SP_COMPLEX_FIXED_LEN];
    uint8_t F3;  // 分区字段
    int32_t F4;
    uint32_t F5;  // 自增列
    int8_t F6;
    uint32_t F7;
    uint32_t F8;
    uint32_t F9;
    uint8_t F10;
    bool F11;
    float F12;
    double F13;
    uint64_t F14;
    uint8_t F15[SP_COMPLEX_BITMAP_LEN / 8];
    uint32_t F16;
    uint32_t F17;
    uint16_t T1_flag;
    SP_COMPLEX_T1_STRUCT *T1;
    uint16_t T1_V_count;
    SP_COMPLEX_T1_V_STRUCT *T1_V;
} SP_COMPLEX_ROOT_STRUCT;
#pragma pack()

void spComplexMalloc(SP_COMPLEX_ROOT_STRUCT *rootN, SP_COMPLEX_T1_STRUCT *t1, SP_COMPLEX_T2_STRUCT *t2,
    SP_COMPLEX_T3_STRUCT *t3, SP_COMPLEX_T1_V_STRUCT *t1_v, uint16_t T2_count, uint16_t T3_count, uint16_t T1_V_count)
{
    int string_len = g_string_len, bytes_len = g_bytes_len;

    // array T2 的变长字段 分配内存
    for (int i = 0; i < T2_count; ++i) {
        t2[i].A5_len = string_len;
        t2[i].A5 = (uint8_t *)malloc(string_len + 1);
        EXPECT_NE((void *)NULL, t2[i].A5);
        t2[i].A6_len = bytes_len;
        t2[i].A6 = (uint8_t *)malloc(bytes_len + 1);
        EXPECT_NE((void *)NULL, t2[i].A6);
        t2[i].T3 = &t3[i * T3_count];
        // array T3 的变长字段 分配内存
        for (int j = 0; j < T3_count; j++) {
            t3[i * T3_count + j].A2_len = string_len;
            t3[i * T3_count + j].A2 = (uint8_t *)malloc(bytes_len + 1);
            EXPECT_NE((void *)NULL, t3[i * T3_count + j].A2);
        }
    }

    // vector 的变长字段 分配内存
    for (int i = 0; i < T1_V_count; ++i) {
        t1_v[i].V4_len = string_len;
        t1_v[i].V4 = (uint8_t *)malloc(string_len + 1);
        EXPECT_NE((void *)NULL, t1_v[i].V4);
    }
    t1->T2 = &t2[0];
    rootN->T1 = t1;
    rootN->T1_V = t1_v;
}

void spComplexFree(SP_COMPLEX_ROOT_STRUCT *d, uint16_t T2_count, uint16_t T3_count, uint16_t T1_V_count)
{
    for (int i = 0; i < T2_count; i++) {
        for (int j = 0; j < T3_count; j++) {
            if (d->T1->T2[i].T3[j].A2) {
                free(d->T1->T2[i].T3[j].A2);
            }
        }
        if (d->T1->T2[i].A5) {
            free(d->T1->T2[i].A5);
        }
        if (d->T1->T2[i].A6) {
            free(d->T1->T2[i].A6);
        }
    }

    for (int i = 0; i < T1_V_count; ++i) {
        if (d->T1_V[i].V4) {
            free(d->T1_V[i].V4);
        }
    }
}

void spComplexSetVertex(
    SP_COMPLEX_ROOT_STRUCT *rootN, int32_t valueT, uint16_t T2_count, uint16_t T3_count, uint16_t T1_V_count)
{
    uint8_t bitmapV = 0xaa;
    char stringV[g_string_len] = "strst";
    char fixedV[SP_COMPLEX_FIXED_LEN] = "hello";
    char bytesV[g_string_len] = "bytes";

    rootN->F1 = (uint64_t)valueT + 0xFFFFFFFF;                  // uint64
    memcpy(rootN->F2, (int8_t *)fixedV, SP_COMPLEX_FIXED_LEN);  // fixed
    rootN->F3 = (valueT % 16) & 0xFF;                           // 分区字段                               // partition
    rootN->F4 = valueT;                                         // int32
    // vertex->F5 = valueT;                                                    //自增列
    rootN->F6 = (valueT % 8) & 0xFF;  // uint8
    rootN->F7 = valueT % 16;
    rootN->F8 = valueT % 1024;
    rootN->F9 = valueT;
    rootN->F10 = (valueT % 32) & 0xFF;  // uint8
    rootN->F11 = (bool)valueT % 2;
    rootN->F12 = (float)valueT * 1.1;
    rootN->F13 = (double)valueT * 1.1;
    rootN->F14 = (uint64_t)20211228 + 0xFFFFFFFF;             // time
    memcpy(rootN->F15, &bitmapV, SP_COMPLEX_BITMAP_LEN / 8);  // bitmap
    rootN->F16 = valueT;
    rootN->F17 = valueT;
    // T1节点
    rootN->T1_flag = 1;
    rootN->T1->R1 = (uint64_t)valueT + 0xFFFFFFFF;
    rootN->T1->R2 = (int64_t)valueT;
    memcpy(rootN->T1->R3, (int8_t *)fixedV, SP_COMPLEX_FIXED_LEN);

    // T2 节点写值
    rootN->T1->T2_count = T2_count;
    for (int i = 0; i < T2_count; i++) {
        rootN->T1->T2[i].A1 = i & 0xFFFF;
        rootN->T1->T2[i].A2 = (uint64_t)20211228 + 0xFFFFFFFF;
        rootN->T1->T2[i].A3 = (float)i * 1.1;
        memcpy(rootN->T1->T2[i].A4, &bitmapV, SP_COMPLEX_BITMAP_LEN / 8);
        rootN->T1->T2[i].A5_len = g_string_len;
        memcpy(rootN->T1->T2[i].A5, (uint8_t *)stringV, g_string_len);
        rootN->T1->T2[i].A6_len = g_bytes_len;
        memcpy(rootN->T1->T2[i].A6, (uint8_t *)bytesV, g_bytes_len);

        // T3节点写值
        rootN->T1->T2[i].T3_count = T3_count;
        for (int k = 0; k < T3_count; k++) {
            rootN->T1->T2[i].T3[k].A1 = i;
            rootN->T1->T2[i].T3[k].A2_len = g_bytes_len;
            memcpy(rootN->T1->T2[i].T3[k].A2, (uint8_t *)bytesV, g_bytes_len);
        }
    }
    // vecotor节点写值
    rootN->T1_V_count = T1_V_count;
    for (int i = 0; i < T1_V_count; i++) {
        rootN->T1_V[i].V1 = i;
        rootN->T1_V[i].V2 = i;
        memcpy(rootN->T1_V[i].V3, &bitmapV, SP_COMPLEX_BITMAP_LEN / 8);
        rootN->T1_V[i].V4_len = g_string_len;
        memcpy(rootN->T1_V[i].V4, (uint8_t *)stringV, g_string_len);
    }
}

// sp_complex表结构化写
int spComplexStructReplace(GmcStmtT *stmt, int32_t startVal, uint32_t count, SP_COMPLEX_ROOT_STRUCT preVertex)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    TestLabelInfoT labelInfo = {g_specificComplexLabelName, 0, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_REPLACE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        spComplexSetVertex(&preVertex, i, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_VECTOR_NUM);
        ret = testStructSetVertexWithBuf(stmt, &preVertex, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

void test_set_sp_complex_primary_key(SP_COMPLEX_ROOT_STRUCT *d, int v)
{
    d->F1 = (uint64_t)v + 0xFFFFFFFF;  // uint64
    memcpy(d->F2, (int8_t *)"hello", SP_COMPLEX_FIXED_LEN);
}

// 结构化删除
int spComplexStructDelete(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    uint32_t keyId = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    SP_COMPLEX_ROOT_STRUCT preVertex = (SP_COMPLEX_ROOT_STRUCT){0};
    TestLabelInfoT labelInfo = {g_specificComplexLabelName, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        test_set_sp_complex_primary_key(&preVertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &preVertex, keyId, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 预置数据
void prepare_write_sp_complex_full_fields(int32_t pkStart, int32_t countN)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    SP_COMPLEX_ROOT_STRUCT preVertex = (SP_COMPLEX_ROOT_STRUCT){0};
    SP_COMPLEX_T1_STRUCT t1N = (SP_COMPLEX_T1_STRUCT){0};
    SP_COMPLEX_T2_STRUCT t2N[SP_COMPLEX_ARRAY_NUM] = {0};
    SP_COMPLEX_T3_STRUCT t3N[SP_COMPLEX_ARRAY_NUM * SP_COMPLEX_ARRAY_NUM] = {0};
    SP_COMPLEX_T1_V_STRUCT t1_vN[SP_COMPLEX_VECTOR_NUM] = {0};

    spComplexMalloc(
        &preVertex, &t1N, t2N, t3N, t1_vN, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_VECTOR_NUM);
    // write
    spComplexStructReplace(stmt, pkStart, countN, preVertex);
    spComplexFree(&preVertex, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_VECTOR_NUM);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 随机产生一个【0,100】的数a，对【a,a+100】的区间进行写，主键更新和memberkey节点全字段更新--主键删除操作
void recycle_write_remove_sp_complex(void *args, GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
    int32_t countN = 100;
    uint32_t countNum = 0;
    longStabilitySetDataVolume(args, &countNum, SPCOMPLEX_NUM * 2);
    SP_COMPLEX_ROOT_STRUCT preVertex = (SP_COMPLEX_ROOT_STRUCT){0};
    SP_COMPLEX_T1_STRUCT t1N = (SP_COMPLEX_T1_STRUCT){0};
    SP_COMPLEX_T2_STRUCT t2N[SP_COMPLEX_ARRAY_NUM] = {0};
    SP_COMPLEX_T3_STRUCT t3N[SP_COMPLEX_ARRAY_NUM * SP_COMPLEX_ARRAY_NUM] = {0};
    SP_COMPLEX_T1_V_STRUCT t1_vN[SP_COMPLEX_VECTOR_NUM] = {0};
    spComplexMalloc(
        &preVertex, &t1N, t2N, t3N, t1_vN, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_VECTOR_NUM);
    srand((uint32_t)time(0));
    int start = rand() % countNum;

    // write
    spComplexStructReplace(stmt, start, countN, preVertex);
    sleep(2);

    // update
    spComplexPkUpdate(stmt, start, countN);
    sleep(ST);

    // remove
    spComplexStructDelete(stmt, start, countN);
    sleep(ST);
    spComplexFree(&preVertex, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_VECTOR_NUM);
}

void sp_complex_struct_read_property(SP_COMPLEX_ROOT_STRUCT *obj, int i, void *fixedV)
{
    int ret = 0;
    uint64_t F1V = (uint64_t)i + 0xFFFFFFFF;
    uint8_t F3V = (i % 16) & 0xFF;  // 分区字段                               //partition
    uint32_t F7 = i % 16;
    uint32_t F8 = i % 1024;
    uint32_t F9 = i;
    uint8_t F10 = (i % 32) & 0xFF;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1V, &obj->F1, sizeof(uint64_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedV, obj->F2, SP_COMPLEX_FIXED_LEN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F7, &obj->F7, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F8, &obj->F8, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F9, &obj->F9, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F10, &obj->F10, sizeof(uint8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void sp_complex_struct_read_property_F2(SP_COMPLEX_ROOT_STRUCT *obj, int i, void *fixedV)
{
    int ret = 0;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedV, obj->F2, SP_COMPLEX_FIXED_LEN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 特殊复杂表结构化读
int spComplexStructPkScan(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    uint32_t keyId = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    bool isFinish = false;
    SP_COMPLEX_ROOT_STRUCT preVertex = (SP_COMPLEX_ROOT_STRUCT){0};
    TestLabelInfoT labelInfo = {g_specificComplexLabelName, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &preVertex, &deseri, &deseriCtx, false, &labelInfo);

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_SCAN);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        test_set_sp_complex_primary_key(&preVertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &preVertex, keyId, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
            continue;
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcFetch(stmt, &isFinish);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (!isFinish) {
            ret = testStructGetVertexDeseri(stmt, &deseri);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            sp_complex_struct_read_property(&preVertex, i, (int8_t *)"hello");
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

// 特殊复杂表结构化读
void scan_by_primary_key_sp_complex_full(void *args, GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
    uint32_t countN = 0;
    longStabilitySetDataVolume(args, &countN, SPCOMPLEX_NUM * 2);

    // scan by primary key
    int index = rand() % countN;
    spComplexStructPkScan(stmt, index, OP_NUM / 10);
    sleep(ST);
}

// 字段F4为partial local索引
void spComplexSetVertexPartialLocalF4(
    SP_COMPLEX_ROOT_STRUCT *rootN, int32_t valueT, uint16_t T2_count, uint16_t T3_count, uint16_t T1_V_count)
{
    uint8_t bitmapV = 0xaa;
    char stringV[g_string_len] = "strst";
    char fixedV[SP_COMPLEX_FIXED_LEN] = "hello";
    char bytesV[g_string_len] = "bytes";

    rootN->F1 = (uint64_t)valueT + 0xFFFFFFFF;                  // uint64
    memcpy(rootN->F2, (int8_t *)fixedV, SP_COMPLEX_FIXED_LEN);  // fixed
    rootN->F3 = (valueT % 16) & 0xFF;  // 分区字段                               //partition
    // F4 为partial local索引字段
    if (valueT % 4 == 0) {
        rootN->F4 = 200;
    } else if (valueT % 4 == 1) {
        rootN->F4 = 300;
    } else {
        rootN->F4 = valueT;  // int32
    }
    // vertex->F5 = valueT;                                                    //自增列
    rootN->F6 = (valueT % 8) & 0xFF;  // uint8
    rootN->F7 = valueT % 16;
    rootN->F8 = valueT % 1024;
    rootN->F9 = valueT;
    rootN->F10 = (valueT % 32) & 0xFF;  // uint8
    rootN->F11 = (bool)valueT % 2;
    rootN->F12 = (float)valueT * 1.1;
    rootN->F13 = (double)valueT * 1.1;
    rootN->F14 = (uint64_t)20211228 + 0xFFFFFFFF;             // time
    memcpy(rootN->F15, &bitmapV, SP_COMPLEX_BITMAP_LEN / 8);  // bitmap
    rootN->F16 = valueT;
    rootN->F17 = valueT;
    // T1节点
    rootN->T1_flag = 1;
    rootN->T1->R1 = (uint64_t)valueT + 0xFFFFFFFF;
    rootN->T1->R2 = (int64_t)valueT;
    memcpy(rootN->T1->R3, (int8_t *)fixedV, SP_COMPLEX_FIXED_LEN);

    // T2 节点写值
    rootN->T1->T2_count = T2_count;
    for (int i = 0; i < T2_count; i++) {
        rootN->T1->T2[i].A1 = i & 0xFFFF;
        rootN->T1->T2[i].A2 = (uint64_t)20211228 + 0xFFFFFFFF;
        rootN->T1->T2[i].A3 = (float)i * 1.1;
        memcpy(rootN->T1->T2[i].A4, &bitmapV, SP_COMPLEX_BITMAP_LEN / 8);
        rootN->T1->T2[i].A5_len = g_string_len;
        memcpy(rootN->T1->T2[i].A5, (uint8_t *)stringV, g_string_len);
        rootN->T1->T2[i].A6_len = g_bytes_len;
        memcpy(rootN->T1->T2[i].A6, (uint8_t *)bytesV, g_bytes_len);

        // T3节点写值
        rootN->T1->T2[i].T3_count = T3_count;
        for (int k = 0; k < T3_count; k++) {
            rootN->T1->T2[i].T3[k].A1 = i;
            rootN->T1->T2[i].T3[k].A2_len = g_bytes_len;
            memcpy(rootN->T1->T2[i].T3[k].A2, (uint8_t *)bytesV, g_bytes_len);
        }
    }
    // vecotor节点写值
    rootN->T1_V_count = T1_V_count;
    for (int i = 0; i < T1_V_count; i++) {
        rootN->T1_V[i].V1 = i;
        rootN->T1_V[i].V2 = i;
        memcpy(rootN->T1_V[i].V3, &bitmapV, SP_COMPLEX_BITMAP_LEN / 8);
        rootN->T1_V[i].V4_len = g_string_len;
        memcpy(rootN->T1_V[i].V4, (uint8_t *)stringV, g_string_len);
    }
}

// sp_complex表结构化写, 其中字段F4为partial local索引
int partial_local_write_sp_complex_full(
    GmcStmtT *stmt, int32_t startVal, uint32_t count, SP_COMPLEX_ROOT_STRUCT preVertex)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    TestLabelInfoT labelInfo = {g_specificComplexLabelName, 0, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_REPLACE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        spComplexSetVertexPartialLocalF4(
            &preVertex, i, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_VECTOR_NUM);
        ret = testStructSetVertexWithBuf(stmt, &preVertex, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

void test_set_sp_complex_local_partical_key_01(SP_COMPLEX_ROOT_STRUCT *d, int v)
{
    if (v % 4 == 0) {
        d->F4 = 200;  // uint32
    } else if (v % 4 == 1) {
        d->F4 = 300;  // uint32
    } else {
        d->F4 = (uint32_t)v;  // uint32
    }
}

void test_set_sp_complex_local_partical_key_02(SP_COMPLEX_ROOT_STRUCT *d, int v)
{
    d->F4 = (uint32_t)v;  // uint32
}

// 特殊复杂表, local partial索引读
int partial_local_scan_sp_complex_full_01(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    uint32_t keyId = 1;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    bool isFinish = false;
    SP_COMPLEX_ROOT_STRUCT preVertex = (SP_COMPLEX_ROOT_STRUCT){0};
    TestLabelInfoT labelInfo = {g_specificComplexLabelName, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &preVertex, &deseri, &deseriCtx, false, &labelInfo);

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_SCAN);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        test_set_sp_complex_local_partical_key_01(&preVertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &preVertex, keyId, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
            continue;
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcFetch(stmt, &isFinish);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            ret = testStructGetVertexDeseri(stmt, &deseri);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            sp_complex_struct_read_property_F2(&preVertex, i, (int8_t *)"hello");
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

// 特殊复杂表, local partial索引读
int partial_local_scan_sp_complex_full_02(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    uint32_t keyId = 1;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    bool isFinish = false;
    SP_COMPLEX_ROOT_STRUCT preVertex = (SP_COMPLEX_ROOT_STRUCT){0};
    TestLabelInfoT labelInfo = {g_specificComplexLabelName, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &preVertex, &deseri, &deseriCtx, false, &labelInfo);

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_SCAN);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        test_set_sp_complex_local_partical_key_02(&preVertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &preVertex, keyId, NULL, &labelInfo);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
            continue;
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcFetch(stmt, &isFinish);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            ret = testStructGetVertexDeseri(stmt, &deseri);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            sp_complex_struct_read_property_F2(&preVertex, i, (int8_t *)"hello");
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

void spComplexSetUpdateRootFiledsF4(GmcNodeT *node, int32_t value)
{
    int32_t ret = 0;
    if (value % 4 == 0 || value % 4 == 1) {
        ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

// 特殊复杂表, 主键更新local partial索引字段
int pk_update_local_fields_sp_complex_full(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL, *T1_V = NULL;

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_specificComplexPkName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        spComplexSetPrimaryIndex(stmt, i);
        spComplexGetNode(stmt, &root, &T1, &T2, &T1_V);
        // 更新根节点
        spComplexSetUpdateRootFiledsF4(root, i);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 特殊复杂表, local partial索引更新
int partial_local_update_sp_complex_full(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL, *T1_V = NULL;

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_specificComplexLocalKeyName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        spComplexSetLocalIndex(stmt, i);
        spComplexGetNode(stmt, &root, &T1, &T2, &T1_V);
        // 更新根节点, 不包含local partial索引字段F4
        spComplexSetUpdateRootFiledsNoF4(root, i);
        // 更新T1节点
        spComplexSetUpdateT1Fileds(T1, i);
        // 更新T2/T3节点
        GmcIndexKeyT *T2key = NULL, *T3key = NULL;
        GmcNodeT *T2_Value = NULL, *T3 = NULL, *T3_Value = NULL;
        ret = GmcNodeAllocKey(T2, "mem_key2", &T2key);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        for (int j = 0; j < SP_COMPLEX_ARRAY_NUM; j++) {
            spComplexMemberKeySetKeyValue_T2(T2key, j);
            ret = GmcNodeGetElementByKey(T2, T2key, &T2_Value);
            spComplexSetUpdateT2Fileds(T2_Value, j);
            ret = GmcNodeGetChild(T2_Value, "T3", &T3);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcNodeAllocKey(T3, "mem_key3", &T3key);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (int k = 0; k < SP_COMPLEX_ARRAY_NUM; k++) {
                spComplexMemberKeySetKeyValue_T3(T3key, j);
                ret = GmcNodeGetElementByKey(T3, T3key, &T3_Value);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                spComplexSetUpdateT3Fileds(T3_Value);
            }
            ret = GmcNodeFreeKey(T3key);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        sleep(2);
        // 更新T1_V节点
        GmcIndexKeyT *T1_Vkey = NULL;
        GmcNodeT *T1_V_Value = NULL;
        ret = GmcNodeAllocKey(T1_V, "mem_keyv", &T1_Vkey);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        for (int j = 0; j < SP_COMPLEX_VECTOR_NUM; j++) {
            spComplexMemberKeySetKeyValue_T1_V(T1_Vkey, j);
            ret = GmcNodeGetElementByKey(T1_V, T1_Vkey, &T1_V_Value);
            spComplexSetUpdateT1_VFileds(T1_V_Value, j);
        }
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcNodeFreeKey(T2key);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T1_Vkey);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

// 特殊复杂表, local partial索引删除
int partial_local_delete_sp_complex_full(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    uint32_t keyId = 1;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    SP_COMPLEX_ROOT_STRUCT preVertex = (SP_COMPLEX_ROOT_STRUCT){0};

    ret = testGmcPrepareStmtByLabelName(stmt, g_specificComplexLabelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcSetIndexKeyName(stmt, g_specificComplexLocalKeyName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        spComplexSetLocalIndex(stmt, i);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 写-- local(partial) 索引扫描-更新-删除
void partial_local_operation_sp_complex_full(void *args, GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
    uint32_t countN = 0;
    longStabilitySetDataVolume(args, &countN, SPCOMPLEX_NUM * 2);
    SP_COMPLEX_ROOT_STRUCT preVertex = (SP_COMPLEX_ROOT_STRUCT){0};
    SP_COMPLEX_T1_STRUCT t1N = (SP_COMPLEX_T1_STRUCT){0};
    SP_COMPLEX_T2_STRUCT t2N[SP_COMPLEX_ARRAY_NUM] = {0};
    SP_COMPLEX_T3_STRUCT t3N[SP_COMPLEX_ARRAY_NUM * SP_COMPLEX_ARRAY_NUM] = {0};
    SP_COMPLEX_T1_V_STRUCT t1_vN[SP_COMPLEX_VECTOR_NUM] = {0};

    spComplexMalloc(
        &preVertex, &t1N, t2N, t3N, t1_vN, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_VECTOR_NUM);
    srand((uint32_t)time(0));
    int start = rand() % countN;
    // write
    partial_local_write_sp_complex_full(stmt, start, countN, preVertex);
    sleep(ST);
    // scan
    partial_local_scan_sp_complex_full_01(stmt, start, countN);
    sleep(ST);
    // 主键更新local partial索引字段
    pk_update_local_fields_sp_complex_full(stmt, start, countN);
    sleep(ST);
    // scan
    partial_local_scan_sp_complex_full_02(stmt, start, countN);
    sleep(ST);
    // update
    partial_local_update_sp_complex_full(stmt, start, countN);
    sleep(ST);
    // delete
    partial_local_delete_sp_complex_full(stmt, start, countN);
    sleep(ST);
    spComplexFree(&preVertex, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_ARRAY_NUM, SP_COMPLEX_VECTOR_NUM);
}

#endif  //_STRUCT_LONG_STABILITY_H_
