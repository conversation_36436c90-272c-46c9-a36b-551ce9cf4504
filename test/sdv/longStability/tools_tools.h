#include "longStability.h"
#include "ifStabilityTest.h"
#include "subLongWriteClientTest.h"
#include "stability_tools.h"
#include "ip4foward_8k_test.h"
#include "ip4foward_mini_lpm.h"
#include "if_32k_stability_test.h"
#define MAX_CMD_SIZE 1024
#define STIME 60
#define VIEWNUM 78
// 存放日志白名单错误码
char g_errorCode01[MAX_CMD_SIZE] = {0};
char g_errorCode02[MAX_CMD_SIZE] = {0};
// 存放错误信息
char g_errorMsg01[MAX_CMD_SIZE] = {0};
char g_errorMsg02[MAX_CMD_SIZE] = {0};
char g_errorMsg03[MAX_CMD_SIZE] = {0};

/*--------------------------工具查询结果匹配 或--------------------------------------------*/
int executeCommandOr(char *cmd, const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL,
    const char *v4 = NULL, const char *v5 = NULL)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return -1;
    }

    std::string cmdOutput;
    for (char buffer[4096]; NULL != fgets(buffer, sizeof(buffer), pf); cmdOutput += buffer)
        ;

    pclose(pf);

    int ret = -1;

    for (const char *v : {v1, v2, v3, v4, v5}) {
        if (v != NULL && cmdOutput.find(v) != std::string::npos) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        printf("cmd: [errno:%d] %s\n", errno, cmd);
        printf("expect:\n");
        for (const char *v : {v1, v2, v3, v4, v5}) {
            if (v != NULL)
                printf("  [match fail] %s\n", v);
        }
        printf("output: [len:%d] %s\n", cmdOutput.size(), cmdOutput.c_str());
    }

    return ret;
}

/*--------------------------工具查询--------------------------------------------*/

// 2021/11/18 wuxch 添加接口，视图查询改用testLongStabilitySysview，防止刷屏打印
void testLongStabilitySysview(char *cmd, char *expect1 = NULL, char *expect2 = NULL, char *expect3 = NULL,
    char *expect4 = NULL, const char *expect5 = NULL, const char *tag = " ")
{
#if TEST_LONG_STABILITY_DEBUG
    printf("%s\n", cmd);
    system(cmd);
#else
    int ret = executeCommand(cmd, expect1, expect2, expect3, expect4, expect5);
    if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
#endif
}

char* myStrStr(const char *str, const char *substr)
{
    const char *mystr = str;
    const char *mysub = substr;

    while (*mystr != '\0') {
        if (*mystr != *mysub) {
            ++mystr;
            continue;
        }
        const char *temp_mystr = mystr;
        const char *temp_mysub = mysub;

        while (*temp_mysub != '\0') {
            if (*temp_mystr != *temp_mysub) {
                ++mystr;
                break;
            }
            ++temp_mysub;
            ++temp_mystr;
        }
        if (*temp_mysub == '\0') {
            return (char*) mystr;
        }
    }
    return NULL;
}

// gmimport：导入系统中没有的一张表再删掉
void *GmimportVertexlabel(void *arg)
{
    int ret = 0;
    srand((uint32_t)time(0));
    int loop = rand() % 20;
    char g_command1[1024] = {0};
    char messCompare2[1024] = {0};
    ret = snprintf(messCompare2, 1024, "ret = %d ", GMERR_LOCK_NOT_AVAILABLE);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
    }
    while (1) {
        THREAD_CONTROL_POINT();
        char const *tools_test_label_name = "tools_test";
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        ret = testGmcConnect(&conn, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, tools_test_label_name);
        testGmcDisconnect(conn, stmt);
        //使用工具再导表一张
        const char filePath[256] = "./schemaFile/tools_test.gmjson";
        snprintf(
            g_command1, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -ns %s", g_toolPath, filePath, g_testNameSpace);
        ret = executeCommandOr(g_command1, "successfully", messCompare2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(g_command1, 0, sizeof(g_command1));
        if (ret == GMERR_OK) {
            GmcConnT *conn_t = NULL;
            GmcStmtT *stmt_t = NULL;
            ret = testGmcConnect(&conn_t, &stmt_t);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcDropVertexLabel(stmt_t, tools_test_label_name);
            // TEST_EXPECT_INT32(GMERR_OK, ret);
            testGmcDisconnect(conn_t, stmt_t);
        }
        sleep(STIME);
    }
}

// 全量视图共计79张
char const *g_viewNames[] = {"V\\$CATA_EDGE_LABEL_INFO", "V\\$CATA_GENERAL_INFO", "V\\$CATA_KV_TABLE_INFO",
    "V\\$CATA_LABEL_SUBS_INFO", "V\\$CATA_MSG_NOTIFY_TABLE_INFO", "V\\$CATA_NAMESPACE_INFO", "V\\$CATA_RESOURCE_INFO",
    "V\\$CATA_TABLESPACE_INFO", "V\\$CATA_TBM_TABLE_INFO", "V\\$CATA_UDF_INFO", "V\\$CATA_VERTEX_LABEL_INFO",
    "V\\$CATA_VERTEX_LABEL_CHECK_INFO", "V\\$CLT_PROCESS_CONN", "V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL",
    "V\\$CLT_PROCESS_INFO", "V\\$CLT_PROCESS_LABEL", "V\\$CLT_PROCESS_TIME_CONSUMPTION", "V\\$COM_DTL_UDF_DYN_CTX",
    "V\\$COM_DYN_CTX", "V\\$COM_MEM_SUMMARY", "V\\$COM_SHMEM_CTX", "V\\$COM_TABLE_MEM_SUMMARY", "V\\$CONFIG_PARAMETERS",
    "V\\$DATALOG_PLAN_EXPLAIN_INFO", "V\\$DB_SERVER", "V\\$DRT_COM_STAT", "V\\$DRT_CONN_STAT",
    "V\\$DRT_CONN_SUBS_STAT", "V\\$DRT_DATA_PLANE_CHANNEL_STAT", "V\\$DRT_LONG_OPERATION_STAT", "V\\$DRT_PIPE_STAT",
    "V\\$DRT_SCHEDULE_STAT", "V\\$DRT_WORKER_POOL_STAT", "V\\$DRT_WORKER_STAT",
    "V\\$PRIVILEGE_ROLE_STAT", "V\\$PRIVILEGE_USER_STAT", "V\\$PTL_DATALOG_SO_INFO", "V\\$QRY_AGE_TASK",
    "V\\$QRY_DML_INFO", "V\\$QRY_DML_OPER_STATIS", "V\\$QRY_DYNMEM", "V\\$QRY_SESSION", "V\\$QRY_SCHEMA_DEGRADE_INFO",
    "V\\$QRY_SHOW_SCHEMA_DEGRADE", "V\\$QRY_STMG_SUBS_CURSOR_INFO", "V\\$QRY_TRX_MONITOR_STAT",
    "V\\$STORAGE_ART_INDEX_STAT", "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "V\\$STORAGE_EDGE_LABEL_STAT",
    "V\\$STORAGE_FSM_STAT", "V\\$STORAGE_HASH_CLUSTER_INDEX_STAT", "V\\$STORAGE_HASH_COLLISION_STAT",
    "V\\$STORAGE_HASH_INDEX_STAT", "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT", "V\\$STORAGE_HEAP_STAT",
    "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT", "V\\$STORAGE_INDEX_GLOBAL_STAT", "V\\$STORAGE_KV_COUNT",
    "V\\$STORAGE_KV_STAT", "V\\$STORAGE_LOCK_CONFLICT_INFO_STAT",
    "V\\$STORAGE_LOCK_OVERVIEW", "V\\$STORAGE_MEMDATA_STAT", "V\\$STORAGE_RES_SESSION_STAT",
    "V\\$STORAGE_RESOURCE_ALL_POOL_STAT", "V\\$STORAGE_RESOURCE_SINGLE_POOL_BITMAP_STAT",
    "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT", "V\\$STORAGE_TRX_DETAIL", "V\\$STORAGE_TRX_STAT",
    "V\\$STORAGE_UNDO_PURGER_INFO", "V\\$STORAGE_UNDO_STAT", "V\\$STORAGE_VERTEX_COUNT", "V\\$MEM_COMPACT_TASKS_STAT",
    "V\\$SERVER_MEMORY_OVERHEAD", "V\\$SYS_MODULE_MEM_INFO", "V\\$CST_SHMEM_INFO", "V\\$YANG_PLAN_DEPENDENCY",
    "V\\$YANG_PLAN_EXPLAIN_INFO", "V\\$YANG_VALIDATION_STAT"};

int TestSysviewCheck(char *cmd)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error. wrong property name.\n", cmd);
        return -1;
    }
    char *tmp = (char *)malloc(sizeof(char) * 1024 * 10240);
    if (tmp == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[TestSysviewCheck] tmp malloc failed !!!");
        pclose(pf);
        return -1;
    }
    memset(tmp, 0, 1024 * 10240);
    
    char buffer[1024] = {0};
    // 存取临时内容
    bool isFind = 0;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "ret = %d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "ret = %d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorMsg01, MAX_CMD_SIZE, "sysview get records unsucc, ret = %d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(g_errorMsg02, MAX_CMD_SIZE, "sysview get records unsucc, ret = %d", GMERR_REQUEST_TIME_OUT);
    (void)snprintf(g_errorMsg03, MAX_CMD_SIZE, "sysview get records unsucc, ret = %d", GMERR_INVALID_NAME);
    
    int size = 1024 * 10240;
    while (fgets(buffer, 1024 - 1, pf) != NULL) {
        strcat_s(tmp, size, buffer);
        if (strstr(buffer, "fetched all records, finish!")) {
            isFind = 1;
            break;
        } else if (strstr(cmd, "QRY_DML_INFO") && strstr(buffer, g_errorCode01)) {
            // 该视图只有参数enableDmlPerfStat和enableDmlOperStat均为1时才能查询到数据
            isFind = 1;
            break;
        } else if (strstr(buffer, g_errorMsg01)) {
            // 查视图存在拿不到锁的情况
            isFind = 1;
            break;
        } else if (strstr(buffer, g_errorMsg02)) {
            // 查视图存在超时的情况
            isFind = 1;
            break;
#ifdef FEATURE_MULTI_TS
        } else if (strstr(buffer, g_errorMsg03)) {
            // 查视图存在表不在的情况
            isFind = 1;
            break;
#endif
        }
    }
    if (pclose(pf) == -1) {
        perror("pclose fail");
    }
    pf = NULL;
    if (!isFind) {
        printf("sysview open error! %s \n", cmd);
        // 查询不符合预期，打屏显示
        printf("[TestSysviewCheck] execute view no match Val is\n%s", tmp);
        // 释放内存
        if (tmp) {
            free(tmp);
        }
        return -1;
    }
    // 释放内存
    if (tmp) {
        free(tmp);
    }
    return 0;
}

// 全量视图检查 随机
void *GmsysviewStability(void *arg)
{
    int ret = 0;
    char command[1024] = {0};
    while (1) {
        THREAD_CONTROL_POINT();
        int viewNum = 0;
        viewNum = rand() % VIEWNUM;
        (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, g_viewNames[viewNum]);
        ret = TestSysviewCheck(command);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(command, 0, sizeof(command));
        sleep(STIME);
    }
}

void *GmlogSwitch(void *arg)
{
    int ret = 0;
    // 使用一个建连操作完成初始化操作
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char g_command[1024];
    while (1) {
        THREAD_CONTROL_POINT();
        char const *g_procName = "toolsLongStab";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -g %s  ", g_toolPath, g_procName);
        ret = executeCommand(g_command, "Execute this command successfully.");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(g_command, 0, sizeof(g_command));
        sleep(STIME);
    }
}

#define EXPORT_NUM 8
void *GmexportStability(void *arg)
{
    int ret = 0;
    char g_command[1024] = {0};
    char messCompare[1024] = {0};
    char messCompare1[1024] = {0};
    ret = snprintf(messCompare, 1024, "ret = %d ", GMERR_UNDEFINED_TABLE);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
    }
    ret = snprintf(messCompare1, 1024, "ret = %d ", GMERR_LOCK_NOT_AVAILABLE);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
    }
    while (1) {
        THREAD_CONTROL_POINT();
        srand((uint32_t)time(0));
        int loop = rand() % EXPORT_NUM;
        if (loop == 0) {
            snprintf(g_command, 1024, "%s/gmexport -c vdata -f exportFile -t %s -ns %s", g_toolPath, g_ip4forwardName,
                g_testNameSpace);
            ret = executeCommandOr(g_command, "export file successfully.", messCompare, messCompare1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(g_command, 0, sizeof(g_command));
        } else if (loop == 1) {
            snprintf(g_command, 1024, "%s/gmexport -c vdata -f exportFile -t %s -ns %s ", g_toolPath, g_ifStabilityName,
                g_testNameSpace);
            ret = executeCommandOr(g_command, "export file successfully.", messCompare, messCompare1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(g_command, 0, sizeof(g_command));
        } else if (loop == 2) {
            snprintf(g_command, 1024, "%s/gmexport -c vdata -f exportFile -t %s -ns %s ", g_toolPath, g_cibLabelName,
                g_testNameSpace);
            ret = executeCommandOr(g_command, "export file successfully.", messCompare, messCompare1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(g_command, 0, sizeof(g_command));
        } else if (loop == 3) {
            snprintf(g_command, 1024, "%s/gmexport -c vdata -f exportFile -t %s -ns %s ", g_toolPath,
                g_sub_ip4fowardName, g_testNameSpace);
            ret = executeCommandOr(g_command, "export file successfully.", messCompare, messCompare1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(g_command, 0, sizeof(g_command));
        } else if (loop == 4) {
            snprintf(g_command, 1024, "%s/gmexport -c vdata -f exportFile -t %s -ns %s ", g_toolPath, g_customerName,
                g_testNameSpace);
            ret = executeCommandOr(g_command, "export file successfully.", messCompare, messCompare1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(g_command, 0, sizeof(g_command));
        } else if (loop == 5) {
            snprintf(g_command, 1024, "%s/gmexport -c vdata -f exportFile -t %s -ns %s ", g_toolPath,
                label_name_ip4foward_mini_lpm, g_testNameSpace);
            ret = executeCommandOr(g_command, "export file successfully.", messCompare, messCompare1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(g_command, 0, sizeof(g_command));
        } else if (loop == 6) {
            snprintf(g_command, 1024, "%s/gmexport -c vdata -f exportFile -t %s -ns %s ", g_toolPath,
                label_name_ip4foward_8k, g_testNameSpace);
            ret = executeCommandOr(g_command, "export file successfully.", messCompare, messCompare1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(g_command, 0, sizeof(g_command));
        } else if (loop == 7) {
            snprintf(g_command, 1024, "%s/gmexport -c vdata -f exportFile -t %s -ns %s ", g_toolPath, g_if32kName,
                g_testNameSpace);
            ret = executeCommandOr(g_command, "export file successfully.", messCompare, messCompare1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(g_command, 0, sizeof(g_command));
        }
        sleep(STIME);
    }
}

int testSysviewAlarm(char *cmd)
{
    FILE *fin = popen(cmd, "r");
    char out[4000] = {0};
    char target[10][100] = {"Alarm Type: alarm_id", "alarm_source", "alarm_middle_status", "alarm_current_status",
        "alarm_active_value", "alarm_cleared_value", "alarm_active_threshold", "alarm_cleared_threshold",
        "alarm_success_times", "alarm_fail_times"};
    int ret = 0;
    int num = 0;
    while (fgets(out, 4000 - 1, fin) != NULL) {
        if (num > 10) {
            ret = 0;
            break;
        }
        if (myStrStr(out, "No alarm data.")) {
            ret = 0;
            break;
        } else {
            if (myStrStr(out, target[num])) {
                num++;
                continue;
            } else {
                if (myStrStr(out, "alarm_location")) {
                    continue;
                }
                printf("[INFO] Sysview Alarm false. %s\n", out);
                ret = -1;
                break;
            }
        }
    }
    pclose(fin);
    return ret;
}

// gmsysview alarm 查看告警信息
void *GmsysviewAlarm(void *arg)
{
    int ret = 0;
    char g_command[1024] = {0};
    while (1) {
        THREAD_CONTROL_POINT();
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview alarm", g_toolPath);
        ret = testSysviewAlarm(g_command);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(g_command, 0, sizeof(g_command));
        sleep(STIME);
    }
}

int MemoryGet(char* viewname, char* memstr, char* memget)
{
    char command[1024] = {0};
    int ret = snprintf(command, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -l 1| grep %s | awk -F [:] '{print $2}'|sed 's/ //g'",
        g_toolPath, viewname, memstr);
    if (ret <= 0) {
        return -1;
    }
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error. wrong property name.\n", command);
        return -1;
    }
    while (fgets(memget, 1024, pf) != NULL) {
    }
    if (pclose(pf) == -1) {
        perror("pclose fail");
    }
    pf = NULL;
    memset(command, 0, sizeof(command));
    return 0;
}

int TestSysviewMemoryCheck(char *viewname, char *usemem, char *totalmem)
{
    int ret = 0;
    char usememGet[1024] = {0};
    char totalmemGet[1024] = {0};
    ret = MemoryGet(viewname, usemem, usememGet);
    ret = MemoryGet(viewname, totalmem, totalmemGet);
    int useMB, useKB, useByte;
    int totalMB, totalKB, totalByte;
    // 从use_mem中提取MB、KB、Byte的值
    (void)sscanf(usememGet, "[%d]MB[%d]KB[%d]Byte", &useMB, &useKB, &useByte);
    // 从total_mem中提取MB、KB、Byte的值
    (void)sscanf(totalmemGet, "[%d]MB[%d]KB[%d]Byte", &totalMB, &totalKB, &totalByte);
    if (useMB > totalMB) {
        printf("Abnormal memory, viewname:%s, %s:%s, %s:%s\n", viewname, usemem,
            usememGet, totalmem, totalmemGet);
        return -1;
    } else if (useMB < totalMB) {
        return 0;
    } else {
        if (useKB > totalKB) {
            printf("Abnormal memory, viewname:%s, %s:%s, %s:%s\n", viewname, usemem,
                usememGet, totalmem, totalmemGet);
            return -1;
        } else if (useKB < totalKB) {
            return 0;
        } else {
            if (useByte > totalByte) {
                printf("Abnormal memory, viewname:%s, %s:%s, %s:%s\n", viewname, usemem,
                    usememGet, totalmem, totalmemGet);
                return -1;
            } else {
                return 0;
            }
        }
    }
}

// gmsysview_memory 内存检查 free_size和used_size不大于total_size
void *GmsysviewMemory(void *arg)
{
    int ret = 0;
    while (1) {
        THREAD_CONTROL_POINT();
        // V$COM_DYN_CTX
        ret = TestSysviewMemoryCheck((char *)"V\\$COM_DYN_CTX", (char *)"TOTAL_ALLOC_SIZE", (char *)"TOTAL_PHY_SIZE:");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // V$COM_MEM_SUMMARY
        ret = TestSysviewMemoryCheck((char *)"V\\$COM_MEM_SUMMARY", (char *)"STORAGE_MEM_USED",
            (char *)"STORAGE_MEM_TOTAL");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = TestSysviewMemoryCheck((char *)"V\\$COM_MEM_SUMMARY", (char *)"NON_STORAGE_SHMEM_ALLOCSIZE",
            (char *)"NON_STORAGE_SHMEM_PHYSIZE");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // V$COM_SHMEM_CTX
        ret = TestSysviewMemoryCheck((char *)"V\\$COM_SHMEM_CTX", (char *)"GLOBAL_ALLOC_SIZE",
            (char *)"GLOBAL_PHY_SIZE");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = TestSysviewMemoryCheck((char *)"V\\$COM_SHMEM_CTX", (char *)"TOTAL_ALLOC_SIZE:",
            (char *)"TOTAL_PHY_SIZE");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // V$CST_SHMEM_INFO
        ret = TestSysviewMemoryCheck((char *)"V\\$CST_SHMEM_INFO", (char *)"TOTAL_PHY_SIZE",
            (char *)"TOTAL_ALLOC_SIZE");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(STIME);
    }
}
