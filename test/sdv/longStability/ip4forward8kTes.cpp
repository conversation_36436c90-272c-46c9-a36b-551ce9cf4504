/****************************************************************************
 Description  : 长稳用例--ip4foward_8k表操作
 recycle_write_remove_sync_8k     随机产生一个【0,5k】数值a，对 【a，a+500]】间的数据进行：写--主键更新--主键删除操作
 localkey_scan_read_ip4foward_8k  随机产生一个【0,5k】数值a，对 【a，a+500】的区间进行 local范围降序扫描，进行读
 read_thread_ip4foward_8k         随机产生一个【0,5k】的数 a，对【a，a+500】 进行结构化读，对【a，a+500】主键读操作
 update_thread_ip4foward_8k       随机产生一个【0,5k】数值a，对 【a，a+500】间的数进行主键索引更新
 batch_operation_thread_ip4foward_8k 随机产生一个【0,5k】的数a，【a,a+20】的区间进行批量操作，进行批量写，更新，删除操作
 hashcluster_scan_thread_ip4foward_8k   随机产生一个【0,5k】数进行非唯一hashcluster
 sub_ip4foward_8k                 订阅，推送时间[0,9]随机值，sub_ip4foward
 get_record_by_hashcluster_8k     随机产生一个【0,5k】数hashcluster非唯一索引获取表记录数
 remove_and_scan_by_primary_key_8k 对【0,5k】间的数据进行先扫描 再删除
 write_thread_ip4foward_8k         对【0,2k】间的数据进行结构化写操作
 hashcluster_scan_ip4foward_8k    【0,5k】间的数据进行hashcluster扫描操作
 localkey_scan_remove_ip4foward_8k 随机产生一个【0,5k】数值a，对 【a，a+500】间的数据进行localkey升序范围扫描删除
 multiple_object_ddl_8k            创建200个object，随机产生一个【0,199】的object进行写，更新和删除操作，再释放200个object
 aging_thread_ip4foward_8k         随机产生一个【50,200】间的时间数值a，每隔a秒对表数据进行老化一次
 local_update_delete_scan_8k       同步：写--local索引等值更新--local索引等值删除--扫描等值操作
 localkey_batch_update_delete_8k   同步：批量写--local索引等值批量更新--local索引等值批量删除
 local_insert_update_delete_scan_async_8k  异步：写-local等值更新-等值删除-批量写-local批量更新-批量删除
 Date         : 2021/08/30
*****************************************************************************/
extern "C" {
}

#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"
#include "ip4foward_8k_test.h"
#include "structLongStability.h"
#include "insufficientAddressSpace.h"
#include "ClientSingnoProtect.h"

#define TESTFOWARD_8K_SECNE_NUM 19  //场景数

uint32_t g_dataN = 0;
int16_t g_sceneFlag[TESTFOWARD_8K_SECNE_NUM + 1] = {0};
int16_t g_sceneThreadN[TESTFOWARD_8K_SECNE_NUM + 1] = {1};
pthread_t ip4forward_8k_thr[THREADS_NUM_MAX];
pthread_t ip4forward_8k_thr1[THREADS_NUM_MAX], ip4forward_8k_thr2[THREADS_NUM_MAX], ip4forward_8k_thr3[THREADS_NUM_MAX],
    ip4forward_8k_thr4[THREADS_NUM_MAX], ip4forward_8k_thr5[THREADS_NUM_MAX];
pthread_t ip4forward_8k_thr6, ip4forward_8k_thr7[THREADS_NUM_MAX], ip4forward_8k_thr8[THREADS_NUM_MAX],
    ip4forward_8k_thr9[THREADS_NUM_MAX], ip4forward_8k_thr10[THREADS_NUM_MAX];
pthread_t ip4forward_8k_thr11[THREADS_NUM_MAX], ip4forward_8k_thr12[THREADS_NUM_MAX], ip4forward_8k_thr13,
    ip4forward_8k_thr14[THREADS_NUM_MAX], ip4forward_8k_thr15[THREADS_NUM_MAX];
pthread_t ip4forward_8k_thr16[THREADS_NUM_MAX], ip4forward_8k_thr17[THREADS_NUM_MAX], ip4forward_8k_thr18;

void *randow_thread_Ip4foward8k(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t countN = 0;
    uint32_t randowNum = 0;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    while (1) {
        THREAD_CONTROL_POINT();
        randowNum = rand() % 17;
        if (randowNum <= 1) {
            recycle_write_remove_sync_8k(conn, stmt, NULL);
        } else if (randowNum == 2) {
            localkey_scan_read_ip4foward_8k(conn, stmt, NULL);
        } else if (randowNum == 3) {
            read_thread_ip4foward_8k(conn, stmt, NULL);
        } else if (randowNum == 4) {
            update_thread_ip4foward_8k(conn, stmt, NULL);
        } else if (randowNum == 5) {
            ret = batch_operation_thread_ip4foward_8k(conn, stmt, NULL);
            // 混沌适配
            testGmcDisconnect(conn, stmt);
            conn = NULL;
            stmt = NULL;
            ret = testGmcConnect(&conn, &stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else if (randowNum == 6) {
            hashcluster_scan_thread_ip4foward_8k(conn, stmt, NULL);
        } else if (randowNum == 7) {
            get_record_by_hashcluster_8k(conn, stmt, NULL);
        } else if (randowNum == 8) {
            remove_and_scan_by_primary_key_8k(conn, stmt, NULL);
        } else if (randowNum == 9) {
            write_thread_ip4foward_8k(conn, stmt, NULL);  // 结构化写
        } else if (randowNum == 10) {
            hashcluster_scan_ip4foward_8k(conn, stmt, NULL);
        } else if (randowNum == 11) {
            localkey_scan_remove_ip4foward_8k(conn, stmt, NULL);
        } else if (randowNum == 12) {
            multiple_object_ddl_8k(conn, stmt, NULL);
        } else if (randowNum == 13) {
            local_update_delete_scan_8k(conn, stmt, NULL);
        } else if (randowNum == 14) {
            localkey_batch_update_delete_8k(conn, stmt, NULL);
        } else if (randowNum == 15) {
            hashcluster_partial_index_sync_8k(conn, stmt, NULL);
        } else {
            local_insert_update_delete_scan_async_8k(conn_async, stmt_async, NULL);
        }
    }
}
    void Ip4foward8kStabilityDefaultScene()
    {
        int ret = 0;
        ret = testEnvInit();
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 创建epoll
        ret = create_epoll_thread();
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 建表
        TEST_EXPECT_INT32(GMERR_OK, create_label_ip4foward_8k());
        // 预制数据
        ret = prepare_write_ip4foward_8k_sync(0, IP4FORWARD_8K_NUM);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        printf("[INFO]insert table ip4foward_8K data success \n");

        for (int thr_num = 0; thr_num < TESTFOWARD_8K_SECNE_NUM/3; thr_num++) {
            pthread_create(&ip4forward_8k_thr[thr_num], g_pthreadAttrPtr, &randow_thread_Ip4foward8k, NULL);
        }

        pthread_create(&ip4forward_8k_thr6, g_pthreadAttrPtr, sub_ip4foward_8k, NULL);            // 订阅
        pthread_create(&ip4forward_8k_thr18, g_pthreadAttrPtr, bitmap_sub_ip4foward_8k, NULL);    // 订阅
        pthread_create(&ip4forward_8k_thr13, g_pthreadAttrPtr, aging_thread_ip4foward_8k, NULL);  // 老化

        void *simple_8k_ret[THR_NUM] = {0};
        for (int thr_num = 0; thr_num < TESTFOWARD_8K_SECNE_NUM/3; thr_num++) {
            pthread_join(ip4forward_8k_thr[thr_num], &simple_8k_ret[thr_num]);
        }
        pthread_join(ip4forward_8k_thr6, NULL);
        pthread_join(ip4forward_8k_thr13, NULL);
        pthread_join(ip4forward_8k_thr18, NULL);

        ret = close_epoll_thread();
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 清环境
        testEnvClean();
    }

    // 按实际输入的参数启动线程
    void Ip4foward8kStabilitySceneTest()
    {
        int ret = 0;
        ret = testEnvInit();
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 创建epoll
        ret = create_epoll_thread();
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 建表
        TEST_EXPECT_INT32(GMERR_OK, create_label_ip4foward_8k());
        // 预制数据
        ret = prepare_write_ip4foward_8k_sync(0, g_dataN);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        printf("[INFO]insert table ip4foward_8K data success \n");

        for (int thr_num = 0; thr_num < TESTFOWARD_8K_SECNE_NUM/3; thr_num++) {
            pthread_create(&ip4forward_8k_thr[thr_num], g_pthreadAttrPtr, &randow_thread_Ip4foward8k, NULL);
        }
        pthread_create(&ip4forward_8k_thr6, g_pthreadAttrPtr, sub_ip4foward_8k, NULL);            // 订阅
        pthread_create(&ip4forward_8k_thr18, g_pthreadAttrPtr, bitmap_sub_ip4foward_8k, NULL);    // 订阅
        pthread_create(&ip4forward_8k_thr13, g_pthreadAttrPtr, aging_thread_ip4foward_8k, NULL);  // 老化

        void *simple_8k_ret[THR_NUM] = {0};
        for (int thr_num = 0; thr_num < TESTFOWARD_8K_SECNE_NUM/3; thr_num++) {
            pthread_join(ip4forward_8k_thr[thr_num], &simple_8k_ret[thr_num]);
        }
        pthread_join(ip4forward_8k_thr6, NULL);
        pthread_join(ip4forward_8k_thr13, NULL);
        pthread_join(ip4forward_8k_thr18, NULL);
        ret = close_epoll_thread();
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 清环境
        testEnvClean();
    }

    int main(int argc, char **argv)
    {
        pthread_t addrSpaceThread;
        createInsufficientAddressSpaceThread(false, &addrSpaceThread);
        if (argc == 1) {
            // 默认原V3场景
            Ip4foward8kStabilityDefaultScene();
        } else {
            // 将标志位/数据量/线程数置0，默认不启动任何场景和线程数
            g_dataN = 0;
            for (int j = 0; j < TESTFOWARD_8K_SECNE_NUM + 1; j++) {
                g_sceneFlag[j] = 0;
                g_sceneThreadN[j] = 0;
            }
            int ret = longStabilitySetSceneFlag(
                argv, g_sceneFlag, g_sceneThreadN, TESTFOWARD_8K_SECNE_NUM, &g_dataN, IP4FORWARD_8K_NUM);
            if (ret != 0) {
                return 1;
            }
            // 按入参启动线程
            Ip4foward8kStabilitySceneTest();
        }
        pthread_join(addrSpaceThread, NULL);
    }
