/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#ifndef DATALOG_STABILITY_TEST_H
#define DATALOG_STABILITY_TEST_H

#ifdef FEATURE_DATALOG

#include "t_datacom_lite.h"
#include "longStability.h"
#include "DatalogSubFuc.h"
#include "cpuControl.h"

#define MAX_CMD_SIZE 1024
#define FILE_PATH 512
#define LABEL_NAME 512
#if defined RUN_INDEPENDENT
    #define DATALOG_NUM 5000
    #define YLOG_NUM 4000
    #define DTL_THR_NUM 3
#else
    #define DATALOG_NUM 1000
    #define YLOG_NUM 1000
    #define DTL_THR_NUM 1
#endif
#define START_NUM 15000
#define DATALOG_WRITE_NUM 500
#define LPM4_PREFIX_MAX 32
#define LPM6_PREFIX_MAX 128
#define VPN_MAX_SIZE 1000000  // .d文件中lpm表的max_size
// ylog中，外部表的约束
#define BRIDGE_MAX_SIZE 256
#define PORT_MAX_SIZE 4608
#define USER_TABLE_SCENE_NUM 6
#define YLOG_TABLE_SCENE_NUM 7

char g_hFile[FILE_PATH] = "../../../pub/include/";
char g_inputTableA[LABEL_NAME] = "namespace01.inputTableA";
char g_inputTableB[LABEL_NAME] = "namespace01.inputTableB";
char g_inputTableC[LABEL_NAME] = "namespace01.inputTableC";
char g_inputTableD[LABEL_NAME] = "namespace01.inputTableD";
char g_timeoutTableA[LABEL_NAME] = "namespace01.timeoutTableA";
char g_timeoutTableB[LABEL_NAME] = "namespace01.timeoutTableB";
char g_updateTable[LABEL_NAME] = "namespace01.updateTable";
char g_updateTableB[LABEL_NAME] = "namespace01.updateTableB";
char g_inputLpm4[LABEL_NAME] = "namespace01.inputLpm4";
char g_inputLpm6[LABEL_NAME] = "namespace01.inputLpm6";
char g_outputLpm4[LABEL_NAME] = "namespace01.outputLpm4";
char g_outputLpm6[LABEL_NAME] = "namespace01.outputLpm6";
char g_partialUpdateTable[LABEL_NAME] = "namespace01.updatePartialTable";
char g_soName[FILE_PATH] = "datalogStabilityFile";
char g_ylogsoName[FILE_PATH] = "y";
char g_tableName[128] = "capv5";
char g_externName[128] = "externalD";
char g_configJson[128] = "{\"max_record_count\":100000, \"isFastReadUncommitted\": false}";
char g_cfgName[MAX_CMD_SIZE] = {0};
char g_command[MAX_CMD_SIZE] = {0};

/*--------------------------------------编译、加载------------------------------------*/
void CompileTest(char *inputFilePath, char *outputFilePath, char *soName, bool haveUdf)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char datalogFile[FILE_PATH] = {0};
    char outputFile[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    char udfFile[FILE_PATH] = {0};
    // 赋值
    (void)sprintf(datalogFile, "%s/%s.d", inputFilePath, soName);
    (void)sprintf(outputFile, "%s/%s.c", outputFilePath, soName);
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    if (haveUdf) {
        (void)sprintf(udfFile, "%s/%s_udf.c", inputFilePath, soName);
    }
    // .d->.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, datalogFile, outputFile);
    int ret = executeCommand(command, "Serialize done");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // .c->.so
    if (haveUdf) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n",
            g_hFile, outputFile, udfFile, libName);
    } else {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n",
            g_hFile, outputFile, libName);
    }
    system(command);
    AW_FUN_Log(LOG_STEP, "compile success.");
}

// 编译ylog
void CompileYlog(char *inputFilePath, char *outputFilePath, char *soName)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char mainFile[FILE_PATH] = {0};
    char ydFile[FILE_PATH] = {0};
    char ycFile[FILE_PATH] = {0};
    char udfFile[FILE_PATH] = {0};
    char iFile[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    // 赋值
    (void)sprintf(mainFile, "%s/core/product/main_soho.d", inputFilePath);
    (void)sprintf(ydFile, "%s/%s.d", inputFilePath, soName);
    (void)sprintf(ycFile, "%s/%s.c", outputFilePath, soName);
    (void)sprintf(udfFile, "%s/udf/hpf/*.c %s/udf/lsw/*.c %s/udf/middle/*.c %s/udf/common/*.c", inputFilePath,
        inputFilePath, inputFilePath, inputFilePath);
    (void)sprintf(iFile, "-I %s/gmjson2c_gen/ -I %s/include/ -I %s/aarch64-linux-gnu/securec -I "
        "%s/aarch64-linux-gnu/orm -I %s/aarch64-linux-gnu/hpf -I %s/aarch64-linux-gnu/hpe-hlibc -I "
        "%s/aarch64-linux-gnu/nat-hpk -I %s/aarch64-linux-gnu/hpf-inner/include_nat -I %s/udf",
        inputFilePath, inputFilePath, inputFilePath, inputFilePath, inputFilePath, inputFilePath, inputFilePath,
        inputFilePath, inputFilePath);
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);

    // /core/product/main_soho.d->y.d
    (void)snprintf(command, MAX_CMD_SIZE, "cpp -P %s %s", mainFile, ydFile);
    system(command);

    // .d->.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s -supressSpeErr", g_toolPath, ydFile, ycFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 生成so
    (void)snprintf(command, MAX_CMD_SIZE, "gcc %s %s -fPIC --shared -Wl,-Bsymbolic -I %s %s -o %s \n",
        ycFile, udfFile, g_hFile, iFile, libName);
    system(command);
    AW_FUN_Log(LOG_STEP, "compile Ylog success.");
}
/*--------------------------------编译生成patch.c+full.d、加载升级patchso---------------------------------*/
void CompileUpgradeAndRollBackTest(char *inputFilePath, char *outputFilePath, char *soName, bool havePatchUdf,
    int upgradeCnt)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    char rollLibName[FILE_PATH] = {0};
    char udfPatchFile[FILE_PATH] = {0};

    if (upgradeCnt == 1) {
        (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
        (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
        (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, upgradeCnt + 1);
        (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);
        (void)snprintf(rollbackPatchCName, FILE_PATH, "%s/%s_patch_rollback.c", outputFilePath, soName);
        if (havePatchUdf) {
            (void)snprintf(udfPatchFile, FILE_PATH, "%s/%s_patch_udf.c", outputFilePath, soName);
        }
    } else {
        (void)snprintf(ruleFile, FILE_PATH, "%s/%s_ruleV%d.d", inputFilePath, soName, upgradeCnt);
        (void)snprintf(patchFile, FILE_PATH, "%s/%s_ruleV%d_patch.d", inputFilePath, soName, upgradeCnt);
        (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, upgradeCnt + 1);
        (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_ruleV%d_patch.c", outputFilePath, soName, upgradeCnt);
        (void)snprintf(rollbackPatchCName, FILE_PATH, "%s/%s_ruleV%d_patch_rollback.c", outputFilePath, soName,
            upgradeCnt);
        if (havePatchUdf) {
            (void)snprintf(udfPatchFile, FILE_PATH, "%s/%s_ruleV%d_patch_udf.c", outputFilePath, soName, upgradeCnt);
        }
    }
    (void)snprintf(libName, FILE_PATH, "%s/%s_patchV%d.so", outputFilePath, soName, upgradeCnt + 1);
    (void)snprintf(rollLibName, FILE_PATH, "%s/%s_rollbackV%d.so", outputFilePath, soName, upgradeCnt + 1);
    // rule.d + patch.d -> patch.c + ruleV2.d + patch_rollback.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supressSpeErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    if (havePatchUdf) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile,
            patchCOutputFile, udfPatchFile, libName);
    } else {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile,
            patchCOutputFile, libName);
    }
    system(command);
    // 生成回滚so
    (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile,
        rollbackPatchCName, rollLibName);
    system(command);
}

// 获取订阅的名字
int GetSubNameStr(char *cmd, char **objName)
{
    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        return T_FAILED;
    }
 
    int size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        (void)pclose(fd);
        return T_FAILED;
    }
    memset(tmpResult, 0, size);
 
    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat(tmpResult, buf);
    }
    int ret = pclose(fd);
    if (ret == T_FAILED) {
        free(tmpResult);
        return T_FAILED;
    }
    *objName = tmpResult;
    return GMERR_OK;
}

// 删除public命名空间下的订阅关系
int TestCancelAllLongStabilitySub(bool flag)
{
#if defined(RUN_DATACOM_HPE)
    AW_FUN_Log(LOG_INFO, "[TestCancelAllLongStabilitySub] popen can not run in hpe env\n");
    return 0;
#else
    if (!flag) {
        return GMERR_OK;
    }
    int ret = 0;
    char *subName = NULL, *viewName = (char *)"V\\$CATA_LABEL_SUBS_INFO";
    char *viewName2 = (char *)"V\\$CATA_NAMESPACE_INFO";
    int nsId = 0;
    char cmd[1024] = {0}, cmd2[1024] = {0}, cmd3[1024] = {0};
    (void)snprintf(cmd, 1024, "%s/gmsysview -q %s", g_toolPath, viewName);
    (void)snprintf(cmd2, 1024, "%s > /dev/null 2>&1", cmd);
    // 验证视图是否可用, 失败则直接返回, 避免往下走陷入死循环
    if (system(cmd2) != 0) {
        ret = executeCommand(cmd, "connect unsucc");
        if (ret == GMERR_OK) {
            return T_FAILED2;
        } else {
            system(cmd);
            RETURN_IFERR(T_FAILED);
        }
    }
    // 获取public命名空间namespace id
    (void)snprintf(cmd3, 1024, "%s/gmsysview -q %s -f NAMESPACE_NAME=\'public\' | grep NAMESPACE_ID  | "
        "awk '{print $2}' | sed 's/ //g'", g_toolPath, viewName2);
    ret = TestGetResultCommand(cmd3, &nsId);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    (void)snprintf(
        cmd, 1024, "%s/gmsysview -q %s -f NAMESPACE_ID=%d | grep \'IS_DELETED: 0\' -B 4 | grep SUBS_NAME "
        "| awk NR==1'{print $2}' | tr -d '\\n'", g_toolPath, viewName, nsId);
    while (!ret) {
        ret = GetSubNameStr(cmd, &subName);
        RETURN_IFERR(ret);
        if (strcmp(subName, "") == 0) {
            free(subName);
            subName = NULL;
            break;
        }
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        ret = testGmcConnect(&conn, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcUseNamespace(stmt, g_testNameSpace);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subName);
        AW_FUN_Log(LOG_INFO, "[TestCancelAllLongStabilitySub] delete sub name : %s, ret : %d", subName, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcDisconnect(conn, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(subName);
        subName = NULL;
    }
    return GMERR_OK;
#endif
}

// 非轻量化创建kv表并插入数据
int CreateKvTable()
{
    int ret = 0;
    // 创建kv表
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    GmcKvDropTable(stmt, g_tableName);
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "para1";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    
    GmcKvTupleT kvInfo1 = {0};
    char key1[32] = "para2";
    char value1[32] = "aaaaaa";
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1) + 1;
    kvInfo1.value = value1;
    kvInfo1.valueLen = strlen(value1) + 1;
    ret = GmcKvSet(stmt, kvInfo1.key, kvInfo1.keyLen, kvInfo1.value, kvInfo1.valueLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return ret;
}
// 非轻量化事务，创建外部表
int CreateExternalTable()
{
    int ret = 0;
    // 创建外部表
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    GmcDropVertexLabel(stmt, g_externName);
    char *schema = NULL;
    readJanssonFile("./datalogFile/externalD.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);
    ret = GmcCreateVertexLabel(stmt, schema, g_configJson);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(schema);

    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return ret;
}

/*-------------------------------------------------订阅回调函数-------------------------------------------------*/
// 回调中，获取属性值并设置属性值
int GetProValueAndSetProValue(GmcStmtT *subStmt, bool isResourcePubSub)
{
    // get值
    int ret = 0;
    bool isNull;
    // a
    int32_t valueA = 0;
    ret = GmcGetVertexPropertyByName(subStmt, "a", &valueA, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // b
    int32_t valueB = 0;
    ret = GmcGetVertexPropertyByName(subStmt, "b", &valueB, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // c
    int32_t valueC = 0;
    ret = GmcGetVertexPropertyByName(subStmt, "c", &valueC, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    int64_t valueD = 0;
    int32_t valueE = 0;
    // e--资源字段
    if (isResourcePubSub) {
        // d
        ret = GmcGetVertexPropertyByName(subStmt, "d", &valueD, sizeof(int64_t), &isNull);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        valueE = 0;
    } else {
        // pubsub可更新表d字段是变长byte；占10240字节
        uint32_t dByteLength = 0;
        ret = GmcGetVertexPropertySizeByName(subStmt, "d", &dByteLength);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        int8_t *valueD1 = (int8_t *)malloc(dByteLength + 1);
        if (valueD1 == NULL) {
            AW_FUN_Log(LOG_STEP, "[GetProValueAndSetProValue] valueD1 malloc failed!!!");
            return -1;
        }
        ret = GmcGetVertexPropertyByName(subStmt, "d", valueD1, dByteLength, &isNull);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(valueD1);
    }
    
    // dtlReservedCount
    int32_t count;
    ret = GmcGetVertexPropertyByName(subStmt, "dtlReservedCount", &count, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // upgradeVersion
    int32_t upgradeVersionVal;
    ret = GmcGetVertexPropertyByName(subStmt, "upgradeVersion", &upgradeVersionVal, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (isResourcePubSub) {
        // set值
        ret = GmcSetVertexProperty(subStmt, "a", GMC_DATATYPE_INT32, &valueA, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(subStmt, "b", GMC_DATATYPE_INT32, &valueB, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(subStmt, "c", GMC_DATATYPE_INT32, &valueC, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(subStmt, "d", GMC_DATATYPE_INT64, &valueD, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(subStmt, "e", GMC_DATATYPE_INT32, &valueE, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(subStmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(subStmt, "upgradeVersion", GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}
typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    bool isResourcePubSub;  // false:notify表， true：pubsub资源表
} SnUserDataWithFuncT;

int snCallbackCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    bool isResourcePubSub = userDefinedData->isResourcePubSub;
    char labelName[LABEL_NAME] = {0};
    unsigned int labelNameLen;

    data->callbackTimes++;
    TEST_EXPECT_INT32(1, info->labelCount);
    int ret = 0;
    GmcRespT *response;
    ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);

    // 区分notify表和pubsub资源表
    if (!isResourcePubSub) {    // notify表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        RETURN_IFERR(ret);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
        ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        RETURN_IFERR(ret);
    } else {                   // pubsub型资源表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
        RETURN_IFERR(ret);
    }
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = LABEL_NAME;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    // pubsub资源表
                    if (isResourcePubSub) {
                        // 获取pubsub型资源表值并设置值
                        ret = GetProValueAndSetProValue(subStmt, isResourcePubSub);
                        TEST_EXPECT_INT32(GMERR_OK, ret);
                        ret = GmcSubAddRespDML(response, subStmt);
                        RETURN_IFERR(ret);
                    } else {
                        // notify
                        ret = GetProValueAndSetProValue(subStmt, isResourcePubSub);
                        TEST_EXPECT_INT32(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    // 屏蔽assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                // 屏蔽assert(0);
            }
        }
    }
    // 发送消息
    ret = GmcSendResp(subStmt, response);
    RETURN_IFERR(ret);
    ret = GmcDestroyResp(subStmt, response);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

void snCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = snCallbackCheck(subStmt, info, userData);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void SubPubSubResourceAndNotify()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅连接
    int chanRingLen = 256;
    GmcConnT *conSnSync = NULL;
    GmcStmtT *stmtSnSync = NULL;
    const char *subConnName = "datalogSubConnName";
    const char *subName1 = "subPubsubResource";
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&conSnSync, &stmtSnSync, GMC_CONN_TYPE_SUB, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // pubsub资源表
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    if (userData01 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "userData01 malloc failed !!!");
        return;
    }
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (userData01->data == NULL) {
        AW_FUN_Log(LOG_DEBUG, "userData01->data malloc failed !!!");
        free(userData01);
        return;
    }
    memset(userData01->data, 0, sizeof(SnUserDataT));
    userData01->isResourcePubSub = true;
    // 创建订阅关系
    char *subInfo01 = NULL;
    readJanssonFile("./datalogFile/subInfoPubsubResourceTable.json", &subInfo01);
    AW_MACRO_EXPECT_NOTNULL(subInfo01);
    GmcSubConfigT tmpSubInfo1;
    tmpSubInfo1.subsName = subName1;
    tmpSubInfo1.configJson = subInfo01;
    ret = GmcSubscribe(stmt, &tmpSubInfo1, conSnSync, snCallback, userData01);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // notify表
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    if (userData02 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "userData02 malloc failed !!!");
        free(userData01->data);
        free(userData01);
        return;
    }
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (userData02->data == NULL) {
        AW_FUN_Log(LOG_DEBUG, "userData02->data malloc failed !!!");
        free(userData01->data);
        free(userData01);
        free(userData02);
        return;
    }
    memset(userData02->data, 0, sizeof(SnUserDataT));
    userData02->isResourcePubSub = false;
    // 创建订阅连接
    
    const char *subName2 = "subPubsubUpdateTable";
    // 创建订阅关系
    char *subInfo02 = NULL;
    readJanssonFile("./datalogFile/subInfoPubSubUpdateTable.json", &subInfo02);
    AW_MACRO_EXPECT_NOTNULL(subInfo02);
    GmcSubConfigT tmpSubInfo2;
    tmpSubInfo2.subsName = subName2;
    tmpSubInfo2.configJson = subInfo02;
    ret = GmcSubscribe(stmt, &tmpSubInfo2, conSnSync, snCallback, userData02);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    free(subInfo01);
    free(subInfo02);
}

void CreateDatalogTableTest()
{
    // 编译
    char inputFilePath[FILE_PATH] = "./datalogFile";
    char outputFilePath[FILE_PATH] = "./datalogFile";
// 欧拉环境编译
#if defined RUN_INDEPENDENT
    CompileTest(inputFilePath, outputFilePath, g_soName, true);
    // 编译生成升级so和降级so
    CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, g_soName, true, 1);
    CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, g_soName, false, 2);
    CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, g_soName, false, 3);
#endif
    // 加载
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, g_soName);
    TEST_EXPECT_INT32(GMERR_OK, TestLoadDatalog(libName));
    AW_FUN_Log(LOG_STEP, "gmimport so success.");
    sleep(ST);
    // 订阅pubsub资源表和notify表
    SubPubSubResourceAndNotify();
}

// 编译生成y.so，y_patchV2.so,y_patchV3.so,y_rollbackV2.so,y_rollbackV3.so;加载y.so
void CreateYlogTableTest()
{
    // 编译
    char inputFilePath[FILE_PATH] = "./ylog";
    char outputFilePath[FILE_PATH] = "./ylog";
    char externlabelJsonPath[FILE_PATH] = "./ylog/H2Dgmjson";
    char command[MAX_CMD_SIZE] = {0};
// 欧拉环境编译并导入外部表，设备上不需要执行
#if defined RUN_INDEPENDENT
    CompileYlog(inputFilePath, outputFilePath, g_ylogsoName);
    // 编译生成升级so和降级so
    CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, g_ylogsoName, false, 1);
    CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, g_ylogsoName, false, 2);
#endif

    // 导入外部表，所有环境都导入
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -s %s -c vschema -f %s -ns %s", g_toolPath, g_connServer,
        externlabelJsonPath, g_testNameSpace);
    int ret = executeCommand(command, "[DONE] Command type: import_vschema", "successfully");
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 加载
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, g_ylogsoName);
    TEST_EXPECT_INT32(GMERR_OK, TestLoadDatalog(libName));
    AW_FUN_Log(LOG_STEP, "gmimport ylog so success.");
    sleep(ST);
}


/*--------------------------------------获取当前时间ms------------------------------------*/
int GetTimeMs(int64_t *currentTime)
{
    if (currentTime == NULL) {
        return FAILED;
    }
    // 获取时间
    struct timespec tmValue;
    (void)clock_gettime(CLOCK_MONOTONIC, &tmValue);
    *currentTime = (int64_t)(tmValue.tv_sec * 1000) + (int64_t)(tmValue.tv_nsec / 1000000);
    return 0;
}

/*--------------------------------------set value------------------------------------*/
void setPkValue(GmcStmtT *stmt, int32_t value)
{
    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void setTableValueNoFieldC(GmcStmtT *stmt, int32_t value)
{
    int aValue = value;
    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &aValue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    int bValue = value;
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &bValue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    int64_t dValue = value;
    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &dValue, sizeof(int64_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void setFieldCValue(GmcStmtT *stmt, int32_t value)
{
    int ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// 设置变长字段的值
void setFieldCStrValue(GmcStmtT *stmt, int32_t value)
{
    uint8_t strvalue[10] = {0};
    (void)snprintf((char *)strvalue, 10, "s%08d", value);
    int ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_STRING, strvalue, strlen((char *)strvalue));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void setTableCFieldCValue(GmcStmtT *stmt, int32_t value)
{
    uint32_t sizeC = 16;
    uint8_t valueC[sizeC];
    (void)memset_s(valueC, sizeC, value, sizeC);
    for (int j = 0; j < sizeC; j++) {
        valueC[j] = value;
    }
    int ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_FIXED, valueC, sizeC);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void setTableDFieldCValue(GmcStmtT *stmt, int32_t value)
{
    uint32_t sizeD = 4;
    uint8_t valueC[sizeD];
    (void)memset_s(valueC, sizeD, value, sizeD);
    for (int j = 0; j < sizeD; j++) {
        valueC[j] = value;
    }
    int ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_FIXED, valueC, sizeD);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void setUpdateValue(GmcStmtT *stmt, int32_t value)
{
    int cValue = value + 10;
    int ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &cValue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void setTimeoutValue(GmcStmtT *stmt, int32_t value, int64_t currentTime)
{
    int64_t dValue = currentTime + (int64_t)((value % 1) * 60 * 60 * 1000);
    int ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &dValue, sizeof(int64_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// 设置20分钟过期
void setTimeoutMinuValue(GmcStmtT *stmt, int32_t value, int64_t currentTime)
{
    int64_t dValue = currentTime + 20 * 60 * 1000;
    int ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &dValue, sizeof(int64_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void setCountValue(GmcStmtT *stmt, int32_t value)
{
    int32_t count = value;
    int ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// flag为true，count为正；flag为false，count为负
// 出现锁情况，重试100次
void TableSingleWrite(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum, bool flag)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startNum; i <= endNum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        if (flag) {
            setCountValue(stmt, i);
        } else {
            setCountValue(stmt, -i);
        }
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[TableSingleWrite] try more 100, ret is %d.", ret);
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                setFieldCValue(stmt, i);
                setTableValueNoFieldC(stmt, i);
                if (flag) {
                    setCountValue(stmt, i);
                } else {
                    setCountValue(stmt, -i);
                }
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 跟热补丁升降级并发，有可能拿不到datalogService读锁
void TableCSingleWrite(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum, bool flag)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startNum; i <= endNum; i++) {
        // set value
        setFieldCStrValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        if (flag) {
            setCountValue(stmt, i);
        } else {
            setCountValue(stmt, -i);
        }
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[TableCSingleWrite] try more 100, ret is %d.", ret);
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                setFieldCStrValue(stmt, i);
                setTableValueNoFieldC(stmt, i);
                if (flag) {
                    setCountValue(stmt, i);
                } else {
                    setCountValue(stmt, -i);
                }
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
void TableUpdateTest(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum, bool flag)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startNum; i <= endNum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i + 10);
        setPkValue(stmt, i);
        if (flag) {
            setCountValue(stmt, i);
        } else {
            setCountValue(stmt, -i);
        }
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                setFieldCValue(stmt, i);
                setTableValueNoFieldC(stmt, i + 10);
                setPkValue(stmt, i);
                if (flag) {
                    setCountValue(stmt, i);
                } else {
                    setCountValue(stmt, -i);
                }
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
void TimeoutTableASingleWrite(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum, bool flag,
    int64_t current)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startNum; i <= endNum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        setTimeoutMinuValue(stmt, i, current);
        if (flag) {
            setCountValue(stmt, i);
        } else {
            setCountValue(stmt, -i);
        }
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // set value
                setFieldCValue(stmt, i);
                setTableValueNoFieldC(stmt, i);
                setTimeoutMinuValue(stmt, i, current);
                if (flag) {
                    setCountValue(stmt, i);
                } else {
                    setCountValue(stmt, -i);
                }
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
void TimeoutTableBSingleWrite(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum, bool flag,
    int64_t current)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startNum; i <= endNum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        setTimeoutValue(stmt, i, current);
        if (flag) {
            setCountValue(stmt, i);
        } else {
            setCountValue(stmt, -i);
        }
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // set value
                setFieldCValue(stmt, i);
                setTableValueNoFieldC(stmt, i);
                setTimeoutValue(stmt, i, current);
                if (flag) {
                    setCountValue(stmt, i);
                } else {
                    setCountValue(stmt, -i);
                }
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 过期表主键删除
void TimeoutTableBPKDelete(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum)
{
    int ret = 0;
    int32_t upgradeVersionVal = -1;
    for (int i = startNum; i <= endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[TimeoutTableBPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

void TableBatchWrite(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum, bool flag)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int i = startNum; i <= endNum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        if (flag) {
            setCountValue(stmt, i);
        } else {
            setCountValue(stmt, -i);
        }
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    uint32_t tryCnt = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 100) {
                break;
            }
            // 重新组装报文
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchOptionInit(&batchOption);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            GmcBatchDestroy(batch);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);

            for (int i = startNum; i <= endNum; i++) {
                // set value
                setFieldCValue(stmt, i);
                setTableValueNoFieldC(stmt, i);
                if (flag) {
                    setCountValue(stmt, i);
                } else {
                    setCountValue(stmt, -i);
                }
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            ret = GmcExecute(stmt);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    if (ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
}

void PrepareWriteTable(int startNum, int dataNum)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char soName[FILE_PATH] = "datalogStabilityFile";
    TableSingleWrite(conn, stmt, g_inputTableA, startNum, startNum + dataNum, true);
    sleep(10);
    TEST_EXPECT_INT32(0, TestWaitDatalogQueue());
    AW_FUN_Log(LOG_STEP, "insert datalog table end.");
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 输入表A：随机产生一个【0,5k】数值a，对 【a，a+500】间的数据进行：单写、删除
void SingleWriteInputAAndInputCThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2) + 1;
    // 单写
    TableSingleWrite(conn, stmt, g_inputTableA, index, index + writeNum, true);
    TableCSingleWrite(conn, stmt, g_inputTableC, index, index + writeNum, true);
    sleep(ST);
    // 删除
    TableSingleWrite(conn, stmt, g_inputTableA, index, index + writeNum, false);
    TableCSingleWrite(conn, stmt, g_inputTableC, index, index + writeNum, false);
    sleep(ST);
}
// 输入表A：随机产生一个【0,5k】数值a，对 【a，a+500】间的数据进行：批写、删除
void BatchWriteInputAThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2) + 1;
    // 批写
    TableBatchWrite(conn, stmt, g_inputTableA, index, index + writeNum, true);
    sleep(ST);
    // 删除
    TableBatchWrite(conn, stmt, g_inputTableA, index, index + writeNum, false);
    sleep(ST);
}
void ResourceTableDML(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startNum; i <= endNum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        setCountValue(stmt, i);
        ret = GmcExecute(stmt);
        // 插入遇到事务锁，不再重复插入
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL
            && ret != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // 如果写入成功
        if (ret == GMERR_OK) {
            // 删除，遇到事务锁，重试100次
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            setFieldCValue(stmt, i);
            setTableValueNoFieldC(stmt, i);
            setCountValue(stmt, -i);
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP, "[ResourceTableDML] delete try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    setFieldCValue(stmt, i);
                    setTableValueNoFieldC(stmt, i);
                    setCountValue(stmt, -i);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
            }
            if (ret != GMERR_OUT_OF_MEMORY) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    }
}
// 超时表：随机产生一个【0,5k】数值a，对 【a，a+500】间的数据进行：写、删除
// 将两个timeout过期表合一起
void *WriteTimeoutTableThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = DATALOG_NUM * 2;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(args, &writeNum, g_globalCfg.insertNum);
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return NULL;
    }
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    while (1) {
        THREAD_CONTROL_POINT();
        time_t timer = time(0);
        if (!timer) {
            printf("[ERROR] get time fail\n");
        }
        srand((uint32_t)timer);
        int index = rand() % (countN / 2) + 1;
        // 单写timeoutTableB，含udf
        // 写namespace01.timeoutTableB表可能会遇到事务锁
        TimeoutTableBSingleWrite(conn, stmt, g_timeoutTableB, index, index + writeNum, true, 0);
        sleep(ST);
        // 主键删除
        TimeoutTableBPKDelete(conn, stmt, g_timeoutTableB, index, index + writeNum);
        sleep(ST);
        // 单写过期表
        int64_t currentTime;
        GetTimeMs(&currentTime);
        // 单写timeoutTableA，不含udf
        TimeoutTableASingleWrite(conn, stmt, g_timeoutTableA, index, index + writeNum, true, 0);
        // 记录20分钟之后过期，等待21分钟, 数据删除
        sleep(2160);
    }

    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// 全量可更新表: 随机产生一个【0,5k】数值a，对 【a，a+500】间的数据进行：写、更新、删除操作
void WriteUpdateTableAndTableBThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2) + 1;
    // 单写
    TableSingleWrite(conn, stmt, g_updateTable, index, index + writeNum, true);
    TableSingleWrite(conn, stmt, g_updateTableB, index, index + writeNum, true);
    sleep(ST);
    // 更新
    TableUpdateTest(conn, stmt, g_updateTable, index, index + writeNum, true);
    TableSingleWrite(conn, stmt, g_updateTableB, index, index + writeNum, true);
    sleep(ST);
    // 删除
    TableSingleWrite(conn, stmt, g_updateTable, index, index + writeNum, false);
    sleep(ST);
}
void PartialUpdateTableDML(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int startNum, int endNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startNum; i <= endNum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        setCountValue(stmt, i);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // 如果写入成功
        if (ret == GMERR_OK) {
            // 更新
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            setUpdateValue(stmt, i + 10);
            setPkValue(stmt, i);
            setCountValue(stmt, i);
            ret = GmcExecute(stmt);
            // 并发会存在4000错误码
            if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_PRIMARY_KEY_VIOLATION
                && ret != GMERR_DATA_EXCEPTION) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            // 删除，遇到事务锁，重试100次
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            setFieldCValue(stmt, i);
            setTableValueNoFieldC(stmt, i);
            setCountValue(stmt, -i);
            tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    setFieldCValue(stmt, i);
                    setTableValueNoFieldC(stmt, i);
                    setCountValue(stmt, -i);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            if (ret != GMERR_OUT_OF_MEMORY) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    }
}
// 部分可更新表: 随机产生一个【0,5k】数值a，对 【a，a+500】间的数据进行：写、部分更新、删除操作
void WritePartialUpdateTableThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    srand((uint32_t)timer);
    int index = rand() % (countN / 2) + 1;
    // 单写--部分更新--删除
    PartialUpdateTableDML(conn, stmt, g_partialUpdateTable, index, index + writeNum);
    sleep(ST);
}
/*---------------------------------------------------写ylog相关的表---------------------------------------*/
// 写表的回调函数
typedef int (*FuncWriteId)(GmcStmtT *stmt, int64_t value, int32_t count);
typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);
int writeRecordId(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int64_t startid, int64_t endid, int32_t count,
    FuncWriteId func, bool isBatch = true, bool isStruct = false)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置批写的数据的记录数
        ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    for (int i = startid; i < endid; i++) {
        ret = func(stmt, i, count);
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecordId] func fail, set tRet, ret = %d.", ret);
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecordId] GmcBatchAddDML fail, set tRet, i: %d, ret = %d.", i, ret);
                break;
            }
        } else {
            ret = GmcExecute(stmt);
            if (ret != GMERR_RESOURCE_POOL_NOT_ENOUGH && ret != GMERR_OUT_OF_MEMORY &&
                ret != GMERR_LOCK_NOT_AVAILABLE) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    }

    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        // 直接屏蔽1012002
        if (ret != GMERR_RESOURCE_POOL_NOT_ENOUGH && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
            // 屏蔽写Ifm.PublishNif表，出现主键冲突场景1005002
            if (strcmp(labelName, "Ifm.PublishNif") == 0 && ret != GMERR_PRIMARY_KEY_VIOLATION) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            if (strcmp(labelName, "Ifm.PublishNif") !=0 && ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "[writeRecordId] labelName : %s, ret is %d.", labelName, ret);
            }
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}
template <typename StructObjT>
int writeRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, const char * labelSchemaJson = NULL)
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置批写的数据的记录数
        ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    for (int i = 0; i < objLen; i++) {
        ret = (isStruct) ? testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo) : func(stmt, (void *)(obj + i));
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                break;
            }
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            // 订阅外部表，存在队列满错误码
            if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        break;
                    }
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = (isStruct) ? testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo) :
                        func(stmt, (void *)(obj + i));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
            }
            // 用例优化成给定线程数，随机执行场景，整体屏蔽1005002错误码
            if (ret != GMERR_RESOURCE_POOL_NOT_ENOUGH && ret != GMERR_UNIQUE_VIOLATION
                && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL
                && ret != GMERR_PRIMARY_KEY_VIOLATION) {
                // 屏蔽Ifm.PublishNif客户端异常断连错误码15002，异常退出有数据残留，会出现主键冲突
                // notify表推送过程中，断连订阅关系取消，会报4000错误码
                if (strcmp(labelName, "Ifm.PublishNif") == 0 && ret != GMERR_UNEXPECTED_NULL_VALUE &&
                    ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_DATA_EXCEPTION) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                if (strcmp(labelName, "Arp.ConfigArp") == 0 && ret != GMERR_UNEXPECTED_NULL_VALUE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                // fast_insert,若主键相同，会出现主键冲突;数据残留;notify表推送过程中，断连订阅关系取消，会报4000错误码
                if ((strcmp(labelName, "Fib.ConfigNhpStandard") == 0 || strcmp(labelName, "Fib.ConfigNhpBasic") == 0) &&
                    ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_UNEXPECTED_NULL_VALUE &&
                    ret != GMERR_DATA_EXCEPTION) {
                        TEST_EXPECT_INT32(GMERR_OK, ret);
                    }
                if (strcmp(labelName, "Ifm.PublishNif") != 0 && strcmp(labelName, "Fib.ConfigNhpStandard") != 0 &&
                    strcmp(labelName, "Fib.ConfigNhpBasic") != 0 && strcmp(labelName, "Arp.ConfigArp")) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    if (ret != GMERR_OK) {
                        AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcExecute fail, labelname:%s, ret = %d", labelName, ret);
                    }
                }
            }
        }
    }
    if (isBatch) {
        uint32_t tryCnt = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                if (isStruct) {
                    GmcBatchUnbindStmt(batch, stmt);
                }
                // 重新组装报文，进行发送
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchOptionInit(&batchOption);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置批写的数据的记录数
                ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                GmcBatchDestroy(batch);
                ret = GmcBatchPrepare(conn, &batchOption, &batch);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (isStruct) {
                    ret = GmcBatchBindStmt(batch, stmt);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                for (int i = 0; i < objLen; i++) {
                    ret = (isStruct) ? testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo) :
                        func(stmt, (void *)(obj + i));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcBatchAddDML(batch, stmt);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                ret = GmcBatchExecute(batch, &batchRet);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
        }
        if (ret != GMERR_RESOURCE_POOL_NOT_ENOUGH && ret != GMERR_UNIQUE_VIOLATION && ret != GMERR_OUT_OF_MEMORY
            && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            // 写Ifm.ConfigAttributes表会触发较多规则，join会超时
            if (strcmp(labelName, "Ifm.ConfigAttributes") == 0 && ret != GMERR_REQUEST_TIME_OUT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            if (strcmp(labelName, "Ifm.ConfigAttributes") != 0) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (ret != GMERR_OK) {
                    AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchExecute fail, labelname:%s, ret is %d",
                        labelName, ret);
                }
            }
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}
// 异步接口
template <typename StructObjT>
int writeRecordAsync(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, const char * labelSchemaJson = NULL)
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    AsyncUserDataT tdata = {0};
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &tdata;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置批写的数据的记录数
        ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    for (int i = 0; i < objLen; i++) {
        ret = (isStruct) ? testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo) : func(stmt, (void *)(obj + i));
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                break;
            }
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecuteAsync(stmt, &insertRequestCtx);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&tdata);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (tdata.status != GMERR_OUT_OF_MEMORY && tdata.status != GMERR_LOCK_NOT_AVAILABLE) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &tdata);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&tdata);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (tdata.status != GMERR_OUT_OF_MEMORY && tdata.status != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}
// ylog导入数据校验
void AbstractTableCheck()
{
    // 设备上，读文件需要权限
    system("touch test.txt");
    system("chmod 777 test.txt");
    int ret = 0;
    // Abstract.a_if_net
    ret = ScanTableData(g_expectabstractaifnet, "Abstract.a_if_net");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.a_if_phy
    ret = ScanTableData(g_expectabstractaifphy, "Abstract.a_if_phy");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.a_if_name
    ret = ScanTableLongFieldData("g_expectAbstractAIfName", "Abstract.a_if_name");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.a_if_link
    ret = ScanTableData(g_expectabstractaiflink, "Abstract.a_if_link");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.a_if_net6
    ret = ScanTableData(g_expectabstractaifnet6, "Abstract.a_if_net6");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.Tbl_Port
    ret = ScanTableLongFieldData("g_expectAbstractTblPort", "Abstract.Tbl_Port");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.Tbl_Port_VlanTagBmp
    ret = ScanTableLongFieldData("g_expectAbstractTblPortVlanTagBmp", "Abstract.Tbl_Port_VlanTagBmp");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.Tbl_Port_VlanUntagBmp
    ret = ScanTableLongFieldData("g_expectAbstractTblPortVlanUntagBmp", "Abstract.Tbl_Port_VlanUntagBmp");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.a_nhp4
    ret = ScanTableData(g_expectabstractanhp4, "Abstract.a_nhp4");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.a_config_if_ipv4_addr
    ret = ScanTableData(g_expectabstractaconfigifipv4addr, "Abstract.a_config_if_ipv4_addr");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.a_radio_name
    ret = ScanTableLongFieldData("g_expectAbstractARadioName", "Abstract.a_radio_name");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.Tbl_Bridge
    ret = ScanTableData(g_expectabstracttblbridge, "Abstract.Tbl_Bridge");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // Abstract.Tbl_Vlan
    ret = ScanTableLongFieldData("g_expectAbstractTblVlan", "Abstract.Tbl_Vlan");
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// ylog表预置数据并对导入的数据进行校验
void PrepareYlogData(int startNum, int dataNum)
{
    // 进入gmdata文件夹
    if (chdir("gmdata") != 0) {
        perror("chdir failed");
        exit(EXIT_FAILURE);
    }
    // 添加权限
    if (system("chmod 777 command.sh") != 0) {
        perror("system failed");
        exit(EXIT_FAILURE);
    }
    // 执行command.sh脚本
    char command[1024] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "./command.sh %s > commandLog.txt", g_testNameSpace);
    if (system(command) != 0) {
        perror("system failed");
        exit(EXIT_FAILURE);
    }
    // 退回当前目录
    if (chdir("..") != 0) {
        perror("chdir failed");
        exit(EXIT_FAILURE);
    }
    // 对导入的数据进行校验
    // 先屏蔽AbstractTableCheck();
    AW_FUN_Log(LOG_STEP, "Ylog import data check success!!!");

    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 对state表预置数据，state字段值设置为1，后续能够触发notify表订阅推送
    ret = writeRecordId(conn, stmt, "state", 1, 2, 1, StateSetId, false);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 写15个输入表
    // Arp一次性批插4000条记录
    ret = writeRecordId(conn, stmt, "Arp.ConfigArp", startNum, dataNum, 1, ConfigArpSetId, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "Arp.ConfigArp prepare data insert success!!!");

    uint32_t oneBatch = 500;
    
    // 预置数据，每次批插500条，直至插满4000条数据，一次性批插4000条，执行全量长稳会导致执行时间过长引起客户端超时
    for (uint32_t start = startNum; start < dataNum; start += oneBatch) {
        // Fib
        ret = writeRecordId(conn, stmt, "Fib.ConfigIpv4Fwd", start, start + oneBatch, 1, ConfigIpv4FwdSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Fib.ConfigNhpGroup", start, start + oneBatch, 1, ConfigNhpGroupSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Fib.ConfigNhpGroupNode", start, start + oneBatch, 1,
            ConfigNhpGroupNodeSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Fib.ConfigNhpBasic", start, start + oneBatch, 1, ConfigNhpBasicSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Fib.ConfigNhpStandard", start, start + oneBatch, 1,
            ConfigNhpStandardSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "Fib prepare data insert success!!!");
        // Ifm
        ret = writeRecordId(conn, stmt, "Ifm.ConfigVapIf", start, start + oneBatch, 1, ConfigVapIfSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Ifm.PublishRadioName", start, start + oneBatch, 1,
            PublishRadioNameSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Ifm.ConfigIf", start, start + oneBatch, 1, ConfigIfSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Ifm.PublishNif", start, start + oneBatch, 1, PublishNifSetId, true);
        if (ret != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = writeRecordId(conn, stmt, "Ifm.ConfigIfIpLearn", start, start + oneBatch, 1, ConfigIfIpLearnSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Ifm.ConfigProtocolEnable", start, start + oneBatch, 1,
            ConfigProtocolEnableSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = writeRecordId(conn, stmt, "Ifm.ConfigLinkUpDownMode", start, start + oneBatch, 1,
            ConfigLinkUpDownModeSetId, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = writeRecordId(conn, stmt, "Ifm.ConfigAttributes", 0, 90, 1, ConfigAttributesSetId, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = writeRecordId(conn, stmt, "Ifm.ConfigIfIpv4Addr", 0, 90, 1, ConfigIfIpv4AddrSetId, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "Ifm prepare data insert success!!!");

    sleep(10);
    AW_FUN_Log(LOG_STEP, "insert ylog datalog table end.");
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 输入表B：随机产生一个【0,5k】数值a，对 【a，a+500】间的数据进行：写、删除
void SingleWriteInputBAndInputLpmThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    C4Int4C1Int1T *objIn1 = (C4Int4C1Int1T *)malloc(sizeof(C4Int4C1Int1T) * writeNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleWriteInputBAndInputLpmThread] objIn1 malloc failed !!!");
        return;
    }
    C3Int4C1Byte16C1Int1T *objIn2 = (C3Int4C1Byte16C1Int1T *)malloc(sizeof(C3Int4C1Byte16C1Int1T) * writeNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleWriteInputBAndInputLpmThread] objIn2 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        return;
    }
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2) + 1;
    // 单写--删除
    ResourceTableDML(conn, stmt, g_inputTableB, index, index + writeNum);
    memset(objIn1, 0, sizeof(C4Int4C1Int1T) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn1[i].id = index + i;
        objIn1[i].vrId = 0;
        objIn1[i].vrfIndex = 0;
        objIn1[i].destIpAddr = index + i;
        objIn1[i].maskLen = 32;
        objIn1[i].dtlReservedCount = 1;
    }
    memset(objIn2, 0, sizeof(C3Int4C1Byte16C1Int1T) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn2[i].id = index + i;
        objIn2[i].vrId = 0;
        objIn2[i].vrfIndex = 0;
        (void)setArryByValue(objIn2[i].destIpAddr, sizeof(objIn2[i].destIpAddr), index + i, U8_MAX, G_MACS);
        objIn2[i].maskLen = 128;
        objIn2[i].dtlReservedCount = 1;
    }
    // lpm4插入数据
    ret = writeRecord(conn, stmt, g_inputLpm4, objIn1, writeNum, NULL, false, true, g_schemaJson13);
    // 利用lpm索引查询数据
    InputLpm4Index1Scan(conn, stmt, g_inputLpm4, index, index + writeNum);
    // lpm6插入数据
    ret = writeRecord(conn, stmt, g_inputLpm6, objIn2, writeNum, NULL, false, true, g_schemaJson14);
    // 删除
    for (int i = 0; i < writeNum; i++) {
        objIn2[i].dtlReservedCount = -1;
    }
    // 主键删除lpm4索引表的数据
    InputLpm4PKDelete(conn, stmt, g_inputLpm4, index, index + writeNum);
    // 删除lpm6索引表的数据
    ret = writeRecord(conn, stmt, g_inputLpm6, objIn2, writeNum, NULL, false, true, g_schemaJson14);
    sleep(ST);

    free(objIn1);
    free(objIn2);
    return;
}

// Arp.ConfigArp表，批写数据
// 主键不冲突，主键冲突，二级索引不冲突，二级索引冲突，count正负都有，每批500条
// 单条主键更新，批量二级索引删除（部分数据的二级索引字段相同）
// 随机产生一个【0,5k】数值a，对 【a，a+500】间的数据进行：插入，更新，删除
void WriteConfigArpThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    uint32_t start2 = 0;
    uint32_t start3 = 0;
    uint32_t start4 = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    start2 = writeNum / 4;
    start3 = start2 * 2;
    start4 = start2 * 3;
    // 统一分配
    ConfigArpT *objIn = (ConfigArpT *)malloc(sizeof(ConfigArpT) * writeNum);
    if (objIn == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[WriteConfigArpThread] objIn malloc failed !!!");
        return;
    }
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2);
    // 批写500条数据
    // 主键不冲突100条，主键冲突100条，二级索引不冲突100条，二级索引冲突100条,count正负都有200条
    // 改成堆变量
    memset(objIn, 0, sizeof(ConfigArpT) * writeNum);
    // 主键索引和二级索引不冲突
    for (int i = 0; i < start2; i++) {   // 100
        objIn[i].addr = index + i;
        objIn[i].ifIndex = index;
        objIn[i].type = 2;
        // 设置过期字段的值为1天
        objIn[i].agingTime = 24 * 60 * 60 * 1000;
        objIn[i].dtlReservedCount = 1;
    }
    // 主键索引冲突
    for (int i = start2; i < start3; i++) {
        objIn[i].addr = index;
        objIn[i].ifIndex = index;
        objIn[i].type = 2;
        objIn[i].agingTime = 24 * 60 * 60 * 1000;
        objIn[i].dtlReservedCount = 1;
    }
    // 二级索引不冲突
    for (int i = start3; i < start4; i++) {    // 100
        objIn[i].addr = index + i;
        objIn[i].ifIndex = index + i;
        objIn[i].agingTime = 24 * 60 * 60 * 1000;
        objIn[i].dtlReservedCount = 1;
    }
    // count有正有负的情况
    for (int i = start4; i < writeNum; i++) {    // 100
        objIn[i].addr = index + i;
        objIn[i].ifIndex = index;
        objIn[i].agingTime = 24 * 60 * 60 * 1000;
        objIn[i].dtlReservedCount = (i % 2 == 0) ? index : -index;
    }
    // 存在1012002的场景，无需校验
    ret = writeRecord(conn, stmt, "Arp.ConfigArp", objIn, writeNum, ConfigArpSet, true);
    // 主键更新
    ConfigArpPKUpdate(conn, stmt, "Arp.ConfigArp", index, index + writeNum);
    // 二级索引删除
    ConfigArpIndex1Delete(conn, stmt, "Arp.ConfigArp", index, index + writeNum);
    sleep(ST);
    free(objIn);
    return;
}
// Arp.ConfigArp Fib.ConfigIpv4Fwd Ifm.ConfigVapIf Ifm.PublishRadioName
// insert, 二级索引更新，主键delete
void SingleStructWriteThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    // 统一分配内存
    ConfigArpT *objIn1 = (ConfigArpT *)malloc(sizeof(ConfigArpT) * writeNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleStructWriteThread] objIn1 malloc failed !!!");
        return;
    }
    ConfigIpv4FwdT *objIn2 = (ConfigIpv4FwdT *)malloc(sizeof(ConfigIpv4FwdT) * writeNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleStructWriteThread] objIn2 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        return;
    }
    ConfigVapIfT *objIn3 = (ConfigVapIfT *)malloc(sizeof(ConfigVapIfT) * writeNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleStructWriteThread] objIn3 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        if (objIn2) {
            free(objIn2);
        }
        return;
    }
    PublishRadioNameT *objIn4 = (PublishRadioNameT *)malloc(sizeof(PublishRadioNameT) * writeNum);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleStructWriteThread] objIn4 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        if (objIn2) {
            free(objIn2);
        }
        if (objIn3) {
            free(objIn3);
        }
        return;
    }
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2);
    memset(objIn1, 0, sizeof(ConfigArpT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn1[i].addr = index + i;
        objIn1[i].ifIndex = index;
        objIn1[i].type = 2;
        // 设置过期字段的值为1天
        objIn1[i].agingTime = 24 * 60 * 60 * 1000;
        objIn1[i].dtlReservedCount = 1;
    }
    // 写Arp.ConfigArp表，单写结构化
    ret = writeRecord(conn, stmt, "Arp.ConfigArp", objIn1, writeNum, NULL, false, true, g_schemaJson1);
    // 二级索引更新
    ConfigArpIndex1Update(conn, stmt, "Arp.ConfigArp", index);
    // 主键删除
    ConfigArpPKDelete(conn, stmt, "Arp.ConfigArp", index, index + writeNum);
    memset(objIn2, 0, sizeof(ConfigIpv4FwdT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn2[i].nsId = index + i;
        objIn2[i].vrfId = index + i;
        objIn2[i].dstIp = index;
        objIn2[i].maskLen = 24;
        objIn2[i].dtlReservedCount = 1;
    }
    // 写Fib.ConfigIpv4Fwd表,单写结构化
    ret = writeRecord(conn, stmt, "Fib.ConfigIpv4Fwd", objIn2, writeNum,  NULL, false, true, g_schemaJson2);
    // 主键更新
    ConfigIpv4FwdPKUpdate(conn, stmt, "Fib.ConfigIpv4Fwd", index, index + writeNum);
    // 主键删除
    ConfigIpv4FwdPKDelete(conn, stmt, "Fib.ConfigIpv4Fwd", index, index + writeNum);
    memset(objIn3, 0, sizeof(ConfigVapIfT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn3[i].nsId = index;
        objIn3[i].ifIndex = index + i;
        objIn3[i].adminState = 1;
        objIn3[i].dtlReservedCount = 1;
    }
    // 写Ifm.ConfigVapIf表，单写结构化
    ret = writeRecord(conn, stmt, "Ifm.ConfigVapIf", objIn3, writeNum,  NULL, false, true, g_schemaJson3);
    // 主键更新
    ConfigVapIfPKUpdate(conn, stmt, "Ifm.ConfigVapIf", index, index + writeNum);

    // 主键删除
    ConfigVapIfPKDelete(conn, stmt, "Ifm.ConfigVapIf", index, index + writeNum);
    // Ifm.PublishRadioName
    memset(objIn4, 0, sizeof(PublishRadioNameT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        (void)setArryByValue(objIn4[i].radioName, sizeof(objIn4[i].radioName), index + i, U8_MAX, G_MACS);
        objIn4[i].tp = 1;
        objIn4[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Ifm.PublishRadioName", objIn4, writeNum, NULL, false, true, g_schemaJson4);
    // 主键更新
    PublishRadioNamePKUpdate(conn, stmt, "Ifm.PublishRadioName", index, index + writeNum);
    // 主键删除
    PublishRadioNamePKDelete(conn, stmt, "Ifm.PublishRadioName", index, index + writeNum);
    sleep(ST);
    free(objIn1);
    free(objIn2);
    free(objIn3);
    free(objIn4);
    return;
}
// Arp.ConfigArp Fib.ConfigNhpGroup Ifm.ConfigIf
// 批量结构化操作（每批600条数据）所有表，每批数据中跨所有表操作，有insert，又有主键delete
// 将Ifm.PublishNif拿出去做客户端异常退出
void BatchStructWriteThread()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = DATALOG_NUM * 2;
    uint32_t writeNum = 0;
    uint32_t tableRecordCount = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    tableRecordCount = writeNum / 2;
    ConfigArpT *objIn1 = (ConfigArpT *)malloc(sizeof(ConfigArpT) * tableRecordCount);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchStructWriteThread] objIn1 malloc failed !!!");
        return;
    }
    ConfigIfT *objIn2 = (ConfigIfT *)malloc(sizeof(ConfigIfT) * tableRecordCount);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchStructWriteThread] objIn2 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        return;
    }
    PortAttrChgT *objIn3 = (PortAttrChgT *)malloc(sizeof(PortAttrChgT) * writeNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchStructWriteThread] objIn3 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        if (objIn2) {
            free(objIn2);
        }
        return;
    }
    EthTrunkT *objIn4 = (EthTrunkT *)malloc(sizeof(EthTrunkT) * writeNum);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchStructWriteThread] objIn4 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        if (objIn2) {
            free(objIn2);
        }
        if (objIn3) {
            free(objIn3);
        }
        return;
    }
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    uint16_t srvMemLimitVal = rand() % 1023 + 2;    // 2到1024
    // 设置连接级，动态内存上限【2,1024】
    YangConnOptionT connOptions = {0};
    connOptions.srvMemCtxLimit = srvMemLimitVal;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 排除18、19、20这三个值，避免join执行时间过长
    int index = rand() % (countN / 2) + 600;
    // 批量结构化含多个表，同时含insert和delete
    // Arp.ConfigArp先插入tableRecordCount条记录
    memset(objIn1, 0, sizeof(ConfigArpT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {   // tableRecordCount
        objIn1[i].addr = index + i;
        objIn1[i].ifIndex = index;
        objIn1[i].type = 2;
        // 设置过期字段的值为1天
        objIn1[i].agingTime = 24 * 60 * 60 * 1000;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Arp.ConfigArp", objIn1, tableRecordCount, ConfigArpSet, true);
    // Ifm.ConfigIf先插入200条记录
    memset(objIn2, 0, sizeof(ConfigIfT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {
        objIn2[i].nsId = index + i;
        objIn2[i].ifIndex = index + i;
        (void)setArryByValue(objIn2[i].ifName, sizeof(objIn2[i].ifName), index + i, U8_MAX, G_MACS);
        objIn2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Ifm.ConfigIf", objIn2, tableRecordCount, ConfigIfSet, true);
    if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY) {
        AW_FUN_Log(LOG_STEP, "Ifm.ConfigIf Write failure, ret is %d", ret);
    }
    // 触发订阅推送
    // 写Ifm.PortAttrChg表、Ifm.EthThrunk表
    // 对Ifm.PortAttrChg表写入数据（500条记录）
    memset(objIn3, 0, sizeof(PortAttrChgT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn3[i].ifIndex = 18;     // 与导入的数据能够产生join数据
        objIn3[i].attr = -128 + i;
        objIn3[i].value = index + i;
        objIn3[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Ifm.PortAttrChg", objIn3, writeNum, PortAttrChgSet, true);
    if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY) {
        AW_FUN_Log(LOG_STEP, "Ifm.PortAttrChg Write failure, ret is %d", ret);
    }
    // 对Ifm.EthTrunk表写入500条记录
    memset(objIn4, 0, sizeof(EthTrunkT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn4[i].ifIndex = index + i;
        objIn4[i].nsId = index;
        objIn4[i].trunkId = index + i;
        objIn4[i].domain = 1;
        objIn4[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Ifm.EthTrunk", objIn4, writeNum, EthTrunkSet, true);
    if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY) {
        AW_FUN_Log(LOG_STEP, "Ifm.EthTrunk Write failure, ret is %d", ret);
    }
    // 删除写入的数据
    // 主键删除Ifm.PortAttrChg表插入的数据
    PortAttrChgPKDelete(conn, stmt, "Ifm.PortAttrChg", 0, writeNum);
    // 主键删除Ifm.EthTrunk表插入的数据
    EthTrunkPKDelete(conn, stmt, "Ifm.EthTrunk", index, index + writeNum);
    // 批量结构化写和删
    WriteAndDelete(conn, stmt, index, tableRecordCount);

    sleep(ST);
    free(objIn1);
    free(objIn2);
    free(objIn3);
    free(objIn4);

    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return;
}
// Arp.ConfigArp Fib.ConfigNhpGroupNode Ifm.ConfigAttributes Ifm.ConfigIfIpv4Addr
// 单条非结构化操作所有表, insert, 主键update, 二级索引delete
void SingleNoStructWriteThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    ConfigArpT *objIn1 = (ConfigArpT *)malloc(sizeof(ConfigArpT) * writeNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleNoStructWriteThread] objIn1 malloc failed !!!");
        return;
    }
    ConfigNhpGroupNodeT *objIn2 = (ConfigNhpGroupNodeT *)malloc(sizeof(ConfigNhpGroupNodeT) * writeNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleNoStructWriteThread] objIn2 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        return;
    }
    ConfigAttributesT *objIn3 = (ConfigAttributesT *)malloc(sizeof(ConfigAttributesT) * writeNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleNoStructWriteThread] objIn3 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        if (objIn2) {
            free(objIn2);
        }
        return;
    }
    ConfigIfIpv4AddrT *objIn4 = (ConfigIfIpv4AddrT *)malloc(sizeof(ConfigIfIpv4AddrT) * 100);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleNoStructWriteThread] objIn4 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        if (objIn2) {
            free(objIn2);
        }
        if (objIn3) {
            free(objIn3);
        }
        return;
    }
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2) + 21;
    // Arp.ConfigArp非结构化单写500条记录
    memset(objIn1, 0, sizeof(ConfigArpT) * writeNum);
    for (int i = 0; i < writeNum; i++) {   // 100
        objIn1[i].addr = index + i;
        objIn1[i].ifIndex = index;
        objIn1[i].type = 2;
        // 设置过期字段的值为1天
        objIn1[i].agingTime = 24 * 60 * 60 * 1000;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Arp.ConfigArp", objIn1, writeNum, ConfigArpSet, false);
    // 主键更新
    ConfigArpPKUpdate(conn, stmt, "Arp.ConfigArp", index, index + writeNum);
    // 二级索引删除
    ConfigArpIndex1Delete(conn, stmt, "Arp.ConfigArp", index, index + writeNum);
    // Fib.ConfigNhpGroupNode
    memset(objIn2, 0, sizeof(ConfigNhpGroupNodeT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn2[i].nsId = index + i;
        objIn2[i].nhpGroupId = index;
        objIn2[i].attributeId = index;
        objIn2[i].primaryNhpId = index;
        objIn2[i].primaryLabel = index;
        objIn2[i].backupNhpId = index;
        objIn2[i].backupLabel = index;
        objIn2[i].vrfId = 1;
        objIn2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Fib.ConfigNhpGroupNode", objIn2, writeNum,
        ConfigNhpGroupNodeSet, false);
    // 主键更新
    ConfigNhpGroupNodePKUpdate(conn, stmt, "Fib.ConfigNhpGroupNode", index, index + writeNum);
    // 二级索引删除
    ConfigNhpGroupNodeIndex1Delete(conn, stmt, "Fib.ConfigNhpGroupNode", index, index + writeNum);
    // Ifm.ConfigAttributes
    memset(objIn3, 0, sizeof(ConfigAttributesT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn3[i].nsId = index + i;
        // ifIndex写死成6500
        objIn3[i].ifIndex = 6500;
        objIn3[i].mtu = 1;
        objIn3[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Ifm.ConfigAttributes", objIn3, writeNum, ConfigAttributesSet, true);
    // 主键更新
    ConfigAttributesPKUpdate(conn, stmt, "Ifm.ConfigAttributes", index, index + writeNum);
    // 主键删除
    ConfigAttributesPKDelete(conn, stmt, "Ifm.ConfigAttributes", index, index + writeNum);
    // Ifm.ConfigIfIpv4Addr, 涉及固定资源表大小100
    memset(objIn4, 0, sizeof(ConfigIfIpv4AddrT) * 100);
    for (int i = 0; i < 100; i++) {
        objIn4[i].ifIndex = index + i;
        objIn4[i].vrfIndex = index;
        objIn4[i].address = index;
        objIn4[i].maskLen = 32;
        objIn4[i].type = 1;
        objIn4[i].dtlReservedCount = 1;
    }
    // 超过资源表上限报1009021, 需要对齐怎么改
    ret = writeRecord(conn, stmt, "Ifm.ConfigIfIpv4Addr", objIn4, 100, ConfigIfIpv4AddrSet, false);
    // 主键更新,主键索引全字段，更新主键的值
    ConfigIfIpv4AddrPKUpdate(conn, stmt, "Ifm.ConfigIfIpv4Addr", index, index + 100);
    // 二级索引删除
    ConfigIfIpv4AddrIndex1Delete(conn, stmt, "Ifm.ConfigIfIpv4Addr", index, index + 100);
    sleep(ST);
    free(objIn1);
    free(objIn2);
    free(objIn3);
    free(objIn4);
    return;
}
// 批量非结构化操作(800条数据)，含插入和删除
// Arp.ConfigArp Fib.ConfigNhpBasic Ifm.ConfigIfIpLearn Ifm.ConfigProtocolEnable
void BatchNoStructWriteThread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    uint32_t tableRecordCount = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    tableRecordCount = writeNum / 2;
    ConfigArpT *objIn1 = (ConfigArpT *)malloc(sizeof(ConfigArpT) * tableRecordCount);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchNoStructWriteThread] objIn1 malloc failed !!!");
        return;
    }
    ConfigIfIpLearnT *objIn2 = (ConfigIfIpLearnT *)malloc(sizeof(ConfigIfIpLearnT) * tableRecordCount);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchNoStructWriteThread] objIn2 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
    }
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2);
    // 批量结构化含多个表，同时含insert和delete
    // Arp.ConfigArp先插入tableRecordCount条记录
    memset(objIn1, 0, sizeof(ConfigArpT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {   // tableRecordCount
        objIn1[i].addr = index + i;
        objIn1[i].ifIndex = index;
        objIn1[i].type = 2;
        // 设置过期字段的值为1天
        objIn1[i].agingTime = 24 * 60 * 60 * 1000;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Arp.ConfigArp", objIn1, tableRecordCount, ConfigArpSet, true);
    // Ifm.ConfigIfIpLearn先插入200条记录
    memset(objIn2, 0, sizeof(ConfigIfIpLearnT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {
        objIn2[i].ifIndex = index + i;
        objIn2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Ifm.ConfigIfIpLearn", objIn2, tableRecordCount, ConfigIfIpLearnSet, true);
    // 批量非结构化结构化写和删
    WriteAndDeleteNoSturct(conn, stmt, index, tableRecordCount);
    sleep(ST);
    free(objIn1);
    free(objIn2);
    return;
}
// 单条结构化操作
// Arp.ConfigArp Fib.ConfigNhpStandard Ifm.ConfigLinkUpDownMode
void SingleStructWrite1Thread(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    ConfigArpT *objIn1 = (ConfigArpT *)malloc(sizeof(ConfigArpT) * writeNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleStructWrite1Thread] objIn1 malloc failed !!!");
        return;
    }
    ConfigNhpStandardT *objIn2 = (ConfigNhpStandardT *)malloc(sizeof(ConfigNhpStandardT) * writeNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleStructWrite1Thread] objIn2 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        return;
    }
    ConfigLinkUpDownModeT *objIn3 = (ConfigLinkUpDownModeT *)malloc(sizeof(ConfigLinkUpDownModeT) * writeNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[SingleStructWrite1Thread] objIn3 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        if (objIn2) {
            free(objIn2);
        }
        return;
    }
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2);
    
    memset(objIn1, 0, sizeof(ConfigArpT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn1[i].addr = index + i;
        objIn1[i].ifIndex = index;
        objIn1[i].type = 2;
        // 设置过期字段的值为1天
        objIn1[i].agingTime = 24 * 60 * 60 * 1000;
        objIn1[i].dtlReservedCount = 1;
    }
    // 写Arp.ConfigArp表，单写结构化
    ret = writeRecord(conn, stmt, "Arp.ConfigArp", objIn1, writeNum, NULL, false, true, g_schemaJson1);
    // 二级索引更新
    ConfigArpIndex1Update(conn, stmt, "Arp.ConfigArp", index);
    // 主键删除
    ConfigArpPKDelete(conn, stmt, "Arp.ConfigArp", index, index + writeNum);

    memset(objIn2, 0, sizeof(ConfigNhpStandardT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn2[i].nhpIndex = index + i;
        objIn2[i].nextHop = index + i;
        objIn2[i].outIfIndex = index;
        objIn2[i].iidFlags = 1;
        objIn2[i].dtlReservedCount = 1;
    }
    // 写Fib.ConfigNhpStandard表,单写结构化
    ret = writeRecord(conn, stmt, "Fib.ConfigNhpStandard", objIn2, writeNum, NULL, false, true, g_schemaJson9);
    // 二级索引更新
    ConfigNhpStandardIndex1Update(conn, stmt, "Fib.ConfigNhpStandard", index, index + writeNum);
    // 主键删除
    ConfigNhpStandardPKDelete(conn, stmt, "Fib.ConfigNhpStandard", index, index + writeNum);

    memset(objIn3, 0, sizeof(ConfigLinkUpDownModeT) * writeNum);
    for (int i = 0; i < writeNum; i++) {
        objIn3[i].ifIndex = index + i;
        objIn3[i].mode = 1;
        objIn3[i].dtlReservedCount = 1;
    }
    // 写Ifm.ConfigLinkUpDownMode表，单写结构化
    ret = writeRecord(conn, stmt, "Ifm.ConfigLinkUpDownMode", objIn3, writeNum, NULL, false, true, g_schemaJson10);
    // 主键更新
    ConfigLinkUpDownModePKUpdate(conn, stmt, "Ifm.ConfigLinkUpDownMode", index, index + writeNum);
    // 主键删除
    ConfigLinkUpDownModePKDelete(conn, stmt, "Ifm.ConfigLinkUpDownMode", index, index + writeNum);
    sleep(ST);
    free(objIn1);
    free(objIn2);
    free(objIn3);
    return;
}
// 订阅 + 取消订阅 + 查询
// notify表Arp.AgingDetectMessage LswOda.PortAttrChg、LswOda.EthTrunk、LswOda.EthTrunkMembers
// 外部表arp if_link if_route vlan
void *SubscribeAndScanThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 外部表使用
    GmcConnT *conn_extern = NULL;
    GmcStmtT *stmt_extern = NULL;
    // 创建同步连接
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[1].userEpollFd;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建外部表使用的同步连接
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[3].userEpollFd;
    ret = TestYangGmcConnect(&conn_extern, &stmt_extern, GMC_CONN_TYPE_SYNC, &connOptions2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt_extern, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅连接
    int chanRingLen = 256;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    GmcConnT *conn_externsub = NULL;
    GmcStmtT *stmt_externsub = NULL;
    const char *subConnName = "subYlogConnName";
    const char *subExternConnName = "subYlogExternConnName";
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&conn_sub, &stmt_sub, GMC_CONN_TYPE_SUB, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建外部表订阅连接
    connOptions2.connName = subExternConnName;
    ret = TestYangGmcConnect(&conn_externsub, &stmt_externsub, GMC_CONN_TYPE_SUB, &connOptions2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 下面三个notify订阅之后，不再取消订阅
    // Arp.AgingDetectMessage, LswOda.PortAttrChg, LswOda.EthTrunk
    SnUserDataT *userData01 = NULL;
    SnUserDataT *userData02 = NULL;
    SnUserDataT *userData03 = NULL;
    SnUserDataT *userData05 = NULL;
    SnUserDataT *userData06 = NULL;
    SnUserDataT *userData07 = NULL;
    SnUserDataT *userData08 = NULL;
    ret = testSnMallocUserData(&userData01, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData02, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData03, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData05, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData06, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData07, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData08, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅关系
    const char *subName01 = "macagingtime";
    const char *subName02 = "portattrchg";
    const char *subName03 = "ethtrunk";
    // Arp.ConfigArp_To_HPF再单独创建1个订阅连接，只在euler跑
    // 2023年8月15日，H2D将notify表更新为Arp.AgingDetectMessage
    // 2023年11月11日，H2D将Ifm业务的notify表更新为LswOda.PortAttrChg、LswOda.EthTrunk、
    // LswOda.EthTrunkMembers
    // 2024年6月1日，H2D对应的Arp.AgingDetectMessage表删除，适配改成LswOda.MacAgingTime表
    testSubscribeYlogNotifyTable(stmt, conn_sub, subName01, subName02, subName03,
        userData01, userData02, userData03);
    int i = 0;
    while (1) {
        THREAD_CONTROL_POINT();
        // arp if_link if_route if_eth_trunk_member
        // 创建订阅关系
        const char *subName05 = "arp";
        const char *subName06 = "iflink";
        const char *subName07 = "ifroute";
        const char *subName08 = "ifethtrunkmember";
        testSubscribeYlogExternalTable(stmt_extern, conn_externsub, subName05, subName06, subName07, subName08,
            userData05, userData06, userData07, userData08);
        sleep(100);

        // external表
        testCancelSubYlogExternalTable(stmt_extern, subName05, subName06, subName07, subName08);
        
        // 全表扫描，主键扫描，二级索引扫描，走串行化直连读
        // 输入表
        // Arp.ConfigArp
        ConfigArpFullScan(conn, stmt, "Arp.ConfigArp");
        ConfigArpPKScan(conn, stmt, "Arp.ConfigArp", -1067162523, 10001);
        ConfigArpIndex1Scan(conn, stmt, "Arp.ConfigArp", 10001);
        // Ifm.ConfigIfIpv4Addr
        ConfigIfIpv4AddrFullScan(conn, stmt, "Ifm.ConfigIfIpv4Addr");
        ConfigIfIpv4AddrPKScan(conn, stmt, "Ifm.ConfigIfIpv4Addr", 5, 1, 2, 32, 0);
        ConfigIfIpv4AddrIndex1Scan(conn, stmt, "Ifm.ConfigIfIpv4Addr", 0);
        // Ifm.ConfigAttributes
        ConfigAttributesFullScan(conn, stmt, "Ifm.ConfigAttributes");
        ConfigAttributesPKScan(conn, stmt, "Ifm.ConfigAttributes", 0, 106);

        // 外部表, 走RC事务直连读
        // if_eth_trunk_member
        ifEthTrunkMemberFullScan(conn, stmt, "if_eth_trunk_member");
        // 主键范围查询
        ifEthTrunkMemberPKScan(conn, stmt, "if_eth_trunk_member", 0, 1000);
        // if_link
        iflinkFullScan(conn, stmt, "if_link");
        iflinkPKScan(conn, stmt, "if_link", 9000);
        // if_route
        ifrouteFullScan(conn, stmt, "if_route");
        ifroutePKScan(conn, stmt, "if_route", 9000);
        
        // kv表，走RC事务直连读
        capV5KVTableScan(conn, stmt, g_tableName);
        sleep(ST);
    }
    testSnFreeUserData(userData01);
    userData01 = NULL;
    testSnFreeUserData(userData02);
    userData02 = NULL;
    testSnFreeUserData(userData03);
    userData03 = NULL;
    testSnFreeUserData(userData05);
    userData05 = NULL;
    testSnFreeUserData(userData06);
    userData06 = NULL;
    testSnFreeUserData(userData07);
    userData07 = NULL;
    testSnFreeUserData(userData08);
    userData08 = NULL;
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// 订阅外部表，取消订阅，查询输入表和外部表
void *ScanInputAndExternalTableThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[4].userEpollFd;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅连接
    int chanRingLen = 256;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    const char *subConnName = "subYlogConnName02";
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&conn_sub, &stmt_sub, GMC_CONN_TYPE_SUB, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    SnUserDataT *userData05 = NULL;
    SnUserDataT *userData06 = NULL;
    SnUserDataT *userData07 = NULL;
    SnUserDataT *userData08 = NULL;
    ret = testSnMallocUserData(&userData05, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData06, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData07, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData08, 6 * 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    while (1) {
        THREAD_CONTROL_POINT();
        // 添加外部表订阅
        // arp if_link if_route if_eth_trunk_member
        
        // 创建订阅关系
        const char *subName05 = "arp02";
        const char *subName06 = "iflink02";
        const char *subName07 = "ifroute02";
        const char *subName08 = "ifethtrunkmember02";
        testSubscribeYlogExternalTable(stmt, conn_sub, subName05, subName06, subName07, subName08,
            userData05, userData06, userData07, userData08);
        sleep(100);
        // external表
        testCancelSubYlogExternalTable(stmt, subName05, subName06, subName07, subName08);

        // 全表扫描，主键扫描，二级索引扫描，走串行化直连读
        // 输入表
        // Arp.ConfigArp
        ConfigArpFullScan(conn, stmt, "Arp.ConfigArp");
        ConfigArpPKScan(conn, stmt, "Arp.ConfigArp", -1067162523, 10001);
        ConfigArpIndex1Scan(conn, stmt, "Arp.ConfigArp", 10001);
        // Ifm.ConfigIfIpv4Addr
        ConfigIfIpv4AddrFullScan(conn, stmt, "Ifm.ConfigIfIpv4Addr");
        ConfigIfIpv4AddrPKScan(conn, stmt, "Ifm.ConfigIfIpv4Addr", 5, 1, 2, 32, 0);
        ConfigIfIpv4AddrIndex1Scan(conn, stmt, "Ifm.ConfigIfIpv4Addr", 0);
        // Ifm.ConfigAttributes
        ConfigAttributesFullScan(conn, stmt, "Ifm.ConfigAttributes");
        ConfigAttributesPKScan(conn, stmt, "Ifm.ConfigAttributes", 0, 106);

        // 外部表, 走RC事务直连读
        // if_eth_trunk_member
        ifEthTrunkMemberFullScan(conn, stmt, "if_eth_trunk_member");
        // 主键范围查询
        ifEthTrunkMemberPKScan(conn, stmt, "if_eth_trunk_member", 0, 1000);
        // if_link
        iflinkFullScan(conn, stmt, "if_link");
        iflinkPKScan(conn, stmt, "if_link", 9000);
        // if_route
        ifrouteFullScan(conn, stmt, "if_route");
        ifroutePKScan(conn, stmt, "if_route", 9000);
        
        // kv表，走RC事务直连读
        capV5KVTableScan(conn, stmt, g_tableName);
        sleep(ST);
    }
    testSnFreeUserData(userData05);
    userData05 = NULL;
    testSnFreeUserData(userData06);
    userData06 = NULL;
    testSnFreeUserData(userData07);
    userData07 = NULL;
    testSnFreeUserData(userData08);
    userData08 = NULL;
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 批写Acl业务4个输入表(1000条数据)
// Acl.AdvRule(1000), Acl.TimeRangeCfg(1000), Acl.PortPoolCfg(1000), Acl.IpPoolCfg(1000)
void BatchWriteAclTable(GmcConnT *conn, GmcStmtT *stmt, uint32_t writeRecordNum)
{
    int ret = 0;
    uint32_t countN = DATALOG_NUM * 2;
    uint32_t writeNum = writeRecordNum;
    // 临时使用该变量，后面再统一使用writeNum变量进行适配
    uint32_t writeAclTableNum = 1000;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2);
    TimeRangeCfgT *objIn1 = (TimeRangeCfgT *)malloc(sizeof(TimeRangeCfgT) * writeAclTableNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteAclTable]objIn1 malloc failed !!!");
        return;
    }
    memset(objIn1, 0, sizeof(TimeRangeCfgT) * writeAclTableNum);
    for (int i = 0; i < writeAclTableNum; i++) {
        objIn1[i].trngId = index + i;     // 该字段值需要与Acl.AdvRule表中的trngId字段值一致
        objIn1[i].vrId = index;
        objIn1[i].trngInnerId = 1;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Acl.TimeRangeCfg", objIn1, writeAclTableNum, TimeRangeCfgSet, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(objIn1);
    
    PortPoolCfgT *objIn2 = (PortPoolCfgT *)malloc(sizeof(PortPoolCfgT) * writeAclTableNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteAclTable]objIn2 malloc failed !!!");
        return;
    }
    memset(objIn2, 0, sizeof(PortPoolCfgT) * writeAclTableNum);
    // PortPoolCfg表中poolId与AclRule表中srcPortPool和dstPortPool的字段一致；rangeOp设为3
    for (int i = 0; i < writeAclTableNum; i++) {
        objIn2[i].vrId = index + i;
        objIn2[i].poolId = index + i;
        objIn2[i].portId = index;
        objIn2[i].rangeOp = 3;
        // 更新该字段
        objIn2[i].startPort = 10;
        objIn2[i].endPort = 10;
        objIn2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Acl.PortPoolCfg", objIn2, writeAclTableNum, PortPoolCfgSet, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(objIn2);

    IpPoolCfgT *objIn3 = (IpPoolCfgT *)malloc(sizeof(IpPoolCfgT) * writeAclTableNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteAclTable]objIn3 malloc failed !!!");
        return;
    }
    memset(objIn3, 0, sizeof(IpPoolCfgT) * writeAclTableNum);
    // IpPoolCfg表中poolId与AclRule表中srcIpPool和dstIpPool字段一致；
    // aclCondMask该字段第22位和23位值设置为1(6,291,456，  16进制0x60 0000)
    for (int i = 0; i < writeAclTableNum; i++) {
        objIn3[i].vrId = index + i;
        objIn3[i].poolId = index + i;
        objIn3[i].ipId = index;
        objIn3[i].ipAddr = 1;
        objIn3[i].ipMask = 2;
        objIn3[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Acl.IpPoolCfg", objIn3, writeAclTableNum, IpPoolCfgSet, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(objIn3);

    AdvRuleT *objIn4 = (AdvRuleT *)malloc(sizeof(AdvRuleT) * writeAclTableNum);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteAclTable]objIn4 malloc failed !!!");
        return;
    }
    memset(objIn4, 0, sizeof(AdvRuleT) * writeAclTableNum);
    for (int i = 0; i < writeAclTableNum; i++) {
        // 主键索引（aclIndex,aclVrId,aclGroupId）,二级索引（aclVrId, aclGroupId）
        objIn4[i].aclIndex = index + i;
        objIn4[i].aclVrId = index;
        objIn4[i].aclGroupId = index + i;
        objIn4[i].trngId = index + i;
        objIn4[i].srcPortPool = index + i;
        objIn4[i].dstPortPool = index + i;
        objIn4[i].srcIpPool = index + i;
        objIn4[i].dstIpPool = index + i;
        objIn4[i].aclCondMask = 0xC00000;        // 第23位和第24位为1  === 0xC00000
        objIn4[i].tableType = 2;                  // 非主键值
        objIn4[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "Acl.AdvRule", objIn4, writeAclTableNum, AdvRuleSet, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(objIn4);

    // Acl.TimeRangeCfg主键更新，主键删除
    TimeRangeCfgPKUpdate(conn, stmt, "Acl.TimeRangeCfg", index, index + writeAclTableNum);
    TimeRangeCfgPKDelete(conn, stmt, "Acl.TimeRangeCfg", index, index + writeAclTableNum);

    // Acl.PortPoolCfg主键更新，主键删除
    PortPoolCfgPKUpdate(conn, stmt, "Acl.PortPoolCfg", index, index + writeAclTableNum);
    PortPoolCfgPKDelete(conn, stmt, "Acl.PortPoolCfg", index, index + writeAclTableNum);

    // Acl.IpPoolCfg主键更新，主键删除
    IpPoolCfgPKUpdate(conn, stmt, "Acl.IpPoolCfg", index, index + writeAclTableNum);
    IpPoolCfgPKDelete(conn, stmt, "Acl.IpPoolCfg", index, index + writeAclTableNum);

    // Acl.AdvRule主键更新，二级索引删除
    AdvRulePKUpdate(conn, stmt, "Acl.AdvRule", index, index + writeAclTableNum);
    AdvRuleIndex1Delete(conn, stmt, "Acl.AdvRule", index, index + writeAclTableNum);
    sleep(ST);
}

// 批写BR业务7个输入表(4000条数据)
// BR.Bridge, BR.BridgeMacAttr   4000条数据
// BR.Port, BR.PortIsolateGrp, BR.PortMacAttr, BR.PortSecurity   512条数据
// BR.VlanTagPort   4000条数据
void BatchWriteBRTable(GmcConnT *conn, GmcStmtT *stmt, uint32_t writeRecordNum)
{
    int ret = 0;
    uint32_t countN = DATALOG_NUM * 2;
    uint32_t writeNum = writeRecordNum;
    // 临时使用该变量，后面再统一使用writeNum变量进行适配
    uint32_t writeBRTableNum = 4000;
    uint32_t writePortReleatedTableNum = 512;
    int bridgeUsedCnt = 0;
    int bridgeRemainCnt = 0;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2);
    // BR.Bridge, BR.BridgeMacAttr
    BridgeT *objIn1 = (BridgeT *)malloc(sizeof(BridgeT) * writeBRTableNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteBRTable]objIn4 malloc failed !!!");
        return;
    }
    memset(objIn1, 0, sizeof(BridgeT) * writeBRTableNum);
    for (int i = 0; i < writeBRTableNum; i++) {
        objIn1[i].nsId = index + i;
        objIn1[i].brId = index + i;
        (void)setArryByValue(objIn1[i].brName, sizeof(objIn1[i].brName), i, U8_MAX, G_MACS);
        objIn1[i].brIfIndex = 1;                // 非主键字段
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "BR.Bridge", objIn1, writeBRTableNum, BridgeSet, true);
    sleep(ST);
    free(objIn1);
    // 先获取外部表bridge表的数目
    ret = TestGetTableCount(&bridgeUsedCnt, "bridge");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    bridgeRemainCnt = BRIDGE_MAX_SIZE - bridgeUsedCnt;
    BridgeMacAttrT *objIn2 = (BridgeMacAttrT *)malloc(sizeof(BridgeMacAttrT) * bridgeRemainCnt);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteBRTable]objIn2 malloc failed !!!");
        return;
    }
    memset(objIn2, 0, sizeof(BridgeMacAttrT) * bridgeRemainCnt);
    // ns_id和br_id字段值与BR.Bridge表中的字段值一致
    for (int i = 0; i < bridgeRemainCnt; i++) {
        objIn2[i].nsId = index + i;
        objIn2[i].brId = index + i;
        objIn2[i].macAgeTime = index;
        // 更新该字段
        objIn2[i].macLimitNum = 10;
        objIn2[i].dtlReservedCount = 1;
    }
    // 由于bridge外部表的限制，bridge最多产生256条数据，写入的数据对外部表可能产生主键冲突（概率极低）
    ret = writeRecord(conn, stmt, "BR.BridgeMacAttr", objIn2, bridgeRemainCnt, BridgeMacAttrSet, true);
    sleep(ST);
    free(objIn2);

    // BR.Bridge主键更新，主键删除
    BridgePKUpdate(conn, stmt, "BR.Bridge", index, index + writeBRTableNum);
    sleep(ST);
    BridgePKDelete(conn, stmt, "BR.Bridge", index, index + writeBRTableNum);

    // BR.BridgeMacAttr主键更新，主键删除
    BridgeMacAttrPKUpdate(conn, stmt, "BR.BridgeMacAttr", index, index + bridgeRemainCnt);
    sleep(ST);
    BridgeMacAttrPKDelete(conn, stmt, "BR.BridgeMacAttr", index, index + bridgeRemainCnt);

    // BR.Port, BR.PortIsolateGrp, BR.PortMacAttr, BR.PortSecurity
    // BR.PortIsolateGrp批写512条，规则产生的BR.PortIfPortIf和BR.PortPort数据量为256
    PortIsolateGrpT *objIn3 = (PortIsolateGrpT *)malloc(sizeof(PortIsolateGrpT) * writePortReleatedTableNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteBRTable]objIn3 malloc failed !!!");
        return;
    }
    memset(objIn3, 0, sizeof(PortIsolateGrpT) * writePortReleatedTableNum);
    for (int i = 0; i < writePortReleatedTableNum; i++) {
        objIn3[i].ifIndex = index + i;
        objIn3[i].portIndex = index + i;
        objIn3[i].grp= -128 + i;
        objIn3[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "BR.PortIsolateGrp", objIn3, writePortReleatedTableNum, PortIsolateGrpSet, true);
    free(objIn3);
    PortMacAttrT *objIn4 = (PortMacAttrT *)malloc(sizeof(PortMacAttrT) * writePortReleatedTableNum);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteBRTable]objIn4 malloc failed !!!");
        return;
    }
    memset(objIn4, 0, sizeof(PortMacAttrT) * writePortReleatedTableNum);
    for (int i = 0; i < writePortReleatedTableNum; i++) {
        objIn4[i].ifIndex = index + i;
        // 非主键字段
        objIn4[i].portIndex = index + i;
        objIn4[i].limit = 1;
        objIn4[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "BR.PortMacAttr", objIn4, writePortReleatedTableNum, PortMacAttrSet, true);
    free(objIn4);
    PortSecurityT *objIn5 = (PortSecurityT *)malloc(sizeof(PortSecurityT) * writePortReleatedTableNum);
    if (objIn5 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteBRTable]objIn5 malloc failed !!!");
        return;
    }
    memset(objIn5, 0, sizeof(PortSecurityT) * writePortReleatedTableNum);
    for (int i = 0; i < writePortReleatedTableNum; i++) {
        objIn5[i].ifIndex = index + i;
        // 非主键字段
        objIn5[i].portIndex = index + i;
        objIn5[i].macSecMax = 1;
        objIn5[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "BR.PortSecurity", objIn5, writePortReleatedTableNum, PortSecuritySet, true);
    free(objIn5);
    PortT *objIn6 = (PortT *)malloc(sizeof(PortT) * writePortReleatedTableNum);
    if (objIn6 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteBRTable]objIn6 malloc failed !!!");
        return;
    }
    memset(objIn6, 0, sizeof(PortT) * writePortReleatedTableNum);
    for (int i = 0; i < writePortReleatedTableNum; i++) {
        objIn6[i].ifIndex = index + i;
        objIn6[i].portIndex = index + i;
        // 非主键字段
        objIn6[i].nsId = 1;
        objIn6[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "BR.Port", objIn6, writePortReleatedTableNum, PortSet, true);
    free(objIn6);
    
    // BR.Port先二级索引更新，再主键删除
    PortIndex1Update(conn, stmt, "BR.Port", index, index + writePortReleatedTableNum);
    sleep(ST);
    PortPKDelete(conn, stmt, "BR.Port", index, index + writePortReleatedTableNum);
    // BR.PortSecurity主键删除
    PortSecurityPKDelete(conn, stmt, "BR.PortSecurity", index, index + writePortReleatedTableNum);
    // BR.PortMacAttr主键删除
    PortMacAttrPKDelete(conn, stmt, "BR.PortMacAttr", index, index + writePortReleatedTableNum);
    // BR.PortIsolateGrp主键删除
    PortIsolateGrpPKDelete(conn, stmt, "BR.PortIsolateGrp", index, writePortReleatedTableNum);
    
    // BR.VlanTagPort
    // BR.VlanTagPort批写4000条, VlanPortTagBitmap表会产生4000条数据，PortVlanTagBitmap表只会产生1条数据
    VlanTagPortT *objIn7 = (VlanTagPortT *)malloc(sizeof(PortT) * writeBRTableNum);
    if (objIn7 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteBRTable]objIn7 malloc failed !!!");
        return;
    }
    memset(objIn7, 0, sizeof(VlanTagPortT) * writeBRTableNum);
    for (int i = 0; i < writeBRTableNum; i++) {
        objIn7[i].nsId = index + 1;
        objIn7[i].brId = index + 1;
        objIn7[i].vlanId = index + i;
        objIn7[i].portIndex = -2147352576;
        objIn7[i].ifIndex = index;
        objIn7[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "BR.VlanTagPort", objIn7, writeBRTableNum, VlanTagPortSet, true);
    sleep(ST);
    free(objIn7);
    // 二级索引更新，更新二级索引值
    VlanTagPortIndex1Update(conn, stmt, "BR.VlanTagPort", index, index + writeBRTableNum);
    sleep(ST);
    // 主键删除
    VlanTagPortPKDelete(conn, stmt, "BR.VlanTagPort", index, index + writeBRTableNum);
    sleep(ST);
}

// 批写vlanTagPort4000条数据，触发4个join规则
void BatchWriteBRVlanTagPortTable(GmcConnT *conn, GmcStmtT *stmt, uint32_t writeRecordNum)
{
    int ret = 0;
    uint32_t countN = DATALOG_NUM * 2;
    uint32_t writeNum = writeRecordNum;
    // 临时使用该变量，后面再统一使用writeNum变量进行适配
    uint32_t writeBRTableNum = 4000;
    uint32_t start2 = 0;
    uint32_t start3 = 0;
    uint32_t start4 = 0;
    int bridgeUsedCnt = 0;
    int bridgeRemainCnt = 0;
    start2 = writeBRTableNum / 4;
    start3 = start2 * 2;
    start4 = start2 * 3;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int index = rand() % (countN / 2);
    
    // BR.VlanTagPort
    // BR.VlanTagPort批写4000条, VlanPortTagBitmap表会产生4000条数据，PortVlanTagBitmap表只会产生1条数据
    VlanTagPortT *objIn1 = (VlanTagPortT *)malloc(sizeof(PortT) * writeBRTableNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BatchWriteBRVlanTagPortTable]objIn1 malloc failed !!!");
        return;
    }
    memset(objIn1, 0, sizeof(VlanTagPortT) * writeBRTableNum);
    // BR.VlanPortTagBitmap表产生1000条数据(GROUP-BY(ns_id, br_id, vlan_id))
    // BR.PortVlanTagBitmap表产生1条数据
    for (int i = 0; i < start2; i++) {
        objIn1[i].nsId = index + 1;
        objIn1[i].brId = index + 1;
        objIn1[i].vlanId = index + i;
        objIn1[i].portIndex = -2147352576;
        objIn1[i].ifIndex = index;
        objIn1[i].dtlReservedCount = 1;
    }
    // BR.PortVlanTagBitmap表产生1000条数据(GROUP-BY(ns_id, br_id, port_index))
    for (int i = start2; i < start3; i++) {
        objIn1[i].nsId = index + 1;
        objIn1[i].brId = index + 1;
        objIn1[i].vlanId = index + 1;
        objIn1[i].portIndex = index + i;
        objIn1[i].ifIndex = index + 100;   // 范围100~4999+100
        objIn1[i].dtlReservedCount = 1;
    }
    // BR.VlanState0表产生1000条数据
    // VlanState0 (ns_id, br_id, vlan_id, phy_state) :-VlanTagPort(ns_id, br_id, vlan_id, -, if_index),
    // AllIfPhyState（中间表）(if_index, type, phy_state), IfTypeIsEth（函数）(type). // type为0,14,15才能产生数据
    for (int i = start3; i < start4; i++) {
        objIn1[i].nsId = index + 1;
        objIn1[i].brId = index + 1;
        objIn1[i].vlanId = index + i;
        objIn1[i].portIndex = -2147352576;
        objIn1[i].ifIndex = 20;
        objIn1[i].dtlReservedCount = 1;
    }
    // BR.VlanState0表产生1000条数据
    // VlanState0 (ns_id, br_id, vlan_id, 0) :-VlanTagPort(ns_id, br_id, vlan_id, -, -1).
    for (int i = start4; i < writeBRTableNum; i++) {
        objIn1[i].nsId = index + 1;
        objIn1[i].brId = index + 1;
        objIn1[i].vlanId = index + i;
        objIn1[i].portIndex = -2147352576;
        objIn1[i].ifIndex = -1;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "BR.VlanTagPort", objIn1, writeBRTableNum, VlanTagPortSet, true);
    sleep(ST);
    free(objIn1);
    // 主键更新，更新二级索引值，避免删掉预置导入的数据
    VlanTagPortPKUpdate(conn, stmt, "BR.VlanTagPort", index, index + writeBRTableNum);
    sleep(ST);
    // 二级索引删除
    VlanTagPortIndex1Delete(conn, stmt, "BR.VlanTagPort", index, index + writeBRTableNum);
    sleep(ST);
}

// Vlan(-, brId, vlanId, -, -, -, -), VlanTagPort(-, brId, vlanId, -, ifIndex), VlanTagPortChange(-, brId).
// 先对Vlan写数据，VlanTagPort写数据
// 热补丁升级和降级场景
void *HotPatchUpgradeAndRollback(void *args)
{
    int ret = 0;
    int writeVlanTableNum = 100;
    char inputFilePath[FILE_PATH] = "./ylog";
    char outputFilePath[FILE_PATH] = "./ylog";
    char command[MAX_CMD_SIZE] = {0};
    char patchSoName1[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackSoName1[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char patchState[128] = {0};
    (void)sprintf(patchSoName1, "%s/%s_patchV2.so", outputFilePath, g_ylogsoName);
    (void)sprintf(rollbackSoName1, "%s/%s_rollbackV2.so", outputFilePath, g_ylogsoName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, g_ylogsoName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, g_ylogsoName);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = DATALOG_NUM * 2;
    uint32_t writeNum = 0;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return NULL;
    }
    // 切换namespace
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    longStabilitySetDataVolume(args, &writeNum, g_globalCfg.insertNum);
    VlanT *objIn1 = (VlanT *)malloc(sizeof(VlanT) * writeVlanTableNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[HotPatchUpgradeAndRollback] objIn1 malloc failed !!!");
        // 屏蔽assert(0);
    }
    VlanTagPortT *objIn2 = (VlanTagPortT *)malloc(sizeof(VlanTagPortT) * writeVlanTableNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[HotPatchUpgradeAndRollback] objIn2 malloc failed !!!");
        // 屏蔽assert(0);
    }
    VlanTagPortChangeT *objIn3 = (VlanTagPortChangeT *)malloc(sizeof(VlanTagPortChangeT) * writeVlanTableNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[HotPatchUpgradeAndRollback] objIn3 malloc failed !!!");
        // 屏蔽assert(0);
    }
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    // 避免跟以前的数据产生冲突
    int index = rand() % (countN / 2) + 10000;
    // 对Vlan表批写数据，VlanTagPort写数据, VlanTagPortChange写数据
    memset(objIn1, 0, sizeof(VlanT) * writeVlanTableNum);
    for (int i = 0; i < writeVlanTableNum; i++) {
        objIn1[i].nsId = 0;
        objIn1[i].brId = index + i;
        objIn1[i].vlanId = index + i;
        objIn1[i].vlanType = index + i;
        (void)setArryByValue(objIn1[i].name, sizeof(objIn1[i].name), i, U8_MAX, G_MACS);
        (void)setArryByValue(objIn1[i].desc, sizeof(objIn1[i].desc), i, U8_MAX, G_MACS);
        objIn1[i].brVlanIf = 10086;                // 非主键字段
        objIn1[i].dtlReservedCount = 1;
    }
    // 批写
    ret = writeRecord(conn, stmt, "BR.Vlan", objIn1, writeVlanTableNum, VlanSet, false);
    memset(objIn2, 0, sizeof(VlanTagPortT) * writeVlanTableNum);
    for (int i = 0; i < writeVlanTableNum; i++) {
        objIn2[i].nsId = 0;
        objIn2[i].brId = index + i;
        objIn2[i].vlanId = index + i;
        objIn2[i].portIndex = -2147352576;
        objIn2[i].ifIndex = index;
        objIn2[i].dtlReservedCount = 1;
    }
    // 批写
    ret = writeRecord(conn, stmt, "BR.VlanTagPort", objIn2, writeVlanTableNum, VlanTagPortSet, true);
    // 加载升级so
    // 屏蔽TEST_EXPECT_INT32(GMERR_OK, TestLoadUpgradeSo(patchSoName1, NULL, false));
    ret = GmimportUpgradeSo(patchSoName1, NULL, g_ylogsoName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(objIn3, 0, sizeof(VlanTagPortChangeT) * writeVlanTableNum);
    for (int i = 0; i < writeVlanTableNum; i++) {
        objIn3[i].nsId = 0;
        objIn3[i].brId = index + i;
        objIn3[i].dtlReservedCount = 1;
    }
    // 批写
    ret = writeRecord(conn, stmt, "BR.VlanTagPortChange", objIn3, writeVlanTableNum, VlanTagPortChangeSet, true);
    // 加载升级so
    ret = GmimportUpgradeSo(patchSoName2, NULL, g_ylogsoName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 可能重做成功，可能重做失败
    // 屏蔽system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO | grep PATCH_STATE -C 10");
    // 加载降级so
    ret = GmimportRollbackSo(rollbackSoName2, NULL, g_ylogsoName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 屏蔽system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO | grep PATCH_STATE -C 10");
    // 加载回滚so
    ret = GmimportRollbackSo(rollbackSoName1, NULL, g_ylogsoName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 删除写入的数据
    VlanPKDelete(conn, stmt, "BR.Vlan", index, index + writeVlanTableNum);
    VlanTagPortPKDelete1(conn, stmt, "BR.VlanTagPort", index, index + writeVlanTableNum);
    free(objIn1);
    free(objIn2);
    free(objIn3);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return NULL;
}

// 分三次升级：第一次升级block 1模式新增udf；第二次升级block 1模式新增可更新表；第三次升级block 0模式将新增的表与原先规则join
void *BlockHotPatchUpgradeAndRollback(void *args)
{
    int ret = 0;
    int writeVlanTableNum = 100;
    char inputFilePath[FILE_PATH] = "./datalogFile";
    char outputFilePath[FILE_PATH] = "./datalogFile";
    char patchSoName1[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char rollbackSoName1[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    (void)sprintf(patchSoName1, "%s/%s_patchV2.so", outputFilePath, g_soName);
    (void)sprintf(rollbackSoName1, "%s/%s_rollbackV2.so", outputFilePath, g_soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, g_soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, g_soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, g_soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, g_soName);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = DATALOG_NUM * 2;
    uint32_t writeNum = 0;
    longStabilitySetDataVolume(args, &writeNum, g_globalCfg.insertNum);
    // 统一分配内存
    C3Int4C1Int8T *objIn1 = (C3Int4C1Int8T *)malloc(sizeof(C3Int4C1Int8T) * writeNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BlockHotPatchUpgradeAndRollback] objIn1 malloc failed !!!");
        return NULL;
    }
    C3Int4C1Int8T *objIn2 = (C3Int4C1Int8T *)malloc(sizeof(C3Int4C1Int8T) * writeNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[BlockHotPatchUpgradeAndRollback] objIn2 malloc failed !!!");
        if (objIn1) {
            free(objIn1);
        }
        return NULL;
    }
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return NULL;
    }
    // 切换namespace
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    while (1) {
        THREAD_CONTROL_POINT();
        time_t timer = time(0);
        if (!timer) {
            printf("[ERROR] get time fail\n");
        }
        srand((uint32_t)timer);
        int index = rand() % (countN / 2) + 100;
        // 先对namespace01.inputTableD表写数据
        memset(objIn1, 0, sizeof(C3Int4C1Int8T) * writeNum);
        for (int i = 0; i < writeNum; i++) {
            objIn1[i].a = index + i;
            objIn1[i].b = index - 1;
            objIn1[i].c = index + i;
            objIn1[i].d = index + i;
            objIn1[i].upgradeVersion = 100;
            objIn1[i].dtlReservedCount = (i % 2) + 1;
        }
        // 结构化批写
        ret = writeRecord(conn, stmt, g_inputTableD, objIn1, writeNum, NULL, true, true, g_schemaJson11);
        // 加载第一次升级so
        ret = GmimportUpgradeSo(patchSoName1, NULL, g_soName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 加载第二次升级so
        ret = GmimportUpgradeSo(patchSoName2, NULL, g_soName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 对新增表的输入表写数据namespace01.inputTableE
        memset(objIn2, 0, sizeof(C3Int4C1Int8T) * writeNum);
        for (int i = 0; i < writeNum; i++) {
            objIn2[i].a = index + i;
            objIn2[i].b = index - 1;
            objIn2[i].c = index + i;
            objIn2[i].d = 3 * index + 2 * i - 1;
            objIn2[i].upgradeVersion = 100;
            objIn2[i].dtlReservedCount = (i % 2) + 1;
        }
        // 结构化批写
        ret = writeRecord(conn, stmt, "namespace01.inputTableE", objIn2, writeNum, NULL, true, true, g_schemaJson12);
        // 加载第三次升级so
        ret = GmimportUpgradeSo(patchSoName3, NULL, g_soName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 依次加载降级so
        ret = GmimportRollbackSo(rollbackSoName3, NULL, g_soName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmimportRollbackSo(rollbackSoName2, NULL, g_soName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmimportRollbackSo(rollbackSoName1, NULL, g_soName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 删除namespace01.inputTableD表写中数据
        for (int i = 0; i < writeNum; i++) {
            objIn1[i].a = index + i;
            objIn1[i].b = index - 1;
            objIn1[i].c = index + i;
            objIn1[i].d = index + i;
            objIn1[i].upgradeVersion = 100;
            objIn1[i].dtlReservedCount = -((i % 2) + 1);
        }
        ret = writeRecord(conn, stmt, g_inputTableD, objIn1, writeNum, NULL, true, true, g_schemaJson11);
        sleep(ST * 4);
    }
    free(objIn1);
    free(objIn2);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

/*----------------------------------------外部表DML、DQL操作--------------------------------------*/
// insert
void insertExternalD(GmcConnT *conn, GmcStmtT *stmt, int startnum, int endnum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_externName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startnum; i < endnum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        ret = GmcExecute(stmt);
        // 当前非阻塞场景重做分两个事务：undo + redo  中间插入1个普通dml删数据，会导致输出表中的数据删不掉；当前先屏蔽1005002错误码
        // 等迭代三非阻塞合并式合入，再放开，代码已合入
        // 优化完线程后，会出现主键冲突的情况
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// replace
void replaceExternalD(GmcConnT *conn, GmcStmtT *stmt, int startnum, int endnum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_externName, GMC_OPERATION_REPLACE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startnum; i < endnum; i++) {
        // set value
        setFieldCValue(stmt, i);
        setTableValueNoFieldC(stmt, i);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// merge
void mergeExternalD(GmcConnT *conn, GmcStmtT *stmt, int startnum, int endnum)
{
    int ret = 0;
    // 先插入数据，再更新
    insertExternalD(conn, stmt, startnum, endnum);
    ret = testGmcPrepareStmtByLabelName(stmt, g_externName, GMC_OPERATION_MERGE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startnum; i < endnum; i++) {
        int32_t aValue = i;
        int32_t bValue = i;
        int32_t cValue = i;
        int64_t dValue = i + 10;
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &aValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &bValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &cValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        int ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &dValue, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// update
void updateExternalD(GmcConnT *conn, GmcStmtT *stmt, int startnum, int endnum)
{
    int ret = 0;
    // 先插入数据，再更新
    insertExternalD(conn, stmt, startnum, endnum);
    ret = testGmcPrepareStmtByLabelName(stmt, g_externName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 主键更新
    for (int i = startnum; i < endnum; i++) {
        int32_t aValue = i;
        int32_t bValue = i;
        int32_t cValue = i;
        int64_t dValue = i + 10;
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &aValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &bValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &cValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        int ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &dValue, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// delete
void deleteExternalD(GmcConnT *conn, GmcStmtT *stmt, int startnum, int endnum)
{
    int ret = 0;
    // 先插入数据，再删除
    insertExternalD(conn, stmt, startnum, endnum);
    ret = testGmcPrepareStmtByLabelName(stmt, g_externName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 主键删除
    for (int i = startnum; i < endnum; i++) {
        int32_t aValue = i;
        int32_t bValue = i;
        int32_t cValue = i;
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &aValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &bValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &cValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// scan
void scanExternalD(GmcConnT *conn, GmcStmtT *stmt, int startnum, int endnum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_externName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        do {
            usleep(10000);
            ret = GmcExecute(stmt);
        } while (ret == GMERR_PROGRAM_LIMIT_EXCEEDED || ret == GMERR_SUB_PUSH_QUEUE_FULL);
    }
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    bool isFinish;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (ret != GMERR_OK || isFinish) {
            break;
        }
        cnt++;
    }
}
// truncate
void truncateExternalD(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
    // GmcDeleteAllFast是异步接口，可能会导致写datalog表时，拿不到事务锁
    ret = GmcTruncateVertexLabel(stmt, g_externName);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

// 1个连接先操作datalog表，再对fastpath表进行随机操作(insert, replace, merge, update, delete, scan)
void WritedtlTableAndRandomOpExternal(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    uint32_t writeNum = 0;
    int sceneNum = 6;
    longStabilitySetDataVolume(NULL, &writeNum, g_globalCfg.insertNum);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    // index为0时，会导致数据删不掉
    int index = rand() % (countN / 2) + 1;
    // 先写datalog表
    TableSingleWrite(conn, stmt, g_inputTableD, index, index + writeNum, true);
    // 删除
    TableSingleWrite(conn, stmt, g_inputTableD, index, index + writeNum, false);
    // 对fastpath表随机操作(insert, replace, merge, update, delete, scan)、truncate表
    int flag = rand() % sceneNum;
    if (flag == 0) {
        insertExternalD(conn, stmt, index, index + writeNum);
    } else if (flag == 1) {
        replaceExternalD(conn, stmt, index, index + writeNum);
    } else if (flag == 2) {
        mergeExternalD(conn, stmt, index, index + writeNum);
    } else if (flag == 3) {
        updateExternalD(conn, stmt, index, index + writeNum);
    } else if (flag == 4) {
        deleteExternalD(conn, stmt, index, index + writeNum);
    } else {
        scanExternalD(conn, stmt, index, index + writeNum);
    }
    // 清空数据
    truncateExternalD(conn, stmt);
    sleep(ST);
}

// 并发查询热补丁视图
void *ScanPatchView(void *args)
{
    char command[1024] = {0};
    int ret = 0;
    (void)snprintf(command, sizeof(command), "%s/gmsysview -q %s -s %s > /dev/null 2>&1", g_toolPath, g_patchViewName,
        g_connServer);
    while (1) {
        system(command);
        // 每隔10ms查询一次视图
        usleep(1000 * 10);
    }
}

#endif // FEATURE_DATALOG

#endif // DATALOG_STABILITY_TEST_H
