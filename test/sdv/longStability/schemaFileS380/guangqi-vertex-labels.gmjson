[{"name": "ietf-interfaces", "type": "container", "config": {"check_validity": true}, "is_root": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"name": "ietf-interfaces.PK", "index": {"type": "primary"}, "node": "ietf-interfaces", "fields": ["ID"], "constraints": {"unique": true}}]}, {"name": "if:interface.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..63"}, {"name": "description", "type": "string", "nullable": true, "length": "0..255"}, {"name": "type", "type": "identity", "enumerate": [{"name": "iana-if-type:iana-interface-type", "value": 0, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type"}]}, {"name": "iana-if-type:other", "value": 1, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:other"}]}, {"name": "iana-if-type:regular1822", "value": 2, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:regular1822"}]}, {"name": "iana-if-type:hdh1822", "value": 3, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hdh1822"}]}, {"name": "iana-if-type:ddnX25", "value": 4, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ddnX25"}]}, {"name": "iana-if-type:rfc877x25", "value": 5, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rfc877x25"}]}, {"name": "iana-if-type:ethernetCsmacd", "value": 6, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd"}]}, {"name": "huawei-tc-an:eth-sub-type-fe-elc", "value": 7, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-fe-elc"}]}, {"name": "huawei-tc-an:eth-sub-type-fe-opt", "value": 8, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-fe-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-ge-elc", "value": 9, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-ge-elc"}]}, {"name": "huawei-tc-an:eth-sub-type-ge-opt", "value": 10, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-ge-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-10ge-opt", "value": 11, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-10ge-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-100ge-opt", "value": 12, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-100ge-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-40ge-opt", "value": 13, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-40ge-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-25ge-opt", "value": 14, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-25ge-opt"}]}, {"name": "iana-if-type:iso88023Csmacd", "value": 15, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88023Csmacd"}]}, {"name": "iana-if-type:iso88024TokenBus", "value": 16, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88024TokenBus"}]}, {"name": "iana-if-type:iso88025TokenRing", "value": 17, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88025TokenRing"}]}, {"name": "iana-if-type:iso88026Man", "value": 18, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88026Man"}]}, {"name": "iana-if-type:star<PERSON>an", "value": 19, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:starLan"}]}, {"name": "iana-if-type:proteon10Mbit", "value": 20, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:proteon10Mbit"}]}, {"name": "iana-if-type:proteon80Mbit", "value": 21, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:proteon80Mbit"}]}, {"name": "iana-if-type:hyperchannel", "value": 22, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hyperchannel"}]}, {"name": "iana-if-type:fddi", "value": 23, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fddi"}]}, {"name": "iana-if-type:lapb", "value": 24, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:lapb"}]}, {"name": "iana-if-type:sdlc", "value": 25, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sdlc"}]}, {"name": "iana-if-type:ds1", "value": 26, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds1"}]}, {"name": "iana-if-type:e1", "value": 27, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:e1"}]}, {"name": "iana-if-type:basicISDN", "value": 28, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:basicISDN"}]}, {"name": "iana-if-type:primaryISDN", "value": 29, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:primaryISDN"}]}, {"name": "iana-if-type:propPointToPointSerial", "value": 30, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propPointToPointSerial"}]}, {"name": "iana-if-type:ppp", "value": 31, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ppp"}]}, {"name": "iana-if-type:softwareLoopback", "value": 32, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:softwareLoopback"}]}, {"name": "iana-if-type:eon", "value": 33, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:eon"}]}, {"name": "iana-if-type:ethernet3Mbit", "value": 34, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernet3Mbit"}]}, {"name": "iana-if-type:nsip", "value": 35, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:nsip"}]}, {"name": "iana-if-type:slip", "value": 36, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:slip"}]}, {"name": "iana-if-type:ultra", "value": 37, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ultra"}]}, {"name": "iana-if-type:ds3", "value": 38, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds3"}]}, {"name": "iana-if-type:sip", "value": 39, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sip"}]}, {"name": "iana-if-type:frame<PERSON><PERSON>y", "value": 40, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frameRelay"}]}, {"name": "iana-if-type:rs232", "value": 41, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rs232"}]}, {"name": "iana-if-type:para", "value": 42, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:para"}]}, {"name": "iana-if-type:arcnet", "value": 43, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:arcnet"}]}, {"name": "iana-if-type:arcnetPlus", "value": 44, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:arcnetPlus"}]}, {"name": "iana-if-type:atm", "value": 45, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atm"}]}, {"name": "iana-if-type:miox25", "value": 46, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:miox25"}]}, {"name": "iana-if-type:sonet", "value": 47, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sonet"}]}, {"name": "iana-if-type:x25ple", "value": 48, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x25ple"}]}, {"name": "iana-if-type:iso88022llc", "value": 49, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88022llc"}]}, {"name": "iana-if-type:localTalk", "value": 50, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:localTalk"}]}, {"name": "iana-if-type:smdsDxi", "value": 51, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:smdsDxi"}]}, {"name": "iana-if-type:frameRelayService", "value": 52, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frameRelayService"}]}, {"name": "iana-if-type:v35", "value": 53, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:v35"}]}, {"name": "iana-if-type:hssi", "value": 54, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hssi"}]}, {"name": "iana-if-type:hippi", "value": 55, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hippi"}]}, {"name": "iana-if-type:modem", "value": 56, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:modem"}]}, {"name": "iana-if-type:aal5", "value": 57, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aal5"}]}, {"name": "iana-if-type:sonetPath", "value": 58, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sonetPath"}]}, {"name": "iana-if-type:sonetVT", "value": 59, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sonetVT"}]}, {"name": "iana-if-type:smdsIcip", "value": 60, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:smdsIcip"}]}, {"name": "iana-if-type:propVirtual", "value": 61, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propVirtual"}]}, {"name": "iana-if-type:propMultiplexor", "value": 62, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propMultiplexor"}]}, {"name": "iana-if-type:ieee80212", "value": 63, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee80212"}]}, {"name": "iana-if-type:fibreChannel", "value": 64, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fibreChannel"}]}, {"name": "iana-if-type:hippiInterface", "value": 65, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hippiInterface"}]}, {"name": "iana-if-type:frameRelayInterconnect", "value": 66, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frameRelayInterconnect"}]}, {"name": "iana-if-type:aflane8023", "value": 67, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aflane8023"}]}, {"name": "iana-if-type:aflane8025", "value": 68, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aflane8025"}]}, {"name": "iana-if-type:cctEmul", "value": 69, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:cctEmul"}]}, {"name": "iana-if-type:fastEther", "value": 70, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fastEther"}]}, {"name": "iana-if-type:isdn", "value": 71, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:isdn"}]}, {"name": "iana-if-type:v11", "value": 72, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:v11"}]}, {"name": "iana-if-type:v36", "value": 73, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:v36"}]}, {"name": "iana-if-type:g703at64k", "value": 74, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g703at64k"}]}, {"name": "iana-if-type:g703at2mb", "value": 75, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g703at2mb"}]}, {"name": "iana-if-type:qllc", "value": 76, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:qllc"}]}, {"name": "iana-if-type:fastEtherFX", "value": 77, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fastEtherFX"}]}, {"name": "iana-if-type:channel", "value": 78, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:channel"}]}, {"name": "iana-if-type:ieee80211", "value": 79, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee80211"}]}, {"name": "iana-if-type:ibm370parChan", "value": 80, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ibm370parChan"}]}, {"name": "iana-if-type:escon", "value": 81, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:escon"}]}, {"name": "iana-if-type:dlsw", "value": 82, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dlsw"}]}, {"name": "iana-if-type:isdns", "value": 83, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:isdns"}]}, {"name": "iana-if-type:isdnu", "value": 84, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:isdnu"}]}, {"name": "iana-if-type:lapd", "value": 85, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:lapd"}]}, {"name": "iana-if-type:ipSwitch", "value": 86, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipSwitch"}]}, {"name": "iana-if-type:rsrb", "value": 87, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rsrb"}]}, {"name": "iana-if-type:atmLogical", "value": 88, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmLogical"}]}, {"name": "iana-if-type:ds0", "value": 89, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds0"}]}, {"name": "iana-if-type:ds0Bundle", "value": 90, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds0Bundle"}]}, {"name": "iana-if-type:bsc", "value": 91, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:bsc"}]}, {"name": "iana-if-type:async", "value": 92, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:async"}]}, {"name": "iana-if-type:cnr", "value": 93, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:cnr"}]}, {"name": "iana-if-type:iso88025Dtr", "value": 94, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88025Dtr"}]}, {"name": "iana-if-type:eplrs", "value": 95, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:eplrs"}]}, {"name": "iana-if-type:arap", "value": 96, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:arap"}]}, {"name": "iana-if-type:propCnls", "value": 97, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propCnls"}]}, {"name": "iana-if-type:hostPad", "value": 98, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hostPad"}]}, {"name": "iana-if-type:termPad", "value": 99, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:termPad"}]}, {"name": "iana-if-type:frameRelayMPI", "value": 100, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frameRelayMPI"}]}, {"name": "iana-if-type:x213", "value": 101, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x213"}]}, {"name": "iana-if-type:adsl", "value": 102, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:adsl"}]}, {"name": "iana-if-type:radsl", "value": 103, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:radsl"}]}, {"name": "iana-if-type:sdsl", "value": 104, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sdsl"}]}, {"name": "iana-if-type:vdsl", "value": 105, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:vdsl"}]}, {"name": "iana-if-type:iso88025CRFPInt", "value": 106, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88025CRFPInt"}]}, {"name": "iana-if-type:myrinet", "value": 107, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:myrinet"}]}, {"name": "iana-if-type:voiceEM", "value": 108, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceEM"}]}, {"name": "iana-if-type:voiceFXO", "value": 109, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceFXO"}]}, {"name": "iana-if-type:voiceFXS", "value": 110, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceFXS"}]}, {"name": "iana-if-type:voiceEncap", "value": 111, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceEncap"}]}, {"name": "iana-if-type:voiceOverIp", "value": 112, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceOverIp"}]}, {"name": "iana-if-type:atmDxi", "value": 113, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmDxi"}]}, {"name": "iana-if-type:atm<PERSON>uni", "value": 114, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmFuni"}]}, {"name": "iana-if-type:atmIma", "value": 115, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmIma"}]}, {"name": "iana-if-type:pppMultilinkBundle", "value": 116, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pppMultilinkBundle"}]}, {"name": "iana-if-type:ipOverCdlc", "value": 117, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipOverCdlc"}]}, {"name": "iana-if-type:ipOverClaw", "value": 118, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipOverClaw"}]}, {"name": "iana-if-type:stackToStack", "value": 119, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:stackToStack"}]}, {"name": "iana-if-type:virtualIpAddress", "value": 120, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:virtualIpAddress"}]}, {"name": "iana-if-type:mpc", "value": 121, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mpc"}]}, {"name": "iana-if-type:ipOverAtm", "value": 122, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipOverAtm"}]}, {"name": "iana-if-type:iso88025Fiber", "value": 123, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88025Fiber"}]}, {"name": "iana-if-type:tdlc", "value": 124, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:tdlc"}]}, {"name": "iana-if-type:gigabitEthernet", "value": 125, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gigabitEthernet"}]}, {"name": "iana-if-type:hdlc", "value": 126, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hdlc"}]}, {"name": "iana-if-type:lapf", "value": 127, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:lapf"}]}, {"name": "iana-if-type:v37", "value": 128, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:v37"}]}, {"name": "iana-if-type:x25mlp", "value": 129, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x25mlp"}]}, {"name": "iana-if-type:x25huntGroup", "value": 130, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x25huntGroup"}]}, {"name": "iana-if-type:transpHdlc", "value": 131, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:transpHdlc"}]}, {"name": "iana-if-type:interleave", "value": 132, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:interleave"}]}, {"name": "iana-if-type:fast", "value": 133, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fast"}]}, {"name": "iana-if-type:ip", "value": 134, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ip"}]}, {"name": "iana-if-type:docsCableMaclayer", "value": 135, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableMaclayer"}]}, {"name": "iana-if-type:docsCableDownstream", "value": 136, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableDownstream"}]}, {"name": "iana-if-type:docsCableUpstream", "value": 137, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableUpstream"}]}, {"name": "iana-if-type:a12MppSwitch", "value": 138, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:a12MppSwitch"}]}, {"name": "iana-if-type:tunnel", "value": 139, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:tunnel"}]}, {"name": "iana-if-type:coffee", "value": 140, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:coffee"}]}, {"name": "iana-if-type:ces", "value": 141, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ces"}]}, {"name": "iana-if-type:atmSubInterface", "value": 142, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmSubInterface"}]}, {"name": "iana-if-type:l2vlan", "value": 143, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:l2vlan"}]}, {"name": "iana-if-type:l3ipvlan", "value": 144, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:l3ipvlan"}]}, {"name": "iana-if-type:l3ipxvlan", "value": 145, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:l3ipxvlan"}]}, {"name": "iana-if-type:digitalPowerline", "value": 146, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:digitalPowerline"}]}, {"name": "iana-if-type:mediaMailOverIp", "value": 147, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mediaMailOverIp"}]}, {"name": "iana-if-type:dtm", "value": 148, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dtm"}]}, {"name": "iana-if-type:dcn", "value": 149, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dcn"}]}, {"name": "iana-if-type:ipForward", "value": 150, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipForward"}]}, {"name": "iana-if-type:msdsl", "value": 151, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:msdsl"}]}, {"name": "iana-if-type:ieee1394", "value": 152, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee1394"}]}, {"name": "iana-if-type:if-gsn", "value": 153, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:if-gsn"}]}, {"name": "iana-if-type:dvbRccMacLayer", "value": 154, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRccMacLayer"}]}, {"name": "iana-if-type:dvbRccDownstream", "value": 155, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRccDownstream"}]}, {"name": "iana-if-type:dvbRccUpstream", "value": 156, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRccUpstream"}]}, {"name": "iana-if-type:atmVirtual", "value": 157, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmVirtual"}]}, {"name": "iana-if-type:mplsTunnel", "value": 158, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mplsTunnel"}]}, {"name": "iana-if-type:srp", "value": 159, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:srp"}]}, {"name": "iana-if-type:voiceOverAtm", "value": 160, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceOverAtm"}]}, {"name": "iana-if-type:voiceOverFrameRelay", "value": 161, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceOverFrameRelay"}]}, {"name": "iana-if-type:idsl", "value": 162, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:idsl"}]}, {"name": "iana-if-type:compositeLink", "value": 163, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:compositeLink"}]}, {"name": "iana-if-type:ss7SigLink", "value": 164, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ss7SigLink"}]}, {"name": "iana-if-type:propWirelessP2P", "value": 165, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propWirelessP2P"}]}, {"name": "iana-if-type:frForward", "value": 166, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frForward"}]}, {"name": "iana-if-type:rfc1483", "value": 167, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rfc1483"}]}, {"name": "iana-if-type:usb", "value": 168, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:usb"}]}, {"name": "iana-if-type:ieee8023adLag", "value": 169, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee8023adLag"}]}, {"name": "iana-if-type:bgppolicyaccounting", "value": 170, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:bgppolicyaccounting"}]}, {"name": "iana-if-type:frf16MfrBundle", "value": 171, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frf16MfrBundle"}]}, {"name": "iana-if-type:h323Gatekeeper", "value": 172, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:h323Gatekeeper"}]}, {"name": "iana-if-type:h323Proxy", "value": 173, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:h323Proxy"}]}, {"name": "iana-if-type:mpls", "value": 174, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mpls"}]}, {"name": "iana-if-type:mfSigLink", "value": 175, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mfSigLink"}]}, {"name": "iana-if-type:hdsl2", "value": 176, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hdsl2"}]}, {"name": "iana-if-type:shdsl", "value": 177, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:shdsl"}]}, {"name": "iana-if-type:ds1FDL", "value": 178, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds1FDL"}]}, {"name": "iana-if-type:pos", "value": 179, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pos"}]}, {"name": "iana-if-type:dvbAsiIn", "value": 180, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbAsiIn"}]}, {"name": "iana-if-type:dvbAsiOut", "value": 181, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbAsiOut"}]}, {"name": "iana-if-type:plc", "value": 182, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:plc"}]}, {"name": "iana-if-type:nfas", "value": 183, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:nfas"}]}, {"name": "iana-if-type:tr008", "value": 184, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:tr008"}]}, {"name": "iana-if-type:gr303RDT", "value": 185, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gr303RDT"}]}, {"name": "iana-if-type:gr303IDT", "value": 186, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gr303IDT"}]}, {"name": "iana-if-type:isup", "value": 187, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:isup"}]}, {"name": "iana-if-type:propDocsWirelessMaclayer", "value": 188, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propDocsWirelessMaclayer"}]}, {"name": "iana-if-type:propDocsWirelessDownstream", "value": 189, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propDocsWirelessDownstream"}]}, {"name": "iana-if-type:propDocsWirelessUpstream", "value": 190, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propDocsWirelessUpstream"}]}, {"name": "iana-if-type:hiperlan2", "value": 191, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hiperlan2"}]}, {"name": "iana-if-type:propBWAp2Mp", "value": 192, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propBWAp2Mp"}]}, {"name": "iana-if-type:sonetOverheadChannel", "value": 193, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sonetOverheadChannel"}]}, {"name": "iana-if-type:digitalWrapperOverheadChannel", "value": 194, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:digitalWrapperOverheadChannel"}]}, {"name": "iana-if-type:aal2", "value": 195, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aal2"}]}, {"name": "iana-if-type:radioMAC", "value": 196, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:radioMAC"}]}, {"name": "iana-if-type:atmRadio", "value": 197, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmRadio"}]}, {"name": "iana-if-type:imt", "value": 198, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:imt"}]}, {"name": "iana-if-type:mvl", "value": 199, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mvl"}]}, {"name": "iana-if-type:reachDSL", "value": 200, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:reachDSL"}]}, {"name": "iana-if-type:frDlciEndPt", "value": 201, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frDlciEndPt"}]}, {"name": "iana-if-type:atmVciEndPt", "value": 202, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmVciEndPt"}]}, {"name": "iana-if-type:opticalChannel", "value": 203, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:opticalChannel"}]}, {"name": "iana-if-type:opticalTransport", "value": 204, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:opticalTransport"}]}, {"name": "iana-if-type:propAtm", "value": 205, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propAtm"}]}, {"name": "iana-if-type:voiceOverCable", "value": 206, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceOverCable"}]}, {"name": "iana-if-type:infiniband", "value": 207, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:infiniband"}]}, {"name": "iana-if-type:teLink", "value": 208, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:teLink"}]}, {"name": "iana-if-type:q2931", "value": 209, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:q2931"}]}, {"name": "iana-if-type:virtualTg", "value": 210, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:virtualTg"}]}, {"name": "iana-if-type:sipTg", "value": 211, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sipTg"}]}, {"name": "iana-if-type:sipSig", "value": 212, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sipSig"}]}, {"name": "iana-if-type:docsCableUpstreamChannel", "value": 213, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableUpstreamChannel"}]}, {"name": "iana-if-type:econet", "value": 214, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:econet"}]}, {"name": "iana-if-type:pon155", "value": 215, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pon155"}]}, {"name": "iana-if-type:pon622", "value": 216, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pon622"}]}, {"name": "iana-if-type:bridge", "value": 217, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:bridge"}]}, {"name": "iana-if-type:linegroup", "value": 218, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:linegroup"}]}, {"name": "iana-if-type:voiceEMFGD", "value": 219, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceEMFGD"}]}, {"name": "iana-if-type:voiceFGDEANA", "value": 220, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceFGDEANA"}]}, {"name": "iana-if-type:voiceDID", "value": 221, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceDID"}]}, {"name": "iana-if-type:mpegTransport", "value": 222, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mpegTransport"}]}, {"name": "iana-if-type:sixToFour", "value": 223, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sixToFour"}]}, {"name": "iana-if-type:gtp", "value": 224, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gtp"}]}, {"name": "iana-if-type:pdnEtherLoop1", "value": 225, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pdnEtherLoop1"}]}, {"name": "iana-if-type:pdnEtherLoop2", "value": 226, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pdnEtherLoop2"}]}, {"name": "iana-if-type:opticalChannelGroup", "value": 227, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:opticalChannelGroup"}]}, {"name": "iana-if-type:homepna", "value": 228, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:homepna"}]}, {"name": "iana-if-type:gfp", "value": 229, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gfp"}]}, {"name": "iana-if-type:ciscoISLvlan", "value": 230, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ciscoISLvlan"}]}, {"name": "iana-if-type:actelisMetaLOOP", "value": 231, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:actelisMetaLOOP"}]}, {"name": "iana-if-type:fcipLink", "value": 232, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fcipLink"}]}, {"name": "iana-if-type:rpr", "value": 233, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rpr"}]}, {"name": "iana-if-type:qam", "value": 234, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:qam"}]}, {"name": "iana-if-type:lmp", "value": 235, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:lmp"}]}, {"name": "iana-if-type:cblVectaStar", "value": 236, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:cblVectaStar"}]}, {"name": "iana-if-type:docsCableMCmtsDownstream", "value": 237, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableMCmtsDownstream"}]}, {"name": "iana-if-type:adsl2", "value": 238, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:adsl2"}]}, {"name": "iana-if-type:macSecControlledIF", "value": 239, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:macSecControlledIF"}]}, {"name": "iana-if-type:macSecUncontrolledIF", "value": 240, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:macSecUncontrolledIF"}]}, {"name": "iana-if-type:aviciOpticalEther", "value": 241, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aviciOpticalEther"}]}, {"name": "iana-if-type:atmbond", "value": 242, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmbond"}]}, {"name": "iana-if-type:voiceFGDOS", "value": 243, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceFGDOS"}]}, {"name": "iana-if-type:mocaVersion1", "value": 244, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mocaVersion1"}]}, {"name": "iana-if-type:ieee80216WMAN", "value": 245, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee80216WMAN"}]}, {"name": "iana-if-type:adsl2plus", "value": 246, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:adsl2plus"}]}, {"name": "iana-if-type:dvbRcsMacLayer", "value": 247, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRcsMacLayer"}]}, {"name": "iana-if-type:dvbTdm", "value": 248, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbTdm"}]}, {"name": "iana-if-type:dvbRcsTdma", "value": 249, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRcsTdma"}]}, {"name": "iana-if-type:x86Laps", "value": 250, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x86Laps"}]}, {"name": "iana-if-type:wwan<PERSON>", "value": 251, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:wwanPP"}]}, {"name": "iana-if-type:wwanPP2", "value": 252, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:wwanPP2"}]}, {"name": "iana-if-type:voiceEBS", "value": 253, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceEBS"}]}, {"name": "iana-if-type:ifPwType", "value": 254, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ifPwType"}]}, {"name": "iana-if-type:ilan", "value": 255, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ilan"}]}, {"name": "iana-if-type:pip", "value": 256, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pip"}]}, {"name": "iana-if-type:aluELP", "value": 257, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluELP"}]}, {"name": "iana-if-type:gpon", "value": 258, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gpon"}]}, {"name": "iana-if-type:vdsl2", "value": 259, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:vdsl2"}]}, {"name": "iana-if-type:capwapDot11Profile", "value": 260, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:capwapDot11Profile"}]}, {"name": "iana-if-type:capwapDot11Bss", "value": 261, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:capwapDot11Bss"}]}, {"name": "iana-if-type:capwapWtpVirtualRadio", "value": 262, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:capwapWtpVirtualRadio"}]}, {"name": "iana-if-type:bits", "value": 263, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:bits"}]}, {"name": "iana-if-type:docsCableUpstreamRfPort", "value": 264, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableUpstreamRfPort"}]}, {"name": "iana-if-type:cableDownstreamRfPort", "value": 265, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:cableDownstreamRfPort"}]}, {"name": "iana-if-type:vmwareVirtualNic", "value": 266, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:vmwareVirtualNic"}]}, {"name": "iana-if-type:ieee802154", "value": 267, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee802154"}]}, {"name": "iana-if-type:otnOdu", "value": 268, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:otnOdu"}]}, {"name": "iana-if-type:otnOtu", "value": 269, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:otnOtu"}]}, {"name": "iana-if-type:ifVfiType", "value": 270, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ifVfiType"}]}, {"name": "iana-if-type:g9981", "value": 271, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g9981"}]}, {"name": "iana-if-type:g9982", "value": 272, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g9982"}]}, {"name": "iana-if-type:g9983", "value": 273, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g9983"}]}, {"name": "iana-if-type:alu<PERSON><PERSON>n", "value": 274, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluEpon"}]}, {"name": "iana-if-type:alu<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 275, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluEponOnu"}]}, {"name": "iana-if-type:aluEponPhysicalUni", "value": 276, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluEponPhysicalUni"}]}, {"name": "iana-if-type:aluEponLogicalLink", "value": 277, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluEponLogicalLink"}]}, {"name": "iana-if-type:alu<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 278, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluGponOnu"}]}, {"name": "iana-if-type:aluGponPhysicalUni", "value": 279, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluGponPhysicalUni"}]}, {"name": "iana-if-type:vmwareNicTeam", "value": 280, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:vmwareNicTeam"}]}, {"name": "iana-if-type:docsOfdmDownstream", "value": 281, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsOfdmDownstream"}]}, {"name": "iana-if-type:docsOfdmaUpstream", "value": 282, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsOfdmaUpstream"}]}, {"name": "iana-if-type:gfast", "value": 283, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gfast"}]}, {"name": "iana-if-type:sdci", "value": 284, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sdci"}]}, {"name": "iana-if-type:xboxWireless", "value": 285, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:xboxWireless"}]}, {"name": "iana-if-type:fastdsl", "value": 286, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fastdsl"}]}, {"name": "iana-if-type:docsCableScte55d1FwdOob", "value": 287, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableScte55d1FwdOob"}]}, {"name": "iana-if-type:docsCableScte55d1RetOob", "value": 288, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableScte55d1RetOob"}]}, {"name": "iana-if-type:docsCableScte55d2DsOob", "value": 289, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableScte55d2DsOob"}]}, {"name": "iana-if-type:docsCableScte55d2UsOob", "value": 290, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableScte55d2UsOob"}]}, {"name": "iana-if-type:docsCableNdf", "value": 291, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableNdf"}]}, {"name": "iana-if-type:docsCableNdr", "value": 292, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableNdr"}]}, {"name": "iana-if-type:ptm", "value": 293, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ptm"}]}, {"name": "bbf-uni-profile:pots", "value": 294, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/bbf-uni-profile:pots"}]}, {"name": "bbf-uni-profile:catv", "value": 295, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/bbf-uni-profile:catv"}]}, {"name": "bbf-uni-profile:wifi", "value": 296, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/bbf-uni-profile:wifi"}]}, {"name": "bbf-uni-profile:e1", "value": 297, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/bbf-uni-profile:e1"}]}, {"name": "bbf-uni-profile:t1", "value": 298, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/bbf-uni-profile:t1"}]}, {"name": "bbf-uni-profile:iphost", "value": 299, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/bbf-uni-profile:iphost"}]}, {"name": "bbf-if-type:bbf-interface-type", "value": 300, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type"}]}, {"name": "bbf-if-type:sub-interface", "value": 301, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:sub-interface"}]}, {"name": "bbf-if-type:vlan-sub-interface", "value": 302, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:sub-interface/bbf-if-type:vlan-sub-interface"}]}, {"name": "bbf-if-type-enhance:vlan-connect-sub-interface", "value": 303, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:sub-interface/bbf-if-type:vlan-sub-interface/bbf-if-type-enhance:vlan-connect-sub-interface"}]}, {"name": "bbf-if-type:l2-termination", "value": 304, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:l2-termination"}]}, {"name": "bbf-if-type:ethernet-like", "value": 305, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:ethernet-like"}]}, {"name": "bbf-xpon-if-type:onu-v-enet", "value": 306, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:ethernet-like/bbf-xpon-if-type:onu-v-enet"}]}, {"name": "bbf-xpon-if-type:olt-v-enet", "value": 307, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:ethernet-like/bbf-xpon-if-type:olt-v-enet"}]}, {"name": "bbf-xpon-if-type:onu-v-vrefpoint", "value": 308, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:ethernet-like/bbf-xpon-if-type:onu-v-vrefpoint"}]}, {"name": "bbf-xpon-if-type:bbf-xpon-interface-type", "value": 309, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type"}]}, {"name": "bbf-xpon-if-type:channel-group", "value": 310, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:channel-group"}]}, {"name": "bbf-xpon-if-type:channel-partition", "value": 311, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:channel-partition"}]}, {"name": "bbf-xpon-if-type:channel-pair", "value": 312, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:channel-pair"}]}, {"name": "bbf-xpon-if-type:channel-termination", "value": 313, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:channel-termination"}]}, {"name": "bbf-xpon-if-type:ani", "value": 314, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:ani"}]}, {"name": "bbf-xpon-if-type:v-ani", "value": 315, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:v-ani"}]}, {"name": "huawei-tc-an:auto", "value": 316, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/huawei-tc-an:auto"}]}, {"name": "huawei-tc-an:vport", "value": 317, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/huawei-tc-an:vport"}]}, {"name": "huawei-tc-an:lport", "value": 318, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/huawei-tc-an:lport"}]}], "nullable": false}, {"name": "enabled", "type": "boolean", "nullable": true, "default": true}, {"name": "ingress-qos-policy-profile", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "egress-qos-policy-profile", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "bind-ni-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/huawei-network-instance-guangqi/network-instances/ni:network-instance.2/name"}]}, {"name": "statistics-accumulation", "type": "enum", "enumerate": [{"name": "enable", "value": 0}, {"name": "disable", "value": 1}, {"name": "system-conditional", "value": 2}], "nullable": true, "default": 2}, {"name": "default-vlan", "type": "uint16", "nullable": true, "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd')"}], "range": "1..4094"}, {"name": "ethernet", "type": "container", "fields": [{"name": "duplex", "type": "enum", "enumerate": [{"name": "full", "value": 0}, {"name": "half", "value": 1}, {"name": "unknown", "value": 2}], "nullable": true, "default": 0}, {"name": "speed", "type": "double", "nullable": true}, {"name": "route-switch", "type": "enum", "enumerate": [{"name": "unconcern", "value": 0}, {"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}], "default": 0}, {"name": "auto-negotiation", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": true}]}, {"name": "flow-control", "type": "container", "fields": [{"name": "force-flow-control", "type": "boolean", "nullable": true, "default": false}]}, {"name": "physical", "type": "container", "fields": [{"name": "loopback-mode", "type": "enum", "enumerate": [{"name": "no-loopback", "value": 0}, {"name": "inner-loopback", "value": 1}, {"name": "outer-loopback", "value": 2}], "nullable": true}, {"name": "mdi", "type": "enum", "enumerate": [{"name": "auto", "value": 1}, {"name": "normal", "value": 2}, {"name": "across", "value": 3}, {"name": "not-supported", "value": 4}], "nullable": true}, {"name": "port-type", "type": "enum", "enumerate": [{"name": "FE", "value": 1}, {"name": "GE", "value": 2}, {"name": "10GE", "value": 3}, {"name": "100GE", "value": 4}, {"name": "40GE", "value": 5}, {"name": "25GE", "value": 6}], "nullable": true}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "logical", "type": "container", "fields": [{"name": "tx-auto-off", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "nullable": true, "default": false}, {"name": "detect-time", "type": "int32", "nullable": true, "clause": [{"type": "when", "formula": "../enabled = true()"}], "range": "1..60", "default": 10}, {"name": "resume-detect-mode", "type": "enum", "enumerate": [{"name": "manual", "value": 0}, {"name": "auto", "value": 1}], "nullable": true, "clause": [{"type": "when", "formula": "../enabled = true()"}], "default": 0}, {"name": "resume-detect-interval", "type": "int32", "nullable": true, "clause": [{"type": "when", "formula": "../enabled = true() and ../resume-detect-mode = 'auto'"}], "range": "100..300", "default": 100}, {"name": "resume-detect-duration", "type": "int32", "nullable": true, "clause": [{"type": "when", "formula": "../enabled = true()"}], "range": "100..3000", "default": 2000}]}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "ethernet-frame", "type": "container", "fields": [{"name": "jumbo-frame", "type": "boolean", "nullable": true, "default": false}, {"name": "mtu", "type": "uint16", "nullable": true, "range": "1519..9280", "default": 2052}]}, {"name": "optical-module", "type": "container", "fields": [{"name": "optical-alarm-profile", "type": "string", "nullable": true}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "fec-mode", "type": "container", "fields": [{"name": "config-fec-mode", "type": "enum", "enumerate": [{"name": "unconcern", "value": 0}, {"name": "off", "value": 1}, {"name": "base-r", "value": 2}, {"name": "rs", "value": 3}], "nullable": true}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "stream-car-traffic-profile", "type": "container", "fields": [{"name": "upstream", "type": "string", "nullable": true}, {"name": "downstream", "type": "string", "nullable": true}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd')"}]}, {"name": "tx-auto-off-config", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "ipv4", "type": "container", "fields": [{"name": "bind-ni-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/huawei-network-instance-guangqi/network-instances/ni:network-instance.2/name"}]}]}, {"name": "subif-lower-layer", "type": "container", "fields": [{"name": "interface", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-if-type:sub-interface')"}]}, {"name": "frame-processing", "type": "choice", "fields": [{"name": "inline-frame-processing", "type": "case", "fields": [{"name": "inline-frame-processing", "type": "container", "fields": [{"name": "uplink-interface", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "when", "formula": "derived-from-or-self(../../../../type, 'bbf-if-type-enhance:vlan-connect-sub-interface')"}, {"type": "must", "formula": "/ietf-interfaces/interfaces/if:interface.1[name = current()]/type='iana-if-type:ethernetCsmacd'"}]}, {"name": "ingress-rule", "type": "container", "fields": []}]}]}, {"name": "frame-processing-profile", "type": "case", "fields": [{"name": "frame-processing-profile", "type": "string", "nullable": true}, {"name": "tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": false, "range": "0..4094"}]}, {"name": "tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": false, "range": "0..4094"}]}, {"name": "ingress-rewrite-tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": false, "range": "0..4094"}]}, {"name": "ingress-rewrite-tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": false, "range": "0..4094"}]}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-if-type:sub-interface')"}]}, {"name": "interface-usage", "type": "container", "fields": [{"name": "interface-usage", "type": "enum", "enumerate": [{"name": "user-port", "value": 0}, {"name": "network-port", "value": 1}, {"name": "subtended-node-port", "value": 2}, {"name": "inherit", "value": 3}, {"name": "dpu-port", "value": 4}], "nullable": true}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "mac-learning", "type": "container", "fields": [{"name": "max-number-mac-addresses", "type": "uint32", "nullable": true, "default": **********}, {"name": "number-committed-mac-addresses", "type": "uint32", "nullable": true, "default": 1}, {"name": "mac-learning-enable", "type": "boolean", "nullable": true, "default": true}, {"name": "mac-learning-failure-action", "type": "enum", "enumerate": [{"name": "forward", "value": 0}, {"name": "discard", "value": 1}], "nullable": true, "default": 0}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "l2-dhcpv4-relay", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": true}, {"name": "trusted-port", "type": "string", "nullable": true, "default": "false"}, {"name": "profile-ref", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../enable = true()"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type,'bbf-if-type:vlan-sub-interface')"}]}, {"name": "dhcpv6-ldra", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": true}, {"name": "trusted-port", "type": "boolean", "nullable": true, "default": true}, {"name": "profile-ref", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../enable = true()"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type,'bbf-if-type:vlan-sub-interface')"}]}, {"name": "pppoe", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": false}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type,'bbf-if-type:vlan-sub-interface')"}]}, {"name": "tm-root", "type": "container", "fields": [{"name": "tc-id-2-queue-id-mapping-profile-name", "type": "string", "nullable": true}, {"name": "shaper-name", "type": "string", "nullable": true}, {"name": "children-type", "type": "choice", "fields": [{"name": "queues", "type": "case", "fields": []}, {"name": "by-queue-policy-profile", "type": "case", "fields": [{"name": "queue-policy-profile", "type": "string", "nullable": true}], "clause": [{"type": "when", "formula": "(derived-from-or-self(../../../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../../../type, 'bbf-xpon-if-type:v-ani') or derived-from-or-self(../../../type, 'iana-if-type:aluEpon') or derived-from(../../../type, 'iana-if-type:gpon')) and not(../../tc-id-2-queue-id-mapping-profile-name)"}]}]}, {"name": "product-data", "type": "bytes", "nullable": true}]}, {"name": "qos-policies", "type": "container", "fields": [{"name": "policing", "type": "container", "fields": [{"name": "statistics", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "nullable": true, "default": false}]}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "subscriber-profile", "type": "container", "fields": [{"name": "profile", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "/huawei-network-instance-guangqi/network-instance/instances/ni:instance.1[name=/huawei-ifm-guangqi/ifm/interfaces/ifm:interface.1[name=current()]/vrf-name]/afs/l3vpn:af.1[type='ipv4-unicast']"}]}]}, {"name": "vsi-profile", "type": "container", "fields": [{"name": "vsi-profile", "type": "string", "nullable": true}, {"name": "tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "string", "nullable": true}]}, {"name": "tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "string", "nullable": true}]}, {"name": "ingress-rewrite-tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": true, "range": "0..4094"}]}, {"name": "ingress-rewrite-tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": true, "range": "0..4094"}]}, {"name": "egress-rewrite-tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "string", "nullable": true, "default": "vlan-id-from-match"}]}, {"name": "egress-rewrite-tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "string", "nullable": true, "default": "vlan-id-from-match"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface')"}]}, {"name": "channel-group", "type": "container", "fields": [{"name": "polling-period", "type": "uint32", "nullable": true, "range": "1..864000", "default": 100}], "clause": [{"type": "when", "formula": "../type = 'bbf-xpon-if-type:channel-group'"}]}, {"name": "channel-partition", "type": "container", "fields": [{"name": "channel-group-ref", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/ietf-interfaces/interfaces/if:interface.1[name=current()]/type,'bbf-xpon-if-type:channel-group')"}]}, {"name": "channel-partition-index", "type": "uint8", "nullable": true, "range": "0..15"}, {"name": "downstream-fec", "type": "boolean", "nullable": true, "default": true}, {"name": "closest-onu-distance", "type": "uint16", "nullable": true, "range": "0..40", "default": 0}, {"name": "maximum-differential-xpon-distance", "type": "uint16", "nullable": true, "range": "20|40", "default": 20}, {"name": "multicast-aes-indicator", "type": "boolean", "nullable": true, "default": false}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:channel-partition')"}]}, {"name": "channel-pair", "type": "container", "fields": [{"name": "channel-group-ref", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/ietf-interfaces/interfaces/if:interface.1[name=current()]/type,'bbf-xpon-if-type:channel-group')"}]}, {"name": "channel-partition-ref", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/ietf-interfaces/interfaces/if:interface.1[name=current()]/type, 'bbf-xpon-if-type:channel-partition') and /ietf-interfaces/interfaces/if:interface.1[name=current()]/channel-partition/channel-group-ref=../channel-group-ref"}]}, {"name": "channel-pair-type", "type": "identity", "enumerate": [{"name": "bbf-xpon-types:ngpon2-twdm", "value": 0, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:ngpon2-twdm"}]}, {"name": "bbf-xpon-types:ngpon2-ptp", "value": 1, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:ngpon2-ptp"}]}, {"name": "bbf-xpon-types:xgs", "value": 2, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:xgs"}]}, {"name": "bbf-xpon-types:xgpon", "value": 3, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:xgpon"}]}, {"name": "bbf-xpon-types:gpon", "value": 4, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:gpon"}]}, {"name": "huawei-xpon-types:hspon", "value": 5, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/huawei-xpon-types:hspon"}]}, {"name": "huawei-xpon-types:xgt", "value": 6, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/huawei-xpon-types:xgt"}]}], "nullable": false}, {"name": "tcont-groups", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:channel-pair'"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:channel-pair')"}]}, {"name": "channel-termination", "type": "container", "fields": [{"name": "channel-pair-ref", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "/ietf-interfaces/interfaces/if:interface.1[name=current()]/enabled = ../../enabled"}]}, {"name": "channel-termination-type", "type": "identity", "enumerate": [{"name": "bbf-xpon-types:ngpon2-twdm", "value": 0, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:ngpon2-twdm"}]}, {"name": "bbf-xpon-types:ngpon2-ptp", "value": 1, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:ngpon2-ptp"}]}, {"name": "bbf-xpon-types:xgs", "value": 2, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:xgs"}]}, {"name": "bbf-xpon-types:xgpon", "value": 3, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:xgpon"}]}, {"name": "bbf-xpon-types:gpon", "value": 4, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:gpon"}]}, {"name": "huawei-xpon-types:hspon", "value": 5, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/huawei-xpon-types:hspon"}]}, {"name": "huawei-xpon-types:xgt", "value": 6, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/huawei-xpon-types:xgt"}]}], "nullable": false}, {"name": "laser-switch", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:channel-termination'"}], "default": true}, {"name": "multicast-encrypt-mode", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}, {"name": "adaptive", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:channel-termination'"}], "default": 1}, {"name": "multicast-encrypt-algorithm", "type": "enum", "enumerate": [{"name": "aes128", "value": 1}, {"name": "aes256", "value": 2}, {"name": "sm4128", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../multicast-encrypt-mode = 'enable'"}], "default": 1}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:channel-termination')"}]}, {"name": "ani", "type": "container", "fields": [{"name": "upstream-fec", "type": "boolean", "nullable": true, "default": false}, {"name": "management-gemport-aes-indicator", "type": "boolean", "nullable": true, "default": true}, {"name": "isolate-switch", "type": "enum", "enumerate": [{"name": "isolate", "value": 1}, {"name": "unisolate", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}, {"name": "tr069-mngt-enable", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "default": false}, {"name": "tr069-ip-index", "type": "uint8", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "range": "0..7", "default": 0}, {"name": "transparent-enable", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "default": false}, {"name": "multicast-mode", "type": "enum", "enumerate": [{"name": "igmp-snooping", "value": 1}, {"name": "olt-control", "value": 2}, {"name": "unconcern", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "default": 3}, {"name": "mac-learning-enable", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "default": true}, {"name": "dummy-ont", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}, {"name": "fiber-route", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}, {"name": "ani-profile-ref", "type": "string", "nullable": true}, {"name": "uni-profile-ref", "type": "string", "nullable": true}, {"name": "power-management", "type": "container", "fields": [{"name": "power-management-profile-ref", "type": "string", "nullable": true}]}, {"name": "multicast-forward", "type": "choice", "fields": [{"name": "untag", "type": "case", "fields": [{"name": "untag", "type": "uint8", "nullable": true}]}, {"name": "tag", "type": "case", "fields": [{"name": "tag", "type": "choice", "fields": [{"name": "translation", "type": "case", "fields": [{"name": "translation", "type": "choice", "fields": [{"name": "vlan-id", "type": "case", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": true}]}, {"name": "igmp-user-vlan", "type": "case", "fields": [{"name": "igmp-user-vlan", "type": "uint8", "nullable": true}]}]}]}, {"name": "transparent", "type": "case", "fields": [{"name": "transparent", "type": "uint8", "nullable": true}]}]}]}, {"name": "unconcern", "type": "case", "default": true, "fields": [{"name": "unconcern", "type": "uint8", "nullable": true}]}], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}, {"name": "snmp-route", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}, {"name": "ont-port-bundle", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:ani')"}]}, {"name": "onu-v-enet", "type": "container", "fields": [{"name": "ani", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/ietf-interfaces/interfaces/if:interface.1[name = current()]/type,'bbf-xpon-if-type:ani')"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:onu-v-enet')"}]}, {"name": "onu-v-vrefpoint", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "derived-from-or-self(../type,'bbf-xpon-if-type:onu-v-vrefpoint')"}]}, {"name": "v-ani", "type": "container", "fields": [{"name": "channel-partition", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/ietf-interfaces/interfaces/if:interface.1[name=current()]/type,'bbf-xpon-if-type:channel-partition')"}]}, {"name": "expected-serial-number", "type": "string", "nullable": true}, {"name": "expected-registration-id", "type": "string", "nullable": true, "length": "0..72"}, {"name": "preferred-channel-pair", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/ietf-interfaces/interfaces/if:interface.1[name=current()]/type, 'bbf-xpon-if-type:channel-pair') and /ietf-interfaces/interfaces/if:interface.1[name=current()]/channel-pair/channel-partition-ref=../channel-partition and(not(../protection-channel-pair) or(current() != ../protection-channel-pair))"}]}, {"name": "protection-channel-pair", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/ietf-interfaces/interfaces/if:interface.1[name=current()]/type, 'bbf-xpon-if-type:channel-pair') and /ietf-interfaces/interfaces/if:interface.1[name=current()]/channel-pair/channel-partition-ref=../channel-partition"}]}, {"name": "upstream-channel-speed", "type": "uint64", "nullable": true}, {"name": "ont-mngt-mode", "type": "enum", "enumerate": [{"name": "omci", "value": 1}, {"name": "stand-alone", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 1}, {"name": "qos-mode", "type": "enum", "enumerate": [{"name": "priority-queue", "value": 1}, {"name": "gem-car", "value": 2}, {"name": "flow-car", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 1}, {"name": "loid", "type": "string", "nullable": true, "length": "1..24"}, {"name": "check-code", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../loid"}], "length": "1..12"}, {"name": "mapping-mode", "type": "enum", "enumerate": [{"name": "vlan", "value": 1}, {"name": "priority", "value": 2}, {"name": "vlan-priority", "value": 3}, {"name": "port", "value": 4}, {"name": "port-vlan", "value": 5}, {"name": "port-priority", "value": 6}, {"name": "port-vlan-priority", "value": 7}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 3}, {"name": "gem-encrypt-mode", "type": "enum", "enumerate": [{"name": "aes128", "value": 1}, {"name": "aes256", "value": 2}, {"name": "sm4128", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 1}, {"name": "tcont-group-index-ref", "type": "uint8", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "range": "0..7"}, {"name": "access-scenario", "type": "enum", "enumerate": [{"name": "auto-negotiation", "value": 0}, {"name": "p2mp", "value": 1}, {"name": "common", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}]}, {"name": "protectside-ont", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": false}, {"name": "register-timeout", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "range": "1..168"}, {"name": "changed-auth-effective-mode", "type": "enum", "enumerate": [{"name": "immediate", "value": 1}, {"name": "next-startup", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 1}, {"name": "optic-alm-threshold-prof", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "length": "0..32"}, {"name": "ont-alarm-policy-prof", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "length": "0..32"}, {"name": "ont-dot1x-prof", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "length": "0..32"}, {"name": "onu-tr069-profile-ref", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}]}, {"name": "onu-power-shedding-profile-ref", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:v-ani')"}]}, {"name": "olt-v-enet", "type": "container", "fields": [{"name": "lower-layer-interface", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/ietf-interfaces/interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/ietf-interfaces/interfaces/if:interface.1[name = current()]/type,'bbf-xpon-if-type:v-ani')"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:olt-v-enet')"}]}, {"name": "bridge-port", "type": "container", "fields": [{"name": "component-name", "type": "string", "nullable": true}, {"name": "port-type", "type": "identity", "enumerate": [{"name": "ieee802-dot1q-bridge:c-vlan-bridge-port", "value": 0, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:c-vlan-bridge-port"}]}, {"name": "ieee802-dot1q-bridge:provider-network-port", "value": 1, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:provider-network-port"}]}, {"name": "ieee802-dot1q-bridge:customer-network-port", "value": 2, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:customer-network-port"}]}, {"name": "ieee802-dot1q-bridge:customer-edge-port", "value": 3, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:customer-edge-port"}]}, {"name": "ieee802-dot1q-bridge:d-bridge-port", "value": 4, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:d-bridge-port"}]}, {"name": "ieee802-dot1q-bridge:remote-customer-access-port", "value": 5, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:remote-customer-access-port"}]}], "nullable": true}, {"name": "pvid", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "range": "1..4094|4096..**********", "default": 1}, {"name": "default-priority", "type": "uint8", "nullable": true, "range": "0..7", "default": 0}, {"name": "pcp-selection", "type": "enum", "enumerate": [{"name": "8P0D", "value": 0}, {"name": "7P1D", "value": 1}, {"name": "6P2D", "value": 2}, {"name": "5P3D", "value": 3}], "nullable": true, "default": 0}, {"name": "use-dei", "type": "boolean", "nullable": true, "default": false}, {"name": "drop-encoding", "type": "boolean", "nullable": true, "default": false}, {"name": "service-access-priority-selection", "type": "boolean", "nullable": true, "default": false}, {"name": "acceptable-frame", "type": "enum", "enumerate": [{"name": "admit-only-VLAN-tagged-frames", "value": 0}, {"name": "admit-only-untagged-and-priority-tagged", "value": 1}, {"name": "admit-all-frames", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": 2}, {"name": "enable-ingress-filtering", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": false}, {"name": "enable-restricted-vlan-registration", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": false}, {"name": "enable-vid-translation-table", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": false}, {"name": "enable-egress-vid-translation-table", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": false}, {"name": "admin-point-to-point", "type": "enum", "enumerate": [{"name": "force-true", "value": 1}, {"name": "force-false", "value": 2}, {"name": "auto", "value": 3}], "nullable": true}, {"name": "priority-regeneration", "type": "container", "fields": [{"name": "priority0", "type": "uint8", "nullable": true, "range": "0..7", "default": 0}, {"name": "priority1", "type": "uint8", "nullable": true, "range": "0..7", "default": 1}, {"name": "priority2", "type": "uint8", "nullable": true, "range": "0..7", "default": 2}, {"name": "priority3", "type": "uint8", "nullable": true, "range": "0..7", "default": 3}, {"name": "priority4", "type": "uint8", "nullable": true, "range": "0..7", "default": 4}, {"name": "priority5", "type": "uint8", "nullable": true, "range": "0..7", "default": 5}, {"name": "priority6", "type": "uint8", "nullable": true, "range": "0..7", "default": 6}, {"name": "priority7", "type": "uint8", "nullable": true, "range": "0..7", "default": 7}]}, {"name": "pcp-decoding-table", "type": "container", "fields": []}, {"name": "pcp-encoding-table", "type": "container", "fields": []}, {"name": "service-access-priority", "type": "container", "fields": [{"name": "priority0", "type": "uint8", "nullable": true, "range": "0..7", "default": 0}, {"name": "priority1", "type": "uint8", "nullable": true, "range": "0..7", "default": 1}, {"name": "priority2", "type": "uint8", "nullable": true, "range": "0..7", "default": 2}, {"name": "priority3", "type": "uint8", "nullable": true, "range": "0..7", "default": 3}, {"name": "priority4", "type": "uint8", "nullable": true, "range": "0..7", "default": 4}, {"name": "priority5", "type": "uint8", "nullable": true, "range": "0..7", "default": 5}, {"name": "priority6", "type": "uint8", "nullable": true, "range": "0..7", "default": 6}, {"name": "priority7", "type": "uint8", "nullable": true, "range": "0..7", "default": 7}]}, {"name": "traffic-class", "type": "container", "fields": []}], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:bridge' or ../type = 'iana-if-type:ethernetCsmacd' or ../type = 'iana-if-type:ieee8023adLag'or ../type = 'iana-if-type:ilan'"}]}, {"name": "aggregator", "type": "container", "fields": [{"name": "agg-system-name", "type": "string", "nullable": true}, {"name": "work-mode", "type": "enum", "enumerate": [{"name": "static", "value": 0}, {"name": "lacp", "value": 1}], "nullable": true, "default": 0}, {"name": "fast-period", "type": "uint32", "nullable": true, "range": "1..10", "default": 1}, {"name": "slow-period", "type": "uint32", "nullable": true, "range": "20..40", "default": 30}, {"name": "max-link-number", "type": "string", "nullable": true, "default": "no-limit"}, {"name": "least-link-number", "type": "string", "nullable": true, "default": "no-limit"}, {"name": "forward-mode", "type": "enum", "enumerate": [{"name": "ingress", "value": 0}, {"name": "egress-ingress", "value": 1}, {"name": "mpls-label", "value": 3}, {"name": "src-dst-ip", "value": 4}, {"name": "ip-enhance", "value": 5}], "nullable": true, "default": 0}, {"name": "preempt-enabled", "type": "boolean", "nullable": true, "default": false}, {"name": "preempt-delay", "type": "uint16", "nullable": true, "range": "0..180", "default": 0}, {"name": "aggregator-lacp", "type": "container", "fields": [{"name": "actor-admin-key", "type": "uint16", "nullable": true, "range": "1..65535"}]}, {"name": "ports", "type": "container", "fields": []}], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:ieee8023adLag' or ../type = 'iana-if-type:ethernetCsmacd' or ../type = 'iana-if-type:bridge'"}]}, {"name": "aggregation-port", "type": "container", "fields": [{"name": "aggregation-port-lacp", "type": "container", "fields": [{"name": "actor-port-priority", "type": "uint16", "nullable": true, "default": 16384}, {"name": "actor-admin-state", "type": "string", "nullable": true}]}], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:ethernetCsmacd' or ../type = 'iana-if-type:bridge'"}]}, {"name": "tpid", "type": "container", "fields": [{"name": "s-vlan-tpid", "type": "enum", "enumerate": [{"name": "dot1q", "value": 1}, {"name": "dot1ad", "value": 2}], "nullable": true}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd')"}]}, {"name": "onu-catv", "type": "container", "fields": [{"name": "frequency", "type": "enum", "enumerate": [{"name": "all-pass", "value": 0}, {"name": "high-pass", "value": 1}, {"name": "low-pass", "value": 2}], "nullable": true, "default": 0}], "clause": [{"type": "when", "formula": "../type = 'bbf-uni-profile:catv'"}]}, {"name": "xpon-port", "type": "container", "fields": [{"name": "ont-auto-find-switch", "type": "boolean", "nullable": true, "default": false}, {"name": "optic-alarm-profile", "type": "string", "nullable": true, "length": "0..32"}, {"name": "ont-password-renew-interval", "type": "string", "nullable": true, "default": "1440"}, {"name": "multi-channel-low-latency-switch", "type": "boolean", "nullable": true}, {"name": "asymmetric-onu-up-rate", "type": "enum", "enumerate": [{"name": "12dot5g-rate", "value": 1}, {"name": "25g-rate", "value": 2}], "nullable": true}, {"name": "dba-bandwidth-assignment-mode", "type": "enum", "enumerate": [{"name": "max-bandwidth-usage", "value": 1}, {"name": "min-loop-delay", "value": 2}, {"name": "default", "value": 3}, {"name": "assigned-period", "value": 4}], "nullable": true}, {"name": "multicast-encrypt-mode", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}, {"name": "adaptive", "value": 3}], "nullable": true, "default": 1}, {"name": "dba-surplus-assign", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "max-guaranteed-bandwidth", "type": "string", "nullable": true, "default": "unlimited"}, {"name": "dba-low-latency", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "ont-online-power-threshold", "type": "string", "nullable": true, "default": "unlimited"}, {"name": "multicast-encrypt-algorithm", "type": "enum", "enumerate": [{"name": "aes128", "value": 1}, {"name": "aes256", "value": 2}, {"name": "sm4128", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../multicast-encrypt-mode = 'enable'"}], "default": 1}, {"name": "traffic-alarm-profile-ref", "type": "string", "nullable": true}, {"name": "regeneration-switch", "type": "boolean", "nullable": true, "default": false}], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:gpon'"}]}, {"name": "product-data", "type": "bytes", "nullable": true}], "keys": [{"name": "if:interface.1.PK", "index": {"type": "primary"}, "node": "if:interface.1", "fields": ["PID", "name"], "constraints": {"unique": true}}, {"name": "if:interface.1.SK", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "if:interface.1", "fields": ["product-data"]}]}, {"name": "if:AllType", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "../F25 = ''"}]}, {"name": "F2", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "../F25 = ''"}]}, {"name": "F3", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "../F26 + ../F0 > 100"}]}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "char", "nullable": true}, {"name": "F6", "type": "uchar", "nullable": true}, {"name": "F7", "type": "int8", "nullable": true}, {"name": "F8", "type": "uint8", "nullable": true}, {"name": "F9", "type": "int16", "nullable": true}, {"name": "F10", "type": "uint16", "nullable": true}, {"name": "F11", "type": "int32", "nullable": true}, {"name": "F12", "type": "uint32", "nullable": true}, {"name": "F13", "type": "int64", "nullable": true}, {"name": "F14", "type": "uint64", "nullable": true}, {"name": "F15", "type": "boolean", "nullable": true}, {"name": "F16", "type": "float", "nullable": true}, {"name": "F17", "type": "double", "nullable": true}, {"name": "F18", "type": "time", "nullable": true}, {"name": "F19", "type": "string", "nullable": true, "size": 7}, {"name": "F20", "type": "bytes", "nullable": true, "size": 7}, {"name": "F21", "type": "fixed", "nullable": true, "size": 7}, {"name": "F22", "type": "bitmap", "nullable": false, "size": 128}, {"name": "F23", "type": "enum", "nullable": true, "enumerate_identity": "F23", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"name": "F24", "type": "identity", "nullable": true, "enumerate_identity": "F24", "enumerate": [{"name": "level1", "value": 1, "derived-paths": [{"derived-path": "level1"}]}, {"name": "level2", "value": 2, "derived-paths": [{"derived-path": "level1/level2"}]}, {"name": "level3", "value": 3, "derived-paths": [{"derived-path": "level1/level2/level3"}]}, {"name": "level0", "value": 0, "derived-paths": [{"derived-path": "level0"}]}, {"name": "level-1", "value": -1, "derived-paths": [{"derived-path": "level-1"}]}, {"name": "level-2", "value": -2, "derived-paths": [{"derived-path": "level-1/level-2"}]}, {"name": "level-3", "value": -3, "derived-paths": [{"derived-path": "level-1/level-2/level-3"}]}]}, {"name": "F25", "type": "empty", "nullable": true, "clause": [{"type": "when", "formula": "../F0 > 0"}]}, {"name": "F26", "type": "union", "nullable": true, "union_types": ["int32", "uint32", "int64", "uint64", "string", "boolean", "empty"]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance-guangqi", "type": "container", "config": {"check_validity": true}, "is_root": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "network-instance", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "cfg-router-id", "type": "string", "nullable": true}, {"name": "as-notation-plain", "type": "boolean", "nullable": true, "default": false}, {"name": "route-distinguisher-auto-ip", "type": "string", "nullable": true}, {"name": "vpn-instance-limit", "type": "uint32", "nullable": true, "range": "1..**********"}]}, {"name": "instances", "type": "container", "fields": []}]}, {"name": "network-instances", "type": "container", "fields": []}], "keys": [{"name": "huawei-network-instance-guangqi.PK", "index": {"type": "primary"}, "node": "huawei-network-instance-guangqi", "fields": ["ID"], "constraints": {"unique": true}}]}, {"name": "ni:network-instance.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "enabled", "type": "boolean", "nullable": true, "default": true}, {"name": "description", "type": "string", "nullable": true}, {"name": "ni-type", "type": "choice", "fields": []}, {"name": "root-type", "type": "choice", "fields": [{"name": "vrf-root", "type": "case", "fields": [{"name": "vrf-root", "type": "container", "fields": []}]}, {"name": "vsi-root", "type": "case", "fields": [{"name": "vsi-root", "type": "container", "fields": []}]}, {"name": "vv-root", "type": "case", "fields": [{"name": "vv-root", "type": "container", "fields": []}]}]}], "keys": [{"name": "ni:network-instance.2.PK", "index": {"type": "primary"}, "node": "ni:network-instance.2", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "ni:instance.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..31"}, {"name": "description", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "not(../name='_public_')"}], "length": "1..242"}, {"name": "traffic-statistic-enable", "type": "boolean", "nullable": true, "default": false}, {"name": "parameter", "type": "container", "fields": [{"name": "identifier", "type": "string", "nullable": true, "clause": [{"type": "must", "formula": "count(/huawei-network-instance-guangqi/network-instance/instances/ni:instance.1[parameter/identifier=current()])=1"}], "length": "3..15"}, {"name": "vn-id", "type": "uint32", "nullable": true, "range": "1..16777215"}]}, {"name": "afs", "type": "container", "fields": []}], "keys": [{"name": "ni:instance.1.PK", "index": {"type": "primary"}, "node": "ni:instance.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "huawei-ifm-guangqi", "type": "container", "config": {"check_validity": true}, "is_root": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "ifm", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "statistic-interval", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "(../statistic-interval) mod 10 = 0"}], "range": "10..600", "default": 300}, {"name": "ipv4-ignore-primary-sub", "type": "boolean", "nullable": true, "default": false}, {"name": "sub-interface-link-trap-enable", "type": "boolean", "nullable": true, "default": true}, {"name": "ipv4-conflict-enable", "type": "container", "fields": [{"name": "preempt-enable", "type": "boolean", "nullable": true, "default": false}], "clause": [{"type": "must", "formula": "../ipv4-ignore-primary-sub=false()"}]}, {"name": "ipv6-conflict-enable", "type": "container", "fields": [{"name": "preempt-enable", "type": "boolean", "nullable": true, "default": false}]}]}, {"name": "damp", "type": "container", "fields": [{"name": "tx-off", "type": "boolean", "nullable": true, "default": false}, {"name": "level", "type": "choice", "fields": [{"name": "auto", "type": "case", "default": true, "fields": [{"name": "auto", "type": "container", "fields": [{"name": "level", "type": "enum", "enumerate": [{"name": "light", "value": 0}, {"name": "middle", "value": 1}, {"name": "heavy", "value": 2}], "nullable": true, "default": 0}]}]}, {"name": "manual", "type": "case", "fields": [{"name": "manual", "type": "container", "fields": [{"name": "suppress", "type": "uint32", "nullable": false, "clause": [{"type": "must", "formula": "(../suppress>../reuse)"}], "range": "1..20000"}, {"name": "reuse", "type": "uint32", "nullable": false, "clause": [{"type": "must", "formula": "(../reuse<../suppress)"}], "range": "1..20000"}, {"name": "max-suppress-time", "type": "uint16", "nullable": false, "range": "1..255"}, {"name": "half-life-period", "type": "uint16", "nullable": false, "range": "1..60"}]}]}]}]}, {"name": "auto-recovery-times", "type": "container", "fields": []}, {"name": "interfaces", "type": "container", "fields": []}, {"name": "if-group-policys", "type": "container", "fields": []}]}], "keys": [{"name": "huawei-ifm-guangqi.PK", "index": {"type": "primary"}, "node": "huawei-ifm-guangqi", "fields": ["ID"], "constraints": {"unique": true}}]}, {"name": "ifm:interface.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..63"}, {"name": "class", "type": "enum", "enumerate": [{"name": "main-interface", "value": 0}, {"name": "sub-interface", "value": 1}], "nullable": true}, {"name": "type", "type": "enum", "enumerate": [{"name": "Ethernet", "value": 0}, {"name": "GigabitEthernet", "value": 2}, {"name": "Eth-Trunk", "value": 4}, {"name": "Ip-Trunk", "value": 5}, {"name": "Virtual-Ethernet", "value": 6}, {"name": "Serial", "value": 8}, {"name": "Pos", "value": 9}, {"name": "C<PERSON>s", "value": 10}, {"name": "ATM", "value": 11}, {"name": "Tunnel", "value": 14}, {"name": "<PERSON><PERSON><PERSON>", "value": 15}, {"name": "NULL", "value": 18}, {"name": "LoopBack", "value": 19}, {"name": "100GE", "value": 20}, {"name": "Lmpif", "value": 21}, {"name": "MTunnel", "value": 22}, {"name": "40GE", "value": 23}, {"name": "10GE", "value": 24}, {"name": "GEBrief", "value": 25}, {"name": "MEth", "value": 26}, {"name": "Stack-Port", "value": 27}, {"name": "<PERSON>p", "value": 28}, {"name": "E1", "value": 30}, {"name": "Mp-group", "value": 31}, {"name": "Ima-group", "value": 32}, {"name": "VMEth", "value": 33}, {"name": "Remote-Ap", "value": 35}, {"name": "VBridge", "value": 36}, {"name": "Atm-Bundle", "value": 37}, {"name": "Fiber-Channel", "value": 38}, {"name": "Infiniband", "value": 39}, {"name": "Vbdif", "value": 40}, {"name": "T1", "value": 41}, {"name": "T3", "value": 42}, {"name": "VC4", "value": 43}, {"name": "VC12", "value": 44}, {"name": "Global-VE", "value": 45}, {"name": "Fabric-Port", "value": 46}, {"name": "E3", "value": 48}, {"name": "Vp", "value": 49}, {"name": "DcnInterface", "value": 50}, {"name": "Cpos-Trunk", "value": 51}, {"name": "Trunk-Serial", "value": 52}, {"name": "Global-Mp-Group", "value": 53}, {"name": "Otn", "value": 55}, {"name": "Global-Ima-Group", "value": 57}, {"name": "Pos-Trunk", "value": 59}, {"name": "Gmpls-Uni", "value": 63}, {"name": "Wdm", "value": 64}, {"name": "Nve", "value": 65}, {"name": "FCoE-Port", "value": 67}, {"name": "Virtual-Template", "value": 68}, {"name": "FC", "value": 70}, {"name": "4x10GE", "value": 71}, {"name": "10x10GE", "value": 72}, {"name": "3x40GE", "value": 73}, {"name": "4x25GE", "value": 74}, {"name": "25GE", "value": 75}, {"name": "IMEth", "value": 79}, {"name": "PW-VE", "value": 88}, {"name": "VX-Tunnel", "value": 89}, {"name": "ServiceIf", "value": 90}, {"name": "XGigabitEthernet", "value": 91}, {"name": "200GE", "value": 92}, {"name": "Virtual-ODUk", "value": 94}, {"name": "FlexE", "value": 95}, {"name": "FlexE-200GE", "value": 96}, {"name": "50|100GE", "value": 101}, {"name": "50GE", "value": 102}, {"name": "FlexE-50G", "value": 103}, {"name": "FlexE-100G", "value": 104}, {"name": "FlexE-50|100G", "value": 105}, {"name": "Virtual-Serial", "value": 107}, {"name": "400GE", "value": 108}, {"name": "HP-GE", "value": 114}, {"name": "FlexE-400G", "value": 115}, {"name": "Virtual-if", "value": 116}, {"name": "Cellular", "value": 117}, {"name": "FgClient", "value": 120}, {"name": "MultiGE", "value": 124}, {"name": "FlexE-10G", "value": 125}, {"name": "OpticalAmplifier", "value": 126}, {"name": "10G-FG", "value": 127}, {"name": "<PERSON><PERSON><PERSON>", "value": 128}, {"name": "1200GE", "value": 129}, {"name": "200GUB", "value": 131}, {"name": "400GUB", "value": 132}, {"name": "200GUB-E", "value": 133}, {"name": "400GUB-E", "value": 134}, {"name": "200GUBR", "value": 135}, {"name": "400GUBR", "value": 136}, {"name": "200GUBR-E", "value": 137}, {"name": "400GUBR-E", "value": 138}, {"name": "200GHL", "value": 139}, {"name": "400GHL", "value": 140}, {"name": "800GE", "value": 141}, {"name": "Wlan-Bss", "value": 143}, {"name": "50-100GE", "value": 144}, {"name": "FlexE-50-100G", "value": 145}, {"name": "200GUBG", "value": 146}, {"name": "400GUBG", "value": 147}, {"name": "200GUBG-E", "value": 148}, {"name": "400GUBG-E", "value": 149}, {"name": "OpenGE", "value": 150}, {"name": "VGE", "value": 151}], "nullable": true}, {"name": "parent-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/huawei-ifm-guangqi/ifm/interfaces/ifm:interface.1/name"}, {"type": "when", "formula": "../class='sub-interface'"}]}, {"name": "number", "type": "string", "nullable": true, "length": "1..63"}, {"name": "description", "type": "string", "nullable": true, "length": "1..242"}, {"name": "admin-status", "type": "enum", "enumerate": [{"name": "down", "value": 0}, {"name": "up", "value": 1}], "nullable": true}, {"name": "link-protocol", "type": "enum", "enumerate": [{"name": "ethernet", "value": 0}, {"name": "ppp", "value": 1}, {"name": "hdlc", "value": 2}, {"name": "fr", "value": 3}, {"name": "atm", "value": 6}, {"name": "tdm", "value": 7}], "nullable": true}, {"name": "router-type", "type": "enum", "enumerate": [{"name": "PtoP", "value": 0}, {"name": "PtoMP", "value": 1}, {"name": "broadcast", "value": 2}, {"name": "NBMA", "value": 3}, {"name": "invalid", "value": 255}], "nullable": true}, {"name": "clear-ip-df", "type": "boolean", "nullable": true, "default": false}, {"name": "link-up-down-trap-enable", "type": "boolean", "nullable": true, "default": true}, {"name": "statistic-enable", "type": "boolean", "nullable": true}, {"name": "statistic-mode", "type": "enum", "enumerate": [{"name": "interface-based", "value": 1}, {"name": "vlan-group-based", "value": 2}], "nullable": true, "clause": [{"type": "must", "formula": "../statistic-mode = 'interface-based' or(../l2-mode-enable = true() and ../statistic-enable = true())"}], "default": 1}, {"name": "mtu", "type": "uint32", "nullable": true, "range": "0..64000"}, {"name": "spread-mtu-flag", "type": "boolean", "nullable": true, "default": false}, {"name": "statistic-interval", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "(../statistic-interval) mod 10 = 0"}], "range": "10..600"}, {"name": "vrf-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/huawei-network-instance-guangqi/network-instance/instances/ni:instance.1/name"}], "default": "_public_"}, {"name": "l2-mode-enable", "type": "boolean", "nullable": true, "default": false}, {"name": "down-delay-time", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "../type='Eth-Trunk' and ../class='main-interface'"}, {"type": "must", "formula": "(../down-delay-time) mod 100 = 0"}], "range": "0..1000", "default": 0}, {"name": "mac-address", "type": "string", "nullable": true, "length": "0..255"}, {"name": "network-layer-status", "type": "enum", "enumerate": [{"name": "ipv4-ipv6-up", "value": 0}, {"name": "ipv4-ipv6-down", "value": 196608}], "nullable": true, "default": 0}, {"name": "protocol-up-delay-time", "type": "uint32", "nullable": true, "range": "0..7200"}, {"name": "bandwidth-config-effect-service", "type": "uint8", "nullable": true}, {"name": "mac-address-system", "type": "uint8", "nullable": true, "clause": [{"type": "when", "formula": "../type='Vlanif'"}, {"type": "must", "formula": "not(../mac-address)"}]}, {"name": "bandwidth-type", "type": "choice", "fields": [{"name": "bandwidth-mbps", "type": "case", "fields": [{"name": "bandwidth", "type": "uint32", "nullable": true, "range": "1..1000000"}]}, {"name": "bandwidth-kbps", "type": "case", "fields": [{"name": "bandwidth-kbps", "type": "uint32", "nullable": true, "range": "1..1000000"}]}]}, {"name": "control-flap", "type": "container", "fields": [{"name": "suppress", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "(../suppress>../reuse) and((../suppress<../ceiling))"}], "range": "1..20000", "default": 2000}, {"name": "reuse", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "(../reuse<../suppress)"}], "range": "1..20000", "default": 750}, {"name": "ceiling", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "(../ceiling>../suppress)"}], "range": "1001..20000", "default": 6000}, {"name": "decay-ok", "type": "uint32", "nullable": true, "range": "1..900", "default": 54}, {"name": "decay-ng", "type": "uint32", "nullable": true, "range": "1..900", "default": 54}]}, {"name": "trap-threshold", "type": "container", "fields": [{"name": "input-rising-rate", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "not(../input-rising-rate and ../input-resume-rate and ../input-rising-rate<../input-resume-rate)"}], "range": "1..100", "default": 100}, {"name": "input-resume-rate", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "not(../input-rising-rate and ../input-resume-rate and ../input-rising-rate<../input-resume-rate)"}], "range": "1..100", "default": 100}, {"name": "output-rising-rate", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "not(../output-rising-rate and ../output-resume-rate and ../output-rising-rate<../output-resume-rate)"}], "range": "1..100", "default": 100}, {"name": "output-resume-rate", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "not(../output-rising-rate and ../output-resume-rate and ../output-rising-rate<../output-resume-rate)"}], "range": "1..100", "default": 100}]}, {"name": "damping", "type": "choice", "fields": [{"name": "ignore-damp", "type": "case", "default": true, "fields": [{"name": "ignore-damp", "type": "container", "fields": [{"name": "ignore-global-damp", "type": "boolean", "nullable": true, "default": false}]}]}, {"name": "damp", "type": "case", "fields": [{"name": "damp", "type": "container", "fields": [{"name": "tx-off", "type": "boolean", "nullable": true, "default": false}, {"name": "level", "type": "choice", "fields": [{"name": "auto", "type": "case", "default": true, "fields": [{"name": "auto", "type": "container", "fields": [{"name": "level", "type": "enum", "enumerate": [{"name": "light", "value": 0}, {"name": "middle", "value": 1}, {"name": "heavy", "value": 2}], "nullable": true, "default": 0}]}]}, {"name": "manual", "type": "case", "fields": [{"name": "manual", "type": "container", "fields": [{"name": "suppress", "type": "uint32", "nullable": false, "clause": [{"type": "must", "formula": "(../suppress>../reuse)"}], "range": "1..20000"}, {"name": "reuse", "type": "uint32", "nullable": false, "clause": [{"type": "must", "formula": "(../reuse<../suppress)"}], "range": "1..20000"}, {"name": "max-suppress-time", "type": "uint16", "nullable": false, "range": "1..255"}, {"name": "half-life-period", "type": "uint16", "nullable": false, "range": "1..60"}]}]}]}]}]}]}, {"name": "mode-flexe", "type": "container", "fields": [{"name": "bandwidth", "type": "uint32", "nullable": true, "range": "2..1000000000", "default": 2}], "clause": [{"type": "when", "formula": "../class='sub-interface' and ../type='GigabitEthernet'"}]}, {"name": "mode-channelize", "type": "container", "fields": [{"name": "bandwidth", "type": "uint32", "nullable": true, "range": "2..1000000000", "default": 2}], "clause": [{"type": "when", "formula": "../class='sub-interface'"}]}, {"name": "tunnel-protocol", "type": "container", "fields": [{"name": "type", "type": "choice", "fields": []}], "clause": [{"type": "when", "formula": "../type='Tunnel'"}]}, {"name": "vlanif-attribute", "type": "container", "fields": [{"name": "damping-time", "type": "uint16", "nullable": true, "range": "0..20", "default": 0}, {"name": "band-width-type", "type": "choice", "fields": [{"name": "band-width-mbps", "type": "case", "default": true, "fields": [{"name": "band-width", "type": "uint32", "nullable": true, "range": "1..1000000", "default": 1000}]}, {"name": "band-width-kbps", "type": "case", "fields": [{"name": "band-width-kbps", "type": "uint32", "nullable": true, "range": "1..1000000"}]}]}]}, {"name": "trunk", "type": "container", "fields": [{"name": "min-up-num", "type": "uint16", "nullable": true, "range": "1..256", "default": 1}, {"name": "min-up-bandwidth", "type": "uint32", "nullable": true, "range": "1..**********"}, {"name": "max-up-num", "type": "uint16", "nullable": true, "range": "1..256"}, {"name": "type", "type": "enum", "enumerate": [{"name": "eth-trunk", "value": 1}, {"name": "ip-trunk", "value": 2}], "nullable": true}, {"name": "hash-type", "type": "enum", "enumerate": [{"name": "ip", "value": 1}, {"name": "mac", "value": 2}, {"name": "packet-all", "value": 3}, {"name": "des-ip", "value": 4}, {"name": "des-<PERSON>", "value": 5}, {"name": "src-ip", "value": 6}, {"name": "src-mac", "value": 7}, {"name": "enhanced", "value": 8}, {"name": "resilient", "value": 9}, {"name": "invalid", "value": 10}, {"name": "l4", "value": 11}, {"name": "random", "value": 12}, {"name": "round-robin", "value": 13}, {"name": "symmetric", "value": 14}, {"name": "symmetric-complement", "value": 15}, {"name": "dynamic-profile", "value": 16}], "nullable": true}, {"name": "work-mode", "type": "enum", "enumerate": [{"name": "manual", "value": 1}, {"name": "dynamic", "value": 2}, {"name": "static", "value": 3}, {"name": "backup", "value": 4}, {"name": "invalid", "value": 5}], "nullable": true, "default": 1}, {"name": "smart-link-flush-vlan", "type": "uint16", "nullable": true, "range": "1..4094"}, {"name": "inactive-port-shutdown", "type": "boolean", "nullable": true, "default": false}, {"name": "preempt-enable", "type": "boolean", "nullable": true, "default": false}, {"name": "preempt-delay-minutes", "type": "uint16", "nullable": true, "range": "0..30", "default": 0}, {"name": "preempt-delay-seconds", "type": "uint8", "nullable": true, "range": "0..59", "default": 0}, {"name": "preempt-delay-milliseconds", "type": "uint16", "nullable": true, "range": "0..999", "default": 0}, {"name": "members", "type": "container", "fields": []}]}, {"name": "ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "vlan-swap", "type": "enum", "enumerate": [{"name": "disable", "value": 0}, {"name": "enable", "value": 1}], "nullable": true, "default": "disable"}, {"name": "qinq-protocol", "type": "string", "nullable": true, "length": "1..6", "default": "0x8100"}, {"name": "l2-mode", "type": "enum", "enumerate": [{"name": "disable", "value": 0}, {"name": "enable", "value": 1}], "nullable": true, "default": "enable"}, {"name": "encapsulation", "type": "container", "fields": [{"name": "pvid", "type": "uint16", "nullable": true, "range": "0..4094"}]}, {"name": "l2-attribute", "type": "container", "fields": [{"name": "link-type", "type": "enum", "enumerate": [{"name": "access", "value": 1}, {"name": "trunk", "value": 2}, {"name": "hybrid", "value": 3}, {"name": "dot1qtunnel", "value": 4}], "nullable": true, "default": "hybrid"}, {"name": "pvid", "type": "uint16", "nullable": true}, {"name": "untag-vlans", "type": "string", "nullable": true, "length": "1..max"}, {"name": "untag-discarding", "type": "boolean", "nullable": true, "default": false}, {"name": "set-trunk-vlans", "type": "choice", "fields": [{"name": "vlan-range", "type": "case", "fields": [{"name": "trunk-vlans", "type": "string", "nullable": true, "length": "1..max"}]}, {"name": "vlan-id-list", "type": "case", "fields": []}]}, {"name": "vlan-classification", "type": "container", "fields": [{"name": "ip-subnet-vlan", "type": "uint8", "nullable": true}]}, {"name": "vlan-stackings", "type": "container", "fields": []}, {"name": "vlan-stacking-remarks", "type": "container", "fields": []}, {"name": "vlan-stacking-tags", "type": "container", "fields": []}, {"name": "vlan-stacking-remark-tags", "type": "container", "fields": []}, {"name": "vlan-stacking-8021p-filters", "type": "container", "fields": []}, {"name": "vlan-stacking-8021p-remarks", "type": "container", "fields": []}, {"name": "vlan-stacking-untag", "type": "container", "fields": [{"name": "stacking-vlan", "type": "uint16", "nullable": true, "range": "1..4094"}, {"name": "stacking-inner-vlan", "type": "uint16", "nullable": true, "range": "1..4094"}]}, {"name": "vlan-mappings", "type": "container", "fields": []}, {"name": "vlan-mapping-remarks", "type": "container", "fields": []}, {"name": "vlan-mapping-singles", "type": "container", "fields": []}, {"name": "vlan-mapping-doubles", "type": "container", "fields": []}, {"name": "vlan-mapping-double-to-singles", "type": "container", "fields": []}, {"name": "statistics-enable-vlans", "type": "container", "fields": [{"name": "vlans", "type": "string", "nullable": true, "length": "1..max"}]}, {"name": "mux-vlan", "type": "container", "fields": [{"name": "vlan-list", "type": "string", "nullable": true, "length": "1..max"}]}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}, {"name": "port-am-isolates", "type": "container", "fields": []}]}, {"name": "l3-sub-interface", "type": "container", "fields": [{"name": "vlan-groups", "type": "container", "fields": []}, {"name": "flow-type", "type": "choice", "fields": [{"name": "vlan-type", "type": "case", "fields": [{"name": "vlan-type-dot1q", "type": "container", "fields": [{"name": "vlan-type-vid", "type": "uint16", "nullable": false, "range": "1..4094"}]}]}, {"name": "dot1q-termination", "type": "case", "fields": [{"name": "dot1q-termination", "type": "container", "fields": [{"name": "tag", "type": "enum", "enumerate": [{"name": "none", "value": 0}, {"name": "local-switch", "value": 1}, {"name": "rt-protocol", "value": 2}], "nullable": false}, {"name": "arp-broadcast-enable", "type": "boolean", "nullable": true, "default": false}, {"name": "dot1q-vlans", "type": "container", "fields": [{"name": "dot1q-vlans", "type": "container", "fields": [{"name": "vlan-list", "type": "string", "nullable": false, "length": "1..max"}]}]}, {"name": "dot1q-vlans-policy", "type": "container", "fields": [{"name": "policy-vlans", "type": "container", "fields": []}, {"name": "policy-vlan-groups", "type": "container", "fields": []}]}, {"name": "vrrps", "type": "container", "fields": []}, {"name": "pwtag", "type": "container", "fields": [{"name": "tag-vid-mode", "type": "enum", "enumerate": [{"name": "null", "value": 0}, {"name": "outer-vlan", "value": 1}, {"name": "inner-vlan", "value": 2}, {"name": "user-define", "value": 3}], "nullable": false}, {"name": "tag-vid", "type": "uint16", "nullable": false, "range": "1..4094"}, {"name": "tag-8021p-mode", "type": "enum", "enumerate": [{"name": "null", "value": 0}, {"name": "outer-vlan", "value": 1}, {"name": "inner-vlan", "value": 2}, {"name": "user-define", "value": 3}], "nullable": true, "default": "null"}, {"name": "tag-8021p-value", "type": "uint8", "nullable": false, "range": "0..7"}]}]}]}, {"name": "qinq-termination", "type": "case", "fields": [{"name": "qinq-termination", "type": "container", "fields": [{"name": "tag", "type": "enum", "enumerate": [{"name": "none", "value": 0}, {"name": "local-switch", "value": 1}, {"name": "rt-protocol", "value": 2}], "nullable": false}, {"name": "arp-broadcast-enable", "type": "boolean", "nullable": true, "default": false}, {"name": "remark-8021p", "type": "container", "fields": [{"name": "remark-type", "type": "enum", "enumerate": [{"name": "precedence", "value": 0}, {"name": "trust-ce-8021p", "value": 1}, {"name": "trust-pe-8021p", "value": 2}], "nullable": true, "default": "trust-pe-8021p"}, {"name": "precedence-value", "type": "uint8", "nullable": true, "range": "0..7"}]}, {"name": "symmetry-mode", "type": "container", "fields": [{"name": "symmetry-type", "type": "enum", "enumerate": [{"name": "asymmetry", "value": 0}, {"name": "symmetry", "value": 1}, {"name": "transparent", "value": 2}, {"name": "default", "value": 3}], "nullable": true, "default": "asymmetry"}, {"name": "user-mode", "type": "enum", "enumerate": [{"name": "disable", "value": 0}, {"name": "enable", "value": 1}], "nullable": true, "default": "disable"}]}, {"name": "qinq-vids", "type": "container", "fields": []}, {"name": "vrrps", "type": "container", "fields": []}, {"name": "pwtag", "type": "container", "fields": [{"name": "tag-vid-mode", "type": "enum", "enumerate": [{"name": "null", "value": 0}, {"name": "outer-vlan", "value": 1}, {"name": "inner-vlan", "value": 2}, {"name": "user-define", "value": 3}], "nullable": false}, {"name": "tag-vid", "type": "uint16", "nullable": false, "range": "1..4094"}, {"name": "tag-8021p-mode", "type": "enum", "enumerate": [{"name": "null", "value": 0}, {"name": "outer-vlan", "value": 1}, {"name": "inner-vlan", "value": 2}, {"name": "user-define", "value": 3}], "nullable": true, "default": "null"}, {"name": "tag-8021p-value", "type": "uint8", "nullable": false, "range": "0..7"}]}]}]}, {"name": "qinq-stacking", "type": "case", "fields": [{"name": "qinq-stacking", "type": "container", "fields": [{"name": "pe-vid", "type": "uint16", "nullable": true, "range": "1..4094"}, {"name": "qinq-stacking-vid-pevids", "type": "container", "fields": []}]}]}, {"name": "qinq-mapping", "type": "case", "fields": [{"name": "qinq-mapping", "type": "container", "fields": [{"name": "mapping-vids", "type": "container", "fields": []}, {"name": "mapping-singles", "type": "container", "fields": []}, {"name": "mapping-double-to-singles", "type": "container", "fields": []}]}]}, {"name": "vlan-type-policy", "type": "case", "fields": [{"name": "vlan-type-policy", "type": "container", "fields": [{"name": "vlan-type-vid", "type": "uint16", "nullable": false, "range": "1..4094"}, {"name": "policy-type", "type": "enum", "enumerate": [{"name": "8021p", "value": 1}, {"name": "dscp", "value": 2}, {"name": "pppoe", "value": 3}, {"name": "default", "value": 4}], "nullable": false}, {"name": "value-8021p", "type": "string", "nullable": false, "length": "1..max"}, {"name": "dscp-values", "type": "string", "nullable": false, "length": "1..max"}]}]}, {"name": "qinq-stacking-policy", "type": "case", "fields": [{"name": "stacking-policy", "type": "container", "fields": [{"name": "policy-vlans", "type": "container", "fields": []}, {"name": "policy-vlan-groups", "type": "container", "fields": []}]}]}, {"name": "untag-policy", "type": "case", "fields": [{"name": "untag-policy", "type": "container", "fields": [{"name": "policy-type", "type": "enum", "enumerate": [{"name": "8021p", "value": 1}, {"name": "dscp", "value": 2}, {"name": "pppoe", "value": 3}, {"name": "default", "value": 4}], "nullable": false}, {"name": "dscp-values", "type": "string", "nullable": false, "length": "1..max"}]}]}, {"name": "user-vlan-comm", "type": "case", "fields": [{"name": "user-vlan-common", "type": "container", "fields": [{"name": "user-vlan-qinqs", "type": "container", "fields": [{"name": "descriptions", "type": "container", "fields": []}]}, {"name": "user-vlan-dot1q", "type": "container", "fields": [{"name": "vlan-list", "type": "string", "nullable": false, "length": "1..max"}, {"name": "descriptions", "type": "container", "fields": []}]}]}]}, {"name": "user-vlan-any", "type": "case", "fields": [{"name": "user-vlan-any", "type": "container", "fields": []}]}]}]}, {"name": "l2-sub-interface", "type": "container", "fields": [{"name": "local-switch", "type": "enum", "enumerate": [{"name": "disable", "value": 0}, {"name": "enable", "value": 1}], "nullable": true, "default": "disable"}, {"name": "flow-type", "type": "choice", "fields": [{"name": "default", "type": "case", "fields": [{"name": "default", "type": "container", "fields": []}]}, {"name": "untag", "type": "case", "fields": [{"name": "untag", "type": "container", "fields": []}]}, {"name": "dot1q", "type": "case", "fields": [{"name": "dot1q", "type": "container", "fields": [{"name": "policy", "type": "choice", "fields": [{"name": "no-policy", "type": "case", "fields": [{"name": "vlans", "type": "container", "fields": [{"name": "vlan-list", "type": "string", "nullable": true, "length": "1..max"}]}]}, {"name": "policy", "type": "case", "fields": [{"name": "policy-vlans", "type": "container", "fields": []}]}]}]}]}, {"name": "qinq", "type": "case", "fields": [{"name": "qinqs", "type": "container", "fields": [{"name": "qinq-vids", "type": "container", "fields": []}, {"name": "qinq-vid-ce-default", "type": "container", "fields": [{"name": "pe-vlan-list", "type": "string", "nullable": true, "length": "1..max"}]}, {"name": "qinq-with-pe-segments", "type": "container", "fields": []}]}]}]}, {"name": "flow-action", "type": "container", "fields": [{"name": "action-type", "type": "enum", "enumerate": [{"name": "pop-outer", "value": 0}, {"name": "push1-layer", "value": 1}, {"name": "swap", "value": 2}, {"name": "map-offset-increase", "value": 3}, {"name": "map-1-to-1", "value": 4}, {"name": "map-2-to-1", "value": 5}, {"name": "map-1-to-2", "value": 6}, {"name": "map-2-to-2", "value": 7}, {"name": "pop-double", "value": 8}, {"name": "push2-layer", "value": 9}, {"name": "map-offset-decrease", "value": 10}, {"name": "map-single-outbound", "value": 12}, {"name": "map-double-outbound", "value": 16}, {"name": "push1-layer-outbound-loose", "value": 17}, {"name": "push2-layer-outbound-loose", "value": 18}], "nullable": false}, {"name": "out-vlan-id", "type": "uint32", "nullable": false, "range": "1..4094"}, {"name": "inner-vlan-id", "type": "uint32", "nullable": false, "range": "1..4094"}, {"name": "outer-8021p", "type": "uint8", "nullable": true, "range": "0..7"}, {"name": "inner-8021p", "type": "uint8", "nullable": true, "range": "0..7"}, {"name": "offset-value", "type": "uint16", "nullable": true, "range": "1..4093"}]}]}]}, {"name": "vethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "trunk-vlans", "type": "string", "nullable": true, "length": "1..max"}]}]}, {"name": "bdif-attribute", "type": "container", "fields": [{"name": "damping-time", "type": "uint16", "nullable": true, "range": "0..20", "default": 0}, {"name": "band-width-type", "type": "choice", "fields": [{"name": "band-width-mbps", "type": "case", "default": true, "fields": [{"name": "band-width", "type": "uint32", "nullable": true, "range": "1..1000000", "default": 1000}]}, {"name": "band-width-kbps", "type": "case", "fields": [{"name": "band-width-kbps", "type": "uint32", "nullable": true, "range": "1..1000000"}]}]}]}], "keys": [{"name": "ifm:interface.1.PK", "index": {"type": "primary"}, "node": "ifm:interface.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "l3vpn:af.1", "type": "list", "config": {"check_validity": true}, "max-elements": 2, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "type", "type": "enum", "enumerate": [{"name": "ipv4-unicast", "value": 1}, {"name": "ipv6-unicast", "value": 5}], "nullable": false}, {"name": "route-distinguisher", "type": "string", "nullable": true, "length": "3..21"}, {"name": "export-policy-add-ert-first", "type": "boolean", "nullable": true, "default": false}, {"name": "label-mode", "type": "enum", "enumerate": [{"name": "per-route", "value": 0}, {"name": "per-instance", "value": 1}, {"name": "per-nexthop", "value": 2}], "nullable": true, "default": "per-instance"}, {"name": "static-label", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "../label-mode = 'per-instance'"}], "range": "16..1048575"}, {"name": "vpn-frr", "type": "boolean", "nullable": true, "default": false}, {"name": "tunnel-policy", "type": "string", "nullable": true}, {"name": "transit-vpn", "type": "boolean", "nullable": true, "default": false}, {"name": "lsp-operation", "type": "enum", "enumerate": [{"name": "pop", "value": 0}, {"name": "pop-go", "value": 1}], "nullable": true, "clause": [{"type": "must", "formula": "../label-mode and ../label-mode!='per-instance' or ../lsp-operation='pop'"}], "default": "pop"}, {"name": "default-color", "type": "uint32", "nullable": true, "range": "0..**********"}, {"name": "mpls-routing-disable", "type": "boolean", "nullable": true, "default": false}, {"name": "local-cross-unicast-enable", "type": "boolean", "nullable": true, "default": true}, {"name": "import-policy-type", "type": "choice", "fields": [{"name": "rtp-ref", "type": "case", "fields": [{"name": "import-policy", "type": "string", "nullable": true}]}, {"name": "xpl-ref", "type": "case", "fields": [{"name": "import-filter", "type": "container", "fields": [{"name": "name", "type": "string", "nullable": true}, {"name": "parameter", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../name"}], "length": "3..1609"}]}]}]}, {"name": "export-policy-type", "type": "choice", "fields": [{"name": "rtp-ref", "type": "case", "fields": [{"name": "export-policy", "type": "string", "nullable": true}]}, {"name": "xpl-ref", "type": "case", "fields": [{"name": "export-filter", "type": "container", "fields": [{"name": "name", "type": "string", "nullable": true}, {"name": "parameter", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../name"}], "length": "3..1609"}]}]}]}, {"name": "vpn-targets", "type": "container", "fields": []}], "keys": [{"name": "l3vpn:af.1.PK", "index": {"type": "primary"}, "node": "l3vpn:af.1", "fields": ["PID", "type"], "constraints": {"unique": true}}]}]