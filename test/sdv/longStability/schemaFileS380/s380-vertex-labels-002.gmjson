[{"name": "ietf-yang-library", "alias": "ietf-yang-library", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ietf-yang-library:yang-library", "type": "container", "is_config": false, "fields": [{"name": "content-id", "type": "string", "is_config": false}]}, {"name": "ietf-yang-library:modules-state", "type": "container", "is_config": false, "fields": [{"name": "module-set-id", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set", "alias": "module-set", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module", "alias": "module", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}, {"name": "namespace", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::location", "alias": "location", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule", "alias": "submodule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule::location", "alias": "location", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::feature", "alias": "feature", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "feature"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::deviation", "alias": "deviation", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "deviation", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "deviation"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module", "alias": "import-only-module", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false, "nullable": false}, {"name": "namespace", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "revision"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::location", "alias": "location", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "alias": "submodule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "alias": "location", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::schema", "alias": "schema", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::schema::module-set", "alias": "module-set", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "module-set", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "module-set"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::datastore", "alias": "datastore", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "schema", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "notifications", "alias": "notifications", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "notifications:notification", "type": "container", "is_config": false, "fields": [{"name": "eventTime", "type": "string", "is_config": false}]}, {"name": "notifications:create-subscription", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "stream", "type": "string", "is_config": false, "default": "NETCONF"}, {"name": "startTime", "type": "string", "is_config": false}, {"name": "stopTime", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "nc-notifications", "alias": "nc-notifications", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "nc-notifications:netconf", "type": "container", "is_config": false, "fields": [{"name": "streams", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "nc-notifications:netconf::streams::stream", "alias": "stream", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "description", "type": "string", "is_config": false}, {"name": "replaySupport", "type": "boolean", "is_config": false}, {"name": "replayLogCreationTime", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-wired-port-profile", "alias": "huawei-wlan-wired-port-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-wired-port-profile:wlan-wired-port-profile", "type": "container", "fields": [{"name": "wiredport-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile", "alias": "wiredport-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "description", "type": "string"}, {"name": "mode", "type": "string", "default": "auto"}, {"name": "user-isolate", "type": "string", "default": "disable"}, {"name": "pvid", "type": "uint16"}, {"name": "tagged-vlan", "type": "string"}, {"name": "untagged-vlan", "type": "string", "default": "1"}, {"name": "eth-trunk", "type": "uint8"}, {"name": "learn-client-address-ipv4", "type": "boolean", "default": false}, {"name": "learn-client-address-ipv6", "type": "boolean", "default": false}, {"name": "dai", "type": "boolean", "default": false}, {"name": "ipsg", "type": "boolean", "default": false}, {"name": "igmp-snooping", "type": "boolean", "default": false}, {"name": "dhcp-trust-port", "type": "boolean", "default": true}, {"name": "stp", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "auto"}, {"name": "auto-shutdown-enable", "type": "boolean", "default": false}, {"name": "auto-shutdown-recovery-time", "type": "uint16", "default": 600, "clause": [{"type": "when", "formula": "../auto-shutdown-enable = true()"}]}]}, {"name": "port-security", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}, {"name": "max-mac-num", "type": "uint8", "clause": [{"type": "when", "formula": "../enable = true()"}]}, {"name": "protect-action", "type": "string", "default": "restrict", "clause": [{"type": "when", "formula": "../enable = true()"}]}, {"name": "mac-address-sticky", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../enable = true()"}]}]}, {"name": "traffic-optimize", "type": "container", "fields": [{"name": "tcp-adjust-mss", "type": "uint16"}]}, {"name": "traffic-filters", "type": "container", "fields": []}, {"name": "huawei-wlan-portlink-profile:binding-portlink-profile", "type": "container", "fields": [{"name": "binding-portlink-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-portlink-profile/huawei-wlan-portlink-profile:wlan-portlink-profile/portlink-profiles/portlink-profile/profile-name"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile::traffic-filters::traffic-filter", "alias": "traffic-filter", "type": "list", "max-elements": 16, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "ipv4-acl-number", "type": "uint32", "nullable": false}, {"name": "l2-acl-number", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "direction", "ipv4-acl-number", "l2-acl-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "not(./ipv4-acl-number = 0 and ./l2-acl-number = 0)"}]}, {"name": "huawei-wlan-portlink-profile", "alias": "huawei-wlan-portlink-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-portlink-profile:wlan-portlink-profile", "type": "container", "fields": [{"name": "portlink-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-portlink-profile:wlan-portlink-profile::portlink-profiles::portlink-profile", "alias": "portlink-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "poe", "type": "container", "fields": [{"name": "disable", "type": "boolean", "default": false}, {"name": "force-power", "type": "boolean", "default": false}, {"name": "legacy", "type": "boolean", "default": false}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp", "alias": "huawei-arp", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-arp:arp", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "strict-learn-enable", "type": "boolean", "default": false}, {"name": "expire-time", "type": "uint32", "default": 1200}, {"name": "gateway-dup-enable", "type": "boolean", "default": false}]}, {"name": "query-entries", "type": "container", "is_config": false, "fields": []}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-arp:arp-entry-clear", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "clear-methods", "type": "choice", "is_config": false, "nullable": false, "fields": [{"name": "clear-types", "type": "case", "is_config": false, "fields": [{"name": "clear-type", "type": "string", "is_config": false}]}, {"name": "if-names", "type": "case", "is_config": false, "fields": [{"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-addr", "type": "string", "is_config": false}]}, {"name": "slots", "type": "case", "is_config": false, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "nullable": false}]}]}]}, {"name": "huawei-arp:arp-send-detect", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ipv4-address", "type": "string", "is_config": false, "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp:arp::query-entries::query-entry", "alias": "query-entry", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ni-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-addr", "type": "string", "is_config": false, "nullable": false}, {"name": "mac-addr", "type": "string", "is_config": false}, {"name": "style-type", "type": "string", "is_config": false}, {"name": "if-name", "type": "string", "is_config": false}, {"name": "pe-vlan", "type": "uint16", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ni-name", "ip-addr"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp:arp::statistics::statistic", "alias": "statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "packets-received", "type": "uint32", "is_config": false}, {"name": "miss-received", "type": "uint32", "is_config": false}, {"name": "learn-count", "type": "uint32", "is_config": false}, {"name": "packets-drop-limit", "type": "uint32", "is_config": false}, {"name": "packets-drop-other", "type": "uint32", "is_config": false}, {"name": "miss-drop-limit", "type": "uint32", "is_config": false}, {"name": "miss-drop-other", "type": "uint32", "is_config": false}, {"name": "packets-drop-speedlmt", "type": "uint32", "is_config": false}, {"name": "packets-proxy-supp", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-mac", "alias": "hua<PERSON>-mac", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-mac:mac", "type": "container", "fields": [{"name": "global-attribute", "type": "container", "fields": [{"name": "aging-time", "type": "uint32", "default": 300}]}, {"name": "global-mac-usage", "type": "container", "fields": [{"name": "mac-threshold", "type": "uint32", "default": 90}]}, {"name": "vlan-dynamic-macs", "type": "container", "is_config": false, "fields": []}, {"name": "mac-statistics", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-mac:reset-dynamic-macs-by-interface", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "interface-name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-mac:reset-vlan-dynamic-macs", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "vlan-id", "type": "uint16", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "alias": "vlan-dynamic-mac", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "vlan-id", "type": "uint16", "is_config": false, "nullable": false}, {"name": "address", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false}, {"name": "out-interface-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "vlan-id", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mac:mac::mac-statistics::mac-statistic", "alias": "mac-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "total", "type": "uint32", "is_config": false}, {"name": "black-hole", "type": "uint32", "is_config": false}, {"name": "static", "type": "uint32", "is_config": false}, {"name": "dynamic", "type": "uint32", "is_config": false}, {"name": "security", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mstp", "alias": "huawei-mstp", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-mstp:mstp", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "bpdu-filter", "type": "boolean", "default": false}, {"name": "edge-port", "type": "boolean", "default": false}]}, {"name": "default-process", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}, {"name": "mode", "type": "string", "default": "mstp"}, {"name": "path-cost-standard", "type": "string", "default": "dot1t"}, {"name": "timer-factor", "type": "uint8", "default": 3}, {"name": "forward-delay", "type": "uint16", "default": 1500, "clause": [{"type": "must", "formula": "(../forward-delay) mod 100 = 0"}]}, {"name": "hello-time", "type": "uint16", "default": 200, "clause": [{"type": "must", "formula": "(../hello-time) mod 100 = 0"}]}, {"name": "max-age", "type": "uint16", "default": 2000, "clause": [{"type": "must", "formula": "(../max-age) mod 100 = 0"}]}, {"name": "default-instance", "type": "container", "fields": [{"name": "priority", "type": "uint32", "default": 32768, "clause": [{"type": "must", "formula": "(../priority) mod 4096 = 0"}, {"type": "when", "formula": "../root-type = 'normal'"}]}, {"name": "root-type", "type": "string", "default": "normal"}]}, {"name": "cist-info", "type": "container", "is_config": false, "fields": [{"name": "bridge-id", "type": "string", "is_config": false}, {"name": "root-id", "type": "string", "is_config": false}, {"name": "erpc-cost", "type": "uint32", "is_config": false}, {"name": "regroot-id", "type": "string", "is_config": false}, {"name": "irpc-cost", "type": "uint32", "is_config": false}, {"name": "active-hello-time", "type": "uint16", "is_config": false}, {"name": "active-max-age", "type": "uint16", "is_config": false}, {"name": "active-forward-delay", "type": "uint16", "is_config": false}, {"name": "active-max-hops", "type": "uint16", "is_config": false}, {"name": "root-port-id", "type": "string", "is_config": false}, {"name": "tc-tcn-receive-num", "type": "uint32", "is_config": false}, {"name": "tc-count-pre-hello", "type": "uint32", "is_config": false}, {"name": "last-change-interval", "type": "string", "is_config": false}, {"name": "tc-number", "type": "uint32", "is_config": false}, {"name": "is-topo-changed", "type": "boolean", "is_config": false}, {"name": "cist-port-infos", "type": "container", "is_config": false, "fields": []}]}, {"name": "interface-bpdu-statistics", "type": "container", "is_config": false, "fields": []}]}, {"name": "error-packet-statistic", "type": "container", "is_config": false, "fields": [{"name": "time", "type": "string", "is_config": false}, {"name": "count", "type": "uint32", "is_config": false}, {"name": "content", "type": "string", "is_config": false}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mstp:mstp::default-process::cist-info::cist-port-infos::cist-port-info", "alias": "cist-port-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "port-role", "type": "string", "is_config": false}, {"name": "port-state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mstp:mstp::default-process::interface-bpdu-statistics::interface-bpdu-statistic", "alias": "interface-bpdu-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "instance-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "port-name", "type": "string", "is_config": false, "nullable": false}, {"name": "tc-send", "type": "uint32", "is_config": false}, {"name": "tc-receive", "type": "uint32", "is_config": false}, {"name": "tc-discard", "type": "uint32", "is_config": false}, {"name": "tcn-send", "type": "uint32", "is_config": false}, {"name": "tcn-receive", "type": "uint32", "is_config": false}, {"name": "tcn-discard", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "instance-id", "port-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-loadbalance", "alias": "huawei-loadbalance", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-loadbalance:loadbalance", "type": "container", "fields": [{"name": "global-attribute", "type": "container", "fields": [{"name": "unequal-cost", "type": "choice", "fields": [{"name": "ucmp", "type": "case", "default": true, "fields": [{"name": "ucmp-enable", "type": "boolean", "default": false}]}, {"name": "ucmp-bandwidth-config", "type": "case", "fields": [{"name": "ucmp-bandwidth-config-enable", "type": "string"}]}]}]}, {"name": "ip-load-balance", "type": "container", "fields": [{"name": "mode", "type": "string", "default": "flow"}, {"name": "src-ip", "type": "boolean", "default": true, "clause": [{"type": "when", "formula": "(../mode = 'flow')"}]}, {"name": "dst-ip", "type": "boolean", "default": true, "clause": [{"type": "when", "formula": "(../mode = 'flow')"}]}, {"name": "src-port", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "(../mode = 'flow')"}]}, {"name": "dst-port", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "(../mode = 'flow')"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ppp-net", "alias": "huawei-ppp-net", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-ppp-net:ppp-net", "type": "container", "fields": [{"name": "ppp-dampening", "type": "container", "presence": true, "fields": [{"name": "dampen-level", "type": "string", "default": "light"}, {"name": "half-life-period", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "../dampen-level = 'manual'"}]}, {"name": "suppress-value", "type": "uint16", "nullable": false, "clause": [{"type": "must", "formula": "../suppress-value >= ../reuse-value"}, {"type": "when", "formula": "../dampen-level = 'manual'"}]}, {"name": "reuse-value", "type": "uint16", "nullable": false, "clause": [{"type": "must", "formula": "../suppress-value >= ../reuse-value"}, {"type": "when", "formula": "../dampen-level = 'manual'"}]}, {"name": "max-suppress-time", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "../dampen-level = 'manual'"}]}]}, {"name": "load-balance-identify-pppoe", "type": "container", "fields": [{"name": "load-balance-enable", "type": "boolean", "default": false}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pppoe-client", "alias": "huawei-pppoe-client", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-pppoe-client:reset-pppoe-client-session", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "session", "type": "choice", "is_config": false, "fields": [{"name": "all-session", "type": "case", "is_config": false, "fields": [{"name": "all", "type": "boolean", "is_config": false, "nullable": false}]}, {"name": "specified-interface-session", "type": "case", "is_config": false, "fields": [{"name": "interface", "type": "string", "is_config": false, "nullable": false}]}]}]}, {"name": "huawei-pppoe-client:get-pppoe-client-bind-info", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-routing", "alias": "huawei-routing", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-routing:reset-all-route-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "address-family", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap", "alias": "huawei-wlan-ap", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-ap:wlan-ap", "type": "container", "fields": [{"name": "ap-auth-mode", "type": "string", "default": "mac"}, {"name": "ap-mac-whitelists", "type": "container", "fields": []}, {"name": "ap-sn-whitelists", "type": "container", "fields": []}, {"name": "ap-mac-blacklists", "type": "container", "fields": []}, {"name": "ap-types", "type": "container", "fields": []}, {"name": "temp-mgmt-psk", "type": "string"}, {"name": "ap-instances", "type": "container", "fields": []}, {"name": "wmi-server-infos", "type": "container", "fields": []}, {"name": "ap-provision", "type": "container", "fields": [{"name": "address-mode", "type": "string"}, {"name": "ip-address-info", "type": "container", "presence": true, "fields": [{"name": "address-type", "type": "choice", "fields": [{"name": "ipv4", "type": "case", "fields": [{"name": "ap-ip-address", "type": "container", "fields": [{"name": "ap-ip-address", "type": "string", "nullable": false}, {"name": "sub-net-mask", "type": "uint8", "nullable": false}, {"name": "gate-way", "type": "string"}], "clause": [{"type": "when", "formula": "../../../../address-mode = 'static'"}]}, {"name": "ap-ipv4-list", "type": "container", "fields": [{"name": "ac-ipv4-list", "type": "string"}]}]}, {"name": "ipv6", "type": "case", "fields": [{"name": "ap-ipv6-address", "type": "container", "fields": [{"name": "ap-ip-v6-address", "type": "string", "nullable": false}, {"name": "prefix-length", "type": "uint32", "nullable": false}, {"name": "gate-way", "type": "string"}], "clause": [{"type": "when", "formula": "../../../../address-mode = 'static'"}]}, {"name": "ap-ipv6-list", "type": "container", "fields": [{"name": "ac-ipv6-list", "type": "string"}]}]}]}]}, {"name": "management-vlan", "type": "uint32"}, {"name": "ap-mode", "type": "string"}, {"name": "server-auth-switch", "type": "string"}, {"name": "server-auth-cn-lists", "type": "container", "fields": []}]}, {"name": "unauthed-aps", "type": "container", "is_config": false, "fields": []}, {"name": "register-aps", "type": "container", "is_config": false, "fields": []}, {"name": "port-lldp-statistics", "type": "container", "is_config": false, "fields": []}, {"name": "port-statistics", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-wlan-ap:reset-ap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "reset-info", "type": "choice", "is_config": false, "fields": [{"name": "ap-mac-info", "type": "case", "is_config": false, "fields": [{"name": "ap-mac", "type": "string", "is_config": false}]}, {"name": "ap-id-info", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "ap-group-info", "type": "case", "is_config": false, "fields": [{"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "ap-type-info", "type": "case", "is_config": false, "fields": [{"name": "ap-type", "type": "string", "is_config": false}]}, {"name": "wait-rtu-validate", "type": "case", "is_config": false, "fields": [{"name": "wait-rtu-validate", "type": "string", "is_config": false}]}]}]}, {"name": "huawei-wlan-ap:reset-ap-with-factory-configuration", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "reset-info", "type": "choice", "is_config": false, "fields": [{"name": "ap-mac-info", "type": "case", "is_config": false, "fields": [{"name": "ap-mac", "type": "string", "is_config": false}]}, {"name": "ap-id-info", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}]}]}, {"name": "huawei-wlan-ap:led-blink-time", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-type", "type": "string", "is_config": false, "nullable": false}, {"name": "time", "type": "uint32", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../oper-type = 'blink'"}]}, {"name": "ap-info", "type": "choice", "is_config": false, "nullable": false, "fields": [{"name": "ap-id", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "ap-mac", "type": "case", "is_config": false, "fields": [{"name": "ap-mac", "type": "string", "is_config": false}]}, {"name": "all", "type": "case", "is_config": false, "fields": [{"name": "ap-all", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "../../../oper-type = 'cancel'"}]}]}]}]}, {"name": "huawei-wlan-ap:ap-provision-commit", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "commit-type", "type": "choice", "is_config": false, "fields": [{"name": "ap-id-type", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "ap-group-type", "type": "case", "is_config": false, "fields": [{"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "all-type", "type": "case", "is_config": false, "fields": [{"name": "all", "type": "string", "is_config": false}]}]}]}, {"name": "huawei-wlan-ap:ap-license-notify", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "license-infos", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-wlan-ap:notify-ap-report-lldp-data", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-instances", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-wlan-ap:notify-ap-report-state", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "choice", "is_config": false, "fields": [{"name": "all-ap", "type": "case", "is_config": false, "fields": [{"name": "all-ap", "type": "string", "is_config": false}]}, {"name": "ap-id-info", "type": "case", "is_config": false, "fields": [{"name": "ap-instances", "type": "container", "is_config": false, "fields": []}]}]}]}, {"name": "huawei-wlan-ap:register-status-ack", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-instances", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-wlan-ap:get-ap-connect-fail-reason", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-instances", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-wlan-ap:reset-ap-connect-fail-reason", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-mac", "type": "string", "is_config": false}]}, {"name": "huawei-wlan-ap:set-auth-svr-down-vap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-type", "type": "string", "is_config": false, "nullable": false}, {"name": "vap-profile", "type": "string", "is_config": false}]}, {"name": "huawei-wlan-ap:ap-ping", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "host", "type": "string", "is_config": false, "nullable": false}, {"name": "packet-count", "type": "uint32", "is_config": false, "default": 4}, {"name": "packet-size", "type": "uint32", "is_config": false, "default": 56}, {"name": "interval", "type": "uint32", "is_config": false, "default": 2000}, {"name": "timeout", "type": "uint32", "is_config": false, "default": 2000}]}, {"name": "huawei-wlan-ap:get-ap-ping-result", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-wlan-ap:ap-diagnostic-information", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-mac-whitelists::ap-mac-whitelist", "alias": "ap-mac-whitelist", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-mac", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-sn-whitelists::ap-sn-whitelist", "alias": "ap-sn-whitelist", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-sn", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-sn"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-mac-blacklists::ap-mac-blacklist", "alias": "ap-mac-blacklist", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-mac", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-types::ap-type", "alias": "ap-type", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type-name", "type": "string", "nullable": false}, {"name": "type-id", "type": "uint8", "nullable": false}, {"name": "state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "k1", "fields": [":pid", "type-id"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "alias": "ap-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-id", "type": "uint32", "nullable": false}, {"name": "ap-mac", "type": "string"}, {"name": "ap-sn", "type": "string"}, {"name": "ap-type", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-ap/huawei-wlan-ap:wlan-ap/ap-types/ap-type/type-name"}]}, {"name": "ap-name", "type": "string"}, {"name": "location", "type": "string"}, {"name": "scene", "type": "string"}, {"name": "site-code", "type": "string"}, {"name": "ap-provision", "type": "container", "fields": [{"name": "address-mode", "type": "string"}, {"name": "ip-address-info", "type": "container", "presence": true, "fields": [{"name": "address-type", "type": "choice", "fields": [{"name": "ipv4", "type": "case", "fields": [{"name": "ap-ip-address", "type": "container", "fields": [{"name": "ap-ip-address", "type": "string", "nullable": false}, {"name": "sub-net-mask", "type": "uint8", "nullable": false}, {"name": "gate-way", "type": "string"}], "clause": [{"type": "when", "formula": "../../../../address-mode = 'static'"}]}, {"name": "ap-ipv4-list", "type": "container", "fields": [{"name": "ac-ipv4-list", "type": "string"}]}]}, {"name": "ipv6", "type": "case", "fields": [{"name": "ap-ipv6-address", "type": "container", "fields": [{"name": "ap-ip-v6-address", "type": "string", "nullable": false}, {"name": "prefix-length", "type": "uint32", "nullable": false}, {"name": "gate-way", "type": "string"}], "clause": [{"type": "when", "formula": "../../../../address-mode = 'static'"}]}, {"name": "ap-ipv6-list", "type": "container", "fields": [{"name": "ac-ipv6-list", "type": "string"}]}]}]}]}, {"name": "management-vlan", "type": "uint32"}, {"name": "ap-mode", "type": "string"}, {"name": "server-auth-switch", "type": "string"}, {"name": "server-auth-cn-lists", "type": "container", "fields": []}]}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "vendor", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}, {"name": "run-state", "type": "string", "is_config": false}, {"name": "ap-group", "type": "string", "is_config": false}, {"name": "central-ap-id", "type": "uint32", "is_config": false}, {"name": "central-ap-mac", "type": "string", "is_config": false}, {"name": "central-ap-name", "type": "string", "is_config": false}, {"name": "huawei-wlan-ap-group-profile:binding-group", "type": "container", "fields": [{"name": "binding-group", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-ap-group-profile/huawei-wlan-ap-group-profile:wlan-ap-group-profile/ap-group-profiles/ap-group-profile/ap-group-name"}]}]}, {"name": "huawei-wlan-ap-radio:radio-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-reg-dom-profile:ap-binding-regular-domain-profile", "type": "container", "fields": [{"name": "binding-regular-domain-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-reg-dom-profile/huawei-wlan-reg-dom-profile:wlan-reg-dom-profile/regulatory-domain-profiles/regulatory-domain-profile/profile-name"}]}]}, {"name": "huawei-wlan-system-profile:binding-system-profile", "type": "container", "fields": [{"name": "binding-system-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-system-profile/huawei-wlan-system-profile:wlan-system-profile/system-profiles/system-profile/profile-name"}]}]}, {"name": "huawei-wlan-wired-port-profile:eth-trunk-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-wired-port-profile:ethernet-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-wired-port-profile:ge-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-wired-port-profile:multige-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-wired-port-profile:xge-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "ap-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "k1", "fields": [":pid", "ap-mac"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}, {"name": "k2", "fields": [":pid", "ap-sn"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}], "clause": [{"type": "must", "formula": "(./ap-mac) or (./ap-sn)"}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::ap-provision::server-auth-cn-lists::server-auth-cn-list", "alias": "server-auth-cn-list", "type": "list", "max-elements": 5, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "cn", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "cn"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-radio:radio-instances::radio-instance", "alias": "radio-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "radio-id", "type": "uint8", "nullable": false}, {"name": "radio-switch", "type": "string"}, {"name": "eirp", "type": "uint8", "default": 255}, {"name": "channel-info", "type": "container", "presence": true, "fields": [{"name": "bandwidth", "type": "choice", "nullable": false, "fields": [{"name": "bandwidth-20mhz", "type": "case", "fields": [{"name": "bandwidth-20mhz", "type": "string"}]}, {"name": "bandwidth-40mhz-minus", "type": "case", "fields": [{"name": "bandwidth-40mhz-minus", "type": "string"}]}, {"name": "bandwidth-40mhz-plus", "type": "case", "fields": [{"name": "bandwidth-40mhz-plus", "type": "string"}]}, {"name": "bandwidth-80mhz", "type": "case", "fields": [{"name": "bandwidth-80mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-80plus80mhz", "type": "case", "fields": [{"name": "bandwidth-80plus80mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-160mhz", "type": "case", "fields": [{"name": "bandwidth-160mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-320mhz", "type": "case", "fields": [{"name": "bandwidth-320mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../frequency = '5G') or (../../../frequency = '6G')"}]}]}, {"name": "channel", "type": "uint8", "nullable": false}]}, {"name": "auto-channel-select", "type": "string", "default": "nocfg"}, {"name": "auto-txpower-select", "type": "string", "default": "nocfg"}, {"name": "frequency", "type": "string", "default": "none", "clause": [{"type": "when", "formula": "(../radio-id != 1)"}]}, {"name": "auto-bandwidth-select", "type": "string", "default": "none"}, {"name": "flexible-radio", "type": "string"}, {"name": "channel-monitor", "type": "string"}, {"name": "antenna-gain", "type": "int32"}, {"name": "work-mode", "type": "string"}, {"name": "spectrum-analysis-switch", "type": "string"}, {"name": "data-3d-calibrate", "type": "string"}, {"name": "interference-visualization-switch", "type": "string", "default": "disable"}, {"name": "huawei-wlan-radio-2g-profile:binding-radio-2g-instance", "type": "container", "fields": [{"name": "binding-radio-2g-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-radio-2g-profile/huawei-wlan-radio-2g-profile:wlan-radio-2g-profile/radio-2g-profiles/radio-2g-profile/profile-name"}]}], "clause": [{"type": "when", "formula": "(../radio-id != 1)"}]}, {"name": "huawei-wlan-radio-5g-profile:binding-radio-5g-instance", "type": "container", "fields": [{"name": "binding-radio-5g-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-radio-5g-profile/huawei-wlan-radio-5g-profile:wlan-radio-5g-profile/radio-5g-profiles/radio-5g-profile/profile-name"}]}]}, {"name": "huawei-wlan-vap-inst:vap-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "radio-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-radio:radio-instances::radio-instance::huawei-wlan-vap-inst:vap-instances::vap-instance", "alias": "vap-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "wlan-id", "type": "uint8", "nullable": false}, {"name": "binding-vap-profile", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-vap-profile/huawei-wlan-vap-profile:wlan-vap-profile/vap-profiles/vap-profile/profile-name"}]}, {"name": "service-vlan", "type": "container", "fields": [{"name": "vlan-mode", "type": "choice", "fields": [{"name": "id", "type": "case", "fields": [{"name": "service-vlan-id", "type": "uint16"}]}, {"name": "pool", "type": "case", "fields": [{"name": "service-vlan-pool", "type": "string"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "wlan-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:eth-trunk-instances::eth-trunk-instance", "alias": "eth-trunk-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:ethernet-instances::ethernet-instance", "alias": "ethernet-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:ge-instances::ge-instance", "alias": "ge-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:multige-instances::multige-instance", "alias": "multige-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:xge-instances::xge-instance", "alias": "xge-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::wmi-server-infos::wmi-server-info", "alias": "wmi-server-info", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "wmi-server-name", "type": "string", "nullable": false}, {"name": "max-packet-size", "type": "uint32", "default": 5}, {"name": "ip-port-info", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": false}]}, {"name": "report-interval", "type": "uint16", "default": 60}, {"name": "log-report-modules", "type": "container", "fields": []}, {"name": "keepalive-para", "type": "container", "fields": [{"name": "interval", "type": "uint16", "default": 3}, {"name": "retry-interval", "type": "uint16", "default": 5}, {"name": "retry-number", "type": "uint32", "default": 0}]}, {"name": "collect-interface-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 60}]}, {"name": "collect-neighbor-device-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 60}]}, {"name": "collect-ssid-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 60}]}, {"name": "collect-radio-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 10}]}, {"name": "collect-device-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 10}]}, {"name": "collect-cpcar-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 300}]}, {"name": "collect-terminal-dhcp-option-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "disable"}, {"name": "interval", "type": "uint32", "default": 300}]}, {"name": "collect-terminal-http-ua-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "disable"}, {"name": "interval", "type": "uint32", "default": 300}]}, {"name": "collect-terminal-mdns-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "disable"}, {"name": "interval", "type": "uint32", "default": 300}]}, {"name": "collect-log-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 300}]}, {"name": "collect-terminal-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 10}]}, {"name": "collect-lldp-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}]}, {"name": "collect-s-ipfpm-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}]}, {"name": "collect-application-statistics-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 300}]}, {"name": "collect-emdi-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 10}]}, {"name": "collect-location-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "disable"}, {"name": "interval", "type": "uint32", "default": 60}]}, {"name": "collect-ai-roam-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "enable"}, {"name": "interval", "type": "uint32", "default": 300}]}, {"name": "collect-dns-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "disable"}, {"name": "interval", "type": "uint32", "default": 60}]}, {"name": "collect-non-wifi-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "disable"}, {"name": "interval", "type": "uint32", "default": 60}]}, {"name": "roam-data", "type": "container", "fields": [{"name": "switch", "type": "string", "default": "disable"}, {"name": "interval", "type": "uint32", "default": 300}]}, {"name": "pki-realm", "type": "string", "default": "default"}], "keys": [{"name": "k0", "fields": [":pid", "wmi-server-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::wmi-server-infos::wmi-server-info::log-report-modules::log-report-module", "alias": "log-report-module", "type": "list", "max-elements": 64, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mid", "type": "string", "nullable": false}, {"name": "sub-module", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "mid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-provision::server-auth-cn-lists::server-auth-cn-list", "alias": "server-auth-cn-list", "type": "list", "max-elements": 5, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "cn", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "cn"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::unauthed-aps::unauthed-ap", "alias": "unauthed-ap", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-sn", "type": "string", "is_config": false, "nullable": false}, {"name": "ap-mac", "type": "string", "is_config": false}, {"name": "ap-type", "type": "string", "is_config": false}, {"name": "site-code", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-sn"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::register-aps::register-ap", "alias": "register-ap", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ap-mac", "type": "string", "is_config": false}, {"name": "ap-sn", "type": "string", "is_config": false}, {"name": "ap-type", "type": "string", "is_config": false}, {"name": "ap-name", "type": "string", "is_config": false}, {"name": "site-code", "type": "string", "is_config": false}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "vendor", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}, {"name": "run-state", "type": "string", "is_config": false}, {"name": "ap-group", "type": "string", "is_config": false}, {"name": "central-ap-id", "type": "uint32", "is_config": false}, {"name": "central-ap-mac", "type": "string", "is_config": false}, {"name": "central-ap-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::port-lldp-statistics::port-lldp-statistic", "alias": "port-lldp-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "local-ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "rem-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "local-port-num", "type": "uint32", "is_config": false, "nullable": false}, {"name": "rem-chassis-id", "type": "string", "is_config": false}, {"name": "rem-port-id", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "local-ap-id", "rem-index", "local-port-num"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::port-statistics::port-statistic", "alias": "port-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "port-index", "type": "uint16", "is_config": false, "nullable": false}, {"name": "port-type", "type": "string", "is_config": false}, {"name": "port-mode", "type": "string", "is_config": false}, {"name": "port-negot", "type": "string", "is_config": false}, {"name": "port-duplex", "type": "string", "is_config": false}, {"name": "port-speed", "type": "uint32", "is_config": false}, {"name": "port-state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-id", "port-index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:ap-license-notify::license-infos::license-info", "alias": "license-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "license-type", "type": "string", "is_config": false, "nullable": false}, {"name": "expire-time", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "license-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:notify-ap-report-lldp-data::ap-instances::ap-instance", "alias": "ap-instance", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-sn", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-sn"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:notify-ap-report-state::type::ap-id-info::ap-instances::ap-instance", "alias": "ap-instance", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:register-status-ack::ap-instances::ap-instance", "alias": "ap-instance", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-sn", "type": "string", "is_config": false, "nullable": false}, {"name": "register-result", "type": "boolean", "is_config": false}, {"name": "register-fail-reason", "type": "uint32", "is_config": false, "clause": [{"type": "when", "formula": "../register-result = false()"}]}, {"name": "status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-sn"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:get-ap-connect-fail-reason::ap-instances::ap-instance", "alias": "ap-instance", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-mac", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile", "alias": "huawei-wlan-ap-group-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile", "type": "container", "fields": [{"name": "ap-group-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "alias": "ap-group-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-group-name", "type": "string", "nullable": false}, {"name": "location", "type": "string"}, {"name": "ap-ip-version", "type": "string", "default": "all"}, {"name": "scene", "type": "string"}, {"name": "site-code", "type": "string"}, {"name": "huawei-wlan-group-radio:radio-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-reg-dom-profile:grp-binding-regular-domain-profile", "type": "container", "fields": [{"name": "binding-regular-domain-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-reg-dom-profile/huawei-wlan-reg-dom-profile:wlan-reg-dom-profile/regulatory-domain-profiles/regulatory-domain-profile/profile-name"}]}]}, {"name": "huawei-wlan-system-profile:binding-system-profile", "type": "container", "fields": [{"name": "binding-system-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-system-profile/huawei-wlan-system-profile:wlan-system-profile/system-profiles/system-profile/profile-name"}]}]}, {"name": "huawei-wlan-wired-port-profile:eth-trunk-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-wired-port-profile:ethernet-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-wired-port-profile:ge-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-wired-port-profile:multige-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-wired-port-profile:xge-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "ap-group-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-group-radio:radio-instances::radio-instance", "alias": "radio-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "radio-id", "type": "uint8", "nullable": false}, {"name": "radio-switch", "type": "string", "default": "enable"}, {"name": "channel-info", "type": "container", "presence": true, "fields": [{"name": "bandwidth", "type": "choice", "nullable": false, "fields": [{"name": "bandwidth-20mhz", "type": "case", "fields": [{"name": "bandwidth-20mhz", "type": "string"}]}, {"name": "bandwidth-40mhz-minus", "type": "case", "fields": [{"name": "bandwidth-40mhz-minus", "type": "string"}]}, {"name": "bandwidth-40mhz-plus", "type": "case", "fields": [{"name": "bandwidth-40mhz-plus", "type": "string"}]}, {"name": "bandwidth-80mhz", "type": "case", "fields": [{"name": "bandwidth-80mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-80plus80mhz", "type": "case", "fields": [{"name": "bandwidth-80plus80mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-160mhz", "type": "case", "fields": [{"name": "bandwidth-160mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-320mhz", "type": "case", "fields": [{"name": "bandwidth-320mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../frequency = '5G') or (../../../frequency = '6G')"}]}]}, {"name": "channel", "type": "uint8", "nullable": false}]}, {"name": "eirp", "type": "uint8", "default": 127}, {"name": "auto-channel-select", "type": "string", "default": "enable"}, {"name": "auto-txpower-select", "type": "string", "default": "enable"}, {"name": "frequency", "type": "string", "clause": [{"type": "when", "formula": "(../radio-id != 1)"}]}, {"name": "auto-bandwidth-select", "type": "string", "default": "disable"}, {"name": "flexible-radio", "type": "string", "default": "enable"}, {"name": "channel-monitor", "type": "string", "default": "disable"}, {"name": "antenna-gain", "type": "int32"}, {"name": "work-mode", "type": "string", "default": "normal"}, {"name": "spectrum-analysis-switch", "type": "string", "default": "disable"}, {"name": "data-3d-calibrate", "type": "string", "default": "enable"}, {"name": "huawei-wlan-radio-2g-profile:binding-radio-2g-instance", "type": "container", "fields": [{"name": "binding-radio-2g-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-radio-2g-profile/huawei-wlan-radio-2g-profile:wlan-radio-2g-profile/radio-2g-profiles/radio-2g-profile/profile-name"}]}], "clause": [{"type": "when", "formula": "(../radio-id != 1)"}]}, {"name": "huawei-wlan-radio-5g-profile:binding-radio-5g-instance", "type": "container", "fields": [{"name": "binding-radio-5g-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-radio-5g-profile/huawei-wlan-radio-5g-profile:wlan-radio-5g-profile/radio-5g-profiles/radio-5g-profile/profile-name"}]}]}, {"name": "huawei-wlan-vap-inst:vap-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "radio-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-group-radio:radio-instances::radio-instance::huawei-wlan-vap-inst:vap-instances::vap-instance", "alias": "vap-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "wlan-id", "type": "uint8", "nullable": false}, {"name": "binding-vap-profile", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-vap-profile/huawei-wlan-vap-profile:wlan-vap-profile/vap-profiles/vap-profile/profile-name"}]}, {"name": "service-vlan", "type": "container", "fields": [{"name": "vlan-mode", "type": "choice", "fields": [{"name": "id", "type": "case", "fields": [{"name": "service-vlan-id", "type": "uint16"}]}, {"name": "pool", "type": "case", "fields": [{"name": "service-vlan-pool", "type": "string"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "wlan-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:eth-trunk-instances::eth-trunk-instance", "alias": "eth-trunk-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:ethernet-instances::ethernet-instance", "alias": "ethernet-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:ge-instances::ge-instance", "alias": "ge-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:multige-instances::multige-instance", "alias": "multige-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:xge-instances::xge-instance", "alias": "xge-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-radio", "alias": "huawei-wlan-ap-radio", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-ap-radio:rfping", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "stop-enable", "type": "boolean", "is_config": false}, {"name": "rfping", "type": "container", "is_config": false, "fields": [{"name": "station-mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "interval", "type": "uint32", "is_config": false, "default": 100}, {"name": "packet-count", "type": "uint32", "is_config": false, "default": 10}, {"name": "priority", "type": "string", "is_config": false, "default": "be"}]}]}, {"name": "huawei-wlan-ap-radio:get-rfping-result", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "test-name", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-2g-profile", "alias": "huawei-wlan-radio-2g-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile", "type": "container", "fields": [{"name": "radio-2g-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "alias": "radio-2g-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "dot11bg-basic-rate-lists", "type": "container", "fields": []}, {"name": "dot11bg-support-rate-lists", "type": "container", "fields": []}, {"name": "radio-type", "type": "string", "default": "dot11be"}, {"name": "utmost-power", "type": "string"}, {"name": "guard-interval-mode", "type": "string", "default": "short"}, {"name": "guard-interval-mode-dot11ax", "type": "string", "default": "dot8"}, {"name": "beacon-interval", "type": "uint16", "default": 100}, {"name": "rts-cts-mode", "type": "string", "default": "rts-cts"}, {"name": "rts-cts-threshold", "type": "uint32", "default": 1400}, {"name": "short-preamble", "type": "string", "default": "enable"}, {"name": "fragmentation-threshold", "type": "uint32", "default": 2346}, {"name": "multicast-rate", "type": "string"}, {"name": "edca", "type": "container", "fields": [{"name": "wmm", "type": "string", "default": "enable"}, {"name": "wmm-mandatory", "type": "string", "default": "disable"}, {"name": "wmm-edca-ap-acvo", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 1}, {"name": "ecwmin", "type": "uint32", "default": 2}, {"name": "ecwmax", "type": "uint32", "default": 3}, {"name": "txoplimit", "type": "uint32", "default": 47}, {"name": "ack-policy", "type": "string", "default": "normal"}], "clause": [{"type": "must", "formula": "./ecwmax >= ./ecwmin"}]}, {"name": "wmm-edca-ap-acvi", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 1}, {"name": "ecwmin", "type": "uint32", "default": 3}, {"name": "ecwmax", "type": "uint32", "default": 4}, {"name": "txoplimit", "type": "uint32", "default": 94}, {"name": "ack-policy", "type": "string", "default": "normal"}], "clause": [{"type": "must", "formula": "./ecwmax >= ./ecwmin"}]}, {"name": "wmm-edca-ap-acbe", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 3}, {"name": "ecwmin", "type": "uint32", "default": 4}, {"name": "ecwmax", "type": "uint32", "default": 6}, {"name": "txoplimit", "type": "uint32", "default": 0}, {"name": "ack-policy", "type": "string", "default": "normal"}], "clause": [{"type": "must", "formula": "./ecwmax >= ./ecwmin"}]}, {"name": "wmm-edca-ap-acbk", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 7}, {"name": "ecwmin", "type": "uint32", "default": 4}, {"name": "ecwmax", "type": "uint32", "default": 10}, {"name": "txoplimit", "type": "uint32", "default": 0}, {"name": "ack-policy", "type": "string", "default": "normal"}], "clause": [{"type": "must", "formula": "./ecwmax >= ./ecwmin"}]}]}, {"name": "smart-antenna", "type": "container", "fields": [{"name": "smart-antenna", "type": "string"}, {"name": "valid-per-scope-high-per-threshold", "type": "string", "default": "80percent"}, {"name": "valid-per-scope-low-per-threshold", "type": "string", "default": "20percent"}, {"name": "training-mpdu-number", "type": "uint32", "default": 640}, {"name": "throughput-triggered-training", "type": "string", "default": "10percent"}, {"name": "training-interval", "type": "choice", "fields": [{"name": "auto", "type": "case", "default": true, "fields": [{"name": "training-interval-auto", "type": "boolean", "default": true}]}, {"name": "interval", "type": "case", "fields": [{"name": "interval-value", "type": "uint32"}]}]}]}, {"name": "anti-interference-parameters", "type": "container", "fields": [{"name": "agc-high-threshold", "type": "int32", "clause": [{"type": "must", "formula": ". >= ../rx-sensitivity"}]}, {"name": "rx-sensitivity", "type": "int32", "default": -128}, {"name": "cca-threshold", "type": "int32"}]}, {"name": "a-mpdu", "type": "string", "default": "enable"}, {"name": "a-msdu", "type": "string", "default": "auto"}, {"name": "wifi-light", "type": "string"}, {"name": "vip-user-reservation-ratio", "type": "uint32", "default": 20}, {"name": "auto-off-by-time-range", "type": "string"}, {"name": "fastpass-parameters", "type": "container", "fields": [{"name": "fastpass-users", "type": "uint32", "default": 5}, {"name": "period-value", "type": "string", "default": "20ms"}, {"name": "ratio-value", "type": "uint32", "default": 25}]}, {"name": "huawei-wlan-rrm-profile:radio-2g-binding-rrm", "type": "container", "fields": [{"name": "binding-rrm-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-rrm-profile/huawei-wlan-rrm-profile:wlan-rrm-profile/rrm-profiles/rrm-profile/profile-name"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-basic-rate-lists::dot11bg-basic-rate-list", "alias": "dot11bg-basic-rate-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "basic-rate", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "basic-rate"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-support-rate-lists::dot11bg-support-rate-list", "alias": "dot11bg-support-rate-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "support-rate", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "support-rate"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-5g-profile", "alias": "huawei-wlan-radio-5g-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "hua<PERSON>-wlan-radio-5g-profile:wlan-radio-5g-profile", "type": "container", "fields": [{"name": "radio-5g-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "alias": "radio-5g-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "dot11a-basic-rate-lists", "type": "container", "fields": []}, {"name": "dot11a-support-rate-lists", "type": "container", "fields": []}, {"name": "radio-type", "type": "string", "default": "dot11be"}, {"name": "utmost-power", "type": "string"}, {"name": "guard-interval-mode", "type": "string", "default": "short"}, {"name": "guard-interval-mode-dot11ax", "type": "string", "default": "dot8"}, {"name": "beacon-interval", "type": "uint16", "default": 100}, {"name": "rts-cts-mode", "type": "string", "default": "rts-cts"}, {"name": "rts-cts-threshold", "type": "uint32", "default": 1400}, {"name": "fragmentation-threshold", "type": "uint32", "default": 2346}, {"name": "multicast-rate", "type": "string"}, {"name": "edca", "type": "container", "fields": [{"name": "wmm", "type": "string", "default": "enable"}, {"name": "wmm-mandatory", "type": "string", "default": "disable"}, {"name": "wmm-edca-ap-acvo", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 1}, {"name": "ecwmin", "type": "uint32", "default": 2}, {"name": "ecwmax", "type": "uint32", "default": 3}, {"name": "txoplimit", "type": "uint32", "default": 47}, {"name": "ack-policy", "type": "string", "default": "normal"}], "clause": [{"type": "must", "formula": "./ecwmax >= ./ecwmin"}]}, {"name": "wmm-edca-ap-acvi", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 1}, {"name": "ecwmin", "type": "uint32", "default": 3}, {"name": "ecwmax", "type": "uint32", "default": 4}, {"name": "txoplimit", "type": "uint32", "default": 94}, {"name": "ack-policy", "type": "string", "default": "normal"}], "clause": [{"type": "must", "formula": "./ecwmax >= ./ecwmin"}]}, {"name": "wmm-edca-ap-acbe", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 3}, {"name": "ecwmin", "type": "uint32", "default": 4}, {"name": "ecwmax", "type": "uint32", "default": 6}, {"name": "txoplimit", "type": "uint32", "default": 0}, {"name": "ack-policy", "type": "string", "default": "normal"}], "clause": [{"type": "must", "formula": "./ecwmax >= ./ecwmin"}]}, {"name": "wmm-edca-ap-acbk", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 7}, {"name": "ecwmin", "type": "uint32", "default": 4}, {"name": "ecwmax", "type": "uint32", "default": 10}, {"name": "txoplimit", "type": "uint32", "default": 0}, {"name": "ack-policy", "type": "string", "default": "normal"}], "clause": [{"type": "must", "formula": "./ecwmax >= ./ecwmin"}]}]}, {"name": "smart-antenna", "type": "container", "fields": [{"name": "smart-antenna", "type": "string"}, {"name": "valid-per-scope-high-per-threshold", "type": "string", "default": "80percent"}, {"name": "valid-per-scope-low-per-threshold", "type": "string", "default": "20percent"}, {"name": "training-mpdu-number", "type": "uint32", "default": 640}, {"name": "throughput-triggered-training", "type": "string", "default": "10percent"}, {"name": "training-interval", "type": "choice", "fields": [{"name": "auto", "type": "case", "default": true, "fields": [{"name": "training-interval-auto", "type": "boolean", "default": true}]}, {"name": "interval", "type": "case", "fields": [{"name": "interval-value", "type": "uint32"}]}]}]}, {"name": "anti-interference-parameters", "type": "container", "fields": [{"name": "agc-high-threshold", "type": "int32", "clause": [{"type": "must", "formula": ". >= ../rx-sensitivity"}]}, {"name": "rx-sensitivity", "type": "int32", "default": -128}, {"name": "cca-threshold", "type": "int32"}]}, {"name": "a-mpdu", "type": "string", "default": "enable"}, {"name": "a-msdu", "type": "string", "default": "auto"}, {"name": "wifi-light", "type": "string"}, {"name": "vip-user-reservation-ratio", "type": "uint32", "default": 20}, {"name": "vip-user-per-packet-tpc", "type": "string", "default": "enable"}, {"name": "vht-a-msdu-max-frame-num", "type": "uint32", "default": 2}, {"name": "auto-off-by-time-range", "type": "string"}, {"name": "fastpass-parameters", "type": "container", "fields": [{"name": "fastpass-users", "type": "uint32", "default": 5}, {"name": "period-value", "type": "string", "default": "20ms"}, {"name": "ratio-value", "type": "uint32", "default": 25}]}, {"name": "huawei-wlan-rrm-profile:radio-5g-binding-rrm", "type": "container", "fields": [{"name": "binding-rrm-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-rrm-profile/huawei-wlan-rrm-profile:wlan-rrm-profile/rrm-profiles/rrm-profile/profile-name"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-basic-rate-lists::dot11a-basic-rate-list", "alias": "dot11a-basic-rate-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "basic-rate", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "basic-rate"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-support-rate-lists::dot11a-support-rate-list", "alias": "dot11a-support-rate-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "support-rate", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "support-rate"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-reg-dom-profile", "alias": "huawei-wlan-reg-dom-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-reg-dom-profile:wlan-reg-dom-profile", "type": "container", "fields": [{"name": "regulatory-domain-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-reg-dom-profile:wlan-reg-dom-profile::regulatory-domain-profiles::regulatory-domain-profile", "alias": "regulatory-domain-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "country-code", "type": "string", "default": "CN"}, {"name": "dca-channel-5g-bandwidth", "type": "string", "default": "auto"}, {"name": "dca-channel-6g-bandwidth", "type": "string", "default": "auto"}, {"name": "dca-channel-2g-set", "type": "string"}, {"name": "dca-channel-5g-set", "type": "string"}, {"name": "dca-channel-6g-set", "type": "string"}, {"name": "channel-load-mode", "type": "string", "default": "outdoor"}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile", "alias": "huawei-wlan-system-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-system-profile:wlan-system-profile", "type": "container", "fields": [{"name": "system-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile", "alias": "system-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "management-vlan", "type": "uint16"}, {"name": "connectivity-detect", "type": "boolean", "default": true}, {"name": "wmi-servers", "type": "container", "fields": []}, {"name": "mtu", "type": "uint32", "default": 1500}, {"name": "memory-usage-threshold", "type": "uint32", "default": 80}, {"name": "disk-usage-threshold", "type": "uint32", "default": 95}, {"name": "cpu-usage-threshold", "type": "uint8", "default": 90}, {"name": "dhcp-client-option12", "type": "string", "default": "ap-type-mac"}, {"name": "console", "type": "boolean", "default": true}, {"name": "stelnet-server", "type": "boolean", "default": true}, {"name": "telnet", "type": "boolean", "default": false}, {"name": "usb", "type": "string", "default": "disable"}, {"name": "led", "type": "container", "fields": [{"name": "led-switch", "type": "string", "default": "turn-on"}, {"name": "turn-off-by-time", "type": "string", "clause": [{"type": "must", "formula": "../turn-off-by-time != 'all'"}, {"type": "when", "formula": "../led-switch = 'turn-off'"}]}]}, {"name": "acl-vtys", "type": "container", "fields": []}, {"name": "acl-vtys-v6", "type": "container", "fields": [{"name": "acl-vtys", "type": "container", "fields": []}]}, {"name": "poe", "type": "container", "fields": [{"name": "max-power", "type": "uint32", "default": 750000}, {"name": "power-threshold", "type": "uint8"}, {"name": "high-inrush", "type": "boolean", "default": false}, {"name": "af-inrush", "type": "boolean", "default": false}, {"name": "power-reserved", "type": "uint8"}]}, {"name": "local-management", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}, {"name": "ipv4-address", "type": "string", "default": "***************"}, {"name": "sub-net-mask", "type": "string", "default": "***************"}]}, {"name": "traffic-optimize", "type": "container", "fields": [{"name": "arp-threshold", "type": "uint32", "default": 256}, {"name": "igmp-threshold", "type": "uint32", "default": 256}, {"name": "nd-threshold", "type": "uint32", "default": 256}, {"name": "dhcp-threshold", "type": "uint32", "default": 256}, {"name": "dhcpv6-threshold", "type": "uint32", "default": 256}, {"name": "mdns-threshold", "type": "uint32", "default": 256}, {"name": "other-broadcast-threshold", "type": "uint32", "default": 16}, {"name": "other-multicast-threshold", "type": "uint32", "default": 16}, {"name": "arp", "type": "boolean", "default": true}, {"name": "igmp", "type": "boolean", "default": true}, {"name": "nd", "type": "boolean", "default": true}, {"name": "dhcp", "type": "boolean", "default": true}, {"name": "dhcpv6", "type": "boolean", "default": true}, {"name": "mdns", "type": "boolean", "default": true}, {"name": "other-broadcast", "type": "boolean", "default": true}, {"name": "other-multicast", "type": "boolean", "default": true}]}, {"name": "igmp-group-bandwidths", "type": "container", "fields": []}, {"name": "cpu-defends", "type": "container", "fields": []}, {"name": "packet-auto-detect", "type": "container", "fields": [{"name": "broadcast-packet", "type": "uint32", "default": 256}, {"name": "multicast-packet", "type": "uint32", "default": 256}, {"name": "unicast-switch", "type": "boolean", "default": true}, {"name": "unicast-packet", "type": "uint32", "default": 128}]}, {"name": "temperature-thresholds", "type": "container", "fields": []}, {"name": "lldp-report", "type": "boolean", "default": false}, {"name": "sea-monitor-interval", "type": "uint32", "default": 10}, {"name": "flow-learning-max-user-flow", "type": "uint32"}, {"name": "sta-arp-nd-proxy", "type": "boolean", "default": false}, {"name": "log-server", "type": "container", "presence": true, "fields": [{"name": "ip-version", "type": "string", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "default": 514}]}, {"name": "spectrum-info", "type": "container", "fields": [{"name": "non-wifi-age-time", "type": "uint16", "default": 3}, {"name": "spectrum-server", "type": "container", "presence": true, "fields": [{"name": "ip-version", "type": "string", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": false}, {"name": "via-ac-flag", "type": "string"}]}]}, {"name": "keep-service", "type": "container", "fields": [{"name": "switch", "type": "boolean", "default": false}, {"name": "allow-new-access", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../switch = true()"}]}, {"name": "no-auth", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../allow-new-access = true()"}]}]}, {"name": "dtls", "type": "container", "fields": [{"name": "data-dtls-switch", "type": "boolean"}, {"name": "cert-cn", "type": "container", "presence": true, "fields": [{"name": "type", "type": "string", "nullable": false}, {"name": "cn-string", "type": "string", "clause": [{"type": "when", "formula": "../type = 'cn-string'"}]}]}]}, {"name": "log-record-level", "type": "string", "default": "info"}, {"name": "ble-mode", "type": "string", "default": "dynamic"}, {"name": "sftp-server", "type": "boolean", "default": true}, {"name": "temp-mngt", "type": "container", "fields": [{"name": "switch", "type": "boolean", "default": true}, {"name": "temp-mgmt-psk", "type": "string"}]}, {"name": "radio-mode", "type": "string"}, {"name": "power-work-mode", "type": "string", "default": "none"}, {"name": "vtys", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "./console = true() or (./console = false() and ./ble-mode = 'disable')"}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::wmi-servers::wmi-server", "alias": "wmi-server", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "wmi-server-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-ap/huawei-wlan-ap:wlan-ap/wmi-server-infos/wmi-server-info/wmi-server-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::acl-vtys::acl-vty", "alias": "acl-vty", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vty-number", "type": "uint8", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-number", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vty-number", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::acl-vtys-v6::acl-vtys::acl-vty", "alias": "acl-vty", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vty-number", "type": "uint8", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-number", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vty-number", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::igmp-group-bandwidths::igmp-group-bandwidth", "alias": "igmp-group-bandwidth", "type": "list", "max-elements": 32, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-group-address", "type": "string", "nullable": false}, {"name": "end-group-address", "type": "string", "nullable": false}, {"name": "bandwidth", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-group-address", "end-group-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::cpu-defends::cpu-defend", "alias": "cpu-defend", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "packet-protocol-type", "type": "string", "nullable": false}, {"name": "packet-type", "type": "string", "nullable": false}, {"name": "rate-limit", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "packet-protocol-type", "packet-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::temperature-thresholds::temperature-threshold", "alias": "temperature-threshold", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "temperature-level", "type": "string", "nullable": false}, {"name": "temperature-type", "type": "string", "nullable": false}, {"name": "low-threshold", "type": "int8", "clause": [{"type": "when", "formula": "../temperature-level = 'low'"}]}, {"name": "high-threshold", "type": "uint8", "clause": [{"type": "when", "formula": "../temperature-level = 'high'"}]}], "keys": [{"name": "k0", "fields": [":pid", "temperature-level", "temperature-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::vtys::vty", "alias": "vty", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vty-number", "type": "uint8", "nullable": false}, {"name": "idle-timeout-min", "type": "uint32", "default": 5}, {"name": "idle-timeout-sec", "type": "uint32", "default": 0}, {"name": "screen-length", "type": "uint16", "default": 24}], "keys": [{"name": "k0", "fields": [":pid", "vty-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-vap-profile", "alias": "huawei-wlan-vap-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile", "type": "container", "fields": [{"name": "vap-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile", "alias": "vap-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "service-mode", "type": "boolean", "default": true}, {"name": "vlan-mode", "type": "choice", "fields": [{"name": "id", "type": "case", "default": true, "fields": [{"name": "service-vlan", "type": "uint16", "default": 1}]}, {"name": "pool", "type": "case", "fields": [{"name": "service-vlan-pool", "type": "string"}]}]}, {"name": "forward-mode", "type": "string", "default": "direct"}, {"name": "vap-type", "type": "string", "default": "service"}, {"name": "auth-server-down-vap", "type": "container", "fields": [{"name": "server-tmplt", "type": "string", "nullable": false}, {"name": "reject-new-access", "type": "boolean", "default": false}], "clause": [{"type": "when", "formula": "../vap-type = 'backup-auth-server-down'"}]}, {"name": "multi-link-operation", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}]}, {"name": "binding-authentication-profile", "type": "container", "fields": [{"name": "authentication-profile", "type": "string"}]}, {"name": "dynamic-flow-inspection", "type": "string", "default": "auto"}, {"name": "home-agent", "type": "string", "default": "ap"}, {"name": "split-tunnel-acl", "type": "uint32"}, {"name": "layer3-roam-switch", "type": "boolean", "default": true}, {"name": "vlan-mobility-group", "type": "uint32", "default": 1}, {"name": "ip-source-check-user-bind-switch", "type": "boolean", "default": false}, {"name": "arp-anti-attack-check-user-bind-switch", "type": "boolean", "default": false}, {"name": "dns-snooping", "type": "boolean", "default": false}, {"name": "mdns-snooping", "type": "boolean", "default": false}, {"name": "dhcp-option82", "type": "container", "fields": [{"name": "insert-switch", "type": "boolean", "default": false}, {"name": "circuit-id", "type": "container", "fields": [{"name": "dhcp-option82-format-type", "type": "string", "default": "ap-mac"}, {"name": "ap-mac-format", "type": "string", "clause": [{"type": "when", "formula": "../dhcp-option82-format-type = 'ap-mac'"}]}, {"name": "ap-mac-ssid-format", "type": "string", "clause": [{"type": "when", "formula": "../dhcp-option82-format-type = 'ap-mac-ssid'"}]}, {"name": "user-defined", "type": "string"}, {"name": "option82-pattern-semicolon", "type": "string", "default": "disable"}], "clause": [{"type": "must", "formula": "(./dhcp-option82-format-type = 'user-defined' and ./user-defined) or (./dhcp-option82-format-type != 'user-defined')"}]}, {"name": "remote-id", "type": "container", "fields": [{"name": "dhcp-option82-format-type", "type": "string", "default": "ap-mac"}, {"name": "ap-mac-format", "type": "string", "clause": [{"type": "when", "formula": "../dhcp-option82-format-type = 'ap-mac'"}]}, {"name": "ap-mac-ssid-format", "type": "string", "clause": [{"type": "when", "formula": "../dhcp-option82-format-type = 'ap-mac-ssid'"}]}, {"name": "user-defined", "type": "string"}, {"name": "option82-pattern-semicolon", "type": "string", "default": "disable"}], "clause": [{"type": "must", "formula": "(./dhcp-option82-format-type = 'user-defined' and ./user-defined) or (./dhcp-option82-format-type != 'user-defined')"}]}]}, {"name": "learn-client-address", "type": "container", "fields": [{"name": "learn-client-ipv4-address", "type": "container", "fields": [{"name": "switch", "type": "boolean", "default": true}, {"name": "conflict-check", "type": "boolean", "default": true}, {"name": "dhcp-strict", "type": "boolean", "default": false}, {"name": "strict-blacklist", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../dhcp-strict = true()"}]}], "clause": [{"type": "must", "formula": "(./switch = true()) or (./switch = false() and ./conflict-check = true() and ./dhcp-strict = false())"}]}, {"name": "learn-client-ipv6-address", "type": "container", "fields": [{"name": "switch", "type": "boolean", "default": true}, {"name": "conflict-check", "type": "boolean", "default": true}, {"name": "strict", "type": "string", "default": "disable"}, {"name": "strict-blacklist", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../strict != 'disable'"}]}], "clause": [{"type": "must", "formula": "(./switch = true()) or (./switch = false() and ./conflict-check = true() and ./strict = 'disable')"}]}]}, {"name": "auto-off-by-time-range", "type": "string"}, {"name": "keep-service", "type": "container", "fields": [{"name": "switch", "type": "boolean", "default": false}, {"name": "allow-new-access", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../switch = true()"}]}, {"name": "no-auth", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../allow-new-access = true()"}]}]}, {"name": "temp-mngt", "type": "boolean", "default": false}, {"name": "band-steer", "type": "boolean", "default": true}, {"name": "sea-monitor-applications", "type": "container", "fields": []}, {"name": "flow-learning", "type": "container", "presence": true, "fields": [{"name": "rules", "type": "container", "presence": true, "fields": []}, {"name": "applications", "type": "container", "fields": []}, {"name": "color-flag-clear", "type": "boolean", "default": false}]}, {"name": "controller-off-track-link", "type": "boolean", "default": false}, {"name": "nd-trust-port", "type": "boolean", "default": false}, {"name": "huawei-wlan-security-profile:binding-security-profile", "type": "container", "fields": [{"name": "binding-security-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-security-profile/huawei-wlan-security-profile:wlan-security-profile/security-profiles/security-profile/profile-name"}]}]}, {"name": "huawei-wlan-ssid-profile:binding-ssid-profile", "type": "container", "fields": [{"name": "binding-ssid-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-ssid-profile/huawei-wlan-ssid-profile:wlan-ssid-profile/ssid-profiles/ssid-profile/profile-name"}]}]}, {"name": "huawei-wlan-traffic-profile:binding-traffic-profile", "type": "container", "fields": [{"name": "binding-traffic-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-traffic-profile/huawei-wlan-traffic-profile:wlan-traffic-profile/traffic-profiles/traffic-profile/profile-name"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile::sea-monitor-applications::sea-monitor-application", "alias": "sea-monitor-application", "type": "list", "max-elements": 16, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "app-name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "app-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile::flow-learning::rules::rule", "alias": "rule", "type": "list", "max-elements": 16, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule-name", "type": "string", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "bidirectional", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "rule-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile::flow-learning::applications::application", "alias": "application", "type": "list", "max-elements": 16, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "app-name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "app-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-wlan-security-profile", "alias": "hua<PERSON>-wlan-security-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "hua<PERSON>-wlan-security-profile:wlan-security-profile", "type": "container", "fields": [{"name": "security-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-security-profile:wlan-security-profile::security-profiles::security-profile", "alias": "security-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "policy", "type": "choice", "fields": [{"name": "open", "type": "case", "fields": [{"name": "open-policy", "type": "string"}]}, {"name": "wpa", "type": "case", "fields": [{"name": "wpa-parameter", "type": "container", "fields": [{"name": "wpa-type", "type": "string", "nullable": false}, {"name": "authentication-method", "type": "choice", "nullable": false, "fields": [{"name": "psk", "type": "case", "fields": [{"name": "pass-type", "type": "string", "nullable": false}, {"name": "psk", "type": "string", "nullable": false}]}, {"name": "dot1x", "type": "case", "fields": [{"name": "dot1x-method", "type": "string", "nullable": false}]}, {"name": "ppsk", "type": "case", "fields": [{"name": "ppsk-method", "type": "string"}]}]}, {"name": "encrption-method", "type": "string", "nullable": false, "clause": [{"type": "must", "formula": "not(../encrption-method = 'tkip-aes' and not(../wpa-type = 'wpa-wpa2'))"}]}]}]}, {"name": "enhanced-open", "type": "case", "fields": [{"name": "enhanced-open-flag", "type": "string", "nullable": false}, {"name": "transition-ssid", "type": "string"}]}, {"name": "wapi", "type": "case", "fields": [{"name": "wapi-parameter", "type": "container", "fields": [{"name": "authentication-method", "type": "choice", "nullable": false, "fields": [{"name": "psk", "type": "case", "fields": [{"name": "psk-info", "type": "container", "fields": [{"name": "pass-type", "type": "string", "nullable": false}, {"name": "psk", "type": "string", "nullable": false}]}]}, {"name": "cert", "type": "case", "fields": [{"name": "cert-method", "type": "string"}]}]}, {"name": "encrption-wapi", "type": "string", "default": "sm4"}, {"name": "sa-timeout", "type": "uint32", "default": 60}, {"name": "bk-threshold", "type": "uint32", "default": 70}, {"name": "bk-update-interval", "type": "uint32", "default": 43200}, {"name": "asu-ip", "type": "string"}, {"name": "pki-realm", "type": "string"}, {"name": "usk-key-update", "type": "string", "default": "time-based"}, {"name": "usk-update-interval", "type": "uint32", "default": 86400}, {"name": "usk-retrans-count", "type": "uint32", "default": 3}, {"name": "msk-key-update", "type": "string", "default": "time-based"}, {"name": "msk-update-interval", "type": "uint32", "default": 86400}, {"name": "msk-retrans-count", "type": "uint32", "default": 3}, {"name": "cert-retrans-count", "type": "uint32", "default": 3}]}]}, {"name": "wep", "type": "case", "fields": [{"name": "wep-parameter", "type": "container", "fields": [{"name": "wep-type", "type": "string", "nullable": false}, {"name": "default-key", "type": "uint8", "nullable": false}, {"name": "wep-key-lists", "type": "container", "fields": []}]}]}]}, {"name": "pmf", "type": "string"}, {"name": "wpa-ptk-update", "type": "string", "default": "disabled"}, {"name": "wpa-ptk-update-interval", "type": "uint32", "default": 43200}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-security-profile:wlan-security-profile::security-profiles::security-profile::policy::wep::wep-parameter::wep-key-lists::wep-key-list", "alias": "wep-key-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "psd-len", "type": "string", "nullable": false}, {"name": "psd-type", "type": "string", "nullable": false}, {"name": "psk", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ssid-profile", "alias": "huawei-wlan-ssid-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-ssid-profile:wlan-ssid-profile", "type": "container", "fields": [{"name": "ssid-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ssid-profile:wlan-ssid-profile::ssid-profiles::ssid-profile", "alias": "ssid-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "ssid", "type": "string", "default": "HUAWEI-WLAN"}, {"name": "max-sta-number", "type": "uint16", "default": 64}, {"name": "beacon-rate", "type": "container", "fields": [{"name": "rate-2g", "type": "string", "default": "5.5mbps"}, {"name": "rate-5g", "type": "string", "default": "6mbps"}, {"name": "rate-6g", "type": "string", "default": "6mbps"}]}, {"name": "advertise-ap-name", "type": "boolean", "default": false}, {"name": "ssid-hide", "type": "boolean", "default": false}, {"name": "mimo-switch", "type": "boolean", "default": true}, {"name": "beamforming-switch", "type": "boolean", "default": true}, {"name": "association-timeout", "type": "uint16", "default": 5}, {"name": "u-apsd-switch", "type": "string", "default": "disable"}, {"name": "probe-response-retry", "type": "uint32", "default": 1}, {"name": "deny-broadcast-probe", "type": "boolean", "default": false}, {"name": "he-mcs-map", "type": "container", "fields": [{"name": "tx-nss", "type": "uint8", "default": 8}, {"name": "tx-mcs", "type": "uint8", "default": 11}, {"name": "rx-nss", "type": "uint8", "default": 8}, {"name": "rx-mcs", "type": "uint8", "default": 11}]}, {"name": "vht-mcs-map", "type": "container", "fields": [{"name": "tx-nss", "type": "uint8"}, {"name": "tx-mcs", "type": "uint8"}, {"name": "rx-nss", "type": "uint8"}, {"name": "rx-mcs", "type": "uint8"}]}, {"name": "dtim-interval", "type": "uint32", "default": 1}, {"name": "qbss-load", "type": "boolean", "default": false}, {"name": "dot11r", "type": "container", "fields": [{"name": "dot11r-switch", "type": "boolean", "default": false}, {"name": "dot11r-mode", "type": "string", "default": "over-the-air"}, {"name": "reassociate-timeout", "type": "uint32", "default": 1}, {"name": "proprietary", "type": "boolean", "default": false}]}, {"name": "wmm-edca-client-acvo", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 2}, {"name": "ecwmin", "type": "uint32", "default": 2}, {"name": "ecwmax", "type": "uint32", "default": 3}, {"name": "txoplimit", "type": "uint32", "default": 47}]}, {"name": "wmm-edca-client-acvi", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 2}, {"name": "ecwmin", "type": "uint32", "default": 3}, {"name": "ecwmax", "type": "uint32", "default": 4}, {"name": "txoplimit", "type": "uint32", "default": 94}]}, {"name": "wmm-edca-client-acbe", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 3}, {"name": "ecwmin", "type": "uint32", "default": 4}, {"name": "ecwmax", "type": "uint32", "default": 10}, {"name": "txoplimit", "type": "uint32", "default": 0}]}, {"name": "wmm-edca-client-acbk", "type": "container", "fields": [{"name": "aifsn", "type": "uint32", "default": 7}, {"name": "ecwmin", "type": "uint32", "default": 4}, {"name": "ecwmax", "type": "uint32", "default": 10}, {"name": "txoplimit", "type": "uint32", "default": 0}]}, {"name": "reach-max-policy", "type": "container", "fields": [{"name": "reach-max-policy", "type": "string", "default": "hide-ssid"}]}, {"name": "ofdma-policy", "type": "container", "fields": [{"name": "ofdma-downlink-switch", "type": "boolean", "default": true}, {"name": "ofdma-uplink-switch", "type": "boolean", "default": true}]}, {"name": "excessive-retry-threshold", "type": "container", "fields": [{"name": "retry-count", "type": "uint16", "default": 512}, {"name": "retry-duration", "type": "uint8", "default": 8}]}, {"name": "active-dull-client", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile", "alias": "huawei-wlan-traffic-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile", "type": "container", "fields": [{"name": "traffic-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "alias": "traffic-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "user-isolate", "type": "string"}, {"name": "rate-limit", "type": "container", "fields": [{"name": "client-up", "type": "uint32", "default": 4294967295}, {"name": "client-down", "type": "uint32", "default": 4294967295}, {"name": "vap-up", "type": "uint32", "default": 4294967295}, {"name": "vap-down", "type": "uint32", "default": 4294967295}]}, {"name": "time-range-up-rate-limits", "type": "container", "fields": []}, {"name": "time-range-down-rate-limits", "type": "container", "fields": []}, {"name": "traffic-optimize", "type": "container", "fields": [{"name": "sta-bridge-forward", "type": "string", "default": "enable"}, {"name": "dynamic-rate-limit", "type": "boolean", "default": true}, {"name": "broadcast-suppression", "type": "uint32"}, {"name": "multicast-suppression", "type": "uint32"}, {"name": "unicast-suppression", "type": "uint32"}, {"name": "multicast-unicast", "type": "boolean", "default": false}, {"name": "bcmc-unicast-arp", "type": "boolean", "default": true}, {"name": "bcmc-unicast-dhcp", "type": "boolean", "default": true}, {"name": "bcmc-unicast-nd", "type": "boolean", "default": true}, {"name": "bcmc-unicast-mismatch-drop", "type": "boolean", "default": true}, {"name": "bcmc-deny", "type": "container", "fields": [{"name": "deny-all", "type": "boolean", "default": false}, {"name": "except-mdns", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../deny-all = true()"}]}]}, {"name": "tcp-adjust-mss", "type": "uint16"}]}, {"name": "traffic-remarks", "type": "container", "presence": true, "fields": []}, {"name": "igmp-mld-snooping", "type": "container", "fields": [{"name": "igmp-snooping", "type": "boolean", "default": false}, {"name": "igmp-snooping-report-suppress", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../igmp-snooping = true()"}]}]}, {"name": "traffic-filters", "type": "container", "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::time-range-up-rate-limits::time-range-up-rate-limit", "alias": "time-range-up-rate-limit", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "time-range", "type": "string", "nullable": false}, {"name": "client-up", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "time-range"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::time-range-down-rate-limits::time-range-down-rate-limit", "alias": "time-range-down-rate-limit", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "time-range", "type": "string", "nullable": false}, {"name": "client-down", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "time-range"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::traffic-remarks::traffic-remark", "alias": "traffic-remark", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "ipv4-acl-number", "type": "uint32", "nullable": false}, {"name": "l2-acl-number", "type": "uint32", "nullable": false}, {"name": "ipv6-acl-number", "type": "uint32", "nullable": false}, {"name": "priority", "type": "uint8", "nullable": false}, {"name": "dscp-or-dot11e", "type": "string"}, {"name": "dscp", "type": "uint16", "clause": [{"type": "when", "formula": "../dscp-or-dot11e = 'dscp'"}]}, {"name": "dot11e", "type": "uint16", "clause": [{"type": "when", "formula": "../dscp-or-dot11e = 'dot11e'"}]}], "keys": [{"name": "k0", "fields": [":pid", "direction", "ipv4-acl-number", "l2-acl-number", "ipv6-acl-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "k1", "fields": [":pid", "direction", "priority"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}], "clause": [{"type": "must", "formula": "((./ipv4-acl-number != 0 or ./l2-acl-number != 0) and ./ipv6-acl-number = 0) or (./ipv4-acl-number = 0 and ./l2-acl-number = 0 and ipv6-acl-number != 0)"}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::traffic-filters::traffic-filter", "alias": "traffic-filter", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "ipv4-acl-number", "type": "uint32", "nullable": false}, {"name": "l2-acl-number", "type": "uint32", "nullable": false}, {"name": "ipv6-acl-number", "type": "uint32", "nullable": false}, {"name": "priority", "type": "uint8", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "direction", "ipv4-acl-number", "l2-acl-number", "ipv6-acl-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "k1", "fields": [":pid", "direction", "priority"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}], "clause": [{"type": "must", "formula": "((./ipv4-acl-number != 0 or ./l2-acl-number != 0) and ./ipv6-acl-number = 0) or (./ipv4-acl-number = 0 and ./l2-acl-number = 0 and ipv6-acl-number != 0)"}]}, {"name": "huawei-wlan-rrm-profile", "alias": "huawei-wlan-rrm-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-rrm-profile:wlan-rrm-profile", "type": "container", "fields": [{"name": "rrm-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-rrm-profile:wlan-rrm-profile::rrm-profiles::rrm-profile", "alias": "rrm-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "high-density-amc-optimize", "type": "string", "default": "disable"}, {"name": "dynamic-edca", "type": "container", "fields": [{"name": "dynamic-edca", "type": "string", "default": "disable"}, {"name": "dynamic-edca-threshold", "type": "uint16", "default": 6}]}, {"name": "antenna-mode", "type": "string", "default": "auto"}, {"name": "multimedia-air-optimize", "type": "string", "default": "enable"}, {"name": "multimedia-tcp-window-tuning", "type": "string", "default": "enable"}, {"name": "multimedia-uplink-delay-guarantee", "type": "string", "default": "enable"}, {"name": "downlink-delay-guarantee", "type": "container", "fields": [{"name": "voice-guarantee-level", "type": "string", "default": "medium"}, {"name": "video-guarantee-level", "type": "string", "default": "medium"}, {"name": "best-effort-guarantee-level", "type": "string", "default": "medium"}, {"name": "background-guarantee-level", "type": "string", "default": "medium"}]}, {"name": "downlink-slice-ratio", "type": "container", "fields": [{"name": "voice-slice-ratio-level", "type": "string", "default": "medium"}, {"name": "video-slice-ratio-level", "type": "string", "default": "medium"}, {"name": "voice-slice-ratio-value", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "../voice-slice-ratio-level = 'user-defined'"}]}, {"name": "video-slice-ratio-value", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "../video-slice-ratio-level = 'user-defined'"}]}]}, {"name": "multimedia-air-optimize-threshold", "type": "container", "fields": [{"name": "video-threshold", "type": "uint16", "default": 100}, {"name": "voice-threshold", "type": "uint16", "default": 30}]}, {"name": "smart-roam", "type": "container", "fields": [{"name": "smart-roam-switch", "type": "string", "default": "enable"}, {"name": "snr-smart-roam-threshold", "type": "uint8", "default": 20}, {"name": "quick-kickoff-threshold", "type": "string", "default": "enable"}, {"name": "snr-quick-kickoff-threshold", "type": "uint8", "default": 15}, {"name": "high-level-margin", "type": "uint16", "default": 12}, {"name": "low-level-margin", "type": "uint16", "default": 10}, {"name": "unable-roam-exp-time", "type": "uint16", "default": 30}]}, {"name": "calibrate-retransmission-rate-check", "type": "container", "fields": [{"name": "interval", "type": "uint8", "default": 1}, {"name": "traffic-threshold", "type": "uint16", "default": 1250}]}, {"name": "dfs-recover-delay", "type": "uint16", "default": 0}, {"name": "uac", "type": "container", "fields": [{"name": "uac-client-snr", "type": "string", "default": "disable"}, {"name": "uac-client-number", "type": "string", "default": "disable"}, {"name": "snr-threshold", "type": "uint8", "default": 15}, {"name": "number-threshold", "type": "container", "fields": [{"name": "access", "type": "uint16", "default": 64}, {"name": "roam", "type": "uint16", "default": 64}], "clause": [{"type": "must", "formula": "./access <= ./roam"}]}, {"name": "reach-access-threshold", "type": "string"}]}, {"name": "load-balance", "type": "container", "fields": [{"name": "load-balance-switch", "type": "string", "default": "enable"}, {"name": "probe-report-intvl", "type": "uint16", "default": 120}, {"name": "sta-start-threshold", "type": "uint16", "default": 10}, {"name": "sta-gap-threshold", "type": "container", "fields": [{"name": "gap-type", "type": "string", "default": "number"}, {"name": "number", "type": "uint16", "default": 3, "clause": [{"type": "when", "formula": "../gap-type = 'number'"}]}, {"name": "percentage", "type": "uint16", "nullable": false, "clause": [{"type": "when", "formula": "../gap-type = 'percentage'"}]}]}, {"name": "rssi-threshold", "type": "int16", "default": -65}, {"name": "rssi-diff-gap", "type": "uint16", "default": 5}, {"name": "de<PERSON>h-fail-times", "type": "uint16", "default": 0}]}, {"name": "calibrate-para", "type": "container", "fields": [{"name": "min-tx-power-2g", "type": "uint16", "default": 9}, {"name": "max-tx-power-2g", "type": "uint16", "default": 127}, {"name": "min-tx-power-5g", "type": "uint16", "default": 12}, {"name": "max-tx-power-5g", "type": "uint16", "default": 127}, {"name": "min-tx-power-6g", "type": "uint16", "default": 12}, {"name": "max-tx-power-6g", "type": "uint16", "default": 127}, {"name": "clb-noise-flr-thrd", "type": "int32", "default": -75}, {"name": "clbt-tpc-thrd", "type": "int32", "default": -60}, {"name": "clbt-restran-rate-thrd", "type": "int16", "default": 60}], "clause": [{"type": "must", "formula": "./min-tx-power-2g <= ./max-tx-power-2g and ./min-tx-power-5g <= ./max-tx-power-5g and ./min-tx-power-6g <= ./max-tx-power-6g"}]}, {"name": "clb-grp-itf-thrld", "type": "int32", "default": -127}, {"name": "spatial-reuse", "type": "container", "fields": [{"name": "bss-color-switch", "type": "boolean", "default": true}, {"name": "spatial-reuse-switch", "type": "boolean", "default": true}, {"name": "co-sr", "type": "boolean", "default": true}], "clause": [{"type": "must", "formula": "(spatial-reuse-switch = false()) or (spatial-reuse-switch = true() and bss-color-switch = true())"}]}, {"name": "band-steer", "type": "container", "fields": [{"name": "start-thrd", "type": "uint16", "default": 100}, {"name": "gap-thrd", "type": "uint16", "default": 90}, {"name": "snr-thrd", "type": "uint16", "default": 20}, {"name": "deny-thrd", "type": "uint16", "default": 0}, {"name": "client-band-expire", "type": "uint32", "default": 35}]}, {"name": "amc-policy", "type": "string", "default": "auto-balance"}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-calibrate", "alias": "huawei-wlan-radio-calibrate", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-radio-calibrate:wlan-radio-calibrate", "type": "container", "fields": [{"name": "dynamic-cca", "type": "container", "fields": [{"name": "upper-threshold", "type": "int8", "default": -70}, {"name": "lower-threshold", "type": "int8", "default": -82}]}, {"name": "edge-sta", "type": "container", "fields": [{"name": "snr-threshold", "type": "int8", "default": 22}, {"name": "rssi-threshold", "type": "int8", "default": -70}]}, {"name": "calibrate", "type": "container", "fields": [{"name": "calibrate-mode", "type": "string", "default": "auto"}, {"name": "auto-mode-para", "type": "container", "fields": [{"name": "auto-interval", "type": "int32", "default": 1440}, {"name": "auto-start-time", "type": "string", "default": "03:00:00"}], "clause": [{"type": "when", "formula": "../calibrate-mode = 'auto'"}]}, {"name": "schedule-mode-para", "type": "container", "presence": true, "fields": [{"name": "time-mode", "type": "string", "nullable": false}, {"name": "time-range", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-time-range/huawei-time-range:time-range/time-range-instances/time-range-instance/name"}, {"type": "when", "formula": "../time-mode = 'schedule-time-range'"}]}, {"name": "specific-time", "type": "string", "nullable": false, "clause": [{"type": "when", "formula": "../time-mode = 'schedule-specific-time'"}]}], "clause": [{"type": "when", "formula": "../calibrate-mode = 'schedule'"}]}, {"name": "calibrate-policy", "type": "container", "fields": [{"name": "rogue-ap", "type": "string"}, {"name": "non-wifi", "type": "string"}]}, {"name": "flexible-radio", "type": "container", "fields": [{"name": "radio-mode", "type": "string"}, {"name": "recognize-time", "type": "uint32", "is_config": false}]}, {"name": "process", "type": "uint32", "is_config": false}, {"name": "rogue-ap-interference", "type": "string", "default": "enable"}, {"name": "sensitivitys", "type": "container", "fields": []}, {"name": "self-calibrate", "type": "boolean", "default": false}]}, {"name": "anti-interference", "type": "container", "fields": [{"name": "anti-interference-switch", "type": "string", "default": "enable"}, {"name": "per-packet-tpc", "type": "string", "default": "enable"}, {"name": "agc-check", "type": "string", "default": "enable"}]}, {"name": "spctrun-src", "type": "container", "presence": true, "fields": [{"name": "version", "type": "string", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}]}]}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "choice", "is_config": false, "fields": [{"name": "ap-list", "type": "case", "is_config": false, "fields": [{"name": "ap-lists", "type": "container", "is_config": false, "fields": []}]}, {"name": "ap-group-list", "type": "case", "is_config": false, "fields": [{"name": "ap-group-lists", "type": "container", "is_config": false, "fields": []}]}]}]}, {"name": "huawei-wlan-radio-calibrate:triger-flexible-radio", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-wlan-radio-calibrate:spectrum-report-add", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "radio-id", "type": "uint8", "is_config": false, "nullable": false}]}, {"name": "huawei-wlan-radio-calibrate:spectrum-report-delete", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper", "type": "string", "is_config": false}, {"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../oper = 'apid-radioid'"}]}, {"name": "radio-id", "type": "uint8", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../oper = 'apid-radioid'"}]}]}, {"name": "huawei-wlan-radio-calibrate:radio-environment", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-esn", "type": "string", "is_config": false}, {"name": "radio", "type": "uint8", "is_config": false}, {"name": "test-name", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-calibrate:wlan-radio-calibrate::calibrate::sensitivitys::sensitivity", "alias": "sensitivity", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "freq", "type": "string", "nullable": false}, {"name": "sensitivity-type", "type": "string", "default": "medium"}, {"name": "custom-percent", "type": "uint32", "nullable": false, "clause": [{"type": "when", "formula": "../sensitivity-type = 'custom-percent'"}]}], "keys": [{"name": "k0", "fields": [":pid", "freq"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-list::ap-lists::ap-list", "alias": "ap-list", "type": "list", "is_config": false, "max-elements": 50, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-group-list::ap-group-lists::ap-group-list", "alias": "ap-group-list", "type": "list", "is_config": false, "max-elements": 4, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-group", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-group"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range", "alias": "huawei-time-range", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-time-range:time-range", "type": "container", "fields": [{"name": "time-range-instances", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance", "alias": "time-range-instance", "type": "list", "max-elements": 256, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "absolute-ranges", "type": "container", "fields": []}, {"name": "period-ranges", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "alias": "absolute-range", "type": "list", "max-elements": 12, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-time", "type": "string", "nullable": false}, {"name": "end-time", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-time", "end-time"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "alias": "period-range", "type": "list", "max-elements": 32, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "day-of-week", "type": "string", "nullable": false}, {"name": "start-time", "type": "string", "nullable": false}, {"name": "end-time", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "day-of-week", "start-time", "end-time"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-wlan-management", "alias": "hua<PERSON>-wlan-management", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-management:wlan-management", "type": "container", "fields": [{"name": "ap-user-password", "type": "container", "presence": true, "fields": [{"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}]}, {"name": "ap-password-policy", "type": "container", "presence": true, "fields": [{"name": "pwd-expire-time", "type": "uint32", "default": 90}, {"name": "pwd-alert-before-expire", "type": "uint32", "default": 30}, {"name": "pwd-history-record-num", "type": "uint8", "default": 5}, {"name": "pwd-alert-original", "type": "boolean", "default": true}]}, {"name": "ap-update-mode", "type": "container", "fields": [{"name": "update-mode", "type": "string", "default": "ac-mode"}]}, {"name": "ap-type-update-files", "type": "container", "fields": []}, {"name": "ap-type-group-update-files", "type": "container", "fields": []}, {"name": "ap-type-update-patchs", "type": "container", "fields": []}, {"name": "ap-type-group-update-patchs", "type": "container", "fields": []}, {"name": "ap-update-ftp-server", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint32"}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}, {"name": "max-connect-number", "type": "uint32", "default": 50}]}, {"name": "ap-update-sftp-server", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint32"}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}, {"name": "max-connect-number", "type": "uint32", "default": 50}]}, {"name": "ap-update-ftp-server-v6", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint32"}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}, {"name": "max-connect-number", "type": "uint32", "default": 50}]}, {"name": "ap-update-sftp-server-v6", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint32"}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}, {"name": "max-connect-number", "type": "uint32", "default": 50}]}, {"name": "ap-update-schedule-tasks", "type": "container", "fields": []}, {"name": "ap-update-capability", "type": "container", "is_config": false, "fields": [{"name": "query-is-support-update-url", "type": "container", "is_config": false, "presence": true, "fields": []}]}, {"name": "run-mode", "type": "container", "presence": true, "fields": [{"name": "run-mode", "type": "string", "default": "fit"}]}]}, {"name": "huawei-wlan-management:load-ap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-info", "type": "container", "is_config": false, "fields": [{"name": "mode", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "file-name", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../mode = 'load'"}]}, {"name": "next-startup", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "(../mode = 'load') and (../type = 'patch')"}]}]}]}, {"name": "huawei-wlan-management:batch-load-ap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-info", "type": "container", "is_config": false, "fields": [{"name": "mode", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "type-or-group", "type": "choice", "is_config": false, "fields": [{"name": "ap-type-and-ap-group", "type": "case", "is_config": false, "fields": [{"name": "ap-type", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "only-ap-group", "type": "case", "is_config": false, "fields": [{"name": "only-ap-group", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "next-startup", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "(../mode = 'load') and (../type = 'patch')"}]}]}]}, {"name": "huawei-wlan-management:batch-update-reset-ap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-info", "type": "container", "is_config": false, "fields": [{"name": "type-or-group", "type": "choice", "is_config": false, "fields": [{"name": "ap-type-and-ap-group", "type": "case", "is_config": false, "fields": [{"name": "ap-type", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "only-ap-group", "type": "case", "is_config": false, "fields": [{"name": "only-ap-group", "type": "string", "is_config": false}]}, {"name": "ap-partition-reset", "type": "case", "is_config": false, "fields": [{"name": "partition-type", "type": "choice", "is_config": false, "fields": [{"name": "all", "type": "case", "is_config": false, "fields": [{"name": "all", "type": "string", "is_config": false}]}, {"name": "partition-id", "type": "case", "is_config": false, "fields": [{"name": "id", "type": "uint16", "is_config": false}]}]}]}, {"name": "cancel-ap-partition-reset", "type": "case", "is_config": false, "fields": [{"name": "cancel-ap-partition-reset", "type": "string", "is_config": false}]}]}]}]}, {"name": "huawei-wlan-management:delete-ap-patch", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-info", "type": "container", "is_config": false, "fields": [{"name": "type-or-group", "type": "choice", "is_config": false, "fields": [{"name": "ap-type-and-ap-group", "type": "case", "is_config": false, "fields": [{"name": "ap-type", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "only-ap-group", "type": "case", "is_config": false, "fields": [{"name": "only-ap-group", "type": "string", "is_config": false, "nullable": false}]}, {"name": "ap-id", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "next-startup", "type": "string", "is_config": false}]}]}, {"name": "huawei-wlan-management:ap-rtu-load", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "filename", "type": "string", "is_config": false, "nullable": false}]}, {"name": "hua<PERSON>-wlan-management:sta-steering", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "dev-id", "type": "uint32", "is_config": false}, {"name": "radio-id", "type": "uint8", "is_config": false}, {"name": "mode", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-type-update-files::ap-type-update-file", "alias": "ap-type-update-file", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "file-name", "type": "string", "nullable": false}, {"name": "url", "type": "string", "clause": [{"type": "when", "formula": "../../../ap-update-mode/update-mode = 'web-mode'"}]}, {"name": "info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-type-group-update-files::ap-type-group-update-file", "alias": "ap-type-group-update-file", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "ap-group", "type": "string", "nullable": false}, {"name": "file-name", "type": "string", "nullable": false}, {"name": "info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-type", "ap-group"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-type-update-patchs::ap-type-update-patch", "alias": "ap-type-update-patch", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "patch-name", "type": "string", "nullable": false}, {"name": "url", "type": "string", "clause": [{"type": "when", "formula": "../../../ap-update-mode/update-mode = 'web-mode'"}]}, {"name": "info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-type-group-update-patchs::ap-type-group-update-patch", "alias": "ap-type-group-update-patch", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "ap-group", "type": "string", "nullable": false}, {"name": "patch-name", "type": "string", "nullable": false}, {"name": "info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-type", "ap-group"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-update-schedule-tasks::ap-update-schedule-task", "alias": "ap-update-schedule-task", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "task-id", "type": "uint8", "nullable": false}, {"name": "mode", "type": "choice", "nullable": false, "fields": [{"name": "ap-type-group", "type": "case", "fields": [{"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "ap-type-group", "type": "string"}, {"name": "ap-list", "type": "string"}]}, {"name": "ap-group", "type": "case", "fields": [{"name": "ap-group", "type": "string", "nullable": false}]}]}, {"name": "start-time", "type": "string", "nullable": false}, {"name": "start-day", "type": "string", "nullable": false}, {"name": "stop-time", "type": "string", "nullable": false}, {"name": "stop-day", "type": "string", "nullable": false}, {"name": "task-state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "task-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-tm", "alias": "huawei-tm", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-tm:tm", "type": "container", "fields": [{"name": "timezone-configuration", "type": "container", "fields": [{"name": "timezone-name", "type": "string", "default": "DefaultZoneName"}, {"name": "option", "type": "string", "default": "add"}, {"name": "timezone-offset", "type": "string", "default": "00:00:00"}]}, {"name": "dst-configuration", "type": "container", "fields": [{"name": "dst-name", "type": "string"}, {"name": "type", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "start-year", "type": "uint16", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "start-month", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "start-day", "type": "string", "clause": [{"type": "when", "formula": "../type = 'OneYear' and ../dst-name or ../type = 'Repeat-date' and ../dst-name"}]}, {"name": "start-weeknum", "type": "string", "clause": [{"type": "when", "formula": "../type = 'Repeat-week' and ../dst-name"}]}, {"name": "start-weekday", "type": "string", "clause": [{"type": "when", "formula": "../type = 'Repeat-week' and ../dst-name"}]}, {"name": "start-time", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "end-year", "type": "uint16", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "end-month", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "end-day", "type": "string", "clause": [{"type": "when", "formula": "../type = 'OneYear' and ../dst-name or ../type = 'Repeat-date' and ../dst-name"}]}, {"name": "end-weeknum", "type": "string", "clause": [{"type": "when", "formula": "../type = 'Repeat-week' and ../dst-name"}]}, {"name": "end-weekday", "type": "string", "clause": [{"type": "when", "formula": "../type = 'Repeat-week' and ../dst-name"}]}, {"name": "end-time", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "dst-offset", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}], "clause": [{"type": "must", "formula": "((type = 'OneYear' and start-year and start-month and start-day and start-time and end-year and end-month and end-day and end-time and dst-offset) or (type = 'Repeat-date' and start-month and start-day and start-time and end-month and end-day and end-time and dst-offset) or (type = 'Repeat-week' and start-month and start-weeknum and start-weekday and start-time and end-month and end-weeknum and end-weekday and end-time and dst-offset) or not(dst-name))"}]}, {"name": "date-and-time", "type": "container", "is_config": false, "fields": [{"name": "current-time", "type": "string", "is_config": false}, {"name": "weekday", "type": "string", "is_config": false}]}, {"name": "local-time", "type": "container", "is_config": false, "fields": [{"name": "current-time", "type": "string", "is_config": false}, {"name": "weekday", "type": "string", "is_config": false}]}]}, {"name": "huawei-tm:date-time", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "date-time", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system", "alias": "huawei-system", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-system:system", "type": "container", "fields": [{"name": "system-info", "type": "container", "fields": [{"name": "sys-name", "type": "string", "default": "HUAWEI"}, {"name": "sys-contact", "type": "string", "default": "R&D Beijing, Huawei Technologies co.,Ltd."}, {"name": "sys-location", "type": "string", "default": "Beijing China"}, {"name": "sys-desc", "type": "string", "is_config": false}, {"name": "sys-object-id", "type": "string", "is_config": false}, {"name": "system-gmt-time", "type": "uint32", "is_config": false}, {"name": "sys-uptime", "type": "uint32", "is_config": false}, {"name": "sys-service", "type": "string", "is_config": false}, {"name": "platform-name", "type": "string", "is_config": false}, {"name": "platform-version", "type": "string", "is_config": false}, {"name": "product-name", "type": "string", "is_config": false}, {"name": "product-version", "type": "string", "is_config": false}, {"name": "uname", "type": "string", "is_config": false}, {"name": "hardware-model", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}, {"name": "esn", "type": "string", "is_config": false}, {"name": "mac", "type": "string", "is_config": false}, {"name": "software-name", "type": "string", "is_config": false}, {"name": "boot-time", "type": "string", "is_config": false}, {"name": "configuration-restoring", "type": "boolean", "is_config": false}, {"name": "huawei-system-controller:upstream-info", "type": "container", "is_config": false, "fields": [{"name": "manage-vlan", "type": "uint32", "is_config": false}, {"name": "ip-address", "type": "string", "is_config": false}]}, {"name": "huawei-system-controller:token-info", "type": "container", "is_config": false, "fields": [{"name": "token", "type": "string", "is_config": false}]}, {"name": "huawei-system-controller:sitecode-info", "type": "container", "is_config": false, "fields": [{"name": "sitecode", "type": "string", "is_config": false}]}]}, {"name": "security-risks", "type": "container", "is_config": false, "fields": []}, {"name": "weak-passwords", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-system:load-weak-password-dictionary", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "filename", "type": "string", "is_config": false}]}, {"name": "huawei-system:unload-weak-password-dictionary", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::system-info::huawei-system-controller:upstream-info::if-name", "alias": "if-name", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::security-risks::security-risk", "alias": "security-risk", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "level", "type": "string", "is_config": false, "nullable": false}, {"name": "feature-name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "content", "type": "string", "is_config": false, "nullable": false}, {"name": "repair-action", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "level", "feature-name", "type", "content"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::weak-passwords::weak-password", "alias": "weak-password", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "password", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "password"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller", "alias": "huawei-system-controller", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-system-controller:system-controller", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "manage-vlan", "type": "uint16"}, {"name": "ipv6", "type": "boolean", "default": false}]}, {"name": "offline-records", "type": "container", "is_config": false, "fields": []}, {"name": "register-fail-records", "type": "container", "is_config": false, "fields": []}, {"name": "register-info", "type": "container", "is_config": false, "fields": [{"name": "register-phase", "type": "string", "is_config": false}]}, {"name": "online-robustness-enhancement", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "default": false}, {"name": "stable-time", "type": "uint32", "default": 8, "clause": [{"type": "when", "formula": "../enabled = true()"}]}, {"name": "retry-time", "type": "uint32", "default": 5, "clause": [{"type": "when", "formula": "../enabled = true()"}]}, {"name": "reboot-device", "type": "boolean", "default": true}, {"name": "offline-time", "type": "uint32", "default": 24, "clause": [{"type": "when", "formula": "../reboot-device = true()"}]}, {"name": "rollback-patch", "type": "boolean", "default": true}, {"name": "rollback-patch-time", "type": "uint32", "default": 30, "clause": [{"type": "when", "formula": "../rollback-patch = true()"}]}, {"name": "rollback-system-software", "type": "boolean", "default": true}, {"name": "rollback-system-software-time", "type": "uint32", "default": 30, "clause": [{"type": "when", "formula": "../rollback-system-software = true()"}]}]}]}, {"name": "huawei-system-controller:set-register-status", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "register-status", "type": "string", "is_config": false}, {"name": "register-fail-reason", "type": "uint32", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller:system-controller::offline-records::offline-record", "alias": "offline-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "time", "type": "string", "is_config": false}, {"name": "reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "alias": "register-fail-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "time", "type": "string", "is_config": false}, {"name": "reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog", "alias": "huawei-syslog", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-syslog:syslog", "type": "container", "fields": [{"name": "log-switch-list", "type": "container", "fields": []}, {"name": "logfiles", "type": "container", "fields": []}, {"name": "collect-log-process", "type": "container", "is_config": false, "fields": [{"name": "state", "type": "string", "is_config": false}, {"name": "operate-progress", "type": "uint8", "is_config": false}]}, {"name": "loginfos", "type": "container", "is_config": false, "fields": []}, {"name": "log-storage-time", "type": "container", "fields": [{"name": "storage-time", "type": "uint32"}]}]}, {"name": "huawei-syslog:collect-log", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "server-type", "type": "choice", "is_config": false, "nullable": false, "fields": [{"name": "http", "type": "case", "is_config": false, "fields": [{"name": "http-url", "type": "string", "is_config": false}]}]}, {"name": "vpn-instance", "type": "string", "is_config": false}, {"name": "user-name", "type": "string", "is_config": false}, {"name": "password", "type": "string", "is_config": false}, {"name": "ssl-policy-name", "type": "string", "is_config": false, "clause": [{"type": "must", "formula": "../server-type/http/http-url"}]}]}, {"name": "huawei-syslog:check-logfile", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "file-path", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-syslog:save-logfile", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "log-type", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::log-switch-list::log-switch", "alias": "log-switch", "type": "list", "max-elements": 65535, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature-name", "type": "string", "nullable": false}, {"name": "log-name", "type": "string", "nullable": false}, {"name": "security-log-flag", "type": "boolean", "is_config": false}, {"name": "log-type", "type": "string", "is_config": false}, {"name": "suppress", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "feature-name", "log-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::logfiles::logfile", "alias": "logfile", "type": "list", "max-elements": 5, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "logfile-type", "type": "string", "nullable": false}, {"name": "latest-logs", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "logfile-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "alias": "latest-log", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32", "is_config": false, "nullable": false}, {"name": "content", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::loginfos::loginfo", "alias": "loginfo", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature-name", "type": "string", "is_config": false, "nullable": false}, {"name": "log-type", "type": "string", "is_config": false, "nullable": false}, {"name": "sequence", "type": "uint32", "is_config": false}, {"name": "log-id", "type": "uint32", "is_config": false}, {"name": "log-content", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "feature-name", "log-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software", "alias": "huawei-software", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-software:software", "type": "container", "fields": [{"name": "versions", "type": "container", "is_config": false, "fields": []}, {"name": "startup-packages", "type": "container", "is_config": false, "fields": []}, {"name": "packages", "type": "container", "is_config": false, "fields": []}, {"name": "operation-schedules", "type": "container", "is_config": false, "fields": []}, {"name": "download-result", "type": "container", "is_config": false, "fields": [{"name": "file-name", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-tag", "type": "uint32", "is_config": false}]}]}, {"name": "huawei-software:startup-by-mode", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "mode", "type": "string", "is_config": false}, {"name": "auto-sync", "type": "boolean", "is_config": false, "default": false, "clause": [{"type": "when", "formula": "../mode = 'all'"}]}]}, {"name": "huawei-software:clean-discarded-package", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "clean-type", "type": "string", "is_config": false}]}, {"name": "huawei-software:download-upgrade-package", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "server-ip", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../transfer-protocol = 'ftp' or ../transfer-protocol = 'sftp'"}]}, {"name": "server-port", "type": "uint32", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../transfer-protocol = 'ftp' or ../transfer-protocol = 'sftp'"}]}, {"name": "user-name", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../transfer-protocol = 'ftp' or ../transfer-protocol = 'sftp'"}]}, {"name": "password", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../transfer-protocol = 'ftp' or ../transfer-protocol = 'sftp'"}]}, {"name": "base-software-directory", "type": "string", "is_config": false}, {"name": "patch-directory", "type": "string", "is_config": false}, {"name": "transfer-protocol", "type": "string", "is_config": false, "default": "sftp"}, {"name": "http-url", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../transfer-protocol = 'https'"}]}, {"name": "ssl-policy", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "../transfer-protocol = 'https'"}]}, {"name": "ssl-verify", "type": "boolean", "is_config": false, "default": true, "clause": [{"type": "when", "formula": "../transfer-protocol = 'https'"}]}]}, {"name": "huawei-software:cancel-download-upgrade-package", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::versions::version", "alias": "version", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "base", "type": "string", "is_config": false, "nullable": false}, {"name": "patch", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "base"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::startup-packages::startup-package", "alias": "startup-package", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "current-package", "type": "string", "is_config": false}, {"name": "next-package", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::startup-packages::startup-package::current-feature-packages", "alias": "current-feature-packages", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "current-feature-packages", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "current-feature-packages"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::startup-packages::startup-package::next-feature-packages", "alias": "next-feature-packages", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "next-feature-packages", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "next-feature-packages"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::startup-packages::startup-package::current-extended-system-packages", "alias": "current-extended-system-packages", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "current-extended-system-packages", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "current-extended-system-packages"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::startup-packages::startup-package::next-extended-system-packages", "alias": "next-extended-system-packages", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "next-extended-system-packages", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "next-extended-system-packages"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::packages::package", "alias": "package", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "software-name", "type": "string", "is_config": false}, {"name": "package-type", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::packages::package::feature-names", "alias": "feature-names", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature-names", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "feature-names"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::operation-schedules::operation-schedule", "alias": "operation-schedule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "operation-type", "type": "string", "is_config": false, "nullable": false}, {"name": "file-name", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "fail-reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "operation-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:startup-by-mode::feature-package-name", "alias": "feature-package-name", "type": "leaf-list", "is_config": false, "min-elements": 1, "max-elements": 9, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature-package-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "feature-package-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software-fwd", "alias": "huawei-software-fwd", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-software-fwd:software-fwd", "type": "container", "fields": [{"name": "fwd-statistics-infos", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software-fwd:software-fwd::fwd-statistics-infos::fwd-statistics-info", "alias": "fwd-statistics-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "cpu-id", "type": "string", "is_config": false, "nullable": false}, {"name": "fwd-memory-usage", "type": "uint8", "is_config": false}, {"name": "packet-drop-nobuff", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "cpu-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]