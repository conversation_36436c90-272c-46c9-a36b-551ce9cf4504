/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: DWcommon.h
 * Author: gwx620465
 * Create: 2024-10-27
 */
#ifndef DW_COMMON_H
#define DW_COMMON_H

#include "t_datacom_lite.h"
#include "ClientSingnoProtect.h"

#ifdef __cplusplus
extern "C" {
#endif

#define FILE_PATH 512
#define NAME_LEN 128
#define MAX_CMD_SIZE 1024
#define VERTEX_TOTAL 26
#define OP_RECORD 10000
sem_t g_sem;

typedef struct TagThrArgT {
    uint32_t thrIndex;
    uint32_t opVertexIndex;
} ThrArgT;

typedef struct TagVertexInfoT {
    int vertexIndex;
    const char *vertexName;
    int nCsFlag;
} VertexInfoT;

static VertexInfoT g_vertexInfo[] = {{.vertexIndex = 1, .vertexName = "test_devm_meth_statistics", .nCsFlag = 0},
    {.vertexIndex = 2, .vertexName = "test_devm_sys_state", .nCsFlag = 0},
    {.vertexIndex = 3, .vertexName = "test_qos_ba", .nCsFlag = 0},
    {.vertexIndex = 4, .vertexName = "test_qos_phb", .nCsFlag = 0},
    {.vertexIndex = 5, .vertexName = "test_ifg_global", .nCsFlag = 0},
    {.vertexIndex = 6, .vertexName = "test_qos_queue_sch", .nCsFlag = 0},
    {.vertexIndex = 7, .vertexName = "test_qos_if_phb", .nCsFlag = 0},
    {.vertexIndex = 8, .vertexName = "test_qos_if_trust", .nCsFlag = 0},
    {.vertexIndex = 9, .vertexName = "test_qos_if_trust_up", .nCsFlag = 0},
    {.vertexIndex = 10, .vertexName = "test_qos_port_priority", .nCsFlag = 0},
    {.vertexIndex = 11, .vertexName = "test_qos_voq_bitmap", .nCsFlag = 0},
    {.vertexIndex = 12, .vertexName = "test_qos_voq_info", .nCsFlag = 0},
    {.vertexIndex = 13, .vertexName = "test_qos_voq_port", .nCsFlag = 0},
    {.vertexIndex = 14, .vertexName = "test_qos_flow_info", .nCsFlag = 0},
    {.vertexIndex = 15, .vertexName = "test_acl_index_map", .nCsFlag = 0},
    {.vertexIndex = 16, .vertexName = "test_frm_mod", .nCsFlag = 0},
    {.vertexIndex = 17, .vertexName = "test_frm_svc", .nCsFlag = 0},
    {.vertexIndex = 18, .vertexName = "test_port_base_info", .nCsFlag = 0},
    {.vertexIndex = 19, .vertexName = "test_hpp_ifindex_ctrl2fwd", .nCsFlag = 0},
    {.vertexIndex = 20, .vertexName = "test_if_port", .nCsFlag = 0},
    {.vertexIndex = 21, .vertexName = "test_if_mac", .nCsFlag = 0},
    {.vertexIndex = 22, .vertexName = "test_if_fwd", .nCsFlag = 0},
    {.vertexIndex = 23, .vertexName = "test_if_lst", .nCsFlag = 0},
    {.vertexIndex = 24, .vertexName = "test_if", .nCsFlag = 0},
    {.vertexIndex = 25, .vertexName = "test_frm_inst", .nCsFlag = 0},
    {.vertexIndex = 26, .vertexName = "test_mqc_entry_avl", .nCsFlag = 0}};

const char *g_vertexDwConfig[VERTEX_TOTAL] = {"{\"direct_write\":true, \"status_merge_sub\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true}", "{\"direct_write\":true, \"status_merge_sub\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true}", "{\"direct_write\":true, \"status_merge_sub\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true}", "{\"direct_write\":true, \"status_merge_sub\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true}", "{\"direct_write\":true, \"status_merge_sub\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true}", "{\"direct_write\":true, \"status_merge_sub\": true}",
    "{\"direct_write\":true, \"status_merge_sub\": true}", "{\"direct_write\":true, \"status_merge_sub\": true}"};

const char *g_vertexConfig[VERTEX_TOTAL] = {"{\"status_merge_sub\": true}", "{\"status_merge_sub\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}",
    "{\"status_merge_sub\": true, \"max_record_count_check\": true}", "{\"status_merge_sub\": true}",
    "{\"status_merge_sub\": true}", "{\"status_merge_sub\": true}", "{\"status_merge_sub\": true}",
    "{\"status_merge_sub\": true}", "{\"status_merge_sub\": true}", "{\"status_merge_sub\": true}",
    "{\"status_merge_sub\": true}", "{\"status_merge_sub\": true}", "{\"status_merge_sub\": true}",
    "{\"status_merge_sub\": true}", "{\"status_merge_sub\": true}"};

int CreateTableTest(GmcStmtT *stmt, int vertexIndex, const char *vertexName = NULL)
{
    char path[FILE_PATH] = {0};
    (void)snprintf(path, sizeof(path), "./DWschemafile/%s.gmjson", g_vertexInfo[vertexIndex].vertexName);
    char *schemaJson = NULL;
    readJanssonFile(path, &schemaJson, false, g_testNameSpace, false);
    if (schemaJson == NULL) {
        AW_FUN_Log(LOG_ERROR, "read schema json file failed");
        return -1;
    }

    const char *config =
        g_vertexInfo[vertexIndex].nCsFlag == 0 ? g_vertexDwConfig[vertexIndex] : g_vertexConfig[vertexIndex];
    int ret = 0;
    if (vertexName == NULL) {
        GmcDropVertexLabel(stmt, g_vertexInfo[vertexIndex].vertexName);
        ret = GmcCreateVertexLabel(stmt, schemaJson, config);
    } else {
        GmcDropVertexLabel(stmt, vertexName);
        ret = GmcCreateVertexLabelWithName(stmt, schemaJson, config, vertexName);
    }
    free(schemaJson);
    return ret;
}

int DropTableTest(GmcStmtT *stmt, int vertexIndex, const char *vertexName = NULL)
{
    if (vertexName == NULL) {
        return GmcDropVertexLabel(stmt, g_vertexInfo[vertexIndex].vertexName);
    } else {
        return GmcDropVertexLabel(stmt, g_vertexInfo[vertexIndex].vertexName);
    }
}

// 直连写 or CS写接口
int TestGmcPrepareStmtByLabelName(
    GmcStmtT *stmt, const char *vertexLabelName, GmcOperationTypeE operationType, int isflag)
{
    if (isflag) {
        return GmcPrepareStmtByLabelName(stmt, vertexLabelName, operationType);
    } else {
        return GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, operationType);
    }
}

void OldSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[NAME_LEN] = {0};
    unsigned int labelNameLen = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;
                break;
            } else {
                break;
            }
        } else if (eof) {
            break;
        }

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            ret = GmcSubGetLabelName(subStmt, i, labelName, &(labelNameLen = sizeof(labelName)));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__,
                        info->eventType);
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                AW_FUN_Log(
                    LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__, info->eventType);
                break;
            }
        }
    }
}

void NewSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    unsigned int labelNameLen = NAME_LEN;
    char labelName[NAME_LEN] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;
                break;
            } else {
                break;
            }
        } else if (eof) {
            break;
        }

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = NAME_LEN;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__,
                        info->eventType);
                    break;
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: eventType");
                break;
            }
        }
    }
}

int TestCreateSub(GmcStmtT *stmt, int vertexIndex, GmcSubCallbackT userCb, GmcConnT **subConn, GmcStmtT **subStmt,
    SnUserDataT *userData, bool isStatusMerge = true)
{
    char subJsonPath[FILE_PATH] = {0};
    if (isStatusMerge) {
        (void)snprintf(subJsonPath, sizeof(subJsonPath), "./DWschemafile/Newsub/%s_status_merge.gmjson",
            g_vertexInfo[vertexIndex].vertexName);
    } else {
        (void)snprintf(subJsonPath, sizeof(subJsonPath), "./DWschemafile/Oldsub/%s_oldsub.gmjson",
            g_vertexInfo[vertexIndex].vertexName);
    }
    char *subJson = NULL;
    readJanssonFile(subJsonPath, &subJson);
    if (subJson == NULL) {
        AW_FUN_Log(LOG_ERROR, "read sub json file failed");
        return -1;
    }

    char subConnName[NAME_LEN] = {0};
    char subName[NAME_LEN] = {0};
    GmcSubConfigT subInfo = {0};
    int chanRingLen = 256;
    (void)snprintf(subConnName, sizeof(subConnName), "subConn_%u_%u", vertexIndex, isStatusMerge);
    (void)snprintf(subName, sizeof(subName), "sub_%u_%u", vertexIndex, isStatusMerge);
    RETURN_IFERR(testSubConnect(subConn, subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen));
    GmcUnSubscribe(stmt, subName);
    subInfo.subsName = subName;
    subInfo.configJson = subJson;
    int ret = GmcSubscribe(stmt, &subInfo, *subConn, userCb, userData);
    free(subJson);
    return ret;
}

int TestUnSub(GmcStmtT *stmt, int vertexIndex, GmcConnT *subConn, GmcStmtT *subStmt, bool isStatusMerge = true)
{
    char subName[NAME_LEN] = {0};
    (void)snprintf(subName, sizeof(subName), "sub_%u_%u", vertexIndex, isStatusMerge);
    RETURN_IFERR(GmcUnSubscribe(stmt, subName));
    RETURN_IFERR(testSubDisConnect(subConn, subStmt));
    return 0;
}

int TestUpgrade(int vertexIndex, char *expectValue, char *nsName = g_testNameSpace, char *uWay = (char *)"online")
{
    int ret = 0;
    char path[FILE_PATH] = {0};
    (void)snprintf(path, sizeof(path), "./DWschemafile/AlterSchema/%s.gmjson", g_vertexInfo[vertexIndex].vertexName);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath,
        g_vertexInfo[vertexIndex].vertexName, path, uWay, nsName);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
        system(cmd);
    }
    return ret;
}

int TestDegrade(int vertexIndex, uint32_t schemaVersion, char *expectValue, char *nsName = g_testNameSpace,
    char *dWay = (char *)"sync")
{
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -v %d -d %s -ns %s", g_toolPath,
        g_vertexInfo[vertexIndex].vertexName, schemaVersion, dWay, nsName);
    int ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
        system(cmd);
    }
    return ret;
}

int replaceDevmMethStatistics(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_devm_meth_statistics")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "chassis_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "slot_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InBytes", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutBytes", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InBCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InMCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InUCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutBCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutMCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutUCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InORErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InErrCE", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InErrSH", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InErrLG", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InFrgPkg", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InUnderSize", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InOverSizePkg", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InCRC", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InJabber", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InResourceErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InDrop", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutLCErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutURErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutRLErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "OutResourceErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutLowErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutDrop", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InFlowDrop", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int updateDevmMethStatistics(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_devm_meth_statistics")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        tUint64Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InBytes", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutBytes", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InBCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InMCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InUCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutBCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutMCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutUCastPkts", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InORErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InErrCE", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InErrSH", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InErrLG", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InFrgPkg", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InUnderSize", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InOverSizePkg", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InCRC", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InJabber", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InResourceErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InDrop", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutLCErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutURErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutRLErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "OutResourceErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutLowErr", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "OutDrop", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "InFlowDrop", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceDevmSysState(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_devm_sys_state")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "chassis_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "slot_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "card_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "current_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "poweroff_time", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "register_time", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "unregister_time", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int updateDevmSysState(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_devm_sys_state")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        tUint64Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "current_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "poweroff_time", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "register_time", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "unregister_time", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosBa(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_ba")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vird_id", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ds_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ba_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ba_value", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "en_service_class", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "en_color", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_serial_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int updateQosBa(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_ba")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));

        tUint8Value++;
        tUint32Value++;
        tUint64Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "en_service_class", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "en_color", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_serial_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosPhb(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_phb")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vird_id", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ds_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ba_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "en_service_class", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "en_color", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ba_value", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_serial_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceIfgGlobal(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_ifg_global")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "car_enable", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "stat_enable", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_serial_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosQueueSch(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_queue_sch")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "queue_index", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "schdle_mode", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "queue_weight", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_serial_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosIfPhb(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_if_phb")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "dscp_enable", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "dot1q_enable", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "exp_enable", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosIfTrust(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_if_trust")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "trust_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosIfTrustUp(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_if_trust_up")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "diffserv_id", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosPortPriority(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_port_priority")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "port_priority", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosVopBitmap(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_voq_bitmap")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "resType", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "resKey", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "mapIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char wr_fixed[128] = {0};
        (void)snprintf(wr_fixed, 128, "f%.10u", tUint32Value);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "map", GMC_DATATYPE_FIXED, wr_fixed, 128));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosVopInfo(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_voq_info")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint16_t tUint16Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "internal", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "chassisID", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "slot", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "unit", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "port", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "voqBase", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "timestamp", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosVopPort(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_voq_port")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint16_t tUint16Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "internal", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "chassisID", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "slot", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceQosFlowInfo(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_qos_flow_info")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint16_t tUint16Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "flapId", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "sb", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "internal", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "chassisID", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "slot", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "unit", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "port", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "voqBase", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "flowBase", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "timestamp", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceFrmInst(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_frm_inst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ver", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "role", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "start_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "mod_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "mod_remote_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "svc_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_chassis", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_slot", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "logic_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_port", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_ip", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_process", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_cpu", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "mod_num", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "svc_num", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_ver", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int updateFrmInst(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_frm_inst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint8Value++;
        tUint32Value++;
        tUint64Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ver", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "role", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "start_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "mod_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "mod_remote_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "svc_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_chassis", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_slot", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "logic_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_port", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_ip", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_process", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_cpu", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "mod_num", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "svc_num", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_ver", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceFrmMod(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_frm_mod")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "inst_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char wr_str[16] = {0};
        (void)snprintf(wr_str, 16, "str%.10u", i);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "name", GMC_DATATYPE_STRING, wr_str, strlen(wr_str)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "status", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "err", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_ver", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "gr_smooth", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "reserve", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceFrmSvc(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_frm_svc")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "inst_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char wr_str[16] = {0};
        (void)snprintf(wr_str, 16, "str%.10u", i);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "name", GMC_DATATYPE_STRING, wr_str, strlen(wr_str)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "status", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "err", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_ver", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "reserve", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replacePortBaseInfo(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_port_base_info")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "dev_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "fe_nodeid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "dev_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "hard_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "slotid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "cardid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "chassisid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "portNo", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "unitid", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "portid", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "port_lrcfgwriteover", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "port_fecfgwriteover", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "device_cfgwriteover", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "device_portoverflag", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "coreId", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "supportSlice", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "port_basecfgwriteover", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "gltpid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "gltpid_ver", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "subPort", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "combine_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int updatePortBaseInfo(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_port_base_info")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint8Value++;
        tUint32Value++;
        tUint64Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "fe_nodeid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "dev_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "hard_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "slotid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "cardid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "chassisid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "portNo", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "unitid", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "portid", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "port_lrcfgwriteover", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "port_fecfgwriteover", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "device_cfgwriteover", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "device_portoverflag", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "coreId", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "supportSlice", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "port_basecfgwriteover", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "gltpid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "gltpid_ver", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "subPort", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "combine_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceIf(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint8_t tUint8Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char wr_fixed[64] = {0};
        (void)snprintf(wr_fixed, 64, "f%.10u", tUint32Value);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "name", GMC_DATATYPE_FIXED, wr_fixed, 64));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "shutdown", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "linkup", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tbtp", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "port_switch", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "forwardType", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char mac_fixed[6] = {0};
        (void)snprintf(wr_fixed, 6, "f%.5u", tUint32Value);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "macAddress", GMC_DATATYPE_FIXED, mac_fixed, 6));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ipv4_mtu", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ipv4_enable", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ipv6_mtu", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ipv6_enable", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "on_board", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lagid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "hppsvcflg", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "error_down", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "speed", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "link_protocol", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "port_group_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_group_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int scanPortBaseInfo(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_port_base_info")
{
    int ret = 0;
    RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN, isCsflag));
    bool isFinish;
    int cnt = 0;
    ret = GmcExecute(stmt);
    if (ret == GMERR_OK) {
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        while (!isFinish) {
            cnt++;
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
        }
    }

    printf(">> %d\n", cnt);
    return 0;
}

int replaceIfPort(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_port")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "unit_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "port_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "logicTB", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "logicTP", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "globalSvcIfIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "isSelf", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "holdDownTime", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "holdUpTime", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceIfFwd(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "segIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifkey_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char wr_fixed[96] = {0};
        (void)snprintf(wr_fixed, 96, "f%.10u", tUint32Value);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifkey", GMC_DATATYPE_FIXED, wr_fixed, 20));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "resource_vsi", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "resource", GMC_DATATYPE_FIXED, wr_fixed, 24));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ready_flags", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "attr_flags", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "update_time", GMC_DATATYPE_TIME, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "down_status", GMC_DATATYPE_FIXED, wr_fixed, 8));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "error_code", GMC_DATATYPE_FIXED, wr_fixed, 8));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "svc_ctx", GMC_DATATYPE_FIXED, wr_fixed, 96));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "globallif", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "globallif_ver", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "actType", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "peVlanId", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ceVlanId", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "subIfType", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceIfMac(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_mac")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;

        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char wr_fixed[6] = {0};
        (void)snprintf(wr_fixed, 6, "f%.5u", i);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "macAddress", GMC_DATATYPE_FIXED, wr_fixed, 6));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int updateIfMac(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_mac")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        char wr_fixed[6] = {0};
        (void)snprintf(wr_fixed, 6, "f%.5u", tUint32Value);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "macAddress", GMC_DATATYPE_FIXED, wr_fixed, 6));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int replaceIfList(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_lst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;

        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        uint64_t tmpResIdx = 0;
        RETURN_IFERR(GmcSetPoolIdResource(0xFFFF, &tmpResIdx));
        RETURN_IFERR(GmcSetCountResource(1, &tmpResIdx));
        RETURN_IFERR(GmcSetStartIdxResource(0xFFFFFFFF, &tmpResIdx));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lstindexres", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lstindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write if_lst failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int updateIfList(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_lst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lstindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int deleteIfList(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_lst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "delete failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

static const char *g_resPoolName001 = "ResourcePool001";
static const char *g_resPoolConfigJson001 =
    R"({
        "name" : "ResourcePool001",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";
static const char *g_resPoolName003 = "ResourcePool003";
static const char *g_resPoolConfigJson003 =
    R"({
        "name" : "ResourcePool003",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";

int repUpdDelIfList(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_lst")
{
    // 创建资源池
    int ret = 0;
    if (strcmp(vertexName, "test_if_lst") == 0) {
        (void)GmcDestroyResPool(stmt, g_resPoolName001);
        ret = GmcCreateResPool(stmt, g_resPoolConfigJson001);
        if (GMERR_RESOURCE_POOL_ALREADY_EXIST != ret) {
            RETURN_IFERR(ret);
        }
        ret = GmcBindResPoolToLabel(stmt, g_resPoolName001, vertexName);
        if (ret != GMERR_RESOURCE_POOL_ALREADY_BOUND) {
            RETURN_IFERR(ret);
        }
    } else {
        (void)GmcDestroyResPool(stmt, g_resPoolName003);
        ret = GmcCreateResPool(stmt, g_resPoolConfigJson003);
        if (GMERR_RESOURCE_POOL_ALREADY_EXIST != ret) {
            RETURN_IFERR(ret);
        }
        ret = GmcBindResPoolToLabel(stmt, g_resPoolName003, vertexName);
        if (ret != GMERR_RESOURCE_POOL_ALREADY_BOUND) {
            RETURN_IFERR(ret);
        }
    }

    RETURN_IFERR(replaceIfList(stmt, start, end, isCsflag, vertexName));
    RETURN_IFERR(updateIfList(stmt, start, end, isCsflag, vertexName));
    RETURN_IFERR(deleteIfList(stmt, start, end, isCsflag, vertexName));

    ret = GmcUnbindResPoolFromLabel(stmt, vertexName);
    RETURN_IFERR(ret);
    // 销毁资源池
    if (strcmp(vertexName, "test_if_lst") == 0) {
        ret = GmcDestroyResPool(stmt, g_resPoolName001);
    } else {
        ret = GmcDestroyResPool(stmt, g_resPoolName003);
    }
    RETURN_IFERR(ret);

    return 0;
}

int replaceHppIfindexCtrl2fwd(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_hpp_ifindex_ctrl2fwd")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;

        RETURN_IFERR(GmcSetVertexProperty(stmt, "ctrlifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        uint64_t tmpResIdx = 0;
        RETURN_IFERR(GmcSetPoolIdResource(0xFFFF, &tmpResIdx));
        RETURN_IFERR(GmcSetCountResource(1, &tmpResIdx));
        RETURN_IFERR(GmcSetStartIdxResource(0xFFFFFFFF, &tmpResIdx));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "fwdifindex", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "version", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write hpp_ifindex_ctrl2fwd failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int updateHppIfindexCtrl2fwd(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_hpp_ifindex_ctrl2fwd")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "version", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int deleteHppIfindexCtrl2fwd(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_hpp_ifindex_ctrl2fwd")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "delete hpp_ifindex_ctrl2fwd failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

static const char *g_resPoolName002 = "ResourcePool002";
static const char *g_resPoolConfigJson002 =
    R"({
        "name" : "ResourcePool002",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";

static const char *g_resPoolName004 = "ResourcePool004";
static const char *g_resPoolConfigJson004 =
    R"({
        "name" : "ResourcePool004",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";

int repUpdDelHppIfindexCtrl2fwd(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_hpp_ifindex_ctrl2fwd")
{
    // 创建资源池
    int ret = 0;
    if (strcmp(vertexName, "test_hpp_ifindex_ctrl2fwd") == 0) {
        (void)GmcDestroyResPool(stmt, g_resPoolName002);
        ret = GmcCreateResPool(stmt, g_resPoolConfigJson002);
        if (GMERR_RESOURCE_POOL_ALREADY_EXIST != ret) {
            RETURN_IFERR(ret);
        }
        ret = GmcBindResPoolToLabel(stmt, g_resPoolName002, vertexName);
        if (ret != GMERR_RESOURCE_POOL_ALREADY_BOUND) {
            RETURN_IFERR(ret);
        }
    } else {
        (void)GmcDestroyResPool(stmt, g_resPoolName004);
        int ret = GmcCreateResPool(stmt, g_resPoolConfigJson004);
        if (GMERR_RESOURCE_POOL_ALREADY_EXIST != ret) {
            RETURN_IFERR(ret);
        }
        ret = GmcBindResPoolToLabel(stmt, g_resPoolName004, vertexName);
        if (ret != GMERR_RESOURCE_POOL_ALREADY_BOUND) {
            RETURN_IFERR(ret);
        }
    }

    RETURN_IFERR(replaceHppIfindexCtrl2fwd(stmt, start, end, isCsflag, vertexName));
    RETURN_IFERR(updateHppIfindexCtrl2fwd(stmt, start, end, isCsflag, vertexName));
    RETURN_IFERR(deleteHppIfindexCtrl2fwd(stmt, start, end, isCsflag, vertexName));

    ret = GmcUnbindResPoolFromLabel(stmt, vertexName);
    RETURN_IFERR(ret);
    // 销毁资源池
    if (strcmp(vertexName, "test_hpp_ifindex_ctrl2fwd") == 0) {
        ret = GmcDestroyResPool(stmt, g_resPoolName002);
    } else {
        ret = GmcDestroyResPool(stmt, g_resPoolName004);
    }
    RETURN_IFERR(ret);
    return 0;
}

int setAclIndexMapChildNode(GmcNodeT *node, int32_t i)
{
    uint32_t tUint32Value = i;
    RETURN_IFERR(GmcNodeSetPropertyByName(node, (char *)"cir", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, (char *)"pir", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, (char *)"cbs", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, (char *)"pbs", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(
        GmcNodeSetPropertyByName(node, (char *)"car_mode", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(
        GmcNodeSetPropertyByName(node, (char *)"car_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int replaceAclIndexMap(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_acl_index_map")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "unit_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "gid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "fisrt_car_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "second_car_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "phy_entry_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "fst_car_local_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "sec_car_local_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "pass_count_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "drop_count_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "sec_pass_count_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "sec_drop_count_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "group_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "is_global", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "logging", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "resv", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "direction", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "cnt_app_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "sec_cnt_app_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "fst_car_app_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "sec_car_app_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        char wr_fixed[20] = {0};
        (void)snprintf(wr_fixed, 20, "f%.10u", tUint32Value);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "rule_mask", GMC_DATATYPE_FIXED, wr_fixed, 8));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "first_car_para", &node1));
        RETURN_IFERR(setAclIndexMapChildNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "sec_car_para", &node2));
        RETURN_IFERR(setAclIndexMapChildNode(node2, i));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int setMqcEntryAvlChildNode1(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "match_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "match_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "drop_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "drop_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "filter_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "filter_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "car_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "car_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    return 0;
}

int setMqcEntryAvlChildNode2(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "match_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "match_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "drop_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "drop_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "filter_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "filter_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "car_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "car_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    return 0;
}

int setMqcEntryAvlChildNode3(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_packet", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    return 0;
}

int replaceMqcEntryAvl(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_mqc_entry_avl")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        RETURN_IFERR(TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag));
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        bool tBoolVal = false;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "unit", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "appinst_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "adp_gid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "local_pri", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "is_aged", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ext_id", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "entry_resv", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "class_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "match_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "rule_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "is_rule_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "is_fisrt_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "is_second_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "is_mirror_port", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "statMode", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "is_log", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "share_car_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "obsrv_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "mc_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "stat_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "res", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "interval", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "sample_rate", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "last_sec", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "last_sec_mill", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(
            GmcSetVertexProperty(stmt, "local_down_pri", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlChildNode1(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlChildNode2(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlChildNode3(node3, i));

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "write failed, index is: %d, ret is : %d.", i, ret);
            return -1;
        }
        RETURN_IFERR(ret);
    }

    return 0;
}

int TestDmlVertex(GmcStmtT *stmt, int vertexIndex, const char *vertexName = NULL)
{
    int tFlag = 0;
    int ret = 0;
    if (vertexName == NULL) {
        vertexName = g_vertexInfo[vertexIndex].vertexName;
    }
    switch (vertexIndex) {
        case 0: {
            tFlag = g_vertexInfo[0].nCsFlag;
            ret = replaceDevmMethStatistics(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            ret = updateDevmMethStatistics(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 1: {
            tFlag = g_vertexInfo[1].nCsFlag;
            ret = replaceDevmSysState(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            ret = updateDevmSysState(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 2: {
            tFlag = g_vertexInfo[2].nCsFlag;
            ret = replaceQosBa(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            ret = updateQosBa(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 3: {
            tFlag = g_vertexInfo[3].nCsFlag;
            ret = replaceQosPhb(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 4: {
            tFlag = g_vertexInfo[4].nCsFlag;
            ret = replaceIfgGlobal(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 5: {
            tFlag = g_vertexInfo[5].nCsFlag;
            ret = replaceQosQueueSch(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 6: {
            tFlag = g_vertexInfo[6].nCsFlag;
            ret = replaceQosIfPhb(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 7: {
            tFlag = g_vertexInfo[7].nCsFlag;
            ret = replaceQosIfTrust(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 8: {
            tFlag = g_vertexInfo[8].nCsFlag;
            ret = replaceQosIfTrustUp(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 9: {
            tFlag = g_vertexInfo[9].nCsFlag;
            ret = replaceQosPortPriority(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 10: {
            tFlag = g_vertexInfo[10].nCsFlag;
            ret = replaceQosVopBitmap(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 11: {
            tFlag = g_vertexInfo[11].nCsFlag;
            ret = replaceQosVopInfo(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 12: {
            tFlag = g_vertexInfo[12].nCsFlag;
            ret = replaceQosVopPort(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 13: {
            tFlag = g_vertexInfo[13].nCsFlag;
            ret = replaceQosFlowInfo(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 24: {
            tFlag = g_vertexInfo[24].nCsFlag;
            ret = replaceFrmInst(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            ret = updateFrmInst(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 15: {
            tFlag = g_vertexInfo[15].nCsFlag;
            ret = replaceFrmMod(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 16: {
            tFlag = g_vertexInfo[16].nCsFlag;
            ret = replaceFrmSvc(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 17: {
            tFlag = g_vertexInfo[17].nCsFlag;
            ret = updatePortBaseInfo(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 23: {  // if
            tFlag = g_vertexInfo[23].nCsFlag;
            ret = replaceIf(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 19: {
            tFlag = g_vertexInfo[19].nCsFlag;
            ret = replaceIfPort(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 21: {
            tFlag = g_vertexInfo[21].nCsFlag;
            ret = replaceIfFwd(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 20: {
            tFlag = g_vertexInfo[20].nCsFlag;
            ret = replaceIfMac(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            ret = updateIfMac(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 22: {
            tFlag = g_vertexInfo[22].nCsFlag;
            ret = repUpdDelIfList(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 18: {
            tFlag = g_vertexInfo[18].nCsFlag;
            ret = repUpdDelHppIfindexCtrl2fwd(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 14: {  // acl_index_map
            tFlag = g_vertexInfo[14].nCsFlag;
            ret = replaceAclIndexMap(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        case 25: {  // mqc_entry_avl
            tFlag = g_vertexInfo[25].nCsFlag;
            ret = replaceMqcEntryAvl(stmt, 0, OP_RECORD, tFlag, vertexName);
            RETURN_IFERR(ret);
            break;
        }
        default: {
            AW_FUN_Log(LOG_INFO, "default: invalid .");
            break;
        }
    }

    return ret;
}

void *DwTheadFunc(void *args)
{
    ThrArgT *ptharg = (ThrArgT *)args;
    TestWriteLog(LOG_INFO, "dw thread %d start, op vertex index %d, name %s.", ptharg->thrIndex, ptharg->opVertexIndex,
        g_vertexInfo[ptharg->opVertexIndex].vertexName);
    SnUserDataT *oldData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (oldData == NULL) {
        TestWriteLog(LOG_ERROR, "dw thread oldData malloc failed.");
        return (void *)0;
    }
    SnUserDataT *newData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (newData == NULL) {
        TestWriteLog(LOG_ERROR, "dw thread newData malloc failed.");
        return (void *)0;
    }
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 随机一个数，如果为偶数的时候是直连写表，奇数的时候为cs表
    uint32_t randNum = 0;
    TestRangeRandNum(&randNum, 0, RAND_MAX);
    g_vertexInfo[ptharg->opVertexIndex].nCsFlag = ((randNum % 2) == 0) ? 0 : 1;
    ret = CreateTableTest(stmt, ptharg->opVertexIndex);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 订阅
    GmcConnT *oldSubConn = NULL;
    GmcStmtT *oldSubStmt = NULL;
    ret = TestCreateSub(stmt, ptharg->opVertexIndex, OldSnCallBack, &oldSubConn, &oldSubStmt, oldData, false);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcConnT *newSubConn = NULL;
    GmcStmtT *newSubStmt = NULL;
    ret = TestCreateSub(stmt, ptharg->opVertexIndex, NewSnCallBack, &newSubConn, &newSubStmt, newData, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = TestDmlVertex(stmt, ptharg->opVertexIndex);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 升级、降级
    char *expectValue1 = (char *)"upgrade successfully";
    ret = TestUpgrade(ptharg->opVertexIndex, expectValue1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char *expectValue2 = (char *)"degrade successfully";
    ret = TestDegrade(ptharg->opVertexIndex, 0, expectValue2);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 取消订阅
    ret = TestUnSub(stmt, ptharg->opVertexIndex, oldSubConn, oldSubStmt, false);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = TestUnSub(stmt, ptharg->opVertexIndex, newSubConn, newSubStmt, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = DropTableTest(stmt, ptharg->opVertexIndex);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(oldData);
    free(newData);
    TestWriteLog(LOG_INFO, "dw thread %d end.", ptharg->thrIndex);
    return (void *)0;
}

void *TheadDmlFunc(void *args)
{
    uint32_t ptharg = *(uint32_t *)args;
    TestWriteLog(LOG_INFO, "TheadDml start thread id is %lu, op vertex index %d, name %s.", pthread_self(), ptharg,
        g_vertexInfo[ptharg].vertexName);

    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char tableName[NAME_LEN] = {0};
    (void)snprintf(
        tableName, NAME_LEN, "%s_%u_%u", g_vertexInfo[ptharg].vertexName, ptharg, g_vertexInfo[ptharg].nCsFlag);
    ret = TestDmlVertex(stmt, ptharg, tableName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = sem_post(&g_sem);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TestWriteLog(LOG_INFO, "TheadDml end.");
    return (void *)0;
}

void *TheadKillFunc(void *args)
{
    pthread_t mainThrId = *(pthread_t *)args;
    TestWriteLog(LOG_INFO, "TheadKill start, thread id is = %lu.", pthread_self());
    int ret = sem_wait(&g_sem);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = pthread_kill(mainThrId, SIGHUP);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TestWriteLog(LOG_INFO, "TheadKill end.");
    return (void *)0;
}

#ifdef __cplusplus
}
#endif
#endif /* DW_COMMON_H */
