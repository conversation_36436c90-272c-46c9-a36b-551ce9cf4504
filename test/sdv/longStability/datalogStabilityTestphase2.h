/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#ifndef DATALOGSTABILITYTESTPHASE2_H
#define DATALOGSTABILITYTESTPHASE2_H

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include <time.h>
#include <stdarg.h>
#include "t_datacom_lite.h"
#include "datalogStabilityTest.h"

#define MAX_CMD_SIZE 1024
#define WRITE_START 1
#define NORMAL_COUNT 100
#define MIN_COUNT 1
#define DLR_WRITE_COUNT 30
#define MAX_SLEEP_TIME_WRITE 20
#define MAX_SLEEP_TIME_DELETE 5
#define MAX_SLEEP_TIME_DLR 1
#define MAX_SEELP_TIME_RECVDATA 100

char g_capv5tableName[128] = "capv5";
char g_capv5configJson[128] = "{\"max_record_count\":100000, \"isFastReadUncommitted\": false}";
char g_datalognsp1[50] = "datalognsp1";
uint32_t g_statusmergefetchNum = 1;
uint32_t g_notifydlrfetchNum = 1;

char g_inp0[50] = "inp0";
char g_inp1[50] = "inp1";
char g_inp2[50] = "inp2";
char g_inp3[50] = "inp3";
char g_inp4[50] = "inp4";
char g_inp5[50] = "inp5";
char g_inp51[50] = "inp5_1";
char g_inp6[50] = "inp6";
char g_datalogsonsp1inp7[50] = "datalogsonsp1.inp7";
char g_inp8[50] = "inp8";
char g_inp9[50] = "inp9";
char g_datalogsonsp1mid1[50] = "datalogsonsp1.mid1";
char g_mid2[50] = "mid2";
char g_mid3[50] = "mid3";
char g_mid5[50] = "mid5";
char g_datalogsonsp2mid6[50] = "datalogsonsp2.mid6";
char g_mid7[50] = "mid7";
char g_out0[50] = "out0";
char g_out1[50] = "out1";
char g_out2[50] = "out2";
char g_out3[50] = "out3";
char g_datalogsonsp2out4[50] = "datalogsonsp2.out4";
char g_out5[50] = "out5";
char g_out8[50] = "out8";
char g_out81[50] = "out8_1";
char g_out10[50] = "out10";
char g_tran1[50] = "tran1";
char g_rsc1[50] = "rsc1";
char g_rsc2[50] = "rsc2";
char g_extern2[50] = "extern2";
char g_extern1[50] = "extern1";
char g_syncinp1[50] = "Sync.inp1";
char g_syncinp1abnormal[50] = "Sync.inp1_2";
char g_syncinp2[50] = "Sync.inp2";
char g_syncinp2abnormal[50] = "Sync.inp2_2";
char g_syncinp3[50] = "Sync.inp3";
char g_syncinp3abnormal[50] = "Sync.inp3_2";
char g_syncinp4[50] = "Sync.inp4";
char g_syncmid1[50] = "Sync.mid1";
char g_syncmid2[50] = "Sync.mid2";
char g_syncmid4[50] = "Sync.mid4";
char g_syncnotifytable[50] = "Sync.notifytable";
char g_syncout3[50] = "Sync.out3";
char g_sync1out6[50] = "Sync1.out6";
char g_sync1inp4[50] = "Sync1.inp4";
char g_sync1inp5[50] = "Sync1.inp5";
char g_sync1inp6[50] = "Sync1.inp6";
char g_sync1inp8[50] = "Sync1.inp8";
char g_sync1mid7[50] = "Sync1.mid7";
char g_sync1inp7[50] = "Sync1.inp7";
char g_sync1inp71[50] = "Sync1.inp7_1";
char g_sync1inp9[50] = "Sync1.inp9";
char g_sync1inp10[50] = "Sync1.inp10";
char g_sync1mid10[50] = "Sync1.mid10";
char g_sync1out9[50] = "Sync1.out9";
AsyncUserDataT recvdata01 = {0}, recvdata02 = {0}, recvdata03 = {0};

typedef int (*FuncWriteValue)(GmcStmtT *stmt, int32_t v);
typedef int (*FunReadValue)(GmcStmtT *stmt);
typedef int (*FuncRead)(GmcStmtT *stmt, bool isPubsubRsc);
typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);

typedef struct DlrReplayData {
    char *replaytablename;
    char *userBuf;
    uint32_t bufSize;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    GmcConnT *subConn = NULL;
    int32_t countcallback = 0;
    int32_t sendEof = 1;
} DlrReplayDataT;

typedef struct Syncinp1TableStruct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a12;
    int64_t a42;
    uint64_t b42;
    uint8_t c12[1];
    uint16_t d10len;
    uint8_t *d10;
    uint16_t d11len;
    uint8_t *d11;
    int64_t timeoutfield;
} Syncinp1TableStructT;

typedef struct WriteTablecfg {
    GmcOperationTypeE opType;
    char *labelName;
    int32_t startVal;  // 主键或其他非成员索引的起始值
    int32_t count;     // 主键或其他非成员索引的数量
    bool isBatch;
    bool isStruct;
    bool isAsync;
    char *lnamespace;
} WriteTablecfgT;

typedef struct TagSnUserDataWithFuncPubT {
    SnUserDataT *data;
    FuncRead func;
    bool isPubsubRsc;
} SnUserDataWithFuncPubT;

SnUserDataWithFuncPubT g_userData1 = {0}, g_userData2 = {0}, g_userData3 = {0};
DlrReplayDataT g_replayData1 = {0}, g_replayData2 = {0}, g_replayData3 = {0}, g_replayData4 = {0}, g_replayData5 = {0};
Syncinp1TableStructT *g_objIn = NULL, *g_objIn1 = NULL;

int AllocreplayData(DlrReplayDataT *replayData, char *replayTableName)
{
    uint32_t bufSize = 204800;  // 1k
    char *userBuf = (char *)malloc(sizeof(char) * bufSize);
    if (userBuf == NULL) {  // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
        return -1;
    }
    replayData->replaytablename = replayTableName;
    replayData->userBuf = userBuf;
    replayData->bufSize = bufSize;
    return 0;
}

int GenerateBytes(uint8_t **bytes, uint32_t byteLen)
{
    uint8_t *byteArray = (uint8_t *)malloc(byteLen + 1);
    if (byteArray == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    for (uint32_t i = 0; i < byteLen; i++) {
        int num = i;
        while (num >= 10) {
            num = num % 10;
        }
        byteArray[i] = (uint8_t)num;
    }
    byteArray[byteLen] = '\n';
    *bytes = byteArray;
    return GMERR_OK;
}

// 无位域字段
void SetSyncinp1TableValue(
    Syncinp1TableStructT *objIn, char *strT, int recordNum = 100, int32_t dtlReservedCount = 1, int startValue = 0)
{
    for (int i = startValue; i < startValue + recordNum; i++) {
        objIn[i - startValue].a12 = i;
        objIn[i - startValue].a42 = i;
        objIn[i - startValue].b42 = i;
        objIn[i - startValue].timeoutfield = 10 * 1000;
        for (int j = 0; j < 1; j++) {
            objIn[i - startValue].c12[j] = (i - startValue + 1) % 50;
        }
        objIn[i - startValue].d10len = 10;
        int ret = GenerateBytes(&objIn[i - startValue].d10, objIn[i - startValue].d10len);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        objIn[i - startValue].d11len = 10;
        ret = GenerateBytes(&objIn[i - startValue].d11, objIn[i - startValue].d11len);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        objIn[i - startValue].dtlReservedCount = 1;
        objIn[i - startValue].upgradeVersion = 0;
    }
}

void SetSyncinp1TableValueAbnormal(
    Syncinp1TableStructT *objIn, char *strT, int recordNum = 100, int32_t dtlReservedCount = 1, int startValue = 0)
{
    for (int i = startValue; i < startValue + recordNum; i++) {
        objIn[i - startValue].a12 = i + 50;
        objIn[i - startValue].a42 = i + 50;
        objIn[i - startValue].b42 = i + 50;
        objIn[i - startValue].timeoutfield = 10 * 1000;
        for (int j = 0; j < 1; j++) {
            objIn[i - startValue].c12[j] = (i - startValue + 1) % 50;
        }
        objIn[i - startValue].d10len = 10;
        int ret = GenerateBytes(&objIn[i - startValue].d10, objIn[i - startValue].d10len);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        objIn[i - startValue].d11len = 10;
        ret = GenerateBytes(&objIn[i - startValue].d11, objIn[i - startValue].d11len);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        objIn[i - startValue].dtlReservedCount = 1;
        objIn[i - startValue].upgradeVersion = 0;
    }
}

// 无位域字段
void SetUpdateSyncinp1TableValue(
    Syncinp1TableStructT *objIn, char *strT, int recordNum = 100, int32_t dtlReservedCount = 1, int startValue = 0)
{
    for (int i = startValue; i < startValue + recordNum; i++) {
        objIn[i - startValue].a12 = i;
        objIn[i - startValue].a42 = i;
        objIn[i - startValue].b42 = i + 1;
        objIn[i - startValue].timeoutfield = 10 * 1000;
        for (int j = 0; j < 1; j++) {
            objIn[i - startValue].c12[j] = (i - startValue + 2) % 50;
        }
        objIn[i - startValue].d10len = 10;
        int ret = GenerateBytes(&objIn[i - startValue].d10, objIn[i - startValue].d10len);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        objIn[i - startValue].d11len = 10;
        ret = GenerateBytes(&objIn[i - startValue].d11, objIn[i - startValue].d11len);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        objIn[i - startValue].dtlReservedCount = 1;
        objIn[i - startValue].upgradeVersion = 0;
    }
}

int AllocWriteObjIn(Syncinp1TableStructT **objIn)
{
    *objIn = (Syncinp1TableStructT *)malloc(sizeof(Syncinp1TableStructT) * DLR_WRITE_COUNT);
    if (*objIn == NULL) {  // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
        return -1;
    }
    char str[10] = {0};
    SetSyncinp1TableValue(*objIn, str, DLR_WRITE_COUNT);
    return 0;
}

int AllocWriteObjInAbnormal(Syncinp1TableStructT **objIn)
{
    *objIn = (Syncinp1TableStructT *)malloc(sizeof(Syncinp1TableStructT) * DLR_WRITE_COUNT);
    if (*objIn == NULL) {  // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
        return -1;
    }
    char str[10] = {0};
    SetSyncinp1TableValueAbnormal(*objIn, str, DLR_WRITE_COUNT);
    return 0;
}

void RandomSleep(int32_t sleepTimeField)
{
    time_t timer = time(0);
    if (!timer) {
        printf("[ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    int32_t sleeptime = rand() % sleepTimeField;
    sleep(sleeptime);
}

void AllocUserDatadata(SnUserDataWithFuncPubT *userData)
{
    int ret = testSnMallocUserData(&userData->data, 800, 800);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

int Setinp7sValue(GmcStmtT *stmt, int32_t v)
{
    int64_t a = v;
    int64_t b = v;
    int64_t c = v;
    int32_t dtlReservedCount = 1;

    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int Readinp7sValue(GmcStmtT *stmt)
{
    int64_t a = 0;
    int64_t b = 0;
    int64_t c = 0;
    int32_t dtlReservedCount = 1;
    bool isNull = true;
    int32_t upVerVal = -1;
    int ret = GmcGetUpgradeVersion(stmt, &upVerVal);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int Readtran1out1Value(GmcStmtT *stmt)
{
    int64_t a = 0;
    int64_t b = 0;
    int32_t c = 0;
    int32_t dtlReservedCount = 1;
    bool isNull = true;
    int32_t upVerVal = -1;
    int ret = GmcGetUpgradeVersion(stmt, &upVerVal);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int Readinp5Value(GmcStmtT *stmt)
{
    int64_t a = 0;
    int64_t b = 0;
    int64_t c = 0;
    int32_t dtlReservedCount = 1;
    bool isNull = true;
    int ret = GmcSetIndexKeyId(stmt, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    int32_t upVerVal = -1;
    ret = GmcGetUpgradeVersion(stmt, &upVerVal);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int ReadRscValue(GmcStmtT *stmt)
{
    int64_t a = 0;
    int64_t b = 0;
    int64_t c = 0;
    int32_t d = 0;
    int32_t dtlReservedCount = 1;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "d", &d, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int createSubscription(GmcStmtT *stmtSync, GmcConnT *connSub, char *subJsonPath, SnUserDataWithFuncPubT *userData,
    uint32_t mallocCount, const char *subsName, GmcSubCallbackT userCb, FuncRead func)
{
    char *subInfo = NULL;
    readJanssonFile(subJsonPath, &subInfo);
    AW_MACRO_EXPECT_NOTNULL(subInfo);

    userData->func = func;

    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subsName;
    tmpSubInfo.configJson = subInfo;
    int ret = GmcSubscribe(stmtSync, &tmpSubInfo, connSub, userCb, userData);
    free(subInfo);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int cancelSubscription(
    GmcStmtT *stmtSync, const char *subsName, SnUserDataWithFuncPubT *userData, int32_t dataStart, uint32_t checkNum)
{
    int ret = GmcUnSubscribe(stmtSync, subsName);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return GMERR_OK;
}

int createDlrSubscription(GmcStmtT *stmtSync, GmcConnT *connSub, char *subJsonPath, DlrReplayData *subData,
    uint32_t mallocCount, const char *subsName, GmcSubCallbackT userCb)
{
    char *subInfo = NULL;
    readJanssonFile(subJsonPath, &subInfo);
    AW_MACRO_EXPECT_NOTNULL(subInfo);
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subsName;
    tmpSubInfo.configJson = subInfo;
    int ret = GmcSubscribe(stmtSync, &tmpSubInfo, connSub, userCb, subData);
    RETURN_IFERR(ret);
    free(subInfo);
    return GMERR_OK;
}

int cancelDlrSubscription(GmcStmtT *stmtSync, const char *subsName)
{
    int ret = GmcUnSubscribe(stmtSync, subsName);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

void batch_execute_callback_merge_and_notify(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->recvNum++;
    }
}

void sn_callback_status_merge_dlr_async(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;
    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    memset(&recvdata01, 0, sizeof(recvdata01));
    RandomSleep(MAX_SLEEP_TIME_DLR);
    while (!getEof) {
        int tryCnt = 0;
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_statusmergefetchNum, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_merge_and_notify, &recvdata01);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (recvdata01.recvNum == 0 && tryCnt < 10) {
            usleep(MAX_SEELP_TIME_RECVDATA);
            tryCnt++;
        }
        // 队列满会导致数据删除失败，后面会出现1005002错误码
        if (recvdata01.status != GMERR_OUT_OF_MEMORY && recvdata01.status != GMERR_LOCK_NOT_AVAILABLE &&
            recvdata01.status != GMERR_SUB_PUSH_QUEUE_FULL && recvdata01.status != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, recvdata01.status);
            if (recvdata01.status) {
                AW_FUN_Log(LOG_STEP, "[sn_callback_status_merge_dlr_async]recvdata01.status is %d, "
                    "replaytablename is %s.", recvdata01.status, replayData->replaytablename);
            }
        }
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

// dtl进程重演到public命名空间Sync.inp4(update_partial)对应的回调
void sn_callback_status_merge_dlr_async02(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;
    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    memset(&recvdata03, 0, sizeof(recvdata03));
    RandomSleep(MAX_SLEEP_TIME_DLR);
    while (!getEof) {
        int tryCnt = 0;
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_statusmergefetchNum, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_merge_and_notify, &recvdata03);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (recvdata03.recvNum == 0 && tryCnt < 10) {
            usleep(MAX_SEELP_TIME_RECVDATA);
            tryCnt++;
        }
        // 队列满会导致数据删除失败，后面会出现1005002错误码
        if (recvdata03.status != GMERR_OUT_OF_MEMORY && recvdata03.status != GMERR_LOCK_NOT_AVAILABLE &&
            recvdata03.status != GMERR_SUB_PUSH_QUEUE_FULL && recvdata03.status != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, recvdata03.status);
            if (recvdata03.status) {
                AW_FUN_Log(LOG_STEP, "[sn_callback_status_merge_dlr_async02]recvdata03.status is %d, "
                    "replaytablename is %s.", recvdata03.status, replayData->replaytablename);
            }
        }
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

void sn_callback_status_merge_dlr_asyncAbnormal(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;
    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    memset(&recvdata01, 0, sizeof(recvdata01));
    RandomSleep(MAX_SLEEP_TIME_DLR);
    while (!getEof) {
        int tryCnt = 0;
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_statusmergefetchNum, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        // 客户端异常退出，会出现该错误码
        if (ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_merge_and_notify, &recvdata01);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (recvdata01.recvNum == 0 && tryCnt < 10) {
                usleep(MAX_SEELP_TIME_RECVDATA);
                tryCnt++;
        }
        if (recvdata01.status != GMERR_OUT_OF_MEMORY && recvdata01.status != GMERR_LOCK_NOT_AVAILABLE &&
            recvdata01.status != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, recvdata01.status);
            if (recvdata01.status) {
                AW_FUN_Log(LOG_STEP, "[sn_callback_status_merge_dlr_asyncAbnormal]recvdata01.status is %d, "
                    "replaytablename is %s.", recvdata01.status, replayData->replaytablename);
            }
        }
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

// 异常退出进程重演到public命名空间Sync.inp4(update_partial)对应的回调
void sn_callback_status_merge_dlr_async02Abnormal(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;
    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    memset(&recvdata03, 0, sizeof(recvdata03));
    RandomSleep(MAX_SLEEP_TIME_DLR);
    while (!getEof) {
        int tryCnt = 0;
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_statusmergefetchNum, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_merge_and_notify, &recvdata03);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (recvdata03.recvNum == 0 && tryCnt < 10) {
            usleep(MAX_SEELP_TIME_RECVDATA);
            tryCnt++;
        }
        // 队列满会导致数据删除失败，后面会出现1005002错误码
        if (recvdata03.status != GMERR_OUT_OF_MEMORY && recvdata03.status != GMERR_LOCK_NOT_AVAILABLE &&
            recvdata03.status != GMERR_SUB_PUSH_QUEUE_FULL && recvdata03.status != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, recvdata03.status);
            if (recvdata03.status) {
                AW_FUN_Log(LOG_STEP, "[sn_callback_status_merge_dlr_async02Abnormal]recvdata03.status is %d, "
                    "replaytablename is %s.", recvdata03.status, replayData->replaytablename);
            }
        }
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

void sn_callback_status_merge_null(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    RandomSleep(MAX_SLEEP_TIME_DLR);
}

void sn_callback_async(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;
    GmcRespT *response = NULL;
    ret = GmcCreateResp(subStmt, &response);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
    ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    memset(&recvdata02, 0, sizeof(recvdata02));
    RandomSleep(MAX_SLEEP_TIME_DLR);
    while (!getEof) {
        int tryCnt = 0;
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_notifydlrfetchNum, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_merge_and_notify, &recvdata02);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (recvdata02.recvNum == 0 && tryCnt < 10) {
                usleep(MAX_SEELP_TIME_RECVDATA);
                tryCnt++;
        }
        if (recvdata02.status != GMERR_OUT_OF_MEMORY && recvdata02.status != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, recvdata02.status);
            if (recvdata02.status) {
                AW_FUN_Log(LOG_STEP, "[sn_callback_async]recvdata02.status is %d, replaytablename is %s.",
                    recvdata02.status, replayData->replaytablename);
            }
        }
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
    }
    ret = GmcSendResp(subStmt, response);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

void sn_callback_asyncAbnormal(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;
    GmcRespT *response = NULL;
    ret = GmcCreateResp(subStmt, &response);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
    ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    memset(&recvdata02, 0, sizeof(recvdata02));
    RandomSleep(MAX_SLEEP_TIME_DLR);
    while (!getEof) {
        int tryCnt = 0;
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_notifydlrfetchNum, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_merge_and_notify, &recvdata02);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (recvdata02.recvNum == 0 && tryCnt < 10) {
                usleep(MAX_SEELP_TIME_RECVDATA);
                tryCnt++;
        }
        if (recvdata02.status != GMERR_OUT_OF_MEMORY && recvdata02.status != GMERR_LOCK_NOT_AVAILABLE &&
            recvdata02.status != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, recvdata02.status);
            if (recvdata02.status) {
                AW_FUN_Log(LOG_STEP, "[sn_callback_asyncAbnormal]recvdata02.status is %d, replaytablename is %s.",
                    recvdata02.status, replayData->replaytablename);
            }
        }
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
    }
    ret = GmcSendResp(subStmt, response);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

void snCallbackExternal(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncPubT *userDefinedData = (SnUserDataWithFuncPubT *)userData;
    SnUserDataT *data = userDefinedData->data;
    SnUserDataT *user_data = (SnUserDataT *)data;
    FuncRead func = userDefinedData->func;
    TEST_EXPECT_INT32(1, info->labelCount);
    int ret, i;
    char labelName[1024] = { 0 };
    unsigned int labelNameLen = 1024;

    bool eof = false;
    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        } else {
            ret = GmcFetch(subStmt, &eof);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            } else if (eof == true) {
                break;
            }
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = 1024;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = func(subStmt, false);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = func(subStmt, false);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = func(subStmt, false);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = func(subStmt, false);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = func(subStmt, false);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MODIFY: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                printf("default: invalid eventType\r\n");
                break;
            }
        }
    }
}

int snFetch(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncPubT *userDefinedData = (SnUserDataWithFuncPubT *)userData;
    SnUserDataT *data = userDefinedData->data;
    bool isPubsubRsc = userDefinedData->isPubsubRsc;
    FuncRead func = userDefinedData->func;
    char labelName[LABEL_NAME] = {0};
    unsigned int labelNameLen;

    data->callbackTimes++;
    TEST_EXPECT_INT32(1, info->labelCount);
    int ret = 0;
    GmcRespT *response;
    ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);

    // 区分notify表和pubsub资源表
    if (!isPubsubRsc) {  // notify表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        RETURN_IFERR(ret);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
        ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        RETURN_IFERR(ret);
    } else {  // pubsub型资源表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
        RETURN_IFERR(ret);
    }
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = LABEL_NAME;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    // pubsub资源表
                    if (isPubsubRsc) {
                        // 获取pubsub型资源表值并设置值
                        ret = func(subStmt, userDefinedData->isPubsubRsc);
                        TEST_EXPECT_INT32(GMERR_OK, ret);
                        ret = GmcSubAddRespDML(response, subStmt);
                        RETURN_IFERR(ret);
                    } else {
                        // notify
                        ret = func(subStmt, userDefinedData->isPubsubRsc);
                        TEST_EXPECT_INT32(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    // 屏蔽assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                // 屏蔽assert(0);
            }
        }
    }
    // 发送消息
    ret = GmcSendResp(subStmt, response);
    RETURN_IFERR(ret);
    ret = GmcDestroyResp(subStmt, response);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

void snCallbackphase2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = snFetch(subStmt, info, userData);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

int rsc2GetValue(GmcStmtT *stmt, bool isPubsubRsc)
{
    int64_t a = 0;
    bool isNull = true;
    int64_t b = 0;
    int64_t c = 0;
    int32_t d = 0;
    int32_t dtlReservedCount = 1;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(int64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "d", &d, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    d = 1;
    if (isPubsubRsc) {
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &d, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

int ReadexternValue(GmcStmtT *stmt)
{
    int64_t a = 0;
    int64_t b = 0;
    int64_t c = 0;
    uint32_t e = 0;
    uint64_t h = 0;
    int32_t dtlReservedCount = 1;
    bool isNull = true;
    char teststr15[10];

    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(c), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t d[128] = {0};
    ret = memset_s(d, 128, 0xff, 128);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "d", &d, sizeof(d), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "e", &e, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "f", teststr15, 10, &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "h", &h, sizeof(uint64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int ReadsubexternValue(GmcStmtT *stmt, bool isPubsubRsc)
{
    int64_t a = 0;
    int64_t b = 0;
    int64_t c = 0;
    uint32_t e = 0;
    uint64_t h = 0;
    int32_t dtlReservedCount = 1;
    bool isNull = true;
    char teststr15[10];

    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(c), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t d[128] = {0};
    ret = memset_s(d, 128, 0xff, 128);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "d", &d, sizeof(d), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "e", &e, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "f", teststr15, 10, &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "h", &h, sizeof(uint64_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int BatchPrepare(GmcConnT *conn, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int UpdateSimpleTableUpdateinp7sValueprimaryindex(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i % 2;
        int32_t dtlReservedCount = 1;
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        int32_t upVerVal = -1;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &a, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

int UpdateSimpleTableUpdateinp7sValuesecondaryindex(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int32_t v = startPkVal; v < startPkVal + vertexCount; v++) {
        int64_t a = v;
        int64_t b = v;
        int64_t c = 1;
        int32_t dtlReservedCount = 1;
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        uint32_t tryCnt = 0;
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP,
                        "[UpdateSimpleTableUpdateinp7sValuesecondaryindex] update try more 100, ret is %d.", ret);
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(int64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

int DeleteSimpleTableDeleteinp7sValuesecondaryindex(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int32_t dtlReservedCount = 1;
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP,
                            "[DeleteSimpleTableDeleteinp7sValuesecondaryindex] delete try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyId(stmt, 1);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchDestroy(batch);
    }
    return ret;
}

int UpdateSimpleTableUpdateinp7sValueprimaryindextoSame(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = 1;
        int32_t dtlReservedCount = 1;
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        int32_t upVerVal = -1;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &a, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        uint32_t tryCnt = 0;
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP,
                        "[UpdateSimpleTableUpdateinp7sValueprimaryindextoSame] update try more 100, ret is %d.", ret);
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upVerVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &a, sizeof(int64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(int64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

int DeleteSimpleTableDeleteinp6Value(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t v = startPkVal; v < startPkVal + vertexCount; v++) {
        int8_t a1 = v;
        int8_t a2 = v;
        int8_t a3 = v;
        int8_t a4 = v;
        int8_t b1 = v;
        int8_t b2 = v;
        int8_t b3 = v;
        int8_t b4 = v;
        int8_t c1 = v;
        int8_t c2 = v;
        int8_t c3 = v;
        int8_t c4 = v;
        int8_t c5 = v;
        int8_t c6 = v;
        int8_t c7 = v;
        int8_t c8 = v;
        int8_t c9 = v;
        int8_t c10 = v;
        int8_t a11 = v;
        int8_t a21 = v;
        int8_t a31 = v;
        int8_t a41 = v;
        int8_t b11 = v;
        int8_t b21 = v;
        int8_t b31 = v;
        int8_t b41 = v;
        int8_t c11 = v;
        int8_t c21 = v;
        int8_t c31 = v;
        int8_t c41 = v;
        int8_t c51 = v;
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        int32_t upVerVal = -1;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &a1, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT8, &a2, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT8, &a3, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT8, &a4, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT8, &b1, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_INT8, &b2, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_INT8, &b3, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 8, GMC_DATATYPE_INT8, &b4, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 9, GMC_DATATYPE_INT8, &c1, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 10, GMC_DATATYPE_INT8, &c2, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 11, GMC_DATATYPE_INT8, &c3, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 12, GMC_DATATYPE_INT8, &c4, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 13, GMC_DATATYPE_INT8, &c5, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 14, GMC_DATATYPE_INT8, &c6, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 15, GMC_DATATYPE_INT8, &c7, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 16, GMC_DATATYPE_INT8, &c8, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 17, GMC_DATATYPE_INT8, &c9, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 18, GMC_DATATYPE_INT8, &c10, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 19, GMC_DATATYPE_INT8, &a11, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 20, GMC_DATATYPE_INT8, &a21, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 21, GMC_DATATYPE_INT8, &a31, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 22, GMC_DATATYPE_INT8, &a41, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 23, GMC_DATATYPE_INT8, &b11, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 24, GMC_DATATYPE_INT8, &b21, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 25, GMC_DATATYPE_INT8, &b31, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 26, GMC_DATATYPE_INT8, &b41, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 27, GMC_DATATYPE_INT8, &c11, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 28, GMC_DATATYPE_INT8, &c21, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 29, GMC_DATATYPE_INT8, &c31, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 30, GMC_DATATYPE_INT8, &c41, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 31, GMC_DATATYPE_INT8, &c51, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP, "[DeleteSimpleTableDeleteinp6Value] delete try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyId(stmt, 0);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcGetUpgradeVersion(stmt, &upVerVal);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &a1, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT8, &a2, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT8, &a3, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT8, &a4, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT8, &b1, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_INT8, &b2, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_INT8, &b3, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 8, GMC_DATATYPE_INT8, &b4, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 9, GMC_DATATYPE_INT8, &c1, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 10, GMC_DATATYPE_INT8, &c2, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 11, GMC_DATATYPE_INT8, &c3, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 12, GMC_DATATYPE_INT8, &c4, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 13, GMC_DATATYPE_INT8, &c5, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 14, GMC_DATATYPE_INT8, &c6, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 15, GMC_DATATYPE_INT8, &c7, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 16, GMC_DATATYPE_INT8, &c8, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 17, GMC_DATATYPE_INT8, &c9, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 18, GMC_DATATYPE_INT8, &c10, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 19, GMC_DATATYPE_INT8, &a11, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 20, GMC_DATATYPE_INT8, &a21, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 21, GMC_DATATYPE_INT8, &a31, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 22, GMC_DATATYPE_INT8, &a41, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 23, GMC_DATATYPE_INT8, &b11, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 24, GMC_DATATYPE_INT8, &b21, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 25, GMC_DATATYPE_INT8, &b31, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 26, GMC_DATATYPE_INT8, &b41, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 27, GMC_DATATYPE_INT8, &c11, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 28, GMC_DATATYPE_INT8, &c21, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 29, GMC_DATATYPE_INT8, &c31, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 30, GMC_DATATYPE_INT8, &c41, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 31, GMC_DATATYPE_INT8, &c51, sizeof(int8_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchDestroy(batch);
    }
    return ret;
}

int DeleteSimpleTableDeleteinp7sValueprimaryindex(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int32_t dtlReservedCount = 1;
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        int32_t upVerVal = -1;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &a, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP,
                            "[DeleteSimpleTableDeleteinp7sValueprimaryindex] delete try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyId(stmt, 0);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcGetUpgradeVersion(stmt, &upVerVal);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &a, sizeof(int64_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchDestroy(batch);
    }
    return ret;
}

int DeleteSimpleTableDeleteinp7sValuesecondaryindexifsame(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = 1;
        int32_t dtlReservedCount = 1;
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP,
                            "[DeleteSimpleTableDeleteinp7sValuesecondaryindexifsame] delete try more 100, ret is %d.",
                            ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyId(stmt, 1);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &b, sizeof(int64_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        GmcBatchDestroy(batch);
    }
    return ret;
}

int DeleteSimpleTableDeleteinp5Valueprimaryindex(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int32_t dtlReservedCount = 1;
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        int32_t upVerVal = -1;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &a, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP,
                            "[DeleteSimpleTableDeleteinp5Valueprimaryindex] delete try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyId(stmt, 0);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcGetUpgradeVersion(stmt, &upVerVal);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &a, sizeof(int64_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchDestroy(batch);
    }
    return ret;
}

int WriteSimpleTable(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i;
        int32_t dtlReservedCount = 1;
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP, "[WriteSimpleTable] write try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(
                        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
            uint32_t totalNum, successNum;
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = GMERR_OK;
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

int Writeinp4SimpleTable(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i;
        int32_t dtlReservedCount = -1;
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP, "[Writeinp4SimpleTable] write try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(
                        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
            uint32_t totalNum, successNum;
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = GMERR_OK;
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

template <typename StructObjT>
int WriteSyncinpTable(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t vertexCount, FuncWrite func, StructObjT *obj, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < vertexCount; i++) {
        ret = func(stmt, (void *)(obj + i));
        TEST_EXPECT_INT32(GMERR_OK, ret);

        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP, "[WriteSyncinpTable] write try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = func(stmt, (void *)(obj + i));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        uint32_t tryCnt = 0;
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[WriteSyncinpTable] write try more 100, ret is %d.", ret);
                    break;
                }
                ret = BatchPrepare(conn, &batch);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                for (int32_t i = 0; i < vertexCount; i++) {
                    ret = func(stmt, (void *)(obj + i));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcBatchAddDML(batch, stmt);
                }
                ret = GmcBatchExecute(batch, &batchRet);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
        }
        if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

template <typename StructObjT>
int WriteTwoTable(GmcConnT *conn, GmcStmtT *stmt, char *labelName1, char *labelName2, int32_t vertexCount,
    FuncWrite func, StructObjT *obj, bool isBatch)
{
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < vertexCount; i++) {
        ret = func(stmt, (void *)(obj + i));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName2, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int32_t i = 0; i < vertexCount; i++) {
        ret = func(stmt, (void *)(obj + i));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    uint32_t tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 100) {
                AW_FUN_Log(LOG_STEP, "[WriteTwoTable] write try more 100, ret is %d.", ret);
                break;
            }
            ret = BatchPrepare(conn, &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (int32_t i = 0; i < vertexCount; i++) {
                ret = func(stmt, (void *)(obj + i));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
            }
            ret = testGmcPrepareStmtByLabelName(stmt, labelName2, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (int32_t i = 0; i < vertexCount; i++) {
                ret = func(stmt, (void *)(obj + i));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
            }
            ret = GmcBatchExecute(batch, &batchRet);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
    }
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    return ret;
}

int Syncinp1TableSet(GmcStmtT *stmt, void *t)
{
    Syncinp1TableStructT *obj = (Syncinp1TableStructT *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a12", GMC_DATATYPE_INT64, &obj->a12, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] a12: %d, ret = %d.", obj->a12, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "a42", GMC_DATATYPE_INT64, &obj->a42, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] a42: %d, ret = %d.", obj->a42, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b42", GMC_DATATYPE_UINT64, &obj->b42, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] b42: %d, ret = %d.", obj->b42, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c12", GMC_DATATYPE_FIXED, obj->c12, sizeof(obj->c12));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] c12, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d10", GMC_DATATYPE_BYTES, obj->d10, obj->d10len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] d10: %s, ret = %d.", obj->d10, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d11", GMC_DATATYPE_BYTES, obj->d11, obj->d10len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] d11: %s, ret = %d.", obj->d11, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "timeoutfield", GMC_DATATYPE_INT64, &obj->timeoutfield, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] timeoutfield: %d, ret = %d.", obj->timeoutfield, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return GMERR_OK;
}

int Sync1inp5TableSet(GmcStmtT *stmt, void *t)
{
    Syncinp1TableStructT *obj = (Syncinp1TableStructT *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a12", GMC_DATATYPE_INT64, &obj->a12, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] a12: %d, ret = %d.", obj->a12, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "a42", GMC_DATATYPE_INT64, &obj->a42, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] a42: %d, ret = %d.", obj->a42, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b42", GMC_DATATYPE_UINT64, &obj->b42, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] b42: %d, ret = %d.", obj->b42, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c12", GMC_DATATYPE_FIXED, obj->c12, sizeof(obj->c12));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] c12, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d10", GMC_DATATYPE_BYTES, obj->d10, obj->d10len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] d10: %s, ret = %d.", obj->d10, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Syncinp1TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return GMERR_OK;
}

int UpdateSyncinp1Data(Syncinp1TableStructT *objIn, GmcConnT *conn, GmcStmtT *stmt, int32_t writeCount, char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int32_t i = 0; i < writeCount; i++) {
        int32_t upVerVal = -1;
        int32_t dtlReservedCount = 1;
        int64_t v = i;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        // 获取upgradeVersion值并设置
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &objIn[i].a12, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &objIn[i].a42, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b42", GMC_DATATYPE_UINT64, &objIn[i].b42, sizeof(uint64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        uint32_t tryCnt = 0;
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[DeleteSyncinp1Data] delete try more 100, ret is %d.", ret);
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &objIn[i].a12, sizeof(int64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &objIn[i].a42, sizeof(int64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "b42", GMC_DATATYPE_UINT64, &objIn[i].b42, sizeof(uint64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
    }
    return ret;
}

int DeleteSyncinp1Data(Syncinp1TableStructT *objIn, GmcConnT *conn, GmcStmtT *stmt, int32_t writeCount, char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int32_t i = 0; i < writeCount; i++) {
        int32_t upVerVal = -1;
        int32_t dtlReservedCount = 1;
        int64_t v = i;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        // 获取upgradeVersion值并设置
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &objIn[i].a12, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &objIn[i].a42, sizeof(int64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        uint32_t tryCnt = 0;
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[DeleteSyncinp1Data] delete try more 100, ret is %d.", ret);
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &v, sizeof(int64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &v, sizeof(int64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
    }
    return ret;
}

int ReadSimpleTable(GmcStmtT *stmt, char *labelName, FunReadValue func)
{
    bool isFinish = false;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (isFinish || ret != GMERR_OK) {
                break;
            }
            ret = func(stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

int Readout7Value(GmcStmtT *stmt)
{
    return 0;
}

int Readout7Table(GmcStmtT *stmt, char *labelName, FunReadValue func)
{
    bool isFinish = false;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (isFinish || ret != GMERR_OK) {
                break;
            }
            ret = func(stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

int ReadSimpleTableReadinp7sValue(GmcStmtT *stmt, char *labelName)
{
    bool isFinish = false;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (isFinish || ret != GMERR_OK) {
                break;
            }
            int64_t a = 0;
            int64_t b = 0;
            int64_t c = 0;
            int32_t dtlReservedCount = 1;
            bool isNull = true;
            ret = GmcSetIndexKeyId(stmt, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            int32_t upVerVal = -1;
            ret = GmcGetUpgradeVersion(stmt, &upVerVal);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int64_t), &isNull);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(int64_t), &isNull);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(int64_t), &isNull);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int64_t), &isNull);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

int Writeinp9Table(GmcConnT *conn, GmcStmtT *stmt, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, g_inp9, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i;
        uint32_t e = i;
        uint64_t h = 1;
        int32_t dtlReservedCount = -1;

        int stringLen = 10;  // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, stringLen + 1, 0);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", i);
        char teststr15[10] = "bytes";

        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint8_t d[128] = {0};
        ret = memset_s(d, 128, 0xff, 128);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_FIXED, &d, sizeof(d));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_UINT32, &e, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_BYTES, teststr15, 10);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "h", GMC_DATATYPE_BITFIELD64, &h, sizeof(uint64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(buf);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[Writeinp9Table] write try more 100, ret is %d.", ret);
                    break;
                }
                // 重新组装报文
                ret = testGmcPrepareStmtByLabelName(stmt, g_inp9, GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                buf = (uint8_t *)malloc(stringLen + 1);
                if (buf == NULL) {
                    return -1;
                }
                memset(buf, stringLen + 1, 0);
                (void)snprintf((char *)buf, stringLen + 1, "b%08d", i);

                ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_FIXED, &d, sizeof(d));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_UINT32, &e, sizeof(uint32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_BYTES, teststr15, 10);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "h", GMC_DATATYPE_BITFIELD64, &h, sizeof(uint64_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(
                    stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                free(buf);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

int WriteExternTable(GmcConnT *conn, GmcStmtT *stmt, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, g_inp9, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i;
        uint32_t e = i;
        uint64_t h = 1;
        int32_t dtlReservedCount = 1;

        int stringLen = 10;  // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, stringLen + 1, 0);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", i);
        char teststr15[10] = "bytes";

        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint8_t d[128] = {0};
        ret = memset_s(d, 128, 0xff, 128);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_FIXED, &d, sizeof(d));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_UINT32, &e, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_BYTES, teststr15, 10);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "h", GMC_DATATYPE_BITFIELD64, &h, sizeof(uint64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(buf);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP, "[WriteExternTable] write try more 100, ret is %d.", ret);
                        break;
                    }
                    // 重新组装报文
                    ret = testGmcPrepareStmtByLabelName(stmt, g_inp9, GMC_OPERATION_INSERT);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    buf = (uint8_t *)malloc(stringLen + 1);
                    if (buf == NULL) {
                        return -1;
                    }
                    memset(buf, stringLen + 1, 0);
                    (void)snprintf((char *)buf, stringLen + 1, "b%08d", i);

                    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_FIXED, &d, sizeof(d));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_UINT32, &e, sizeof(uint32_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_BYTES, teststr15, 10);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt, "h", GMC_DATATYPE_BITFIELD64, &h, sizeof(uint64_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(
                        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    free(buf);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
            uint32_t totalNum, successNum;
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = GMERR_OK;
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

int SetinpallValue(GmcStmtT *stmt, int32_t v)
{
    int8_t a1 = v;
    int8_t a2 = v;
    int8_t a3 = v;
    int8_t a4 = v;
    int8_t b1 = v;
    int8_t b2 = v;
    int8_t b3 = v;
    int8_t b4 = v;
    int8_t c1 = v;
    int8_t c2 = v;
    int8_t c3 = v;
    int8_t c4 = v;
    int8_t c5 = v;
    int8_t c6 = v;
    int8_t c7 = v;
    int8_t c8 = v;
    int8_t c9 = v;
    int8_t c10 = v;
    int8_t a11 = v;
    int8_t a21 = v;
    int8_t a31 = v;
    int8_t a41 = v;
    int8_t b11 = v;
    int8_t b21 = v;
    int8_t b31 = v;
    int8_t b41 = v;
    int8_t c11 = v;
    int8_t c21 = v;
    int8_t c31 = v;
    int8_t c41 = v;
    int8_t c51 = v;
    int8_t c61 = v;
    int8_t c71 = v;
    int8_t c81 = v;
    int8_t c91 = v;
    int8_t c101 = v;
    int8_t a12 = v;
    int16_t a22 = v;
    int32_t a32 = v;
    int64_t a42 = v;
    uint8_t b12 = v;
    uint16_t b22 = v;
    uint32_t b32 = v;
    uint64_t b42 = v;
    uint8_t c12[1] = {0};
    int ret = memset_s(c12, 1, 0xff, 1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c22[2] = {0};
    ret = memset_s(c22, 2, 0xff, 2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c32[4] = {0};
    ret = memset_s(c32, 4, 0xff, 4);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c42[8] = {0};
    ret = memset_s(c42, 8, 0xff, 8);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c52[16] = {0};
    ret = memset_s(c52, 16, 0xff, 16);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c62[32] = {0};
    ret = memset_s(c62, 32, 0xff, 32);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c72[64] = {0};
    ret = memset_s(c72, 64, 0xff, 64);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c82[128] = {0};
    ret = memset_s(c82, 128, 0xff, 128);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c92[256] = {0};
    ret = memset_s(c92, 256, 0xff, 256);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t c102[512] = {0};
    ret = memset_s(c102, 512, 0xff, 512);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint8_t d1 = 1;
    uint16_t d3 = 1;
    uint16_t d4 = 1;
    uint32_t d5 = 1;
    uint32_t d6 = 1;
    uint64_t d7 = 1;
    uint64_t d8 = 1;

    int stringLen = 10;  // string
    uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
    if (buf == NULL) {
        return -1;
    }
    memset(buf, stringLen + 1, 0);
    (void)snprintf((char *)buf, stringLen + 1, "b%08d", v);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &a1, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &a2, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &a3, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &a4, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT8, &b1, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT8, &b2, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT8, &b3, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT8, &b4, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT8, &c1, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT8, &c2, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT8, &c3, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT8, &c4, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT8, &c5, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT8, &c6, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT8, &c7, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT8, &c8, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c9", GMC_DATATYPE_INT8, &c9, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c10", GMC_DATATYPE_INT8, &c10, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a11", GMC_DATATYPE_INT8, &a11, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a21", GMC_DATATYPE_INT8, &a21, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a31", GMC_DATATYPE_INT8, &a31, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a41", GMC_DATATYPE_INT8, &a41, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b11", GMC_DATATYPE_INT8, &b11, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b21", GMC_DATATYPE_INT8, &b21, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b31", GMC_DATATYPE_INT8, &b31, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b41", GMC_DATATYPE_INT8, &b41, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c11", GMC_DATATYPE_INT8, &c11, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c21", GMC_DATATYPE_INT8, &c21, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c31", GMC_DATATYPE_INT8, &c31, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c41", GMC_DATATYPE_INT8, &c41, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c51", GMC_DATATYPE_INT8, &c51, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c61", GMC_DATATYPE_INT8, &c61, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c71", GMC_DATATYPE_INT8, &c71, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c81", GMC_DATATYPE_INT8, &c81, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c91", GMC_DATATYPE_INT8, &c91, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c101", GMC_DATATYPE_INT8, &c101, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a12", GMC_DATATYPE_INT8, &a12, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a22", GMC_DATATYPE_INT16, &a22, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a32", GMC_DATATYPE_INT32, &a32, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a42", GMC_DATATYPE_INT64, &a42, sizeof(int64_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b12", GMC_DATATYPE_UINT8, &b12, sizeof(uint8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b22", GMC_DATATYPE_UINT16, &b22, sizeof(uint16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b32", GMC_DATATYPE_UINT32, &b32, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b42", GMC_DATATYPE_UINT64, &b42, sizeof(uint64_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c12", GMC_DATATYPE_FIXED, &c12, sizeof(c12));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c22", GMC_DATATYPE_FIXED, &c22, sizeof(c22));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c32", GMC_DATATYPE_FIXED, &c32, sizeof(c32));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c42", GMC_DATATYPE_FIXED, &c42, sizeof(c42));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c52", GMC_DATATYPE_FIXED, &c52, sizeof(c52));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c62", GMC_DATATYPE_FIXED, &c62, sizeof(c62));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c72", GMC_DATATYPE_FIXED, &c72, sizeof(c72));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c82", GMC_DATATYPE_FIXED, &c82, sizeof(c82));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c92", GMC_DATATYPE_FIXED, &c92, sizeof(c92));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c102", GMC_DATATYPE_FIXED, &c102, sizeof(c102));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_BITFIELD8, &d1, sizeof(uint8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_BITFIELD16, &d3, sizeof(uint16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_BITFIELD16, &d4, sizeof(uint16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_BITFIELD32, &d5, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_BITFIELD32, &d6, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_BITFIELD64, &d7, sizeof(uint64_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_BITFIELD64, &d8, sizeof(uint64_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d10", GMC_DATATYPE_BYTES, teststr15, 10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    int32_t dtlReservedCount = 1;
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(buf);
    ret = GmcExecute(stmt);
    return ret;
}

void MergeSubTableScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}

int LoadExtern1Vertex(GmcStmtT *stmt)
{
    char *schema = NULL;
    GmcDropVertexLabel(stmt, "extern1");
    readJanssonFile("./datalogFile/extern1.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson =
        "{\"max_record_count\":10000, \"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    int ret = GmcCreateVertexLabel(stmt, schema, configJson);
    free(schema);
    return ret;
}

int LoadExtern2Vertex(GmcStmtT *stmt)
{
    char *schema = NULL;
    GmcDropVertexLabel(stmt, "extern2");
    readJanssonFile("./datalogFile/extern2.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":false}";
    int ret = GmcCreateVertexLabel(stmt, schema, configJson);
    free(schema);
    return ret;
}

int DropExtern1Vertex(GmcStmtT *stmt)
{
    int ret = GmcDropVertexLabel(stmt, "extern1");
    return ret;
}

int DropExtern2Vertex(GmcStmtT *stmt)
{
    int ret = GmcDropVertexLabel(stmt, "extern2");
    return ret;
}

int WriteGiantTable(GmcConnT *conn, GmcStmtT *stmt, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, g_inp6, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = SetinpallValue(stmt, i);
        uint32_t tryCnt = 0;
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[WriteGiantTable] write try more 100, ret is %d.", ret);
                    break;
                }
                ret = SetinpallValue(stmt, i);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

// 非轻量化创建kv表并插入数据
int CreateKvTablephase2(char *localnamespace)
{
    int ret = 0;
    // 创建kv表
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, localnamespace);
    GmcKvDropTable(stmt, g_capv5tableName);
    ret = GmcKvCreateTable(stmt, g_capv5tableName, g_capv5configJson);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_capv5tableName);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "para1";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char key1[32] = "para2";
    char value1[32] = "aaaaaa";
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1) + 1;
    kvInfo1.value = value1;
    kvInfo1.valueLen = strlen(value1) + 1;
    ret = GmcKvSet(stmt, kvInfo1.key, kvInfo1.keyLen, kvInfo1.value, kvInfo1.valueLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return ret;
}

void CreateDatalogphase2TableTest()
{
    // 编译
    char inputFilePath[512] = "./datalogFile";
    char outputFilePath[512] = "./datalogFile";
    char localsoName[512] = "datalogStabilityFilephase2";
// 欧拉环境编译
#if defined RUN_INDEPENDENT
    CompileTest(inputFilePath, outputFilePath, localsoName, true);
    // 编译生成升级so和降级so
    CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, localsoName, true, 1);
#endif
    sleep(2);
}

int AllocGlobalMemory()
{
    AllocUserDatadata(&g_userData1);
    AllocUserDatadata(&g_userData2);
    AllocUserDatadata(&g_userData3);
    int ret = AllocreplayData(&g_replayData1, g_syncinp2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = AllocreplayData(&g_replayData2, g_syncinp3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = AllocreplayData(&g_replayData3, g_sync1inp6);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = AllocreplayData(&g_replayData4, g_syncinp4);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = AllocreplayData(&g_replayData5, g_sync1inp8);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = AllocWriteObjIn(&g_objIn);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = AllocWriteObjIn(&g_objIn1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

// 加载datalogStabilityFilephase2.so到datalognsp1命名空间，并对输入表inp0预置500条数据
int CreateNamespaceAndLoadso()
{
    int ret = 0;
    GmcConnT *inconn = NULL;
    GmcStmtT *instmt = NULL;
    char user[50] = "user";
    char filepath[100] = "./datalogFile/datalogStabilityFilephase2.so";
    char inp0init[50] = "inp0";
    CreateDatalogphase2TableTest();

    ret = testGmcConnect(&inconn, &instmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcCreateNamespace(instmt, g_datalognsp1, user);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(instmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = LoadExtern1Vertex(instmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = LoadExtern2Vertex(instmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CreateKvTablephase2(g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteSimpleTable(inconn, instmt, inp0init, 1, 500, true);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(inconn, instmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int UnLoadsoAndDropNamespace()
{
    int ret = 0;
    GmcConnT *inconn = NULL;
    GmcStmtT *instmt = NULL;
    char datalogStabilityFilephase2[50] = "datalogStabilityFilephase2";
    ret = testGmcConnect(&inconn, &instmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(instmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = TestUninstallDatalog(datalogStabilityFilephase2, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DropExtern1Vertex(instmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DropExtern2Vertex(instmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcKvDropTable(instmt, g_capv5tableName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(instmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcDropNamespace(instmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(inconn, instmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

void *MultinamespaceWriteUpateAndRead(void *arg)
{
    AW_FUN_Log(LOG_STEP, "MultinamespaceWriteUpateAndRead start");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    while (true) {
        THREAD_CONTROL_POINT();
        ret = GmcUseNamespace(stmt, g_datalognsp1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp1, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp1, WRITE_START + NORMAL_COUNT, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = UpdateSimpleTableUpdateinp7sValueprimaryindex(
            conn, stmt, g_inp1, WRITE_START + NORMAL_COUNT / 2, NORMAL_COUNT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp5, WRITE_START, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp5, WRITE_START + NORMAL_COUNT, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = UpdateSimpleTableUpdateinp7sValuesecondaryindex(
            conn, stmt, g_inp5, WRITE_START + NORMAL_COUNT / 2, NORMAL_COUNT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp51, WRITE_START, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValuesecondaryindex(
            conn, stmt, g_inp51, WRITE_START + NORMAL_COUNT / 2, NORMAL_COUNT / 2, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(
            conn, stmt, g_inp51, WRITE_START + NORMAL_COUNT / 2, NORMAL_COUNT + NORMAL_COUNT / 2, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = UpdateSimpleTableUpdateinp7sValueprimaryindextoSame(conn, stmt, g_inp51, WRITE_START, NORMAL_COUNT * 2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteExternTable(conn, stmt, 101, 100, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = ReadSimpleTable(stmt, g_inp0, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_out0, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_datalogsonsp1mid1, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_out1, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_extern2, ReadexternValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_extern1, ReadexternValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = DeleteSimpleTableDeleteinp7sValueprimaryindex(conn, stmt, g_inp1, WRITE_START, NORMAL_COUNT * 2, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValuesecondaryindexifsame(conn, stmt, g_inp51, WRITE_START, MIN_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp5Valueprimaryindex(conn, stmt, g_inp5, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValuesecondaryindex(
            conn, stmt, g_inp5, WRITE_START + NORMAL_COUNT, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValueprimaryindex(
            conn, stmt, g_inp9, WRITE_START + NORMAL_COUNT, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(ST);
    }
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "MultinamespaceWriteUpateAndRead end");
}

void *MultinamespaceWriteUpgradeAndDegrade(void *arg)
{
    AW_FUN_Log(LOG_STEP, "MultinamespaceWriteUpgradeAndDegrade start");
    GmcConnT *conn = NULL, *pubsubconn = NULL;
    GmcStmtT *stmt = NULL, *pubsubstmt = NULL;
    char datalogsoname[50] = "datalogStabilityFilephase2";
    char datalogpatchsoname[512] = "./datalogFile/datalogStabilityFilephase2_patchV2.so";
    char datalogrollbacksoname[512] = "./datalogFile/datalogStabilityFilephase2_rollbackV2.so";
    char pubsubconnName[50] = "pubsubconnName";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[5].userEpollFd;
    int ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    connOptions.connName = pubsubconnName;
    ret = TestYangGmcConnect(&pubsubconn, &pubsubstmt, GMC_CONN_TYPE_SUB, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    int32_t dataStart1 = WRITE_START;
    uint32_t writeCount1 = NORMAL_COUNT;
    uint32_t writeCount2 = NORMAL_COUNT;
    uint32_t totalCount = NORMAL_COUNT * 2;
    g_userData1.isPubsubRsc = true;
    ret = createSubscription(stmt, pubsubconn, (char *)"schemaFile/rsc2.gmjson", &g_userData1,
        dataStart1 + writeCount1 + writeCount2, g_rsc2, snCallbackphase2, rsc2GetValue);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    while (true) {
        THREAD_CONTROL_POINT();
        ret = WriteSimpleTable(conn, stmt, g_inp1, WRITE_START + NORMAL_COUNT * 2, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp2, WRITE_START, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp3, WRITE_START, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp4, WRITE_START, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 加载升级so，等待重做完成
        ret = GmimportUpgradeSo(datalogpatchsoname, g_datalognsp1, datalogsoname);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp8, WRITE_START, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp1, WRITE_START + NORMAL_COUNT * 3, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_inp4, WRITE_START + NORMAL_COUNT, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_inp8, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 加载降级so，等待重做完成
        ret = GmimportRollbackSo(datalogrollbacksoname, g_datalognsp1, datalogsoname);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteExternTable(conn, stmt, WRITE_START + NORMAL_COUNT, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_inp0, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_out0, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_rsc1, ReadRscValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_rsc2, ReadRscValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_out10, ReadRscValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_datalogsonsp1mid1, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_tran1, Readtran1out1Value);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_datalogsonsp2out4, Readinp7sValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_extern2, ReadexternValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_extern1, ReadexternValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = DeleteSimpleTableDeleteinp7sValueprimaryindex(
            conn, stmt, g_inp1, WRITE_START + NORMAL_COUNT * 2, NORMAL_COUNT * 2, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValueprimaryindex(conn, stmt, g_inp2, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValueprimaryindex(conn, stmt, g_inp3, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = Writeinp4SimpleTable(conn, stmt, g_inp4, WRITE_START, NORMAL_COUNT * 2, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValueprimaryindex(
            conn, stmt, g_inp9, WRITE_START + NORMAL_COUNT * 2, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(ST);
    }
    ret = cancelSubscription(stmt, g_rsc2, &g_userData1, dataStart1, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(pubsubconn);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "MultinamespaceWriteUpgradeAndDegrade end");
}

void *MultinamespaceWriteSubeandMergeSub(void *arg)
{
    AW_FUN_Log(LOG_STEP, "MultinamespaceWriteSubeandMergeSub start");
    GmcConnT *conn = NULL, *subconn = NULL, *mergesubconn = NULL;
    GmcStmtT *stmt = NULL, *substmt = NULL, *mergesubstmt = NULL;
    GmcSignalRegisterNotify();
    char subconnName[50] = "subconnName";
    char mergesubconnName[50] = "mergesubconnName";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[6].userEpollFd;
    int ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    connOptions.connName = subconnName;
    ret = TestYangGmcConnect(&subconn, &substmt, GMC_CONN_TYPE_SUB, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[6].userEpollFd;
    connOptions2.connName = mergesubconnName;
    ret = TestYangGmcConnect(&mergesubconn, &mergesubstmt, GMC_CONN_TYPE_SUB, &connOptions2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    while (true) {
        THREAD_CONTROL_POINT();
        ret = GmcUseNamespace(stmt, g_datalognsp1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        int32_t dataStart1 = 1;
        uint32_t writeCount1 = NORMAL_COUNT;
        uint32_t totalCount = writeCount1;
        g_userData2.isPubsubRsc = false;
        g_userData3.isPubsubRsc = false;
        SnUserDataWithFuncPubT userData3 = {0};
        ret = WriteGiantTable(conn, stmt, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteSimpleTable(conn, stmt, g_datalogsonsp1inp7, WRITE_START, NORMAL_COUNT, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = WriteExternTable(conn, stmt, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = createSubscription(stmt, subconn, (char *)"schemaFile/extern1.gmjson", &g_userData2,
            dataStart1 + writeCount1, g_extern1, snCallbackExternal, ReadsubexternValue);
        ret = createSubscription(stmt, mergesubconn, (char *)"schemaFile/extern2.gmjson", &g_userData3,
            dataStart1 + writeCount1, g_extern2, snCallbackExternal, ReadsubexternValue);

        sleep(29);
        ret = Writeinp9Table(conn, stmt, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = cancelSubscription(stmt, g_extern2, &g_userData3, dataStart1, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = WriteExternTable(conn, stmt, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = cancelSubscription(stmt, g_extern1, &g_userData2, dataStart1, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_datalogsonsp2mid6, ReadexternValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_mid7, ReadexternValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_extern2, ReadexternValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ReadSimpleTable(stmt, g_extern1, ReadexternValue);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = DeleteSimpleTableDeleteinp6Value(conn, stmt, g_inp6, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValueprimaryindex(
            conn, stmt, g_datalogsonsp1inp7, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = DeleteSimpleTableDeleteinp7sValueprimaryindex(conn, stmt, g_inp9, WRITE_START, NORMAL_COUNT, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(ST);
    }

    ret = testGmcDisconnect(subconn);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(mergesubconn);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "MultinamespaceWriteSubeandMergeSub end");
}

// public命名空间Sync.out1(normal) 重演到datalognsp1命名空间Sync.inp2(update),
// datalognsp1命名空间Sync.notifytable 重演到public命名空间Sync.inp3(transient)
void BothMergeSubandNotifyDlrWriteGiantdata(
    GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, bool isBatch, char *labelName = g_syncinp1)
{
    int ret = 0;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    g_statusmergefetchNum = 128;
    g_notifydlrfetchNum = 1;
    ret = WriteSyncinpTable(conn, stmt, labelName, vertexCount, Syncinp1TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    RandomSleep(MAX_SLEEP_TIME_WRITE);
    ret = WriteSyncinpTable(conn, stmt, labelName, vertexCount + 10, Syncinp1TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = UpdateSyncinp1Data(g_objIn1, conn, stmt, vertexCount + 10, labelName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    MergeSubTableScan(conn, stmt, g_syncout3);
    sleep(ST * 2);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, labelName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    sleep(ST * 2);
}
// public命名空间Sync.out1(normal) 重演到datalognsp1命名空间Sync.inp2(update),
// datalognsp1命名空间Sync.notifytable 重演到public命名空间Sync.inp3(transient)
void BothMergeSubandNotifyDlrWriteFewdata(
    GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, bool isBatch, char *labelName = g_syncinp1)
{
    int ret = 0;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    g_statusmergefetchNum = 1;
    g_notifydlrfetchNum = 128;
    ret = WriteSyncinpTable(conn, stmt, labelName, vertexCount, Syncinp1TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    RandomSleep(MAX_SLEEP_TIME_WRITE);
    ret = WriteSyncinpTable(conn, stmt, labelName, vertexCount + 10, Syncinp1TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = UpdateSyncinp1Data(g_objIn1, conn, stmt, vertexCount + 10, labelName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    MergeSubTableScan(conn, stmt, g_syncout3);
    RandomSleep(MAX_SLEEP_TIME_DELETE);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, labelName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    sleep(ST * 2);
}

// public命名空间Sync1.inp5(update_partial)
// 重演到public命名空间Sync.inp4(update_partial)和datalognsp1命名空间Sync1.inp6(update)
void MergeSubInptoInpTable(GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, bool isBatch)
{
    int ret = 0;
    g_statusmergefetchNum = 128;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteSyncinpTable(conn, stmt, g_sync1inp5, vertexCount, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    RandomSleep(MAX_SLEEP_TIME_WRITE);
    ret = WriteSyncinpTable(conn, stmt, g_sync1inp5, vertexCount + 10, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = UpdateSyncinp1Data(g_objIn1, conn, stmt, vertexCount + 10, g_sync1inp5);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    MergeSubTableScan(conn, stmt, g_syncmid2);
    RandomSleep(MAX_SLEEP_TIME_DELETE);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, g_sync1inp5);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    sleep(ST * 2);
}
// datalognsp1命名空间的Sync.notifytable 重演到public命名空间的Sync.inp3(transient)
void NotifyDlrtoTransientTable(GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, bool isBatch,
    char *replaytablename = g_syncinp3, char *labelName = g_syncinp2)
{
    int ret = 0;
    g_notifydlrfetchNum = 128;
    g_replayData2.replaytablename = replaytablename;
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteSyncinpTable(conn, stmt, labelName, vertexCount, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    RandomSleep(MAX_SLEEP_TIME_WRITE);
    ret = WriteSyncinpTable(conn, stmt, labelName, vertexCount + 10, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = UpdateSyncinp1Data(g_objIn1, conn, stmt, vertexCount + 10, labelName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    MergeSubTableScan(conn, stmt, g_syncout3);
    RandomSleep(MAX_SLEEP_TIME_DELETE);
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, labelName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    sleep(ST * 2);
}
// datalognsp1命名空间的Sync.notifytable 重演到public命名空间的Sync.inp4(update)
void NotifyDlrtoUpdateTable(GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, bool isBatch,
    char *labelName = g_syncinp2)
{
    int ret = 0;
    g_notifydlrfetchNum = 128;
    g_replayData2.replaytablename = g_syncinp4;
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteSyncinpTable(conn, stmt, labelName, vertexCount, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    RandomSleep(MAX_SLEEP_TIME_WRITE);
    ret = WriteSyncinpTable(conn, stmt, labelName, vertexCount + 10, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = UpdateSyncinp1Data(g_objIn1, conn, stmt, vertexCount + 10, labelName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    MergeSubTableScan(conn, stmt, g_syncout3);
    RandomSleep(MAX_SLEEP_TIME_DELETE);
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, labelName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    sleep(ST * 2);
}

// public命名空间的Sync1.mid7(rsc) 重演到datalognsp1命名空间的Sync1.inp8(update)
void RscDlrtoupdateTable(GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, bool isBatch)
{
    int ret = 0;
    g_notifydlrfetchNum = 128;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteSyncinpTable(conn, stmt, g_sync1inp7, vertexCount, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    RandomSleep(MAX_SLEEP_TIME_WRITE);
    ret = WriteSyncinpTable(conn, stmt, g_sync1inp7, vertexCount + 10, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = UpdateSyncinp1Data(g_objIn1, conn, stmt, vertexCount + 10, g_sync1inp7);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    MergeSubTableScan(conn, stmt, g_sync1inp8);
    RandomSleep(MAX_SLEEP_TIME_DELETE);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, g_sync1inp7);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    sleep(ST * 2);
}

// public命名空间的Sync1.inp7和Sync1.inp7_1 同时向Sync1.mid7(rsc)写数据 重演到datalognsp1命名空间的Sync1.inp8(update)
void RscDlrtoupdateTableWriteTwoInpTable(GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, bool isBatch)
{
    int ret = 0;
    g_notifydlrfetchNum = 128;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteTwoTable(conn, stmt, g_sync1inp7, g_sync1inp71, vertexCount, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteTwoTable(conn, stmt, g_sync1inp7, g_sync1inp71, vertexCount + 10, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = UpdateSyncinp1Data(g_objIn1, conn, stmt, vertexCount + 10, g_sync1inp7);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    MergeSubTableScan(conn, stmt, g_sync1inp8);
    RandomSleep(MAX_SLEEP_TIME_DELETE);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, g_sync1inp7);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, g_sync1inp71);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    sleep(ST * 2);
}

// public命名空间的Sync1.inp9和Sync1.inp10 写两张表，触发订阅
void WriteTwoInpTableMergeSub(GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, bool isBatch)
{
    int ret = 0;
    g_notifydlrfetchNum = 128;
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteTwoTable(conn, stmt, g_sync1inp9, g_sync1inp10, vertexCount, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = WriteTwoTable(conn, stmt, g_sync1inp9, g_sync1inp10, vertexCount + 10, Sync1inp5TableSet, g_objIn, isBatch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = UpdateSyncinp1Data(g_objIn1, conn, stmt, vertexCount + 10, g_sync1inp9);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    RandomSleep(MAX_SLEEP_TIME_DELETE);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, g_sync1inp9);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = DeleteSyncinp1Data(g_objIn, conn, stmt, vertexCount + 10, g_sync1inp10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    sleep(ST * 2);
}

void *MergeSubandNotifyDLR(void *arg)
{
    AW_FUN_Log(LOG_STEP, "MergeSubandNotifyDLR start");
    AsyncUserDataT data = { 0 };
    GmcConnT *conn = NULL, *subconn = NULL, *replayconn1 = NULL, *replayconn2 = NULL;
    GmcStmtT *stmt = NULL, *substmt = NULL, *replaystmt1 = NULL, *replaystmt2 = NULL;
    char mergeSubconnName[50] = "mergeSubconnName";
    int ret = testGmcConnect(&replayconn1, &replaystmt1, GMC_CONN_TYPE_ASYNC, true, g_epoll_reg_info, NULL, NULL);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(replaystmt1, g_datalognsp1, use_namespace_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = testGmcConnect(&replayconn2, &replaystmt2, GMC_CONN_TYPE_ASYNC, true, g_epoll_reg_info, NULL, NULL);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(data));
    ret = GmcUseNamespaceAsync(replaystmt2, g_testNameSpace, use_namespace_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSubConnect(&subconn, &substmt, 1, g_epoll_reg_info, mergeSubconnName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    g_replayData1.subConn = subconn;
    g_replayData1.syncConn = replayconn1;
    g_replayData1.syncStmt = replaystmt1;
    g_replayData2.subConn = subconn;
    g_replayData2.syncConn = replayconn2;
    g_replayData2.syncStmt = replaystmt2;
    g_replayData3.subConn = subconn;
    g_replayData3.syncConn = replayconn1;
    g_replayData3.syncStmt = replaystmt1;
    g_replayData4.subConn = subconn;
    g_replayData4.syncConn = replayconn2;
    g_replayData4.syncStmt = replaystmt2;
    g_replayData5.subConn = subconn;
    g_replayData5.syncConn = replayconn1;
    g_replayData5.syncStmt = replaystmt1;
    ret = createDlrSubscription(stmt, subconn, (char *)"datalogFile/Syncout1.gmjson", &g_replayData1, 100, g_syncinp2,
                                sn_callback_status_merge_dlr_async);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = createDlrSubscription(stmt, subconn, (char *)"datalogFile/Sync1inp5.gmjson", &g_replayData3, 100, g_sync1inp6,
                                sn_callback_status_merge_dlr_async);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = createDlrSubscription(stmt, subconn, (char *)"datalogFile/Sync1mid10.gmjson", &g_replayData3, 100,
                                g_sync1mid10, sn_callback_status_merge_null);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = createDlrSubscription(stmt, subconn, (char *)"datalogFile/Sync1out9.gmjson", &g_replayData3, 100, g_sync1out9,
                                sn_callback_status_merge_null);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = createDlrSubscription(stmt, subconn, (char *)"datalogFile/Sync1inp5.gmjson", &g_replayData4, 100, g_syncinp4,
                                sn_callback_status_merge_dlr_async02);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = createDlrSubscription(stmt, subconn, (char *)"datalogFile/Sync1mid7.gmjson", &g_replayData5, 100, g_sync1inp8,
                                sn_callback_status_merge_dlr_async);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = createDlrSubscription(stmt, subconn, (char *)"datalogFile/Syncnotifytable.gmjson", &g_replayData2, 100,
                                g_syncinp3, sn_callback_async);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    while (true) {
        uint32_t casenumber = 8, writecountN = 19;
        time_t timer = time(0);
        if (!timer) {
            printf("[ERROR] get time fail\n");
        }
        srand((uint32_t)timer);
        int32_t entercase = rand() % casenumber;
        int32_t enterwritecount = rand() % writecountN + 1;
        g_replayData1.countcallback = 0;
        g_replayData2.countcallback = 0;
        g_replayData3.countcallback = 0;
        g_replayData4.countcallback = 0;
        switch (entercase) {
            case 0:
                BothMergeSubandNotifyDlrWriteGiantdata(conn, stmt, enterwritecount, true);
                break;
            case 1:
                BothMergeSubandNotifyDlrWriteFewdata(conn, stmt, enterwritecount, true);
                break;
            case 2:
                MergeSubInptoInpTable(conn, stmt, enterwritecount, true);
                break;
            case 3:
                NotifyDlrtoTransientTable(conn, stmt, enterwritecount, true);
                break;
            case 4:
                NotifyDlrtoUpdateTable(conn, stmt, enterwritecount, true);
                break;
            case 5:
                RscDlrtoupdateTable(conn, stmt, enterwritecount, true);
                break;
            case 6:
                RscDlrtoupdateTableWriteTwoInpTable(conn, stmt, enterwritecount, true);
                break;
            case 7:
                WriteTwoInpTableMergeSub(conn, stmt, enterwritecount, true);
                break;
            default:
                break;
        }
        sleep(3);
    }
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = cancelDlrSubscription(stmt, g_syncinp2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = cancelDlrSubscription(stmt, g_sync1inp6);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = cancelDlrSubscription(stmt, g_syncinp4);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = cancelDlrSubscription(stmt, g_sync1mid10);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = cancelDlrSubscription(stmt, g_sync1out9);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = cancelDlrSubscription(stmt, g_sync1inp8);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_datalognsp1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = cancelDlrSubscription(stmt, g_syncinp3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(replayconn1, replaystmt1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(replayconn2, replaystmt2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(subconn, substmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int32_t i = 0; i < DLR_WRITE_COUNT; i++) {
        free(g_objIn[i].d10);
        free(g_objIn[i].d11);
        free(g_objIn1[i].d10);
        free(g_objIn1[i].d11);
    }
    free(g_replayData1.userBuf);
    free(g_replayData2.userBuf);
    free(g_replayData3.userBuf);
    free(g_replayData4.userBuf);
    free(g_replayData5.userBuf);
    AW_FUN_Log(LOG_STEP, "MergeSubandNotifyDLR end");
}

#endif  // DATALOG_STABILITY_TEST_H
