/* ****************************************************************************
 Description  :长稳用例头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2021/08/25
**************************************************************************** */
#ifndef _LONG_STABILITY_H_
#define _LONG_STABILITY_H_

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"

#include "t_datacom_lite.h"
#include "cpuControl.h"

#if defined ENV_RTOSV2X   // IoT设备
#define RECORD_NUM 10000  // complex 表扫描数据量
#define OP_NUM 50         // 表数据单次操作量
#define OP_NUM_ASYNC 10   // 异步单次操作数
#define BATCH_NUM 20      // 批量操作
#define WRITE_NUM 1000    // 订阅长稳用例生产者数据量
#define ST 15
#define GRAPH_WRITE_NUM 2000           // 表预置数据量
#define CONNECTION_POOL_THREADS_NUM 1  // 建连断链线程数
#define BATCH_MAX_COUNT 30             // 一次批量最大点个数
#define RES_WRITE_COUNT 100            // 资源表写的数据量
#define RES_TRANS_WRITE_COUNT 500
#define STMT_NUM 2
#define IFGENERAL_32K_NUM 10
#define SPCOMPLEX_NUM 10
#define IP4FORWARD_8K_NUM 2000
#define IP4FOWARD_MINI_NUM 2000
#define IP4FORWARD_THR_NUM 4  // ip4fowardLSTest线程数量
#define IF_THR_NUM 2          // ifStabilityTest线程数量
#define THR_NUM 1             // 线程数量
int g_sub_num = 1;
#else
#define RECORD_NUM 100000  // complex 表扫描数据量
#define OP_NUM 2000        // 表数据单次操作量
#define OP_NUM_ASYNC 200   // 异步单次操作数
#define BATCH_NUM 20       // 批量操作
#define WRITE_NUM 5000     // 订阅长稳用例生产者数据量
#define ST 8
#define GRAPH_WRITE_NUM 10000           // 表预置数据量
#define CONNECTION_POOL_THREADS_NUM 50  // 建连断链线程数
#define BATCH_MAX_COUNT 300
#define RES_WRITE_COUNT 1000  // 资源表写的数据量
#define RES_TRANS_WRITE_COUNT 1000
#define STMT_NUM 200
#define IFGENERAL_32K_NUM 100  // GRAPH_WRITE_NUM/200
#define SPCOMPLEX_NUM 100      // GRAPH_WRITE_NUM/200
#define IP4FORWARD_8K_NUM 20000
#define IP4FOWARD_MINI_NUM 20000
#define IP4FORWARD_THR_NUM 8  // ip4fowardLSTest线程数量
#define IF_THR_NUM 6          // ifStabilityTest线程数量
#define THR_NUM 2             // 线程数量
int g_sub_num = 3;
#endif

#define THREADS_NUM_MAX 100  // 1个场景最大线程数，暂定100
#define GMERR_LOCK_NOT_AVAILABLE_CIRCLE_TIME 100
#define IP4FORWARD_F1_LEN 5
#define IP4FORWARD_F7_LEN 5
#define IP4FORWARD_BITMAP_LEN 8
#define AGE_TASK_CNT 1000
char g_ip4forwardName[128] = "testforward";
char g_ip4forwardPk[128] = "primary_key";
char g_ip4forwardLocalKey[128] = "local_key";
char g_ip4forwardhashclusterkey1[128] = "hashcluster_key1";
char g_ip4forwardhashclusterkey2[128] = "hashcluster_key2";
char g_ip4forwardhashclusterkey3[128] = "hashcluster_key3";

char g_longStabilityLabel_config[] = "{\"defragmentation\":true}";
char g_longStabilityLabelccehConfig[] = "{\"defragmentation\":true, \"hash_type\": \"cceh\"}";

void longStabilityStartServer()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"minFragmentationMemThreshold=1\"");
    // 起服务，初始化
    system("sh $TEST_HOME/tools/start.sh -f");
}

// 设置场景/线程数/数量标志位
int longStabilitySetSceneFlag(char **argvInput, int16_t *sceneFlag, int16_t *sceneThreadN, int16_t sceneNum,
    uint32_t *dataN, uint32_t defaultDataN)
{
    char delims[] = ",";
    char *sceneN = NULL;
    char *threadsN = NULL;
    char *pSave1 = NULL;
    char *pSave2 = NULL;
    int i = 0;
    int k = 0;
    if (0 == strcmp(argvInput[1], "-s")) {
        if (argvInput[2]) {
            if (argvInput[3]) {
                if (0 == strcmp(argvInput[3], "-t")) {
                    if (argvInput[4]) {
                        sceneN = strtok_r(argvInput[2], delims, &pSave1);
                        threadsN = strtok_r(argvInput[4], delims, &pSave2);
                        if (sceneN != NULL) {
                            i = atoi(sceneN);
                            if (i > sceneNum || i == 0) {
                                printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                                return 1;
                            } else {
                                sceneFlag[i] = 1;  // 设置场景flag标志
                                if (threadsN != NULL) {
                                    k = atoi(threadsN);
                                    if (k > THREADS_NUM_MAX || k == 0) {
                                        printf("parameter 4 error,should be >0 && <= %d,eg:1,4,3\n", THREADS_NUM_MAX);
                                        return 1;
                                    } else {
                                        sceneThreadN[i] = k;  // 设置对应的场景的线程数
                                    }
                                }
                            }
                        }
                        while (sceneN != NULL) {
                            sceneN = strtok_r(NULL, delims, &pSave1);
                            // printf("sceneN:%s\n",sceneN);
                            if (sceneN != NULL) {
                                i = atoi(sceneN);
                                if (i > sceneNum || i == 0) {
                                    printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                                    return 1;
                                } else {
                                    sceneFlag[i] = 1;
                                    threadsN = strtok_r(NULL, delims, &pSave2);
                                    if (threadsN != NULL) {
                                        k = atoi(threadsN);
                                        if (k > THREADS_NUM_MAX || k == 0) {
                                            printf(
                                                "parameter 4 error,should be >0 && <= %d,eg:1,4,3\n", THREADS_NUM_MAX);
                                            return 1;
                                        } else {
                                            sceneThreadN[i] = k;  // 设置对应的场景的线程数
                                        }
                                    } else {
                                        sceneThreadN[i] = 1;  // threadsN为空设置默认线程数
                                    }
                                }
                            }
                        }
                        if (argvInput[5] != NULL) {
                            if (0 == strcmp(argvInput[5], "-n")) {
                                if (argvInput[6]) {
                                    (*dataN) = atoi(argvInput[6]);
                                    if ((*dataN) == 0) {
                                        printf("parameter 6 error,should be number >0,eg:40000\n");
                                        return 1;
                                    }
                                } else {
                                    (*dataN) = defaultDataN;  // 默认值
                                }
                            } else {
                                printf("parameter 5 error,should be '-n'\n");
                                return 1;
                            }
                        } else {
                            (*dataN) = defaultDataN;  // 默认值
                        }
                    } else {
                        // argv[4]为空则默认启动1个线程
                        (*dataN) = defaultDataN;  // 默认值
                        sceneN = strtok(argvInput[2], delims);
                        if (sceneN != NULL) {
                            i = atoi(sceneN);
                            if (i > sceneNum || i == 0) {
                                printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                                return 1;
                            }
                            sceneFlag[i] = 1;
                            sceneThreadN[i] = 1;
                        }
                        while (sceneN != NULL) {
                            sceneN = strtok(NULL, delims);
                            if (sceneN != NULL) {
                                i = atoi(sceneN);
                                if (i > sceneNum || i == 0) {
                                    printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                                    return 1;
                                }
                                sceneFlag[i] = 1;
                                sceneThreadN[i] = 1;
                            }
                        }
                    }
                } else {
                    printf("parameter 3 error,should be '-t'\n");
                    return 1;
                }
            } else  // argv[3]为空的情况
            {
                (*dataN) = defaultDataN;  // 默认值
                sceneN = strtok(argvInput[2], delims);
                if (sceneN != NULL) {
                    i = atoi(sceneN);
                    if (i > sceneNum || i == 0) {
                        printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                        return 1;
                    }
                    sceneFlag[i] = 1;
                    sceneThreadN[i] = 1;
                }
                while (sceneN != NULL) {
                    sceneN = strtok(NULL, delims);
                    if (sceneN != NULL) {
                        i = atoi(sceneN);
                        if (i > sceneNum || i == 0) {
                            printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                            return 1;
                        }
                        sceneFlag[i] = 1;
                        sceneThreadN[i] = 1;
                    }
                }
            }
        } else {
            // argv[2]为空则启动默认的原V3场景
            (*dataN) = defaultDataN;
            for (int j = 1; j < sceneNum + 1; j++) {
                sceneFlag[j] = 1;
                sceneThreadN[j] = 1;
            }
        }
    } else {
        printf("parameter 1 error,should be '-s'\n");
        return 1;
    }
    // for(int j=1;j<sceneNum+1;j++)
    //{
    // printf("sceneThreadN[%d]:%d sceneThreadN[%d]:%d\n",j,sceneFlag[j],j,sceneThreadN[j]);
    //}
    printf("(*dataN):%u\n", (*dataN));
    return 0;
}

// 线程里设置数据量
void longStabilitySetDataVolume(void *args, uint32_t *DataVolume, uint32_t defaultValue)
{
    if (args && *(uint32_t *)args != 0) {
        (*DataVolume) = *(uint32_t *)args;
    } else {
        (*DataVolume) = defaultValue;
    }
}

// 线程里设置object数据量
void longStabilitySetObjectDataVolume(void *args, uint32_t *count, uint32_t defaultValue)
{
    if (args && *(uint32_t *)args != 0) {
        (*count) = *(uint32_t *)args;
    } else {
        (*count) = defaultValue;
    }
}

int create_testforward_label()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *test_schema = NULL;

    readJanssonFile("./schemaFile/testforward.gmjson", &test_schema);
    EXPECT_NE((void *)NULL, test_schema);

    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema, g_longStabilityLabelccehConfig);
    if (ret != GMERR_DUPLICATE_TABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    if (ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE) {
        printf("[INFO]Test create label success \n");
    }
    free(test_schema);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

void long_stability_whold_label_scan(GmcStmtT *stmt, char *labelName)
{
    int ret = 0;
    bool isFinish = true;
    uint32_t fetchNum = 0;
    // 全表扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret == GMERR_OK) {
        while (1) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_ASSERT_INT32(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            fetchNum++;
        }
    } else if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        printf("long_stability_whold_label_scan:lalbelName: %s\n", labelName);
        TEST_ASSERT_INT32(GMERR_OK, ret);
    }
}

int checkAccountStatus(GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    EXPECT_NE(GMC_CHECK_STATUS_ABNORMAL, checkStatus);
    return ret;
}

int GetAgeTaskCnt(int *value)
{
    char command[1024] = {0};
    int ret = snprintf(command, sizeof(command), "%s/gmsysview -q %s -s %s | grep WAITING | wc -l", g_toolPath,
        "V\\$QRY_AGE_TASK", g_connServer);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

void longStabilityAgeCheck(GmcStmtT *stmt, const char *labelName, uint8_t partitionId, bool isAbnormal)
{
    int ret = 0;
    int ageTaskCnt = 0;
    ret = GetAgeTaskCnt(&ageTaskCnt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 获取当前老化任务数，避免太多处理不过来
    if (ageTaskCnt < AGE_TASK_CNT) {
        ret = GmcBeginCheck(stmt, labelName, partitionId);
        if (ret == GMERR_OK) {
            do {
                ret = GmcEndCheck(stmt, labelName, partitionId, isAbnormal);
                if (ret == GMERR_OK) {
                    usleep(100);
                    break;
                } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                    usleep(100);
                } else {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            if (ret == GMERR_OK) {
                ret = checkAccountStatus(stmt, labelName);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE &&
                   ret != GMERR_TABLE_IN_CHECKING) {
            printf("labelName:%s GmcBeginCheck failed\n", labelName);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        sleep(10);
    }
}

void longStabilityCleanEnv(void)
{
    int ret = close_epoll_thread();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testEnvClean();
}

int TestLongStabilityAffactRows(GmcStmtT *stmt, int32_t expect_value)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(expect_value, affectRows);
    return expect_value == affectRows ? GMERR_OK : 1;
}
void TestSetVertexPkPropertyByName(GmcStmtT *stmt, int i, void *fixedValue)
{
    int ret = 0;

    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_FIXED, fixedValue, IP4FORWARD_F1_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t f2Value = i % 5000;
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t f3Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t f4Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint8_t f5Value = i % 256;
    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT8, &f5Value, sizeof(uint8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}
void TestSetVertexPropertyByName(GmcStmtT *stmt, int i, void *fixedValue)
{
    int ret = 0;

    uint32_t f2Value = i % 5000;
    uint32_t f4Value = i;

    GmcBitMapT bitMap = {0, 7, NULL};
    uint8_t f6bits[IP4FORWARD_BITMAP_LEN / 8] = {0xff};
    bitMap.bits = f6bits;
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_BITMAP, &bitMap, sizeof(bitMap));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_FIXED, fixedValue, IP4FORWARD_F7_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void write_ip4foward_data_sync(GmcConnT *conn, GmcStmtT *stmt, int start_pk, int end_pk, int set_type)
{
    int ret = 0;
    char *f1_value = (char *)"fixed";
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;

    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_REPLACE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    if (set_type == 0) {
        for (int loop = start_pk; loop < end_pk; loop++) {
            TestSetVertexPkPropertyByName(stmt, loop, f1_value);
            TestSetVertexPropertyByName(stmt, loop, f1_value);
            ret = GmcExecute(stmt);
            if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE) {
                TEST_ASSERT_INT32(GMERR_OK, 0);
            } else {
                TEST_ASSERT_INT32(GMERR_OK, ret);
            }
        }
    }
    // 批量写
    if (set_type == 1) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        for (int loop = start_pk; loop < end_pk; loop++) {
            TestSetVertexPkPropertyByName(stmt, loop, f1_value);
            TestSetVertexPropertyByName(stmt, loop, f1_value);
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            GmcResetStmt(stmt);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        if (ret == GMERR_OK) {
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        GmcBatchDestroy(batch);
    }
}

void insert_ip4foward_data_sync(GmcConnT *conn, GmcStmtT *stmt, int startPk, int endPk, int setType)
{
    int ret = 0;
    char *f1_value = (char *)"fixed";
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;

    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_INSERT);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    if (setType == 0) {
        for (int loop = startPk; loop < endPk; loop++)  // 一个个vertex写
        {
            TestSetVertexPkPropertyByName(stmt, loop, f1_value);
            TestSetVertexPropertyByName(stmt, loop, f1_value);
            ret = GmcExecute(stmt);
            if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE ||
                ret == GMERR_PRIMARY_KEY_VIOLATION) {
                TEST_ASSERT_INT32(GMERR_OK, 0);
            } else {
                TEST_ASSERT_INT32(GMERR_OK, ret);
            }
        }
    }
    // 批量写
    if (setType == 1) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        for (int loop = startPk; loop < endPk; loop++) {
            TestSetVertexPkPropertyByName(stmt, loop, f1_value);
            TestSetVertexPropertyByName(stmt, loop, f1_value);
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            GmcResetStmt(stmt);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE ||
            ret == GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        if (ret == GMERR_OK) {
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        GmcBatchDestroy(batch);
    }
}
void set_pk_index(GmcStmtT *stmt, int pk, void *fixed_value)
{
    int ret = 0;
    uint32_t f2Value = (pk % 5000);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, fixed_value, IP4FORWARD_F1_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_ip4forwardPk);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void update_set_test(GmcStmtT *stmt, int loop)
{
    int ret = 0;
    uint32_t f8Value = (loop + 66) % (GRAPH_WRITE_NUM * 2);
    uint32_t f9Value = (loop + 66) % 5000;
    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT32, &f8Value, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_UINT32, &f9Value, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 主键更新
void update_ip4foward_by_primary_key(GmcStmtT *stmt, int start_pk, int end_pk)
{
    int ret = 0;
    char *f1_value = (char *)"fixed";
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_UPDATE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    for (int i = start_pk; i < end_pk; i++) {
        set_pk_index(stmt, i, f1_value);
        // 设置更新的值
        update_set_test(stmt, i);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_PRIMARY_KEY_VIOLATION &&
            ret != GMERR_UNIQUE_VIOLATION && ret != GMERR_OUT_OF_MEMORY) {
            TEST_ASSERT_INT32(GMERR_OK, ret);
        }
    }
}

// merge
void merge_ip4foward_by_primary_key(GmcStmtT *stmt, int start_pk, int end_pk)
{
    int ret = 0;
    char *f1_value = (char *)"fixed";
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_MERGE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    for (int i = start_pk; i < end_pk; i++) {
        set_pk_index(stmt, i, f1_value);
        // 设置更新的值
        TestSetVertexPropertyByName(stmt, i, f1_value);
        update_set_test(stmt, i);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_PRIMARY_KEY_VIOLATION &&
            ret != GMERR_UNIQUE_VIOLATION && ret != GMERR_OUT_OF_MEMORY) {
            TEST_ASSERT_INT32(GMERR_OK, ret);
        }
    }
}

// 主键删除
void remove_ip4foward_by_primary_key(GmcStmtT *stmt, int start_pk, int end_pk)
{
    int ret = 0;
    char *f1_value = (char *)"fixed";
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_DELETE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    for (int i = start_pk; i < end_pk; i++) {
        set_pk_index(stmt, i, f1_value);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

// 【  随机产生一个【0,4w】数值a，对 【a，a+2000]】间的数据进行：主键写--主键更新--主键删除操作】
void recycle_write_remove_sync(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    srand((uint32_t)time(0));
    int index = rand() % countN;
    // write
    write_ip4foward_data_sync(conn, stmt, index, index + OP_NUM, 0);
    sleep(ST);

    if (index % 2 == 0) {  // update
        update_ip4foward_by_primary_key(stmt, index, index + OP_NUM);
        sleep(ST);
    } else {  // remove
        remove_ip4foward_by_primary_key(stmt, index, index + OP_NUM);
        sleep(ST);
    }
    // printf("INFO[thread %d]:graph recycle_write_remove_sync \n",n);
}

void read_ip4foward_by_get(GmcStmtT *stmt)
{
    int ret = 0;
    bool isNull = false;
    char f1_value[IP4FORWARD_F1_LEN] = {0};
    uint32_t f2_value = 0;
    uint32_t f3_value = 0;
    uint32_t f4_value = 0;
    char f7_value[IP4FORWARD_F1_LEN] = {0};
    uint32_t f8_value = 0;
    uint32_t f9_value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "F1", f1_value, IP4FORWARD_F1_LEN, &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2_value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "F3", &f3_value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "F4", &f4_value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "F9", &f9_value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "F7", f7_value, IP4FORWARD_F1_LEN, &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "F8", &f8_value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 【  随机产生一个【0,4w】数值a，对 【a，a+2000】间的数进行主键索引更新  】
void update_thread_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    char *f1_value = (char *)"fixed";

    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    srand((uint32_t)time(0));
    int loop = rand() % countN;

    update_ip4foward_by_primary_key(stmt, loop, loop + OP_NUM);
    sleep(ST);
    // printf("[INFO]:update_thread_ip4foward \n");
}

// 主键批量更新
void batch_update_by_pk(GmcConnT *conn, GmcStmtT *stmt, int32_t start_pk, int32_t end_pk)
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    char *f1_value = (char *)"fixed";
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int i = start_pk; i < end_pk; i++) {
        set_pk_index(stmt, i, f1_value);
        // 设置更新的值
        update_set_test(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    if (ret == GMERR_OK) {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
}

// 主键批量删除
void batch_delete_by_pk(GmcConnT *conn, GmcStmtT *stmt, int32_t start_pk, int32_t end_pk)
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    char *f1_value = (char *)"fixed";
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int i = start_pk; i < end_pk; i++) {
        set_pk_index(stmt, i, f1_value);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    if (ret == GMERR_OK) {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
}

// 【随机产生一个【0,4w】的数a，【1,21】间的随机数b，对【a，a+b】进行批量操作，进行批量写，更新，删除操作】
void batch_operation_thread_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    char *f1_value = (char *)"fixed";
    uint32_t total_op_num = 0, success_op_num = 0;
    // 将随机批量操作数量改大，改成1000
    int batchNum = 1024;

    srand((uint32_t)time(0));  // 随机产生一个区间进行批写操作
    int start = rand() % countN;
    int end = (rand() % batchNum) + 1;

    // 随机操作insert、update、delete中的一个
    int flag = rand() % 3;
    if (flag == 0) {
        // batch write
        write_ip4foward_data_sync(conn, stmt, start, start + end, 1);
        sleep(ST);
    } else if (flag == 1) {
        // batch update
        batch_update_by_pk(conn, stmt, start, start + end);
        sleep(ST);
    } else {
        // batch remove
        batch_delete_by_pk(conn, stmt, start, start + end);
        sleep(ST);
    }
    // batch remove
    batch_delete_by_pk(conn, stmt, start, start + end);
    sleep(ST);
    // printf("[INFO]:batch_operation_thread_ip4foward \n");
}
void set_hashcluster_key1(GmcStmtT *stmt, uint32_t keyValue)
{
    int ret = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_ip4forwardhashclusterkey1);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void set_hashcluster_key2(GmcStmtT *stmt, int32_t keyValue, void *fixed_value)
{
    int ret = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &keyValue, sizeof(uint8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, fixed_value, IP4FORWARD_F7_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_ip4forwardhashclusterkey2);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void set_hashcluster_key3(GmcStmtT *stmt, int32_t keyValue)
{
    int ret = 0;
    uint32_t f10Value = keyValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f10Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f10Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &f10Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_ip4forwardhashclusterkey3);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

// 【  随机产生一个【0,5000】数进行非唯一hashcluster 条件扫描，随机产生一个【0,4w】的数进行唯一hashcluster扫描 】
void hashcluster_scan_thread_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    char *f1_value = (char *)"fixed";
    // 随机产生一个【0,5000】的数进行非唯一hashcluster条件扫描
    srand((uint32_t)time(0));
    uint32_t loop = rand() % (countN / 8);
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    set_hashcluster_key1(stmt, loop);
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    if (ret == GMERR_OK) {
        ret = GmcFetch(stmt, &isFinish);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (!isFinish) {
            read_ip4foward_by_get(stmt);
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    // 随机产生一个【0,4w】的数进行唯一hashcluster扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    int32_t k = rand() % countN;
    int32_t end = k + 666;
    for (int32_t index = k; index < end; index++) {
        int32_t keyValue = index;
        set_hashcluster_key3(stmt, keyValue);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                read_ip4foward_by_get(stmt);
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    }
    // printf("[iNFO]:hashcluster_scan_thread_ip4foward \n");
    sleep(ST);
}

// 【  随机产生一个【0,5000】数按hashcluster非唯一索引获取表记录数】
void get_record_by_hashcluster(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    char *f1_value = (char *)"fixed";

    srand((uint32_t)time(0));
    int loop = rand() % (countN / 8);
    uint64_t count = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_ip4forwardName, g_ip4forwardhashclusterkey1, &count);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    sleep(ST);
}

// 随机产生一个【0,4w】数值a，对 【a，a+666】间的数进行主键扫描，然后对【0,4w】间的数据进行主键删除操作，串行操作
void remove_and_scan_by_primary_key(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    char *f1_value = (char *)"fixed";
    // scan by primary key
    int index = rand() % countN;
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = index; loop < (index + OP_NUM / 10); loop++) {
        set_pk_index(stmt, loop, f1_value);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                read_ip4foward_by_get(stmt);
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
    // remove
    remove_ip4foward_by_primary_key(stmt, index, index + OP_NUM / 10);
    sleep(ST);
    // printf("[Graph info]:ip4foward remove_by_primary_key \n ");
}

// 【  对【0,4w】间的数据进行hashcluster条件扫描操作】
void hashcluster_scan_thread2_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = 0; loop < countN; loop++) {
        set_hashcluster_key3(stmt, loop);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                read_ip4foward_by_get(stmt);
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
    sleep(1);
}

// 【 随机产生一个【0,4w】数值a，对 【a，a+2000]】间的数据进行localkey升序范围扫描删除
void localkey_scan_remove_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    unsigned int arrLen = 2;
    uint32_t l_val_scan = 0;
    uint32_t r_val_scan = 0;
    char *f1_value = (char *)"fixed";
    GmcPropValueT *leftKeyProps_scan = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (leftKeyProps_scan == NULL) {
        return;
    }
    GmcPropValueT *rightKeyProps_scan = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (rightKeyProps_scan == NULL) {
        free(leftKeyProps_scan);
        return;
    }
    GmcRangeItemT items_sc[arrLen];

    // 适配GmcSetKeyRange接口
    GmcStmtT *stmtSetKey = NULL;
    ret = GmcAllocStmt(conn, &stmtSetKey);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (stmtSetKey == NULL) {
        return;
    }

    srand((uint32_t)time(0));
    int loop = rand() % countN;
    ret = testGmcPrepareStmtByLabelName(stmtSetKey, g_ip4forwardName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    l_val_scan = loop;
    r_val_scan = loop + OP_NUM;
    // printf("[INFO] l_val_scan:%u  r_val_scan:%u\n",l_val_scan,r_val_scan);
    // 设置左值
    leftKeyProps_scan[0].type = GMC_DATATYPE_FIXED;
    leftKeyProps_scan[0].value = f1_value;
    leftKeyProps_scan[0].size = IP4FORWARD_F7_LEN;
    leftKeyProps_scan[1].type = GMC_DATATYPE_UINT32;
    leftKeyProps_scan[1].value = &l_val_scan;
    leftKeyProps_scan[1].size = sizeof(uint32_t);
    // 设置右值
    rightKeyProps_scan[0].type = GMC_DATATYPE_FIXED;
    rightKeyProps_scan[0].value = f1_value;
    rightKeyProps_scan[0].size = IP4FORWARD_F7_LEN;
    rightKeyProps_scan[1].type = GMC_DATATYPE_UINT32;
    rightKeyProps_scan[1].value = &r_val_scan;
    rightKeyProps_scan[1].size = sizeof(uint32_t);

    items_sc[0].lValue = &leftKeyProps_scan[0];
    items_sc[0].rValue = &rightKeyProps_scan[0];
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items_sc[0].order = GMC_ORDER_ASC;
    items_sc[1].lValue = &leftKeyProps_scan[1];
    items_sc[1].rValue = &rightKeyProps_scan[1];
    items_sc[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[1].rFlag = GMC_COMPARE_RANGE_OPEN;
    items_sc[1].order = GMC_ORDER_ASC;

    ret = GmcSetKeyRange(stmtSetKey, items_sc, arrLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmtSetKey, g_ip4forwardLocalKey);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmtSetKey);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    if (stmtSetKey) {
        GmcFreeStmt(stmtSetKey);
    }
    sleep(ST);
    free(leftKeyProps_scan);
    free(rightKeyProps_scan);
}

void multiple_object_ddl(GmcConnT *conn, const uint32_t countN)
{
    int ret = 0;
    GmcStmtT *stmt[countN];
    memset(stmt, 0, sizeof(stmt));
    char *f1Value = (char *)"fixed";
    int allocStmtSuccessNum = 0;

    // write
    allocStmtSuccessNum = 0;
    for (int i = 0; i < countN; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        if (ret == GMERR_OK) {
            allocStmtSuccessNum++;
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
            break;
        }
    }
    srand((uint32_t)time(0));
    if (allocStmtSuccessNum != 0) {
        int j = rand() % allocStmtSuccessNum;
        write_ip4foward_data_sync(conn, stmt[j], j, j + 1, 0);
        // update
        // update_ip4foward_by_primary_key(stmt[j],j,j+1);//主键update
        merge_ip4foward_by_primary_key(stmt[j], j, j + 1);  // 主键merge
        // remove
        remove_ip4foward_by_primary_key(stmt[j], j, j + 1);

        for (int i = 0; i < allocStmtSuccessNum; i++) {
            GmcFreeStmt(stmt[i]);
            stmt[i] = NULL;
        }
    }
    sleep(ST);
    // printf("info[thread %d]:graph multiple_object_ddl \n",n);
}

// 对账老化
void aging_thread_ip4foward(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    bool isFinish = false;
    bool isAbnormal = false;

    srand((uint32_t)time(0));
    int loop = rand() % 200 + 50;
    sleep(loop);
    longStabilityAgeCheck(stmt, g_ip4forwardName, 0xff, isAbnormal);
}

void sub_callback_with_old_ip4foward(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
        }
    }
}

void *sub_ip4foward(void *arg)
{
    int ret = 0;
    char *sub_info = NULL;
    int chanRingLen = 256;
    const char *subConnName = (const char *)"subConnip4foward";
    GmcStmtT *stmt_sub = NULL;
    GmcConnT *testSubConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char *subName = "subVertexip4foward";
    SnUserDataT *user_data = NULL;

    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅连接
    ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    readJanssonFile("schemaFile/test_schema_subinfo.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    // 订阅事件
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    GmcUnSubscribe(stmt, subName);
    ret = GmcSubscribe(stmt, &tmp_sub_info, testSubConn, sub_callback_with_old_ip4foward, user_data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(sub_info);

    srand(time(NULL));
    while (1) {
        int times = rand() % 10;
        sleep(times);
        user_data->subIndex = 0;
        user_data->insertNum = 0;
        user_data->deleteNum = 0;
        user_data->updateNum = 0;
        user_data->replaceNum = 0;
    }
    ret = GmcUnSubscribe(stmt, subName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(user_data);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void prepare_write_ip4foward_sync(int start_pk, int end_pk)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    write_ip4foward_data_sync(conn, stmt, start_pk, end_pk, 0);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void condition_filter_update_ip4foward(GmcStmtT *stmt, char *condition)
{
    int ret = 0;
    int32_t update_value = 100;
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_UPDATE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    update_set_test(stmt, update_value);
    ret = GmcSetFilter(stmt, condition);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void condition_filter_delete_ip4foward(GmcStmtT *stmt, char *condition)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_DELETE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, condition);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void condition_filter_scan_ip4foward(GmcStmtT *stmt, char *condition)
{
    int ret = 0;
    bool isFinish = false;
    bool isNull = false;

    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, condition);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetOutputFormat(stmt, NULL);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret == GMERR_OK) {
        // check
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_ASSERT_INT32(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint32_t f3_query = 0;
            ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &f3_query, sizeof(uint32_t), &isNull);
            TEST_ASSERT_INT32(GMERR_OK, ret);
            if (f3_query >= 30000 && f3_query < 30200) {
                TEST_ASSERT_INT32(GMERR_OK, 0);
            } else {
                printf("F3:%u\n", f3_query);
                TEST_ASSERT_INT32(1, 0);
            }
        }
    } else {
        TEST_ASSERT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
    }
}

void *condition_filter_ip4foward_sync(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    int32_t start_pk = 30000;
    char *cond = (char *)"30000<=testforward.F3 and testforward.F3 < 30200";

    srand((uint32_t)time(0));
    int loop = rand() % 200;
    int32_t end_pk = start_pk + loop;
    write_ip4foward_data_sync(conn, stmt, start_pk, end_pk, 0);
    // 过滤查询
    condition_filter_scan_ip4foward(stmt, cond);
    // 过滤更新
    condition_filter_update_ip4foward(stmt, cond);
    // 过滤删除
    condition_filter_delete_ip4foward(stmt, cond);
    sleep(loop);
    return NULL;
}

void local_equi_index_set(GmcStmtT *stmt, void *fixed_value, int32_t index_value)
{
    int ret = 0;
    uint32_t keyValue = index_value;
    ret = GmcSetIndexKeyName(stmt, g_ip4forwardLocalKey);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, fixed_value, IP4FORWARD_F7_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}
void local_equi_ip4foward_scan(GmcStmtT *stmt, int32_t start_num, int32_t end_num)
{

    int ret = 0;
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    for (int32_t i = start_num; i < end_num; i++) {

        local_equi_index_set(stmt, (char *)"fixed", i);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_ASSERT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                read_ip4foward_by_get(stmt);
                ret = GmcFetch(stmt, &isFinish);
                TEST_ASSERT_INT32(GMERR_OK, ret);
            }
        } else {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
}

void local_equi_ip4foward_update_sync(GmcStmtT *stmt, int32_t start_num, int32_t end_num)
{

    int ret = 0;
    int32_t update_value = 300;
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_UPDATE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    for (int32_t i = start_num; i < end_num; i++) {
        local_equi_index_set(stmt, (char *)"fixed", i);
        update_set_test(stmt, update_value);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
}

void local_equi_ip4foward_delete_sync(GmcStmtT *stmt, int32_t start_num, int32_t end_num)
{

    int ret = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_DELETE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    for (int32_t i = start_num; i < end_num; i++) {
        local_equi_index_set(stmt, (char *)"fixed", i);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
}

void local_equi_ip4foward_delete_bath(GmcConnT *conn, GmcStmtT *stmt, int32_t start_num, int32_t end_num)
{

    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int32_t i = start_num; i < end_num; i++) {
        local_equi_index_set(stmt, (char *)"fixed", i);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
    }
    if (ret == GMERR_OK) {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
}

void local_equi_ip4foward_update_bath(GmcConnT *conn, GmcStmtT *stmt, int32_t start_num, int32_t end_num)
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    int32_t update_value = 100;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    for (int32_t i = start_num; i < end_num; i++) {
        local_equi_index_set(stmt, (char *)"fixed", i);
        update_set_test(stmt, update_value);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
    }
    if (ret == GMERR_OK) {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
}

void local_equi_ip4foward_sync(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    srand((uint32_t)time(0));
    int loop = rand() % countN;

    int32_t end = loop + BATCH_NUM;
    int32_t bath_start = end;
    int32_t bath_end = end + BATCH_NUM;

    // 查询
    local_equi_ip4foward_scan(stmt, loop, end);
    // 更新
    local_equi_ip4foward_update_sync(stmt, loop, end);
    sleep(ST);
    // 删除
    local_equi_ip4foward_delete_sync(stmt, loop, end);
    sleep(ST);
    // 批量更新
    local_equi_ip4foward_update_bath(conn, stmt, bath_start, bath_end);
    // 批量删除
    local_equi_ip4foward_delete_bath(conn, stmt, bath_start, bath_end);
    sleep(ST);
}

static const char *g_resPoolName = "resource_pool_test";
static const char *g_resPool =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 1,
        "start_id" : 0,
        "capacity" : 65535,
        "order" : 0,
        "alloc_type" : 0
    })";

#define RES_FIXED_PROPERTY_SIZE 7
#define RES_BYTES_PROPERTY_SIZE 7
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000

void ResGmcSetNodePropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void ResGmcNodeSetPropertyByName_Root(GmcNodeT *node, int i, bool bool_value, char *f14_value, void *fixed_value)
{
    int ret = 0;

    uint64_t f1_value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t dest_ip_addr = 0;
    uint8_t mask_len = 0;
    if (i <= MAX_MASK_LEN_16) {
        dest_ip_addr = ((i + 2) << 16);
        mask_len = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        dest_ip_addr = ((i + 2) << 8);
        mask_len = ((24) & 0xff);
    } else {
        dest_ip_addr = ((i + 2));
        mask_len = ((32) & 0xff);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(0xFFFF, &tmpResIdx);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetCountResource(1, &tmpResIdx);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(0xFFFFFFFF, &tmpResIdx);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "res", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, fixed_value, RES_BYTES_PROPERTY_SIZE);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, fixed_value, RES_FIXED_PROPERTY_SIZE);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t f17_value = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_UINT32, &f17_value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    uint32_t f18_value = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_UINT32, &f18_value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void ResGmcNodeUpdate_Root(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    uint64_t f1_value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void ResGmcNodeSetPropertyByName_Node(GmcNodeT *node, int i, char *f14_value, void *fixed_value)
{
    int ret = 0;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    int16_t f4_value = (i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint16_t f5_value = (i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    int8_t f6_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint8_t f7_value = (i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, fixed_value, RES_FIXED_PROPERTY_SIZE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

const char *V2_member_key_name = (const char *)"member_keyV2_unique";
const char *V3_member_key_name = (const char *)"member_keyV3";
const char *V5_member_key_name = (const char *)"member_keyV5_unique";
char *g_res_label = (char *)"multilayer_test";
char *g_res_pk = (char *)"primary_key";
char *g_res_lpm4 = (char *)"lpm4_index";
const char *g_namespaceUserName = (const char *)"abc";
const char *g_nameSpace = (const char *)"user001";
void test_create_resource_pool_and_bind(GmcStmtT *stmt, const char *pool, const char *pool_name)
{
    (void)GmcDestroyResPool(stmt, pool_name);

    // 创建资源池
    int ret = GmcCreateResPool(stmt, pool);
    if (GMERR_RESOURCE_POOL_ALREADY_EXIST != ret) {
        TEST_ASSERT_INT32(GMERR_OK, ret);
    }
    ret = GmcBindResPoolToLabel(stmt, pool_name, g_res_label);
    if (ret != GMERR_RESOURCE_POOL_ALREADY_BOUND) {
        TEST_ASSERT_INT32(GMERR_OK, ret);
    }
}

void test_unbind_resource_pool_and_destroy(GmcStmtT *stmt, char *label_name, const char *pool_name)
{
    int ret = GmcUseNamespace(stmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 解绑资源池到表，第一要归还所有资源池，第二要解绑表，第三要解绑所有资源池
    ret = GmcUnbindResPoolFromLabel(stmt, label_name);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    // 销毁资源池
    ret = GmcDestroyResPool(stmt, pool_name);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

// 获取根节点以及非根节点下非嵌套的vector节点
void TestResGetRootAndChild_V(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **V2)
{
    GmcNodeT *Root = NULL, *node[2] = {NULL};

    // 获取根节点与子节点
    int ret = GmcGetRootNode(stmt, &Root);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "V2", &node[0]);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    *root = Root;
    *V2 = node[0];
}
int ResVertexAffactRows(GmcStmtT *stmt, int32_t expect_value)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return expect_value == affectRows ? GMERR_OK : 1;
}

int long_stability_excute_time(GmcStmtT *stmt, int32_t returnV)
{
    int32_t circleTime = 0;
    int32_t ret = returnV;
    while (ret == GMERR_LOCK_NOT_AVAILABLE) {
        circleTime++;
        usleep(10);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            break;
        }
        if (circleTime == GMERR_LOCK_NOT_AVAILABLE_CIRCLE_TIME) {
            break;
        }
    }
    return ret;
}

int ResReplaceOrInsertVertex(GmcStmtT *stmt, const char *labelName, bool bool_value, char *f14_value, char *fixed_value,
    int start_num, int end_num, int vector_num, int write_flag = 0)
{
    int ret = 0;
    int flagW = 0;
    int circleTime = 0;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;
    if (write_flag == 0) {
        flagW = GMC_OPERATION_REPLACE;
    } else {
        flagW = GMC_OPERATION_INSERT;
    }
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        circleTime = 0;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, (GmcOperationTypeE)flagW);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChild_V(stmt, &rootNode, &V2Node);
        ResGmcSetNodePropertyByName_PK(rootNode, i);
        ResGmcNodeSetPropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);

        // 插入vector节点 V2和V2.V3和V2.V3.V5
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(V2Node, &V2Node);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ResGmcNodeSetPropertyByName_Node(V2Node, j, f14_value, fixed_value);
            // 获取V3节点
            ret = GmcNodeGetChild(V2Node, "V3", &V3Node);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (uint32_t k = 0; k < vector_num; k++) {
                ret = GmcNodeAppendElement(V3Node, &V3Node);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ResGmcNodeSetPropertyByName_Node(V3Node, k, f14_value, fixed_value);
                // 获取V5节点
                ret = GmcNodeGetChild(V3Node, "V5", &V5Node);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                for (uint32_t m = 0; m < vector_num; m++) {
                    ret = GmcNodeAppendElement(V5Node, &V5Node);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ResGmcNodeSetPropertyByName_Node(V5Node, m, f14_value, fixed_value);
                }
            }
        }
        ret = GmcExecute(stmt);
        while (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = long_stability_excute_time(stmt, ret);
        }
        if (ret != GMERR_OK && ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_PRIMARY_KEY_VIOLATION &&
            ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_FEATURE_NOT_SUPPORTED &&
            ret != GMERR_UNIQUE_VIOLATION && ret != GMERR_RESOURCE_POOL_ERROR) {
            printf("[ResReplaceOrInsertVertex]i:%d line:%d\n", i, __LINE__);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                // 混沌适配
                return ret;
            }
        }
        // GmcResetStmt(stmt);
    }
    return ret;
}

void ResSetPkIndexUpdate(GmcStmtT *stmt, int64_t pkValue)
{
    int ret = 0;
    int64_t keyValue = pkValue;
    ret = GmcSetIndexKeyName(stmt, g_res_pk);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void ResUpdateVertex(
    GmcStmtT *stmt, const char *labelName, bool bool_value, char *f14_value, int start_num, int end_num)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;
    int circleTime = 0;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        circleTime = 0;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ResSetPkIndexUpdate(stmt, i);
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChild_V(stmt, &rootNode, &V2Node);
        ResGmcNodeUpdate_Root(rootNode, (i + 1), bool_value, f14_value);
        ret = GmcExecute(stmt);
        ret = long_stability_excute_time(stmt, ret);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_TRANSACTION_ROLLBACK) {
            TEST_ASSERT_INT32(GMERR_OK, ret);
        }
        // GmcResetStmt(stmt);
    }
}

void ResDeleteVertex(GmcStmtT *stmt, const char *labelName, int start_num, int end_num)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ResSetPkIndexUpdate(stmt, i);
        ret = GmcExecute(stmt);
        ret = long_stability_excute_time(stmt, ret);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_TRANSACTION_ROLLBACK) {
            TEST_ASSERT_INT32(GMERR_OK, ret);
        }
        // GmcResetStmt(stmt);
    }
}

int ResBatchInsert(GmcConnT *conn, GmcStmtT *stmt, int start_num, int end_num, int vector_num)
{
    int ret = 0;
    int retN = 0;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;
    uint32_t totalNum = 0, successNum = 0;
    char *f14_value = (char *)"1vertex";
    char *fixed_value = (char *)"test111";
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_res_label, GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChild_V(stmt, &rootNode, &V2Node);
        ResGmcSetNodePropertyByName_PK(rootNode, i);
        ResGmcNodeSetPropertyByName_Root(rootNode, i, false, f14_value, fixed_value);

        // 插入vector节点 V2和V2.V3和V2.V3.V5
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(V2Node, &V2Node);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ResGmcNodeSetPropertyByName_Node(V2Node, j, f14_value, fixed_value);
            // 获取V3节点
            ret = GmcNodeGetChild(V2Node, "V3", &V3Node);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (uint32_t k = 0; k < vector_num; k++) {
                ret = GmcNodeAppendElement(V3Node, &V3Node);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ResGmcNodeSetPropertyByName_Node(V3Node, k, f14_value, fixed_value);
                // 获取V5节点
                ret = GmcNodeGetChild(V3Node, "V5", &V5Node);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                for (uint32_t m = 0; m < vector_num; m++) {
                    ret = GmcNodeAppendElement(V5Node, &V5Node);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ResGmcNodeSetPropertyByName_Node(V5Node, m, f14_value, fixed_value);
                }
            }
        }
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK && ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_LOCK_NOT_AVAILABLE &&
        ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_UNIQUE_VIOLATION &&
        ret != GMERR_FEATURE_NOT_SUPPORTED) {
        if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (ret == GMERR_OK) {
        retN = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, retN);
    }
    GmcBatchDestroy(batch);
    // GmcResetStmt(stmt);
    return ret;
}

int ResBatchDelete(GmcConnT *conn, GmcStmtT *stmt, int start_num, int end_num)
{
    int ret = 0;
    int retN = 0;
    uint32_t totalNum = 0, successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_res_label, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ResSetPkIndexUpdate(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK && ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_OUT_OF_MEMORY &&
        ret != GMERR_LOCK_NOT_AVAILABLE) {
        if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    if (ret == GMERR_OK) {
        retN = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, retN);
    }
    GmcBatchDestroy(batch);
    // GmcResetStmt(stmt);
    return ret;
}

int32_t res_label_write_thread(GmcConnT *syncConn, GmcStmtT *syncStmt, uint32_t startC)
{
    int32_t ret = 0;
    int32_t vector_num = 3;
    uint32_t startNum = 0;
    uint32_t endNum = RES_WRITE_COUNT;
    int32_t customer_batch_num = RES_WRITE_COUNT / BATCH_MAX_COUNT;
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 插入
    ret = ResReplaceOrInsertVertex(syncStmt, g_res_label, false, (char *)"vertex", (char *)"test111", startC,
        (startC + RES_WRITE_COUNT), vector_num);
    if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        return ret;
    }
    sleep(ST);
    // 更新
    ResUpdateVertex(syncStmt, g_res_label, true, (char *)"111111", startC, (startC + RES_WRITE_COUNT));
    sleep(ST);
    // delete
    ResDeleteVertex(syncStmt, g_res_label, startC, (startC + RES_WRITE_COUNT));
    sleep(ST);
    // 批量写,数据量为BATCH_MAX_COUNT
    for (int i = 0; i < customer_batch_num; i++) {
        startNum = startC + i * BATCH_MAX_COUNT;
        endNum = startNum + BATCH_MAX_COUNT;
        ResBatchInsert(syncConn, syncStmt, startNum, endNum, vector_num);
    }
    if ((RES_WRITE_COUNT + startC - endNum) > 0) {
        ResBatchInsert(syncConn, syncStmt, endNum, (RES_WRITE_COUNT + startC), vector_num);
    }
    // 批量删
    for (int i = 0; i < customer_batch_num; i++) {
        startNum = startC + i * BATCH_MAX_COUNT;
        endNum = startNum + BATCH_MAX_COUNT;
        ResBatchDelete(syncConn, syncStmt, startNum, endNum);
    }
    if ((RES_WRITE_COUNT + startC - endNum) > 0) {
        ResBatchDelete(syncConn, syncStmt, endNum, (RES_WRITE_COUNT + startC));
    }
    sleep(ST);
    return GMERR_OK;
}

// 对账老化
void *aging_thread_res_test(void *arg)
{
    int ret = 0;
    bool isFinish = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;

    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    while (1) {
        srand((uint32_t)time(0));
        int loop = rand() % 200;
        sleep(loop);
        longStabilityAgeCheck(stmt, g_res_label, 0xff, isAbnormal);
    }
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void resReConnServer(GmcConnT **conn, GmcStmtT **stmt, const char *nameSpaceT = NULL)
{
    int ret = 0;
    GmcConnT *testConn = NULL;
    GmcStmtT *testStmt = NULL;
    ret = testGmcConnect(&testConn, &testStmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (nameSpaceT) {
        ret = GmcUseNamespace(testStmt, nameSpaceT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    *conn = testConn;
    *stmt = testStmt;
}

void res_label_trans_write_thread(void *args)
{
    int32_t ret = 0;
    uint32_t startC = *(uint32_t *)args;
    GmcConnT *batchConn = NULL;
    GmcStmtT *batchStmt = NULL;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_PESSIMISITIC_TRX;  // 悲观事务
    config.readOnly = false;
    int32_t vectorNum = 3;
    uint32_t startNum = 0;
    uint32_t endNum = RES_TRANS_WRITE_COUNT;
    int32_t customer_batch_num = RES_TRANS_WRITE_COUNT / BATCH_MAX_COUNT;
    ret = testGmcConnect(&syncConn, &syncStmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcConnect(&batchConn, &batchStmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUseNamespace(batchStmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 插入
    do {
        sleep(ST);
        ret = GmcTransStart(syncConn, &config);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = ResReplaceOrInsertVertex(syncStmt, g_res_label, false, (char *)"vertex", (char *)"test111", startC,
            (startC + RES_TRANS_WRITE_COUNT), vectorNum);
        if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
            // 混沌适配
            testGmcDisconnect(syncConn, syncStmt);
            syncConn = NULL;
            syncStmt = NULL;
            ret = testGmcConnect(&syncConn, &syncStmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcUseNamespace(syncStmt, g_nameSpace);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcTransCommit(syncConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(syncConn);
            TEST_EXPECT_INT32(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 更新
    do {
        sleep(ST);
        ret = GmcTransStart(syncConn, &config);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ResUpdateVertex(syncStmt, g_res_label, true, (char *)"111111", startC, (startC + RES_TRANS_WRITE_COUNT));
        ret = GmcTransCommit(syncConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(syncConn);
            TEST_EXPECT_INT32(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // delete
    do {
        sleep(ST);
        ret = GmcTransStart(syncConn, &config);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ResDeleteVertex(syncStmt, g_res_label, startC, (startC + RES_TRANS_WRITE_COUNT));
        ret = GmcTransCommit(syncConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(syncConn);
            TEST_EXPECT_INT32(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 批量写,数据量为BATCH_MAX_COUNT
    do {
        sleep(ST);
        ret = GmcTransStart(batchConn, &config);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        for (int i = 0; i < customer_batch_num; i++) {
            startNum = startC + i * BATCH_MAX_COUNT;
            endNum = startNum + BATCH_MAX_COUNT;
            ret = ResBatchInsert(batchConn, batchStmt, startNum, endNum, vectorNum);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                break;
            }
        }
        if ((RES_TRANS_WRITE_COUNT + startC - endNum) > 0) {
            ret = ResBatchInsert(batchConn, batchStmt, endNum, (RES_TRANS_WRITE_COUNT + startC), vectorNum);
        }
        ret = GmcTransCommit(batchConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(batchConn);
            TEST_EXPECT_INT32(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
            testGmcDisconnect(batchConn, batchStmt);
            batchConn = NULL;
            batchStmt = NULL;
            resReConnServer(&batchConn, &batchStmt, g_nameSpace);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 批量删
    do {
        sleep(ST);
        ret = GmcTransStart(batchConn, &config);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        for (int i = 0; i < customer_batch_num; i++) {
            startNum = startC + i * BATCH_MAX_COUNT;
            endNum = startNum + BATCH_MAX_COUNT;
            ret = ResBatchDelete(batchConn, batchStmt, startNum, endNum);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                break;
            }
        }
        if ((RES_TRANS_WRITE_COUNT + startC - endNum) > 0) {
            ret = ResBatchDelete(batchConn, batchStmt, endNum, (RES_TRANS_WRITE_COUNT + startC));
        }
        ret = GmcTransCommit(batchConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(batchConn);
            TEST_EXPECT_INT32(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
            testGmcDisconnect(batchConn, batchStmt);
            batchConn = NULL;
            batchStmt = NULL;
            resReConnServer(&batchConn, &batchStmt, g_nameSpace);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(batchConn, batchStmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void res_set_IndexKeyValue_lpm4(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, uint32_t ipaddr, uint8_t mask_len)
{
    int ret = 0;
    uint32_t t_vrid = vrid;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &t_vrid, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    uint32_t t_vrfid = vrfid;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &t_vrfid, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    uint32_t t_ipaddr = ipaddr;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &t_ipaddr, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    uint8_t t_mask_len = mask_len;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &t_mask_len, sizeof(uint8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_res_lpm4);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void ResGmcGetNodePropertyByName_Root(GmcNodeT *node)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    int16_t f4_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    uint16_t f5_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    int8_t f6_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    bool f8_value = false;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    float f9_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    double f10_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    uint64_t f11_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    char f12_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    unsigned char f13_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    unsigned int propSize = 0;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", string_value, propSize, &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);

    char bytes_value_query[10] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", bytes_value_query, RES_FIXED_PROPERTY_SIZE, &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    char fixed_value_query[10] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", fixed_value_query, RES_FIXED_PROPERTY_SIZE, &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void res_lpm4_scan(GmcStmtT *stmt, int start_num, int end_num)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;
    bool isFinish = false;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_res_label, GMC_OPERATION_SCAN);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        uint32_t dest_ip_addr = 0;
        uint8_t mask_len = 0;
        if (i <= MAX_MASK_LEN_16) {
            dest_ip_addr = ((i + 2) << 16);
            mask_len = ((16) & 0xff);
        } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
            dest_ip_addr = ((i + 2) << 8);
            mask_len = ((24) & 0xff);
        } else {
            dest_ip_addr = ((i + 2));
            mask_len = ((32) & 0xff);
        }
        res_set_IndexKeyValue_lpm4(stmt, 0, 0, dest_ip_addr, mask_len);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_ASSERT_INT32(GMERR_OK, ret);
            if (!isFinish) {
                // 获取根节点与非嵌套的vector节点和array节点
                TestResGetRootAndChild_V(stmt, &rootNode, &V2Node);
                ResGmcGetNodePropertyByName_Root(rootNode);
                if (ret == GMERR_OK) {
                    ret = GmcFreeNode(rootNode);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
        } else if (ret != GMERR_NO_DATA && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_ASSERT_INT32(GMERR_OK, ret);
        }
        // GmcResetStmt(stmt);
    }
}

// lpm4扫描线程
void res_lpm4_scan_thread(GmcConnT *syncConn, GmcStmtT *syncStmt)
{

    int32_t ret = 0;
    int32_t vector_num = 3;
    uint32_t startNum = 0;
    uint32_t endNum = 500;
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    srand((uint32_t)time(0));
    int index = rand() % (RES_WRITE_COUNT + RES_TRANS_WRITE_COUNT);
    res_lpm4_scan(syncStmt, index, (index + endNum));
    sleep(ST);
}

void res_sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        } else if (eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

// 订阅推送
void *res_sub_thread(void *args)
{
    int ret = 0;
    char *sub_info = NULL;
    int chanRingLen = 256;
    char subConnName[128] = "res_sub_conn";
    GmcStmtT *stmt_sub = NULL;
    GmcConnT *testSubConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char subName[128] = "res_sub";
    SnUserDataT *user_data = NULL;

    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅连接
    ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    readJanssonFile("schemaFile/res_sub_full.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    // 全量订阅事件
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;

    ret = GmcUseNamespace(stmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    (void)GmcUnSubscribe(stmt, subName);
    ret = GmcSubscribe(stmt, &tmp_sub_info, testSubConn, res_sn_callback_not_cmp, user_data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(sub_info);
    while (1) {
        sleep(1);
    }
    ret = GmcUnSubscribe(stmt, subName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(user_data);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void ResMemberKeySetKeyValue_V2_V3(GmcIndexKeyT *key, int32_t i, void *fixed_value)
{
    int64_t key1_value = i;
    uint64_t key2_value = i;
    int32_t key3_value = i;
    int16_t key4_value = i;
    uint16_t key5_value = i % 32768;
    int8_t key6_value = i % 128;
    uint64_t key8_value = i;
    //"F0","F1","F2","F4","F5","F6","F16","F11"
    int ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_INT64, &key1_value, sizeof(int64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_UINT64, &key2_value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 2, GMC_DATATYPE_INT32, &key3_value, sizeof(int32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 3, GMC_DATATYPE_INT16, &key4_value, sizeof(int16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 4, GMC_DATATYPE_UINT16, &key5_value, sizeof(uint16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 5, GMC_DATATYPE_INT8, &key6_value, sizeof(int8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 6, GMC_DATATYPE_FIXED, fixed_value, RES_FIXED_PROPERTY_SIZE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 7, GMC_DATATYPE_TIME, &key8_value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}
void ResMemberKeySetKeyValue_V2(GmcIndexKeyT *key, uint64_t v2_key1, int32_t v2_key2, uint32_t v2_key3)
{
    int ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT64, &v2_key1, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_INT32, &v2_key2, sizeof(int32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 2, GMC_DATATYPE_UINT32, &v2_key3, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}
void ResMemberKeySetKeyValue_V5(GmcIndexKeyT *key, int64_t key1, int32_t key2, int16_t key3)
{
    int64_t v5_key1 = key1;
    int32_t v5_key2 = key2;
    int16_t v5_key3 = key3 % 32768;
    int ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_INT64, &v5_key1, sizeof(int64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_INT32, &v5_key2, sizeof(int32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 2, GMC_DATATYPE_INT16, &v5_key3, sizeof(uint16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void ResMemberkeyUpdateRemoveAppend(GmcStmtT *stmt, int64_t pkvalue)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = pkvalue;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V2_V3Node = NULL, *V2_V3_V5Node = NULL;

    ret = testGmcPrepareStmtByLabelName(stmt, g_res_label, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ResSetPkIndexUpdate(stmt, primary_index_value);
    // 获取根节点与非嵌套的vector节点和array节点
    TestResGetRootAndChild_V(stmt, &rootNode, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL, *V5_Value1 = NULL;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ResMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    // update 1次
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    ResGmcNodeSetPropertyByName_Node(V2_Value1, update_value, f14_value, fixed_value);
    // append 13次
    for (uint32_t m = 3; m < 16; m++) {
        ret = GmcNodeAppendElement(V2Node, &V2Node);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ResGmcNodeSetPropertyByName_Node(V2Node, m, f14_value, fixed_value);
    }
    // remove 2次
    for (int i = 1; i < vector_num; i++) {
        v2_key1 = i;
        v2_key2 = i;
        v2_key3 = i;
        ResMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
        ret = GmcNodeRemoveElementByKey(V2Node, V2key);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        ResMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // append 13次
    for (uint32_t m = 3; m < 16; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ResGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }
    // update 1次
    ResMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ResGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);

    // 再对V3下面的V5进行操作
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3Key2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ResMemberKeySetKeyValue_V2_V3(V3Key2, 2, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3Key2, &V3_Value1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    //"F0","F2","F4"
    // remove 2次
    ResMemberKeySetKeyValue_V5(V5key, 0, 0, 0);
    ret = GmcNodeRemoveElementByKey(V2_V3_V5Node, V5key);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ResMemberKeySetKeyValue_V5(V5key, 1, 1, 1);
    ret = GmcNodeRemoveElementByKey(V2_V3_V5Node, V5key);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // append 13次
    for (uint32_t m = 3; m < 16; m++) {
        ret = GmcNodeAppendElement(V2_V3_V5Node, &V2_V3_V5Node);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ResGmcNodeSetPropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
    }
    // update 1次
    ResMemberKeySetKeyValue_V5(V5key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(V2_V3_V5Node, V5key, &V5_Value1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ResGmcNodeSetPropertyByName_Node(V5_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(stmt);
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        ret = long_stability_excute_time(stmt, ret);
    }
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_UNIQUE_VIOLATION &&
        ret != GMERR_DATA_EXCEPTION) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = GmcNodeFreeKey(V2key);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3Key2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void TestGmcGetNodePropertyByName_Node(GmcNodeT *node, int i, char *f14_value, void *fixed_value)
{
    int ret = 0;
    bool isNull = 0;

    int64_t f0_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32(i, f0_value);

    uint64_t f1_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32(i, f1_value);

    int32_t f2_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32(i, f2_value);

    uint32_t f3_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32(i, f3_value);

    int16_t f4_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32((i) % 32768, f4_value);

    uint16_t f5_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32((i) % 65536, f5_value);

    int8_t f6_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32((i) % 128, f6_value);

    uint8_t f7_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32((i) % 256, f7_value);

    uint64_t f11_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32(i, f11_value);

    unsigned int propSize = 0;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", string_value, propSize, &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    TEST_ASSERT_INT32((unsigned int)0, isNull);
    TEST_ASSERT_INT32(strcmp(string_value, f14_value), 0);

    char fixed_value_query[10] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", fixed_value_query, RES_FIXED_PROPERTY_SIZE, &isNull);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = memcmp(fixed_value, fixed_value_query, RES_FIXED_PROPERTY_SIZE);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void ResMemberKeyCheckData(GmcStmtT *stmt, int64_t pkvalue)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    bool isFinish = true;
    int64_t primary_index_value = pkvalue;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V2_V3Node = NULL, *V2_V3_V5Node = NULL;
    uint32_t update_value = 100;
    // 获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    ret = testGmcPrepareStmtByLabelName(stmt, g_res_label, GMC_OPERATION_SCAN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ResSetPkIndexUpdate(stmt, primary_index_value);
    ret = GmcExecute(stmt);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    int res = GmcFetch(stmt, &isFinish);
    TEST_ASSERT_INT32(GMERR_OK, res);
    if (isFinish) {
        return;
    }
    TestResGetRootAndChild_V(stmt, &rootNode, &V2Node);
    ResGmcGetNodePropertyByName_Root(rootNode);

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    uint32_t count = 0;
    ret = GmcNodeGetElementCount(V2Node, &count);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(V2_V3Node, &count);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    GmcNodeT *V3NodeTest = NULL;

    // 获取V5节点
    ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    if (res == GMERR_OK) {
        ret = GmcFreeNode(rootNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

// member key
int32_t res_memberkey_inc_thread(GmcConnT *syncConn, GmcStmtT *syncStmt, uint32_t startNum)
{

    int32_t ret = 0;
    int32_t vector_num = 3;
    uint32_t endNum = 50;
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = ResReplaceOrInsertVertex(syncStmt, g_res_label, false, (char *)"vertexTest", (char *)"vertex1", startNum,
        (startNum + endNum), vector_num, 1);
    if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        return ret;
    }
    sleep(ST);
    // member key增量更新
    ResMemberkeyUpdateRemoveAppend(syncStmt, startNum);
    sleep(ST);
    ResMemberKeyCheckData(syncStmt, startNum);
    sleep(ST);
    // delete
    ResDeleteVertex(syncStmt, g_res_label, startNum, (startNum + endNum));
    return GMERR_OK;
}

// 全表扫描线程
void res_whole_label_scan(GmcConnT *conn, GmcStmtT *syncStmt)
{
    int ret = 0;
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    srand(time(NULL));
    int times = rand() % 200;
    if (times < 50) {
        times = times + 50;
    }
    // 全表扫描 --同步
    long_stability_whold_label_scan(syncStmt, g_res_label);
    sleep(times);
}

void superfield_ip4foward_sync(GmcConnT *conn, GmcStmtT *stmt, uint32_t countN)
{
    int ret = 0;
    char *f1_value = (char *)"fixed";
    srand((uint32_t)time(0));
    int index = rand() % countN;
    for (int loop = index; loop < (index + OP_NUM / 10); loop++) {
        // 使用superfiled写
        ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_REPLACE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // set superfiled by name
        char *sp_1 = (char *)malloc(18);
        if (sp_1 == NULL) {
            return;
        }
        strncpy(sp_1, (char *)"fixed", 5);
        *(uint32_t *)(sp_1 + 5) = loop % 5000;
        *(uint32_t *)(sp_1 + 9) = loop;
        *(uint32_t *)(sp_1 + 13) = loop;
        *(uint8_t *)(sp_1 + 17) = loop % 256;
        ret = GmcSetSuperfieldByName(stmt, "SPF2", sp_1, 18);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBitMapT bitMap = {0, 7, NULL};
        uint8_t f6_bits[IP4FORWARD_BITMAP_LEN / 8] = {0xff};
        bitMap.bits = f6_bits;
        ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_BITMAP, &bitMap, sizeof(bitMap));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        char *sp_0 = (char *)malloc(33);
        if (sp_0 == NULL) {
            free(sp_1);
            return;
        }
        strncpy(sp_0, (char *)"fixed", 5);
        *(uint32_t *)(sp_0 + 5) = loop;
        *(uint32_t *)(sp_0 + 9) = loop % 5000;
        *(uint32_t *)(sp_0 + 13) = loop;
        *(uint32_t *)(sp_0 + 17) = loop;
        *(uint32_t *)(sp_0 + 21) = loop;
        *(uint32_t *)(sp_0 + 25) = loop;
        *(uint32_t *)(sp_0 + 29) = loop;
        ret = GmcSetSuperfieldByName(stmt, "SPF1", sp_0, 33);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        free(sp_0);
        free(sp_1);
    }
    bool isFinish = false;
    // 使用superfiled读
    ret = testGmcPrepareStmtByLabelName(stmt, g_ip4forwardName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = index; loop < (index + OP_NUM / 10); loop++) {
        set_pk_index(stmt, loop, f1_value);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // get superfiled by name
        char *sp_1_get = (char *)malloc(18);
        if (sp_1_get == NULL) {
            return;
        }
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (!isFinish) {
                ret = GmcGetSuperfieldById(stmt, 1, sp_1_get, 18);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
        free(sp_1_get);
    }
    sleep(ST);
}
void ResourceAsyncReplaceCb(
    void *userData, uint32_t affectedRows, GmcResourceInfoT *resInfo, int32_t status, const char *errMsg)
{
    int ret = 0;
    for (uint32_t i = 0; i < resInfo->resIdNum; i++) {
        uint16_t poolId = 0;
        uint16_t count = 0;
        uint32_t startIndex = 0;
        ret = GmcGetPoolIdResource(resInfo->resIdBuf[i], &poolId);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetCountResource(resInfo->resIdBuf[i], &count);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resInfo->resIdBuf[i], &startIndex);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    // 原有的回调
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->affectRows = affectedRows;
        user_data->historyRecvNum++;
        if (user_data->lastError != NULL) {
            ret = strcmp(user_data->lastError, errMsg);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        user_data->recvNum++;
    }
}
void ResAsyncReplaceOrInsertVertex(GmcStmtT *stmt, const char *labelName, bool bool_value, char *f14_value,
    char *fixed_value, int start_num, int end_num, int vector_num, int write_flag = 0)
{
    int ret = 0;
    int flagW = 0;
    uint64_t resId;
    uint32_t resCnt = 1;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;
    if (write_flag == 0) {
        flagW = GMC_OPERATION_REPLACE_WITH_RESOURCE;
    } else {
        flagW = GMC_OPERATION_INSERT_WITH_RESOURCE;
    }
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, (GmcOperationTypeE)flagW);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChild_V(stmt, &rootNode, &V2Node);
        ResGmcSetNodePropertyByName_PK(rootNode, i);
        ResGmcNodeSetPropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);

        // 插入vector节点 V2和V2.V3和V2.V3.V5
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(V2Node, &V2Node);
            TEST_ASSERT_INT32(GMERR_OK, ret);
            ResGmcNodeSetPropertyByName_Node(V2Node, j, f14_value, fixed_value);
            // 获取V3节点
            ret = GmcNodeGetChild(V2Node, "V3", &V3Node);
            TEST_ASSERT_INT32(GMERR_OK, ret);
            for (uint32_t k = 0; k < vector_num; k++) {
                ret = GmcNodeAppendElement(V3Node, &V3Node);
                TEST_ASSERT_INT32(GMERR_OK, ret);
                ResGmcNodeSetPropertyByName_Node(V3Node, k, f14_value, fixed_value);
                // 获取V5节点
                ret = GmcNodeGetChild(V3Node, "V5", &V5Node);
                TEST_ASSERT_INT32(GMERR_OK, ret);
                for (uint32_t m = 0; m < vector_num; m++) {
                    ret = GmcNodeAppendElement(V5Node, &V5Node);
                    TEST_ASSERT_INT32(GMERR_OK, ret);
                    ResGmcNodeSetPropertyByName_Node(V5Node, m, f14_value, fixed_value);
                }
            }
        }
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceWithResourceCb = ResourceAsyncReplaceCb;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &replaceRequestCtx);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        if (data.status != GMERR_CONFIGURATION_LIMIT_EXCEEDED && data.status != GMERR_LOCK_NOT_AVAILABLE &&
            data.status != GMERR_PRIMARY_KEY_VIOLATION && data.status != GMERR_UNIQUE_VIOLATION &&
            data.status != GMERR_FEATURE_NOT_SUPPORTED && data.status != GMERR_RESOURCE_POOL_ERROR) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
}

void ResAsyncUpdateVertex(
    GmcStmtT *stmt, const char *labelName, bool bool_value, char *f14_value, int start_num, int end_num)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ResSetPkIndexUpdate(stmt, i);
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChild_V(stmt, &rootNode, &V2Node);
        ResGmcNodeUpdate_Root(rootNode, (i + 1), bool_value, f14_value);
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (data.status != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
}

void ResAsyncDeleteVertex(GmcStmtT *stmt, const char *labelName, int start_num, int end_num)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ResSetPkIndexUpdate(stmt, i);
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 并发删除数据，可能会有部分删除失败
        if (data.status != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
}

// 资源表异步DML操作，写--主键更新--主键删除操作
void res_label_async_operation_thread(GmcConnT *asyncConn, GmcStmtT *asyncStmt, void *args)
{
    int32_t ret = 0;
    uint32_t startC = *(uint32_t *)args;
    int32_t vector_num = 3;
    AsyncUserDataT tdata = {0};
    ret = GmcUseNamespaceAsync(asyncStmt, g_nameSpace, use_namespace_callback, &tdata);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tdata);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, tdata.status);
    // 插入, 资源表异步insert、replace的GmcOperationTypeE和同步不一样
    ResAsyncReplaceOrInsertVertex(asyncStmt, g_res_label, false, (char *)"vertex", (char *)"test111", startC,
        (startC + RES_WRITE_COUNT), vector_num);
    sleep(ST);
    // 更新
    ResAsyncUpdateVertex(asyncStmt, g_res_label, true, (char *)"111111", startC, (startC + RES_WRITE_COUNT));
    sleep(ST);
    // 删除
    ResAsyncDeleteVertex(asyncStmt, g_res_label, startC, (startC + RES_WRITE_COUNT));
    sleep(ST);
}
void SetPropValueUInt32(GmcPropValueT &propValue, uint32_t *val)
{
    EXPECT_NE(val, nullptr);
    propValue.size = sizeof(uint32_t);
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = val;
}

void SetPropValueUInt8(GmcPropValueT &propValue, uint8_t *val)
{
    EXPECT_NE(val, nullptr);
    propValue.size = sizeof(uint8_t);
    propValue.type = GMC_DATATYPE_UINT8;
    propValue.value = val;
}

void SetPropValueFixed(GmcPropValueT &propValue, uint8_t *val, uint32_t len)
{
    EXPECT_NE(val, nullptr);
    propValue.size = len;
    propValue.type = GMC_DATATYPE_FIXED;
    propValue.value = val;
}
void SetValueRange(GmcRangeItemT *items, GmcPropValueT *leftVals, GmcPropValueT *rightVals, uint32_t arrLen)
{
    EXPECT_NE(items, nullptr);
    EXPECT_NE(leftVals, nullptr);
    EXPECT_NE(rightVals, nullptr);
    for (uint32_t i = 0; i < arrLen; ++i) {
        items[i].lValue = &(leftVals[i]);
        items[i].rValue = &(rightVals[i]);
    }
}
void SetFlagAndOrder(
    GmcRangeItemT *items, GmcCompareFlagE lFlag, GmcCompareFlagE rFlag, GmcOrderDirectionE order, uint32_t arrLen)
{
    EXPECT_NE(items, nullptr);
    for (uint32_t i = 0; i < arrLen; ++i) {
        items[i].lFlag = lFlag;
        items[i].rFlag = rFlag;
        items[i].order = order;
    }
}
void ResLpm4LocalScan(GmcConnT *conn, GmcStmtT *stmt, int startNum, int endNum)
{
    int ret = 0;
    GmcStmtT *stmtSetKey = NULL;
    GmcNodeT *rootNode = NULL, *V2Node = NULL, *V3Node = NULL, *V5Node = NULL;
    bool isFinish;
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        // 适配GmcSetKeyRange接口
        stmtSetKey = NULL;
        ret = GmcAllocStmt(conn, &stmtSetKey);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (stmtSetKey == NULL) {
            AW_FUN_Log(LOG_DEBUG, "[ResLpm4LocalScan]syncStmt alloc failed !!!");
            continue;
        }
        // 设置ip字段
        uint32_t destIpAddr = 0;
        uint8_t maskLen = 0;
        if (i <= MAX_MASK_LEN_16) {
            destIpAddr = ((i + 2) << 16);
            maskLen = ((16) & 0xff);
        } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
            destIpAddr = ((i + 2) << 8);
            maskLen = ((24) & 0xff);
        } else {
            destIpAddr = ((i + 2));
            maskLen = ((32) & 0xff);
        }
        // 设置索引列的范围值
        uint32_t lVrId = 0;
        uint32_t lVrfIndex = 0;
        uint8_t lMaskLen = maskLen;
        uint32_t rVrId = 0;
        uint32_t rVrfIndex = 1;
        uint8_t rMaskLen = maskLen + 8;

        // 设置lpm key 左值
        unsigned int arrLen = 4;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        if (leftKeyProps == NULL) {
            printf("malloc failed!\n");
            continue;
        }
        SetPropValueUInt32(leftKeyProps[0], &lVrId);
        SetPropValueUInt32(leftKeyProps[1], &lVrfIndex);
        SetPropValueUInt32(leftKeyProps[2], &destIpAddr);
        SetPropValueUInt8(leftKeyProps[3], &lMaskLen);

        // 设置lpm key 右值
        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        if (rightKeyProps == NULL) {
            printf("malloc failed!\n");
            free(leftKeyProps);
            continue;
        }
        SetPropValueUInt32(rightKeyProps[0], &rVrId);
        SetPropValueUInt32(rightKeyProps[1], &rVrfIndex);
        SetPropValueUInt32(rightKeyProps[2], &destIpAddr);
        SetPropValueUInt8(rightKeyProps[3], &rMaskLen);

        GmcRangeItemT items[arrLen];
        SetValueRange(items, leftKeyProps, rightKeyProps, arrLen);
        SetFlagAndOrder(items, GMC_COMPARE_RANGE_CLOSED, GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC, arrLen);
        // 执行扫描
        ret = testGmcPrepareStmtByLabelName(stmtSetKey, g_res_label, GMC_OPERATION_SCAN);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtSetKey, g_res_lpm4);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmtSetKey, items, arrLen);
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmtSetKey);
        isFinish = false;
        if (ret == GMERR_OK) {
            while (!isFinish) {
                ret = GmcFetch(stmtSetKey, &isFinish);
                TEST_ASSERT_INT32(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                // 获取根节点与非嵌套的vector节点和array节点
                TestResGetRootAndChild_V(stmtSetKey, &rootNode, &V2Node);
                ResGmcGetNodePropertyByName_Root(rootNode);
                if (ret == GMERR_OK) {
                    ret = GmcFreeNode(rootNode);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
        } else if (ret != GMERR_NO_DATA && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_ASSERT_INT32(GMERR_OK, ret);
        }
        if (stmtSetKey) {
            GmcFreeStmt(stmtSetKey);
        }
        free(leftKeyProps);
        free(rightKeyProps);
    }
}
// lpm4按序索引扫描线程
void ResLpm4LocalScanThread(GmcConnT *syncConn, GmcStmtT *syncStmt)
{
    int32_t ret = 0;
    int32_t vector_num = 3;
    uint32_t startNum = 0;
    uint32_t endNum = 500;

    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    srand((uint32_t)time(0));
    int index = rand() % (RES_WRITE_COUNT + RES_TRANS_WRITE_COUNT);
    ResLpm4LocalScan(syncConn, syncStmt, index, (index + endNum));
    sleep(ST);
}

#endif  //_LONG_STABILITY_H_
