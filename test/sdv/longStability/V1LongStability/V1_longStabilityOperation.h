/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: V1_longStabilityPthread.h
 * Description: V1长稳函数定义
 * Create:
 */

#ifndef V1_LONG_STABILITY_OPERATION_H_
#define V1_LONG_STABILITY_OPERATION_H_

#ifdef FEATURE_SIMPLEREL

#include "V1_longStability.h"
#include "V1_longStabilityMem.h"
#include "V1_longStabilityTbl.h"

// 索引字段和非索引字段的关系
#define FIELD_VAL_RELATION_1 1                  // 字段值的关系1
#define FIELD_VAL_RELATION_2 2                  // 字段值的关系2

// 大对象表
#define BIG_OBJ_TBL_REC_INIT 100                // 大对象表初始化记录数
#define BIG_OBJ_TBL_REC_MAX 1000                // 大对象表最大记录数
#define BIG_OBJ_TBL_RANDOM_OPT_REC_MAX 10       // 随机操作的最大数量

// dml + diff + 查询的表
#define DML_TBL_REC_INIT 1000                   // 表初始化的数据量
#define DML_TBL_REC_MAX 10000                   // 表最大的数据量
#define DML_TBL_RANDOM_OPT_REC_MAX 100          // 随机操作的最大数量

// 导入导出的表
#define IEP_FIX_TBL_CNT 3                       // 固定存在的表数量
#define IEP_FIX_TBL_REC_INIT 1000               // 固定表的初始化数据量
#define IEP_FIX_TBL_REC_MAX 10000               // 固定表的最大数据量
#define IEP_FIX_TBL_RANDOM_OPT_REC_MAX 100      // 随机操作的最大数量
#define IEP_RAN_TBL_CNT 10                      // 随机存在的表数量
#define IEP_RAN_TBL_CNT_MAX 30                  // 随机存在的最大表数量
#define IEP_RAN_TBL_REC_INIT 1                  // 随机表的初始化数据量
#define IEP_RAN_TBL_REC_MAX 1                   // 随机表的最大数据量
#define IEP_ALL_TBL_CNT_MAX (IEP_FIX_TBL_CNT + IEP_RAN_TBL_CNT_MAX + 1) // 所有表最大数量

// 多线程操作同一张表
#define PTH_SAME_TBL_REC_INIT 1000              // 表初始化的数据量
#define PTH_SAME_TBL_REC_MAX 10000              // 表最大的数据量
#define PTH_SAME_TBL_RANDOM_OPT_REC_MAX 100     // 随机操作的最大数量

// 图查询
#define EDGE_QUERY_TBL_CNT 40                   // 表数量
#define EDGE_QUERY_TBL_ID_START 10000           // 起始表id，用户给定表id，不是接口分配
#define EDGE_QUERY_TBL_REC_INIT 100             // 初始化数据量
#define EDGE_QUERY_TBL_REC_MAX 1000             // 最大数据量
#define EDGE_QUERY_TBL_RANDOM_OPT_REC_MAX 100   // 随机操作的最大数量

// TPC接口和DB接口混用
#define TPC_DB_TBL_REC_INIT 1000                // 表初始化的数据量
#define TPC_DB_TBL_REC_MAX 10000                // 表最大的数据量
#define TPC_DB_TBL_RANDOM_OPT_REC_MAX 100       // 随机操作的最大数量

#define V1_LS_INVALID_DB_ID (100000)            // 无效的DB ID，这只是用例的设置，并非Db的设置
#define V1_LS_INVALID_TBL_ID 0xFFFF             // 无效的表ID，这只是用例的设置，并非Db的设置

// 写更新操作
typedef enum TagV1LsRanDmlOptE {
    OPT_INSERT = 0,             // 写
    OPT_UPDATE = 1,             // 不更新索引字段
    OPT_UPDATE_IDX = 2,         // 更新索引字段
    OPT_DELETE = 3,             // 删除
    OPT_INVALID = 4             // 无效值
} V1LsRanDmlOptE;

// 查询操作
typedef enum TagV1LsQueryOptE {
    OPT_QUERY_RAN = 0,         // 随机选择后面的类型
    OPT_QUERY_1 = 1,
    OPT_QUERY_2 = 2,
    OPT_QUERY_3 = 3,
    OPT_QUERY_4 = 4,
    OPT_QUERY_5 = 5,
    OPT_QUERY_6 = 6,
    OPT_QUERY_7 = 7,
    OPT_QUERY_8 = 8,
    OPT_QUERY_9 = 9,
    OPT_QUERY_10 = 10,
    OPT_QUERY_INVALID
} V1LsQueryOptE;

// 字段值的信息
typedef struct TagV1LstblRecFieldValInfoT {
    uint32_t fieldVal;       // 校验数据的正确性，校验diff，非索引字段的值=oldFieldVal*relation
    uint32_t relation;       // 校验数据的正确性，校验diff，非索引字段的值=oldFieldVal*relation
} V1LstblRecFieldValInfoT;

// diff的信息
typedef struct TagV1LstblRecOptDiffInfoT {
    bool isCreateDiff;                          // 是否会生成diff
    uint32_t diffCnt;                           // diff的个数
    V1LstblRecFieldValInfoT oldFieldValInfo;    // 生成diff的记录的字段值信息, 更新操作会用到
    V1LstblRecFieldValInfoT newFieldValInfo;    // 生成diff的记录的字段值信息
} V1LstblRecOptDiffInfoT;

// 操作记录的信息
typedef struct TagV1LstblRecOptInfoT {
    V1LsRanDmlOptE opt;                 // 操作类型
    int32_t status;                     // 操作的结果
    V1LstblRecOptDiffInfoT diffInfo;    // 操作生成的diff信息
} V1LstblRecOptInfoT;

// 操作记录的范围信息
typedef struct TagV1LstblRecOptRangeInfoT {
    uint32_t recStart;                  // 操作的起始记录
    uint32_t recEnd;                    // 操作的结束记录
    uint32_t recOptCntMax;              // 最大操作多少记录
} V1LstblRecOptRangeInfoT;

// 记录的信息
typedef struct TagV1LstblRecInfoT {
    bool isExist;                           // 该记录是否存在
    V1LstblRecFieldValInfoT fieldValInfo;   // 记录的字段值信息
} V1LsTblRecInfoT;

// 随机表的增删
typedef enum TagV1LsRanTblOptE {
    TBL_OPT_CREATE = 0,             // 创建表
    TBL_OPT_DROP = 1,               // 删除表
    TBL_OPT_INVALID                 // 无效值
} V1LsRanTblOptE;

// 申请二维数组动态内存
int32_t V1LsMallocUint8ArrayMem(VOS_UINT8 ***ptr, uint32_t arraySize, uint32_t blockSize)
{
    *ptr = (VOS_UINT8 **)V1LsGetMem(sizeof(VOS_UINT8 *) * arraySize);
    if (*ptr == NULL) {
        return -1;
    }
    for (uint32_t i = 0; i < arraySize; i++) {
        (*ptr)[i] = (VOS_UINT8 *)V1LsGetMem(blockSize);
        if ((*ptr)[i] == NULL) {
            for (uint32_t j = 0; j < i; j++) {
                V1LsFreeMem((*ptr)[j]);
            }
            V1LsFreeMem(*ptr);
            return -1;
        }
    }

    return 0;
}

void V1LsFreeUint8ArrayMem(VOS_UINT8 **ptr, uint32_t arraySize)
{
    for (uint32_t i = 0; i < arraySize; i++) {
        V1LsFreeMem(ptr[i]);
    }
    V1LsFreeMem(ptr);
}

int32_t V1LsMallocCharArrayMem(char ***ptr, uint32_t arraySize, uint32_t blockSize)
{
    *ptr = (char **)V1LsGetMem(sizeof(char *) * arraySize);
    if (*ptr == NULL) {
        return -1;
    }
    for (uint32_t i = 0; i < arraySize; i++) {
        (*ptr)[i] = (char *)V1LsGetMem(blockSize);
        if ((*ptr)[i] == NULL) {
            for (uint32_t j = 0; j < i; j++) {
                V1LsFreeMem((*ptr)[j]);
            }
            V1LsFreeMem(*ptr);
            return -1;
        }
    }

    return 0;
}

void V1LsFreeCharArrayMem(char **ptr, uint32_t arraySize)
{
    for (uint32_t i = 0; i < arraySize; i++) {
        V1LsFreeMem(ptr[i]);
    }
    V1LsFreeMem(ptr);
}

void V1LsWriteBuffToFile(const char *file, char *buff, uint32_t len)
{
    FILE *fp = fopen(file, "wb+");
    if (fp == NULL) {
        V1LS_LOG(V1LS_LOG_ERROR, "fopen file(%s) failed", file);
        return;
    }
    uint32_t writeBytes = fwrite(buff, 1, len, fp);
    if (writeBytes != len) {
        V1LS_LOG(V1LS_LOG_ERROR, "write to file %s failed, len: %u, writeBytes: %u", file, len, writeBytes);
    }
    (void)fflush(fp);
    (void)fclose(fp);
}

int32_t V1LsSysviewExportDbDataByTxt(V1LsTblInfoT *tblInfo, char *expFilePath)
{
    int32_t ret;

    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_EXPORT_TXT;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = { 0 };
    VOS_UINT8 *pucResult = NULL;

    // 设置参数
    sysviewArgs.pucFilePath = (VOS_UINT8 *)expFilePath;
    sysviewArgs.ulDbId = tblInfo->dbId;
    sysviewArgs.pucDbName = (VOS_UINT8 *)tblInfo->dbName;

    ret = TPC_Sysview(sysviewType, &sysviewArgs, &pucResult);
    if (ret == DB_SUCCESS_V1) {
        TPC_FreeSysviewResult(&pucResult);
    }

    return ret;
}

pthread_mutex_t g_v1LsFileLock = PTHREAD_MUTEX_INITIALIZER;
bool g_isWriteFileFirst = true;
int32_t V1LsCompAllFieldRec(V1LsTblInfoT *tblInfo, VOS_UINT8 *expRecData, void *actRecData, uint32_t compLen)
{
    int32_t ret = 0;
    ret = memcmp(expRecData, actRecData, compLen);
    if (ret != 0) {
        pthread_mutex_lock(&g_v1LsFileLock);
        // 只记录第一次失败的数据，后续不做记录
        if (g_isWriteFileFirst) {
            // 记录当前预期的数据
            V1LsWriteBuffToFile("comp_fail_ExpRecData.txt", (char *)expRecData, compLen);
            // 记录当前实际的数据
            V1LsWriteBuffToFile("comp_fail_actRecData.txt", (char *)actRecData, compLen);
            // 导出DB的数据
            int32_t ret1 = V1LsSysviewExportDbDataByTxt(tblInfo, (char *)"comp_fail_DbData.txt");
            if (ret1 != 0) {
                V1LS_LOG(V1LS_LOG_ERROR, "V1LsSysviewExportDbDataByTxt failed, ret: %d", ret1);
            }
            g_isWriteFileFirst = false;
        }
        pthread_mutex_unlock(&g_v1LsFileLock);
    }

    return ret;
}

// 设置条件的字段
typedef enum TagV1LsDmlCondFieldE {
    COND_FIELD_F0 = F0,             // F0字段作为条件字段
    COND_FIELD_F26 = F26,           // F26字段作为条件字段
    COND_FIELD_F24 = F24,           // F24字段作为条件字段
    COND_FIELD_F29 = F29,           // F29字段作为条件字段
    COND_FIELD_INVALID              // 无效值
} V1LsDmlCondFieldE;
V1LsDmlCondFieldE g_v1LsCondField[4] = { COND_FIELD_F0, COND_FIELD_F26, COND_FIELD_F24, COND_FIELD_F29 };

V1LsDmlCondFieldE V1LsGetRangeDmlCondField()
{
    uint32_t index = V1LsGetRandomRange(0, sizeof(g_v1LsCondField) / sizeof(V1LsDmlCondFieldE) - 1);
    return g_v1LsCondField[index];
}

// 获取表的记录数
int32_t V1LsGetTblRecNumFromUser(V1LsTblRecInfoT *tblRecInfo, uint32_t recMax)
{
    V1LS_FUNC_RETURN_FAIL_IF_NULL(tblRecInfo);
    uint32_t recNum = 0;
    for (uint32_t i = 0; i < recMax; i++) {
        if (tblRecInfo[i].isExist) {
            recNum++;
        }
    }

    return recNum;
}

void V1LsSetTblRecFieldValInfo(V1LstblRecFieldValInfoT *tblRecFieldValInfo, uint32_t fieldVal, uint32_t relation)
{
    tblRecFieldValInfo->fieldVal = fieldVal;
    tblRecFieldValInfo->relation = relation;
}

void V1LsCopyTblRecInfo(V1LsTblRecInfoT *tblRecInfo1, V1LsTblRecInfoT *tblRecInfo2, uint32_t cnt)
{
    for (uint32_t i = 0; i < cnt; i++) {
        tblRecInfo1[i].isExist = tblRecInfo2[i].isExist;
        tblRecInfo1[i].fieldValInfo.fieldVal = tblRecInfo2[i].fieldValInfo.fieldVal;
        tblRecInfo1[i].fieldValInfo.relation = tblRecInfo2[i].fieldValInfo.relation;
    }
}

void V1LsSetTblRecOptDiffInfo(V1LstblRecOptDiffInfoT *tblRecOptDiffInfo, bool isCreateDiff, uint32_t diffCnt,
    V1LstblRecFieldValInfoT *tblRecOldFieldValInfo, V1LstblRecFieldValInfoT *tblRecNewFieldValInfo)
{
    tblRecOptDiffInfo->isCreateDiff = isCreateDiff;
    tblRecOptDiffInfo->diffCnt = diffCnt;
    V1LsSetTblRecFieldValInfo(&tblRecOptDiffInfo->oldFieldValInfo, tblRecOldFieldValInfo->fieldVal,
        tblRecOldFieldValInfo->relation);
    V1LsSetTblRecFieldValInfo(&tblRecOptDiffInfo->newFieldValInfo, tblRecNewFieldValInfo->fieldVal,
        tblRecNewFieldValInfo->relation);
}

void V1LsSetTblRecOptInfo(V1LstblRecOptInfoT *tblRecOptInfo, V1LsRanDmlOptE opt, int32_t status,
    V1LstblRecOptDiffInfoT *tblRecOptDiffInfo)
{
    tblRecOptInfo->opt = opt;
    tblRecOptInfo->status = status;
    if (tblRecOptDiffInfo != NULL) {
        V1LsSetTblRecOptDiffInfo(&tblRecOptInfo->diffInfo, tblRecOptDiffInfo->isCreateDiff, tblRecOptDiffInfo->diffCnt,
            &tblRecOptDiffInfo->oldFieldValInfo, &tblRecOptDiffInfo->newFieldValInfo);
    } else {
        tblRecOptInfo->diffInfo.isCreateDiff = false;
        tblRecOptInfo->diffInfo.diffCnt = 0;
    }
}

void V1LsSetTblRecInfo(V1LsTblRecInfoT *tblRecInfo, bool isExist, V1LstblRecFieldValInfoT *tblRecFieldValInfo)
{
    tblRecInfo->isExist = isExist;
    if (tblRecInfo->isExist) {
        tblRecInfo->fieldValInfo.fieldVal = tblRecFieldValInfo->fieldVal;
        tblRecInfo->fieldValInfo.relation = tblRecFieldValInfo->relation;
    } else {
        if (tblRecFieldValInfo != NULL) {
            tblRecInfo->fieldValInfo.fieldVal = 0;
            tblRecInfo->fieldValInfo.relation = 0;
        }
    }
}

void V1LsSetTblRecInfoRange(V1LsTblRecInfoT *tblRecInfo, uint32_t startRec, uint32_t endRec,
    bool isExist, uint32_t relation)
{
    V1LstblRecFieldValInfoT tblRecFieldValInfo;
    for (VOS_UINT32 i = startRec; i <= endRec; i++) {
        if (isExist) {
            V1LsSetTblRecFieldValInfo(&tblRecFieldValInfo, i, relation);
        } else {
            V1LsSetTblRecFieldValInfo(&tblRecFieldValInfo, 0, 0);
        }
        V1LsSetTblRecInfo(&tblRecInfo[i], isExist, &tblRecFieldValInfo);
    }
}

int32_t V1LsInsertRecRange(uint32_t cdbId, V1LsTblInfoT *tblInfo, uint32_t startRec, uint32_t endRec,
    uint32_t fieldValRelation)
{
    int32_t ret;

    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_FAIL_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");
    for (VOS_UINT32 i = startRec; i <= endRec; i++) {
        V1LsSetRecBuf(tblInfo, &pstDsBuf, i, i * fieldValRelation);
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_InsertRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstDsBuf);
        } else {
            ret = DBS_LocalAddRecordForDatabase(tblInfo->dbId, tblInfo->tblId, &pstDsBuf);
        }
        if (ret != DB_SUCCESS_V1) {
            break;
        } else {
            tblInfo->recCurr++;
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);

    return ret;
}

// 随机插入随机数据量的数据
int32_t V1LsRanDmlInsert(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo,
    V1LstblRecOptRangeInfoT *tblOptRecRangeInfo, V1LstblRecOptInfoT *optRecInfo, uint32_t *optRecCnt)
{
    int32_t ret;

    // 随机数种子
    time_t t;
    (void)time(&t);
    (void)srand((VOS_UINT32)t);

    // 要操作多少条数据，限制在1000条
    uint32_t optRecCntTmp = V1LsGetRandomRange(1, tblOptRecRangeInfo->recOptCntMax);

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    uint32_t fieldVal = 0;
    V1LstblRecFieldValInfoT tblRecOldFieldValInfo;
    V1LstblRecFieldValInfoT tblRecNewFieldValInfo;
    V1LstblRecOptDiffInfoT tblRecOptDiffInfo;
    for (uint32_t i = 0; i < optRecCntTmp; i++) {
        // 生成随机的要操作的记录
        fieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        V1LsSetRecBuf(tblInfo, &pstDsBuf, fieldVal, fieldVal * FIELD_VAL_RELATION_1);
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_InsertRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstDsBuf);
        } else {
            ret = DBS_LocalAddRecordForDatabase(tblInfo->dbId, tblInfo->tblId, &pstDsBuf);
        }
        if (ret == DB_SUCCESS_V1) {
            // 这里如果成功的情况下，表中的这条数据原先一定是不存在的（索引唯一的情况下）
            V1LS_EXPECT_FALSE(tblRecInfo[fieldVal].isExist, "check fieldVal:%u status failed", fieldVal);
            // 设置状态存在，记录对应的操作信息和字段值信息
            V1LsSetTblRecFieldValInfo(&tblRecOldFieldValInfo, 0, 0);
            V1LsSetTblRecFieldValInfo(&tblRecNewFieldValInfo, fieldVal, FIELD_VAL_RELATION_1);
            V1LsSetTblRecOptDiffInfo(&tblRecOptDiffInfo, true, 1, &tblRecOldFieldValInfo, &tblRecNewFieldValInfo);
            V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_INSERT, DB_SUCCESS_V1, &tblRecOptDiffInfo);
            // 插入的数据存在
            V1LsSetTblRecInfo(&tblRecInfo[fieldVal], true, &tblRecNewFieldValInfo);
            tblInfo->recCurr++;
        } else {
            // 把操作的结果返回到外面
            V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_INSERT, ret, NULL);
            if (ret != VOS_ERRNO_DB_KEYDUPLICATE) {
                V1LsFreeDsBuf(&pstDsBuf);
            }
            // 索引唯一的情况下，失败应该是数据冲突
            V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_KEYDUPLICATE, ret, " exp InsertRec ret failed");
            // 写数据失败，只能是旧的数据存在，导致写数据冲突，不能是其它错误码(假设没有内存不足的情况下)
            V1LS_EXPECT_TRUE(tblRecInfo[fieldVal].isExist, "check fieldVal:%u status failed", fieldVal);
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
    *optRecCnt = optRecCntTmp;

    return DB_SUCCESS_V1;
}

// 随机更新随机数据量的数据
int32_t V1LsRanDmlUpdate(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo,
    V1LstblRecOptRangeInfoT *tblOptRecRangeInfo, V1LstblRecOptInfoT *optRecInfo, uint32_t *optRecCnt, char **buff)
{
    int32_t ret;

    // 随机数种子
    time_t t;
    (void)time(&t);
    (void)srand((VOS_UINT32)t);

    // 要操作多少条数据，限制在1000条
    uint32_t optRecCntTmp = V1LsGetRandomRange(1, tblOptRecRangeInfo->recOptCntMax);

    // 设置条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    pstCond.aCond->ucFieldId = V1LsGetRangeDmlCondField();
    pstCond.aCond->enOp = DB_OP_EQUAL;

    // 更新所有字段
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter);

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    uint32_t fieldVal = 0;
    VOS_UINT32 pulRecNum;
    V1LstblRecFieldValInfoT tblRecOldFieldValInfo;
    V1LstblRecFieldValInfoT tblRecNewFieldValInfo;
    V1LstblRecOptDiffInfoT tblRecOptDiffInfo;
    for (uint32_t i = 0; i < optRecCntTmp; i++) {
        // 旧值作为条件
        fieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        g_v1LsTblField[pstCond.aCond->ucFieldId].setCond(pstCond.aCond->aucValue,
            tblInfo->fldStoreLen[pstCond.aCond->ucFieldId], fieldVal, buff[0]);
        V1LsSetRecBuf(tblInfo, &pstDsBuf, fieldVal, fieldVal * FIELD_VAL_RELATION_2);

        pulRecNum = 0;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_UpdateRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
        } else {
            ret = DBS_LocalModRecordForDatabase(
                tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
        }
        if (ret == DB_SUCCESS_V1) {
            if (pulRecNum != 0) {
                V1LS_EXPECT_EQ(1, pulRecNum, "exp UpdateRec pulRecNum err");
                // 如果新数据更新成功，那么旧数据是一定存在
                V1LS_EXPECT_TRUE(tblRecInfo[fieldVal].isExist, "check fieldVal:%u status failed", fieldVal);
                // 设置状态存在，记录对应的操作信息和字段值信息
                V1LsSetTblRecFieldValInfo(&tblRecOldFieldValInfo, fieldVal, tblRecInfo[fieldVal].fieldValInfo.relation);
                V1LsSetTblRecFieldValInfo(&tblRecNewFieldValInfo, fieldVal, FIELD_VAL_RELATION_2);
                V1LsSetTblRecOptDiffInfo(&tblRecOptDiffInfo, true, 2, &tblRecOldFieldValInfo, &tblRecNewFieldValInfo);
                V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_UPDATE, DB_SUCCESS_V1, &tblRecOptDiffInfo);
                // 更新的数据存在
                V1LsSetTblRecInfo(&tblRecInfo[fieldVal], true, &tblRecNewFieldValInfo);
            } else {
                // 把操作的结果返回到外面
                V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_UPDATE, STATUS_FAILED, NULL);
                // 旧数据不存在
                V1LS_EXPECT_FALSE(tblRecInfo[fieldVal].isExist, "check fieldVal:%u status failed", fieldVal);
            }
        } else {
            // 把操作的结果返回到外面
            V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_UPDATE, ret, NULL);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
            }
            // 这个场景不应该返回失败
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp UpdateRec ret failed");
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
    *optRecCnt = optRecCntTmp;

    return DB_SUCCESS_V1;
}

// 随机更新随机数据量的数据，更新主键
int32_t V1LsRanDmlUpdateIdx(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo,
    V1LstblRecOptRangeInfoT *tblOptRecRangeInfo, V1LstblRecOptInfoT *optRecInfo, uint32_t *optRecCnt, char **buff)
{
    int32_t ret;

    // 随机数种子
    time_t t;
    (void)time(&t);
    (void)srand((VOS_UINT32)t);

    // 要操作多少条数据，限制在1000条
    uint32_t optRecCntTmp = V1LsGetRandomRange(1, tblOptRecRangeInfo->recOptCntMax);

    // 设置条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    pstCond.aCond->ucFieldId = V1LsGetRangeDmlCondField();
    pstCond.aCond->enOp = DB_OP_EQUAL;

    // 更新所有字段
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter);

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    uint32_t oldFieldVal = 0;
    uint32_t newFieldVal = 0;
    VOS_UINT32 pulRecNum;
    V1LstblRecFieldValInfoT tblRecOldFieldValInfo;
    V1LstblRecFieldValInfoT tblRecNewFieldValInfo;
    V1LstblRecOptDiffInfoT tblRecOptDiffInfo;
    for (uint32_t i = 0; i < optRecCntTmp; i++) {
        // 旧直作为条件
        oldFieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        g_v1LsTblField[pstCond.aCond->ucFieldId].setCond(pstCond.aCond->aucValue,
            tblInfo->fldStoreLen[pstCond.aCond->ucFieldId], oldFieldVal, buff[0]);
        // 新值作为更新值
        newFieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        uint32_t relation = 0;
        if (newFieldVal == oldFieldVal) {
            if (tblRecInfo[oldFieldVal].fieldValInfo.relation == FIELD_VAL_RELATION_1) {
                relation = FIELD_VAL_RELATION_2;
            } else if (tblRecInfo[oldFieldVal].fieldValInfo.relation == FIELD_VAL_RELATION_2) {
                relation = FIELD_VAL_RELATION_1;
            }
        }
        V1LsSetRecBuf(tblInfo, &pstDsBuf, newFieldVal, newFieldVal * relation);

        pulRecNum = 0;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_UpdateRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
        } else {
            ret = DBS_LocalModRecordForDatabase(
                tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
        }
        if (ret == DB_SUCCESS_V1) {
            if (pulRecNum != 0) {
                // 等值条件，唯一主键，只能查到1条数据
                V1LS_EXPECT_EQ(1, pulRecNum, "exp UpdateRec pulRecNum err");
                // 如果新数据更新成功，那么旧数据是一定存在
                V1LS_EXPECT_TRUE(tblRecInfo[oldFieldVal].isExist,  "check oldFieldVal:%u status failed", oldFieldVal);
                // 新数据如果不等于旧数据，那么新数据原来一定不存在
                if (oldFieldVal != newFieldVal) {
                    V1LS_EXPECT_FALSE(
                        tblRecInfo[newFieldVal].isExist, "check newFieldVal:%u status failed", newFieldVal);
                }
                // 设置状态存在，记录对应的操作信息和字段值信息
                // 新数据的设置需要放在旧数据后面设置，有可能新旧是同一个值
                V1LsSetTblRecFieldValInfo(&tblRecOldFieldValInfo, oldFieldVal,
                    tblRecInfo[oldFieldVal].fieldValInfo.relation);
                V1LsSetTblRecFieldValInfo(&tblRecNewFieldValInfo, newFieldVal, relation);
                V1LsSetTblRecOptDiffInfo(&tblRecOptDiffInfo, true, 2, &tblRecOldFieldValInfo, &tblRecNewFieldValInfo);
                V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_UPDATE_IDX, DB_SUCCESS_V1, &tblRecOptDiffInfo);
                // 更新的旧数据不存在
                V1LsSetTblRecInfo(&tblRecInfo[oldFieldVal], false, NULL);
                // 更新的新数据存在
                V1LsSetTblRecInfo(&tblRecInfo[newFieldVal], true, &tblRecNewFieldValInfo);
            } else {
                V1LS_EXPECT_FALSE(tblRecInfo[oldFieldVal].isExist, "check oldFieldVal:%u status failed", oldFieldVal);
                // 把操作的结果返回到外面
                V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_UPDATE_IDX, STATUS_FAILED, NULL);
            }
        } else {
            // 把操作的结果返回到外面
            V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_UPDATE_IDX, ret, NULL);
            if (ret != VOS_ERRNO_DB_KEYDUPLICATE && ret != VOS_ERRNO_DB_CHGKEY) {
                V1LsFreeDsBuf(&pstDsBuf);
            }
            // 这里如果失败，只能是新数据跟原有数据冲突，不能是其它错误码(假设没有内存不足的情况下)
            if (tblInfo->dbType == DB_TYPE_TPC && cdbId != TPC_GLOBAL_CDB) {
                // CDB更新冲突
                V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_KEYDUPLICATE, ret,
                    "exp UpdateRec ret failed, oldFieldVal:%u, newFieldVal:%u", oldFieldVal, newFieldVal);
            } else {
                // RDB更新冲突
                V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_CHGKEY, ret,
                    "exp UpdateRec ret failed, oldFieldVal:%u, newFieldVal:%u", oldFieldVal, newFieldVal);
            }
            V1LS_EXPECT_TRUE(tblRecInfo[oldFieldVal].isExist, "check oldFieldVal:%u status failed", oldFieldVal);
            V1LS_EXPECT_TRUE(tblRecInfo[newFieldVal].isExist, "check newFieldVal:%u status failed", newFieldVal);
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
    *optRecCnt = optRecCntTmp;

    return DB_SUCCESS_V1;
}

// 随机删除随机数据量的数据
int32_t V1LsRanDmlDelete(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo,
    V1LstblRecOptRangeInfoT *tblOptRecRangeInfo, V1LstblRecOptInfoT *optRecInfo, uint32_t *optRecCnt, char **buff)
{
    int32_t ret;

    // 随机数种子
    time_t t;
    (void)time(&t);
    (void)srand((VOS_UINT32)t);

    // 要操作多少条数据，限制在1000条
    uint32_t optRecCntTmp = V1LsGetRandomRange(1, tblOptRecRangeInfo->recOptCntMax);

    // 设置条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    pstCond.aCond->ucFieldId = V1LsGetRangeDmlCondField();
    pstCond.aCond->enOp = DB_OP_EQUAL;

    uint32_t fieldVal = 0;
    VOS_UINT32 pulRecNum = 0;
    V1LstblRecFieldValInfoT tblRecOldFieldValInfo;
    V1LstblRecFieldValInfoT tblRecNewFieldValInfo;
    V1LstblRecOptDiffInfoT tblRecOptDiffInfo;
    for (uint32_t i = 0; i < optRecCntTmp; i++) {
        fieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        g_v1LsTblField[pstCond.aCond->ucFieldId].setCond(pstCond.aCond->aucValue,
            tblInfo->fldStoreLen[pstCond.aCond->ucFieldId], fieldVal, buff[0]);

        pulRecNum = 0;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_DeleteRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pulRecNum);
        } else {
            ret = DBS_LocalDelRecordForDatabase(tblInfo->dbId, tblInfo->tblId, &pstCond, &pulRecNum);
        }
        if (ret == DB_SUCCESS_V1) {
            if (pulRecNum == 0) {
                // 这里如果pulRecNum==0情况下，表中的这条数据是一定不存在的
                V1LS_EXPECT_FALSE(tblRecInfo[fieldVal].isExist, "check fieldVal:%u status failed", fieldVal);
                // 把操作的结果返回到外面
                V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_DELETE, STATUS_FAILED, NULL);
            } else {
                // 等值查询，且还是唯一索引，只能查到1条
                V1LS_EXPECT_EQ(1, pulRecNum, "exp DeleteRec pulRecNum err");
                // 这里如果pulRecNum==1情况下，表中的这条数据原先一定是存在的
                V1LS_EXPECT_TRUE(tblRecInfo[fieldVal].isExist, "check fieldVal:%u status failed", fieldVal);
                // 设置状态存在，记录对应的操作信息和字段值信息
                V1LsSetTblRecFieldValInfo(&tblRecOldFieldValInfo, fieldVal, tblRecInfo[fieldVal].fieldValInfo.relation);
                V1LsSetTblRecFieldValInfo(&tblRecNewFieldValInfo, 0, 0);
                V1LsSetTblRecOptDiffInfo(&tblRecOptDiffInfo, true, 1, &tblRecOldFieldValInfo, &tblRecNewFieldValInfo);
                V1LsSetTblRecOptInfo(&optRecInfo[i], OPT_DELETE, DB_SUCCESS_V1, &tblRecOptDiffInfo);
                // 被删除的数据不存在
                V1LsSetTblRecInfo(&tblRecInfo[fieldVal], false, &tblRecNewFieldValInfo);
                tblInfo->recCurr--;
            }
        }
        // 删除操作不应该返回报错
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp DeleteRec ret failed");
    }
    *optRecCnt = optRecCntTmp;

    return DB_SUCCESS_V1;
}

int32_t V1LsRanDmlOpt(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo,
    V1LstblRecOptRangeInfoT *tblOptRecRangeInfo, V1LstblRecOptInfoT *tblOptRecInfo, uint32_t *optRecCnt)
{
    int32_t ret;

    // 提前申请一块内存，避免频繁的申请内存，需要超过最长字段的长度
    uint32_t buffSize = 3;
    char **buff = (char **)V1LsGetMem(sizeof(char *) * buffSize);
    V1LS_FUNC_RETURN_FAIL_IF_NULL(buff);
    for (uint32_t i = 0; i < buffSize; i++) {
        buff[i] = (char *)V1LsGetMem(V1LsGetTblMaxFldLen(tblInfo) + 1);
        V1LS_FUNC_RETURN_FAIL_IF_NULL(buff[i]);
    }

    V1LsRanDmlOptE ranDmlOpt = (V1LsRanDmlOptE)V1LsGetRandomRange((uint32_t)OPT_INSERT, (uint32_t)OPT_DELETE);
    switch (ranDmlOpt) {
        // 随机插入随机数量的数据
        case OPT_INSERT:
            ret = V1LsRanDmlInsert(cdbId, tblInfo, tblRecInfo, tblOptRecRangeInfo, tblOptRecInfo, optRecCnt);
            break;
        // 随机更新随机数量的数据
        case OPT_UPDATE:
            ret = V1LsRanDmlUpdate(cdbId, tblInfo, tblRecInfo, tblOptRecRangeInfo, tblOptRecInfo, optRecCnt, buff);
            break;
        // 随机更新随机数量的数据，会更新索引字段
        case OPT_UPDATE_IDX:
            ret = V1LsRanDmlUpdateIdx(cdbId, tblInfo, tblRecInfo, tblOptRecRangeInfo, tblOptRecInfo, optRecCnt, buff);
            break;
        // 随机删除随机数量的数据
        case OPT_DELETE:
            ret = V1LsRanDmlDelete(cdbId, tblInfo, tblRecInfo, tblOptRecRangeInfo, tblOptRecInfo, optRecCnt, buff);
            break;
        default: ret = -1; break;
    }
    if (ret != DB_SUCCESS_V1) {
        V1LS_LOG(V1LS_LOG_INFO, "dbId:%u, dbType:%u, tblName:%s, tblId:%u", tblInfo->dbId, tblInfo->dbType,
            tblInfo->tblName, tblInfo->tblId);
    }

    for (uint32_t i = 0; i < buffSize; i++) {
        V1LsFreeMem(buff[i]);
    }
    V1LsFreeMem(buff);

    if (ret != DB_SUCCESS_V1) {
        V1LS_LOG(V1LS_LOG_ERROR,
            "ranDmlOpt:%u(0:INSERT,1:UPDATE,2:UPDATE_IDX,3:DELETE) failed, ret:%#x/%d", (uint32_t)ranDmlOpt, ret, ret);
    }

    return ret;
}

// diff数据的对比
int32_t V1LsCompDiff(VOS_UINT8 *recData, void *diffData, uint32_t recLen)
{
    return memcmp(recData, diffData, recLen);
}

// 插入操作diff校验
int32_t V1LsInsertDiffCheck(V1LsTblInfoT *tblInfo, V1LstblRecOptInfoT *optRecInfo, void *diffRecList,
    uint32_t currDiffCnt)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 这里需要转换，不然不好做指针的偏移
    uint8_t *diffRecListPtr = (uint8_t *)diffRecList;
    // 对于插入来说，单-的插入操作，diff总会为1
    V1LS_EXPECT_EQ(1, optRecInfo->diffInfo.diffCnt, "exp optRecInfo->diffInfo.diffCnt failed");
    // 校验diff
    V1LsSetRecBuf(tblInfo, &pstDsBuf, optRecInfo->diffInfo.newFieldValInfo.fieldVal,
        optRecInfo->diffInfo.newFieldValInfo.fieldVal * optRecInfo->diffInfo.newFieldValInfo.relation);
    ret = V1LsCompDiff(pstDsBuf.StdBuf.pucData, diffRecListPtr + currDiffCnt * tblInfo->recLen, tblInfo->recLen);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeDsBuf(&pstDsBuf);
        V1LS_EXPECT_EQ(DB_SUCCESS_V1, ret, "tblName: %s, V1LsInsertDiffCheck V1LsCompDiff failed", tblInfo->tblName);
        return ret;
    }
    V1LsFreeDsBuf(&pstDsBuf);

    return DB_SUCCESS_V1;
}

// 更新操作diff校验，空实现
int32_t V1LsIUpdateDiffCheck(V1LsTblInfoT *tblInfo, V1LstblRecOptInfoT *optRecInfo, void *diffRecList,
    uint32_t currDiffCnt)
{
    return DB_SUCCESS_V1;
}

// 更新操作diff校验，空实现
int32_t V1LsUpdateIdxDiffCheck(V1LsTblInfoT *tblInfo, V1LstblRecOptInfoT *optRecInfo, void *diffRecList,
    uint32_t currDiffCnt)
{
    return DB_SUCCESS_V1;
}

// 删除操作diff校验
int32_t V1LsDeleteDiffCheck(V1LsTblInfoT *tblInfo, V1LstblRecOptInfoT *optRecInfo, void *diffRecList,
    uint32_t currDiffCnt)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    uint8_t *diffRecListPtr = (uint8_t *)diffRecList;
    // 对于删除来说，单-的删除操作，diff总会为1
    V1LS_EXPECT_EQ(1, optRecInfo->diffInfo.diffCnt, "exp optRecInfo->diffInfo.diffCnt failed");
    // 校验diff
    V1LsSetRecBuf(tblInfo, &pstDsBuf, optRecInfo->diffInfo.oldFieldValInfo.fieldVal,
        optRecInfo->diffInfo.oldFieldValInfo.fieldVal * optRecInfo->diffInfo.oldFieldValInfo.relation);
    ret = V1LsCompDiff(pstDsBuf.StdBuf.pucData, diffRecListPtr + currDiffCnt * tblInfo->recLen, tblInfo->recLen);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeDsBuf(&pstDsBuf);
        V1LS_EXPECT_EQ(DB_SUCCESS_V1, ret, "tblName: %s, V1LsDeleteDiffCheck V1LsCompDiff failed", tblInfo->tblName);
        return ret;
    }
    V1LsFreeDsBuf(&pstDsBuf);

    return DB_SUCCESS_V1;
}

// 先记update(记2条(删除的1条，更新的1条)(同一条数据不会生成diff)), 再记delete, 最后记insert
int32_t V1LsGetDiffAndCheck(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo,
    V1LstblRecOptInfoT *optRecInfo, uint32_t optRecCnt)
{
    int32_t ret;

    // 如果不是TPC接口创建的表，不支持diff查询，直接返回成功
    if (!tblInfo->dbType == DB_TYPE_DB) {
        return DB_SUCCESS_V1;
    }

    // 获取diff
    uint32_t insrecNum = 0;
    void *insRecList = NULL;
    uint32_t delrecNum = 0;
    void *delRecList = NULL;
    uint32_t updrecNum = 0;
    void *updRecList = NULL;
    ret = TPC_SelectCdbDiffData(cdbId, tblInfo->tblId, &insrecNum, &insRecList, &delrecNum, &delRecList,
        &updrecNum, &updRecList);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsGetDiffAndCheck TPC_SelectCdbDiffData failed");

    uint32_t totalDiffCnt = 0;
    uint32_t insertDiffCnt = 0;
    uint32_t updateDiffCnt = 0;
    uint32_t deleteDiffCnt = 0;

    // 当前只能校验insert和delete操作，如果其中有update操作，不做校验
    // 原因：diff是拿初始状态下的数据和最后状态下的数据做diff，当前长稳设计中有可能会随机更新同一条数据
    // 就会导致diff的数量和diff的数据很难校验，即使是记录了所有操作的结果，但还是很难校验
    for (uint32_t i = 0; i < optRecCnt; i++) {
        if (updrecNum != 0) {
            TPC_FreeDiffData(insRecList, delRecList, updRecList);
            return DB_SUCCESS_V1;
        }
        if (optRecInfo[i].status == DB_SUCCESS_V1 && optRecInfo[i].diffInfo.isCreateDiff) {
            totalDiffCnt += optRecInfo[i].diffInfo.diffCnt;
        }
    }
    // 需要先验证用例的预期的diff数量和实际的diff数量是否一致，否则可能会越界
    if (totalDiffCnt != (insrecNum + updrecNum + delrecNum)) {
        TPC_FreeDiffData(insRecList, delRecList, updRecList);
        V1LS_FUNC_RETURN_FAIL_IF_NE(totalDiffCnt, (insrecNum + updrecNum + delrecNum), "exp totalDiffCnt failed");
    }

    // 校验diff
    for (uint32_t i = 0; i < optRecCnt; i++) {
        if (optRecInfo[i].status == DB_SUCCESS_V1 && optRecInfo[i].diffInfo.isCreateDiff) {
            switch (optRecInfo[i].opt) {
                case OPT_INSERT: V1LsInsertDiffCheck(tblInfo, &optRecInfo[i], insRecList, insertDiffCnt);
                    insertDiffCnt++;
                    break;
                case OPT_UPDATE: V1LsIUpdateDiffCheck(tblInfo, &optRecInfo[i], updRecList, updateDiffCnt);
                    updateDiffCnt++;
                    break;
                case OPT_UPDATE_IDX: V1LsUpdateIdxDiffCheck(tblInfo, &optRecInfo[i], updRecList, updateDiffCnt);
                    updateDiffCnt++;
                    break;
                case OPT_DELETE: V1LsDeleteDiffCheck(tblInfo, &optRecInfo[i], delRecList, deleteDiffCnt);
                    deleteDiffCnt++;
                    break;
                default: break;
            }
        }
    }

    // 释放diff
    TPC_FreeDiffData(insRecList, delRecList, updRecList);

    return DB_SUCCESS_V1;
}

// 4条件，3索引 + 1非索引，索引字段按schema顺序，TPC_SelectAllRec接口
int32_t V1LsQueryData1(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t recStart,
    uint32_t recEnd, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 申请数据buff
    DB_DSBUF_STRU pstDsBufCheck;
    ret = V1LsMallocDsBuf(&pstDsBufCheck, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 4;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    VOS_UINT32 condVal;
    for (uint32_t i = recStart; i < recEnd; i++) {
        condVal = i;
        V1LsSetUint32CondVal(&(pstCond.aCond[0]), F0, DB_OP_EQUAL, condVal);
        V1LsSetUint64CondVal(&(pstCond.aCond[1]), F7, DB_OP_LESSEQUAL, condVal);
        V1LsSetStringCondValLikeOneData(
            &(pstCond.aCond[2]), F11, DB_OP_LIKE, tblInfo->fldStoreLen[F11], condVal, buff[0]);
        V1LsSetBcdCondVal(&(pstCond.aCond[3]), F25, DB_OP_HAVEPREFIX, tblInfo->fldStoreLen[F25],
            condVal * tblRecInfo[i].fieldValInfo.relation, buff[1]);

        pstDsBuf.usRecNum = 2;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_SelectAllRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        } else {
            ret = DB_SelectAllRec(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        }
        
        if (tblRecInfo[i].isExist) {
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp SelectAllRec ret failed, condVal:%u", condVal);
            if (pstDsBuf.usRecNum != 1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_FAIL_IF_NE(1, pstDsBuf.usRecNum, "exp pstDsBuf.usRecNum failed");
            // 校验数据
            V1LsSetRecBuf(tblInfo, &pstDsBufCheck, condVal, condVal * tblRecInfo[i].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(tblInfo, pstDsBuf.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, tblInfo->recLen);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed");
        } else {
            if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_RECNOTEXIST, ret, "exp SelectAllRec ret failed");
            ret = DB_SUCCESS_V1;
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
    V1LsFreeDsBuf(&pstDsBufCheck);

    return ret;
}

// 5条件，4索引 + 1非索引，索引字段按schema顺序，结果字段按schema顺序，TPC_SelectAllRecByOrderEx接口
int32_t V1LsQueryData2(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t recStart,
    uint32_t recEnd, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_BUF_STRU pstBuf;
    ret = V1LsMallocBufEx(&pstBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocBufEx failed");

    // 申请数据buff
    DB_BUF_STRU pstBufCheck;
    ret = V1LsMallocBufEx(&pstBufCheck, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocBufEx failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 5;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    VOS_UINT32 condVal;
    for (uint32_t i = recStart; i < recEnd; i++) {
        condVal = i;
        V1LsSetUint32CondVal(&(pstCond.aCond[0]), F0, DB_OP_EQUAL, condVal);
        V1LsSetUint64CondVal(&(pstCond.aCond[1]), F7, DB_OP_LESSEQUAL, condVal);
        V1LsSetStringCondValLikeOneData(
            &(pstCond.aCond[2]), F11, DB_OP_LIKE, tblInfo->fldStoreLen[F11], condVal, buff[0]);
        V1LsSetVbytesCondVal(
            &(pstCond.aCond[3]), F13, DB_OP_LARGEREQUAL, tblInfo->fldStoreLen[F13], condVal, buff[1]);
        V1LsSetBcdCondVal(&(pstCond.aCond[4]), F25, DB_OP_HAVEPREFIX, tblInfo->fldStoreLen[F25],
            condVal * tblRecInfo[i].fieldValInfo.relation, buff[2]);

        pstBuf.ulRecNum = 2;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_SelectAllRecByOrderEx(
                cdbId, tblInfo->dbId, tblInfo->tblId, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
        } else {
            ret = DB_SelectAllRecByOrderEx(
                tblInfo->dbId, tblInfo->tblId, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
        }
        if (tblRecInfo[i].isExist) {
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeBufEx(&pstBuf);
                V1LsFreeBufEx(&pstBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp SelectAllRecByOrderEx failed, condVal:%u", condVal);
            if (pstBuf.ulRecNum != 1) {
                V1LsFreeBufEx(&pstBuf);
                V1LsFreeBufEx(&pstBufCheck);
            }
            V1LS_FUNC_RETURN_FAIL_IF_NE(1, pstBuf.ulRecNum, "exp pstBuf.ulRecNum failed");
            // 校验数据
            V1LsSetRecBufEx(tblInfo, &pstBufCheck, condVal, condVal * tblRecInfo[i].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(
                tblInfo, (VOS_UINT8 *)pstBuf.pBuf, (VOS_UINT8 *)pstBufCheck.pBuf, tblInfo->recLen);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeBufEx(&pstBuf);
                V1LsFreeBufEx(&pstBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed");
        } else {
            if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
                V1LsFreeBufEx(&pstBuf);
                V1LsFreeBufEx(&pstBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_RECNOTEXIST, ret, "exp SelectAllRecByOrderEx ret failed");
            ret = DB_SUCCESS_V1;
        }
    }
    V1LsFreeBufEx(&pstBuf);
    V1LsFreeBufEx(&pstBufCheck);

    return ret;
}

// 4条件，3索引 + 1非索引，条件的字段按schema乱序，TPC_BeginSelect接口
int32_t V1LsQueryData3(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t recStart,
    uint32_t recEnd, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 申请数据buff
    DB_DSBUF_STRU pstDsBufCheck;
    ret = V1LsMallocDsBuf(&pstDsBufCheck, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 3;
    V1LsSetUint32CondVal(&(pstCond.aCond[1]), F0, DB_OP_LARGEREQUAL, recStart);
    V1LsSetUint64CondVal(&(pstCond.aCond[0]), F7, DB_OP_LESSEQUAL, recEnd);
    V1LsSetStringCondValLikeAllData(&(pstCond.aCond[2]), F11, DB_OP_LIKE);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    DB_SELHANDLE selectHdl;
    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_BeginSelect(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &selectHdl);
    } else {
        ret = DB_BeginSelect(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &selectHdl);
    }
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeDsBuf(&pstDsBuf);
        V1LsFreeDsBuf(&pstDsBufCheck);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp BeginSelect ret failed");

    for (uint32_t i = recStart; i < recEnd; i++) {
        if (tblRecInfo[i].isExist) {
            // 获取数据
            pstDsBuf.usRecNum = 1;
            if (tblInfo->dbType == DB_TYPE_TPC) {
                ret = TPC_FetchSelectRec(cdbId, selectHdl, &pstDsBuf);
            } else {
                ret = DB_FetchSelectRec(selectHdl, &pstDsBuf);
            }
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp FetchSelectRec ret failed, i:%u", i);
            if (pstDsBuf.usRecNum != 1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_FAIL_IF_NE(1, pstDsBuf.usRecNum, "exp pstDsBuf.usRecNum failed");
            // 校验数据
            V1LsSetRecBuf(tblInfo, &pstDsBufCheck, i, i * tblRecInfo[i].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(tblInfo, pstDsBuf.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, tblInfo->recLen);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed");
        }
    }
    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_EndSelect(cdbId, selectHdl);
    } else {
        ret = DB_EndSelect(selectHdl);
    }
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeDsBuf(&pstDsBuf);
        V1LsFreeDsBuf(&pstDsBufCheck);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp ret EndSelect failed");

    V1LsFreeDsBuf(&pstDsBuf);
    V1LsFreeDsBuf(&pstDsBufCheck);

    return ret;
}

// 2条件，非索引字段，条件的字段按schema乱序，结果字段(2个字段)按schema乱序，TPC_BeginIdxSelectByOrder接口
int32_t V1LsQueryData4(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t recStart,
    uint32_t recEnd, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, sizeof(int32_t) + sizeof(int64_t), 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 申请数据buff
    DB_DSBUF_STRU pstDsBufCheck;
    ret = V1LsMallocDsBuf(&pstDsBufCheck, sizeof(int32_t) + sizeof(int64_t), 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 2;
    V1LsSetInt32CondVal(&(pstCond.aCond[1]), F1, DB_OP_LARGEREQUAL, -2147483648);
    V1LsSetInt64CondVal(&(pstCond.aCond[0]), F6, DB_OP_LESSEQUAL, 9223372036854775807);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    VOS_UINT8 fieldId[2] = { F6, F1 };
    V1LsSetFldFilter(&pstFldFilter, sizeof(fieldId), fieldId);

    // 结果排序
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    DB_SELHANDLE selectHdl;
    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_BeginIdxSelectByOrder(cdbId, tblInfo->dbId, tblInfo->tblId, &pstSort, &pstCond, &pstFldFilter,
            &selectHdl);
    } else {
        ret = DB_BeginIdxSelectByOrder(tblInfo->dbId, tblInfo->tblId, &pstSort, &pstCond, &pstFldFilter,
            &selectHdl);
    }
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeDsBuf(&pstDsBuf);
        V1LsFreeDsBuf(&pstDsBufCheck);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp BeginIdxSelectByOrder ret failed");
    for (uint32_t i = recStart; i < recEnd; i++) {
        if (tblRecInfo[i].isExist) {
            // 获取数据
            pstDsBuf.usRecNum = 1;
            if (tblInfo->dbType == DB_TYPE_TPC) {
                ret = TPC_FetchSelectRec(cdbId, selectHdl, &pstDsBuf);
            } else {
                ret = DB_FetchSelectRec(selectHdl, &pstDsBuf);
            }
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp FetchSelectRec ret failed");
            if (pstDsBuf.usRecNum != 1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_FAIL_IF_NE(1, pstDsBuf.usRecNum, "exp pstDsBuf.usRecNum failed");

            // 校验数据
            V1LsSetPartFieldRecBuf(&pstDsBufCheck, i * tblRecInfo[i].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(tblInfo, pstDsBuf.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData,
                sizeof(int32_t) + sizeof(int64_t));
            if (ret != DB_SUCCESS_V1) {
                int64_t val = *(int64_t *)pstDsBuf.StdBuf.pucData;
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
                V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed, i:%u, actual:%lld", i, val);
            }
        }
    }
    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_EndSelect(cdbId, selectHdl);
    } else {
        ret = DB_EndSelect(selectHdl);
    }
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeDsBuf(&pstDsBuf);
        V1LsFreeDsBuf(&pstDsBufCheck);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp EndSelect ret failed");

    V1LsFreeDsBuf(&pstDsBuf);
    V1LsFreeDsBuf(&pstDsBufCheck);

    return ret;
}

// 全表扫
int32_t V1LsQueryData5(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t expTblRecCnt,
    char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_BUF_STRU pstBuf;
    ret = V1LsMallocBufEx(&pstBuf, tblInfo->recLen, tblInfo->recMax, tblInfo->recMax * tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocBufEx failed");

    // 申请数据buff
    DB_BUF_STRU pstBufCheck;
    ret = V1LsMallocBufEx(&pstBufCheck, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocBufEx failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 0;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_SelectAllRecEx(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    } else {
        ret = DB_SelectAllRecEx(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    }
    if (expTblRecCnt == 0) {
        if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
            V1LsFreeBufEx(&pstBuf);
            V1LsFreeBufEx(&pstBufCheck);
        }
        V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_RECNOTEXIST, ret, "exp SelectAllRecEx ret failed");
        ret = DB_SUCCESS_V1;
    } else {
        if (ret != DB_SUCCESS_V1) {
            V1LsFreeBufEx(&pstBuf);
            V1LsFreeBufEx(&pstBufCheck);
        }
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp SelectAllRecEx ret failed");
        if (pstBuf.ulRecNum != expTblRecCnt) {
            V1LsFreeBufEx(&pstBuf);
            V1LsFreeBufEx(&pstBufCheck);
        }
        V1LS_FUNC_RETURN_FAIL_IF_NE(expTblRecCnt, pstBuf.ulRecNum, "exp pstBuf.usRecNum failed");
        // 校验数据
        uint32_t preKeyVal = 0;
        for (uint32_t i = 0; i < pstBuf.ulRecNum; i++) {
            // 拿主键来做校验
            preKeyVal = *(uint32_t *)((uint8_t *)pstBuf.pBuf + i * tblInfo->recLen);
            // 检验数据
            V1LsSetRecBufEx(tblInfo, &pstBufCheck, preKeyVal, preKeyVal * tblRecInfo[preKeyVal].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(tblInfo, (uint8_t *)pstBuf.pBuf + i * tblInfo->recLen, pstBufCheck.pBuf,
                tblInfo->recLen);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeBufEx(&pstBuf);
                V1LsFreeBufEx(&pstBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed");
            // 这个数据跟用户预期的存在状态一致
            V1LS_EXPECT_TRUE(tblRecInfo[preKeyVal].isExist, "check data status failed, fieldVal:%u", preKeyVal);
        }
    }
    V1LsFreeBufEx(&pstBuf);
    V1LsFreeBufEx(&pstBufCheck);

    return ret;
}

// 自定义数据类型任意条件匹配所有数据
int32_t V1LsQueryData6(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t expTblRecCnt,
    char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_BUF_STRU pstBuf;
    ret = V1LsMallocBufEx(&pstBuf, tblInfo->recLen, tblInfo->recMax, tblInfo->recMax * tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 申请数据buff
    DB_BUF_STRU pstBufCheck;
    ret = V1LsMallocBufEx(&pstBufCheck, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    uint32_t conVal = V1LsGetRandomRange(0, tblInfo->recMax + 10000);
    V1LsSetCustomFldCondVal(&(pstCond.aCond[0]), F27, DB_OP_LESS, conVal);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_SelectAllRecEx(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    } else {
        ret = DB_SelectAllRecEx(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    }
    if (expTblRecCnt == 0) {
        if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
            V1LsFreeBufEx(&pstBuf);
            V1LsFreeBufEx(&pstBufCheck);
        }
        V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_RECNOTEXIST, ret, "exp TPC_SelectAllRecEx ret failed");
        ret = DB_SUCCESS_V1;
    } else {
        if (ret != DB_SUCCESS_V1) {
            V1LsFreeBufEx(&pstBuf);
            V1LsFreeBufEx(&pstBufCheck);
        }
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp SelectAllRecEx ret failed");
        if (pstBuf.ulRecNum != expTblRecCnt) {
            V1LsFreeBufEx(&pstBuf);
            V1LsFreeBufEx(&pstBufCheck);
        }
        V1LS_FUNC_RETURN_FAIL_IF_NE(expTblRecCnt, pstBuf.ulRecNum, "exp pstBuf.usRecNum failed");
        // 校验数据
        uint32_t preKeyVal = 0;
        for (uint32_t i = 0; i < pstBuf.ulRecNum; i++) {
            // 拿主键来做校验
            preKeyVal = *(uint32_t *)((uint8_t *)pstBuf.pBuf + i * tblInfo->recLen);
            // 检验数据
            V1LsSetRecBufEx(tblInfo, &pstBufCheck, preKeyVal, preKeyVal * tblRecInfo[preKeyVal].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(tblInfo, (uint8_t *)pstBuf.pBuf + i * tblInfo->recLen, pstBufCheck.pBuf,
                tblInfo->recLen);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeBufEx(&pstBuf);
                V1LsFreeBufEx(&pstBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed");
            // 这个数据跟用户预期的存在状态一致
            V1LS_EXPECT_TRUE(tblRecInfo[preKeyVal].isExist, "check data status failed, fieldVal:%u", preKeyVal);
        }
    }
    V1LsFreeBufEx(&pstBuf);
    V1LsFreeBufEx(&pstBufCheck);

    return ret;
}

// 自定义数据类型任意条件匹配0条数据
int32_t V1LsQueryData7(uint32_t cdbId, V1LsTblInfoT *tblInfo, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_BUF_STRU pstBuf;
    ret = V1LsMallocBufEx(&pstBuf, tblInfo->recLen, 1, 1 * tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    uint32_t conVal = V1LsGetRandomRange(0, 100000);
    V1LsSetCustomFldCondVal(&(pstCond.aCond[0]), F28, DB_OP_LARGER, conVal);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_SelectAllRecEx(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    } else {
        ret = DB_SelectAllRecEx(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    }
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        V1LsFreeBufEx(&pstBuf);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_RECNOTEXIST, ret, "exp SelectAllRecEx ret failed");
    ret = DB_SUCCESS_V1;

    V1LsFreeBufEx(&pstBuf);

    return ret;
}

// 自定义数据类型正常匹配，1个自定义字段
int32_t V1LsQueryData8(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t recStart,
    uint32_t recEnd, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 申请数据buff
    DB_DSBUF_STRU pstDsBufCheck;
    ret = V1LsMallocDsBuf(&pstDsBufCheck, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    VOS_UINT32 condVal;
    for (uint32_t i = recStart; i < recEnd; i++) {
        condVal = i;
        V1LsSetCustomFldCondVal(&(pstCond.aCond[0]), F26, DB_OP_EQUAL, condVal);

        pstDsBuf.usRecNum = 2;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_SelectAllRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        } else {
            ret = DB_SelectAllRec(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        }
        if (tblRecInfo[i].isExist) {
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp SelectAllRec ret failed, condVal:%u", condVal);
            if (pstDsBuf.usRecNum != 1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_FAIL_IF_NE(1, pstDsBuf.usRecNum, "exp pstDsBuf.usRecNum failed");
            // 校验数据
            V1LsSetRecBuf(tblInfo, &pstDsBufCheck, condVal, condVal * tblRecInfo[i].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(tblInfo, pstDsBuf.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, tblInfo->recLen);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed");
        } else {
            if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_RECNOTEXIST, ret, "exp SelectAllRec ret failed");
            ret = DB_SUCCESS_V1;
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
    V1LsFreeDsBuf(&pstDsBufCheck);

    return ret;
}

// 自定义数据类型正常匹配，2个自定义字段
int32_t V1LsQueryData9(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t recStart,
    uint32_t recEnd, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 申请数据buff
    DB_DSBUF_STRU pstDsBufCheck;
    ret = V1LsMallocDsBuf(&pstDsBufCheck, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 2;
    V1LsSetCustomFldCondVal(&(pstCond.aCond[0]), F26, DB_OP_LARGEREQUAL, recStart);
    V1LsSetCustomFldCondVal(&(pstCond.aCond[1]), F27, DB_OP_LESSEQUAL, recEnd);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    DB_SELHANDLE selectHdl;
    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_BeginSelect(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &selectHdl);
    } else {
        ret = DB_BeginSelect(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &selectHdl);
    }
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeDsBuf(&pstDsBuf);
        V1LsFreeDsBuf(&pstDsBufCheck);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp BeginSelect ret failed");

    for (uint32_t i = recStart; i < recEnd; i++) {
        if (tblRecInfo[i].isExist) {
            // 获取数据
            pstDsBuf.usRecNum = 1;
            if (tblInfo->dbType == DB_TYPE_TPC) {
                ret = TPC_FetchSelectRec(cdbId, selectHdl, &pstDsBuf);
            } else {
                ret = DB_FetchSelectRec(selectHdl, &pstDsBuf);
            }
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp FetchSelectRec ret failed, i:%u", i);
            if (pstDsBuf.usRecNum != 1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_FAIL_IF_NE(1, pstDsBuf.usRecNum, "exp pstDsBuf.usRecNum failed");
            // 校验数据
            V1LsSetRecBuf(tblInfo, &pstDsBufCheck, i, i * tblRecInfo[i].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(tblInfo, pstDsBuf.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, tblInfo->recLen);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed");
        }
    }
    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TPC_EndSelect(cdbId, selectHdl);
    } else {
        ret = DB_EndSelect(selectHdl);
    }
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeDsBuf(&pstDsBuf);
        V1LsFreeDsBuf(&pstDsBufCheck);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp EndSelect ret failed");

    V1LsFreeDsBuf(&pstDsBuf);
    V1LsFreeDsBuf(&pstDsBufCheck);

    return ret;
}

// 字段长度超过256字节的查询方式
int32_t V1LsQueryData10(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo, uint32_t recStart,
    uint32_t recEnd, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 申请数据buff
    DB_DSBUF_STRU pstDsBufCheck;
    ret = V1LsMallocDsBuf(&pstDsBufCheck, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 2;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    VOS_UINT32 condVal;
    for (uint32_t i = recStart; i < recEnd; i++) {
        condVal = i;
        V1LsSetMibStrCondVal(&(pstCond.aCond[0]), F24, DB_OP_EQUAL, tblInfo->fldStoreLen[F24], condVal, buff[0]);
        V1LsSetStringCondVal(&(pstCond.aCond[1]), F29, DB_OP_EQUAL, tblInfo->fldStoreLen[F29], condVal, buff[1]);

        pstDsBuf.usRecNum = 2;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_SelectAllRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        } else {
            ret = DB_SelectAllRec(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        }
        if (tblRecInfo[i].isExist) {
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp SelectAllRec ret failed, condVal:%u", condVal);
            if (pstDsBuf.usRecNum != 1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_FAIL_IF_NE(1, pstDsBuf.usRecNum, "exp pstDsBuf.usRecNum failed");
            // 校验数据
            V1LsSetRecBuf(tblInfo, &pstDsBufCheck, condVal, condVal * tblRecInfo[i].fieldValInfo.relation);
            ret = V1LsCompAllFieldRec(tblInfo, pstDsBuf.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, tblInfo->recLen);
            if (ret != DB_SUCCESS_V1) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed");
        } else {
            if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
                V1LsFreeDsBuf(&pstDsBuf);
                V1LsFreeDsBuf(&pstDsBufCheck);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_RECNOTEXIST, ret, "exp SelectAllRec ret failed");
            ret = DB_SUCCESS_V1;
        }
    }

    V1LsFreeDsBuf(&pstDsBuf);
    V1LsFreeDsBuf(&pstDsBufCheck);

    return ret;
}

// 对于OPT_QUERY_5和OPT_QUERY_6需要提供正确的expTblRecCnt
int32_t V1LsQueryData(V1LsQueryOptE queryType, uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT *tblRecInfo,
    uint32_t recStart, uint32_t recEnd, uint32_t expTblRecCnt = 0)
{
    int32_t ret = 0;

    // 提前申请一块内存，避免频繁的申请内存，需要超过最长字段的长度
    uint32_t buffSize = 3;
    char **buff = (char **)V1LsGetMem(sizeof(char *) * buffSize);
    V1LS_FUNC_RETURN_FAIL_IF_NULL(buff);
    for (uint32_t i = 0; i < buffSize; i++) {
        buff[i] = (char *)V1LsGetMem(V1LsGetTblMaxFldLen(tblInfo) + 1);
        V1LS_FUNC_RETURN_FAIL_IF_NULL(buff[i]);
    }

    V1LsQueryOptE queryTypeTmp = queryType;
    if (queryTypeTmp == OPT_QUERY_RAN) {
        queryTypeTmp = (V1LsQueryOptE)V1LsGetRandomRange(OPT_QUERY_1, OPT_QUERY_INVALID - 1);
    }
    switch (queryTypeTmp) {
        // 4条件，3索引 + 1非索引，索引字段按schema顺序，TPC_SelectAllRec接口
        case OPT_QUERY_1: ret = V1LsQueryData1(cdbId, tblInfo, tblRecInfo, recStart, recEnd, buff); break;
        // 5条件，4索引 + 1非索引，索引字段按schema顺序，结果字段按schema顺序，TPC_SelectAllRecByOrderEx接口
        case OPT_QUERY_2: ret = V1LsQueryData2(cdbId, tblInfo, tblRecInfo, recStart, recEnd, buff); break;
        // 4条件，3索引 + 1非索引，条件的字段按schema乱序，TPC_BeginSelect接口
        case OPT_QUERY_3: ret = V1LsQueryData3(cdbId, tblInfo, tblRecInfo, recStart, recEnd, buff); break;
        // 2条件，非索引字段，条件的字段按schema乱序，结果字段(2个字段)按schema乱序，TPC_BeginIdxSelectByOrder接口
        case OPT_QUERY_4: ret = V1LsQueryData4(cdbId, tblInfo, tblRecInfo, recStart, recEnd, buff); break;
        // 全表扫
        case OPT_QUERY_5: ret = V1LsQueryData5(cdbId, tblInfo, tblRecInfo, expTblRecCnt, buff); break;
        // 自定义数据类型任意条件匹配所有数据
        case OPT_QUERY_6: ret = V1LsQueryData6(cdbId, tblInfo, tblRecInfo, expTblRecCnt, buff); break;
        // 自定义数据类型任意条件匹配0条数据
        case OPT_QUERY_7: ret = V1LsQueryData7(cdbId, tblInfo, buff); break;
        // 自定义数据类型正常匹配，1个字段
        case OPT_QUERY_8: ret = V1LsQueryData8(cdbId, tblInfo, tblRecInfo, recStart, recEnd, buff); break;
        // 自定义数据类型正常匹配，2个字段
        case OPT_QUERY_9: ret = V1LsQueryData9(cdbId, tblInfo, tblRecInfo, recStart, recEnd, buff); break;
        // 超所256字节的查询，有超过256就是超过156的方式，不超过256就是正常的方式
        case OPT_QUERY_10: ret = V1LsQueryData10(cdbId, tblInfo, tblRecInfo, recStart, recEnd, buff); break;
        default: ret = DB_SUCCESS_V1; break;
    }
    if (ret != DB_SUCCESS_V1) {
        V1LS_LOG(V1LS_LOG_INFO, "dbId:%u, dbType:%u, tblName:%s, tblId:%u", tblInfo->dbId, tblInfo->dbType,
            tblInfo->tblName, tblInfo->tblId);
    }

    for (uint32_t i = 0; i < buffSize; i++) {
        V1LsFreeMem(buff[i]);
    }
    V1LsFreeMem(buff);

    return ret;
}

// 随机的增删随机表
void V1LsCreateOrDelRanTbl(V1LsTblInfoT *tblInfo, V1LsRanTblOptE *optTypeOut, uint32_t *optRanTblIdxOut,
    uint32_t *optRanTblCntInOut, int32_t *optRanTblStatus)
{
    uint32_t optType = V1LsGetRandomRange(TBL_OPT_CREATE, TBL_OPT_DROP);
    *optTypeOut = (V1LsRanTblOptE)optType;
    uint32_t optRanTblCnt = V1LsGetRandomRange(1, *optRanTblCntInOut);
    *optRanTblCntInOut = optRanTblCnt;
    uint32_t optRanTblIdx;
    // 删表
    if (optType == TBL_OPT_DROP) {
        for (uint32_t i = 0; i < optRanTblCnt; i++) {
            optRanTblIdx = V1LsGetRandomRange(0, IEP_RAN_TBL_CNT_MAX - 1);
            optRanTblIdxOut[i] = optRanTblIdx;
            optRanTblStatus[i] = V1LsDropTbl(&tblInfo[optRanTblIdx]);
        }
    }
    // 建表
    if (optType == TBL_OPT_CREATE) {
        for (uint32_t i = 0; i < optRanTblCnt; i++) {
            optRanTblIdx = V1LsGetRandomRange(0, IEP_RAN_TBL_CNT_MAX - 1);
            optRanTblIdxOut[i] = optRanTblIdx;
            optRanTblStatus[i] = V1LsCreateTbl(&tblInfo[optRanTblIdx], &g_V1LsRelDef[0], g_V1LsRelFldStoreLen[0]);
        }
    }
}

int32_t V1LsMultiPthSameTblInit(V1LsTblInfoT *tblInfo)
{
    int32_t ret;

    // 新建DB
    DB_INST_CONFIG_STRU dbCfg = { 0 };
    VOS_UINT32 dbId = V1_LS_INVALID_DB_ID;
    if (tblInfo->dbType == DB_TYPE_TPC) {
        (void)sprintf_s(tblInfo->dbName, sizeof(tblInfo->dbName), "TpcOptSameTbl");
        ret = TPC_CreateDB((VOS_UINT8 *)tblInfo->dbName, NULL, &dbCfg);
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_CreateDB failed");
        ret = TPC_OpenDB(NULL, (VOS_UINT8 *)tblInfo->dbName, &dbId);
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_OpenDB failed");
    } else {
        (void)sprintf_s(tblInfo->dbName, sizeof(tblInfo->dbName), "DbOptSameTbl");
        ret = DB_CreateDB((VOS_UINT8 *)tblInfo->dbName, NULL, &dbCfg);
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_CreateDB failed");
        ret = DB_OpenDB(NULL, (VOS_UINT8 *)tblInfo->dbName, &dbId);
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_OpenDB failed");
    }

    // 建表
    tblInfo->dbId = dbId;
    tblInfo->tblType = TBL_TYPE_NORMAL;
    tblInfo->recInit = PTH_SAME_TBL_REC_INIT;
    tblInfo->recMax = PTH_SAME_TBL_REC_MAX;
    memset_s(tblInfo->tblName, sizeof(tblInfo->tblName), 0x00, sizeof(tblInfo->tblName));
    (void)sprintf_s(tblInfo->tblName, sizeof(tblInfo->tblName), "V1LS_PthSameTbl");
    ret = V1LsCreateTbl(tblInfo, &g_V1LsRelDef[0], g_V1LsRelFldStoreLen[0]);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCreateTbl failed");

    // 写数据
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");
    for (VOS_UINT32 i = 0; i < tblInfo->recInit; i++) {
        V1LsSetRecBuf(tblInfo, &pstDsBuf, i, i * FIELD_VAL_RELATION_1);
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, tblInfo->dbId, tblInfo->tblId, &pstDsBuf);
        } else {
            ret = DB_InsertRec(tblInfo->dbId, tblInfo->tblId, &pstDsBuf);
        }
        if (ret != DB_SUCCESS_V1) {
            V1LsFreeDsBuf(&pstDsBuf);
        }
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "dbType: %u, %s InsertRec failed, i:%u",
            tblInfo->dbType, tblInfo->tblName, i);
    }
    V1LsFreeDsBuf(&pstDsBuf);

    return ret;
}

void V1LsMultiPthSameTblDestory(V1LsTblInfoT *tblInfo)
{
    // 删表
    V1LsDropTbl(tblInfo);
    // 关闭并删除DB
    if (tblInfo->dbType == DB_TYPE_TPC) {
        TPC_CloseDB(tblInfo->dbId);
        TPC_DropDB((VOS_UINT8 *)tblInfo->dbName, TPC_NOWAIT);
    } else {
        DB_CloseDB(tblInfo->dbId);
        DB_DropDB((VOS_UINT8 *)tblInfo->dbName, TPC_NOWAIT);
    }
}

// 随机插入随机数据量的数据
void V1LsRanDmlInsertNotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LstblRecOptRangeInfoT *tblOptRecRangeInfo,
    uint32_t *succCnt, char **buff)
{
    int32_t ret;
    uint32_t succCntTmp = 0;

    // 随机数种子
    time_t t;
    (void)time(&t);
    (void)srand((VOS_UINT32)t);

    // 要操作多少条数据，限制在1000条
    uint32_t optRecCntTmp = V1LsGetRandomRange(1, tblOptRecRangeInfo->recOptCntMax);

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    uint32_t fieldVal = 0;
    for (uint32_t i = 0; i < optRecCntTmp; i++) {
        // 生成随机的要操作的记录
        fieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        V1LsSetRecBuf(tblInfo, &pstDsBuf, fieldVal, fieldVal * FIELD_VAL_RELATION_1);

        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_InsertRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstDsBuf);
        } else {
            ret = DB_InsertRec(tblInfo->dbId, tblInfo->tblId, &pstDsBuf);
        }
        if (ret == DB_SUCCESS_V1) {
            succCntTmp++;
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
    *succCnt = succCntTmp;
}

// 随机更新随机数据量的数据
void V1LsRanDmlUpdateNotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LstblRecOptRangeInfoT *tblOptRecRangeInfo,
    uint32_t *succCnt, char **buff)
{
    int32_t ret;
    uint32_t succCntTmp = 0;

    // 随机数种子
    time_t t;
    (void)time(&t);
    (void)srand((VOS_UINT32)t);

    // 要操作多少条数据，限制在1000条
    uint32_t optRecCntTmp = V1LsGetRandomRange(1, tblOptRecRangeInfo->recOptCntMax);

    // 设置条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    pstCond.aCond->ucFieldId = V1LsGetRangeDmlCondField();
    pstCond.aCond->enOp = DB_OP_EQUAL;

    // 更新所有字段
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter);

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    uint32_t fieldVal = 0;
    VOS_UINT32 pulRecNum;
    for (uint32_t i = 0; i < optRecCntTmp; i++) {
        // 旧值作为条件
        fieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        g_v1LsTblField[pstCond.aCond->ucFieldId].setCond(pstCond.aCond->aucValue,
            tblInfo->fldStoreLen[pstCond.aCond->ucFieldId], fieldVal, buff[0]);
        V1LsSetRecBuf(tblInfo, &pstDsBuf, fieldVal, fieldVal * FIELD_VAL_RELATION_2);

        pulRecNum = 0;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_UpdateRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
        } else {
            ret = DB_UpdateRec(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
        }
        if (ret == DB_SUCCESS_V1 && pulRecNum != 0) {
            succCntTmp++;
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
    *succCnt = succCntTmp;
}

// 随机更新随机数据量的数据，更新主键
void V1LsRanDmlUpdateIdxNotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LstblRecOptRangeInfoT *tblOptRecRangeInfo,
    uint32_t *succCnt, char **buff)
{
    int32_t ret;
    uint32_t succCntTmp = 0;

    // 随机数种子
    time_t t;
    (void)time(&t);
    (void)srand((VOS_UINT32)t);

    // 要操作多少条数据，限制在1000条
    uint32_t optRecCntTmp = V1LsGetRandomRange(1, tblOptRecRangeInfo->recOptCntMax);

    // 设置条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    pstCond.aCond->ucFieldId = V1LsGetRangeDmlCondField();
    pstCond.aCond->enOp = DB_OP_EQUAL;

    // 更新所有字段
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter);

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, 1, tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    uint32_t oldFieldVal = 0;
    uint32_t newFieldVal = 0;
    VOS_UINT32 pulRecNum;
    for (uint32_t i = 0; i < optRecCntTmp; i++) {
        // 旧直作为条件
        oldFieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        g_v1LsTblField[pstCond.aCond->ucFieldId].setCond(pstCond.aCond->aucValue,
            tblInfo->fldStoreLen[pstCond.aCond->ucFieldId], oldFieldVal, buff[0]);
        // 新值作为更新值
        newFieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        V1LsSetRecBuf(tblInfo, &pstDsBuf, newFieldVal, newFieldVal * FIELD_VAL_RELATION_2);

        pulRecNum = 0;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_UpdateRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
        } else {
            ret = DB_UpdateRec(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
        }
        if (ret == DB_SUCCESS_V1 && pulRecNum != 0) {
            succCntTmp++;
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
    *succCnt = succCntTmp;
}

// 随机删除随机数据量的数据
void V1LsRanDmlDeleteNotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LstblRecOptRangeInfoT *tblOptRecRangeInfo,
    uint32_t *succCnt, char **buff)
{
    int32_t ret;
    uint32_t succCntTmp = 0;

    // 随机数种子
    time_t t;
    (void)time(&t);
    (void)srand((VOS_UINT32)t);

    // 要操作多少条数据，限制在1000条
    uint32_t optRecCntTmp = V1LsGetRandomRange(1, tblOptRecRangeInfo->recOptCntMax);

    // 设置条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    pstCond.aCond->ucFieldId = V1LsGetRangeDmlCondField();
    pstCond.aCond->enOp = DB_OP_EQUAL;

    uint32_t fieldVal = 0;
    VOS_UINT32 pulRecNum = 0;
    for (uint32_t i = 0; i < optRecCntTmp; i++) {
        fieldVal = V1LsGetRandomRange(tblOptRecRangeInfo->recStart, tblOptRecRangeInfo->recEnd);
        g_v1LsTblField[pstCond.aCond->ucFieldId].setCond(pstCond.aCond->aucValue,
            tblInfo->fldStoreLen[pstCond.aCond->ucFieldId], fieldVal, buff[0]);

        pulRecNum = 0;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_DeleteRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pulRecNum);
        } else {
            ret = DB_DeleteRec(tblInfo->dbId, tblInfo->tblId, &pstCond, &pulRecNum);
        }
        if (ret == DB_SUCCESS_V1 && pulRecNum != 0) {
            succCntTmp++;
        }
    }
    *succCnt = succCntTmp;
}

void V1LsRanDmlOptNotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, V1LstblRecOptRangeInfoT *tblOptRecRangeInfo,
    uint32_t *succCnt)
{
    // 提前申请一块内存，避免频繁的申请内存，需要超过最长字段的长度
    uint32_t buffSize = 3;
    char **buff = (char **)V1LsGetMem(sizeof(char *) * buffSize);
    V1LS_FUNC_RETURN_NONE_IF_NULL(buff);
    for (uint32_t i = 0; i < buffSize; i++) {
        buff[i] = (char *)V1LsGetMem(V1LsGetTblMaxFldLen(tblInfo) + 1);
        V1LS_FUNC_RETURN_NONE_IF_NULL(buff[i]);
    }

    V1LsRanDmlOptE ranDmlOpt = (V1LsRanDmlOptE)V1LsGetRandomRange((uint32_t)OPT_INSERT, (uint32_t)OPT_DELETE);
    switch (ranDmlOpt) {
        // 随机插入随机数量的数据
        case OPT_INSERT:
            V1LsRanDmlInsertNotExp(cdbId, tblInfo, tblOptRecRangeInfo, succCnt, buff);
            break;
        // 随机更新随机数量的数据
        case OPT_UPDATE:
            V1LsRanDmlUpdateNotExp(cdbId, tblInfo, tblOptRecRangeInfo, succCnt, buff);
            break;
        // 随机更新随机数量的数据，会更新索引字段
        case OPT_UPDATE_IDX:
            V1LsRanDmlUpdateIdxNotExp(cdbId, tblInfo, tblOptRecRangeInfo, succCnt, buff);
            break;
        // 随机删除随机数量的数据
        case OPT_DELETE:
            V1LsRanDmlDeleteNotExp(cdbId, tblInfo, tblOptRecRangeInfo, succCnt, buff);
            break;
        default: break;
    }

    for (uint32_t i = 0; i < buffSize; i++) {
        V1LsFreeMem(buff[i]);
    }
    V1LsFreeMem(buff);
}

// 4条件，3索引 + 1非索引，索引字段按schema顺序，TPC_SelectAllRec接口
void V1LsQueryData1NotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, uint32_t recStart, uint32_t recEnd,
    uint32_t maxRecCnt, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, maxRecCnt, maxRecCnt * tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 4;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    VOS_UINT32 condVal;
    for (uint32_t i = recStart; i < recEnd; i++) {
        condVal = i;
        V1LsSetUint32CondVal(&(pstCond.aCond[0]), F0, DB_OP_EQUAL, condVal);
        V1LsSetUint64CondVal(&(pstCond.aCond[1]), F7, DB_OP_LESSEQUAL, condVal);
        V1LsSetStringCondValLikeOneData(
            &(pstCond.aCond[2]), F11, DB_OP_LIKE, tblInfo->fldStoreLen[F11], condVal, buff[0]);
        V1LsSetBcdCondVal(&(pstCond.aCond[3]), F25, DB_OP_HAVEPREFIX, tblInfo->fldStoreLen[F25], condVal, buff[1]);

        pstDsBuf.usRecNum = 2;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            TPC_SelectAllRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        } else {
            DB_SelectAllRec(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
}

// 2条件，非索引字段，条件的字段按schema乱序，结果字段(2个字段)按schema乱序，TPC_BeginIdxSelectByOrder接口
void V1LsQueryData4NotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, uint32_t recStart, uint32_t recEnd,
    uint32_t maxRecCnt, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, sizeof(uint32_t) + sizeof(int64_t), maxRecCnt, maxRecCnt * tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 2;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    VOS_UINT8 fieldId[2] = { F6, F1 };
    V1LsSetFldFilter(&pstFldFilter, sizeof(fieldId), fieldId);

    // 结果排序
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    VOS_UINT32 condVal;
    DB_SELHANDLE selectHdl;
    for (uint32_t i = recStart; i < recEnd; i++) {
        condVal = i;
        V1LsSetUint32CondVal(&(pstCond.aCond[1]), F1, DB_OP_EQUAL, condVal);
        V1LsSetUint64CondVal(&(pstCond.aCond[0]), F6, DB_OP_LESSEQUAL, condVal);

        if (tblInfo->dbType == DB_TYPE_TPC) {
            ret = TPC_BeginIdxSelectByOrder(
                cdbId, tblInfo->dbId, tblInfo->tblId, &pstSort, &pstCond, &pstFldFilter, &selectHdl);
        } else {
            ret = DB_BeginIdxSelectByOrder(
                tblInfo->dbId, tblInfo->tblId, &pstSort, &pstCond, &pstFldFilter, &selectHdl);
        }
        if (ret != DB_SUCCESS_V1) {
            V1LsFreeDsBuf(&pstDsBuf);
            return;
        }
        // 获取数据
        TPC_FetchSelectRec(cdbId, selectHdl, &pstDsBuf);
        TPC_EndSelect(cdbId, selectHdl);
    }
    V1LsFreeDsBuf(&pstDsBuf);
}

// 全表扫，不校验返回值
void V1LsQueryData5NotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, uint32_t maxRecCnt, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_BUF_STRU pstBuf;
    ret = V1LsMallocBufEx(&pstBuf, tblInfo->recLen, maxRecCnt, maxRecCnt * tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocBufEx failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 0;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    if (tblInfo->dbType == DB_TYPE_TPC) {
        TPC_SelectAllRecEx(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    } else {
        DB_SelectAllRecEx(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    }

    // 释放内存
    V1LsFreeBufEx(&pstBuf);
}

// 自定义数据类型任意条件匹配所有数据
void V1LsQueryData6NotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, uint32_t maxRecCnt, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_BUF_STRU pstBuf;
    ret = V1LsMallocBufEx(&pstBuf, tblInfo->recLen, tblInfo->recMax, tblInfo->recMax * tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    uint32_t conVal = V1LsGetRandomRange(0, 100000);
    V1LsSetCustomFldCondVal(&(pstCond.aCond[0]), F27, DB_OP_LESS, conVal);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    if (tblInfo->dbType == DB_TYPE_TPC) {
        TPC_SelectAllRecEx(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    } else {
        DB_SelectAllRecEx(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    }
    V1LsFreeBufEx(&pstBuf);
}

// 自定义数据类型任意条件匹配0条数据
void V1LsQueryData7NotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, uint32_t maxRecCnt, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_BUF_STRU pstBuf;
    ret = V1LsMallocBufEx(&pstBuf, tblInfo->recLen, tblInfo->recMax, tblInfo->recMax * tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;
    uint32_t conVal = V1LsGetRandomRange(0, 100000);
    V1LsSetCustomFldCondVal(&(pstCond.aCond[0]), F28, DB_OP_LARGER, conVal);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    if (tblInfo->dbType == DB_TYPE_TPC) {
        TPC_SelectAllRecEx(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    } else {
        DB_SelectAllRecEx(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstBuf);
    }
    V1LsFreeBufEx(&pstBuf);
}

// 自定义数据类型正常匹配，1个自定义字段
void V1LsQueryData8NotExp(uint32_t cdbId, V1LsTblInfoT *tblInfo, uint32_t recStart, uint32_t recEnd,
    uint32_t maxRecCnt, char **buff)
{
    int32_t ret;

    // 申请数据buff
    DB_DSBUF_STRU pstDsBuf;
    ret = V1LsMallocDsBuf(&pstDsBuf, tblInfo->recLen, tblInfo->recMax, tblInfo->recMax * tblInfo->recLen);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

    // 设置查询条件
    DB_COND_STRU pstCond = { 0 };
    pstCond.usCondNum = 1;

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    V1LsSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    VOS_UINT32 condVal;
    for (uint32_t i = recStart; i < recEnd; i++) {
        condVal = i;
        V1LsSetCustomFldCondVal(&(pstCond.aCond[0]), F26, DB_OP_EQUAL, condVal);
        pstDsBuf.usRecNum = 2;
        if (tblInfo->dbType == DB_TYPE_TPC) {
            TPC_SelectAllRec(cdbId, tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        } else {
            DB_SelectAllRec(tblInfo->dbId, tblInfo->tblId, &pstCond, &pstFldFilter, &pstDsBuf);
        }
    }
    V1LsFreeDsBuf(&pstDsBuf);
}

// 对于OPT_QUERY_5和OPT_QUERY_6需要提供正确的maxRecCnt
int32_t V1LsQueryDataNotExp(V1LsQueryOptE queryType, uint32_t cdbId, V1LsTblInfoT *tblInfo, uint32_t recStart,
    uint32_t recEnd, uint32_t maxRecCnt = 0)
{
    int32_t ret = 0;

    // 提前申请一块内存，避免频繁的申请内存，需要超过最长字段的长度
    uint32_t buffSize = 3;
    char **buff = (char **)V1LsGetMem(sizeof(char *) * buffSize);
    V1LS_FUNC_RETURN_FAIL_IF_NULL(buff);
    for (uint32_t i = 0; i < buffSize; i++) {
        buff[i] = (char *)V1LsGetMem(V1LsGetTblMaxFldLen(tblInfo) + 1);
        V1LS_FUNC_RETURN_FAIL_IF_NULL(buff[i]);
    }

    V1LsQueryOptE queryTypeTmp = queryType;
    V1LsQueryOptE supportQueryType[6] = { OPT_QUERY_1, OPT_QUERY_4, OPT_QUERY_5, OPT_QUERY_6, OPT_QUERY_7, OPT_QUERY_8};
    if (queryTypeTmp == OPT_QUERY_RAN) {
        uint32_t idx = V1LsGetRandomRange(0, sizeof(supportQueryType) / sizeof(V1LsQueryOptE) - 1);
        queryTypeTmp = supportQueryType[idx];
    }
    bool isSupport = false;
    for (uint32_t i = 0; i < sizeof(supportQueryType) / sizeof(V1LsQueryOptE); i++) {
        if (queryTypeTmp == supportQueryType[i]) {
            isSupport = true;
            break;
        }
    }
    V1LS_FUNC_RETURN_FAIL_IF_NE(true, isSupport, "V1LsQueryDataNotExp not support queryType(%u)", queryTypeTmp);
    switch (queryTypeTmp) {
        // 4条件，3索引 + 1非索引，索引字段按schema顺序，TPC_SelectAllRec接口
        case OPT_QUERY_1: V1LsQueryData1NotExp(cdbId, tblInfo, recStart, recEnd, maxRecCnt, buff); break;
        // 2条件，非索引字段，条件的字段按schema乱序，结果字段(2个字段)按schema乱序，TPC_BeginIdxSelectByOrder接口
        case OPT_QUERY_4: V1LsQueryData4NotExp(cdbId, tblInfo, recStart, recEnd, maxRecCnt, buff); break;
        // 全表扫
        case OPT_QUERY_5: V1LsQueryData5NotExp(cdbId, tblInfo, maxRecCnt, buff); break;
        // 自定义数据类型任意条件匹配所有数据
        case OPT_QUERY_6: V1LsQueryData6NotExp(cdbId, tblInfo, maxRecCnt, buff); break;
        // 自定义数据类型任意条件匹配0条数据
        case OPT_QUERY_7: V1LsQueryData7NotExp(cdbId, tblInfo, maxRecCnt, buff); break;
        // 自定义数据类型正常匹配，1个字段
        case OPT_QUERY_8: V1LsQueryData8NotExp(cdbId, tblInfo, recStart, recEnd, maxRecCnt, buff); break;
        default: ret = STATUS_FAILED; break;
    }

    for (uint32_t i = 0; i < buffSize; i++) {
        V1LsFreeMem(buff[i]);
    }
    V1LsFreeMem(buff);

    return ret;
}

void V1LsSetEdgeDef(DB_EDGE_DEF_STRU *stEdgeDef, bool isPst,
    VOS_UINT16 tblId1, VOS_UINT16 tblId2, VOS_UINT32 field1Num, VOS_UINT8 *fieldArray1,
    VOS_UINT32 field2Num, VOS_UINT8 *fieldArray2)
{
    DB_ERR_CODE ret;

    stEdgeDef->isPersistent = isPst;
    stEdgeDef->relInfos[0].relId = tblId1;
    stEdgeDef->relInfos[0].FieldNum = field1Num;
    for (VOS_UINT32 i = 0; i < field1Num; i++) {
        stEdgeDef->relInfos[0].aucField[i] = fieldArray1[i];
    }
    stEdgeDef->relInfos[1].relId = tblId2;
    stEdgeDef->relInfos[1].FieldNum = field2Num;
    for (VOS_UINT32 i = 0; i < field2Num; i++) {
        stEdgeDef->relInfos[1].aucField[i] = fieldArray2[i];
    }
}

// 建边表的场景
typedef enum {
    CREATE_EDGE_ORDER = 0,
    CREATE_EDGE_LOOP,
    CREATE_EDGE_TREE
} V1LS_CREATE_EDGE_SCENE_E;

// 顺序边表
void V1LsCreateEdgeTblByOrder(uint32_t dbId, uint32_t *tblIdArray, uint32_t arrayNum,
    char (*edgeTblName)[DB_REL_NAME_LEN], uint32_t *createEdgeCnt, int32_t *status)
{
    int32_t ret = DB_SUCCESS_V1;

    DB_EDGE_DEF_STRU edgeDef;
    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };

    // 根据随机出来的顺序建边表
    uint32_t createEdgeCntTmp = 0;
    for (uint32_t i = 0; i < arrayNum - 1; i++) {
        V1LsSetEdgeDef(&edgeDef, true, tblIdArray[i], tblIdArray[i + 1], fieldNum, fieldArray1, fieldNum, fieldArray2);
        memset_s(edgeTblName[createEdgeCntTmp], DB_REL_NAME_LEN, 0x00, DB_REL_NAME_LEN);
        (void)sprintf_s(edgeTblName[createEdgeCntTmp], DB_REL_NAME_LEN, "edge_%02u_%02u",
            tblIdArray[i] - EDGE_QUERY_TBL_ID_START, tblIdArray[i + 1] - EDGE_QUERY_TBL_ID_START);
        ret = TPC_CreateEdge(dbId, (uint8_t *)edgeTblName[createEdgeCntTmp], &edgeDef);
        status[createEdgeCntTmp] = ret;
        createEdgeCntTmp++;
    }
    *createEdgeCnt = createEdgeCntTmp;
}

// 环形边表
void V1LsCreateEdgeTblByLoop(uint32_t dbId, uint32_t *tblIdArray, uint32_t arrayNum,
    char (*edgeTblName)[DB_REL_NAME_LEN], uint32_t *createEdgeCnt, int32_t *status)
{
    int32_t ret = DB_SUCCESS_V1;

    DB_EDGE_DEF_STRU edgeDef;
    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };

    // 复制一份列表，避免直接在上面修改
    uint32_t tblIdArrayTmp[arrayNum];
    for (uint32_t i = 0; i < arrayNum; i++) {
        tblIdArrayTmp[i] = tblIdArray[i];
    }

    // 放掉最后一个随机数，把最后一个随机数设置为第一个随机数
    tblIdArrayTmp[arrayNum - 1] = tblIdArrayTmp[0];

    // 根据随机出来的顺序建边表
    uint32_t createEdgeCntTmp = 0;
    for (uint32_t i = 0; i < arrayNum - 1; i++) {
        V1LsSetEdgeDef(&edgeDef, true, tblIdArrayTmp[i], tblIdArrayTmp[i + 1], fieldNum, fieldArray1,
            fieldNum, fieldArray2);
        memset_s(edgeTblName[createEdgeCntTmp], DB_REL_NAME_LEN, 0x00, DB_REL_NAME_LEN);
        (void)sprintf_s(edgeTblName[createEdgeCntTmp], DB_REL_NAME_LEN, "edge_%02u_%02u",
            tblIdArrayTmp[i] - EDGE_QUERY_TBL_ID_START, tblIdArrayTmp[i + 1] - EDGE_QUERY_TBL_ID_START);
        ret = TPC_CreateEdge(dbId, (uint8_t *)edgeTblName[createEdgeCntTmp], &edgeDef);
        status[createEdgeCntTmp] = ret;
        createEdgeCntTmp++;
    }
    *createEdgeCnt = createEdgeCntTmp;
}

// 排好的编号
// 当前只进行最多8条路径的查询，每条路径最长为4，如果后续有更大的需求，需要自己填
#define TREE_PATH_CNT_MAX 8
#define TREE_PATH_LEN_MAX 4
uint32_t g_treeNodePath[TREE_PATH_CNT_MAX][TREE_PATH_LEN_MAX] = {
    { 1, 2, 4, 8 }, { 1, 3, 6, 12 }, { 1, 2, 5, 10 }, { 1, 3, 7, 14 },
    { 1, 2, 4, 9 }, { 1, 2, 5, 11 }, { 1, 3, 6, 13 }, { 1, 3, 7, 15 }
};

// 树形边表
void V1LsCreateEdgeTblByTree(uint32_t dbId, uint32_t *tblIdArray, uint32_t arrayNum,
    char (*edgeTblName)[DB_REL_NAME_LEN], uint32_t *createEdgeCnt, int32_t *status)
{
    int32_t ret = DB_SUCCESS_V1;

    DB_EDGE_DEF_STRU edgeDef;
    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };

    // 1棵树需要使用的表数量不能少于3个
    if (arrayNum < 3) {
        V1LS_LOG(V1LS_LOG_ERROR, "arrayNum too few, return");
        return;
    }

    // 这里的树模型
    //       1
    //     /   \
    //    2     3
    //   / \   / \
    // 4   5  6   7

    uint32_t nodeNum[10] = { 1, 2, 4, 8, 16, 32, 64, 128, 256, 512 };
    uint32_t totalNodeNum = 0;
    uint32_t pathLen = 0;
    for (uint32_t i = 0;  i < sizeof(nodeNum) / sizeof(uint32_t); i++) {
        if (totalNodeNum + nodeNum[i] > arrayNum) {
            break;
        }
        totalNodeNum += nodeNum[i];
        pathLen++;
    }
    uint32_t pathCnt = pathLen * 2;

    uint32_t createEdgeCntTmp = 0;
    for (uint32_t i = 0; i < pathCnt; i++) {
        for (uint32_t j = 0; j < pathLen - 1; j++) {
            V1LsSetEdgeDef(&edgeDef, true, tblIdArray[g_treeNodePath[i][j]], tblIdArray[g_treeNodePath[i][j + 1]],
                fieldNum, fieldArray1, fieldNum, fieldArray2);
            memset_s(edgeTblName[createEdgeCntTmp], DB_REL_NAME_LEN, 0x00, DB_REL_NAME_LEN);
            (void)sprintf_s(edgeTblName[createEdgeCntTmp], DB_REL_NAME_LEN, "edge_%02u_%02u",
                tblIdArray[g_treeNodePath[i][j]] - EDGE_QUERY_TBL_ID_START,
                tblIdArray[g_treeNodePath[i][j + 1]] - EDGE_QUERY_TBL_ID_START);
            ret = TPC_CreateEdge(dbId, (uint8_t *)edgeTblName[createEdgeCntTmp], &edgeDef);
            status[createEdgeCntTmp] = ret;
            createEdgeCntTmp++;
        }
    }
    *createEdgeCnt = createEdgeCntTmp;
}

// 建边表
void V1LsCreateEdgeTbl(uint32_t dbId, uint32_t *tblIdArray, uint32_t arrayNum, char (*edgeTblName)[DB_REL_NAME_LEN],
    V1LS_CREATE_EDGE_SCENE_E scene, uint32_t *createEdgeCnt, int32_t *status)
{
    // 根据不同的参数来构造不同的场景
    switch (scene) {
        case CREATE_EDGE_ORDER:
            // 根据随机出来的顺序建边表
            V1LsCreateEdgeTblByOrder(dbId, tblIdArray, arrayNum, edgeTblName, createEdgeCnt, status); break;
        case CREATE_EDGE_LOOP:
            // 根据随机出来的顺序建边表，尾接头，形成环状
            V1LsCreateEdgeTblByLoop(dbId, tblIdArray, arrayNum, edgeTblName, createEdgeCnt, status); break;
        case CREATE_EDGE_TREE:
            // 树杈
            V1LsCreateEdgeTblByTree(dbId, tblIdArray, arrayNum, edgeTblName, createEdgeCnt, status); break;
        default : break;
    }
}

// 删边表
void V1LsDropEdgeTbl(uint32_t dbId, char (*edgeTblName)[DB_REL_NAME_LEN], uint32_t num, int32_t *dropStatus)
{
    int32_t ret = DB_SUCCESS_V1;

    // 删除边表
    for (uint32_t i = 0; i < num; i++) {
        ret = TPC_DropEdge(dbId, (uint8_t *)edgeTblName[i]);
        dropStatus[i] = ret;
    }
}

// 设置查询路径
void V1LsSetEdgeTblSelectPath(DB_PATH_STRU *pstPath, uint32_t *tblIdArray, VOS_UINT32 edgeNum,
    DB_FIELDFILTER_STRU *pstFldFilter, uint32_t fieldNum, uint8_t *fieldArray1, uint8_t *fieldArray2)
{
    for (uint32_t i = 0; i < edgeNum; i++) {
        pstPath->edge[i].edgeInfo[0].relId = tblIdArray[i];
        pstPath->edge[i].edgeInfo[0].FieldNum = fieldNum;
        for (uint32_t j = 0; j < fieldNum; j++) {
            pstPath->edge[i].edgeInfo[0].aucField[j] = fieldArray1[j];
        }
        pstPath->edge[i].edgeInfo[1].relId = tblIdArray[i + 1];
        pstPath->edge[i].edgeInfo[1].FieldNum = fieldNum;
        for (uint32_t j = 0; j < fieldNum; j++) {
            pstPath->edge[i].edgeInfo[1].aucField[j] = fieldArray2[j];
        }
        pstPath->edge[i].pstFldFilter = pstFldFilter;
    }
    pstPath->edgeNum = edgeNum;
}

// 全路径查询
int32_t V1LsEdgeTblSelectByPath(uint32_t cdbId, uint32_t dbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT **tblRecInfo,
    uint32_t *tblIdArray, V1LS_CREATE_EDGE_SCENE_E scene)
{
    int32_t ret = DB_SUCCESS_V1;

    // 查询条件
    DB_COND_STRU cond;
    cond.usCondNum = 0;

    // 设置投影的字段
    DB_FIELDFILTER_STRU fldFilter;
    fldFilter.ucFieldNum = DB_FIELD_ALL;

    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };

    // 随机的表数，查询的路径数
    // 对于CREATE_EDGE_ORDER和CREATE_EDGE_LOOP来说，路径就是1条，顺着边查
    // 对于CREATE_EDGE_TREE来说，路径会有多条
    uint32_t pathCnt = 1;
    uint32_t edgeNum = 3;
    uint32_t treeNodePathTblId[TREE_PATH_CNT_MAX][TREE_PATH_LEN_MAX] = { 0 };
    DB_PATH_STRU pstPath[TREE_PATH_CNT_MAX];
    if (scene == CREATE_EDGE_ORDER || scene == CREATE_EDGE_LOOP) {
        pathCnt = 1;
        edgeNum = V1LsGetRandomRange(3, 9);
        V1LsSetEdgeTblSelectPath(&pstPath[0], tblIdArray, edgeNum, &fldFilter, fieldNum, fieldArray1, fieldArray2);
    } else {
        pathCnt = TREE_PATH_CNT_MAX;
        edgeNum = TREE_PATH_LEN_MAX - 1;
        // 取真实的表id
        for (uint32_t i = 0; i < TREE_PATH_CNT_MAX; i++) {
            for (uint32_t j = 0; j < TREE_PATH_LEN_MAX; j++) {
                treeNodePathTblId[i][j] = tblIdArray[g_treeNodePath[i][j]];
            }
        }
        for (uint32_t i = 0; i < pathCnt; i++) {
            V1LsSetEdgeTblSelectPath(&pstPath[i], treeNodePathTblId[i], edgeNum, &fldFilter, fieldNum,
                fieldArray1, fieldArray2);
        }
    }

    uint32_t *tblId = tblIdArray;
    for (uint32_t i = 0; i < pathCnt; i++) {
        // 顶点表
        if (scene == CREATE_EDGE_ORDER || scene == CREATE_EDGE_LOOP) {
            tblId = tblIdArray;
        } else {
            tblId = treeNodePathTblId[i];
        }

        // 查询
        DB_MUTIL_BUF_STRU *mutilDataBuff = NULL;
        ret = TPC_SelectAllRecByPath(cdbId, dbId, tblId[0], &cond, &fldFilter, &pstPath[i], &mutilDataBuff);
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp TPC_SelectAllRecByPath failed, scene(%u)", scene);

        // 校验表的数量，除非该表没数据，不存在这种情况，0这条记录是必存在的
        V1LS_FUNC_RETURN_FAIL_IF_NE(edgeNum + 1, mutilDataBuff->realRelNum, "expect mutilDataBuff->realRelNum failed");

        // 校验数据
        uint8_t *pBuf = NULL;
        DB_DSBUF_STRU pstDsBufCheck;
        uint32_t idx;
        for (uint32_t j = 0; j < mutilDataBuff->realRelNum; j++) {
            idx = tblId[j] - EDGE_QUERY_TBL_ID_START;
            ret = V1LsMallocDsBuf(&pstDsBufCheck, tblInfo[idx].recLen, 1, tblInfo[idx].recLen);
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");
            uint32_t pKVal = 0;
            pBuf = (uint8_t *)mutilDataBuff->dataBuf[j].pBuf;
            for (uint32_t k = 0; k < mutilDataBuff->dataBuf[j].ulRecNum; k++) {
                // 拿主键来做校验
                pKVal = *(uint32_t *)(pBuf + k * tblInfo[idx].recLen);
                // 检验数据
                V1LsSetRecBuf(tblInfo, &pstDsBufCheck, pKVal,
                    (pKVal * tblRecInfo[idx][pKVal].fieldValInfo.relation) % 128);
                ret = V1LsCompAllFieldRec(tblInfo, pBuf + k * tblInfo[idx].recLen, pstDsBufCheck.StdBuf.pucData,
                    tblInfo[idx].recLen);
                if (ret != DB_SUCCESS_V1) {
                    V1LsFreeDsBuf(&pstDsBufCheck);
                    TPC_FreeBufMemById(mutilDataBuff->memId);
                }
                V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed, fieldVal:%u", pKVal);
                // 这个数据跟用户预期的存在状态一致
                V1LS_EXPECT_TRUE(tblRecInfo[idx][pKVal].isExist, "check data status failed, fieldVal:%u", pKVal);
            }
            V1LsFreeDsBuf(&pstDsBufCheck);
        }
        TPC_FreeBufMemById(mutilDataBuff->memId);
    }

    return ret;
}

// 全路径查询，不做预期
void V1LsEdgeTblSelectByPathNotExp(uint32_t cdbId, uint32_t dbId, uint32_t *tblIdArray, V1LS_CREATE_EDGE_SCENE_E scene)
{
    int32_t ret = DB_SUCCESS_V1;

    // 查询条件
    DB_COND_STRU cond;
    cond.usCondNum = 0;

    // 设置投影的字段
    DB_FIELDFILTER_STRU fldFilter;
    fldFilter.ucFieldNum = DB_FIELD_ALL;

    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };

    // 随机的表数，查询的路径数
    // 对于CREATE_EDGE_ORDER和CREATE_EDGE_LOOP来说，路径就是1条，顺着边查
    // 对于CREATE_EDGE_TREE来说，路径会有多条
    uint32_t pathCnt = 1;
    uint32_t edgeNum = 3;
    uint32_t treeNodePathTblId[TREE_PATH_CNT_MAX][TREE_PATH_LEN_MAX] = { 0 };
    DB_PATH_STRU pstPath[TREE_PATH_CNT_MAX];
    if (scene == CREATE_EDGE_ORDER || scene == CREATE_EDGE_LOOP) {
        pathCnt = 1;
        edgeNum = V1LsGetRandomRange(3, 9);
        V1LsSetEdgeTblSelectPath(&pstPath[0], tblIdArray, edgeNum, &fldFilter, fieldNum, fieldArray1, fieldArray2);
    } else {
        pathCnt = TREE_PATH_CNT_MAX;
        edgeNum = TREE_PATH_LEN_MAX - 1;
        // 取真实的表id
        for (uint32_t i = 0; i < TREE_PATH_CNT_MAX; i++) {
            for (uint32_t j = 0; j < TREE_PATH_LEN_MAX; j++) {
                treeNodePathTblId[i][j] = tblIdArray[g_treeNodePath[i][j]];
            }
        }
        for (uint32_t i = 0; i < pathCnt; i++) {
            V1LsSetEdgeTblSelectPath(&pstPath[i], treeNodePathTblId[i], edgeNum, &fldFilter, fieldNum,
                fieldArray1, fieldArray2);
        }
    }

    uint32_t *tblId = tblIdArray;
    for (uint32_t i = 0; i < pathCnt; i++) {
        // 顶点表
        if (scene == CREATE_EDGE_ORDER || scene == CREATE_EDGE_LOOP) {
            tblId = tblIdArray;
        } else {
            tblId = treeNodePathTblId[i];
        }

        // 查询
        DB_MUTIL_BUF_STRU *mutilDataBuff = NULL;
        ret = TPC_SelectAllRecByPath(cdbId, dbId, tblId[0], &cond, &fldFilter, &pstPath[i], &mutilDataBuff);
        if (ret == DB_SUCCESS_V1) {
            TPC_FreeBufMemById(mutilDataBuff->memId);
        }
    }
}

// 设置单跳查询路径
void V1LsSetTopoPath(DB_EDGE_CON_STRU *nextedge, uint16_t labelId1, uint16_t labelId2,
    DB_FIELDFILTER_STRU *pstFldFilter, uint32_t fieldNum, uint8_t *field1, uint8_t *field2)
{
    nextedge->edgeInfo[0].relId = labelId1;
    nextedge->edgeInfo[0].FieldNum = fieldNum;
    for (uint32_t j = 0; j < fieldNum; j++) {
        nextedge->edgeInfo[0].aucField[j] = field1[j];
    }
    nextedge->edgeInfo[1].relId = labelId2;
    nextedge->edgeInfo[1].FieldNum = fieldNum;
    for (uint32_t j = 0; j < fieldNum; j++) {
        nextedge->edgeInfo[1].aucField[j] = field2[j];
    }
    nextedge->pstFldFilter = pstFldFilter;
}

// 单跳查询
int32_t V1LsEdgeTblSelectByTopo(uint32_t cdbId, uint32_t dbId, V1LsTblInfoT *tblInfo, V1LsTblRecInfoT **tblRecInfo,
    uint32_t *tblIdArray, V1LS_CREATE_EDGE_SCENE_E scene)
{
    int32_t ret = DB_SUCCESS_V1;

    // 查询条件
    DB_COND_STRU cond;
    cond.usCondNum = 0;

    // 设置投影的字段
    DB_FIELDFILTER_STRU fldFilter;
    fldFilter.ucFieldNum = DB_FIELD_ALL;

    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };

    // 对于CREATE_EDGE_ORDER和CREATE_EDGE_LOOP来说，路径就是1条，顺着边查
    // 对于CREATE_EDGE_TREE来说，路径会有多条
    uint32_t pathCnt = 1;
    uint32_t topoNum = 3;
    uint32_t treeNodePathTblId[TREE_PATH_CNT_MAX][TREE_PATH_LEN_MAX] = { 0 };
    DB_PATH_STRU pstPath[TREE_PATH_CNT_MAX];
    if (scene == CREATE_EDGE_ORDER || scene == CREATE_EDGE_LOOP) {
        pathCnt = 1;
        topoNum = V1LsGetRandomRange(3, 9);
    } else {
        pathCnt = TREE_PATH_CNT_MAX;
        topoNum = TREE_PATH_LEN_MAX - 1;
        // 取真实的表id
        for (uint32_t i = 0; i < TREE_PATH_CNT_MAX; i++) {
            for (uint32_t j = 0; j < TREE_PATH_LEN_MAX; j++) {
                treeNodePathTblId[i][j] = tblIdArray[g_treeNodePath[i][j]];
            }
        }
    }

    // 顶点表
    uint32_t *tblId = tblIdArray;
    DB_SELHANDLE phSelect;
    for (uint32_t i = 0; i < pathCnt; i++) {
        // 顶点表
        if (scene == CREATE_EDGE_ORDER || scene == CREATE_EDGE_LOOP) {
            tblId = tblIdArray;
        } else {
            tblId = treeNodePathTblId[i];
        }
        // 查询
        ret = TPC_BeginSelectByTopo(cdbId, dbId, tblId[0], &cond, &fldFilter, &phSelect);
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_BeginSelectByTopo failed");

        VOS_UINT32 fetchCount = EDGE_QUERY_TBL_REC_MAX;
        DB_MUTIL_BUF_STRU *mutilDataBuff = NULL;
        DB_EDGE_CON_STRU nextedge;
        for (uint32_t j = 0; j < topoNum; j++) {
            // 进行跳表
            if (j != 0) {
                V1LsSetTopoPath(&nextedge, tblId[j - 1], tblId[j], &fldFilter, fieldNum, fieldArray1, fieldArray2);
                ret = TPC_MoveNextByEdge(phSelect, nextedge);
                if (ret != DB_SUCCESS_V1) {
                    int32_t retTmp = TPC_EndTopoSelect(cdbId, phSelect);
                    V1LS_EXPECT_EQ(DB_SUCCESS_V1, retTmp, "TPC_EndTopoSelect failed");
                }
                V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp TPC_MoveNextByEdge failed, scene(%u)", scene);
            }

            // 获取对应的表信息位置
            uint32_t idx = tblId[j] - EDGE_QUERY_TBL_ID_START;

            // 查询数据
            ret = TPC_FetchSelectTopoRec(cdbId, phSelect, fetchCount, &mutilDataBuff);
            if (ret != DB_SUCCESS_V1) {
                int32_t retTmp = TPC_EndTopoSelect(cdbId, phSelect);
                V1LS_EXPECT_EQ(DB_SUCCESS_V1, retTmp, "exp TPC_EndTopoSelect failed, scene(%u)", scene);
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp TPC_FetchSelectTopoRec failed, scene(%u)", scene);

            // 校验表的数量，除非该表没数据，不存在这种情况，0这条记录是必存在的
            V1LS_EXPECT_EQ(1, mutilDataBuff->realRelNum, "exp mutilDataBuff->realRelNum failed");
            V1LS_EXPECT_EQ(tblId[j], mutilDataBuff->dataBuf[0].relId, "exp mutilDataBuff->->dataBuf[0].relId failed");

            // 校验数据
            uint8_t *pBuf = NULL;
            DB_DSBUF_STRU pstDsBufCheck;
            ret = V1LsMallocDsBuf(&pstDsBufCheck, tblInfo[idx].recLen, 1, tblInfo[idx].recLen);
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocDsBuf failed");

            uint32_t pKVal = 0;
            pBuf = (uint8_t *)mutilDataBuff->dataBuf[0].pBuf;
            for (uint32_t k = 0; k < mutilDataBuff->dataBuf[0].ulRecNum; k++) {
                // 拿主键来做校验
                pKVal = *(uint32_t *)(pBuf + k * tblInfo[idx].recLen);
                // 检验数据
                V1LsSetRecBuf(tblInfo, &pstDsBufCheck, pKVal,
                    (pKVal * tblRecInfo[idx][pKVal].fieldValInfo.relation) % 128);
                ret = V1LsCompAllFieldRec(tblInfo, pBuf + k * tblInfo[idx].recLen, pstDsBufCheck.StdBuf.pucData,
                    tblInfo[idx].recLen);
                if (ret != DB_SUCCESS_V1) {
                    V1LsFreeDsBuf(&pstDsBufCheck);
                    TPC_FreeBufMemById(mutilDataBuff->memId);
                    int32_t retTmp = TPC_EndTopoSelect(cdbId, phSelect);
                    V1LS_EXPECT_EQ(DB_SUCCESS_V1, retTmp, "TPC_EndTopoSelect failed");
                }
                V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsCompAllFieldRec failed, fieldVal:%u", pKVal);
                // 这个数据跟用户预期的存在状态一致
                V1LS_EXPECT_TRUE(tblRecInfo[idx][pKVal].isExist, "check data status failed, fieldVal:%u", pKVal);
            }
            V1LsFreeDsBuf(&pstDsBufCheck);
            TPC_FreeBufMemById(mutilDataBuff->memId);
        }

        // 跳回起始位置，跳的次数有限制，回跳可以减少这个次数，再开始下一个路径的跳表
        for (uint32_t j = topoNum - 1; j > 0; j--) {
            ret = TPC_MoveBack(phSelect);
            if (ret != DB_SUCCESS_V1) {
                int32_t retTmp = TPC_EndTopoSelect(cdbId, phSelect);
                V1LS_EXPECT_EQ(DB_SUCCESS_V1, retTmp, "TPC_EndTopoSelect failed");
            }
            V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_MoveBack failed");
        }
        ret = TPC_EndTopoSelect(cdbId, phSelect);
        V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_EndTopoSelect failed");
    }

    return ret;
}

// 单跳查询，不做预期
void V1LsEdgeTblSelectByTopoNotExp(uint32_t cdbId, uint32_t dbId, uint32_t *tblIdArray, V1LS_CREATE_EDGE_SCENE_E scene)
{
    int32_t ret = DB_SUCCESS_V1;

    // 查询条件
    DB_COND_STRU cond;
    cond.usCondNum = 0;

    // 设置投影的字段
    DB_FIELDFILTER_STRU fldFilter;
    fldFilter.ucFieldNum = DB_FIELD_ALL;

    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };

    // 对于CREATE_EDGE_ORDER和CREATE_EDGE_LOOP来说，路径就是1条，顺着边查
    // 对于CREATE_EDGE_TREE来说，路径会有多条
    uint32_t pathCnt = 1;
    uint32_t topoNum = 3;
    uint32_t treeNodePathTblId[TREE_PATH_CNT_MAX][TREE_PATH_LEN_MAX] = { 0 };
    DB_PATH_STRU pstPath[TREE_PATH_CNT_MAX];
    if (scene == CREATE_EDGE_ORDER || scene == CREATE_EDGE_LOOP) {
        pathCnt = 1;
        topoNum = V1LsGetRandomRange(3, 9);
    } else {
        pathCnt = TREE_PATH_CNT_MAX;
        topoNum = TREE_PATH_LEN_MAX - 1;
        // 取真实的表id
        for (uint32_t i = 0; i < TREE_PATH_CNT_MAX; i++) {
            for (uint32_t j = 0; j < TREE_PATH_LEN_MAX; j++) {
                treeNodePathTblId[i][j] = tblIdArray[g_treeNodePath[i][j]];
            }
        }
    }

    // 顶点表
    uint32_t *tblId = tblIdArray;
    DB_SELHANDLE phSelect;
    for (uint32_t i = 0; i < pathCnt; i++) {
        // 顶点表
        if (scene == CREATE_EDGE_ORDER || scene == CREATE_EDGE_LOOP) {
            tblId = tblIdArray;
        } else {
            tblId = treeNodePathTblId[i];
        }
        // 查询
        ret = TPC_BeginSelectByTopo(cdbId, dbId, tblId[0], &cond, &fldFilter, &phSelect);
        if (ret == DB_SUCCESS_V1) {
            VOS_UINT32 fetchCount = EDGE_QUERY_TBL_REC_MAX;
            DB_MUTIL_BUF_STRU *mutilDataBuff = NULL;
            DB_EDGE_CON_STRU nextedge;
            for (uint32_t j = 0; j < topoNum; j++) {
                // 进行跳表
                if (j != 0) {
                    V1LsSetTopoPath(&nextedge, tblId[j - 1], tblId[j], &fldFilter, fieldNum, fieldArray1, fieldArray2);
                    TPC_MoveNextByEdge(phSelect, nextedge);
                }
                // 查询数据
                ret = TPC_FetchSelectTopoRec(cdbId, phSelect, fetchCount, &mutilDataBuff);
                if (ret == DB_SUCCESS_V1) {
                    TPC_FreeBufMemById(mutilDataBuff->memId);
                }
            }

            // 跳回起始位置，跳的次数有限制，回跳可以减少这个次数，再开始下一个路径的跳表
            for (uint32_t j = topoNum - 1; j > 0; j--) {
                TPC_MoveBack(phSelect);
            }
            ret = TPC_EndTopoSelect(cdbId, phSelect);
            V1LS_EXPECT_EQ(DB_SUCCESS_V1, ret, "TPC_EndTopoSelect failed");
        }
    }
}

// 检查是否是预期的错误码
bool V1LsCheckRet(int32_t *actualRet, uint32_t size1, int32_t *expectRet, uint32_t size2)
{
    bool isCheckSucc;
    for (uint32_t i = 0; i < size1; i++) {
        isCheckSucc = false;
        for (uint32_t j = 0; j < size2; j++) {
            if (actualRet[i] == expectRet[j]) {
                isCheckSucc = true;
                break;
            }
        }
        if (!isCheckSucc) {
            V1LS_LOG(V1LS_LOG_ERROR, "check ret failed, ret: %#x/%u", actualRet[i], actualRet[i]);
            break;
        }
    }

    return isCheckSucc;
}

// 检查是否是预期的字符串
bool V1LsCheckStr(char **actualStr, uint32_t size1, char *expectStr)
{
    bool isCheckSucc = false;
    uint32_t findCnt = 0;
    for (uint32_t i = 0; i < size1; i++) {
        if (strcmp(actualStr[i], expectStr) == 0) {
            isCheckSucc = true;
            findCnt++;
        }
    }
    if (!isCheckSucc || findCnt != 1) {
        V1LS_LOG(V1LS_LOG_ERROR, "check str failed, str: %s", expectStr);
        return false;
    }

    return isCheckSucc;
}

bool V1LsCheckTblName(char **actualStr, uint32_t size1, char **expectStr, uint32_t size2)
{
    bool isCheckSucc = true;
    uint32_t findCnt = 0;
    for (uint32_t i = 0; i < size2; i++) {
        isCheckSucc = false;
        findCnt = 0;
        for (uint32_t j = 0; j < size1; j++) {
            if (strcmp(actualStr[j], expectStr[i]) == 0) {
                isCheckSucc = true;
                findCnt++;
            }
        }
        if (!isCheckSucc || findCnt != 1) {
            V1LS_LOG(V1LS_LOG_ERROR, "check str failed, str: %s", expectStr[i]);
            for (uint32_t j = 0; j < size1; j++) {
                V1LS_LOG(V1LS_LOG_ERROR, "actualStr[%u]: %s", j, actualStr[j]);
            }
            for (uint32_t j = 0; j < size2; j++) {
                V1LS_LOG(V1LS_LOG_ERROR, "expectStr[%u]: %s", j, expectStr[j]);
            }
            return false;
        }
    }

    return isCheckSucc;
}

// 从文件中获取某个字符串开头的字符串，cnt为查找到的第几次，endCh为结束字符
int32_t V1LsFindStr(char *srcStr, const char *findStr, uint32_t cnt, char endCh,
    char *output, uint32_t size)
{
    uint32_t findCnt = 0;
    char *p = NULL;
    while (1) {
        p = strstr(srcStr, findStr);
        if (p != NULL) {
            findCnt++;
        } else {
            return STATUS_FAILED;
        }
        // 如果找到的次数和要求的次数不一致，继续往下找
        if (findCnt != cnt) {
            continue;
        }

        // 获取值
        uint32_t len = strlen(p);
        uint32_t i;
        for (i = 0; i < len; i++) {
            if (*p != endCh && i < (size - 2)) {
                output[i] = *p;
                if (output[i] == '\0') {
                    output[i] = ' ';
                }
            } else {
                break;
            }
            p++;
        }
        return 0;
    }

    return 0;
}

typedef enum tagV1LsImpTypeT {
    RESSTORE_RANDOM = 0,
    RESSTORE,
    RESSTORE_WITH_DATA_CONVHOOK,
    RESSTORE_INVALID
} V1LsImpTypeT;

// 随机的选择一种导入方式进行数据导入
int32_t V1LsTpcImp(V1LsImpTypeT impType, VOS_UINT8 *filePath, VOS_UINT8 *dbName, VOS_UINT8 *pucDestDir,
    DB_RESTORETYPE_ENUM enRestore, const DB_RESTORE_CONFIG_STRU *pstDbConfig, VOS_UINT32 ulBehavior)
{
    int32_t ret;
    if (impType == RESSTORE_RANDOM) {
        impType = (V1LsImpTypeT)V1LsGetRandomRange(RESSTORE, RESSTORE_INVALID - 1);
    }
    DB_GetTblConvHook pfnGetTblConvHook = NULL;
    switch (impType) {
        case RESSTORE:
            ret = TPC_Restore(filePath, dbName, pucDestDir, enRestore, pstDbConfig, ulBehavior);
            break;
        case RESSTORE_WITH_DATA_CONVHOOK:
            ret = TPC_RestoreWithDataConvHook(filePath, dbName, pucDestDir, enRestore, pstDbConfig,
                ulBehavior, pfnGetTblConvHook);
            break;
        default: ret = -1; break;
    }

    return ret;
}

// 随机的选择一种导入方式进行数据导入
int32_t V1LsDbImp(V1LsImpTypeT impType, VOS_UINT8 *filePath, VOS_UINT8 *dbName, VOS_UINT8 *pucDestDir,
    DB_RESTORETYPE_ENUM enRestore, DB_RESTORE_CONFIG_STRU *pstDbConfig, VOS_UINT32 ulBehavior)
{
    int32_t ret;
    if (impType == RESSTORE_RANDOM) {
        impType = (V1LsImpTypeT)V1LsGetRandomRange(RESSTORE, RESSTORE_INVALID - 1);
    }
    DB_GetTblConvHook pfnGetTblConvHook = NULL;
    switch (impType) {
        case RESSTORE:
            ret = DB_Restore(filePath, dbName, pucDestDir, enRestore, pstDbConfig);
            break;
        case RESSTORE_WITH_DATA_CONVHOOK:
            ret = DB_RestoreWithDataConvHook(filePath, dbName, pucDestDir, enRestore, pstDbConfig, pfnGetTblConvHook);
            break;
        default: ret = -1; break;
    }

    return ret;
}

typedef enum tagV1LsExpTypeT {
    BKPPHY_RANDOM = 0,
    BKPPHY,
    BKPPHYEX,
    BKPPHYEX_WITH_DATA_CONVHOOK,
    PHYBKP2,
    BKPPHY_INVALID
} V1LsExpTypeT;

// 随机的选择一种导出方式进行数据导出
int32_t V1LsTpcExp(V1LsExpTypeT expTpye, VOS_UINT32 ulDbId, VOS_UINT8 *filePath)
{
    int32_t ret;

    if (expTpye == BKPPHY_RANDOM) {
        expTpye = (V1LsExpTypeT)V1LsGetRandomRange(BKPPHY, BKPPHY_INVALID - 1);
    }
    DB_GetTblConvHook pfnGetTblConvHook = NULL;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = (DB_FILE_FORMAT_TYPE)V1LsGetRandomRange(DB_BIG_ENDIAN, DB_LITTLE_ENDIAN);
    DB_BAKPHY_OPTIONS_STRU pstBakPhyOpts = { 0 };
    pstBakPhyOpts.pstFileSaveOpt = &saveOpt;
    pstBakPhyOpts.pfnGetTblConvHook = pfnGetTblConvHook;
    switch (expTpye) {
        case BKPPHY:
            ret = TPC_BkpPhy(ulDbId, filePath);
            break;
        case BKPPHYEX:
            ret = TPC_BkpPhyEx(ulDbId, filePath, &saveOpt);
            break;
        case BKPPHYEX_WITH_DATA_CONVHOOK:
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, filePath, &saveOpt, pfnGetTblConvHook);
            break;
        case PHYBKP2:
            ret = TPC_PhyBkp2(ulDbId, filePath, &pstBakPhyOpts);
            break;
        default: ret = -1; break;
    }

    return ret;
}

int32_t V1LsDbExp(V1LsExpTypeT expTpye, VOS_UINT32 ulDbId, VOS_UINT8 *filePath)
{
    int32_t ret;

    if (expTpye == BKPPHY_RANDOM) {
        expTpye = (V1LsExpTypeT)V1LsGetRandomRange(BKPPHY, BKPPHY_INVALID - 1);
    }
    DB_GetTblConvHook pfnGetTblConvHook = NULL;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = (DB_FILE_FORMAT_TYPE)V1LsGetRandomRange(DB_BIG_ENDIAN, DB_LITTLE_ENDIAN);
    DB_BAKPHY_OPTIONS_STRU pstBakPhyOpts = { 0 };
    pstBakPhyOpts.pstFileSaveOpt = &saveOpt;
    pstBakPhyOpts.pfnGetTblConvHook = pfnGetTblConvHook;
    switch (expTpye) {
        case BKPPHY:
            ret = DB_BkpPhy(ulDbId, filePath);
            break;
        case BKPPHYEX:
            ret = DB_BkpPhyEx(ulDbId, filePath, &saveOpt);
            break;
        case BKPPHYEX_WITH_DATA_CONVHOOK:
            ret = DB_BkpPhyWithDataConvHook(ulDbId, filePath, &saveOpt, pfnGetTblConvHook);
            break;
        case PHYBKP2:
            ret = DB_PhyBkp2(ulDbId, filePath, &pstBakPhyOpts);
            break;
        default: ret = -1; break;
    }

    return ret;
}

// TPC元数据接口调用
int32_t V1LsTpcApi(V1LsTblInfoT *tblInfo)
{
    int32_t ret = 0;

    VOS_UINT32 pulActiveCDBNum;
    ret = TPC_GetActiveCDBNum(tblInfo->dbId, &pulActiveCDBNum);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetActiveCDBNum failed");

    VOS_UINT8 pDBDescrInfo[100];
    VOS_UINT32 pulLen = sizeof(pDBDescrInfo);
    ret = TPC_GetDBDesc((VOS_UINT8 *)tblInfo->dbName, NULL, pDBDescrInfo, &pulLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetDBDesc failed");

    VOS_UINT8 pucDbName[DB_NAME_LEN] = { 0 };
    ret = TPC_GetDBName(tblInfo->dbId, pucDbName);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetDBId failed");
    ret = strcmp(tblInfo->dbName, (char *)pucDbName);
    V1LS_FUNC_RETURN_FAIL_IF_NE(DB_SUCCESS_V1, ret, "check TPC_GetDBName db name failed");

    VOS_UINT32 pulDatabaseId;
    ret = TPC_GetDBId((VOS_UINT8 *)tblInfo->dbName, &pulDatabaseId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetDBId failed");
    V1LS_FUNC_RETURN_FAIL_IF_NE(tblInfo->dbId, pulDatabaseId, "exp TPC_GetDBId ulDbId failed");

    DB_HANDLEINFO_STRU pstHndlInfo[10];
    DB_HANDLECTRL_INFO_STRU pstHandleCtrlInfo = { .ulHndlCnt = 10, .pstHndlInfo = pstHndlInfo };
    ret = TPC_GetOpenHandleInfo(tblInfo->dbId, &pstHandleCtrlInfo);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetOpenHandleInfo failed");

    VOS_UINT32 pulRelNum;
    ret = TPC_GetTblCount(tblInfo->dbId, &pulRelNum);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetTblCount failed");
    V1LS_FUNC_RETURN_FAIL_IF_NE(1, pulRelNum, "exp TPC_GetTblCount pulRelNum failed");

    VOS_UINT16 pusRelId;
    ret = TPC_GetTblId(tblInfo->dbId, (VOS_UINT8 *)tblInfo->tblName, &pusRelId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetTblId failed");
    V1LS_FUNC_RETURN_FAIL_IF_NE(tblInfo->tblId, pusRelId, "exp TPC_GetTblId pusRelId failed");

    DB_RELATION_INFO pstRelationInfo = { 0 };
    ret = TPC_GetTblInfo(tblInfo->dbId, tblInfo->tblId, &pstRelationInfo);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetTblInfo failed");

    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(tblInfo->dbId, tblInfo->tblId, &pulActRec);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetRelActRec failed");

    VOS_UINT32 ulBufSize = tblInfo->tblDef->ulNCols * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = (DB_FIELD_INFO *)V1LsGetMem(ulBufSize);
    V1LS_FUNC_RETURN_FAIL_IF_NULL(pstAllFieldInfo);
    ret = TPC_GetTblColInfo(tblInfo->dbId, tblInfo->tblId, pstAllFieldInfo, ulBufSize);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeMem(pstAllFieldInfo);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetTblColInfo failed");
    V1LsFreeMem(pstAllFieldInfo);

    ulBufSize = tblInfo->tblDef->ulNIdxs * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = (DB_INDEX_INFO *)V1LsGetMem(ulBufSize);
    ret = TPC_GetTblIdxInfo(tblInfo->dbId, tblInfo->tblId, pstAllIndexInfo, ulBufSize);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeMem(pstAllIndexInfo);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetTblIdxInfo failed");
    V1LsFreeMem(pstAllIndexInfo);

    VOS_UINT32 pulTblCount = pulRelNum;
    VOS_UINT8 **ppucRelation = NULL;
    ret = V1LsMallocUint8ArrayMem(&ppucRelation, pulRelNum, DB_REL_NAME_LEN);
    V1LS_FUNC_RETURN_FAIL_IF_NE(DB_SUCCESS_V1, ret, "exp V1LsMallocUint8ArrayMem failed");
    ret = TPC_GetTblNamesAndCount(tblInfo->dbId, &pulTblCount, ppucRelation);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeUint8ArrayMem(ppucRelation, pulRelNum);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "exp TPC_GetTblNamesAndCount failed");
    if (pulTblCount != 1) {
        V1LsFreeUint8ArrayMem(ppucRelation, pulRelNum);
    }
    V1LS_FUNC_RETURN_FAIL_IF_NE(1, pulTblCount, "exp TPC_GetTblNamesAndCount pulTblCount failed");
    V1LsFreeUint8ArrayMem(ppucRelation, pulRelNum);

    return ret;
}

// TPC元数据接口调用，不做预期
void V1LsTpcApiNotExp(V1LsTblInfoT *tblInfo)
{
    int32_t ret;

    VOS_UINT32 pulActiveCDBNum;
    ret = TPC_GetActiveCDBNum(tblInfo->dbId, &pulActiveCDBNum);

    VOS_UINT8 pDBDescrInfo[100];
    VOS_UINT32 pulLen = sizeof(pDBDescrInfo);
    ret = TPC_GetDBDesc((VOS_UINT8 *)tblInfo->dbName, NULL, pDBDescrInfo, &pulLen);

    VOS_UINT8 pucDbName[DB_NAME_LEN] = { 0 };
    ret = TPC_GetDBName(tblInfo->dbId, pucDbName);

    VOS_UINT32 pulDatabaseId;
    ret = TPC_GetDBId((VOS_UINT8 *)tblInfo->dbName, &pulDatabaseId);

    DB_HANDLEINFO_STRU pstHndlInfo[10];
    DB_HANDLECTRL_INFO_STRU pstHandleCtrlInfo = { .ulHndlCnt = 10, .pstHndlInfo = pstHndlInfo };
    ret = TPC_GetOpenHandleInfo(tblInfo->dbId, &pstHandleCtrlInfo);

    VOS_UINT32 pulRelNum;
    ret = TPC_GetTblCount(tblInfo->dbId, &pulRelNum);

    VOS_UINT16 pusRelId;
    ret = TPC_GetTblId(tblInfo->dbId, (VOS_UINT8 *)tblInfo->tblName, &pusRelId);

    DB_RELATION_INFO pstRelationInfo = { 0 };
    ret = TPC_GetTblInfo(tblInfo->dbId, tblInfo->tblId, &pstRelationInfo);

    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(tblInfo->dbId, tblInfo->tblId, &pulActRec);

    VOS_UINT32 ulBufSize = tblInfo->tblDef->ulNCols * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = (DB_FIELD_INFO *)V1LsGetMem(ulBufSize);
    V1LS_FUNC_RETURN_NONE_IF_NULL(pstAllFieldInfo);
    ret = TPC_GetTblColInfo(tblInfo->dbId, tblInfo->tblId, pstAllFieldInfo, ulBufSize);
    V1LsFreeMem(pstAllFieldInfo);

    ulBufSize = tblInfo->tblDef->ulNIdxs * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = (DB_INDEX_INFO *)V1LsGetMem(ulBufSize);
    ret = TPC_GetTblIdxInfo(tblInfo->dbId, tblInfo->tblId, pstAllIndexInfo, ulBufSize);
    V1LsFreeMem(pstAllIndexInfo);

    VOS_UINT32 pulTblCount = 1;
    VOS_UINT8 **ppucRelation = NULL;
    ret = V1LsMallocUint8ArrayMem(&ppucRelation, 1, DB_REL_NAME_LEN);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "exp V1LsMallocUint8ArrayMem failed");
    ret = TPC_GetTblNamesAndCount(tblInfo->dbId, &pulTblCount, ppucRelation);
    V1LsFreeUint8ArrayMem(ppucRelation, 1);
}

// 可以跟TPC接口混用的DB元数据接口
int32_t V1LsCanMixUseDbApi(V1LsTblInfoT *tblInfo)
{
    int32_t ret = DB_SUCCESS_V1;

    VOS_UINT32 pulNumOfDatabases;
    ret = DBINIT_GetNumOfDatabases(&pulNumOfDatabases);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBINIT_GetNumOfDatabases failed");

    pulNumOfDatabases = 100;
    VOS_UINT8 **ppucDatabaseNames = NULL;
    ret = V1LsMallocUint8ArrayMem(&ppucDatabaseNames, pulNumOfDatabases, DB_NAME_LEN);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocUint8ArrayMem failed");
    ret = DBINIT_GetDatabaseNamesEx(&pulNumOfDatabases, ppucDatabaseNames);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeUint8ArrayMem(ppucDatabaseNames, 100);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBINIT_GetDatabaseNamesEx failed");
    V1LsFreeUint8ArrayMem(ppucDatabaseNames, 100);

    uint32_t actRec = 0;
    actRec = DBDDL_GetRelActRec(tblInfo->dbId, tblInfo->tblId);

    uint32_t maxRec = 0;
    maxRec = DBDDL_GetRelMaxRecNum(tblInfo->dbId, tblInfo->tblId);
    V1LS_FUNC_RETURN_FAIL_IF_NE(1000000, maxRec, "exp DBDDL_GetRelMaxRecNum maxRec failed");

    uint32_t recLen = 0;
    recLen = DBDDL_GetRelRecLen(tblInfo->dbId, tblInfo->tblId);
    V1LS_FUNC_RETURN_FAIL_IF_NE(tblInfo->recLen, recLen, "exp DBDDL_GetRelRecLen recLen failed");

    VOS_UINT32 pulActRec;
    ret = DBS_GetRelActRec(tblInfo->dbId, tblInfo->tblId, &pulActRec);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBS_GetRelActRec failed");

    VOS_UINT32 pulMaxRec;
    ret = DBS_GetRelMaxRecNum(tblInfo->dbId, tblInfo->tblId, &pulMaxRec);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBS_GetRelMaxRecNum failed");
    V1LS_FUNC_RETURN_FAIL_IF_NE(1000000, pulMaxRec, "exp DBS_GetRelMaxRecNum pulMaxRec failed");

    VOS_UINT32 pulRecLen;
    ret = DBS_GetRelRecLen(tblInfo->dbId, tblInfo->tblId, &pulRecLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBS_GetRelRecLen failed");
    V1LS_FUNC_RETURN_FAIL_IF_NE(tblInfo->recLen, pulRecLen, "exp DBS_GetRelRecLen pulRecLen failed");

    VOS_UINT32 pulIdxStatistic;
    ret = DBS_GetIdxStatistic(tblInfo->dbId, tblInfo->tblId, 0, &pulIdxStatistic);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBS_GetIdxStatistic failed");

    VOS_UINT16 usIdxId = 0;
    ret = DBS_ResetIdxStat(tblInfo->dbId, tblInfo->tblId, usIdxId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBS_ResetIdxStat failed");

    ret = DBS_ResetAllIdxStat(tblInfo->dbId, tblInfo->tblId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBS_ResetAllIdxStat failed");

    bool isValid = false;
    isValid = DB_IsDatabaseValid(tblInfo->dbId);
    V1LS_EXPECT_TRUE(isValid, "DB_IsDatabaseValid failed");

    isValid = false;
    isValid = DB_IsRelationValid(tblInfo->dbId, tblInfo->tblId);
    V1LS_EXPECT_TRUE(isValid, "DB_IsRelationValid failed");

    DB_BKPSTATE_ENUM penBkpState = (DB_BKPSTATE_ENUM)V1LsGetRandomRange(DB_BKPSTATE_INIT, DB_BKPSTATE_BUTT - 1);
    VOS_UINT32 pulProgress;
    ret = DB_GetBkpProgress(tblInfo->dbId, &penBkpState, &pulProgress);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetBkpProgress failed");

    ret = DB_ResetBkpProgress(tblInfo->dbId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_ResetBkpProgress failed");

    return ret;
}

// 可以跟TPC接口混用的DB元数据接口，不做预期
void V1LsCanMixUseDbApiNotExp(V1LsTblInfoT *tblInfo)
{
    int32_t ret = DB_SUCCESS_V1;

    VOS_UINT32 pulNumOfDatabases;
    ret = DBINIT_GetNumOfDatabases(&pulNumOfDatabases);

    pulNumOfDatabases = 100;
    VOS_UINT8 **ppucDatabaseNames = NULL;
    ret = V1LsMallocUint8ArrayMem(&ppucDatabaseNames, 100, DB_NAME_LEN);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocUint8ArrayMem failed");
    ret = DBINIT_GetDatabaseNamesEx(&pulNumOfDatabases, ppucDatabaseNames);
    V1LsFreeUint8ArrayMem(ppucDatabaseNames, 100);

    ret = DBDDL_GetRelActRec(tblInfo->dbId, tblInfo->tblId);

    ret = DBDDL_GetRelMaxRecNum(tblInfo->dbId, tblInfo->tblId);

    ret = DBDDL_GetRelRecLen(tblInfo->dbId, tblInfo->tblId);

    VOS_UINT32 pulActRec;
    ret = DBS_GetRelActRec(tblInfo->dbId, tblInfo->tblId, &pulActRec);

    VOS_UINT32 pulMaxRec;
    ret = DBS_GetRelMaxRecNum(tblInfo->dbId, tblInfo->tblId, &pulMaxRec);

    VOS_UINT32 pulRecLen;
    ret = DBS_GetRelRecLen(tblInfo->dbId, tblInfo->tblId, &pulRecLen);

    VOS_UINT32 pulIdxStatistic;
    ret = DBS_GetIdxStatistic(tblInfo->dbId, tblInfo->tblId, 0, &pulIdxStatistic);

    ret = DBS_ResetAllIdxStat(tblInfo->dbId, tblInfo->tblId);

    VOS_UINT16 usIdxId = 0;
    ret = DBS_ResetIdxStat(tblInfo->dbId, tblInfo->tblId, usIdxId);

    DB_IsDatabaseValid(tblInfo->dbId);
    DB_IsRelationValid(tblInfo->dbId, tblInfo->tblId);

    DB_BKPSTATE_ENUM penBkpState = DB_BKPSTATE_INIT;
    VOS_UINT32 pulProgress;
    ret = DB_GetBkpProgress(tblInfo->dbId, &penBkpState, &pulProgress);

    ret = DB_ResetBkpProgress(tblInfo->dbId);
}

// DB元数据接口调用
int32_t V1LsDbApi(V1LsTblInfoT *tblInfo)
{
    int32_t ret = 0;

    VOS_UINT32 pulDatabaseId;
    ret = DBINIT_GetDatabaseId((VOS_UINT8 *)tblInfo->dbName, &pulDatabaseId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBINIT_GetDatabaseId failed");

    VOS_UINT32 pulRelNum;
    ret = DBINIT_GetNumOfRelations(tblInfo->dbId, &pulRelNum);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBINIT_GetNumOfRelations failed");
    V1LS_FUNC_RETURN_FAIL_IF_NE(1, pulRelNum, "DBINIT_GetDatabaseId failed");

    VOS_UINT16 pusRelId;
    ret = DBINIT_GetRelationId(tblInfo->dbId, (VOS_UINT8 *)tblInfo->tblName, &pusRelId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBINIT_GetRelationId failed");

    VOS_UINT32 ulBufSize = tblInfo->tblDef->ulNCols * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = (DB_FIELD_INFO *)V1LsGetMem(ulBufSize);
    V1LS_FUNC_RETURN_FAIL_IF_NULL(pstAllFieldInfo);
    ret = DBINIT_GetAllFieldInfo(tblInfo->dbId, tblInfo->tblId, pstAllFieldInfo, ulBufSize);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeMem(pstAllFieldInfo);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBINIT_GetAllFieldInfo failed");
    V1LsFreeMem(pstAllFieldInfo);

    ulBufSize = tblInfo->tblDef->ulNIdxs * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = (DB_INDEX_INFO *)V1LsGetMem(ulBufSize);
    ret = DBINIT_GetAllIndexInfo(tblInfo->dbId, tblInfo->tblId, pstAllIndexInfo, ulBufSize);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeMem(pstAllIndexInfo);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "TPC_GetTblIdxInfo failed");
    V1LsFreeMem(pstAllIndexInfo);

    DB_RELATION_INFO pstRelationInfo = { 0 };
    ret = DBS_GetRelationInfo(tblInfo->dbId, tblInfo->tblId, &pstRelationInfo);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBS_GetRelationInfo failed");

    VOS_UINT32 pulMaxRec;
    ret = DBS_GetRelMaxRecNum(tblInfo->dbId, tblInfo->tblId, &pulMaxRec);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DBS_GetRelMaxRecNum failed");

    DB_HANDLEINFO_STRU pstHndlInfo[10];
    DB_HANDLECTRL_INFO_STRU pstHandleCtrlInfo = { .ulHndlCnt = 10, .pstHndlInfo = pstHndlInfo };
    ret = DB_GetOpenHandleInfo(tblInfo->dbId, &pstHandleCtrlInfo);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetOpenHandleInfo failed");

    VOS_UINT8 pDBDescrInfo[100];
    VOS_UINT32 pulLen = sizeof(pDBDescrInfo);
    ret = DB_GetDBDesc((VOS_UINT8 *)tblInfo->dbName, NULL, pDBDescrInfo, &pulLen);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetDBDesc failed");

    VOS_UINT8 pucDbName[DB_NAME_LEN];
    ret = DB_GetDBName(tblInfo->dbId, pucDbName);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetDBName failed");

    ret = DB_GetDBId((VOS_UINT8 *)tblInfo->dbName, &pulDatabaseId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetDBId failed");

    VOS_UINT32 pulTblCount = pulRelNum;
    VOS_UINT8 **ppucRelation = NULL;
    ret = V1LsMallocUint8ArrayMem(&ppucRelation, pulRelNum, DB_REL_NAME_LEN);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocUint8ArrayMem failed");
    ret = DB_GetTblNamesAndCount(tblInfo->dbId, &pulTblCount, ppucRelation);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeUint8ArrayMem(ppucRelation, pulRelNum);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetTblNamesAndCount failed");
    V1LsFreeUint8ArrayMem(ppucRelation, pulRelNum);

    ret = DB_GetTblInfo(tblInfo->dbId, tblInfo->tblId, &pstRelationInfo);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetTblInfo failed");

    ret = DB_GetTblCount(tblInfo->dbId, &pulRelNum);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetTblCount failed");

    ret = DB_GetTblId(tblInfo->dbId, (VOS_UINT8 *)tblInfo->tblName, &pusRelId);
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetTblId failed");

    ulBufSize = tblInfo->tblDef->ulNCols * sizeof(DB_FIELD_INFO);
    pstAllFieldInfo = (DB_FIELD_INFO *)V1LsGetMem(ulBufSize);
    V1LS_FUNC_RETURN_FAIL_IF_NULL(pstAllFieldInfo);
    ret = DB_GetTblColInfo(tblInfo->dbId, tblInfo->tblId, pstAllFieldInfo, ulBufSize);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeMem(pstAllFieldInfo);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetTblColInfo failed");
    V1LsFreeMem(pstAllFieldInfo);

    ulBufSize = tblInfo->tblDef->ulNIdxs * sizeof(DB_INDEX_INFO);
    pstAllIndexInfo = (DB_INDEX_INFO *)V1LsGetMem(ulBufSize);
    ret = DB_GetTblIdxInfo(tblInfo->dbId, tblInfo->tblId, pstAllIndexInfo, ulBufSize);
    if (ret != DB_SUCCESS_V1) {
        V1LsFreeMem(pstAllIndexInfo);
    }
    V1LS_FUNC_RETURN_RET_IF_NE(DB_SUCCESS_V1, ret, "DB_GetTblIdxInfo failed");
    V1LsFreeMem(pstAllIndexInfo);

    DB_OP_STATUS_STRU pstOpStat = { 0 };
    ret = DB_ResetOpProgress(tblInfo->dbId, &pstOpStat);
    V1LS_FUNC_RETURN_RET_IF_NE(VOS_ERRNO_DB_NOTSUPPORT, ret, "DB_ResetOpProgress failed");
    ret = DB_SUCCESS_V1;

    return ret;
}

// DB元数据接口调用，不做预期
void V1LsDbApiNotExp(V1LsTblInfoT *tblInfo)
{
    int32_t ret = 0;

    VOS_UINT32 pulDatabaseId;
    ret = DBINIT_GetDatabaseId((VOS_UINT8 *)tblInfo->dbName, &pulDatabaseId);

    VOS_UINT32 pulRelNum;
    ret = DBINIT_GetNumOfRelations(tblInfo->dbId, &pulRelNum);

    VOS_UINT16 pusRelId;
    ret = DBINIT_GetRelationId(tblInfo->dbId, (VOS_UINT8 *)tblInfo->tblName, &pusRelId);

    VOS_UINT32 ulBufSize = tblInfo->tblDef->ulNCols * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = (DB_FIELD_INFO *)V1LsGetMem(ulBufSize);
    V1LS_FUNC_RETURN_NONE_IF_NULL(pstAllFieldInfo);
    ret = DBINIT_GetAllFieldInfo(tblInfo->dbId, tblInfo->tblId, pstAllFieldInfo, ulBufSize);
    V1LsFreeMem(pstAllFieldInfo);

    ulBufSize = tblInfo->tblDef->ulNIdxs * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = (DB_INDEX_INFO *)V1LsGetMem(ulBufSize);
    ret = DBINIT_GetAllIndexInfo(tblInfo->dbId, tblInfo->tblId, pstAllIndexInfo, ulBufSize);
    V1LsFreeMem(pstAllIndexInfo);

    DB_RELATION_INFO pstRelationInfo = { 0 };
    ret = DBS_GetRelationInfo(tblInfo->dbId, tblInfo->tblId, &pstRelationInfo);

    VOS_UINT32 pulMaxRec;
    ret = DBS_GetRelMaxRecNum(tblInfo->dbId, tblInfo->tblId, &pulMaxRec);

    VOS_UINT8 pDBDescrInfo[100];
    VOS_UINT32 pulLen = sizeof(pDBDescrInfo);
    ret = DB_GetDBDesc((VOS_UINT8 *)tblInfo->dbName, NULL, pDBDescrInfo, &pulLen);

    VOS_UINT8 pucDbName[DB_NAME_LEN];
    ret = DB_GetDBName(tblInfo->dbId, pucDbName);

    ret = DB_GetDBId((VOS_UINT8 *)tblInfo->dbName, &pulDatabaseId);

    ret = DB_GetTblCount(tblInfo->dbId, &pulRelNum);

    VOS_UINT32 pulTblCount = 1;
    VOS_UINT8 **ppucRelation = NULL;
    ret = V1LsMallocUint8ArrayMem(&ppucRelation, 1, DB_REL_NAME_LEN);
    V1LS_FUNC_RETURN_NONE_IF_NE(DB_SUCCESS_V1, ret, "V1LsMallocUint8ArrayMem failed");
    ret = DB_GetTblNamesAndCount(tblInfo->dbId, &pulTblCount, ppucRelation);
    V1LsFreeUint8ArrayMem(ppucRelation, 1);

    ret = DB_GetTblId(tblInfo->dbId, (VOS_UINT8 *)tblInfo->tblName, &pusRelId);

    ret = DB_GetTblInfo(tblInfo->dbId, tblInfo->tblId, &pstRelationInfo);

    ulBufSize = tblInfo->tblDef->ulNCols * sizeof(DB_FIELD_INFO);
    pstAllFieldInfo = (DB_FIELD_INFO *)V1LsGetMem(ulBufSize);
    V1LS_FUNC_RETURN_NONE_IF_NULL(pstAllFieldInfo);
    ret = DB_GetTblColInfo(tblInfo->dbId, tblInfo->tblId, pstAllFieldInfo, ulBufSize);
    V1LsFreeMem(pstAllFieldInfo);

    ulBufSize = tblInfo->tblDef->ulNIdxs * sizeof(DB_INDEX_INFO);
    pstAllIndexInfo = (DB_INDEX_INFO *)V1LsGetMem(ulBufSize);
    ret = DB_GetTblIdxInfo(tblInfo->dbId, tblInfo->tblId, pstAllIndexInfo, ulBufSize);
    V1LsFreeMem(pstAllIndexInfo);

    DB_HANDLECTRL_INFO_STRU pstHandleCtrlInfo = { 0 };
    ret = DB_GetOpenHandleInfo(tblInfo->dbId, &pstHandleCtrlInfo);

    DB_OP_STATUS_STRU pstOpStat = { 0 };
    ret = DB_ResetOpProgress(tblInfo->dbId, &pstOpStat);
}

// 表压缩
int32_t V1LsCompressTbl(V1LsTblInfoT *tblInfo)
{
    int32_t ret = DB_SUCCESS_V1;

#if FEATURE_COMPRESS_TBL
    // 表压缩，数据量的一半或者2倍
    uint32_t comPressCnt[2] = { 0 };
    comPressCnt[0] = tblInfo->recCurr / 2;
    comPressCnt[1] = tblInfo->recCurr * 2;
    uint32_t idx = V1LsGetRandomRange(0, 1);
    if (tblInfo->dbType == DB_TYPE_TPC) {
        ret = TestTPC_CompressTable(tblInfo->dbId, tblInfo->tblId, comPressCnt[idx]);
    } else {
        ret = TestDB_CompressTable(tblInfo->dbId, tblInfo->tblId, comPressCnt[idx]);
    }
#endif

    return ret;
}

// 获取表的信息
int32_t V1LsGetTblRecFromApi(V1LsTblInfoT *tblInfo)
{
    int32_t ret;
    DB_RELATION_INFO tblInfoGet = { 0 };
    ret = TPC_GetTblInfo(tblInfo->dbId, tblInfo->tblId, &tblInfoGet);
    if (ret != DB_SUCCESS_V1) {
        V1LS_LOG(V1LS_LOG_ERROR, "TPC_GetTblInfo failed, ret:%d", ret);
        return 0;
    }

    return tblInfoGet.ulActualRecNum;
}

#endif

#endif
