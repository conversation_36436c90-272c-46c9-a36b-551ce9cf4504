// cpcar/blacklist 在2025设备上这些特性由芯片实现，其它设备上这些特性由HPF实现

namespace Hpf {
%table CAP_TABLE_CPCAR_PRO(protocolType: int4, flags: int1, carId: int4, priority: int1)
    { index(0(protocolType, flags)), tbm }
%table CAP_TABLE_HSEC_BLACKLIST(policy_id:int4, filter_id:int4, group_id:int4, group_type:int4)
    { index(0(policy_id, filter_id)), tbm }
%table CAP_TABLE_HSEC_TOTALCAR(carId:int4)
    { index(0(carId)), tbm }
%table CAP_TABLE_HSEC_SESSCAR_CARID(index:int4, carId:int4)
    { index(0(index)), tbm }

%table hpf_dns_global_deny_req_from_wan_enable(noUse: int4) { index(0(noUse)), tbm }

// cpcar
%resource CpCarId(protocolType: int4, flags: int1 -> carId: int4) { sequential(max_size(32742)) }
CpCarId(protocolType, flags, -) :- Hsec.Mid_PolicyCpcar(protocolType, flags, pps), NotEqual4(pps, 0).
CarId(CAR_ID_CPCAR, id, -) :- CpCarId(-, -, id).

// Using tc for pps.

CAP_TABLE_CAR(carId, HPF_CAP_TABLE_CAR_PPS,
    0, 0, 0, p, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
    Hsec.Mid_PolicyCpcar(protocolType, flags, pps),
    CpCarId(protocolType, flags, id), I4ToI8(pps, p),
    CarId(CAR_ID_CPCAR, id, carId).
CAP_TABLE_CPCAR_PRO(protocolType, flags, carId, priority) :-
    CpCarId(protocolType, flags, id), Hsec.CpcarPriority(protocolType, priority), CarId(CAR_ID_CPCAR, id, carId).

// blacklist
CAP_TABLE_HSEC_BLACKLIST(policy_id, filter_id, downGroupId, downGroupType) :-
    Hsec.PolicyFilterCfg(policy_id, filter_id, group_id),
    Hsec.AppliedPolicyCfg(policy_id, -),
    Acl.GroupCfg(group_id, groupType, -, -, -, -, -, -),
    Acl.GroupType2HpfType(groupType, dt),
    I1ToI4(dt, downGroupType),
    Acl.GetHpfGroupId(0, group_id, downGroupId).

Acl.GroupInUseOne(downGroupId, aclGroupId, groupType) :-
    Hsec.PolicyFilterCfg(policy_id, -, aclGroupId),
    Hsec.AppliedPolicyCfg(policy_id, -),
    Acl.GroupCfg(aclGroupId, groupType, -, -, -, -, -, -),
    Acl.GetHpfGroupId(0, aclGroupId, downGroupId).

// total car
%table TotalCar(nsId:int4, pps: int8) { index(0(nsId)) }
CarId(CAR_ID_TOTAL_CAR, 0, -) :- TotalCar(0, -).
CAP_TABLE_CAR(carId, HPF_CAP_TABLE_CAR_PPS,
    0, 0, 0, pps, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
    CarId(CAR_ID_TOTAL_CAR, -, carId),
    TotalCar(0, pps).
CAP_TABLE_HSEC_TOTALCAR(carId) :- CarId(CAR_ID_TOTAL_CAR, -, carId).

%function InitTotalCar(id: int1) { access_kv(capv5), access_delta(TotalCar) }
null0(0):- tbl_init(-), InitTotalCar(0).

// session car
CAP_TABLE_CAR(carId, HPF_CAP_TABLE_CAR_PPS,
    0, 0, 0, SESSION_CAR_PPS, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
    CarId(CAR_ID_SESSION, -, carId).
null0(0) :- tbl_init(-), GenCarId(CAR_ID_SESSION, 5).
CAP_TABLE_HSEC_SESSCAR_CARID(index, carId) :- CarId(CAR_ID_SESSION, index, carId).

hpf_dns_global_deny_req_from_wan_enable(noUse) :-
    Hsec.DnsRequestDenyFromWan(noUse).
}
