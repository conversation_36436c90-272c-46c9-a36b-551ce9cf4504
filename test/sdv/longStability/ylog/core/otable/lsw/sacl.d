/*
 * 处理用户配置sacl下lswm，只有s310s/s220需要使用此文件
 * 思路：
 * 1.将配置group/rule拆成芯片可以接收的rule，这个在acl_cfg.d中完成
 * 2.将sacl引用的acl展成rule，相互复制
 * 3.对复制后的rule聚合策略及参数
 * 4.将rule在芯片acl类别上分组，聚合在一组的rule生成独立的localIndex，作为芯片acl的组内优先级，再根据其生成芯片acl id
 * 5.计算rule可能存在的每种业务
 * 6.将rule下给lswm
*/

namespace LswOda {

// 根据业务配置产生一个业务id，主要用于与NCTL对接统计id
%resource TrafficParamsToMultiAclGroup(policyType: int1, direction: int1, protoFamily: int1, appType: int1, appValue: int4 -> multiAclGroupId: int4) { sequential(max_size(100000)) }
TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, -) :-
    Sacl.TrafficInstance(policyType, direction, protoFamily, appType, appValue, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).

// 记录被引用的acl
%table GroupActive(vrId: int4, aclGroupId: int4)
GroupActive(vrId, aclL2GroupId) :-
    Acl.GroupCfg(aclL2GroupId, -, -, -, -, -, vrId, -),
    Sacl.TrafficInstance(-, -, -, -, -, aclL2GroupId, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, vrId, -, -, -, -, -, -).
GroupActive(vrId, aclL4GroupId) :-
    Acl.GroupCfg(aclL4GroupId, -, -, -, -, -, vrId, -),
    Sacl.TrafficInstance(-, -, -, -, -, -, aclL4GroupId, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, vrId, -, -, -, -, -, -).

// 感知acl变化后翻转
%table GroupChange(vrId: int4, g: int4, flip: int4) { transient(field(flip)) } 
GroupChange(vrId, aclGroupId, 0) :- GroupActive(vrId, aclGroupId).
GroupChange(vrId, aclGroupId, 1) :- GroupChangeMark(vrId, aclGroupId).

// 将sacl引用的acl展成rule，相互复制
// 在soho上，sacl可以引用L2ACL和L4ACL，它们为两条独立的acl，不是并且关系
%function GetLswAclTypeForSacl(direction:int1, appType:int1, isVlanif: int1 -> aclType: int1, groupType: int1)
%table SaclRule(aclGroupId: int4, aclIndex: int4, priority: int4, order: int4, aclType: int1, groupType: int1,
    policyType:int1, direction:int1, protoFamily:int1, appType:int1, appValue:int4, 
    actionType:int4, actionValue:int4,
    cir:int4, pir:int4, cbs:int4, pbs:int4,
    greenAction:int1, yellowAction:int1, redAction:int1)
// 得到接口是否是vlanif
%table SaclAppType(appType:int1, appValue:int4, isVlanif: int1)
SaclAppType(HPF_SACL_APP_TYPE_GLOBAL, appValue, 0) :- TrafficParamsToMultiAclGroup(-, -, -, HPF_SACL_APP_TYPE_GLOBAL, appValue, -).
SaclAppType(HPF_SACL_APP_TYPE_FWDIF, brIfIndex, 1) :- 
    TrafficParamsToMultiAclGroup(-, -, -, HPF_SACL_APP_TYPE_FWDIF, brIfIndex, -),
    AllPhyPort(brIfIndex, -, -, PORT_TYPE_VLAN).
SaclAppType(HPF_SACL_APP_TYPE_FWDIF, ifIndex, 0) :- 
    TrafficParamsToMultiAclGroup(-, -, -, HPF_SACL_APP_TYPE_FWDIF, ifIndex, -),
    AllPhyPort(ifIndex, -, -, ifType), NotEqual1(ifType, PORT_TYPE_VLAN).
// 得到sacl与配置rule间的配对关系
SaclRule(aclL2GroupId, aclIndex, priority, order, aclType, groupType,
        policyType, direction, protoFamily, appType, appValue, 
        actionType, actionValue, cir, pir, cbs, pbs, greenAction, yellowAction, redAction) :-
    Sacl.TrafficInstance(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, -, -, 
        actionType, actionValue, cir, pir, cbs, pbs, greenAction, -, -, yellowAction, -, -, redAction, -, -, vrId, -, -, -, -, -, order),
    AclRuleForLsw(aclL2GroupId, aclIndex, priority, -, ACL_LSW_TYPE_ETH, -, -, -, -),
    SaclAppType(appType, appValue, isVlanif),
    GetLswAclTypeForSacl(direction, appType, isVlanif, aclType, groupType),
    ChangeGroupMark(vrId, aclL2GroupId).
SaclRule(aclL4GroupId, aclIndex, priority, order, aclType, groupType,
        policyType, direction, protoFamily, appType, appValue, 
        actionType, actionValue, cir, pir, cbs, pbs, greenAction, yellowAction, redAction) :-
    Sacl.TrafficInstance(policyType, direction, protoFamily, appType, appValue, -, aclL4GroupId, -, 
        actionType, actionValue, cir, pir, cbs, pbs, greenAction, -, -, yellowAction, -, -, redAction, -, -, vrId, -, -, -, -, -, order),
    AclRuleForLsw(aclL4GroupId, aclIndex, priority, -, ACL_LSW_TYPE_ADV4, -, -, -, -),
    SaclAppType(appType, appValue, isVlanif),
    GetLswAclTypeForSacl(direction, appType, isVlanif, aclType, groupType),
    ChangeGroupMark(vrId, aclL4GroupId).

// 将rule的策略聚合在一起
// 由于策略被聚合，所以配置rule仅在方向/端口/全局维度上被复制
// 在HPF，由于group可以在5元组确定的业务上被复用，所以group要在所有引用它的5元组上展开(复制)，在HPF各业务独立执行
// 在芯片，由于rule可以在端口和方向上被复用，所以rule要在所有引用它的端口和方向上展开(复制)，引用这条rule的业务会被合并，在芯片一并执行
// HPF: app1(rule1, rule2), app2(rule1, rule2), 基于业务复制和分组打包group/rule，HPF里使用打包的group/rule逐个做业务
// LSW: rule1(app1, app2),  rule2(app1, app2),  基于rule复制和分组打包业务，芯片匹配rule时做所有打包的业务
%table SaclRulePolicy(aclGroupId: int4, aclIndex: int4, priority: int4, order: int4, aclType: int1, groupType: int1,
    direction:int1, appType:int1, appValue:int4, protoFamily:int1, 
    filter: int1, filterStatEn: int1,
    limit: int1, cir:int4, pir:int4, cbs:int4, pbs:int4, greenAction:int2, yellowAction:int2, redAction:int2,
    redirect: int1, redirectType: int1, redirectValue: int4,
    remark: int1, remarkType: int1, remarkValue: int1,
    statistics: int1,
    mirror: int1, mirrorId: int4,
    chgLocalPri: int1, qIndex: int1)
%aggregate AggSaclPolicy(policyType:int1,
    actionType:int4, actionValue:int4,
    cir:int4, pir:int4, cbs:int4, pbs:int4,
    greenAction:int1, yellowAction:int1, redAction:int1
    -> filter: int1, filterStatEn: int1,
    limit: int1, cirO:int4, pirO:int4, cbsO:int4, pbsO:int4, greenActionO:int2, yellowActionO:int2, redActionO:int2,
    redirect: int1, redirectType: int1, redirectValue: int4,
    remark: int1, remarkType: int1, remarkValue: int1,
    statistics: int1, 
    mirror: int1, mirrorId: int4,
    chgLocalPri: int1, qIndex: int1)
SaclRulePolicy(aclGroupId, aclIndex, priority, order,  aclType, groupType,  direction, appType, appValue,  protoFamily, 
        filter, filterStatEn,
        limit, cirO, pirO, cbsO, pbsO, greenActionO, yellowActionO, redActionO,
        redirect, redirectType, redirectValue,
        remark, remarkType, remarkValue,
        statistics,
        mirror, mirrorId, 
        chgLocalPri, qIndex) :-
    SaclRule(aclGroupId, aclIndex, priority, order,  aclType, groupType,
        policyType, direction, protoFamily, appType, appValue, 
        actionType, actionValue,
        cir, pir, cbs, pbs,
        greenAction, yellowAction, redAction) GROUP-BY (aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, protoFamily)
    AggSaclPolicy(policyType,   actionType, actionValue,   cir, pir, cbs, pbs,   greenAction, yellowAction, redAction,
        filter, filterStatEn,
        limit, cirO, pirO, cbsO, pbsO, greenActionO, yellowActionO, redActionO,
        redirect, redirectType, redirectValue,
        remark, remarkType, remarkValue,
        statistics, 
        mirror, mirrorId, 
        chgLocalPri, qIndex).

// 按芯片ACL维度(aclType+groupType)对rule分类，并根据group+rule配置优先级生成rule在芯片ACL类型里的localIndex，用于表示rule在芯片上的优先级
// 同时产生rule在芯片上的acl id
// 在端口/全局上复制rule时，只能使用 group.order + rule.priority 保证相对顺序正确
%table SaclLswShortRule(aclType: int1, groupType: int1, aclGroupId: int4, aclIndex: int4, priority: int4, order: int4, direction:int1, appType: int1, appValue: int4)
%table SaclLswRuleLocalIndex(aclType: int1, groupType: int1, localIndex: int4, 
    aclGroupId: int4, aclIndex: int4, priority: int4, order: int4, direction:int1, appType:int1, appValue:int4)
%aggregate AggLswAclType(aclGroupId: int4, aclIndex: int4, priority: int4, order: int4, direction:int1, appType:int1, appValue:int4
    -> aclGroupIdO: int4, aclIndexO: int4, priorityO: int4, orderO: int4, directionO: int1, appTypeO: int1, appValueO: int4, localIndex: int4) { ordered, many_to_many } 
SaclLswShortRule(aclType, groupType, aclGroupId, aclIndex, priority, order,  direction, appType, appValue) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order,  aclType, groupType,  direction, appType, appValue,  -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupIdO, aclIndexO, priorityO, orderO, directionO, appTypeO, appValueO) :-
    SaclLswShortRule(aclType, groupType, aclGroupId, aclIndex, priority, order, direction, appType, appValue) GROUP-BY (aclType, groupType)
    AggLswAclType(aclGroupId, aclIndex, priority, order, direction, appType, appValue,
        aclGroupIdO, aclIndexO, priorityO, orderO, directionO, appTypeO, appValueO, 
        localIndex).
AclId(aclType, groupType, localIndex, -) :- SaclLswRuleLocalIndex(aclType, groupType, localIndex, -, -, -, -, -, -, -).

// 得到每条rule的方向和端口
// 端口有4种情况：全局，面板口，eth-trunk口，Vlanif口
// 应用在全局的sacl要下发给所有芯片
// 注意这里及之后的代码，不能再复制rule了
%table SaclDirPort(lswAclId: int4, direction:int1, unit: int1, portOrTrunkId: int1, portMatchMask: int4, vlanId: int2, vlanifMac: byte6)
SaclDirPort(lswAclId, direction, unit, 0, LSW_ACL_MATCH_MASK_FIELD_GLOBAL, 0, "0x00") :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, HPF_SACL_APP_TYPE_GLOBAL, appValue, 
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, HPF_SACL_APP_TYPE_GLOBAL, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    Units(unit).
SaclDirPort(lswAclId, direction, unit, port, LSW_ACL_MATCH_MASK_FIELD_PORT, 0, "0x00") :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, HPF_SACL_APP_TYPE_FWDIF, ifIndex, 
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, HPF_SACL_APP_TYPE_FWDIF, ifIndex),
    AclId(aclType, groupType, localIndex, lswAclId),
    AllPhyPort(ifIndex, unit, port, PORT_TYPE_PHY), Ifm.IfLink(ifIndex, -, 1, 1).
SaclDirPort(lswAclId, direction, unit, trunkId1, LSW_ACL_MATCH_MASK_FIELD_SRC_TRUNK, 0, "0x00") :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, HPF_SACL_APP_TYPE_FWDIF, ethTrunkIfIndex, 
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, HPF_SACL_APP_TYPE_FWDIF, ethTrunkIfIndex),
    AclId(aclType, groupType, localIndex, lswAclId),
    Ifm.EthTrunkMembers(memIfIndex, trunkId, -, ethTrunkIfIndex, -), I4ToI1(trunkId, trunkId1),
    Ifm.IfLink(memIfIndex, -, 1, 1),
    PhyPort(memIfIndex, unit, -).
SaclDirPort(lswAclId, direction, unit, 0, LSW_ACL_MATCH_MASK_L2_VLANID, vlanId, mac) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, HPF_SACL_APP_TYPE_FWDIF, vlanifIndex, 
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, HPF_SACL_APP_TYPE_FWDIF, vlanifIndex),
    AclId(aclType, groupType, localIndex, lswAclId),
    AllPhyPort(vlanifIndex, unit, -, PORT_TYPE_VLAN), Ifm.IfNet(vlanifIndex, -, -, -, 1),
    BR.Vlan(-, -, vlanId, -, -, -, vlanifIndex, -, -),
    Ifm.IfPhy(vlanifIndex, -, -, -, -, -, -, -, -, mac, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).

// 报文过滤 traffic-filler
%table SaclFilter(lswAclId: int4, valid: int1)
SaclFilter(lswAclId, filterValid) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, filterValid, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId).

// 流量统计 traffic-statistics
// 申请统计id和下发统计表，用于流量统计traffic-statistics和报文过滤traffic-filler。编码完成后移到stat.d
// 只有流量统计和报文过滤统计需要分配统计id并给lsw下发统计表
%function GetStatTypeForSacl(direction:int1, limitEn: int1 -> statType: int1)
%function CheckNeedStat(filterStatEn: int1, statistics: int1)
StatId(lswAclId, statType, -) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, -, -, -, -, filterStatEn, limitEn, -, -, -, -, -, -, -, -, -, -, -, -, -, statistics, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, -, -, -),
    AclId(aclType, groupType, localIndex, lswAclId),
    GetStatTypeForSacl(direction, limitEn, statType),
    CheckNeedStat(filterStatEn, statistics).
Stat(unit, statType, statId) :- StatId(-, statType, statId), Units(unit).
// 给每条lswAcl匹配上一条statId，不需要做stat业务的valid标记为0，用于生成lswAcl的actionMask
%table SaclStatId(lswAclId: int4, statId: int4, valid: int1)
SaclStatId(lswAclId, statId, 1) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, filterStatEn, limitEn, -, -, -, -, -, -, -, -, -, -, -, -, -, statistics, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    GetStatTypeForSacl(direction, limitEn, statType),
    CheckNeedStat(filterStatEn, statistics),
    StatId(lswAclId, statType, statId).
SaclStatId(lswAclId, 0, 0) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, 0, -, -, -, -, -, -, -, -, -, -, -, -, -, -, 0, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId).
// 向NCTL提供sacl实例
sacl_inst_ext(appType, direction, policyType, 0, appValue,
    0, groupStage, protoFamily, g2, ifType,
    applyVlan, ifVlanId, 0, 0, actionType,
    actionValue, mgid) :-
    Sacl.TrafficInstance(
        policyType, direction, protoFamily1, appType, appValue,
        g2, -, -, actionType, actionValue, SACL_TRFC_CAR_IGNORE_FIELDS, -, -, groupStage, ifType, applyVlan, ifVlanId, -),
    CmpInt4(g2, 0, 5), // g2 != 0
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily1, appType, appValue, mgid),
    I1ToI4(protoFamily1, protoFamily).
sacl_inst_ext(appType, direction, policyType, 0, appValue,
    0, groupStage, protoFamily, g4, ifType,
    applyVlan, ifVlanId, 0, 0, actionType,
    actionValue, mgid) :-
    Sacl.TrafficInstance(
        policyType, direction, protoFamily1, appType, appValue,
        -, g4, -, actionType, actionValue, SACL_TRFC_CAR_IGNORE_FIELDS, -, -, groupStage, ifType, applyVlan, ifVlanId, -),
    CmpInt4(g4, 0, 5), // g4 != 0
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily1, appType, appValue, mgid),
    I1ToI4(protoFamily1, protoFamily).
// 同HPF侧保持一致，使用固定算法产生multiAclGroupId
%function GenerateNctlSaclInstId(multiAclGroupId: int4 -> multiAclGroupIdEx: int4)
sacl_rule_status(multiAclGroupIdEx, g2, aclIndex, 0, statId, 1) :-
    Sacl.TrafficInstance(policyType, direction, protoFamily, appType, appValue, g2, -, -, -, -, SACL_TRFC_CAR_IGNORE_FIELDS, -, -, -, -, -, -, -),
    CmpInt4(g2, 0, 5), // g2 != 0
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, multiAclGroupId),
    GenerateNctlSaclInstId(multiAclGroupId, multiAclGroupIdEx),
    SaclRulePolicy(g2, aclIndex, priority, order, aclType, groupType, direction, appType, appValue,  protoFamily, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, g2, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    SaclStatId(lswAclId, statId, 1).
sacl_rule_status(multiAclGroupIdEx, g4, aclIndex, 0, statId, 1) :-
    Sacl.TrafficInstance(policyType, direction, protoFamily, appType, appValue, -, g4, -, -, -, SACL_TRFC_CAR_IGNORE_FIELDS, -, -, -, -, -, -, -),
    CmpInt4(g4, 0, 5), // g4 != 0
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, multiAclGroupId),
    GenerateNctlSaclInstId(multiAclGroupId, multiAclGroupIdEx),
    SaclRulePolicy(g4, aclIndex, priority, order, aclType, groupType, direction, appType, appValue,  protoFamily, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, g4, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    SaclStatId(lswAclId, statId, 1).

// 流量监管 traffic-limit
// 申请carId和下发car表，用于流量监管traffic-limit。编码完成后移到car.d
// 流量监管需要向lswm下发car表
// 流量监管不需要做统计
// 2025芯片只支持car后remark local-pri + drop-pri，不支持car后remark dscp/8021p
%function GetCarTypeForSacl(direction:int1 -> carType: int1)
CarId(carType, mgid, 0, -) :- 
    TrafficParamsToMultiAclGroup(HPF_ACL_TRAFFIC_LIMIT, direction, -, -, -, mgid),
    GetCarTypeForSacl(direction, carType).
Car(unit, carId, carType, 0, carAttr) :- 
    SaclRulePolicy(-, -, -, -, -, -, direction, appType, appValue, protoFamily, -, -, 1, cir, pir, cbs, pbs, gAct, yAct, rAct, -, -, -, -, -, -, -, -, -, -, -),
    TrafficParamsToMultiAclGroup(HPF_ACL_TRAFFIC_LIMIT, direction, protoFamily, appType, appValue, mgid),
    GetCarTypeForSacl(direction, carType),
    CarId(carType, mgid, 0, carId),
    EncodeCarAttr(LSW_CAR_MODE_TRTCM, LSW_CAR_COLOR_BLIND, 0, 0, 0, cir, pir, cbs, pbs, 0, 0, gAct, 0, 0, yAct, 0, 0, rAct, 0, 0, carAttr),
    Units(unit).
// 给每条lswAcl匹配上一条carId，不需要做car业务的valid标记为0，用于生成lswAcl的actionMask
%table SaclCarIdTmp(lswAclId: int4, carId: int4)
SaclCarIdTmp(lswAclId, carId) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, protoFamily, -, -, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    GetCarTypeForSacl(direction, carType),
    TrafficParamsToMultiAclGroup(HPF_ACL_TRAFFIC_LIMIT, direction, protoFamily, appType, appValue, mgid),
    CarId(carType, mgid, 0, carId).
%table SaclCarId(lswAclId: int4, carId: int4, valid: int1)
SaclCarId(lswAclId, carId, 1) :- SaclCarIdTmp(lswAclId, carId).
SaclCarId(lswAclId, 0, 0) :- AclId(LSW_ACL_TYPE_IACL, -, -, lswAclId), NOT SaclCarIdTmp(lswAclId, -).
SaclCarId(lswAclId, 0, 0) :- AclId(LSW_ACL_TYPE_EACL, -, -, lswAclId), NOT SaclCarIdTmp(lswAclId, -).

// 用于定位
CarT(4, mgid, 0, unit, carId, carType, 0, carAttr) :- 
    SaclRulePolicy(-, -, -, -, -, -, direction, appType, appValue, protoFamily, -, -, 1, cir, pir, cbs, pbs, gAct, yAct, rAct, -, -, -, -, -, -, -, -, -, -, -),
    TrafficParamsToMultiAclGroup(HPF_ACL_TRAFFIC_LIMIT, direction, protoFamily, appType, appValue, mgid),
    GetCarTypeForSacl(direction, carType),
    CarId(carType, mgid, 0, carId),
    EncodeCarAttr(LSW_CAR_MODE_TRTCM, LSW_CAR_COLOR_BLIND, 0, 0, 0, cir, pir, cbs, pbs, 0, 0, gAct, 0, 0, yAct, 0, 0, rAct, 0, 0, carAttr),
    Units(unit).

// 重标记 traffic-remark
%table SaclRemark(lswAclId: int4, remarkDscp: int1, newDscp: int1, remark8021p: int1, new8021p: int1)
%function GetRemarkValue(remark: int1, remarkType: int1, remarkValue: int1 -> remarkDscp: int1, newDscp: int1, remark8021p: int1, new8021p: int1)
SaclRemark(lswAclId, remarkDscp, newDscp, remark8021p, new8021p) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, -, -, -, -, -, -, -, -, -, -, -, -, remark, remarkType, remarkValue, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    GetRemarkValue(remark, remarkType, remarkValue, remarkDscp, newDscp, remark8021p, new8021p).

// 重定向 traffic-redirect
// 支持两个场景：重定向到下一跳；重定向的出接口且NCTL指定了出接口对应的下一跳
// 由于2025不支持多一跳ecmp/ucmp，所以单纯的重定向到出接口在2025上没有意义，不用支持
%table SaclRedirectNhp(lswAclId: int4, nhpIdx: int4, valid: int1)
%table SaclRedirectNhpT(lswAclId: int4, nhpIdx: int4)
SaclRedirectNhpT(lswAclId, nhpIdx) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, -, -, -, -, -, -, -, -, -, 1, HPF_ACL_REDIRECT_TYPE_IPNEXTHP, nxtHop, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    ArpT(-, -, -, -, nxtHop, nhpIdx, -, -, -).
SaclRedirectNhp(lswAclId, nhpIdx, 1) :- SaclRedirectNhpT(lswAclId, nhpIdx).
SaclRedirectNhp(lswAclId, 0, 0) :- 
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    NOT SaclRedirectNhpT(lswAclId, -).

// 流镜像
%table SaclMirror(lswAclId: int4, mirrorId: int4, valid: int1)
SaclMirror(lswAclId, mirrorId, valid) :- 
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, valid, mirrorId, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId).

// 将报文的（外部）优先级映射到芯片的内部队列优先级
%table SaclChgLocalPri(lswAclId: int4, qIndex: int1, valid: int1)
SaclChgLocalPri(lswAclId, qIndex, valid) :- 
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, valid, qIndex),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId).

// 将所有业务聚合在lsw rule上
%function ProcMatch(direction: int1, vlanId: int2, vlanifMac: byte6, l2: byte40, portMatchMask: int4, ruleMatchMask: int4 -> l2O: byte40, matchMask: int4)
%function GetActionMaskForSacl(filterValid: int1, statValid: int1, carValid: int1, remarkDscpValid: int1, remark8021pValid: int1, redirectNhpValid: int1, mirrorValid: int1, chgLocalPri: int1 -> actionMask: int4)
Acl(unit, lswAclId, aclType, groupType, localIndex, direction, matchMask, l2Out, l3, l4, "0x00", 0, portOrTrunkId, "0x00", actionMask, 0, carId, statId, newDscp, new8021p, nhpIdx, 0, mirrorId, 0, qIndex) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    SaclDirPort(lswAclId, direction, unit, portOrTrunkId, portMatchMask, vlanId, vlanifMac),
    AclRuleForLsw(aclGroupId, aclIndex, priority, -, -, ruleMatchMask, l2, l3, l4),
    ProcMatch(direction, vlanId, vlanifMac, l2, portMatchMask, ruleMatchMask, l2Out, matchMask),
    SaclFilter(lswAclId, filterValid),
    SaclStatId(lswAclId, statId, statValid),
    SaclCarId(lswAclId, carId, carValid),
    SaclRemark(lswAclId, remarkDscpValid, newDscp, remark8021pValid, new8021p),
    SaclRedirectNhp(lswAclId, nhpIdx, redirectNhpValid),
    SaclMirror(lswAclId, mirrorId, mirrorValid),
    SaclChgLocalPri(lswAclId, qIndex, chgLocalPri),
    GetActionMaskForSacl(filterValid, statValid, carValid, remarkDscpValid, remark8021pValid, redirectNhpValid, mirrorValid, chgLocalPri, actionMask),
    GroupChange(-, aclGroupId, -).

// 等稳定后删除
AclT(123, 456, unit, lswAclId, aclType, groupType, localIndex, direction, matchMask, l2Out, l3, l4, "0x00", 0, portOrTrunkId, "0x00", actionMask, 0, carId, statId, newDscp, new8021p, nhpIdx, 0, mirrorId, 0, qIndex) :-
    SaclRulePolicy(aclGroupId, aclIndex, priority, order, aclType, groupType, direction, appType, appValue, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    SaclLswRuleLocalIndex(aclType, groupType, localIndex, aclGroupId, aclIndex, priority, order, direction, appType, appValue),
    AclId(aclType, groupType, localIndex, lswAclId),
    SaclDirPort(lswAclId, direction, unit, portOrTrunkId, portMatchMask, vlanId, vlanifMac),
    AclRuleForLsw(aclGroupId, aclIndex, priority, -, -, ruleMatchMask, l2, l3, l4),
    ProcMatch(direction, vlanId, vlanifMac, l2, portMatchMask, ruleMatchMask, l2Out, matchMask),
    SaclFilter(lswAclId, filterValid),
    SaclStatId(lswAclId, statId, statValid),
    SaclCarId(lswAclId, carId, carValid),
    SaclRemark(lswAclId, remarkDscpValid, newDscp, remark8021pValid, new8021p),
    SaclRedirectNhp(lswAclId, nhpIdx, redirectNhpValid),
    SaclMirror(lswAclId, mirrorId, mirrorValid),
    SaclChgLocalPri(lswAclId, qIndex, chgLocalPri),
    GetActionMaskForSacl(filterValid, statValid, carValid, remarkDscpValid, remark8021pValid, redirectNhpValid, mirrorValid, chgLocalPri, actionMask),
    GroupChange(-, aclGroupId, -).
}
