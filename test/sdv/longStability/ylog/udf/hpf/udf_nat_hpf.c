/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: fwdif tbm udf.
 * Author: h2d project team
 * Create: 2023-4-1
 */

#include "ylog_Hpf_name_space_ormg.h"
#include "udf_common.h"
#include "udf_nat.h"
#include "udf_utils.h"
#include "sess.h"
#include "svc.h"

int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy(uint32_t op, OrmgHpfHpfNatPolicyT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_address_mask(uint32_t op, OrmgHpfHpfNatPolicyAddressMaskT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_address_masklen(uint32_t op, OrmgHpfHpfNatPolicyAddressMasklenT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_address_range(uint32_t op, OrmgHpfHpfNatPolicyAddressRangeT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_service(uint32_t op, OrmgHpfHpfNatPolicyServiceT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_interface(uint32_t op, OrmgHpfHpfNatPolicyInterfaceT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_action(uint32_t op, OrmgHpfHpfNatPolicyActionT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_action_staticMap(uint32_t op, OrmgHpfHpfNatPolicyActionStaticMapT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_action_dnat(uint32_t op, OrmgHpfHpfNatPolicyActionDnatT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_policy_action_nonat(uint32_t op, OrmgHpfHpfNatPolicyActionNonatT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_server(uint32_t op, OrmgHpfHpfNatServerT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_pool_group(uint32_t op, OrmgHpfHpfNatPoolGroupT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_pool_section(uint32_t op, OrmgHpfHpfNatPoolSectionT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_pool_exclude_ip(uint32_t op, OrmgHpfHpfNatPoolExcludeIpT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_pool_exclude_port(uint32_t op, OrmgHpfHpfNatPoolExcludePortT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_pool_group_type(uint32_t op, OrmgHpfHpfNatPoolGroupTypeT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_arp_enable(uint32_t op, OrmgHpfHpfNatArpEnableT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_arp_pool_section(uint32_t op, OrmgHpfHpfNatArpPoolSectionT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_arp_pool_exclude(uint32_t op, OrmgHpfHpfNatArpPoolExcludeT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_host_arp(uint32_t op, OrmgHpfHpfNatHostArpT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_aspf_config(uint32_t op, OrmgHpfHpfNatAspfConfigT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_aging_time(uint32_t op, OrmgHpfHpfNatAgingTimeT *tuple)
{
    return GMERR_OK;
}
int32_t dtl_tbm_tbl_Hpf_hpf_nat_interface_enable(uint32_t op, OrmgHpfHpfNatInterfaceEnableT *tuple)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_Hpf_hpf_nat_interface_aspf_svc(uint32_t op, OrmgHpfHpfNatInterfaceAspfSvcT *tuple)
{
    return GMERR_OK;
}
