

#include "subLongWriteClientTest.h"

void sub_callback_with_sub_ip4foward_newrollback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);

            switch (info->msgType) {
                case 1: {
                    //默认推
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_AGED: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            read_sub_ip4foward_by_get(subStmt);
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            read_sub_ip4foward_by_get(subStmt);
                            break;
                        }
                        default: {
                            printf("[cltSubChann008]default: invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }

                case 2:  //推送new object
                {
                    //默认推送new object和old object
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            read_sub_ip4foward_by_get(subStmt);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            read_sub_ip4foward_by_get(subStmt);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            read_sub_ip4foward_by_get(subStmt);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("[cltSubChann008]default: invalid eventType:%d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    printf("[cltSubChann008]default: invalid msgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sub_callback_with_customer_newrollback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    GmcNodeT *root = NULL, *nodeA = NULL, *nodeV = NULL;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);

            //推送new object和old object
            switch (info->msgType) {
                case 1: {
                    //默认推
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_AGED: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            customer_get_node(subStmt, &root, &nodeA, &nodeV);
                            read_sub_customer_by_get(root);
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            customer_get_node(subStmt, &root, &nodeA, &nodeV);
                            read_sub_customer_by_get(root);
                            break;
                        }
                        default: {
                            printf("[cltSubChann008]default: invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }

                case 2:  //推送new object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            customer_get_node(subStmt, &root, &nodeA, &nodeV);
                            read_sub_customer_by_get(root);
                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            customer_get_node(subStmt, &root, &nodeA, &nodeV);
                            read_sub_customer_by_get(root);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            customer_get_node(subStmt, &root, &nodeA, &nodeV);
                            read_sub_customer_by_get(root);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("[cltSubChann008]default: invalid eventType:%d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    printf("[cltSubChann008]default: invalid msgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sub_callback_with_cib_newrollback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 1: {
                    //默认推
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_AGED: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            cib_get_node(subStmt, &root, &node1, &node2);
                            read_sub_cib_by_get(root);
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            cib_get_node(subStmt, &root, &node1, &node2);
                            read_sub_cib_by_get(root);
                            break;
                        }
                        default: {
                            printf("[cltSubChann008]default: invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 2:  //推送new object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            cib_get_node(subStmt, &root, &node1, &node2);
                            read_sub_cib_by_get(root);
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            cib_get_node(subStmt, &root, &node1, &node2);
                            read_sub_cib_by_get(root);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            TEST_EXPECT_INT32(GMERR_OK, ret);
                            cib_get_node(subStmt, &root, &node1, &node2);
                            read_sub_cib_by_get(root);
                            break;
                        }
                        default: {
                            printf("[cltSubChann008]default: invalid eventType:%d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    printf("[cltSubChann008]default: invalid msgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

int main(int argc, char **argv)
{
    int ret = 0;
    int32_t numN = 0;
    if (argv[1]) {
        numN = atoi(argv[1]);
    }
    ret = testEnvInit();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = create_epoll_thread();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char *sub_info = NULL;
    int chanRingLen = 64;
    char subConnName[MAX_SUB_CONN_LEN] = {0};
    sprintf(subConnName, "subNewdataRollback%d", numN);
    GmcStmtT *stmt_sub = NULL;
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub1 = NULL;
    GmcConnT *testSubConn1 = NULL;
    GmcStmtT *stmt_sub2 = NULL;
    GmcConnT *testSubConn2 = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char subName[MAX_SUB_NAME_LEN] = {0};
    sprintf(subName, "sub_ip4forwardNewRollback%d", numN);
    char subName1[MAX_SUB_NAME_LEN] = {0};
    sprintf(subName1, "customerNewRollback%d", numN);
    char subName2[MAX_SUB_NAME_LEN] = {0};
    sprintf(subName2, "cibNewRollback%d", numN);
    SnUserDataT *user_data = NULL;
    SnUserDataT *user_data1 = NULL;
    SnUserDataT *user_data2 = NULL;

    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    //创建订阅连接
    ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    readJanssonFile("schemaFile/sub_ip4forward_subnewrollback.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    //订阅sub_ip4foward事件
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    GmcUnSubscribe(stmt, subName);
    ret = GmcSubscribe(stmt, &tmp_sub_info, testSubConn, sub_callback_with_sub_ip4foward_newrollback, user_data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(sub_info);

    readJanssonFile("schemaFile/customer_subnewrollback.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    user_data1 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data1, 0, sizeof(SnUserDataT));
    //订阅customer
    tmp_sub_info.subsName = subName1;
    tmp_sub_info.configJson = sub_info;
    GmcUnSubscribe(stmt, subName1);
    ret = GmcSubscribe(stmt, &tmp_sub_info, testSubConn, sub_callback_with_customer_newrollback, user_data1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(sub_info);

    readJanssonFile("schemaFile/cib_subnewrollback.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    user_data2 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data2, 0, sizeof(SnUserDataT));
    //订阅customer
    tmp_sub_info.subsName = subName2;
    tmp_sub_info.configJson = sub_info;
    GmcUnSubscribe(stmt, subName2);
    ret = GmcSubscribe(stmt, &tmp_sub_info, testSubConn, sub_callback_with_cib_newrollback, user_data2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(sub_info);

    while (1) {
        sleep(1);
    }
    ret = GmcUnSubscribe(stmt, subName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(user_data);
    free(user_data1);
    free(user_data2);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = close_epoll_thread();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testEnvClean();
}
