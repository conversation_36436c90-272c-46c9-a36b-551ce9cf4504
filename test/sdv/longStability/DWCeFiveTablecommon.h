/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: DWCeFiveTablecommon.h（带直连写的表项）
 * Author: ywx1157510
 * Create: 2024-11-9
 */
#ifndef DW_CE_FIVE_TABLE_COMMON_H
#define DW_CE_FIVE_TABLE_COMMON_H

#include "t_datacom_lite.h"
#include "ClientSingnoProtect.h"
#include "longStability.h"
#include "NewSubGeneralDMLDw.h"
#include "NewSubSpecialDMLDw.h"

#ifdef __cplusplus
extern "C" {
#endif

#define FILE_PATH 512
#define NAME_LEN 128
#define MAX_CMD_SIZE 1024
#define VERTEX_TOTAL 26
#define OP_RECORD 10000
#define MAX_COUNT_AUTO_LABLE 20000  // 含自增列为主键表写入数据上限

int CreateLabel(const char *path, char *testConfig = NULL)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *testSchema = NULL;
    char config[] = "{\"direct_write\":true}";

    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    readJanssonFile(path, &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    if (testConfig != NULL) {
        ret = GmcCreateVertexLabel(stmt, testSchema, testConfig);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = GmcCreateVertexLabel(stmt, testSchema, config);
    }
    if (ret != GMERR_DUPLICATE_TABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    free(testSchema);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int CreateCeLabels()
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return 0;
#endif
    ret = CreateLabel((char *)"./DWschemafile/test_if.gmjson");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char testConfig[] = "{\"direct_write\":true,\"auto_increment\":1}";
    ret = CreateLabel((char *)"./DWschemafile/test_if_fwd.gmjson", testConfig);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CreateLabel((char *)"./DWschemafile/test_if_lst.gmjson");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CreateLabel((char *)"./DWschemafile/test_frm_inst.gmjson");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CreateLabel((char *)"./DWschemafile/test_mqc_entry_avl.gmjson");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = CreateLabel((char *)"./DWschemafile/test_mqc_entry_avlvar.gmjson");
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 特殊复杂表和一般复杂表
    TestPrepareNewSubLabel();
    return ret;
}

// 直连写 or CS写接口
int TestGmcPrepareStmtByLabelName(
    GmcStmtT *stmt, const char *vertexLabelName, GmcOperationTypeE operationType, int isflag)
{
    if (isflag) {
        return GmcPrepareStmtByLabelName(stmt, vertexLabelName, operationType);
    } else {
        return GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, operationType);
    }
}

void DwOldSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[NAME_LEN] = {0};
    unsigned int labelNameLen = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;
                break;
            } else {
                break;
            }
        } else if (eof) {
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                AW_FUN_Log(
                    LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__, info->eventType);
                break;
            }
        }
    }
}

/****************新增特殊复杂表new_sub_special_dw****************************/
void scanComplexByPk(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag = false, const char *vertexName = "new_sub_special_dw")
{
    int ret = 0;
    bool isFinish = false;
    // scan by primary key
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN, false);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = start; loop < end; loop++) {
        uint32_t tUint64Value = loop;
        uint32_t tUint32Value = loop;
        uint8_t tUint8Value = loop;
        int8_t f2[6];
        memcpy(f2, (int8_t *)"hello", 6);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, f2, sizeof(f2));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
}

void longStabilityAgeCheck(GmcStmtT *stmt, const char *labelName, bool isAbnormal)
{
    int ret = 0;
    int ageTaskCnt = 0;
    ret = DwGetAgeTaskCnt(&ageTaskCnt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 获取当前老化任务数，避免太多处理不过来
    if (ageTaskCnt < AGETASKCNT) {
        ret = GmcBeginCheck(stmt, labelName, 0xff);
        if (ret == GMERR_OK) {
            do {
                ret = GmcEndCheck(stmt, labelName, 0xff, isAbnormal);
                if (ret == GMERR_OK) {
                    usleep(100);
                    break;
                } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                    usleep(100);
                } else {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            if (ret == GMERR_OK) {
                ret = checkAccountStatus(stmt, labelName, 255);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE &&
                   ret != GMERR_TABLE_IN_CHECKING) {
            printf("labelName:%s GmcBeginCheck failed\n", labelName);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        sleep(10);
    }
}
// 含自增列，走直连写配置表 状态合并表
void randow_dml_ComplexCe(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 1, int32_t end = OP_RECORD,
    int isCsflag = false, const char *vertexName = "new_sub_special_dw")
{
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_ComplexCe [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    
    randowNum = rand() % 5;
    // 获取当前表数据量，超过数据量则走指定路径
    uint64_t count = 0;
    (void)GmcGetVertexCount(stmt, vertexName, NULL, &count);
    if (count >= MAX_COUNT_AUTO_LABLE) {
        randowNum = 3;
    }
    if (randowNum == 0) {
        // 结构化写读
        TestInsertSpecialRecord(stmt, start, end);
        TestReadSpecialRecordPk(stmt, start, end);
    } else if (randowNum == 1) {
        // 主键读
        scanComplexByPk(stmt, start, end);
    } else if (randowNum == 2) {
        // 随机主键更新
        TestUpdateSpecialRecordPk(stmt, start, end);
    } else if (randowNum == 3) {
        // localhash索引删除
        TestDeleteSpecialRecordLocal(stmt, start, end);
    } else {
        // 二级localhash索引更新
        TestUpdateSpecialRecordLocalhashUnique(stmt, start, end);
    }
}
/****************新增一般复杂表new_sub_general_dw****************************/
void randow_dml_GeneralCe(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 1, int32_t end = OP_RECORD,
    int isCsflag = false, const char *vertexName = "new_sub_general_dw")
{
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    uint32_t randowNum = 0;
   
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_ComplexCe [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 3;
    // 获取当前表数据量，超过数据量则走指定路径
    uint64_t count = 0;
    (void)GmcGetVertexCount(stmt, vertexName, NULL, &count);
    if (count >= MAX_COUNT_AUTO_LABLE) {
        TestDeleteGeneralRecordLocalhashUnique(stmt, start, end);
    }
    if (randowNum == 0) {
        // 主键更新
        TestInsertGeneralRecord(stmt, start, end);
        TestUpdateGeneralRecordPk(stmt, start, end);
    } else if (randowNum == 1) {
        // 过滤条件查询，更新，删除
        TestScanFilterGeneralRecordLocal(stmt);
        TestUpdateFilterGeneralRecordLocal(stmt, start, end / 100);
        TestDeleteFilterGeneralRecordLocal(stmt, start, end);
    } else {
        // localhash更新
        TestUpdateGeneralRecordLocalhashUnique(stmt, start, end);
    }
}

/**********************test_frm_inst*****************************************/
int setFrmInstPkProperty(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    RETURN_IFERR(GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int setFrmInstProperty(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    uint64_t tUint64Value = i;
    uint16_t tUint16Value = i;
    uint8_t tUint8Value = i;
    RETURN_IFERR(GmcSetVertexProperty(stmt, "ver", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "role", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "start_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "mod_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "mod_remote_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "svc_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_chassis", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_slot", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "logic_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_port", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_ip", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_process", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_cpu", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "mod_num", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "svc_num", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_id", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "obj_ver", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int getFrmInstProperty(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    uint64_t tUint64Value = i;
    uint16_t tUint16Value = i;
    uint8_t tUint8Value = i;
    bool isNull = false;
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "ver", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "role", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "start_type", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "state", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "mod_state", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "mod_remote_state", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "svc_state", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "local_chassis", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "local_slot", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "logic_type", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "local_port", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "local_ip", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "local_process", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "local_cpu", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "mod_num", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "svc_num", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "obj_id", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "obj_ver", &tUint32Value, sizeof(uint32_t), &isNull));
    return 0;
}
int replaceFrmInst(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_frm_inst")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setFrmInstPkProperty(stmt, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setFrmInstProperty(stmt, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
            if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
                ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
#else
            if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
#endif
        }
    }

    return 0;
}

int updateFrmInstByPk(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_frm_inst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = setFrmInstProperty(stmt, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}
int removeFrmInstByPk(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_frm_inst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}
int scanFrmInstByPk(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag = false, const char *vertexName = "test_frm_inst")
{
    int ret = 0;
    bool isFinish = false;
    // scan by primary key
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN, isCsflag);
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = start; loop < end; loop++) {
        uint32_t tUint32Value = loop;
        uint8_t tUint8Value = loop;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                ret = getFrmInstProperty(stmt, loop);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
    return ret;
}
int dwSubFrmInst(GmcStmtT *stmt)
{
    int ret = 0;
    int32_t subNum = 1;
    char *subInfo = NULL;
    char subConnName[46] = "subConnfrmInst";
    GmcStmtT *stmt_sub[46] = {NULL};
    GmcConnT *testSubConn[46] = {NULL};
    char subName[46] = "subVertexfrmInst";
    SnUserDataT *user_data[46] = {NULL};
    EpollThreadDataT subEpoll[46] = {0};
    YangConnOptionT connOptions = {0};
    // 创建订阅连接
    for (int32_t i = 0; i < subNum; i++) {
        ret = create_epoll_thread(&subEpoll[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(subConnName, "ywysubConnfrmInst%d", i + 1);
        connOptions.epollFd = &subEpoll[i].userEpollFd;
        connOptions.connName = subConnName;
        connOptions.needEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = epollRegWithUserdata;
        ret = TestYangGmcConnect(&testSubConn[i], &stmt_sub[i], GMC_CONN_TYPE_SUB, &connOptions);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            sleep(10);
            return 0;
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    char schemaPath[200] = "";
    for (int32_t i = 0; i < subNum; i++) {
        (void)sprintf(schemaPath, "./DWschemafile/Oldsub/test_frm_inst_oldsub%d.gmjson", i + 1);
        (void)sprintf(subName, "subVertexfrminst%d", i + 1);
        readJanssonFile(schemaPath, &subInfo);
        EXPECT_NE((void *)NULL, subInfo);
        user_data[i] = (SnUserDataT *)malloc(sizeof(SnUserDataT));
        if (user_data[i] == NULL) {
            for (--i; i >= 0; i--) {
                free(user_data[i]);
            }
            break;
        }
        memset(user_data[i], 0, sizeof(SnUserDataT));
        // 订阅事件
        GmcSubConfigT tmp_sub_info;
        tmp_sub_info.subsName = subName;
        tmp_sub_info.configJson = subInfo;
        GmcUnSubscribe(stmt, subName);
        ret = GmcSubscribe(stmt, &tmp_sub_info, testSubConn[i], DwOldSnCallBack, user_data[i]);
        if (ret != 0) {
            printf("GmcSubscribe error %d", i + 1);
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(subInfo);
    }

    srand(time(NULL));
    while (1) {
        int times = rand() % 10;
        sleep(times);
        for (int32_t i = 0; i < subNum; i++) {
            user_data[i]->subIndex = 0;
            user_data[i]->insertNum = 0;
            user_data[i]->deleteNum = 0;
            user_data[i]->updateNum = 0;
            user_data[i]->replaceNum = 0;
        }
    }
    for (int32_t i = 0; i < subNum; i++) {
        (void)sprintf(subName, "subVertexfrminst%d", i + 1);
        ret = GmcUnSubscribe(stmt, subName);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        free(user_data[i]);
        // 释放订阅连接
        ret = testSubDisConnect(testSubConn[i], stmt_sub[i]);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}
/**********************test_if*****************************************/
int replaceIf(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint8_t tUint8Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char wrFixed[64] = {0};
        (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "name", GMC_DATATYPE_FIXED, wrFixed, 64));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "if_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "shutdown", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "linkup", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tbtp", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tb", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "port_switch", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "forwardType", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        char macFixed[6] = {0};
        (void)snprintf(wrFixed, 6, "f%.5u", tUint32Value);
        RETURN_IFERR(GmcSetVertexProperty(stmt, "macAddress", GMC_DATATYPE_FIXED, macFixed, 6));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ipv4_mtu", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ipv4_enable", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ipv6_mtu", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ipv6_enable", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "on_board", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lagid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "hppsvcflg", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "error_down", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "speed", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "link_protocol", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "port_group_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

// 获取node
void getIfNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **ifmN, GmcNodeT **devN, GmcNodeT **l2N, GmcNodeT **portN)
{
    GmcNodeT *Root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL, *node4 = NULL, *node5 = NULL;
    *root = NULL;
    *ifmN = NULL;
    *devN = NULL;
    *l2N = NULL;
    *portN = NULL;
    // 获取根节点与子节点
    int ret = GmcGetRootNode(stmt, &Root);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "ifm", &node1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "dev", &node2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "l2", &node3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "port", &node4);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    *root = Root;
    *ifmN = node1;
    *devN = node2;
    *l2N = node3;
    *portN = node4;
}
void setIfPk(GmcStmtT *stmt, uint32_t pkValue)
{
    int ret = 0;
    uint32_t tUint32Value = pkValue;
    ret = GmcSetIndexKeyId(stmt, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void setIfLocalHash(GmcStmtT *stmt, uint32_t pkValue)
{
    int ret = 0;
    uint32_t tUint32Value = pkValue;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    ret = GmcSetIndexKeyId(stmt, 1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, &wrFixed, 64);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void setIfPkProperty(GmcNodeT *node, uint32_t pkValue)
{
    int ret = 0;
    uint32_t ifindex = pkValue;
    ret = GmcNodeSetPropertyByName(node, (char *)"ifindex", GMC_DATATYPE_UINT32, &ifindex, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

int setIfRootNode(GmcNodeT *node, uint32_t index, bool NoName = false)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    if (!NoName) {
        RETURN_IFERR(GmcNodeSetPropertyByName(node, "name", GMC_DATATYPE_FIXED, wrFixed, 64));
    }
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "vrid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "if_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "shutdown", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "linkup", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "tbtp", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "tb", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "tp", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "port_switch", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "forwardType", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    char macFixed[6] = {0};
    (void)snprintf(wrFixed, 6, "f%.5u", tUint32Value);
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "macAddress", GMC_DATATYPE_FIXED, macFixed, 6));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ipv4_mtu", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ipv4_enable", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ipv6_mtu", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ipv6_enable", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "on_board", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "lagid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "hppsvcflg", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "error_down", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "speed", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "link_protocol", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "vrf_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "port_group_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "if_group_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "if_group_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "if_df", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "encap_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "is_subif", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "mainifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "logicTB", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "logicTP", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "vlandomain", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "coreId", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ipv4mss", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ipv6mss", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    char svcFixed[24] = {0};
    (void)snprintf(svcFixed, 6, "f%.12u", tUint32Value);
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, svcFixed, 24));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "status_high_prio", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(
        GmcNodeSetPropertyByName(node, "errcode_high_prio", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "linkState", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "modechannel", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "bandwidth", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ifMode", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ifMode", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "carrier_state", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int setIfIfmNode(GmcNodeT *node, uint32_t index)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "simple_name", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "description", GMC_DATATYPE_FIXED, wrFixed, 64));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "is_configure", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "main_ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "sub_max_num", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "sub_curr_num", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "error_down", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "statistic", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "vsys_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "zone_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "last_up_time", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(
        GmcNodeSetPropertyByName(node, "last_down_time", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int setIfDevNode(GmcNodeT *node, uint32_t index)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "dev_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "chassis_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "slot_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "card_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "unit_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "port_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "port_num", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "supportSlice", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "coreId", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "gltpid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "gltpid_ver", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "logic_portid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "sub_port", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "combine_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    return 0;
}

int setIfL2Node(GmcNodeT *node, uint32_t index)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "trunk_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "vlan_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "l2_portindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "vsi", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "tunnel_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "bd_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "peVlanBgn", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "peVlanEnd", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ceVlanBgn", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "ceVlanEnd", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "segIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "flowIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}
int setIfPortNode(GmcNodeT *node, uint32_t index)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "speed", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "duplex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "flow_control", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "phy_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "jumbo", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "baud", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "rmon", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "phy_link", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "if_mib", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "on_board", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int dwReplaceIf(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, bool isBatch = false,
    const char *vertexName = "test_if")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        getIfNode(stmt, &root, &ifmN, &devN, &l2N, &portN);
        setIfPkProperty(root, i);
        ret = setIfRootNode(root, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfIfmNode(ifmN, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfDevNode(devN, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfL2Node(l2N, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfPortNode(portN, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
        }
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

void DwReplaceIf(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = dwReplaceIf(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
int dwUpdateIfByPk(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, bool isBatch = false,
    const char *vertexName = "test_if")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        getIfNode(stmt, &root, &ifmN, &devN, &l2N, &portN);
        setIfPk(stmt, i);
        ret = setIfRootNode(root, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfIfmNode(ifmN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfDevNode(devN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfL2Node(l2N, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfPortNode(portN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
        }
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            ret = GMERR_OK;
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            ret = GMERR_OK;
        }
#endif
    }

    return ret;
}

void DwUpdateIfByPk(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = dwUpdateIfByPk(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
int dwUpdateIfByLocalHash(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD,
    bool isBatch = false, const char *vertexName = "test_if")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        getIfNode(stmt, &root, &ifmN, &devN, &l2N, &portN);
        setIfLocalHash(stmt, i);
        ret = setIfRootNode(root, i + 1, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfIfmNode(ifmN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfDevNode(devN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfL2Node(l2N, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfPortNode(portN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
        }
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return ret;
}

void DwUpdateIfByLocalHash(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = dwUpdateIfByLocalHash(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

int dwRemoveIfByPK(GmcConnT *conn, GmcStmtT *stmt, int32_t startPk = 0, int32_t endPk = OP_RECORD, int isCsflag = false,
    const char *vertexName = "test_if")
{
    int ret = 0;
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, isCsflag);
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPk; i < endPk; i++) {
        uint32_t tUint32Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }
    return 0;
}
void DwRemoveIfByPK(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = dwRemoveIfByPK(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// if async
int dwReplaceIfAsync(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start, int32_t end, bool isBatch, const char *vertexName = "test_if")
{
    int ret = 0;
    AsyncUserDataT data = {0};
    uint32_t totalNum = 0;
    uint32_t successNum = 0;

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        getIfNode(stmt, &root, &ifmN, &devN, &l2N, &portN);
        setIfPkProperty(root, i);
        ret = setIfRootNode(root, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfIfmNode(ifmN, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfDevNode(devN, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfL2Node(l2N, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfPortNode(portN, i);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testWaitAsyncRecv(&data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY &&
                    data.status != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    TEST_EXPECT_INT32(GMERR_OK, data.status);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            GmcAsyncRequestDoneContextT requestCtx;
            requestCtx.replaceCb = replace_vertex_callback;
            requestCtx.userData = &data;
            ret = GmcExecuteAsync(stmt, &requestCtx);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY &&
                data.status != GMERR_LOCK_NOT_AVAILABLE) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                TEST_EXPECT_INT32(GMERR_OK, data.status);
            }
        }
    }

    return ret;
}

int dwUpdateIfByPkAsync(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start, int32_t end, bool isBatch, const char *vertexName = "test_if")
{
    int ret = 0;
    AsyncUserDataT data = {0};
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        getIfNode(stmt, &root, &ifmN, &devN, &l2N, &portN);
        setIfPk(stmt, i);
        ret = setIfRootNode(root, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfIfmNode(ifmN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfDevNode(devN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfL2Node(l2N, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfPortNode(portN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testWaitAsyncRecv(&data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY &&
                    data.status != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    TEST_EXPECT_INT32(GMERR_OK, data.status);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            GmcAsyncRequestDoneContextT requestCtx;
            requestCtx.replaceCb = update_vertex_callback;
            requestCtx.userData = &data;
            ret = GmcExecuteAsync(stmt, &requestCtx);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY &&
                data.status != GMERR_LOCK_NOT_AVAILABLE) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                TEST_EXPECT_INT32(GMERR_OK, data.status);
            }
        }
    }

    return ret;
}

int dwUpdateIfByLocalHashAsync(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start, int32_t end, bool isBatch, const char *vertexName = "test_if")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    AsyncUserDataT data = {0};
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        getIfNode(stmt, &root, &ifmN, &devN, &l2N, &portN);
        setIfLocalHash(stmt, i);
        ret = setIfRootNode(root, i + 1, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfIfmNode(ifmN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfDevNode(devN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfL2Node(l2N, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = setIfPortNode(portN, i + 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testWaitAsyncRecv(&data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY &&
                    data.status != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    TEST_EXPECT_INT32(GMERR_OK, data.status);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            GmcAsyncRequestDoneContextT requestCtx;
            requestCtx.replaceCb = update_vertex_callback;
            requestCtx.userData = &data;
            ret = GmcExecuteAsync(stmt, &requestCtx);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY &&
                data.status != GMERR_LOCK_NOT_AVAILABLE) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                TEST_EXPECT_INT32(GMERR_OK, data.status);
            }
        }
    }

    return ret;
}

int dwRemoveIfByPKAsync(
    GmcStmtT *stmt, int32_t startPk, int32_t endPk, int isCsflag, const char *vertexName = "test_if")
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, isCsflag);
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPk; i < endPk; i++) {
        uint32_t tUint32Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        GmcAsyncRequestDoneContextT requestCtx;
        requestCtx.replaceCb = delete_vertex_callback;
        requestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &requestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY && data.status != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    return 0;
}

// san if
int getIfRootNode(GmcNodeT *node, uint32_t index, bool NoName = false)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    bool isNull = false;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    if (!NoName) {
        RETURN_IFERR(GmcNodeGetPropertyByName(node, "name", wrFixed, 64, &isNull));
    }
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "vrid", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "if_type", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "shutdown", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "linkup", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "tbtp", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "tb", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "tp", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "port_switch", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "forwardType", &tUint32Value, sizeof(uint32_t), &isNull));
    char macFixed[6] = {0};
    (void)snprintf(wrFixed, 6, "f%.5u", tUint32Value);
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "macAddress", macFixed, 6, &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ipv4_mtu", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ipv4_enable", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ipv6_mtu", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ipv6_enable", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "on_board", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "lagid", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "hppsvcflg", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "error_down", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "speed", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "link_protocol", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "vrf_index", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "port_group_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "if_group_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "if_group_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "if_df", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "encap_type", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "is_subif", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "mainifindex", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "logicTB", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "logicTP", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "vlandomain", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "coreId", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ipv4mss", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ipv6mss", &tUint32Value, sizeof(uint32_t), &isNull));
    char svcFixed[24] = {0};
    (void)snprintf(svcFixed, 6, "f%.12u", tUint32Value);
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "svc_ctx_high_prio", svcFixed, 24, &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "status_high_prio", &tUint8Value, sizeof(uint8_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "errcode_high_prio", &tUint8Value, sizeof(uint8_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "linkState", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "modechannel", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "bandwidth", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ifMode", &tUint8Value, sizeof(uint8_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ifMode", &tUint8Value, sizeof(uint8_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "carrier_state", &tUint32Value, sizeof(uint32_t), &isNull));
    return 0;
}

int getIfIfmNode(GmcNodeT *node, uint32_t index)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    bool isNull = false;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "simple_name", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "description", wrFixed, 64, &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "is_configure", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "main_ifindex", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "sub_max_num", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "sub_curr_num", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "error_down", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "statistic", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "vsys_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "zone_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "last_up_time", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "last_down_time", &tUint32Value, sizeof(uint32_t), &isNull));
    return 0;
}

int getIfDevNode(GmcNodeT *node, uint32_t index)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    bool isNull = false;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "dev_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "chassis_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "slot_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "card_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "unit_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "port_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "port_num", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "supportSlice", &tUint8Value, sizeof(uint8_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "coreId", &tUint8Value, sizeof(uint8_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "gltpid", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "gltpid_ver", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "logic_portid", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "sub_port", &tUint8Value, sizeof(uint8_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "combine_type", &tUint8Value, sizeof(uint8_t), &isNull));
    return 0;
}

int getIfL2Node(GmcNodeT *node, uint32_t index)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    bool isNull = false;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "trunk_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "vlan_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "l2_portindex", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "vsi", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "tunnel_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "bd_id", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "peVlanBgn", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "peVlanEnd", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ceVlanBgn", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "ceVlanEnd", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "segIndex", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "flowIndex", &tUint32Value, sizeof(uint32_t), &isNull));
    return 0;
}
int getIfPortNode(GmcNodeT *node, uint32_t index)
{
    uint32_t tUint32Value = index;
    uint8_t tUint8Value = index;
    uint64_t tUint64Value = index;
    uint16_t tUint16Value = index;
    bool isNull = false;
    char wrFixed[64] = {0};
    (void)snprintf(wrFixed, 64, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "speed", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "duplex", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "flow_control", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "phy_type", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "jumbo", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "baud", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "rmon", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "phy_link", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "if_mib", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "on_board", &tUint32Value, sizeof(uint32_t), &isNull));
    return 0;
}
int dwScanIfByPk(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isCsflag = false,
    const char *vertexName = "test_if")
{
    int ret = 0;
    bool isFinish = false;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL;
    // scan by primary key
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN, isCsflag);
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = start; loop < end; loop++) {
        setIfPk(stmt, loop);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                getIfNode(stmt, &root, &ifmN, &devN, &l2N, &portN);
                ret = getIfRootNode(root, loop + 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = getIfIfmNode(ifmN, loop + 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = getIfDevNode(devN, loop + 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = getIfL2Node(l2N, loop + 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = getIfPortNode(portN, loop + 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
#ifdef DIRECT_WRITE
            if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
                ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
#else
            if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
#endif
        }
    }
    return ret;
}
void DwScanIfByPk(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = dwScanIfByPk(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
int dwSubIf(GmcStmtT *stmt)
{
    int ret = 0;
    const int32_t subNum = 1;
    char *subInfo = NULL;
    char subConnName[46] = "subConnIf1";
    GmcStmtT *stmt_sub[subNum] = {NULL};
    GmcConnT *testSubConn[subNum] = {NULL};
    char subName[46] = "subVertexIf";
    SnUserDataT *user_data[subNum] = {NULL};
    EpollThreadDataT subEpoll[46] = {0};
    YangConnOptionT connOptions = {0};

    // 创建订阅连接
    for (int32_t i = 0; i < subNum; i++) {
        ret = create_epoll_thread(&subEpoll[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(subConnName, "ywysubConnIf%d", i + 1);
        connOptions.epollFd = &subEpoll[i].userEpollFd;
        connOptions.connName = subConnName;
        connOptions.needEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = epollRegWithUserdata;
        ret = TestYangGmcConnect(&testSubConn[i], &stmt_sub[i], GMC_CONN_TYPE_SUB, &connOptions);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            sleep(10);
            return 0;
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    char schemaPath[200] = "";
    for (int32_t i = 0; i < subNum; i++) {
        (void)sprintf(schemaPath, "./DWschemafile/Oldsub/test_if_sub%d.gmjson", i + 1);
        (void)sprintf(subName, "subVertexIf%d", i + 1);
        readJanssonFile(schemaPath, &subInfo);
        EXPECT_NE((void *)NULL, subInfo);
        user_data[i] = (SnUserDataT *)malloc(sizeof(SnUserDataT));
        if (user_data[i] == NULL) {
            for (--i; i >= 0; i--) {
                free(user_data[i]);
            }
            break;
        }
        memset(user_data[i], 0, sizeof(SnUserDataT));
        // 订阅事件
        GmcSubConfigT tmp_sub_info;
        tmp_sub_info.subsName = subName;
        tmp_sub_info.configJson = subInfo;
        GmcUnSubscribe(stmt, subName);
        ret = GmcSubscribe(stmt, &tmp_sub_info, testSubConn[i], DwOldSnCallBack, user_data[i]);
        if (ret != 0) {
            printf("GmcSubscribe error %d", i + 1);
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(subInfo);
    }

    srand(time(NULL));
    while (1) {
        int times = rand() % 10;
        sleep(times);
        for (int32_t i = 0; i < subNum; i++) {
            user_data[i]->subIndex = 0;
            user_data[i]->insertNum = 0;
            user_data[i]->deleteNum = 0;
            user_data[i]->updateNum = 0;
            user_data[i]->replaceNum = 0;
        }
    }
    for (int32_t i = 0; i < subNum; i++) {
        (void)sprintf(subName, "subVertexIf%d", i + 1);
        ret = GmcUnSubscribe(stmt, subName);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        free(user_data[i]);
        // 释放订阅连接
        ret = testSubDisConnect(testSubConn[i], stmt_sub[i]);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}
// 2025.2.17 当前该表已含自增列
/**********************test_if_fwd*****************************************/
// 通过二级索引获取当前自增列的值
int32_t getIfFwdAutoValue(uint32_t value, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN, false);
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t tUint32Value = value;
    RETURN_IFERR(GmcSetIndexKeyId(stmt, 2));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    uint32_t tUint32Value1 = 0;
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isNull = false;
    uint32_t propVal = 0;
    ret = GmcGetVertexPropertyById(stmt, 0, &tUint32Value1, sizeof(propVal), &isNull);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (isNull) {
        return value;
    }
    return tUint32Value1;
}
int setIfFwdPk(GmcStmtT *stmt, uint32_t pk, uint32_t autoValue)
{
    uint32_t tUint32Value = pk;
    RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &autoValue, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int setIfFwdPkProperty(GmcStmtT *stmt, uint32_t pk, uint32_t autoValue)
{
    uint32_t tUint32Value = pk;
    RETURN_IFERR(GmcSetVertexProperty(stmt, "autoAddValue", GMC_DATATYPE_UINT32, &autoValue, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "segIndex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int setIfFwdProperty(GmcStmtT *stmt, uint32_t propertyValue)
{
    uint32_t tUint32Value = propertyValue;
    uint64_t tUint64Value = propertyValue;
    uint16_t tUint16Value = propertyValue;
    char wrFixed[96] = {0};
    (void)snprintf(wrFixed, 96, "f%.10u", tUint32Value);
    RETURN_IFERR(GmcSetVertexProperty(stmt, "ifkey_type", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "ifkey", GMC_DATATYPE_FIXED, wrFixed, 20));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "resource_vsi", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "resource", GMC_DATATYPE_FIXED, wrFixed, 24));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "ready_flags", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "attr_flags", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "update_time", GMC_DATATYPE_TIME, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "down_status", GMC_DATATYPE_FIXED, wrFixed, 8));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "error_code", GMC_DATATYPE_FIXED, wrFixed, 8));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "svc_ctx", GMC_DATATYPE_FIXED, wrFixed, 96));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "globallif", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "globallif_ver", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "actType", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "peVlanId", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "ceVlanId", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    GmcBitMapT bitMap1 = {0, 4095, NULL};
    uint8_t bits1[4096] = {0x08, 0x09, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15};
    bitMap1.bits = bits1;
    RETURN_IFERR(GmcSetVertexProperty(stmt, "vlanbitmap", GMC_DATATYPE_BITMAP, &bitMap1, sizeof(bitMap1)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "subIfType", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int dwReplaceIfFwd(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 1, int32_t end = OP_RECORD, int isBatch = false,
    const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    uint32_t tUint32Value1 = getIfFwdAutoValue(start);
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        setIfFwdPkProperty(stmt, tUint32Value, tUint32Value1++);
        setIfFwdProperty(stmt, tUint32Value);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE &&
                    ret != GMERR_UNIQUE_VIOLATION) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
        }
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        // 2025.2.19 含自增列并发删除时,主键删除易失败报二级索引唯一性冲突
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }
    return 0;
}

void DwReplaceIfFwd(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = dwReplaceIfFwd(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
int updateIfFwdByPk(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 1, int32_t end = OP_RECORD, int isBatch = false,
    const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    // 模拟随机
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_IfFwd [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % (end - 200);
    // 2025.2.17模式1随机主键更新200条数据，模式2主键更新所有数据
    if (randowNum % 2 == 0) {
        end = randowNum + 200;
        start = randowNum;
    }
    uint32_t tUint32Value1 = getIfFwdAutoValue(start);
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isBatch);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        setIfFwdPk(stmt, tUint32Value, tUint32Value1++);
        tUint32Value++;
        setIfFwdProperty(stmt, tUint32Value);
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

void UpdateIfFwdByPk(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = updateIfFwdByPk(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

int updateIfFwdByLocalHash(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 1, int32_t end = OP_RECORD,
    int isCsflag = false, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 1));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        setIfFwdProperty(stmt, tUint32Value);

        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

// 随机二级索引更新
int updateIfFwdBySecondIndex(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 1, int32_t end = OP_RECORD,
    int isCsflag = false, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    uint32_t keyId = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_IfFwd [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    keyId = 1;
    keyId = rand() % 4 + 1;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, keyId));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        setIfFwdProperty(stmt, tUint32Value);

        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

void UpdateIfFwdByLocalHash(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = updateIfFwdByLocalHash(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

int removeIfFwdByPK(GmcConnT *conn, GmcStmtT *stmt, int32_t startPk = 1, int32_t endPk = OP_RECORD,
    int isCsflag = false, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    uint32_t tUint32Value1 = 0;
    for (int i = startPk; i < endPk; i++) {
        tUint32Value1 = getIfFwdAutoValue(i);
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value1, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }
    return 0;
}

int removeIfFwdBySecondIndexUnique(GmcConnT *conn, GmcStmtT *stmt, int32_t startPk = 1, int32_t endPk = OP_RECORD,
    int isCsflag = false, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    for (int i = startPk; i < endPk; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 2));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }
    return 0;
}

void RemoveIfFwdByPK(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = removeIfFwdByPK(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// async test_if_fwd
int dwReplaceIfFwdAsync(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    AsyncUserDataT data = {0};
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    uint32_t tUint32Value1 = getIfFwdAutoValue(start);
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        setIfFwdPkProperty(stmt, tUint32Value, tUint32Value1++);
        setIfFwdProperty(stmt, tUint32Value);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testWaitAsyncRecv(&data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY &&
                    data.status != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_UNIQUE_VIOLATION) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    TEST_EXPECT_INT32(GMERR_OK, data.status);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            GmcAsyncRequestDoneContextT requestCtx;
            requestCtx.replaceCb = replace_vertex_callback;
            requestCtx.userData = &data;
            ret = GmcExecuteAsync(stmt, &requestCtx);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY &&
                data.status != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_UNIQUE_VIOLATION) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                TEST_EXPECT_INT32(GMERR_OK, data.status);
            }
        }
    }
    return ret;
}

int updateIfFwdByPkAsync(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    AsyncUserDataT data = {0};
    for (int32_t i = start; i < end; i++) {
        uint32_t tUint32Value1 = getIfFwdAutoValue(start);
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        setIfFwdPk(stmt, tUint32Value, tUint32Value1++);
        tUint32Value++;
        setIfFwdProperty(stmt, tUint32Value);
        GmcAsyncRequestDoneContextT requestCtx;
        requestCtx.replaceCb = update_vertex_callback;
        requestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &requestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY && data.status != GMERR_LOCK_NOT_AVAILABLE &&
            ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }

    return 0;
}

int updateIfFwdByLocalHashAsync(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    AsyncUserDataT data = {0};
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 1));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        setIfFwdProperty(stmt, tUint32Value);

        GmcAsyncRequestDoneContextT requestCtx;
        requestCtx.replaceCb = update_vertex_callback;
        requestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &requestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY && data.status != GMERR_LOCK_NOT_AVAILABLE &&
            ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }

    return 0;
}

int removeIfFwdByPKAsync(
    GmcStmtT *stmt, int32_t startPk, int32_t endPk, int isCsflag, const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    AsyncUserDataT data = {0};

    for (int i = startPk; i < endPk; i++) {
        uint32_t tUint32Value1 = getIfFwdAutoValue(startPk);
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value1, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        GmcAsyncRequestDoneContextT requestCtx;
        requestCtx.replaceCb = delete_vertex_callback;
        requestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &requestCtx);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (data.status != GMERR_OK && data.status != GMERR_OUT_OF_MEMORY && data.status != GMERR_LOCK_NOT_AVAILABLE &&
            ret != GMERR_UNIQUE_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    return 0;
}

// read test_if_fwd
int readIfFwdByGet(GmcStmtT *stmt)
{
    int ret = 0;
    bool isNull = false;
    uint32_t tUint32Value = 0;
    uint64_t tUint64Value = 0;
    uint16_t tUint16Value = 0;
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "ifindex", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "segIndex", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "ifkey_type", &tUint32Value, sizeof(uint32_t), &isNull));
    char wrFixed[96] = {0};
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "ifkey", wrFixed, 20, &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "vrid", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "resource_vsi", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "resource", wrFixed, 24, &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "ready_flags", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "attr_flags", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "update_time", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "down_status", wrFixed, 8, &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "error_code", wrFixed, 8, &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "svc_ctx", wrFixed, 96, &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "vrf_index", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "globallif", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "globallif_ver", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "actType", &tUint32Value, sizeof(uint32_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "peVlanId", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "ceVlanId", &tUint16Value, sizeof(uint16_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "subIfType", &tUint32Value, sizeof(uint32_t), &isNull));
    GmcBitMapT bitMap1 = {0, 4095, NULL};
    uint8_t bits1[4096] = {0x08, 0x09, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15};
    bitMap1.bits = bits1;
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "vlanbitmap", &bits1, sizeof(bits1), &isNull));
    return 0;
}
int scanIfFwdByPk(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isCsflag = false,
    const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    bool isFinish = false;
    // scan by primary key
    uint32_t tUint32Value1 = getIfFwdAutoValue(start);
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN, isCsflag);
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = start; loop < end; loop++) {
        setIfFwdPk(stmt, loop, tUint32Value1++);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                ret = readIfFwdByGet(stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
    return ret;
}
void ScanIfFwdByPk(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    ret = scanIfFwdByPk(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

int subIfFwd(GmcStmtT *stmt)
{
    int ret = 0;
    char *subInfo1 = NULL;
    char *subInfo2 = NULL;
    int chanRingLen = 256;
    const char *subConnName1 = (const char *)"subConnIfFwd1";
    const char *subConnName2 = (const char *)"subConnIfFwd2";
    GmcStmtT *stmt_sub1 = NULL;
    GmcConnT *testSubConn1 = NULL;
    GmcStmtT *stmt_sub2 = NULL;
    GmcConnT *testSubConn2 = NULL;
    const char *subName1 = "subVertexIfFwd1";
    const char *subName2 = "subVertexIfFwd2";
    SnUserDataT *user_data1 = NULL;
    SnUserDataT *user_data2 = NULL;

    SnUserDataT *userDataGereral;
    userDataGereral = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (userDataGereral == NULL) {
        return 0;
    }

    SnUserDataT *userDataSpecial;
    userDataSpecial = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (userDataSpecial == NULL) {
        free(userDataGereral);
        return 0;
    }

    user_data1 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data1, 0, sizeof(SnUserDataT));
    if (user_data1 == NULL) {
        free(userDataGereral);
        free(userDataSpecial);
        return 0;
    }

    user_data2 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data2, 0, sizeof(SnUserDataT));
    if (user_data2 == NULL) {
        free(userDataGereral);
        free(userDataSpecial);
        free(user_data1);
        return 0;
    }
    // 创建订阅连接
    ret = testSubConnect(&testSubConn1, &stmt_sub1, 1, g_epoll_reg_info, subConnName1, &chanRingLen);
    if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
        free(userDataGereral);
        free(userDataSpecial);
        free(user_data1);
        free(user_data2);
        sleep(10);
        return 0;
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSubConnect(&testSubConn2, &stmt_sub2, 1, g_epoll_reg_info, subConnName2, &chanRingLen);
    if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
        free(userDataGereral);
        free(userDataSpecial);
        free(user_data1);
        free(user_data2);
        ret = testSubDisConnect(testSubConn1, stmt_sub1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(10);
        return 0;
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);

    readJanssonFile("./DWschemafile/Oldsub/test_if_fwd_sub1.gmjson", &subInfo1);
    EXPECT_NE((void *)NULL, subInfo1);
    readJanssonFile("./DWschemafile/Oldsub/test_if_fwd_sub2.gmjson", &subInfo2);
    EXPECT_NE((void *)NULL, subInfo2);
    // 订阅事件
    GmcSubConfigT tmp_sub_info1;
    tmp_sub_info1.subsName = subName1;
    tmp_sub_info1.configJson = subInfo1;
    GmcUnSubscribe(stmt, subName1);
    ret = GmcSubscribe(stmt, &tmp_sub_info1, testSubConn1, DwOldSnCallBack, user_data1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subInfo1);

    GmcSubConfigT tmp_sub_info2;
    tmp_sub_info2.subsName = subName2;
    tmp_sub_info2.configJson = subInfo2;
    GmcUnSubscribe(stmt, subName2);
    ret = GmcSubscribe(stmt, &tmp_sub_info2, testSubConn2, DwOldSnCallBack, user_data2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subInfo2);

    EpollThreadDataT subEpoll[1] = {0};
    GmcStmtT *stmt_sub = {NULL};
    GmcConnT *testSubConn = {NULL};
    YangConnOptionT connOptions = {0};
    char subConnName[] = "newSubConnnew_subinfo_all_dw";
    for (int32_t i = 0; i < 1; i++) {
        ret = create_epoll_thread(&subEpoll[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(subConnName, "ywysubConnDw%d", i + 1);
        connOptions.epollFd = &subEpoll[i].userEpollFd;
        connOptions.connName = subConnName;
        connOptions.needEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = epollRegWithUserdata;
        ret = TestYangGmcConnect(&testSubConn, &stmt_sub, GMC_CONN_TYPE_SUB, &connOptions);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            free(userDataGereral);
            free(userDataSpecial);
            free(user_data1);
            free(user_data2);
            sleep(10);
            return 0;
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 特殊复杂表
    memset(userDataSpecial, 0, sizeof(SnUserDataT));
    const char *subNameSpecial = "new_subinfo_all_specialdw";
    TestCreateSpecialNewSub(stmt, testSubConn, subNameSpecial, userDataSpecial);

    // 一般复杂表
    memset(userDataGereral, 0, sizeof(SnUserDataT));
    const char *subNameSpecial2 = "new_subinfo_all_gerneraldw";
    TestCreateGeneralNewSub(stmt, testSubConn, subNameSpecial2, userDataGereral);
    while (1) {
        srand(time(NULL));
        int times = rand() % 10;
        sleep(times);
        user_data1->subIndex = 0;
        user_data1->insertNum = 0;
        user_data1->deleteNum = 0;
        user_data1->updateNum = 0;
        user_data1->replaceNum = 0;

        user_data2->subIndex = 0;
        user_data2->insertNum = 0;
        user_data2->deleteNum = 0;
        user_data2->updateNum = 0;
        user_data2->replaceNum = 0;

        userDataSpecial->subIndex = 0;
        userDataSpecial->insertNum = 0;
        userDataSpecial->deleteNum = 0;
        userDataSpecial->updateNum = 0;
        userDataSpecial->replaceNum = 0;
        userDataSpecial->agedNum = 0;

        userDataGereral->subIndex = 0;
        userDataGereral->insertNum = 0;
        userDataGereral->deleteNum = 0;
        userDataGereral->updateNum = 0;
        userDataGereral->replaceNum = 0;
        userDataGereral->agedNum = 0;
    }
    ret = GmcUnSubscribe(stmt, subName1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(user_data1);
    free(user_data2);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn1, stmt_sub1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSubDisConnect(testSubConn2, stmt_sub2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

// 随机数a，间隔a秒对表数据进行对账，设置操作为check_replace
int checkReplaceIfFwd(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 1, int32_t end = OP_RECORD, int isBatch = false,
    const char *vertexName = "test_if_fwd")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    // 随机时间
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_IfFwd [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 20;
    sleep(randowNum + 1);

    //  开启对账
    int ageTaskCnt = 0;
    int beginRet = -1;
    bool isBeginCheckOk = false;
    bool isAbnormal = false;
    ret = DwGetAgeTaskCnt(&ageTaskCnt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (ageTaskCnt < AGETASKCNT) {
        beginRet = GmcBeginCheck(stmt, vertexName, 0xff);
        if (beginRet == GMERR_OK) {
            isBeginCheckOk = true;
        }
    }
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    uint32_t tUint32Value1 = getIfFwdAutoValue(start);
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_CHECK_REPLACE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        setIfFwdPkProperty(stmt, tUint32Value, tUint32Value1++);
        setIfFwdProperty(stmt, tUint32Value);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE &&
                    ret != GMERR_UNIQUE_VIOLATION) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
        }
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNIQUE_VIOLATION &&
            ret != GMERR_TABLE_IN_CHECKING && ret != GMERR_TABLE_NOT_IN_CHECKING &&
            ret != GMERR_REPLACE_CONFLICT_IN_CHECKING) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNIQUE_VIOLATION && ret != GMERR_TABLE_IN_CHECKING && ret != GMERR_TABLE_NOT_IN_CHECKING &&
            ret != GMERR_REPLACE_CONFLICT_IN_CHECKING) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }
    //  对账结束
    if (isBeginCheckOk) {
        do {
            ret = GmcEndCheck(stmt, vertexName, 0xff, isAbnormal);
            if (ret == GMERR_OK) {
                usleep(100);
                break;
            } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                usleep(100);
            } else {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        if (ret == GMERR_OK) {
            ret = checkAccountStatus(stmt, vertexName, 255);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    } else if (beginRet != GMERR_LOCK_NOT_AVAILABLE && beginRet != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE &&
               beginRet != GMERR_TABLE_IN_CHECKING && beginRet != GMERR_UNIQUE_VIOLATION && beginRet != -1) {
        TEST_EXPECT_INT32(GMERR_OK, beginRet);
    }
    return 0;
}

/**********************test_mqc_entry_avl*****************************************/
// 控制升降级
bool g_isNeedUpgrade = true;
bool g_isNeedUpgrade2 = true;
int setMqcEntryAvlcount_infoNode(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "match_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "match_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "drop_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "drop_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "filter_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "filter_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "car_pkt", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "car_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    return 0;
}
int getMqcEntryAvlcount_infoNode(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    bool isNull = false;
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "match_pkt", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "match_byte", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "pass_pkt", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "pass_byte", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "drop_pkt", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "drop_byte", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "filter_pkt", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "filter_byte", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "car_pkt", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "car_byte", &tUint64Value, sizeof(uint64_t), &isNull));
    return 0;
}
int setMqcEntryAvlRate_infoNode(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "match_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "match_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "drop_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "drop_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "filter_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "filter_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "car_pps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "car_bps", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    return 0;
}

int getMqcEntryAvlRate_infoNode(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    bool isNull = false;
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "match_pps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "match_bps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "pass_pps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "pass_bps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "drop_pps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "drop_bps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "filter_pps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "filter_bps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "car_pps", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "car_bps", &tUint64Value, sizeof(uint64_t), &isNull));
    return 0;
}

int setMqcEntryAvlHisStat_infoNode(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_packet", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    RETURN_IFERR(GmcNodeSetPropertyByName(node, "pass_byte", GMC_DATATYPE_UINT64, &tUint64Value, sizeof(uint64_t)));
    return 0;
}

int getMqcEntryAvlHisStat_infoNode(GmcNodeT *node, int32_t i)
{
    uint64_t tUint64Value = i;
    bool isNull = false;
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "pass_packet", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcNodeGetPropertyByName(node, "pass_byte", &tUint64Value, sizeof(uint64_t), &isNull));
    return 0;
}
int setMqcEntryAvlPkproperty(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    uint64_t tUint64Value = i;
    uint16_t tUint16Value = i;
    uint8_t tUint8Value = i;
    bool tBoolVal = false;
    RETURN_IFERR(GmcSetVertexProperty(stmt, "unit", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "appinst_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "adp_gid", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_pri", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_aged", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "ext_id", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    return 0;
}

int setMqcEntryAvlPk(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    uint64_t tUint64Value = i;
    uint16_t tUint16Value = i;
    uint8_t tUint8Value = i;
    bool tBoolVal = false;
    RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    return 0;
}

int setMqcEntryAvlHashCluster(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    uint64_t tUint64Value = i;
    uint16_t tUint16Value = i;
    uint8_t tUint8Value = i;
    bool tBoolVal = false;
    RETURN_IFERR(GmcSetIndexKeyId(stmt, 1));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int setMqcEntryAvlLocal(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    uint64_t tUint64Value = i;
    uint16_t tUint16Value = i;
    uint8_t tUint8Value = i;
    bool tBoolVal = false;
    RETURN_IFERR(GmcSetIndexKeyId(stmt, 4));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}

int setMqcEntryAvlproperty(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    uint64_t tUint64Value = i;
    uint16_t tUint16Value = i;
    uint8_t tUint8Value = i;
    bool tBoolVal = false;
    RETURN_IFERR(GmcSetVertexProperty(stmt, "entry_resv", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "class_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "match_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "rule_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_rule_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_fisrt_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_second_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_mirror_port", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "statMode", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_log", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "share_car_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "obsrv_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "mc_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "stat_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "res", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "interval", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "sample_rate", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "last_sec", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "last_sec_mill", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_down_pri", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    return 0;
}
int setMqcEntryAvlVarproperty(GmcStmtT *stmt, int32_t i)
{
    uint32_t tUint32Value = i;
    uint64_t tUint64Value = i;
    uint16_t tUint16Value = i;
    uint8_t tUint8Value = i;
    bool tBoolVal = false;
    RETURN_IFERR(GmcSetVertexProperty(stmt, "entry_resv", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "class_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "match_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "rule_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_rule_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_fisrt_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_second_share", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_mirror_port", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "statMode", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "is_log", GMC_DATATYPE_BOOL, &tBoolVal, sizeof(bool)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "share_car_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "obsrv_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "mc_id", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "stat_type", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "res", GMC_DATATYPE_UINT8, &tUint8Value, sizeof(uint8_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "interval", GMC_DATATYPE_UINT16, &tUint16Value, sizeof(uint16_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "sample_rate", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "last_sec", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "last_sec_mill", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "local_down_pri", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
    char demo[10] = "151651";
    RETURN_IFERR(GmcSetVertexProperty(stmt, "PropertyString", GMC_DATATYPE_STRING, demo, strlen(demo)));
    RETURN_IFERR(GmcSetVertexProperty(stmt, "PropertyByte", GMC_DATATYPE_BYTES, demo, strlen(demo)));
    return 0;
}
int dwReplaceMqcEntryAvl(GmcConnT *conn, GmcStmtT *stmt, int32_t start, int32_t end, int isBatch,
    const char *vertexName = "test_mqc_entry_avl", uint32_t versionId = 0)
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    bool isUpgrade = false;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    for (int32_t i = start; i < end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexName, versionId, GMC_OPERATION_REPLACE);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        RETURN_IFERR(setMqcEntryAvlPkproperty(stmt, i));
        RETURN_IFERR(setMqcEntryAvlproperty(stmt, i));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlcount_infoNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlRate_infoNode(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlHisStat_infoNode(node3, i));
        if (versionId == 1) {
            uint32_t tUint32Value = i;
            ret = GmcSetVertexProperty(
                stmt, "upgrade1PropertyInt32", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t));
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }

        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
        }
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int dwUpdateMqcEntryAvlByPk(
    GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_mqc_entry_avl")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        if (ret == GMERR_UNDEFINED_TABLE) {
            return 0;
        }
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        RETURN_IFERR(setMqcEntryAvlPk(stmt, i));

        RETURN_IFERR(setMqcEntryAvlproperty(stmt, i + 1));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlcount_infoNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlRate_infoNode(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlHisStat_infoNode(node3, i));

        ret = GmcExecute(stmt);
        // 2024.12.27更新和升降级存在并发报错1009010
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int dwUpdateMqcEntryAvlByHashCluter(
    GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_mqc_entry_avl")
{
    int ret = 0;

    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        if (ret == GMERR_UNDEFINED_TABLE) {
            return 0;
        }
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        RETURN_IFERR(setMqcEntryAvlHashCluster(stmt, i));

        RETURN_IFERR(setMqcEntryAvlproperty(stmt, i + 1));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlcount_infoNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlRate_infoNode(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlHisStat_infoNode(node3, i));

        ret = GmcExecute(stmt);

#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int dwUpdateMqcEntryAvlByLocal(
    GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_mqc_entry_avl")
{
    int ret = 0;

    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        if (ret == GMERR_UNDEFINED_TABLE) {
            return 0;
        }
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        RETURN_IFERR(setMqcEntryAvlLocal(stmt, i));

        RETURN_IFERR(setMqcEntryAvlproperty(stmt, i + 1));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlcount_infoNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlRate_infoNode(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlHisStat_infoNode(node3, i));
        ret = GmcExecute(stmt);

#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // 2025.2.26更新和升降级存在并发报错1009010
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}
int dwReplaceMqcEntryAvlVar(GmcConnT *conn, GmcStmtT *stmt, int32_t start, int32_t end, int isBatch,
    const char *vertexName = "test_mqc_entry_avlvar", int32_t versionId = 0)
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    bool isUpgrade = false;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    for (int32_t i = start; i < end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexName, versionId, GMC_OPERATION_REPLACE);
        if (ret == GMERR_UNDEFINED_TABLE) {
            return 0;
        }
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        RETURN_IFERR(setMqcEntryAvlPkproperty(stmt, i));
        RETURN_IFERR(setMqcEntryAvlVarproperty(stmt, i));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlcount_infoNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlRate_infoNode(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlHisStat_infoNode(node3, i));
        if (versionId == 1) {
            char demo[10] = "151651";
            RETURN_IFERR(GmcSetVertexProperty(stmt, "upGradePropertyString", GMC_DATATYPE_STRING, demo, strlen(demo)));
            RETURN_IFERR(GmcSetVertexProperty(stmt, "upGradePropertyByte", GMC_DATATYPE_BYTES, demo, strlen(demo)));
        }

        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            GmcResetStmt(stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
        }
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int dwUpdateMqcEntryAvlVarByPk(
    GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_mqc_entry_avlvar")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        if (ret == GMERR_UNDEFINED_TABLE) {
            return 0;
        }
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);

        RETURN_IFERR(setMqcEntryAvlPk(stmt, i));

        RETURN_IFERR(setMqcEntryAvlVarproperty(stmt, i + 1));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlcount_infoNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlRate_infoNode(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlHisStat_infoNode(node3, i));

        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int dwUpdateMqcEntryAvlVarByHashCluter(
    GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_mqc_entry_avlvar")
{
    int ret = 0;

    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        if (ret == GMERR_UNDEFINED_TABLE) {
            return 0;
        }
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        RETURN_IFERR(setMqcEntryAvlHashCluster(stmt, i));

        RETURN_IFERR(setMqcEntryAvlVarproperty(stmt, i + 1));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlcount_infoNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlRate_infoNode(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlHisStat_infoNode(node3, i));

        ret = GmcExecute(stmt);

#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int dwUpdateMqcEntryAvlVarByLocal(
    GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_mqc_entry_avlvar")
{
    int ret = 0;

    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        if (ret == GMERR_UNDEFINED_TABLE) {
            return 0;
        }
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        RETURN_IFERR(setMqcEntryAvlLocal(stmt, i));

        RETURN_IFERR(setMqcEntryAvlVarproperty(stmt, i + 1));

        GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
        // 获取根节点与子节点
        RETURN_IFERR(GmcGetRootNode(stmt, &root));
        RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
        RETURN_IFERR(setMqcEntryAvlcount_infoNode(node1, i));
        RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
        RETURN_IFERR(setMqcEntryAvlRate_infoNode(node2, i));
        RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
        RETURN_IFERR(setMqcEntryAvlHisStat_infoNode(node3, i));
        ret = GmcExecute(stmt);

#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int dwRemoveMqcEntryAvlByPK(
    GmcStmtT *stmt, int32_t startPk, int32_t endPk, int isCsflag, const char *vertexName = "test_mqc_entry_avl")
{
    int ret = 0;
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, false);
    if (ret == GMERR_UNDEFINED_TABLE) {
        return 0;
    }
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPk; i < endPk; i++) {
        uint32_t tUint32Value = i;
        RETURN_IFERR(setMqcEntryAvlPk(stmt, i));
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }
    return 0;
}

int dwRemoveMqcEntryAvlVarByPK(
    GmcStmtT *stmt, int32_t startPk, int32_t endPk, int isCsflag, const char *vertexName = "test_mqc_entry_avlvar")
{
    int ret = 0;
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, false);
    if (ret == GMERR_UNDEFINED_TABLE) {
        return 0;
    }
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startPk; i < endPk; i++) {
        uint32_t tUint32Value = i;
        RETURN_IFERR(setMqcEntryAvlPk(stmt, i));
        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_UNDEFINED_TABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }
    return 0;
}
int TestUpdateVertexLabelSchema(char *schemaPath, char *expectValue, const char *labelName = NULL,
    char *nsName = g_testNameSpace, char *uWay = (char *)"online")
{
    // gmddl工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    if (labelName) {
        (void)snprintf(
            cmd, 512, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath, labelName, schemaPath, uWay, nsName);
    } else {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -f %s -u %s -ns %s", g_toolPath, schemaPath, uWay, nsName);
    }

    ret = executeCommand(cmd, expectValue);
    return ret;
}
int ExecuteCommandNotPrint(char *cmd, const char *v1)
{
    uint32_t num = 0;
    char buffer[1024] = {0};
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return -1;
    }
    while (NULL != fgets(buffer, 500, pf)) {
        if (strstr(buffer, v1)) {
            num++;
        }
    }
    pclose(pf);
    pf = NULL;
    if (num == 0) {
        return -1;
    } else {
        return 0;
    }
}
int TestDownGradeVertexLabel(const char *labelName, uint32_t schemaVersion, char *expectValue,
    char *nsName = g_testNameSpace, char *dWay = (char *)"async")
{
    char cmd[512] = {0};
    int ret = 0;
    (void)snprintf(
        cmd, 512, "%s/gmddl -c alter -t %s -v %d -d %s -ns %s", g_toolPath, labelName, schemaVersion, dWay, nsName);
    ret = executeCommand(cmd, expectValue);
    char const *viewName = "V\\$QRY_SCHEMA_DEGRADE_INFO";
    char command[256] = {0};
    memset(command, 0, 256);
    int cycle = 0;
    (void)snprintf(command, 256, "%s/gmsysview -q %s -f TABLE_NAME=%s", g_toolPath, viewName, labelName);
    while (cycle < 1000) {
        ret = ExecuteCommandNotPrint(command, "test_mqc_entry_avl");
        if (ret == GMERR_OK) {
            cycle++;
            sleep(1);
            continue;
        } else {
            // 查不到视图代表降级已经结束
            break;
        }
    }
    return ret;
}
// 升降级（uint32)
void upgradeMqcEntryAvl(
    char *schemaPath, char *expectValue = (char *)"upgrade successfully", const char *labelName = "test_mqc_entry_avl")
{
    int ret = 0;
    ret = TestUpdateVertexLabelSchema(schemaPath, expectValue, labelName);
    if (!ret) {
        g_isNeedUpgrade = false;
    }
}

void downgradeMqcEntryAvl(const char *labelName = "test_mqc_entry_avl", uint32_t schemaVersion = 0,
    char *expectValue = (char *)"degrade successfully")
{
    int ret = 0;
    ret = TestDownGradeVertexLabel(labelName, schemaVersion, expectValue);
    if (!ret) {
        g_isNeedUpgrade = true;
    }
}
// 升降级（string)
void upgradeMqcEntryAvlVar(char *schemaPath, char *expectValue = (char *)"upgrade successfully",
    const char *labelName = "test_mqc_entry_avlvar")
{
    int ret = 0;
    if (g_isNeedUpgrade2) {
        ret = TestUpdateVertexLabelSchema(schemaPath, expectValue, labelName);
    }
    if (!ret) {
        g_isNeedUpgrade2 = false;
    }
}

void downgradeMqcEntryAvlVar(const char *labelName = "test_mqc_entry_avlvar", uint32_t schemaVersion = 0,
    char *expectValue = (char *)"degrade successfully")
{
    int ret = 0;
    if (!g_isNeedUpgrade2) {
        ret = TestDownGradeVertexLabel(labelName, schemaVersion, expectValue);
    }

    if (!ret) {
        g_isNeedUpgrade2 = true;
    }
}

int dwScanMqcEntryAvlByPk(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_mqc_entry_avl")
{
    int ret = 0;
    bool isFinish = false;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL;
    // scan by primary key
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN, isCsflag);
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = start; loop < end; loop++) {
        setMqcEntryAvlPk(stmt, loop);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                GmcNodeT *root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL;
                // 获取根节点与子节点
                RETURN_IFERR(GmcGetRootNode(stmt, &root));
                RETURN_IFERR(GmcNodeGetChild(root, "count_info", &node1));
                RETURN_IFERR(getMqcEntryAvlcount_infoNode(node1, loop));
                RETURN_IFERR(GmcNodeGetChild(root, "rate_info", &node2));
                RETURN_IFERR(getMqcEntryAvlRate_infoNode(node2, loop));
                RETURN_IFERR(GmcNodeGetChild(root, "hisStat_info", &node3));
                RETURN_IFERR(getMqcEntryAvlHisStat_infoNode(node3, loop));
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
            if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_UNDEFINED_TABLE) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    }
    return 0;
}
/**********************test_if_lst*****************************************/
int dwReplaceIfLst(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start, int32_t end, int isBatch, const char *vertexName = "test_if_lst")
{
    int ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    GmcBatchOptionT batchOption;
    bool isUpgrade = false;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_REPLACE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        uint64_t tmpResIdx = 0;
        RETURN_IFERR(GmcSetPoolIdResource(0xFFFF, &tmpResIdx));
        RETURN_IFERR(GmcSetCountResource(1, &tmpResIdx));
        RETURN_IFERR(GmcSetStartIdxResource(0xFFFFFFFF, &tmpResIdx));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lstindexres", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(uint64_t)));
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lstindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if ((i > 0 && i % 500 == 0) || (i == end - 1)) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE &&
                    ret != GMERR_RESOURCE_POOL_ERROR && ret != GMERR_FEATURE_NOT_SUPPORTED) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                } else {
                    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
            if (i == end - 1) {
                GmcBatchDestroy(batch);
            }

        } else {
            ret = GmcExecute(stmt);
        }
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_RESOURCE_POOL_ERROR &&
            ret != GMERR_FEATURE_NOT_SUPPORTED) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_FEATURE_NOT_SUPPORTED && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int updateIfLstByPk(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_lst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, isCsflag);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lstindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

int updateIfLstByLocalHash(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_lst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_UPDATE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 1));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        tUint32Value++;
        RETURN_IFERR(GmcSetVertexProperty(stmt, "lstindex", GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }

    return 0;
}

static const char *g_resPoolName001 = "ResourcePool001";
static const char *g_resPoolConfigJson001 =
    R"({
        "name" : "ResourcePool001",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";
static const char *g_resPoolName003 = "ResourcePool003";
static const char *g_resPoolConfigJson003 =
    R"({
        "name" : "ResourcePool003",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";
bool g_isHaveResouce = false;
int deleteIfLstBypk(GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag, const char *vertexName = "test_if_lst")
{
    int ret = 0;
    for (int32_t i = start; i < end; i++) {
        ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_DELETE, false);
        // 防止客户端异常退出
#ifdef DIRECT_WRITE
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            return 0;
        }
#endif
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tUint32Value = i;
        uint64_t tUint64Value = i;
        uint16_t tUint16Value = i;
        uint8_t tUint8Value = i;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));

        ret = GmcExecute(stmt);
#ifdef DIRECT_WRITE
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#else
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#endif
    }
    if (g_isHaveResouce) {
        ret = GmcUnbindResPoolFromLabel(stmt, vertexName);
        if (ret != GMERR_RESOURCE_POOL_ERROR) {
            RETURN_IFERR(ret);
        }
        // 销毁资源池
        ret = GmcDestroyResPool(stmt, g_resPoolName001);
        if (ret != GMERR_RESOURCE_POOL_ERROR) {
            RETURN_IFERR(ret);
        }
        g_isHaveResouce = false;
    }
    return 0;
}

int ResouceIfLst(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag = false, const char *vertexName = "test_if_lst")
{
    // 创建资源池
    int ret = 0;
    if (!g_isHaveResouce) {
        (void)GmcDestroyResPool(stmt, g_resPoolName001);
        ret = GmcCreateResPool(stmt, g_resPoolConfigJson001);
        if (GMERR_RESOURCE_POOL_ALREADY_EXIST != ret) {
            RETURN_IFERR(ret);
        }
        ret = GmcBindResPoolToLabel(stmt, g_resPoolName001, vertexName);
        if (ret != GMERR_RESOURCE_POOL_ALREADY_BOUND && ret != GMERR_RESOURCE_POOL_ERROR) {
            RETURN_IFERR(ret);
        }
        g_isHaveResouce = true;
    }

    return 0;
}
int readIfLstByGet(GmcStmtT *stmt)
{
    int ret = 0;
    bool isNull = false;
    uint32_t tUint32Value = 0;
    uint64_t tUint64Value = 0;
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "lstindexres", &tUint64Value, sizeof(uint64_t), &isNull));
    RETURN_IFERR(GmcGetVertexPropertyByName(stmt, "lstindex", &tUint32Value, sizeof(uint32_t), &isNull));
    return 0;
}
int scanIfLstByPk(
    GmcStmtT *stmt, int32_t start, int32_t end, int isCsflag = false, const char *vertexName = "test_if_lst")
{
    int ret = 0;
    bool isFinish = false;
    // scan by primary key
    ret = TestGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN, isCsflag);
    // 防止客户端异常退出
#ifdef DIRECT_WRITE
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return 0;
    }
#endif
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int loop = start; loop < end; loop++) {
        uint32_t tUint32Value = loop;
        uint8_t tUint8Value = loop;
        RETURN_IFERR(GmcSetIndexKeyId(stmt, 0));
        RETURN_IFERR(GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tUint32Value, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                ret = readIfLstByGet(stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
            TEST_EXPECT_INT32(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
    return ret;
}

void randow_dml_If(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isCsflag = false)
{
    int ret = 0;
    uint32_t randowNum = 0;
    time_t timer = time(0);
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    if (!timer) {
        printf("randow_dml_If [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 6;
    if (randowNum == 0) {
        ret = dwReplaceIf(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 1) {
        ret = dwUpdateIfByPk(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 2) {
        ret = dwUpdateIfByLocalHash(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 3) {
        ret = dwRemoveIfByPK(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 4) {
        ret = dwScanIfByPk(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = dwReplaceIf(conn, stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void *RandowDmlIfSub(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = dwSubIf(stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return NULL;
}

void randow_dml_If_Async(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isCsflag = false)
{
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    int ret = 0;
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_If_Async [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 8;
    if (randowNum == 0) {
        ret = dwReplaceIfAsync(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 1) {
        ret = dwUpdateIfByPkAsync(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 2) {
        ret = dwUpdateIfByLocalHashAsync(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 3) {
        ret = dwRemoveIfByPKAsync(stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = dwReplaceIfAsync(conn, stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void randow_dml_IfFwd(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 1, int32_t end = OP_RECORD, int isCsflag = false)
{
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    int ret = 0;
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_IfFwd [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 8;
    // 获取当前表数据量，超过数据量则走指定路径
    uint64_t count = 0;
    (void)GmcGetVertexCount(stmt, "test_if_fwd", NULL, &count);
    if (count >= MAX_COUNT_AUTO_LABLE) {
        randowNum = 5;
    }
    if (randowNum == 0) {
        ret = dwReplaceIfFwd(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 1) {
        ret = updateIfFwdByPk(conn, stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 2) {
        ret = updateIfFwdByLocalHash(conn, stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 3) {
        ret = removeIfFwdByPK(conn, stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 4) {
        ret = scanIfFwdByPk(conn, stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 5) {
        ret = removeIfFwdBySecondIndexUnique(conn, stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 6) {
        ret = dwReplaceIfFwd(conn, stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = updateIfFwdBySecondIndex(conn, stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void *RandowDmlIfFwdSub(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = subIfFwd(stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return NULL;
}

void randow_dml_IfFwd_Async(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isCsflag = false)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_IfFwd_Async [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 9;
    if (randowNum == 0) {
        ret = dwReplaceIfFwdAsync(conn, stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 1) {
        ret = updateIfFwdByPkAsync(stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 2) {
        ret = updateIfFwdByLocalHashAsync(stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 3) {
        ret = removeIfFwdByPKAsync(stmt, start, end, isCsflag);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = dwReplaceIfFwdAsync(conn, stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void randow_dml_Frm_Inst(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isBatch = false)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_Frm_Inst [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 5;
    if (randowNum == 0) {
        ret = replaceFrmInst(conn, stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 1) {
        ret = updateFrmInstByPk(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 2) {
        ret = replaceFrmInst(conn, stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 3) {
        ret = removeFrmInstByPk(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = scanFrmInstByPk(stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void *RandowDmlFrmInstSub(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = dwSubFrmInst(stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return NULL;
}

void randow_dml_Mqc_Entry_Avl_Upgrade(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isBatch = false)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    if (g_isNeedUpgrade) {
        sleep(1);
    } else {
        return;
    }
    char *schemaPath = (char *)"./DWschemafile/test_mqc_entry_avlUpgrade1.gmjson";
    upgradeMqcEntryAvl(schemaPath);
    if (g_isNeedUpgrade) {
        return;
    } else {
        ret = dwReplaceMqcEntryAvl(conn, stmt, start, end, isBatch, "test_mqc_entry_avl", 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(1);
    }
    downgradeMqcEntryAvl();
}

void randow_dml_Mqc_Entry_Avl_NoUpgrade(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isBatch = false)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_Mqc_Entry_Avl_NoUpgrade [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 6;
    if (randowNum == 0) {
        ret = dwReplaceMqcEntryAvl(conn, stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 1) {
        ret = dwUpdateMqcEntryAvlByPk(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 2) {
        ret = dwUpdateMqcEntryAvlByHashCluter(stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 3) {
        ret = dwUpdateMqcEntryAvlByLocal(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 4) {
        ret = dwScanMqcEntryAvlByPk(stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = dwRemoveMqcEntryAvlByPK(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}
void randow_dml_Mqc_Entry_Avl_Var_Upgrade(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isBatch = false)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    if (g_isNeedUpgrade2) {
        sleep(1);
    } else {
        return;
    }
    char *schemaPath = (char *)"./DWschemafile/test_mqc_entry_avlvarUpgrade1.gmjson";
    upgradeMqcEntryAvlVar(schemaPath);
    if (g_isNeedUpgrade2) {
        return;
    } else {
        ret = dwReplaceMqcEntryAvlVar(conn, stmt, start, end, isBatch, "test_mqc_entry_avlvar", 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(1);
    }
    downgradeMqcEntryAvlVar();
}

void randow_dml_Mqc_Entry_Avl_Var_NoUpgrade(
    GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isBatch = false)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_Mqc_Entry_Avl_Var_NoUpgrade [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 6;
    if (randowNum == 0) {
        ret = dwReplaceMqcEntryAvlVar(conn, stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 1) {
        ret = dwUpdateMqcEntryAvlVarByPk(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 2) {
        ret = dwUpdateMqcEntryAvlVarByHashCluter(stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 3) {
        ret = dwUpdateMqcEntryAvlVarByLocal(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 4) {
        ret = dwScanMqcEntryAvlByPk(stmt, start, end, false, "test_mqc_entry_avlvar");
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = dwRemoveMqcEntryAvlVarByPK(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void randow_dml_If_Lst(GmcConnT *conn, GmcStmtT *stmt, int32_t start = 0, int32_t end = OP_RECORD, int isBatch = false)
{
    int ret = 0;
#ifdef ENV_RTOSV2X
    // CE场景业务表，soho不涉及
    return;
#endif
    uint32_t randowNum = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("randow_dml_If_Lst [ERROR] get time fail\n");
    }
    srand((uint32_t)timer);
    randowNum = rand() % 7;
    if (randowNum == 0) {
        ret = dwReplaceIfLst(conn, stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 1) {
        ret = updateIfLstByPk(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 2) {
        ret = updateIfLstByLocalHash(stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 3) {
        ret = deleteIfLstBypk(stmt, start, end, isBatch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 4) {
        ret = scanIfLstByPk(stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (randowNum == 5) {
        ret = ResouceIfLst(stmt, start, end, false);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else {
        ret = dwReplaceIfLst(conn, stmt, start, end, true);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

#ifdef __cplusplus
}
#endif
#endif /* DW_CE_FIVE_TABLE_COMMON_H */
