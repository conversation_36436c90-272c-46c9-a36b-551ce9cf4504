/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * Description: tsdb_query.h
 * Author: jiangjincheng
 */
#ifndef TSDB_LONG_STABILITY_H
#define TSDB_LONG_STABILITY_H

#if defined FEATURE_TS

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <string>
#include <atomic>
#include <vector>
#include <iostream>
#include <cmath>
#include <chrono>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "adpt_sleep.h"
#include "t_rd_ts.h"
#include "gmc_sysview.h"
#include "gmc_test.h"
#include "t_light.h"
#include "gmc_persist.h"
#include "gmc_ts_persist.h"
#include "../insufficientAddressSpace.h"

#define THREADS_NUM_MAX 100             // 场景数
#define ALL_ACTUAL_TABLE_NUM 53         // 全部业务使用表数53
#define ALL_THREAT_TABLE_NUM 12         // 全部威胁表表数12
#define ALL_REPORT_TABLE_NUM 40         // 全部报表表数40
#define MAX_IP_RAND_NUM 999999          // ip最大随机值
#define MIN_IP_RAND_NUM 100000          // ip最小随机值
#define MAX_PORT_RAND_NUM 65535         // port最大随机值
#define MIN_PORT_RAND_NUM 1             // port最小随机值
#define IPV6_LEN 33                     // ipv6定义长度
#define TEXT_LEN 129                    // userName定义长度
#define APPEND_DATA_LEN 512             // appendData定义长度
#define CONTENT_LEN 3072                // content定义长度
#define MAIL_ADDRESS_LEN 256            // mailAddress定义长度
#define CONTINUOUS_CREATE_TABLE_NUM 16  // 连续建表删表数目
#define TEN_MINUTES 600                 // 10分钟时间
#define INSERT_INTO_SIZE 50000          // insert into指定数据量
#define COPY_TO_SIZE 50000              // copy to指定数据量
#define SQL_LENGTH 2048                 // sql语句最大长度
#define ALL_SUPPORTED_VIEW_NUM 64       // 全部已支持视图数64

#if defined(CPU_BIT_32)

#define BULK_INSERT_SIZE 200            // arm32一次注入数据最大设置为200
#define PER_SECOND_INSERT_SIZE 50       // arm32每s注入数据量大小
#define INIT_FLEW_TABLE_SIZE 5000000    // arm32预置流表数据量
#define INIT_THREAT_TABLE_SIZE 1000000  // arm32预置威胁表数据量
#define INIT_REPORT_TABLE_SIZE 500000   // arm32预置报表数据量

#else

#define BULK_INSERT_SIZE 50000           // 一次注入数据最大设置为50000
#define PER_SECOND_INSERT_SIZE 1000      // 每s注入数据量大小
#define INIT_FLEW_TABLE_SIZE 100000000   // 预置流表数据量
#define INIT_THREAT_TABLE_SIZE 10000000  // 预置威胁表数据量
#define INIT_REPORT_TABLE_SIZE 5000000   // 预置报表数据量

#endif

#ifdef __cplusplus
extern "C" {
#endif
static int ret = 0;
// 创建读写锁，切换目录时上写锁，其他操作上读锁
pthread_rwlock_t rwlock;
int rwlockCounter = 0;

typedef struct {
    GmcConnT *conn;
    GmcStmtT *stmt;
    char *tableName;
    int dataCount;
    int logTime;
} TableBody;

// 提前申请写数据时使用内存
void *flewTableMemoryManagement[24] = {0};
void *threatTableMemoryManagement[25] = {0};
void *otherThreatTableMemoryManagement[16] = {0};
void *reportTableMemoryManagement[12] = {0};
void *truncateTableMemoryManagement[12] = {0};
void *memoryTableMemoryManagement[12] = {0};

typedef struct {
    int64_t *logId;
    int64_t *startTime;
    int64_t *endTime;
    char **vsysId;
    char *srcIp;
    char *dstIp;
    int64_t *srcPort;
    int64_t *dstPort;
    int64_t *protocolType;
    int64_t *userId;
    int64_t *userGroupId;
    int64_t *appCategoryId;
    int64_t *subAppId;
    int64_t *appId;
    int64_t *srcZoneId;
    int64_t *dstZoneId;
    int64_t *secPolicyId;
    int64_t *inIntfId;
    int64_t *outIntfId;
    int64_t *sendByte;
    int64_t *receiveByte;
    char *userName;
    char *natSrcIpv6;
    char *natDstIpv6;
    int64_t *logTime;
    char *appendData;
    char *extendInfo;
    char *content1;
    char *content2;
    char *mailAddress;
} TableInsertData;

int TableInsertDataMalloc(TableInsertData *tableInsertData)
{
    tableInsertData->logId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->logId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->startTime = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->startTime == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->endTime = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->endTime == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->vsysId = (char **)malloc(BULK_INSERT_SIZE * sizeof(char *));
    if (tableInsertData->vsysId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    for (int i = 0; i < BULK_INSERT_SIZE; i++) {
        tableInsertData->vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        if (tableInsertData->vsysId[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
            return FAILED;
        }
    }
    tableInsertData->srcIp = (char *)malloc(BULK_INSERT_SIZE * IPV6_LEN);
    if (tableInsertData->srcIp == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->dstIp = (char *)malloc(BULK_INSERT_SIZE * IPV6_LEN);
    if (tableInsertData->dstIp == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->srcPort = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->srcPort == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->dstPort = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->dstPort == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->protocolType = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->protocolType == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->userId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->userId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->userGroupId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->userGroupId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->appCategoryId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->appCategoryId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->subAppId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->subAppId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->appId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->appId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->srcZoneId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->srcZoneId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->dstZoneId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->dstZoneId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->secPolicyId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->secPolicyId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->inIntfId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->inIntfId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->outIntfId = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->outIntfId == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->sendByte = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->sendByte == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->receiveByte = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->receiveByte == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->userName = (char *)malloc(BULK_INSERT_SIZE * TEXT_LEN);
    if (tableInsertData->userName == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->natSrcIpv6 = (char *)malloc(BULK_INSERT_SIZE * IPV6_LEN);
    if (tableInsertData->natSrcIpv6 == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->natDstIpv6 = (char *)malloc(BULK_INSERT_SIZE * IPV6_LEN);
    if (tableInsertData->natDstIpv6 == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->logTime = (int64_t *)malloc(BULK_INSERT_SIZE * sizeof(int64_t));
    if (tableInsertData->logTime == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->appendData = (char *)malloc(BULK_INSERT_SIZE * APPEND_DATA_LEN);
    if (tableInsertData->appendData == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->extendInfo = (char *)malloc(BULK_INSERT_SIZE * APPEND_DATA_LEN);
    if (tableInsertData->extendInfo == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->content1 = (char *)malloc(BULK_INSERT_SIZE * CONTENT_LEN);
    if (tableInsertData->content1 == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->content2 = (char *)malloc(BULK_INSERT_SIZE * CONTENT_LEN);
    if (tableInsertData->content2 == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    tableInsertData->mailAddress = (char *)malloc(BULK_INSERT_SIZE * MAIL_ADDRESS_LEN);
    if (tableInsertData->mailAddress == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    return 0;
}

void TableInsertDataToNull(TableInsertData *tableInsertData)
{
    (void)memset(tableInsertData->logId, 0, sizeof(tableInsertData->logId));
    (void)memset(tableInsertData->startTime, 0, sizeof(tableInsertData->startTime));
    (void)memset(tableInsertData->endTime, 0, sizeof(tableInsertData->endTime));
    for (int i = 0; i < BULK_INSERT_SIZE; i++) {
        (void)memset(tableInsertData->vsysId[i], 0, sizeof(tableInsertData->vsysId[i]));
    }
    (void)memset(tableInsertData->srcIp, 0, sizeof(tableInsertData->srcIp));
    (void)memset(tableInsertData->dstIp, 0, sizeof(tableInsertData->dstIp));
    (void)memset(tableInsertData->srcPort, 0, sizeof(tableInsertData->srcPort));
    (void)memset(tableInsertData->dstPort, 0, sizeof(tableInsertData->dstPort));
    (void)memset(tableInsertData->protocolType, 0, sizeof(tableInsertData->protocolType));
    (void)memset(tableInsertData->userId, 0, sizeof(tableInsertData->userId));
    (void)memset(tableInsertData->userGroupId, 0, sizeof(tableInsertData->userGroupId));
    (void)memset(tableInsertData->appCategoryId, 0, sizeof(tableInsertData->appCategoryId));
    (void)memset(tableInsertData->subAppId, 0, sizeof(tableInsertData->subAppId));
    (void)memset(tableInsertData->appId, 0, sizeof(tableInsertData->appId));
    (void)memset(tableInsertData->srcZoneId, 0, sizeof(tableInsertData->srcZoneId));
    (void)memset(tableInsertData->dstZoneId, 0, sizeof(tableInsertData->dstZoneId));
    (void)memset(tableInsertData->secPolicyId, 0, sizeof(tableInsertData->secPolicyId));
    (void)memset(tableInsertData->inIntfId, 0, sizeof(tableInsertData->inIntfId));
    (void)memset(tableInsertData->outIntfId, 0, sizeof(tableInsertData->outIntfId));
    (void)memset(tableInsertData->sendByte, 0, sizeof(tableInsertData->sendByte));
    (void)memset(tableInsertData->receiveByte, 0, sizeof(tableInsertData->receiveByte));
    (void)memset(tableInsertData->userName, 0, sizeof(tableInsertData->userName));
    (void)memset(tableInsertData->natSrcIpv6, 0, sizeof(tableInsertData->natSrcIpv6));
    (void)memset(tableInsertData->natDstIpv6, 0, sizeof(tableInsertData->natDstIpv6));
    (void)memset(tableInsertData->logTime, 0, sizeof(tableInsertData->logTime));
    (void)memset(tableInsertData->appendData, 0, sizeof(tableInsertData->appendData));
    (void)memset(tableInsertData->extendInfo, 0, sizeof(tableInsertData->extendInfo));
    (void)memset(tableInsertData->content1, 0, sizeof(tableInsertData->content1));
    (void)memset(tableInsertData->content2, 0, sizeof(tableInsertData->content2));
    (void)memset(tableInsertData->mailAddress, 0, sizeof(tableInsertData->mailAddress));
}

// 释放申请堆内存
void freeHeapMemory(void *ptr)
{
    if (ptr != NULL) {
        free(ptr);
        ptr = NULL;
    }
}

// 释放TableInsertData申请的内存
void TableInsertDataFree(TableInsertData *tableInsertData)
{
    freeHeapMemory(tableInsertData->logId);
    freeHeapMemory(tableInsertData->startTime);
    freeHeapMemory(tableInsertData->endTime);
    for (int i = 0; i < BULK_INSERT_SIZE; i++) {
        freeHeapMemory(tableInsertData->vsysId[i]);
    }
    freeHeapMemory(tableInsertData->vsysId);
    freeHeapMemory(tableInsertData->srcIp);
    freeHeapMemory(tableInsertData->dstIp);
    freeHeapMemory(tableInsertData->srcPort);
    freeHeapMemory(tableInsertData->dstPort);
    freeHeapMemory(tableInsertData->protocolType);
    freeHeapMemory(tableInsertData->userId);
    freeHeapMemory(tableInsertData->userGroupId);
    freeHeapMemory(tableInsertData->appCategoryId);
    freeHeapMemory(tableInsertData->subAppId);
    freeHeapMemory(tableInsertData->appId);
    freeHeapMemory(tableInsertData->srcZoneId);
    freeHeapMemory(tableInsertData->dstZoneId);
    freeHeapMemory(tableInsertData->secPolicyId);
    freeHeapMemory(tableInsertData->inIntfId);
    freeHeapMemory(tableInsertData->outIntfId);
    freeHeapMemory(tableInsertData->sendByte);
    freeHeapMemory(tableInsertData->receiveByte);
    freeHeapMemory(tableInsertData->userName);
    freeHeapMemory(tableInsertData->natSrcIpv6);
    freeHeapMemory(tableInsertData->natDstIpv6);
    freeHeapMemory(tableInsertData->logTime);
    freeHeapMemory(tableInsertData->appendData);
    freeHeapMemory(tableInsertData->extendInfo);
    freeHeapMemory(tableInsertData->content1);
    freeHeapMemory(tableInsertData->content2);
    freeHeapMemory(tableInsertData->mailAddress);
}

// 建立时序服务端链接
int CreateTsConnect(GmcConnT **conn, GmcStmtT **stmt)
{
    int ret = 0;
    // 释放连接
    if (*stmt != NULL || *conn != NULL) {
        testGmcDisconnect(*conn, *stmt);
        *conn = NULL;
        *stmt = NULL;
    }
    // 重新建连
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;
    tsConnOptions.serverLocator = g_connServerTsdb;
    tsConnOptions.requestTimeout = 300000;
    tsConnOptions.msgReadTimeout = 300000;
    ret = TestYangGmcConnect(conn, stmt, 0, &tsConnOptions);
    return ret;
}

// 重启执行GmcExecDirect
int reStartGmcExecDirect(TableBody *tableDataBody, const char *statementText, uint32_t textLength)
{
    int ret = GmcExecDirect(tableDataBody->stmt, statementText, textLength);
    if (ret == GMERR_CONNECTION_RESET_BY_PEER || ret == GMERR_NULL_VALUE_NOT_ALLOWED) {
        int retryTimes = 0;
        while (true) {
            if (retryTimes > 200) {
                break;
            }
            DbSleep(100);
            ret = CreateTsConnect(&tableDataBody->conn, &tableDataBody->stmt);
            if (ret != GMERR_OK) {
                retryTimes++;
                continue;
            }
            ret = GmcExecDirect(tableDataBody->stmt, statementText, textLength);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER || ret == GMERR_NULL_VALUE_NOT_ALLOWED) {
                retryTimes++;
                continue;
            }
            break;
        }
    }
    return ret;
}

// 重启执行GmcPrepareStmtByLabelName
int reStartGmcPrepareStmtByLabelName(TableBody *tableDataBody, GmcOperationTypeE operationType)
{
    int ret = GmcPrepareStmtByLabelName(tableDataBody->stmt, tableDataBody->tableName, GMC_OPERATION_SQL_INSERT);
    if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        int retryTimes = 0;
        while (true) {
            if (retryTimes > 200) {
                break;
            }
            DbSleep(100);
            ret = CreateTsConnect(&tableDataBody->conn, &tableDataBody->stmt);
            if (ret != GMERR_OK) {
                continue;
            }
            ret = GmcPrepareStmtByLabelName(tableDataBody->stmt, tableDataBody->tableName, GMC_OPERATION_SQL_INSERT);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                continue;
            }
            break;
        }
    }
    return ret;
}

// 重启执行GmcExecute
int reStartGmcExecute(TableBody *tableBody)
{
    int ret = GmcExecute(tableBody->stmt);
    if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        int retryTimes = 0;
        while (true) {
            if (retryTimes > 200) {
                break;
            }
            DbSleep(100);
            ret = CreateTsConnect(&tableBody->conn, &tableBody->stmt);
            if (ret != GMERR_OK) {
                continue;
            }
            ret = GmcExecute(tableBody->stmt);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                continue;
            }
            break;
        }
    }
    return ret;
}

// 生成随机字符串
void generate_random_string(char **textData, int dataCount)
{
    time_t time_T;
    time_T = time(NULL);
    (void)srand((uint32_t)time_T);
    char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    int textLength = 0;
    char str[TEXT_LEN] = {0};
    for (int i = 0; i < dataCount; i++) {
        textLength = rand() % TEXT_LEN + 1;
        (void)memset(str, 0, TEXT_LEN);
        for (int n = 0; n < textLength - 1; n++) {
            int key = rand() % (int)(sizeof(charset) - 1);
            str[n] = charset[key];
        }
        str[textLength - 1] = '\0';
        (void)sprintf(*(textData + i), str, TEXT_LEN);
    }
    // 定义用于生成随机字符串的字符集
}

// 生成随机的 IP 地址（IPv4 或 IPv6）
void generate_random_ip(char *ipData, int dataCount)
{
    time_t time_T;
    time_T = time(NULL);
    (void)srand((uint32_t)time_T);
    int ipType = 1;
    for (int i = 0; i < dataCount; i++) {
        ipType = rand() % 10;
        if (ipType == 0) {
            (void)sprintf(ipData + i * IPV6_LEN, "1234567890abcdef1234567890ab%x%x%x%x\0", rand() % 0xf, rand() % 0xf,
                rand() % 0xf, rand() % 0xf);
        } else {
            (void)sprintf(ipData + i * IPV6_LEN, "11%x%x%x%x%x%x\0", (int)(rand() % 10), (int)(rand() % 10),
                (int)(rand() % 10), (int)(rand() % 10), (int)(rand() % 10), (int)(rand() % 10));
        }
    }
}

static char allSupportedViewName[ALL_SUPPORTED_VIEW_NUM][50] = {"CONFIG_PARAMETERS", "DB_SERVER", "QRY_SESSION",
    "COM_DYN_CTX", "COM_MEM_SUMMARY", "COM_SHMEM_CTX", "COM_SHMEM_GROUP", "COM_SHMEM_USAGE_STAT",
    "COM_TABLE_MEM_SUMMARY", "SERVER_MEMORY_OVERHEAD", "SYS_MODULE_MEM_INFO", "QRY_DYNMEM", "CST_SHMEM_INFO",
    "CATA_TABLESPACE_INFO", "CATA_GENERAL_INFO", "STORAGE_VERTEX_COUNT", "STORAGE_HASH_COLLISION_STAT",
    "STORAGE_HASH_CLUSTER_INDEX_STAT", "STORAGE_HASH_LINKLIST_INDEX_STAT", "STORAGE_INDEX_GLOBAL_STAT",
    "STORAGE_LOCK_OVERVIEW", "STORAGE_TRX_STAT", "QRY_TRX_MONITOR_STAT", "STORAGE_PERSISTENT_STAT",
    "STORAGE_BTREE_INDEX_STAT", "STORAGE_BUFFERPOOL_STAT", "STORAGE_DISK_USAGE", "DRT_DATA_PLANE_CHANNEL_STAT",
    "DRT_LONG_OPERATION_STAT", "DRT_SCHEDULE_STAT", "DRT_WORKER_POOL_STAT", "STORAGE_UNDO_STAT",
    "STORAGE_UNDO_PURGER_INFO", "CLT_PROCESS_FLOWCTRL_INFO_LABEL", "CLT_PROCESS_INFO", "CLT_PROCESS_LABEL",
    "MEM_COMPACT_TASKS_STAT", "STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "STORAGE_SHMEM_INFO", "DRT_CONN_STAT",
    "STORAGE_SPACE_INFO", "CLT_PROCESS_CONN", "CLT_PROCESS_TIME_CONSUMPTION", "DRT_COM_STAT", "DRT_CONN_SUBS_STAT",
    "CATA_NAMESPACE_INFO", "PRIVILEGE_ROLE_STAT", "PRIVILEGE_USER_STAT", "CATA_GENERAL_INFO", "CATA_VERTEX_LABEL_INFO",
    "STORAGE_HEAP_VERTEX_LABEL_STAT", "STORAGE_HASH_INDEX_STAT", "STORAGE_ART_INDEX_STAT", "STORAGE_TRX_DETAIL",
    "DRT_PIPE_STAT", "DRT_WORKER_STAT", "STORAGE_MEMDATA_STAT", "CATA_LABEL_SUBS_INFO", "STORAGE_HEAP_STAT",
    "QRY_DML_OPER_STATIS", "STORAGE_FSM_STAT", "STORAGE_TABLE_SHM_INFO", "CATA_VERTEX_LABEL_CHECK_INFO",
    "QRY_DML_INFO"};

// 第1张表为流量表，第2~13为威胁表，第14~53为报表，数组中30为表名长度，暂且限制为30个字节长度
static char allActualTableName[ALL_ACTUAL_TABLE_NUM][30] = {"trf_org", "thrt_org", "trf_mrg", "url_org", "sys_org",
    "server_org", "oper_org", "alarm_org", "pcap_org", "idm_org", "policy_org", "content_org", "maf_org",
    "thrtlog_type_name_1h", "thrtlog_attk_5m", "trflog_app_1h", "trflog_srcIp_5m", "thrtlog_attk_20m",
    "thrtlog_type_name_1d", "thrtlog_app_5m", "trflog_app_1d", "trflog_srcIp_20m", "thrtlog_attk_1h", "thrtlog_app_20m",
    "thrtlog_mrg_5m", "trfmrglog_mrg_5m", "trflog_srcIp_1h", "thrtlog_attk_1d", "thrtlog_app_1h", "thrtlog_app_1d",
    "trflog_srcIp_1d", "thrtlog_vctm_5m", "thrtlog_policy_5m", "thrtlog_mrg_20m", "thrtlog_policy_20m",
    "trflog_dstIp_5m", "trfmrglog_mrg_20m", "thrtlog_vctm_20m", "thrtlog_policy_1h", "trflog_dstIp_20m",
    "thrtlog_vctm_1h", "thrtlog_policy_1d", "thrtlog_mrg_1h", "trflog_dstIp_1h", "trfmrglog_mrg_1h", "thrtlog_vctm_1d",
    "trflog_dstIp_1d", "thrtlog_mrg_1d", "thrtlog_type_name_5m", "trflog_app_5m", "trfmrglog_mrg_1d",
    "thrtlog_type_name_20m", "trflog_app_20m"};

int createActualTable(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
    char *currentDir = getenv("PWD");
    TableBody tableBody = {0};
    tableBody.conn = conn;
    tableBody.stmt = stmt;
    // 建表使用长度，以防建表语句过长，采用2048长度
    char sqlCmd[SQL_LENGTH] = {0};
    // 建流量表，
    (void)sprintf(sqlCmd,
        "CREATE TABLE IF NOT EXISTS %s(logId INTEGER, startTime INTEGER, "
        "endTime INTEGER, vsysId TEXT, srcIp INET, dstIp INET, nat_srcIp INET, nat_dstIp INET, "
        "srcPort INTEGER, dstPort INTEGER, nat_srcPort INTEGER, nat_dstPort INTEGER, protocolType INTEGER, "
        "userId INTEGER, userGroupId INTEGER, appCategoryId INTEGER, subAppId INTEGER, appId INTEGER, "
        "srcZoneId INTEGER, dstZoneId INTEGER, secPolicyId INTEGER, qos_rule_id INTEGER, inIntfId INTEGER, "
        "outIntfId INTEGER, sendByte INTEGER, receiveByte INTEGER, send_pkt INTEGER, receive_pkt INTEGER, "
        "src_loc_id INTEGER, dst_loc_id INTEGER, src_mask_id INTEGER, dst_mask_id INTEGER, url_sub_category_id "
        "INTEGER, close_reason INTEGER, url_subcategory_type INTEGER, userName CHAR(129), natSrcIpv6 CHAR(33), "
#if defined(CPU_BIT_32)
        "natDstIpv6 CHAR(33)) WITH (time_col = 'endTime', interval = '1 hour', disk_limit = '512 MB', "
        "sensitive_col = 'secPolicyId', cache_size = 100);",
#else
        "natDstIpv6 CHAR(33)) WITH (time_col = 'endTime', interval = '1 hour', disk_limit = '2 GB', "
        "sensitive_col = 'secPolicyId', cache_size = 1000);",
#endif
        allActualTableName[0]);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(&tableBody, sqlCmd, cmdLen);

    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 建thrt_org威胁表
    (void)sprintf(sqlCmd,
        "CREATE TABLE IF NOT EXISTS %s(logId INTEGER, logTime INTEGER, srcIp INET, dstIp INET, "
        "nat_srcIp INET, nat_dstIp INET, src_loc_id INTEGER, dst_loc_id INTEGER, src_country_id INTEGER, "
        "dst_country_id INTEGER, user_type INTEGER, userId INTEGER, userGroupId INTEGER, event_num INTEGER, "
        "secPolicyId INTEGER, vsysId TEXT, protocolType INTEGER, srcPort INTEGER, dstPort INTEGER, "
        "nat_srcPort INTEGER, nat_dstPort INTEGER, srcZoneId INTEGER, dstZoneId INTEGER, "
        "appCategoryId INTEGER, subAppId INTEGER, appId INTEGER, profile_id INTEGER, attacker_type INTEGER, "
        "attacker INTEGER, vctmer_type INTEGER, vctmer INTEGER, threat_type INTEGER, severity INTEGER, "
        "action_type INTEGER, thrt_id INTEGER, thrtname_id INTEGER, virus_type INTEGER, virus_plat INTEGER, "
        "virus_family INTEGER, packet_id CHAR(64), appendData CHAR(512), MD5 CHAR(128), cappkt_flag INTEGER, "
        "ipssub_type INTEGER, risk INTEGER, file_type INTEGER, file_name CHAR(256), target INTEGER, os INTEGER, "
        "log_category INTEGER, role INTEGER, domain_name CHAR(256), intf_id INTEGER, startTime INTEGER, "
        "endTime INTEGER, single_packet INTEGER, packet_num INTEGER, max_speed INTEGER, src_mask_id INTEGER, "
        "dst_mask_id INTEGER, ips_reference CHAR(256), extendInfo CHAR(512), extendInfo_len INTEGER, "
        "direction_flag INTEGER, check_result CHAR(17), file_size INTEGER, action_len INTEGER, url_len INTEGER, "
        "sdbx_type INTEGER, malic_url CHAR(256), kill_chain INTEGER, attacker_flag INTEGER, "
        "forensics_type INTEGER, region1 CHAR(128), content1 CHAR(3072), region2 CHAR(128), content2 CHAR(3072), "
        "userName CHAR(129), natSrcIpv6 CHAR(33), natDstIpv6 CHAR(33)) WITH (time_col = 'endTime', "
#if defined(CPU_BIT_32)
        "interval = '1 hour', disk_limit = '256 MB', sensitive_col = 'secPolicyId', cache_size = 100);",
#else
        "interval = '1 hour', disk_limit = '512 MB', sensitive_col = 'secPolicyId', cache_size = 1000);",
#endif
        allActualTableName[1]);
    cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(&tableBody, sqlCmd, cmdLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 建剩下全部威胁表
    for (int i = 2; i < 13; i++) {
        (void)sprintf(sqlCmd,
            "CREATE TABLE IF NOT EXISTS %s(logId INTEGER, logTime INTEGER, maf_type INTEGER, "
            "vsysId TEXT, srcIp INET, dstIp INET, srcPort INTEGER, dstPort INTEGER, "
            "nat_srcIp INET, nat_dstIp INET, nat_srcPort INTEGER, nat_dstPort INTEGER, "
            "action_type INTEGER, subAppId INTEGER, appId INTEGER, appCategoryId INTEGER, "
            "protocolType INTEGER, srcZoneId INTEGER, dstZoneId INTEGER, profile_id INTEGER, "
            "secPolicyId INTEGER, maf_behavior INTEGER, filter_type INTEGER, group_id INTEGER, "
            "mailAddress CHAR(256), userName CHAR(129), event_num INTEGER) WITH (time_col = 'logTime', "
#if defined(CPU_BIT_32)
            "interval = '1 hour', disk_limit = '128 MB', sensitive_col = 'secPolicyId', cache_size = 100);",
#else
            "interval = '1 hour', disk_limit = '256 MB', sensitive_col = 'secPolicyId', cache_size = 5000);",
#endif
            allActualTableName[i]);
        cmdLen = strlen(sqlCmd);
        ret = reStartGmcExecDirect(&tableBody, sqlCmd, cmdLen);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    for (int i = 0; i < ALL_REPORT_TABLE_NUM; i++) {
        if (i < 10) {
            // 建报表，报表有ttl限制，共计10张表
            (void)sprintf(sqlCmd,
                "CREATE TABLE IF NOT EXISTS %s(logTime INTEGER, vsysId TEXT, ip_version INTEGER, srcIp INET, "
                "dstIp INET, nat_srcIp INET, nat_dstIp INET, inIntfId INTEGER, "
                "outIntfId INTEGER, src_loc_id INTEGER, dst_loc_id INTEGER, src_country_id INTEGER, "
                "dst_country_id INTEGER, userId INTEGER, appCategoryId INTEGER, subAppId INTEGER, "
                "appId INTEGER, app_casa_flag INTEGER, app_casa_behavior INTEGER, secPolicyId INTEGER, "
                "session_num INTEGER, consession_num INTEGER, sendByte INTEGER, receiveByte INTEGER, "
                "total_byte INTEGER) WITH (time_col = 'logTime', interval = '1 hour', "
#if defined(CPU_BIT_32)
                "ttl = '3 hour', disk_limit = '75 MB', sensitive_col = 'secPolicyId', table_path = '%s/newTablePath/', "
                "is_volatile_label = 'true');",
                allActualTableName[i + 13], currentDir);
#else
                "ttl = '3 hour', disk_limit = '75 MB', sensitive_col = 'secPolicyId');",
                allActualTableName[i + 13]);
#endif
            cmdLen = strlen(sqlCmd);
            ret = reStartGmcExecDirect(&tableBody, sqlCmd, cmdLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else if (i < 20) {
            // 建报表，报表有disklimit限制
            (void)sprintf(sqlCmd,
                "CREATE TABLE IF NOT EXISTS %s(logTime INTEGER, vsysId TEXT, ip_version INTEGER, srcIp INET, "
                "dstIp INET, nat_srcIp INET, nat_dstIp INET, inIntfId INTEGER, "
                "outIntfId INTEGER, src_loc_id INTEGER, dst_loc_id INTEGER, src_country_id INTEGER, "
                "dst_country_id INTEGER, userId INTEGER, appCategoryId INTEGER, subAppId INTEGER, "
                "appId INTEGER, app_casa_flag INTEGER, app_casa_behavior INTEGER, secPolicyId INTEGER, "
                "session_num INTEGER, consession_num INTEGER, sendByte INTEGER, receiveByte INTEGER, "
                "total_byte INTEGER) WITH (time_col = 'logTime', interval = '1 hour', "
#if defined(CPU_BIT_32)
                "disk_limit = '75 MB', sensitive_col = 'secPolicyId', table_path = '%s/newTablePath/', "
                "is_volatile_label = 'true');",
                allActualTableName[i + 13], currentDir);
#else
                "disk_limit = '75 MB', sensitive_col = 'secPolicyId');",
                allActualTableName[i + 13]);
#endif
            cmdLen = strlen(sqlCmd);
            ret = reStartGmcExecDirect(&tableBody, sqlCmd, cmdLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            // 建报表，报表不指定ttl和disklimit限制
            (void)sprintf(sqlCmd,
                "CREATE TABLE IF NOT EXISTS %s(logTime INTEGER, vsysId TEXT, ip_version INTEGER, srcIp INET, "
                "dstIp INET, nat_srcIp INET, nat_dstIp INET, inIntfId INTEGER, "
                "outIntfId INTEGER, src_loc_id INTEGER, dst_loc_id INTEGER, src_country_id INTEGER, "
                "dst_country_id INTEGER, userId INTEGER, appCategoryId INTEGER, subAppId INTEGER, "
                "appId INTEGER, app_casa_flag INTEGER, app_casa_behavior INTEGER, secPolicyId INTEGER, "
                "session_num INTEGER, consession_num INTEGER, sendByte INTEGER, receiveByte INTEGER, "
                "total_byte INTEGER) WITH (time_col = 'logTime', interval = '1 hour', disk_limit = '75 MB', "
#if defined(CPU_BIT_32)
                "sensitive_col = 'secPolicyId', table_path = '%s/newTablePath/', "
                "is_volatile_label = 'true');",
                allActualTableName[i + 13], currentDir);
#else
                "sensitive_col = 'secPolicyId');",
                allActualTableName[i + 13]);
#endif
            cmdLen = strlen(sqlCmd);
            ret = reStartGmcExecDirect(&tableBody, sqlCmd, cmdLen);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

int deleteAllActualTable(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
    TableBody tableBody = {0};
    tableBody.conn = conn;
    tableBody.stmt = stmt;
    char sqlCmd[SQL_LENGTH] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < ALL_ACTUAL_TABLE_NUM; i++) {
        // 删除所有表
        (void)sprintf(sqlCmd, "DROP TABLE %s;", allActualTableName[i]);
        cmdLen = strlen(sqlCmd);
        ret = reStartGmcExecDirect(&tableBody, sqlCmd, cmdLen);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

// 给流量表注入数据
int insertDataToFlewtable(TableBody *insertDataBody, TableInsertData *tableInsertData)
{
    int ret = 0;
    // 该值大小确定后续申请的数组大小，单次插入数据量超过5w时，本次长稳内保证是5w的倍数
    int dataCountMallocSize = 0;
    dataCountMallocSize = insertDataBody->dataCount > BULK_INSERT_SIZE ? BULK_INSERT_SIZE : insertDataBody->dataCount;
    // insertTimes大小决定了循环执行的次数，实际循环次数为insertTimes+1
    int insertTimes = insertDataBody->dataCount / BULK_INSERT_SIZE;
    time_t time_T;
    time_T = time(NULL);
    srand((uint32_t)time_T);
    char userNameHalf[TEXT_LEN] = "bob";
    char userNameOverHalf[TEXT_LEN] = "leyu";
    // 申请内存置空
    TableInsertDataToNull(tableInsertData);
    (void)memset(tableInsertData->natSrcIpv6, 0, sizeof(tableInsertData->natSrcIpv6));
    (void)memset(tableInsertData->natDstIpv6, 0, sizeof(tableInsertData->natDstIpv6));
    (void)memset(tableInsertData->srcIp, 0, sizeof(tableInsertData->srcIp));
    (void)memset(tableInsertData->dstIp, 0, sizeof(tableInsertData->dstIp));
    generate_random_string(tableInsertData->vsysId, dataCountMallocSize);
    generate_random_ip(tableInsertData->srcIp, dataCountMallocSize);
    generate_random_ip(tableInsertData->dstIp, dataCountMallocSize);
    if (insertTimes > 0) {
        for (int j = 0; j < insertTimes; j++) {
            for (int i = 0; i < BULK_INSERT_SIZE; i++) {
                // 初始化数据，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改
                tableInsertData->logId[i] = time_T + i;
                tableInsertData->endTime[i] =
                    insertDataBody->logTime + (i + j * BULK_INSERT_SIZE) / PER_SECOND_INSERT_SIZE;
                tableInsertData->startTime[i] = tableInsertData->endTime[i] + rand() % 20000 + 30000;
                tableInsertData->srcPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                tableInsertData->dstPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                tableInsertData->protocolType[i] = (i / PER_SECOND_INSERT_SIZE) / PER_SECOND_INSERT_SIZE + 1;
                tableInsertData->userId[i] = i / PER_SECOND_INSERT_SIZE;
                tableInsertData->userGroupId[i] = tableInsertData->userId[i] / PER_SECOND_INSERT_SIZE;
                tableInsertData->appCategoryId[i] = rand() % 1000 + 11000;
                tableInsertData->subAppId[i] = rand() % 1000 + 12000;
                tableInsertData->appId[i] = rand() % 10000;
                tableInsertData->srcZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
                tableInsertData->dstZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
                tableInsertData->secPolicyId[i] = rand() % 20;
                tableInsertData->inIntfId[i] = rand() % 20;
                tableInsertData->outIntfId[i] = rand() % 20;
                tableInsertData->sendByte[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                tableInsertData->receiveByte[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                if ((i * 2) < BULK_INSERT_SIZE) {
                    memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameHalf, TEXT_LEN);
                } else {
                    memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameOverHalf, TEXT_LEN);
                }
            }
            // 执行绑定和注入动作
            ret = reStartGmcPrepareStmtByLabelName(insertDataBody, GMC_OPERATION_SQL_INSERT);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
                break;
            }
            ret = GmcSetStmtAttr(
                insertDataBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &dataCountMallocSize, sizeof(int));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->startTime, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->endTime, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 3, (GmcDataTypeE)DB_DATATYPE_STRING, tableInsertData->vsysId, sizeof(char *), 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 4, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->srcIp, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->dstIp, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcPort, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 9, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstPort, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 12, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->protocolType, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 13, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 14, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userGroupId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 15, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appCategoryId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 16, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->subAppId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 17, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 18, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcZoneId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 19, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstZoneId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 20, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->secPolicyId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 22, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->inIntfId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 23, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->outIntfId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 24, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->sendByte, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 25, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->receiveByte, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 35, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->userName, TEXT_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 36, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->natSrcIpv6, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 37, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->natDstIpv6, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = reStartGmcExecute(insertDataBody);
            if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    } else {
        for (int i = 0; i < dataCountMallocSize; i++) {
            // 初始化数据，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改
            tableInsertData->logId[i] = time_T + i;
            tableInsertData->endTime[i] = insertDataBody->logTime + i / PER_SECOND_INSERT_SIZE;
            tableInsertData->startTime[i] = tableInsertData->endTime[i] + rand() % 20000 + 30000;
            tableInsertData->srcPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            tableInsertData->dstPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            tableInsertData->protocolType[i] = (i / PER_SECOND_INSERT_SIZE) / PER_SECOND_INSERT_SIZE + 1;
            tableInsertData->userId[i] = i / PER_SECOND_INSERT_SIZE;
            tableInsertData->userGroupId[i] = tableInsertData->userId[i] / PER_SECOND_INSERT_SIZE;
            tableInsertData->appCategoryId[i] = rand() % 1000 + 11000;
            tableInsertData->subAppId[i] = rand() % 1000 + 12000;
            tableInsertData->appId[i] = rand() % 10000;
            tableInsertData->srcZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
            tableInsertData->dstZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
            tableInsertData->secPolicyId[i] = rand() % 20;
            tableInsertData->inIntfId[i] = rand() % 20;
            tableInsertData->outIntfId[i] = rand() % 20;
            tableInsertData->sendByte[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            tableInsertData->receiveByte[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            if ((i * 2) < dataCountMallocSize) {
                memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameHalf, TEXT_LEN);
            } else {
                memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameOverHalf, TEXT_LEN);
            }
        }
        // 执行绑定和注入动作
        ret = reStartGmcPrepareStmtByLabelName(insertDataBody, GMC_OPERATION_SQL_INSERT);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
            return ret;
        }
        ret = GmcSetStmtAttr(insertDataBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &dataCountMallocSize, sizeof(int));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->startTime, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->endTime, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 3, (GmcDataTypeE)DB_DATATYPE_STRING, tableInsertData->vsysId, sizeof(char *), 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 4, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->srcIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->dstIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcPort, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 9, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstPort, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret =
            GmcBindCol(insertDataBody->stmt, 12, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->protocolType, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 13, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 14, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userGroupId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret =
            GmcBindCol(insertDataBody->stmt, 15, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appCategoryId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 16, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->subAppId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 17, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 18, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcZoneId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 19, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstZoneId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 20, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->secPolicyId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 22, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->inIntfId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 23, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->outIntfId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 24, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->sendByte, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 25, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->receiveByte, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 35, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->userName, TEXT_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 36, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->natSrcIpv6, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 37, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->natDstIpv6, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = reStartGmcExecute(insertDataBody);
        if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 给威胁表thrt_org注入数据
int insertDataToThreattable(TableBody *insertDataBody, TableInsertData *tableInsertData)
{
    int ret = 0;
    // 该值大小确定后续申请的数组大小，单次插入数据量超过5w时，本次长稳内保证是5w的倍数
    int dataCountMallocSize = 0;
    // 由于威胁表单条数据约10k，导致单次插入预计最多5000条，arm32场景不跟BULK_INSERT_SIZE做判断的话会导致溢出踩存
    int maxInsertToThrtCount = 5000 > BULK_INSERT_SIZE ? BULK_INSERT_SIZE : 5000;
    dataCountMallocSize =
        insertDataBody->dataCount > maxInsertToThrtCount ? maxInsertToThrtCount : insertDataBody->dataCount;
    // insertTimes大小决定了循环执行的次数，实际循环次数为insertTimes+1
    int insertTimes = insertDataBody->dataCount / maxInsertToThrtCount;
    // 定位信息用，正式使用时不放开printf("insertTimes is %d\n", insertTimes);
    time_t time_T;
    time_T = time(NULL);
    srand((uint32_t)time_T);
    char userNameHalf[TEXT_LEN] = "bob";
    char userNameOverHalf[TEXT_LEN] = "leyu";
    char content[CONTENT_LEN] = "CVE-2008-0017|CVE-2008-5012|CVE-2009-0017|CVE-2013-0217";
    // 申请内存置空
    TableInsertDataToNull(tableInsertData);
    (void)memset(tableInsertData->appendData, 0, sizeof(tableInsertData->appendData));
    (void)memset(tableInsertData->extendInfo, 0, sizeof(tableInsertData->extendInfo));
    (void)memset(tableInsertData->natSrcIpv6, 0, sizeof(tableInsertData->natSrcIpv6));
    (void)memset(tableInsertData->natDstIpv6, 0, sizeof(tableInsertData->natDstIpv6));
    (void)memset(tableInsertData->srcIp, 0, sizeof(tableInsertData->srcIp));
    (void)memset(tableInsertData->dstIp, 0, sizeof(tableInsertData->dstIp));
    generate_random_string(tableInsertData->vsysId, dataCountMallocSize);
    generate_random_ip(tableInsertData->srcIp, dataCountMallocSize);
    generate_random_ip(tableInsertData->dstIp, dataCountMallocSize);
    if (insertTimes > 0) {
        for (int j = 0; j < insertTimes; j++) {
            for (int i = 0; i < dataCountMallocSize; i++) {
                // 初始化数据，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改
                tableInsertData->logId[i] = time_T + i;
                tableInsertData->logTime[i] =
                    insertDataBody->logTime + (i + j * dataCountMallocSize) / PER_SECOND_INSERT_SIZE;
                tableInsertData->userId[i] = i / PER_SECOND_INSERT_SIZE;
                tableInsertData->userGroupId[i] = tableInsertData->userId[i] / PER_SECOND_INSERT_SIZE;
                tableInsertData->secPolicyId[i] = rand() % 20;
                tableInsertData->protocolType[i] = (i / PER_SECOND_INSERT_SIZE) / PER_SECOND_INSERT_SIZE + 1;
                tableInsertData->srcPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                tableInsertData->dstPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                tableInsertData->srcZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
                tableInsertData->dstZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
                tableInsertData->appCategoryId[i] = rand() % 1000 + 11000;
                tableInsertData->subAppId[i] = rand() % 1000 + 12000;
                tableInsertData->appId[i] = rand() % 10000;
                tableInsertData->endTime[i] =
                    insertDataBody->logTime + (i + j * dataCountMallocSize) / PER_SECOND_INSERT_SIZE;
                tableInsertData->startTime[i] = tableInsertData->endTime[i] + rand() % 20000 + 30000;
                memcpy(tableInsertData->content1 + i * CONTENT_LEN, (char *)content, CONTENT_LEN);
                memcpy(tableInsertData->content2 + i * CONTENT_LEN, (char *)content, CONTENT_LEN);
                if ((i * 2) < dataCountMallocSize) {
                    memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameHalf, TEXT_LEN);
                } else {
                    memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameOverHalf, TEXT_LEN);
                }
            }
            // 执行绑定和注入动作
            ret = reStartGmcPrepareStmtByLabelName(insertDataBody, GMC_OPERATION_SQL_INSERT);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
                break;
            }
            ret = GmcSetStmtAttr(
                insertDataBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &dataCountMallocSize, sizeof(int));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logTime, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->srcIp, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->dstIp, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 11, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 12, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userGroupId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 14, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->secPolicyId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 15, (GmcDataTypeE)DB_DATATYPE_STRING, tableInsertData->vsysId, sizeof(char *), 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 16, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->protocolType, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 17, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcPort, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 18, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstPort, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 21, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcZoneId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 22, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstZoneId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 23, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appCategoryId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 24, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->subAppId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 25, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 53, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->startTime, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 54, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->endTime, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 40, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->appendData,
                APPEND_DATA_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 61, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->extendInfo,
                APPEND_DATA_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 74, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->content1, CONTENT_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 76, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->content2, CONTENT_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 77, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->userName, TEXT_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 78, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->natSrcIpv6, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 79, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->natDstIpv6, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = reStartGmcExecute(insertDataBody);
            if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    } else {
        for (int i = 0; i < dataCountMallocSize; i++) {
            // 初始化数据，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改
            tableInsertData->logId[i] = time_T + i;
            tableInsertData->logTime[i] = insertDataBody->logTime + i / PER_SECOND_INSERT_SIZE;
            tableInsertData->userId[i] = i / PER_SECOND_INSERT_SIZE;
            tableInsertData->userGroupId[i] = tableInsertData->userId[i] / PER_SECOND_INSERT_SIZE;
            tableInsertData->secPolicyId[i] = rand() % 20;
            tableInsertData->protocolType[i] = (i / PER_SECOND_INSERT_SIZE) / PER_SECOND_INSERT_SIZE + 1;
            tableInsertData->srcPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            tableInsertData->dstPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            tableInsertData->srcZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
            tableInsertData->dstZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
            tableInsertData->appCategoryId[i] = rand() % 1000 + 11000;
            tableInsertData->subAppId[i] = rand() % 1000 + 12000;
            tableInsertData->appId[i] = rand() % 10000;
            tableInsertData->endTime[i] = insertDataBody->logTime + i / PER_SECOND_INSERT_SIZE;
            tableInsertData->startTime[i] = tableInsertData->endTime[i] + rand() % 20000 + 30000;
            memcpy(tableInsertData->content1 + i * CONTENT_LEN, (char *)content, CONTENT_LEN);
            memcpy(tableInsertData->content2 + i * CONTENT_LEN, (char *)content, CONTENT_LEN);
            if ((i * 2) < dataCountMallocSize) {
                memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameHalf, TEXT_LEN);
            } else {
                memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameOverHalf, TEXT_LEN);
            }
        }
        // 执行绑定和注入动作
        ret = reStartGmcPrepareStmtByLabelName(insertDataBody, GMC_OPERATION_SQL_INSERT);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
            return ret;
        }
        ret = GmcSetStmtAttr(insertDataBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &dataCountMallocSize, sizeof(int));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logTime, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->srcIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->dstIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 11, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 12, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userGroupId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 14, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->secPolicyId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 15, (GmcDataTypeE)DB_DATATYPE_STRING, tableInsertData->vsysId, sizeof(char *), 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret =
            GmcBindCol(insertDataBody->stmt, 16, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->protocolType, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 17, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcPort, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 18, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstPort, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 21, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcZoneId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 22, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstZoneId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret =
            GmcBindCol(insertDataBody->stmt, 23, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appCategoryId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 24, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->subAppId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 25, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 53, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->startTime, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 54, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->endTime, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 40, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->appendData, APPEND_DATA_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 61, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->extendInfo, APPEND_DATA_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 74, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->content1, CONTENT_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 76, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->content2, CONTENT_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 77, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->userName, TEXT_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 78, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->natSrcIpv6, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 79, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->natDstIpv6, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = reStartGmcExecute(insertDataBody);
        if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 给除thrt_org外的威胁表注入数据
int insertDataToOtherThreattable(TableBody *insertDataBody, TableInsertData *tableInsertData)
{
    int ret = 0;
    // 该值大小确定后续申请的数组大小，单次插入数据量超过5w时，本次长稳内保证是5w的倍数
    int dataCountMallocSize = 0;
    dataCountMallocSize = insertDataBody->dataCount > BULK_INSERT_SIZE ? BULK_INSERT_SIZE : insertDataBody->dataCount;
    // insertTimes大小决定了循环执行的次数，实际循环次数为insertTimes+1
    int insertTimes = insertDataBody->dataCount / BULK_INSERT_SIZE;
    // printf("threat table insertTimes is %d\n", insertTimes);
    time_t time_T;
    time_T = time(NULL);
    srand((uint32_t)time_T);
    char userNameHalf[TEXT_LEN] = "bob";
    char userNameOverHalf[TEXT_LEN] = "leyu";
    // 申请内存置空
    TableInsertDataToNull(tableInsertData);
    (void)memset(tableInsertData->mailAddress, 0, sizeof(tableInsertData->mailAddress));
    (void)memset(tableInsertData->srcIp, 0, sizeof(tableInsertData->srcIp));
    (void)memset(tableInsertData->dstIp, 0, sizeof(tableInsertData->dstIp));
    generate_random_string(tableInsertData->vsysId, dataCountMallocSize);
    generate_random_ip(tableInsertData->srcIp, dataCountMallocSize);
    generate_random_ip(tableInsertData->dstIp, dataCountMallocSize);

    if (insertTimes > 0) {
        for (int j = 0; j < insertTimes; j++) {
            for (int i = 0; i < BULK_INSERT_SIZE; i++) {
                // 初始化数据，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改
                tableInsertData->logId[i] = time_T + i;
                tableInsertData->logTime[i] =
                    insertDataBody->logTime + (i + j * BULK_INSERT_SIZE) / PER_SECOND_INSERT_SIZE;
                tableInsertData->srcPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                tableInsertData->dstPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                tableInsertData->subAppId[i] = rand() % 1000 + 12000;
                tableInsertData->appId[i] = rand() % 10000;
                tableInsertData->appCategoryId[i] = rand() % 1000 + 11000;
                tableInsertData->protocolType[i] = (i / PER_SECOND_INSERT_SIZE) / PER_SECOND_INSERT_SIZE + 1;
                tableInsertData->srcZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
                tableInsertData->dstZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
                tableInsertData->secPolicyId[i] = rand() % 20;
                if ((i * 2) < BULK_INSERT_SIZE) {
                    memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameHalf, TEXT_LEN);
                } else {
                    memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameOverHalf, TEXT_LEN);
                }
            }
            // 执行绑定和注入动作
            ret = reStartGmcPrepareStmtByLabelName(insertDataBody, GMC_OPERATION_SQL_INSERT);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
                break;
            }
            ret = GmcSetStmtAttr(
                insertDataBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &dataCountMallocSize, sizeof(int));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logTime, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 3, (GmcDataTypeE)DB_DATATYPE_STRING, tableInsertData->vsysId, sizeof(char *), 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 4, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->srcIp, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->dstIp, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcPort, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstPort, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 13, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->subAppId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 14, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 15, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appCategoryId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 16, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->protocolType, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 17, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcZoneId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 18, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstZoneId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 20, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->secPolicyId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 24, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->mailAddress,
                MAIL_ADDRESS_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 25, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->userName, TEXT_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = reStartGmcExecute(insertDataBody);
            if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    } else {
        for (int i = 0; i < dataCountMallocSize; i++) {
            // 初始化数据，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改
            tableInsertData->logId[i] = time_T + i;
            tableInsertData->logTime[i] = insertDataBody->logTime + i / PER_SECOND_INSERT_SIZE;
            tableInsertData->srcPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            tableInsertData->dstPort[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            tableInsertData->subAppId[i] = rand() % 1000 + 12000;
            tableInsertData->appId[i] = rand() % 10000;
            tableInsertData->appCategoryId[i] = rand() % 1000 + 11000;
            tableInsertData->protocolType[i] = (i / PER_SECOND_INSERT_SIZE) / PER_SECOND_INSERT_SIZE + 1;
            tableInsertData->srcZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
            tableInsertData->dstZoneId[i] = (rand() % 9 + ((rand() % 13) / 10) * 20000);
            tableInsertData->secPolicyId[i] = rand() % 20;
            if ((i * 2) < dataCountMallocSize) {
                memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameHalf, TEXT_LEN);
            } else {
                memcpy((tableInsertData->userName + i * TEXT_LEN), (char *)userNameOverHalf, TEXT_LEN);
            }
        }
        // 执行绑定和注入动作
        ret = reStartGmcPrepareStmtByLabelName(insertDataBody, GMC_OPERATION_SQL_INSERT);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
            return ret;
        }
        ret = GmcSetStmtAttr(insertDataBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &dataCountMallocSize, sizeof(int));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logTime, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 3, (GmcDataTypeE)DB_DATATYPE_STRING, tableInsertData->vsysId, sizeof(char *), 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 4, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->srcIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->dstIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcPort, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstPort, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 13, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->subAppId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 14, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret =
            GmcBindCol(insertDataBody->stmt, 15, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appCategoryId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret =
            GmcBindCol(insertDataBody->stmt, 16, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->protocolType, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 17, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->srcZoneId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 18, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->dstZoneId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 20, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->secPolicyId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 24, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->mailAddress,
            MAIL_ADDRESS_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 25, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->userName, TEXT_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = reStartGmcExecute(insertDataBody);
        if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 给报表注入数据
int insertDataToReportTable(TableBody *insertDataBody, TableInsertData *tableInsertData)
{
    int ret = 0;
    // 该值大小确定后续申请的数组大小，单次插入数据量超过5w时，本次长稳内保证是5w的倍数
    int dataCountMallocSize = 0;
    dataCountMallocSize = insertDataBody->dataCount > BULK_INSERT_SIZE ? BULK_INSERT_SIZE : insertDataBody->dataCount;
    // insertTimes大小决定了循环执行的次数，实际循环次数为insertTimes+1
    int insertTimes = insertDataBody->dataCount / BULK_INSERT_SIZE;
    // printf("report table insertTimes is %d\n", insertTimes);
    time_t time_T;
    time_T = time(NULL);
    srand((uint32_t)time_T);
    // 申请内存置空
    TableInsertDataToNull(tableInsertData);
    (void)memset(tableInsertData->srcIp, 0, sizeof(tableInsertData->srcIp));
    (void)memset(tableInsertData->dstIp, 0, sizeof(tableInsertData->dstIp));
    generate_random_string(tableInsertData->vsysId, dataCountMallocSize);
    generate_random_ip(tableInsertData->srcIp, dataCountMallocSize);
    generate_random_ip(tableInsertData->dstIp, dataCountMallocSize);

    if (insertTimes > 0) {
        for (int j = 0; j < insertTimes; j++) {
            for (int i = 0; i < BULK_INSERT_SIZE; i++) {
                // 初始化数据，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改
                tableInsertData->logTime[i] =
                    insertDataBody->logTime + (i + j * BULK_INSERT_SIZE) / PER_SECOND_INSERT_SIZE;
                tableInsertData->inIntfId[i] = rand() % 20;
                tableInsertData->userId[i] = i / PER_SECOND_INSERT_SIZE;
                tableInsertData->appCategoryId[i] = rand() % 1000 + 11000;
                tableInsertData->subAppId[i] = rand() % 1000 + 12000;
                tableInsertData->appId[i] = rand() % 10000;
                tableInsertData->secPolicyId[i] = rand() % 20;
                tableInsertData->sendByte[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
                tableInsertData->receiveByte[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            }
            // 执行绑定和注入动作
            ret = reStartGmcPrepareStmtByLabelName(insertDataBody, GMC_OPERATION_SQL_INSERT);
            if (ret != GMERR_UNDEFINED_TABLE) {
                if (ret != GMERR_OK) {
                    AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
                    break;
                }
            }
            ret = GmcSetStmtAttr(
                insertDataBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &dataCountMallocSize, sizeof(int));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logTime, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_STRING, tableInsertData->vsysId, sizeof(char *), 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->srcIp, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 4, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->dstIp, IPV6_LEN, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->inIntfId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 13, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 14, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appCategoryId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 15, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->subAppId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(insertDataBody->stmt, 16, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 19, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->secPolicyId, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret =
                GmcBindCol(insertDataBody->stmt, 22, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->sendByte, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBindCol(
                insertDataBody->stmt, 23, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->receiveByte, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = reStartGmcExecute(insertDataBody);
            if (ret != GMERR_UNEXPECTED_NULL_VALUE && ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE &&
                ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    } else {
        for (int i = 0; i < dataCountMallocSize; i++) {
            // 初始化数据，部分使用具体数字原因为数据构造使用无特殊意义，一般不进行修改
            tableInsertData->logTime[i] = insertDataBody->logTime + i / PER_SECOND_INSERT_SIZE;

            tableInsertData->inIntfId[i] = rand() % 20;
            tableInsertData->userId[i] = i / PER_SECOND_INSERT_SIZE;
            tableInsertData->appCategoryId[i] = rand() % 1000 + 11000;
            tableInsertData->subAppId[i] = rand() % 1000 + 12000;
            tableInsertData->appId[i] = rand() % 10000;
            tableInsertData->secPolicyId[i] = rand() % 20;
            tableInsertData->sendByte[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
            tableInsertData->receiveByte[i] = rand() % (MAX_PORT_RAND_NUM - MIN_PORT_RAND_NUM) + MIN_PORT_RAND_NUM;
        }
        // 执行绑定和注入动作
        ret = reStartGmcPrepareStmtByLabelName(insertDataBody, GMC_OPERATION_SQL_INSERT);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
            return ret;
        }
        ret = GmcSetStmtAttr(insertDataBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &dataCountMallocSize, sizeof(int));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->logTime, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(
            insertDataBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_STRING, tableInsertData->vsysId, sizeof(char *), 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->srcIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 4, (GmcDataTypeE)DB_DATATYPE_FIXED, tableInsertData->dstIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->inIntfId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 13, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->userId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret =
            GmcBindCol(insertDataBody->stmt, 14, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appCategoryId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 15, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->subAppId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 16, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->appId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 19, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->secPolicyId, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 22, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->sendByte, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(insertDataBody->stmt, 23, (GmcDataTypeE)DB_DATATYPE_INT64, tableInsertData->receiveByte, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = reStartGmcExecute(insertDataBody);
        if (ret != GMERR_UNEXPECTED_NULL_VALUE && ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE &&
            ret != GMERR_TABLE_EXCEED_DISK_LIMIT && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    return ret;
}

// 获取关键字value
int64_t GetViewFieldResultValue(const char *viewName, const char *key)
{
    char cmdOutput[SQL_LENGTH] = {0};
    static int64_t result = {0};
    int i = 0;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[SQL_LENGTH] = {0};
    (void)snprintf(command, SQL_LENGTH, "%s |grep %s |awk -F ':' '{print $2}'", viewName, key);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_ERROR, "popen(%s) error./n", command);
        return FAILED;
    }
    while (NULL != fgets(cmdOutput, SQL_LENGTH, pf) && i < SQL_LENGTH) {
        result = atoll(cmdOutput);
        i++;
    };
    pclose(pf);

    return result;
}

int64_t getTableDIskUsage(TableBody *insertDataBody)
{
    char sqlCmd[SQL_LENGTH] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name='%s'\" -s %s",
        insertDataBody->tableName, g_connServerTsdb);
    char filterStr[SQL_LENGTH] = "DISK_USAGE";
    int64_t cDiskUsage = GetViewFieldResultValue(sqlCmd, filterStr);
    return cDiskUsage;
}

// 对表新增10列
void alterTableWithNewCol(TableBody *insertDataBody)
{
    int ret = 0;
    uint32_t cmdLen = 0;
    char sqlCmd[SQL_LENGTH] = {0};
    for (int i = 1; i < 10; i++) {
        // 创建含有alter的sql语句
        (void)sprintf(sqlCmd, "alter table %s add newValue%u integer;", insertDataBody->tableName, i);
        cmdLen = strlen(sqlCmd);
        ret = reStartGmcExecDirect(insertDataBody, sqlCmd, cmdLen);
        if (ret != GMERR_DUPLICATE_COLUMN) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

void simpleLogicTableProcess(TableBody *tableBody)
{
    int ret = 0;
    char sqlCmd[SQL_LENGTH] = {0};
    char tempTable[64] = "tempTable";
    char *currentDir = getenv("PWD");
    // 建逻辑表
    (void)sprintf(sqlCmd,
        "CREATE TABLE IF NOT EXISTS %s(id integer, time integer, age integer, value1 integer, "
        "value2 integer, value3 integer, value4 integer, value5 inet, name char(64)) "
        "WITH (time_col = 'time', interval = '1 day', sensitive_col = 'id', table_path = '%s/newTablePath/', "
        "is_volatile_label = 'true');",
        tempTable, currentDir);
    uint32_t cmdLen = strlen(sqlCmd);
    tableBody->tableName = tempTable;
    tableBody->dataCount = INIT_REPORT_TABLE_SIZE;
    tableBody->logTime = 0;

    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    int tryTimes = 0;
    while (ret == GMERR_LOCK_NOT_AVAILABLE && tryTimes < 5) {
        sleep(60);
        ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
        tryTimes++;
    }
    time_t time_T;
    time_T = time(NULL);
    int currentTime = time_T;
    // 注入数据
    int64_t cyclesTimes = 300;
    const int64_t count = BULK_INSERT_SIZE;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    int64_t value[count] = {0};
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed.\n");
        return;
    }
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char *valueIp = (char *)malloc(count * IPV6_LEN);
    if (valueIp == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return;
    }
    (void)memset(valueIp, 0, sizeof(valueIp));
    generate_random_ip(valueIp, count);

    srand(currentTime);
    for (int j = 0; j < cyclesTimes; j++) {
        if (j == cyclesTimes / 2) {
            // 目的表表升级
            alterTableWithNewCol(tableBody);
            sleep(20);
        }
        for (int i = 0; i < count; i++) {
            id[i] = i + 1 + (j * BULK_INSERT_SIZE);
            time[i] = currentTime + i + (j * BULK_INSERT_SIZE);
            age[i] = rand() % 75;
            value[i] = rand() % BULK_INSERT_SIZE;
            if (i % 10 == 0) {
                memcpy((name + i * 64), (char *)nameSource, 640);
            }
        }
        ret = reStartGmcPrepareStmtByLabelName(tableBody, GMC_OPERATION_SQL_INSERT);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "prepare table failed.\n");
            break;
        }
        ret = GmcSetStmtAttr(tableBody->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(tableBody->stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(tableBody->stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(tableBody->stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, age, 0, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        for (int k = 3; k < 7; k++) {
            ret = GmcBindCol(tableBody->stmt, k, (GmcDataTypeE)DB_DATATYPE_INT64, value, 0, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcBindCol(tableBody->stmt, 7, (GmcDataTypeE)DB_DATATYPE_FIXED, valueIp, IPV6_LEN, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBindCol(tableBody->stmt, 8, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(nameSource[0]), 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (j > cyclesTimes / 2) {
            for (int k = 9; k < 15; k++) {
                ret = GmcBindCol(tableBody->stmt, k, (GmcDataTypeE)DB_DATATYPE_INT64, value, 0, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
        ret = reStartGmcExecute(tableBody);
        if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_UNEXPECTED_NULL_VALUE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    // 查询数据
    (void)sprintf(sqlCmd, "select * from %s limit 50000;", tempTable);
    cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    if (ret == GMERR_INVALID_BUFFER) {
        return;
    } else {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    int64_t reaultCount = 10;
    ret = GmcGetStmtAttr(tableBody->stmt, GMC_STMT_ATTR_RESULT_ROWS, &reaultCount, sizeof(int));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 删逻辑表
    (void)sprintf(sqlCmd, "drop table %s;", tempTable);
    cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    freeHeapMemory(name);
    freeHeapMemory(valueIp);
}

void continuousCreateAndDeleteLogicTable(TableBody *tableBody)
{
    int ret = 0;
    char sqlCmd[SQL_LENGTH] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < CONTINUOUS_CREATE_TABLE_NUM; i++) {
        (void)sprintf(sqlCmd,
            "CREATE TABLE IF NOT EXISTS tempTable%u(id integer, time integer, age integer, "
            "value1 integer, value2 integer, value3 integer, value4 integer, "
            "value5 inet, name char(64)) "
            "WITH (time_col = 'time', interval = '1 hour', sensitive_col = 'id');",
            i);
        cmdLen = strlen(sqlCmd);
        ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
        int tryTimes = 0;
        while (ret == GMERR_LOCK_NOT_AVAILABLE && tryTimes < 5) {
            sleep(20);
            ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
            tryTimes++;
        }
    }
    sleep(30);
    for (int i = 0; i < CONTINUOUS_CREATE_TABLE_NUM; i++) {
        (void)sprintf(sqlCmd, "drop table tempTable%u;", i);
        cmdLen = strlen(sqlCmd);
        ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

// 对给定的目的表执行insert into动作将源表中1w条数据插入目的表中
int insertIntoTable(TableBody *purposeTableBody, TableBody *sourceTableBody)
{
    int ret = 0;
    char sqlCmd[SQL_LENGTH] = {0};
    // 创建含有insert into的sql语句
    (void)sprintf(sqlCmd,
        "insert into %s select logTime, vsysId, ip_version, srcIp, "
        "dstIp, nat_srcIp, nat_dstIp, inIntfId, "
        "outIntfId, src_loc_id, dst_loc_id, src_country_id, "
        "dst_country_id, userId, appCategoryId, subAppId, "
        "appId, app_casa_flag, app_casa_behavior, secPolicyId, "
        "session_num, consession_num, sendByte, receiveByte, "
        "total_byte from %s limit %d;",
        purposeTableBody->tableName, sourceTableBody->tableName, sourceTableBody->dataCount);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(sourceTableBody, sqlCmd, cmdLen);
    if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

// 删表
void dropTable(TableBody *tableBody)
{
    int ret = 0;
    char sqlCmd[SQL_LENGTH] = {0};
    // 创建drop删表语句
    (void)sprintf(sqlCmd, "drop table %s;", tableBody->tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    if (ret != GMERR_UNDEFINED_TABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

// 对指定表进行指定数据量大小的copy to操作，目的地址为当前目录下data.csv
void copyToWithTable(TableBody *copyToTableBody)
{
    int ret = 0;
    sleep(5);
    char sqlCmd[SQL_LENGTH] = {0};
    char *currentDir = getenv("PWD");
    if (currentDir == NULL) {
        printf("getenv failed\n");
    }
    // 创建含有copy to的sql语句
    (void)sprintf(sqlCmd, "COPY (SELECT * FROM %s LIMIT %d) TO '%s/data.csv';", copyToTableBody->tableName,
        copyToTableBody->dataCount, currentDir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(copyToTableBody, sqlCmd, cmdLen);
    if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

// 随机在1张流量表，3张威胁表，4张报表中选择一张表进行copy to操作
void simpleCopyTo(int seed)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t time_T;

    char copyToOperateTableList[8][30] = {"trf_org", "thrt_org", "trf_mrg", "url_org", "thrtlog_type_name_1h",
        "thrtlog_app_20m", "thrtlog_mrg_20m", "trflog_dstIp_1h"};
    int operateTableIndex = 0;
    int logTime;
    TableBody copyToTableBody = {0};
    copyToTableBody.conn = conn;
    copyToTableBody.stmt = stmt;
    while (true) {
        time_T = time(NULL);
        logTime = time_T + seed;
        srand(logTime);
        operateTableIndex = rand() % 8;
        copyToTableBody.tableName = copyToOperateTableList[operateTableIndex];
        copyToTableBody.dataCount = COPY_TO_SIZE;
        copyToTableBody.logTime = 0;
        copyToWithTable(&copyToTableBody);
#if defined(CPU_BIT_32)
        sleep(rand() % 50 + 20);
#else
        sleep(rand() % 5 + 20);
#endif
    }
    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 查询结果直接写表insert into、表升级（表schama修改，新增列）、对升级后表注入数据、删表、重新建表
void resultInsertAndTableUpgrades(int seed)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    time_t time_T;
    // 设置源表，源表使用固定的第1张报表
    int operateTableIndex = 0;
    TableBody sourceTableBody = {conn, stmt, allActualTableName[13], INSERT_INTO_SIZE, 0};
    TableBody purposeTableBody = {0};
    purposeTableBody.conn = conn;
    purposeTableBody.stmt = stmt;
    int logTime = 0;

    while (true) {
        time_T = time(NULL);
        logTime = time_T + seed;
        srand(logTime);
        // 设置目的表，确保目的表和源表不是同一张表
        operateTableIndex = rand() % 38 + 14;
        purposeTableBody.tableName = allActualTableName[operateTableIndex];
        purposeTableBody.dataCount = INIT_REPORT_TABLE_SIZE;
        purposeTableBody.logTime = logTime;
        // insert into执行步骤
        ret = insertIntoTable(&purposeTableBody, &sourceTableBody);
        if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
#if defined(CPU_BIT_32)
        sleep(rand() % 50 + 20);
#else
        sleep(rand() % 5 + 20);
#endif
    }

    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 尝试修改tsAllowDiskClean值，90%改成1，10%改成0
void changeDiskFullPolicy(TableBody *tableBody)
{
    int ret = 0;
    time_t time_T;
    time_T = time(NULL);
    srand(time_T);
    uint32_t setValue = 1;
    int randValue = rand() % 10;
    if (randValue < 9) {
        setValue = 1;
    } else {
        setValue = 0;
    }
    // 修改配置项tsAllowDiskClean为0
    ret = GmcSetCfg(tableBody->stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 循环以下动作，新建一张逻辑表，多次注入数据，查询全表检验数据量，最后删除整张表
// 循环以下动作，连续建256张表，连续删创建的16张表
void createLogicTableAndDelete()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    time_t time_T;
    time_T = time(NULL);
    int64_t currentTime = 0;
    int64_t lastSecondSceneTime = 0;
    TableBody tableBody = {0};
    tableBody.conn = conn;
    tableBody.stmt = stmt;

    while (true) {
        simpleLogicTableProcess(&tableBody);
        sleep(115);
        time_T = time(NULL);
        currentTime = time_T;
        if ((currentTime - lastSecondSceneTime) > TEN_MINUTES) {
            // 每隔600s尝试修改tsAllowDiskClean值，90%改成1，10%改成0
            changeDiskFullPolicy(&tableBody);
            continuousCreateAndDeleteLogicTable(&tableBody);
            lastSecondSceneTime = time_T;
        }
        sleep(5);
    }
    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 常驻线程，一直对随机一张表进行注入随机数据量的注入动作，不sleep
int residentInsertThread(TableInsertData *tableInsertData)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建立连接
    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    time_t time_T;
    int dataCount = 0;
    int logTime = 0;
    int operateTableIndex = 0;
    int insertTableType = 0;
    TableBody insertDataTableBody = {0};
    int64_t insertExcuteTimes = 0;
    insertDataTableBody.conn = conn;
    insertDataTableBody.stmt = stmt;

    while (true) {
        time_T = time(NULL);
        logTime = time_T;
        srand(logTime);
        insertTableType = rand() % 5;
        if (insertTableType == 0) {
            operateTableIndex = 0;
        } else if (insertTableType == 1) {
            operateTableIndex = rand() % ALL_THREAT_TABLE_NUM + 1;
        } else {
            operateTableIndex = rand() % ALL_REPORT_TABLE_NUM + 13;
        }
        dataCount = rand() % BULK_INSERT_SIZE + 1;
        insertDataTableBody.tableName = allActualTableName[operateTableIndex];
        insertDataTableBody.dataCount = dataCount;
        insertDataTableBody.logTime = logTime;

        if (operateTableIndex == 0) {
            ret = insertDataToFlewtable(&insertDataTableBody, tableInsertData);
            if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else if (operateTableIndex == 1) {
            ret = insertDataToThreattable(&insertDataTableBody, tableInsertData);
            if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else if (operateTableIndex < 13) {
            ret = insertDataToOtherThreattable(&insertDataTableBody, tableInsertData);
            if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        } else {
            ret = insertDataToReportTable(&insertDataTableBody, tableInsertData);
            if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
#if defined(CPU_BIT_32)
        DbSleep(rand() % 500 + 1);
#else
        DbSleep(rand() % 100 + 1);
#endif
    }

    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 常驻线程，间隔10s，查询全部预置表磁盘使用视图，超过预定值时，执行aging（）动作
void residentViewThread()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建立连接
    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    int64_t flewMaxSize = 2147483648;
    int64_t threatMaxSize = 536870912;
    int64_t threatOtherMaxSize = 268435456;
    int64_t reportMaxSize = 78643200;
    int times = 0;

    uint32_t cmdLen = 0;
    int64_t actual_disk_usage = 0;
    uint32_t size = sizeof(actual_disk_usage);
    char agingSql[64] = {0};
    TableBody tableBody = {0};
    tableBody.conn = conn;
    tableBody.stmt = stmt;

    int i = 0;
    while (true) {
        for (i = 0; i < ALL_ACTUAL_TABLE_NUM; i++) {
            tableBody.tableName = allActualTableName[i];
            actual_disk_usage = getTableDIskUsage(&tableBody);
            if (i == 0) {
                if (actual_disk_usage > flewMaxSize) {
                    (void)sprintf(agingSql, "SELECT tsdb_aging('%s');", allActualTableName[i]);
                    cmdLen = strlen(agingSql);
                    ret = reStartGmcExecDirect(&tableBody, agingSql, cmdLen);
                    if (ret != GMERR_LOCK_NOT_AVAILABLE) {
                        TEST_EXPECT_INT32(GMERR_OK, ret);
                    }
                }
            } else if (i == 1) {
                if (actual_disk_usage > threatMaxSize) {
                    (void)sprintf(agingSql, "SELECT tsdb_aging('%s');", allActualTableName[i]);
                    cmdLen = strlen(agingSql);
                    ret = reStartGmcExecDirect(&tableBody, agingSql, cmdLen);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            } else if (i < 13) {
                if (actual_disk_usage > threatOtherMaxSize) {
                    (void)sprintf(agingSql, "SELECT tsdb_aging('%s');", allActualTableName[i]);
                    cmdLen = strlen(agingSql);
                    ret = reStartGmcExecDirect(&tableBody, agingSql, cmdLen);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            } else if (i > 22) {
                if (actual_disk_usage > reportMaxSize) {
                    (void)sprintf(agingSql, "SELECT tsdb_aging('%s');", allActualTableName[i]);
                    cmdLen = strlen(agingSql);
                    ret = reStartGmcExecDirect(&tableBody, agingSql, cmdLen);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
#if defined(CPU_BIT_32)
            DbSleep(500);
#else
            DbSleep(100);
#endif

            times++;
        }
        sleep(60);
    }

    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void randomSelectFlewTable(TableBody *tableBody, char *tabelName, int opreatSqlFlag)
{
    int ret = 0;
    char querySql[SQL_LENGTH] = {0};
    // 参数化查询填值为50
    int parameteredLength = 50;
    switch (opreatSqlFlag) {
        case 0:
            // 查询全表
            (void)sprintf(querySql, "select * from %s limit 100000", tabelName);
            break;
        case 1:
            // 高基数分组聚合
            (void)sprintf(querySql,
                "select endTime, first(srcIp), last(dstIp), sum(logId + 10) "
                "from %s where inIntfId != 10 and outIntfId < 19 group by endTime limit 100000",
                tabelName);
            break;
        case 2:
            // 分组为多列且排序为多列，查询语句中有常量
            (void)sprintf(querySql,
                "select 1234, endTime, vsysId, userGroupId, secPolicyId, length(dstPort), first(logId), "
                "last(startTime), count(dstIp) from %s where inIntfId != 10 and outIntfId < 19 and "
                "length(srcPort) = 5 group by endTime, userGroupId, secPolicyId order by "
                "secPolicyId ASC, length(dstPort) DESC limit 100000",
                tabelName);
            break;
        case 3:
            // 字符串等值查询（原场景应该为like查询，但是该场景耗时太长，flew表数据会很多，导致写锁拿不到）
            (void)sprintf(querySql,
                "select endTime, secPolicyId from %s where inIntfId != 10 and outIntfId < 19 and "
                "length(srcPort) = 5 and userName like 'leyu' group by endTime, secPolicyId order by "
                "secPolicyId ASC limit 100",
                tabelName);
            break;
        case 4:
            // 查询TEXT相关聚合函数
            (void)sprintf(querySql,
                "select endTime, vsysId, max(vsysId), length(vsysId) from %s where "
                "length(vsysId) < 50 and inIntfId > 15 order by endTime",
                tabelName);
            break;
        case 5:
            // 参数化查询inet类型作为高基数查询
            (void)sprintf(querySql,
                "select srcIp, last(dstIp) from %s where inIntfId != 10 "
                "and length(vsysId) < ? group by srcIp "
                "order by srcIp ASC, last(dstIp) DESC limit 10000",
                tabelName);
            TEST_EXPECT_INT32(GMERR_OK, GmcPrepareSql(tableBody->stmt, querySql, strlen(querySql)));
            TEST_EXPECT_INT32(GMERR_OK, GmcBindPara(tableBody->stmt, 1, GMC_DATATYPE_INT64, &parameteredLength, 0));
            break;
        case 6:
            // 表达式a+b和limit带offset
            (void)sprintf(querySql,
                "select srcIp, srcPort + dstPort from %s where inIntfId != 10 and appId + subAppId < 15000"
                "order by srcIp ASC limit 10000 offset 10000",
                tabelName);
            break;
        case 7:
            // 聚合函数avg
            (void)sprintf(querySql,
                "select endTime, avg(startTime), avg(logId) "
                "from %s where inIntfId != 5 and outIntfId < 15 group by endTime",
                tabelName);
            break;
        default:
            break;
    }
    if (opreatSqlFlag != 5) {
        uint32_t cmdLen = strlen(querySql);
        ret = reStartGmcExecDirect(tableBody, querySql, cmdLen);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (opreatSqlFlag == 5) {
        ret = reStartGmcExecute(tableBody);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void randomSelectThreatTable(TableBody *tableBody, char *tabelName, int opreatSqlFlag)
{
    int ret = 0;
    char querySql[SQL_LENGTH] = {0};
    // 参数化查询填值为50
    int parameteredLength = 50;
    switch (opreatSqlFlag) {
        case 0:
            // 查询全表
            (void)sprintf(querySql, "select * from %s limit 100000", tabelName);
            break;
        case 1:
            // 高基数分组聚合
            (void)sprintf(querySql,
                "select endTime, first(srcIp), last(dstIp), sum(logId + 15) "
                "from %s where appId > 1000 and srcZoneId < 10 group by endTime limit 100000",
                tabelName);
            break;
        case 2:
            // 分组为多列且排序为多列，查询语句中有常量，且触发投影列比分组列多场景
            (void)sprintf(querySql,
                "select 1234, endTime, vsysId, userGroupId, secPolicyId, logId, length(dstPort), "
                "last(startTime), count(dstIp) from %s where appId > 2000 or srcZoneId > 10 and "
                "length(srcPort) = 5 group by endTime, userGroupId, secPolicyId order by "
                "secPolicyId ASC, length(dstPort) DESC limit 100000",
                tabelName);
            break;
        case 3:
            // 字符串等值查询（原场景应该为like查询，但是该场景耗时太长，该威胁表数据会很多，导致写锁拿不到）
            (void)sprintf(querySql,
                "select endTime, subAppId from %s where appId > 1000 and srcZoneId < 10 and "
                "length(srcPort) = 5 and userName = 'bob' group by endTime, subAppId order by "
                "subAppId ASC limit 100",
                tabelName);
            break;
        case 4:
            // 查询TEXT相关聚合函数
            (void)sprintf(querySql,
                "select endTime, vsysId, max(vsysId), length(vsysId) from %s where "
                "length(vsysId) < 50 and secPolicyId > 15 order by endTime",
                tabelName);
            break;
        case 5:
            // 参数化查询inet类型作为高基数查询
            (void)sprintf(querySql,
                "select srcIp, last(dstIp) from %s where secPolicyId != 10 "
                "and length(vsysId) < ? group by srcIp "
                "order by srcIp ASC, last(dstIp) DESC limit 10000",
                tabelName);
            TEST_EXPECT_INT32(GMERR_OK, GmcPrepareSql(tableBody->stmt, querySql, strlen(querySql)));
            TEST_EXPECT_INT32(GMERR_OK, GmcBindPara(tableBody->stmt, 1, GMC_DATATYPE_INT64, &parameteredLength, 0));
            break;
        case 6:
            // 表达式a+b和limit带offset
            (void)sprintf(querySql,
                "select srcIp, srcPort + dstPort from %s where srcZoneId != 10 and appId + subAppId < 15000"
                "order by srcIp ASC limit 10000 offset 10000",
                tabelName);
            break;
        case 7:
            // 聚合函数avg
            (void)sprintf(querySql,
                "select endTime, avg(startTime), avg(logId) "
                "from %s where and srcZoneId < 15 group by endTime",
                tabelName);
            break;
        default:
            break;
    }
    if (opreatSqlFlag != 5) {
        uint32_t cmdLen = strlen(querySql);
        ret = reStartGmcExecDirect(tableBody, querySql, cmdLen);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (opreatSqlFlag == 5) {
        ret = reStartGmcExecute(tableBody);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void randomSelectOtherThreatTable(TableBody *tableBody, char *tabelName, int opreatSqlFlag)
{
    int ret = 0;
    char querySql[SQL_LENGTH] = {0};
    // 参数化查询填值为50
    int parameteredLength = 50;
    switch (opreatSqlFlag) {
        case 0:
            // 查询全表
            (void)sprintf(querySql, "select * from %s limit 100000", tabelName);
            break;
        case 1:
            // 高基数分组聚合
            (void)sprintf(querySql,
                "select logTime, first(srcIp), last(dstIp), sum(logId) "
                "from %s where appCategoryId > 11100 and srcZoneId < 10 group by logTime limit 100000",
                tabelName);
            break;
        case 2:
            // 分组为多列且排序为多列，查询语句中有常量，且触发投影列比分组列多场景
            (void)sprintf(querySql,
                "select 1234, logTime, vsysId, secPolicyId, logId, length(dstPort), last(appId), "
                "count(dstIp) from %s where appCategoryId > 11200 or srcZoneId > 10 and "
                "length(srcPort) = 5 group by logTime, secPolicyId order by "
                "secPolicyId ASC, length(dstPort) DESC limit 100000",
                tabelName);
            break;
        case 3:
            // 模糊查询
            (void)sprintf(querySql,
                "select logTime, * from %s where appId > 1000 and srcZoneId < 10 and "
                "length(srcPort) = 5 and userName like 'b_b' group by logTime order by "
                "logTime ASC limit 100",
                tabelName);
            break;
        case 4:
            // 查询TEXT相关聚合函数
            (void)sprintf(querySql,
                "select logTime, vsysId, max(vsysId), length(vsysId) from %s where "
                "length(vsysId) < 50 and secPolicyId > 15 order by logTime",
                tabelName);
            break;
        case 5:
            // 参数化查询inet类型作为高基数查询
            (void)sprintf(querySql,
                "select srcIp, last(dstIp) from %s where secPolicyId != 10 "
                "and length(vsysId) < ? group by srcIp "
                "order by srcIp ASC, last(dstIp) DESC limit 10000",
                tabelName);
            TEST_EXPECT_INT32(GMERR_OK, GmcPrepareSql(tableBody->stmt, querySql, strlen(querySql)));
            TEST_EXPECT_INT32(GMERR_OK, GmcBindPara(tableBody->stmt, 1, GMC_DATATYPE_INT64, &parameteredLength, 0));
            break;
        case 6:
            // 表达式a+b和limit带offset
            (void)sprintf(querySql,
                "select srcIp, srcPort + dstPort from %s where srcZoneId != 10 and appId + subAppId < 15000"
                "order by srcIp ASC limit 10000 offset 10000",
                tabelName);
            break;
        case 7:
            // 聚合函数avg
            (void)sprintf(querySql,
                "select logTime, avg(appCategoryId), avg(logId) "
                "from %s where and srcZoneId < 15 group by logTime",
                tabelName);
            break;
        default:
            break;
    }
    if (opreatSqlFlag != 5) {
        uint32_t cmdLen = strlen(querySql);
        ret = reStartGmcExecDirect(tableBody, querySql, cmdLen);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (opreatSqlFlag == 5) {
        ret = reStartGmcExecute(tableBody);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void randomSelectReportTable(TableBody *tableBody, char *tabelName, int opreatSqlFlag)
{
    int ret = 0;
    char querySql[SQL_LENGTH] = {0};
    // 参数化查询填值为50
    int parameteredLength = 50;
    switch (opreatSqlFlag) {
        case 0:
            // 查询全表
            (void)sprintf(querySql, "select * from %s limit 50000", tabelName);
            break;
        case 1:
            // 高基数分组聚合
            (void)sprintf(querySql,
                "select logTime, first(srcIp), last(dstIp), sum(inIntfId + 5) "
                "from %s where inIntfId != 10 and sendByte > 1000 group by logTime limit 50000",
                tabelName);
            break;
        case 2:
            // 分组为多列且排序为多列，查询语句中有常量
            (void)sprintf(querySql,
                "select 1234, logTime, vsysId, secPolicyId, length(receiveByte), last(subAppId), "
                "count(dstIp) from %s where inIntfId != 10 or sendByte < 50000 "
                "group by logTime, secPolicyId order by "
                "secPolicyId ASC, length(receiveByte) DESC limit 50000",
                tabelName);
            break;
        case 3:
            // 条件过滤含有表达式
            (void)sprintf(querySql,
                "select logTime, * from %s where inIntfId != 10 and sendByte < 50000 and "
                "userId + appId > 5000 group by logTime order by "
                "logTime ASC limit 100",
                tabelName);
            break;
        case 4:
            // 查询TEXT相关聚合函数
            (void)sprintf(querySql,
                "select logTime, vsysId, max(vsysId), length(vsysId) from %s where "
                "length(vsysId) < 50 and inIntfId > 15 order by logTime",
                tabelName);
            break;
        case 5:
            // 参数化查询inet类型作为高基数查询
            (void)sprintf(querySql,
                "select srcIp, last(dstIp) from %s where inIntfId != 10 "
                "and length(vsysId) < ? group by srcIp "
                "order by srcIp ASC, last(dstIp) DESC limit 10000",
                tabelName);
            TEST_EXPECT_INT32(GMERR_OK, GmcPrepareSql(tableBody->stmt, querySql, strlen(querySql)));
            TEST_EXPECT_INT32(GMERR_OK, GmcBindPara(tableBody->stmt, 1, GMC_DATATYPE_INT64, &parameteredLength, 0));
            break;
        case 6:
            // 表达式a+b和limit带offset
            (void)sprintf(querySql,
                "select srcIp, receiveByte + sendByte from %s where inIntfId != 10 and appId + subAppId < 15000"
                "order by srcIp ASC limit 10000 offset 10000",
                tabelName);
            break;
        case 7:
            // 聚合函数avg
            (void)sprintf(querySql,
                "select logTime, avg(appCategoryId), avg(logId) "
                "from %s where and inIntfId < 15 group by logTime",
                tabelName);
            break;
        default:
            break;
    }
    if (opreatSqlFlag != 5) {
        uint32_t cmdLen = strlen(querySql);
        ret = reStartGmcExecDirect(tableBody, querySql, cmdLen);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    } else if (opreatSqlFlag == 5) {
        ret = reStartGmcExecute(tableBody);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void randomQuery(TableBody *tableBody, char *tabelName, int opreatSqlFlag, int operateTableIndex)
{
    time_t time_T;
    time_T = time(NULL);
    char querySql[SQL_LENGTH] = {0};
    int currentTime = time_T;
    srand(currentTime + opreatSqlFlag);
    if (operateTableIndex == 0) {
        randomSelectFlewTable(tableBody, tabelName, opreatSqlFlag);
    } else if (operateTableIndex == 1) {
        randomSelectThreatTable(tableBody, tabelName, opreatSqlFlag);
    } else if (operateTableIndex < 7) {
        randomSelectOtherThreatTable(tableBody, tabelName, opreatSqlFlag);
    } else {
        randomSelectReportTable(tableBody, tabelName, opreatSqlFlag);
    }
}

int allTableShotQuery(int seed)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;
    tsConnOptions.requestTimeout = 1000000;
    tsConnOptions.msgReadTimeout = 1000000;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(&conn, &stmt, 0, &tsConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    time_t time_T;
    // 标记查询哪张表
    int operateTableIndex = 0;
    // 标记执行哪条查询语句
    int opreatSqlFlag = 0;
    // 标记查询动作执行次数
    int64_t queryExcuteTimes = 0;
    // 从1张报表、6张威胁表、20张报表中随机选择1张表进行短查询动作
    char queryOperateTableList[27][30] = {
        "trf_org",
        "thrt_org",
        "trf_mrg",
        "url_org",
        "sys_org",
        "server_org",
        "oper_org",
        "thrtlog_type_name_1h",
        "thrtlog_attk_5m",
        "trflog_app_1h",
        "trflog_srcIp_5m",
        "thrtlog_attk_20m",
        "thrtlog_app_20m",
        "thrtlog_mrg_5m",
        "trfmrglog_mrg_5m",
        "trflog_srcIp_1h",
        "thrtlog_attk_1d",
        "thrtlog_mrg_20m",
        "thrtlog_policy_20m",
        "trflog_dstIp_5m",
        "trfmrglog_mrg_20m",
        "thrtlog_vctm_20m",
        "trflog_dstIp_1h",
        "trfmrglog_mrg_1h",
        "thrtlog_vctm_1d",
        "trflog_dstIp_1d",
        "thrtlog_mrg_1d",
    };
    TableBody tableBody = {0};
    tableBody.conn = conn;
    tableBody.stmt = stmt;
    while (true) {
        operateTableIndex = rand() % 27;
        opreatSqlFlag = rand() % 6;
        randomQuery(&tableBody, queryOperateTableList[operateTableIndex], opreatSqlFlag, operateTableIndex);
#if defined(CPU_BIT_32)
        DbSleep(500);
#else
        DbSleep(100);
#endif
        queryExcuteTimes++;
        if ((queryExcuteTimes % 100) == 0) {
            queryExcuteTimes = 0;
            sleep(5);
        }
    }

    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void allViewRandomQuery()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    time_t time_T;
    // 建立连接
    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    int queryViewIndex = 0;
    char sqlCmd[SQL_LENGTH] = {0};
    while (true) {
        time_T = time(NULL);
        (void)srand((uint32_t)time_T);
        queryViewIndex = rand() % ALL_SUPPORTED_VIEW_NUM;
        (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$%s'\" -s %s >> gmsysviewResult.txt",
            allSupportedViewName[queryViewIndex], g_connServerTsdb);
        system(sqlCmd);
        sleep(rand() % 60 + 30);
    }
    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对内存表进行建表，写入数据、查询数据、作为源表insert into至预置表中，删表
void randomMemoryTableProcess(TableBody *tableBody, TableInsertData *tableInsertData)
{
    int ret = 0;
    char sqlCmd[SQL_LENGTH] = {0};
    char memoryTable[64] = "memoryTable";
    char *currentDir = getenv("PWD");
    // 建内存表
    (void)sprintf(sqlCmd,
        "CREATE TABLE IF NOT EXISTS %s(logTime INTEGER, vsysId TEXT, ip_version INTEGER, srcIp INET, "
        "dstIp INET, nat_srcIp INET, nat_dstIp INET, inIntfId INTEGER, "
        "outIntfId INTEGER, src_loc_id INTEGER, dst_loc_id INTEGER, src_country_id INTEGER, "
        "dst_country_id INTEGER, userId INTEGER, appCategoryId INTEGER, subAppId INTEGER, "
        "appId INTEGER, app_casa_flag INTEGER, app_casa_behavior INTEGER, secPolicyId INTEGER, "
        "session_num INTEGER, consession_num INTEGER, sendByte INTEGER, receiveByte INTEGER, "
        "total_byte INTEGER, INDEX idx1(appCategoryId))"
        "with (enGine = 'mEmOry', max_size = 1000000, time_col = 'logTime', interval = '1 hour');",
        memoryTable);
    uint32_t cmdLen = strlen(sqlCmd);
    tableBody->tableName = memoryTable;
    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    int tryTimes = 0;
    while (ret == GMERR_LOCK_NOT_AVAILABLE && tryTimes < 5) {
        sleep(60);
        ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
        tryTimes++;
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);

    time_t time_T;
    int currentTime = 0;
    // 注入数据
    int64_t cyclesTimes = 20;
    for (int i = 0; i < cyclesTimes; i++) {
        time_T = time(NULL);
        currentTime = time_T + rand() % BULK_INSERT_SIZE + 1;
        int dataCount = rand() % (BULK_INSERT_SIZE / 5) + 1;
        tableBody->dataCount = dataCount;
        tableBody->logTime = currentTime;
        ret = insertDataToReportTable(tableBody, tableInsertData);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    // 查询数据
    (void)sprintf(sqlCmd, "select * from %s limit 50000;", memoryTable);
    cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    if (ret == GMERR_INVALID_BUFFER) {
        return;
    } else {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    // 查询结果直接写表insert into至报表中
    tableBody->dataCount = INSERT_INTO_SIZE;
    TableBody purposeTableBody = {0};
    int32_t logTime = time_T;
    srand(logTime);
    // 设置目的表，确保目的表和源表不是同一张表
    int operateTableIndex = rand() % 38 + 14;
    purposeTableBody.tableName = allActualTableName[operateTableIndex];
    purposeTableBody.dataCount = INIT_REPORT_TABLE_SIZE;
    purposeTableBody.logTime = logTime;
    ret = insertIntoTable(&purposeTableBody, tableBody);
    if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    // 删内存表
    (void)sprintf(sqlCmd, "drop table %s;", memoryTable);
    cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 内存表基本操作
void memoryTableOpreate(TableInsertData *tableInsertData)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建立连接
    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    time_t time_T;
    time_T = time(NULL);
    int64_t currentTime = 0;
    int64_t lastSecondSceneTime = 0;
    TableBody tableBody = {0};
    tableBody.conn = conn;
    tableBody.stmt = stmt;

    while (true) {
        randomMemoryTableProcess(&tableBody, tableInsertData);
        sleep(300);
    }
    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

// 建Truncate逻辑表
int createTruncateTable(TableBody *tableBody)
{
    int ret = 0;
    // 建表使用长度，以防建表语句过长，采用2048长度
    char sqlCmd[SQL_LENGTH] = {0};
    // 建流量表，
    (void)sprintf(sqlCmd,
        "CREATE TABLE IF NOT EXISTS %s(logId INTEGER, startTime INTEGER, "
        "endTime INTEGER, vsysId TEXT, srcIp INET, dstIp INET, nat_srcIp INET, nat_dstIp INET, "
        "srcPort INTEGER, dstPort INTEGER, nat_srcPort INTEGER, nat_dstPort INTEGER, protocolType INTEGER, "
        "userId INTEGER, userGroupId INTEGER, appCategoryId INTEGER, subAppId INTEGER, appId INTEGER, "
        "srcZoneId INTEGER, dstZoneId INTEGER, secPolicyId INTEGER, qos_rule_id INTEGER, inIntfId INTEGER, "
        "outIntfId INTEGER, sendByte INTEGER, receiveByte INTEGER, send_pkt INTEGER, receive_pkt INTEGER, "
        "src_loc_id INTEGER, dst_loc_id INTEGER, src_mask_id INTEGER, dst_mask_id INTEGER, url_sub_category_id "
        "INTEGER, close_reason INTEGER, url_subcategory_type INTEGER, userName CHAR(129), natSrcIpv6 CHAR(33), "
        "natDstIpv6 CHAR(33)) WITH (time_col = 'endTime', interval = '1 hour', "
        "sensitive_col = 'secPolicyId');",
        tableBody->tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    return ret;
}

// 循环插入1条数据，10w次后Truncate全表
int insertDataToTruncateTable(TableBody *tableBody, TableInsertData *tableInsertData)
{
    int ret = 0;
    int64_t startTime = 5000000000;
    int64_t logTime = 0;
    tableBody->dataCount = 1;

    // 循环10w次，每次注入1条数据，20w+就会导致行存超过1GB，触发1010000报错，arm32场景压力缩小至2w次
#if defined(CPU_BIT_32)
    for (int i = 0; i < 20000; i++) {
#else
    for (int i = 0; i < 100000; i++) {
#endif
        logTime = startTime + i;
        tableBody->logTime = logTime;
        ret = insertDataToFlewtable(tableBody, tableInsertData);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    return ret;
}

// 主动触发Truncate
int executeTruncateTable(TableBody *tableBody)
{
    int ret = 0;
    char sqlCmd[SQL_LENGTH] = {0};
    (void)sprintf(sqlCmd, "truncate table %s", tableBody->tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = reStartGmcExecDirect(tableBody, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// 逻辑表Truncate操作
void truncateTable(TableInsertData *tableInsertData)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char truncateTableName[20] = "truncatetable";
    time_t time_T;
    // 建立连接
    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    TableBody tableBody = {0};
    tableBody.conn = conn;
    tableBody.stmt = stmt;
    tableBody.tableName = truncateTableName;

    while (true) {
        // 建表
        ret = createTruncateTable(&tableBody);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            sleep(60);
            continue;
        }
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 插入10w条数据
        ret = insertDataToTruncateTable(&tableBody, tableInsertData);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 触发Truncate
        ret = executeTruncateTable(&tableBody);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(600);
        ret = DropTsTable(stmt, truncateTableName);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        sleep(600);
    }
    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

int SwapDataDirAndTryIfFailed(GmcStmtT *stmt, char *ctrlFilePath, char *tempFilePath)
{
    int ret = 0;
    int tryTimes = 0;
    ret = GmcSwapDataDir(stmt, ctrlFilePath, tempFilePath);
    while (ret == GMERR_LOCK_NOT_AVAILABLE) {
        if (tryTimes > 10) {
            return 1;
        }
        // 如果执行失败，等待60s后重试
        sleep(60);
        ret = GmcSwapDataDir(stmt, ctrlFilePath, tempFilePath);
        tryTimes++;
    }
    return 0;
}

// 在线切换DB目录
int swapDataDir(int lastSwapDir)
{
    int ret = 0;
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    time_t time_T;
    // 建立连接
    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
#if defined RUN_INDEPENDENT
    char swapDirDefault[MAIL_ADDRESS_LEN] = "/data/gmdb";
#else
    char swapDirDefault[MAIL_ADDRESS_LEN] = "/mnt/hdd/data/gmdb";
#endif
    char *currentDir = getenv("PWD");
    char swapDirCurrent[MAIL_ADDRESS_LEN] = {0};
    (void)sprintf(swapDirCurrent, "%s/gmdb", currentDir);

    if (lastSwapDir == 0) {
        // 切换目录
        SwapDataDirAndTryIfFailed(stmt, swapDirCurrent, NULL);
        lastSwapDir = 1;
    } else {
        SwapDataDirAndTryIfFailed(stmt, swapDirDefault, NULL);
        lastSwapDir = 0;
    }
    sleep(60);
    // 建表
    ret = createActualTable(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return lastSwapDir;
}

// 初始化全部预置表数据
int initAllTableData(TableInsertData *tableInsertData)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建立连接
    ret = CreateTsConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 流表预置数据1亿条
    int dataCount = INIT_FLEW_TABLE_SIZE;
    // 定位问题时使用int dataCount = 100000;
    time_t time_T;
    time_T = time(NULL);
    int logTime = 0;
    int actualDiskUsage = 0;

    // 预置1亿条数据到流量表中
    TableBody flewDataBody = {conn, stmt, allActualTableName[0], dataCount, logTime};
    ret = insertDataToFlewtable(&flewDataBody, tableInsertData);
    if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    // 预置1千万条数据到威胁表thrt_org中
    dataCount = INIT_THREAT_TABLE_SIZE;
    TableBody threatDataBody[12];
    threatDataBody[0] = {conn, stmt, allActualTableName[1], dataCount, logTime};
    ret = insertDataToThreattable(&threatDataBody[0], tableInsertData);
    if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }

    // 预置1000万条数据到剩余威胁表中
    for (int i = 2; i < 13; i++) {
        threatDataBody[i - 1] = {conn, stmt, allActualTableName[i], dataCount, logTime};
        ret = insertDataToOtherThreattable(&threatDataBody[i - 1], tableInsertData);
        if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }

    TableBody reportDataBody[40];
    dataCount = INIT_REPORT_TABLE_SIZE;
    time_T = time(NULL);
    logTime = time_T;
    // 预置500万条数据到有ttl限制的报表中
    for (int i = 13; i < 23; i++) {
        // 获取当前时间
        reportDataBody[i - 13] = {conn, stmt, allActualTableName[i], dataCount, logTime};
        ret = insertDataToReportTable(&reportDataBody[i - 13], tableInsertData);
        if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }

    // 预置500万条数据到剩余报表中
    for (int i = 23; i < 53; i++) {
        // 获取当前时间
        logTime = 0;
        reportDataBody[i - 13] = {conn, stmt, allActualTableName[i], dataCount, logTime};
        ret = insertDataToReportTable(&reportDataBody[i - 13], tableInsertData);
        if (ret != GMERR_TABLE_EXCEED_DISK_LIMIT) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// 设置场景/线程数/数量标志位
int longStabilitySetSceneFlag(char **argvInput, int16_t *sceneFlag, int16_t *sceneThreadN, int16_t sceneNum,
    uint32_t *dataN, uint32_t defaultDataN)
{
    char delims[] = ",";
    char *sceneN = NULL;
    char *threadsN = NULL;
    char *pSave1 = NULL;
    char *pSave2 = NULL;
    int i = 0;
    int k = 0;
    if (strcmp(argvInput[1], "-s") == 0) {
        if (argvInput[2]) {
            if (argvInput[3]) {
                if (strcmp(argvInput[3], "-t") == 0) {
                    if (argvInput[4]) {
                        sceneN = strtok_r(argvInput[2], delims, &pSave1);
                        threadsN = strtok_r(argvInput[4], delims, &pSave2);
                        if (sceneN != NULL) {
                            i = atoi(sceneN);
                            if (i > sceneNum || i == 0) {
                                printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                                return 1;
                            } else {
                                sceneFlag[i] = 1;  // 设置场景flag标志
                                if (threadsN != NULL) {
                                    k = atoi(threadsN);
                                    if (k > THREADS_NUM_MAX || k == 0) {
                                        printf("parameter 4 error,should be >0 && <= %d,eg:1,4,3\n", THREADS_NUM_MAX);
                                        return 1;
                                    } else {
                                        sceneThreadN[i] = k;  // 设置对应的场景的线程数
                                    }
                                }
                            }
                        }
                        while (sceneN != NULL) {
                            sceneN = strtok_r(NULL, delims, &pSave1);
                            if (sceneN != NULL) {
                                i = atoi(sceneN);
                                if (i > sceneNum || i == 0) {
                                    printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                                    return 1;
                                } else {
                                    sceneFlag[i] = 1;
                                    threadsN = strtok_r(NULL, delims, &pSave2);
                                    if (threadsN != NULL) {
                                        k = atoi(threadsN);
                                        if (k > THREADS_NUM_MAX || k == 0) {
                                            printf(
                                                "parameter 4 error,should be >0 && <= %d,eg:1,4,3\n", THREADS_NUM_MAX);
                                            return 1;
                                        } else {
                                            sceneThreadN[i] = k;  // 设置对应的场景的线程数
                                        }
                                    } else {
                                        sceneThreadN[i] = 1;  // threadsN为空设置默认线程数
                                    }
                                }
                            }
                        }
                        if (argvInput[5] != NULL) {
                            if (strcmp(argvInput[5], "-n") == 0) {
                                if (argvInput[6]) {
                                    (*dataN) = atoi(argvInput[6]);
                                    if ((*dataN) == 0) {
                                        printf("parameter 6 error,should be number >0,eg:40000\n");
                                        return 1;
                                    }
                                } else {
                                    (*dataN) = defaultDataN;  // 默认值
                                }
                            } else {
                                printf("parameter 5 error,should be '-n'\n");
                                return 1;
                            }
                        } else {
                            (*dataN) = defaultDataN;  // 默认值
                        }
                    } else {
                        // argv[4]为空则默认启动1个线程
                        (*dataN) = defaultDataN;  // 默认值
                        sceneN = strtok(argvInput[2], delims);
                        if (sceneN != NULL) {
                            i = atoi(sceneN);
                            if (i > sceneNum || i == 0) {
                                printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                                return 1;
                            }
                            sceneFlag[i] = 1;
                            sceneThreadN[i] = 1;
                        }
                        while (sceneN != NULL) {
                            sceneN = strtok(NULL, delims);
                            if (sceneN != NULL) {
                                i = atoi(sceneN);
                                if (i > sceneNum || i == 0) {
                                    printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                                    return 1;
                                }
                                sceneFlag[i] = 1;
                                sceneThreadN[i] = 1;
                            }
                        }
                    }
                } else {
                    printf("parameter 3 error,should be '-t'\n");
                    return 1;
                }
            } else {                      // argv[3]为空的情况
                (*dataN) = defaultDataN;  // 默认值
                sceneN = strtok(argvInput[2], delims);
                if (sceneN != NULL) {
                    i = atoi(sceneN);
                    if (i > sceneNum || i == 0) {
                        printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                        return 1;
                    }
                    sceneFlag[i] = 1;
                    sceneThreadN[i] = 1;
                }
                while (sceneN != NULL) {
                    sceneN = strtok(NULL, delims);
                    if (sceneN != NULL) {
                        i = atoi(sceneN);
                        if (i > sceneNum || i == 0) {
                            printf("parameter 2 error,should be >0 && <= %d,eg:1,2,%d\n", sceneNum, sceneNum);
                            return 1;
                        }
                        sceneFlag[i] = 1;
                        sceneThreadN[i] = 1;
                    }
                }
            }
        } else {
            // argv[2]为空则启动默认的原V3场景
            (*dataN) = defaultDataN;
            for (int j = 1; j < sceneNum + 1; j++) {
                sceneFlag[j] = 1;
                sceneThreadN[j] = 1;
            }
        }
    } else {
        printf("parameter 1 error,should be '-s'\n");
        return 1;
    }
    printf("(*dataN):%u\n", (*dataN));
    return 0;
}

#ifdef __cplusplus
}
#endif

#endif

#endif /* TSDB_LONG_STABILITY_H */
