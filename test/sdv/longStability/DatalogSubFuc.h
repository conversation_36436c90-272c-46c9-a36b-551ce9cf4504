/****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 Description  : DatalogSubFuc.h

 Date         : 2023/05/18
*****************************************************************************/
#ifndef DATALOGSUBFUC_H
#define DATALOGSUBFUC_H

#ifdef FEATURE_DATALOG

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include <time.h>
#include <stdarg.h>
#include "t_datacom_lite.h"
#include "share_function.h"
#include "tableModule.h"

#define PRINT_INFO
#define MAX_CMD_SIZE 1024
#define LOAD_HOTPATCH_MAX_SIZE 5

const uint8_t G_MACS[10000] = {0x0A, 0x0E, 0x0F, 0x00, 0x00, 0x0F};  // --PublishNif
const int32_t G_IF_INDEXS = 17;                                      // --ConfigAttributes/PublishNif
char const *g_patchViewName = "V\\$PTL_DATALOG_PATCH_INFO";

const char *g_schemaJson1 = R"([ {
        "type" : "record",
        "name" : "Arp.ConfigArp",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "addr", "type" : "int32"},
            {"name" : "ifIndex", "type" : "int32"},
            {"name" : "type", "type" : "int8"},
            {"name" : "mac", "type" : "fixed", "size" : 6},
            {"name" : "fakeFlag", "type" : "int8"},
            {"name" : "vlanId", "type" : "int16"},
            {"name" : "workIfIndex", "type" : "int32"},
            {"name" : "detectCount", "type" : "int32"},
            {"name" : "agingTime", "type" : "int64"}
        ],
        "keys" : [
            {
                "node" : "Arp.ConfigArp",
                "name" : "0",
                "fields" : ["upgradeVersion", "addr", "ifIndex"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "Arp.ConfigArp",
                "name" : "1",
                "fields" : ["ifIndex"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : false}
            }
        ]
    } ])";

const char *g_schemaJson2 = R"([ {
        "type" : "record",
        "name" : "Fib.ConfigIpv4Fwd",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "nsId", "type" : "int32"},
            {"name" : "vrfId", "type" : "int32"},
            {"name" : "dstIp", "type" : "int32"},
            {"name" : "maskLen", "type" : "int32"},
            {"name" : "nhpGroupFlag", "type" : "int8"},
            {"name" : "routeAttr", "type" : "int16"},
            {"name" : "routeFlags", "type" : "int16"},
            {"name" : "pathFlags", "type" : "int32"},
            {"name" : "nhpGroupId", "type" : "int32"},
            {"name" : "primaryLabel", "type" : "int32"},
            {"name" : "attributeId", "type" : "int32"},
            {"name" : "qosId", "type" : "int16"}
        ]
    } ])";

// Ifm.ConfigVapIf
const char *g_schemaJson3 = R"([ {
        "type" : "record",
        "name" : "Ifm.ConfigVapIf",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "nsId", "type" : "int32"},
            {"name" : "ifIndex", "type" : "int32"},
            {"name" : "radioName", "type" : "fixed", "size" : 64},
            {"name" : "adminState", "type" : "int8"},
            {"name" : "vapType", "type" : "int8"},
            {"name" : "vapIndex", "type" : "int8"},
            {"name" : "linkedId", "type" : "int8"},
            {"name" : "mac", "type" : "fixed", "size" : 6},
            {"name" : "userIdentify", "type" : "int8"},
            {"name" : "userService", "type" : "int8"},
            {"name" : "userAuth", "type" : "int8"},
            {"name" : "authType", "type" : "int8"},
            {"name" : "isEapolKeyToCp", "type" : "int8"},
            {"name" : "protocolTunnelFwd", "type" : "int8"},
            {"name" : "ssid", "type" : "fixed", "size" : 33},
            {"name" : "globalIdx", "type" : "int16"},
            {"name" : "vapFlag", "type" : "int8"},
            {"name" : "broadPeerId", "type" : "int32"},
            {"name" : "broadPfeVapIdx", "type" : "int32"},
            {"name" : "broadSessionInfo", "type" : "int16"},
            {"name" : "mainLinkFlag", "type" : "int8"},
            {"name" : "multiMediaEn", "type" : "int8"},
            {"name" : "tcpWndTurn", "type" : "int8"},
            {"name" : "arpTest", "type" : "int8"}
        ]
    } ])";
// Ifm.PublishRadioName
const char *g_schemaJson4 = R"([ {
        "type" : "record",
        "name" : "Ifm.PublishRadioName",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "radioName", "type" : "fixed", "size" : 64},
            {"name" : "type", "type" : "fixed", "size" : 16},
            {"name" : "tp", "type" : "int32"},
            {"name" : "phy_port", "type" : "int32"},
            {"name" : "port_id", "type" : "int32"},
            {"name" : "cpu_type", "type" : "int8"},
            {"name" : "drvPortType", "type" : "int8"},
            {"name" : "drvSubType", "type" : "int8"}
        ]
    } ])";
// Fib.ConfigNhpGroup
const char *g_schemaJson5 = R"([ {
        "type" : "record",
        "name" : "Fib.ConfigNhpGroup",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "nsId", "type" : "int32"},
            {"name" : "nhpGroupId", "type" : "int32"},
            {"name" : "nhpNum", "type" : "int32"},
            {"name" : "vrfId", "type" : "int32"}
        ]
    } ])";
// Ifm.ConfigIf：含主键删除
const char *g_schemaJson6 = R"([ {
        "type" : "record",
        "name" : "Ifm.ConfigIf",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "nsId", "type" : "int32"},
            {"name" : "ifName", "type" : "fixed", "size" : 64},
            {"name" : "ifIndex", "type" : "int32"},
            {"name" : "type", "type" : "int16"},
            {"name" : "phyPort", "type" : "int32"},
            {"name" : "masterIfIndex", "type" : "int32"},
            {"name" : "state", "type" : "int8"}
        ],
        "keys" : [
            {
                "node" : "Ifm.ConfigIf",
                "name" : "0",
                "fields" : ["upgradeVersion", "nsId", "ifName"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";
// Ifm.PublishNif
const char *g_schemaJson7 = R"([ {
        "type" : "record",
        "name" : "Ifm.PublishNif",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "ifIndex", "type" : "int32"},
            {"name" : "ifName", "type" : "fixed", "size" : 64},
            {"name" : "type", "type" : "fixed", "size" : 16},
            {"name" : "tp", "type" : "int32"},
            {"name" : "chip_unit", "type" : "int8"},
            {"name" : "oda_phy_port", "type" : "int8"},
            {"name" : "state", "type" : "int8"},
            {"name" : "mac", "type" : "fixed", "size" : 6},
            {"name" : "dev_id", "type" : "int32"},
            {"name" : "chassis_id", "type" : "int32"},
            {"name" : "slot_id", "type" : "int32"},
            {"name" : "card_id", "type" : "int32"},
            {"name" : "unit_id", "type" : "int32"},
            {"name" : "port_id", "type" : "int32"},
            {"name" : "cpu_type", "type" : "int8"},
            {"name" : "drvPortType", "type" : "int8"},
            {"name" : "drvSubType", "type" : "int8"}
        ]
    } ])";
// Ifm.EthTrunkMembers
const char *g_schemaJson8 = R"([ {
        "type" : "record",
        "name" : "Ifm.EthTrunkMembers",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "memIfIndex", "type" : "int32"},
            {"name" : "trunkId", "type" : "int32"},
            {"name" : "nsId", "type" : "int32"},
            {"name" : "ifIndex", "type" : "int32"},
            {"name" : "weight", "type" : "int16"}
        ]
    } ])";
// Fib.ConfigNhpStandard
const char *g_schemaJson9 = R"([ {
        "type" : "record",
        "name" : "Fib.ConfigNhpStandard",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "nhpIndex", "type" : "int32"},
            {"name" : "nextHop", "type" : "int32"},
            {"name" : "outIfIndex", "type" : "int32"},
            {"name" : "vrfId", "type" : "int32"},
            {"name" : "iidFlags", "type" : "int32"},
            {"name" : "nsId", "type" : "int32"}
        ]
    } ])";
// Ifm.ConfigLinkUpDownMode
const char *g_schemaJson10 = R"([ {
        "type" : "record",
        "name" : "Ifm.ConfigLinkUpDownMode",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "ifIndex", "type" : "int32"},
            {"name" : "mode", "type" : "int8"}
        ]
    } ])";
// namespace01.inputTableD
const char *g_schemaJson11 = R"([ {
        "type" : "record",
        "name" : "namespace01.inputTableD",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "int32"},
            {"name" : "d", "type" : "int64"}
        ]
    } ])";
// namespace01.inputTableE
const char *g_schemaJson12 = R"([ {
        "type" : "record",
        "name" : "namespace01.inputTableE",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "int32"},
            {"name" : "d", "type" : "int64"}
        ]
    } ])";
// namespace01.inputLpm4
const char *g_schemaJson13 = R"([ {
        "type" : "record",
        "name" : "namespace01.inputLpm4",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "id", "type" : "int32"},
            {"name" : "vrId", "type" : "int32"},
            {"name" : "vrfIndex", "type" : "int32"},
            {"name" : "destIpAddr", "type" : "int32"},
            {"name" : "maskLen", "type" : "int8"}
        ]
    } ])";
// namespace01.inputLpm6
const char *g_schemaJson14 = R"([ {
        "type" : "record",
        "name" : "namespace01.inputLpm6",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "id", "type" : "int32"},
            {"name" : "vrId", "type" : "int32"},
            {"name" : "vrfIndex", "type" : "int32"},
            {"name" : "destIpAddr", "type" : "fixed", "size": 16},
            {"name" : "maskLen", "type" : "int8"}
        ]
    } ])";

/*
输入表和输出表数据关系
hpr表转化关系（ex. : uint32_t->uint8[4]）
*/

void Uint32ToUint8(uint32_t input, uint8_t output[4])
{
    output[0] = (input >> 24) & 0xFF;
    output[1] = (input >> 16) & 0xFF;
    output[2] = (input >> 8) & 0xFF;
    output[3] = input & 0xFF;
}

void Uint8ToUint32(uint8_t input[4], uint32_t &output)
{
    output = (input[0] << 24) + (input[1] << 16) + (input[2] << 8) + input[3];
}
void StateSetData(StateT *setObj, int64_t value, int32_t count)
{
    setObj->state = (int32_t)(value);
    setObj->dtlReservedCount = count;
}
// 输入表数据构造
// addr: int4, ifIndex: int4, type: int1, mac: byte6, fakeFlag: int1, vlanId: int2, workIfIndex: int4, detectCount:int4,
// agingTime: int8
void ConfigArpSetData(ConfigArpT *setObj, int64_t value, int32_t count)
{
    setObj->addr = (int32_t)(value);
    setObj->ifIndex = 20;
    setObj->type = 1;
    setObj->mac[0] = 0x08;
    setObj->mac[1] = 0x26;
    setObj->mac[2] = 0xAE;
    setObj->mac[3] = 0x37;
    setObj->mac[4] = 0x48;
    setObj->mac[5] = 0xB4;
    setObj->vlanId = 0;
    setObj->fakeFlag = 0;
    // 设置为7天
    setObj->agingTime = 7 * 24 * 60 * 60 * 1000;
    setObj->dtlReservedCount = count;
}
// 更新ConfigArp表的数据
void ConfigArpUpdateData(ConfigArpT *setObj, int64_t value, int32_t count)
{
    setObj->addr = (int32_t)(value);
    setObj->ifIndex = 20;
    setObj->type = 1;
    setObj->mac[0] = 0x08;
    setObj->mac[1] = 0x26;
    setObj->mac[2] = 0xAE;
    setObj->mac[3] = 0x37;
    setObj->mac[4] = 0x48;
    setObj->mac[5] = 0xB4;
    setObj->vlanId = 0;
    // 将该值从0变成1
    setObj->fakeFlag = 1;
    setObj->agingTime = 7 * 24 * 60 * 60 * 1000;
    setObj->dtlReservedCount = count;
}
// Fib
void ConfigIpv4FwdSetData(ConfigIpv4FwdT *setObj, int64_t value, int32_t count)
{
    (*setObj).nsId = 0;
    (*setObj).vrfId = (int32_t)(value);
    (*setObj).dstIp = 1;
    (*setObj).maskLen = (value % 2 == 0) ? 24 : 32;
    (*setObj).nhpGroupFlag = 0;
    (*setObj).routeAttr = 2;
    (*setObj).routeFlags = 1;
    (*setObj).pathFlags = 1;
    (*setObj).nhpGroupId = 2;
    (*setObj).primaryLabel = 3;
    (*setObj).attributeId = 1;
    (*setObj).qosId = 1;
    (*setObj).dtlReservedCount = count;
}
void ConfigNhpGroupSetData(ConfigNhpGroupT *setObj, int64_t value, int32_t count)
{
    (*setObj).nsId = 0;
    (*setObj).nhpGroupId = (int32_t)(value);
    (*setObj).nhpNum = 1;
    (*setObj).vrfId = 1;
    (*setObj).dtlReservedCount = count;
}
void ConfigNhpGroupNodeSetData(ConfigNhpGroupNodeT *setObj, int64_t value, int32_t count)
{
    (*setObj).nsId = 0;
    (*setObj).nhpGroupId = (int32_t)(value);
    (*setObj).attributeId = (int32_t)(value + 1);
    (*setObj).primaryNhpId = 1;
    (*setObj).primaryLabel = 1;
    (*setObj).backupNhpId = 1;
    (*setObj).backupLabel = 0;
    (*setObj).vrfId = 1;
    (*setObj).dtlReservedCount = count;
}
void ConfigNhpBasicSetData(ConfigNhpBasicT *setObj, int64_t value, int32_t count)
{
    (*setObj).nhpIndex = (int32_t)(value);
    (*setObj).vrfId = 1;
    (*setObj).originNhp = 1;
    (*setObj).iidFlags = 1;
    (*setObj).nsId = 0;
    (*setObj).dtlReservedCount = count;
}
void ConfigNhpStandardSetData(ConfigNhpStandardT *setObj, int64_t value, int32_t count)
{
    (*setObj).nhpIndex = (int32_t)(value);
    (*setObj).nextHop = 1;
    (*setObj).outIfIndex = 2;
    (*setObj).vrfId = 1;
    (*setObj).iidFlags = 1;
    (*setObj).nsId = 0;
    (*setObj).dtlReservedCount = count;
}
// Ifm
void ConfigVapIfSetData(ConfigVapIfT *setObj, int64_t value, int32_t count)
{
    (*setObj).nsId = 0;
    (*setObj).ifIndex = (int32_t)(value + G_IF_INDEXS);
    (void)setArryByValue((*setObj).radioName, sizeof((*setObj).radioName), 0, U8_MAX);
    (*setObj).adminState = 0;
    (*setObj).vapType = 0;
    (*setObj).vapIndex = 1;
    (*setObj).linkedId = 1;
    (void)setArryByValue((*setObj).mac, sizeof((*setObj).mac), 0, U8_MAX);
    (*setObj).userIdentify = 1;
    (*setObj).userService = 1;
    (*setObj).userAuth = 0;
    (*setObj).authType = 1;
    (*setObj).isEapolKeyToCp = 1;
    (*setObj).protocolTunnelFwd = 0;
    (void)setArryByValue((*setObj).ssid, sizeof((*setObj).ssid), 0, U8_MAX);
    (*setObj).dtlReservedCount = count;
}
void PublishRadioNameSetData(PublishRadioNameT *setObj, int64_t value, int32_t count)
{
    (void)setArryByValue((*setObj).radioName, sizeof((*setObj).radioName), value, U8_MAX, G_MACS);
    (void)setArryByValue((*setObj).type, sizeof((*setObj).type), 0, U8_MAX);
    (*setObj).tp = 1;
    (*setObj).phyPort = 2;
    (*setObj).portId = 1;
    (*setObj).cpuType = 1;
    (*setObj).drvPortType = 2;
    (*setObj).drvSubType = 1;
    (*setObj).dtlReservedCount = count;
}
void ConfigIfSetData(ConfigIfT *setObj, int64_t value, int32_t count)
{
    (*setObj).nsId = 0;
    (*setObj).ifIndex = (int32_t)value + 10000;  // 避免join执行时间过长
    (*setObj).type = (int16_t)(value + 1);
    (*setObj).phyPort = 0;
    (*setObj).masterIfIndex = 0;
    (*setObj).state = 1;
    char ifNameStr1[] = "lo";
    char ifNameStr2[] = "NULL";
    char *ifNamePtr = (value % 2 == 0) ? ifNameStr1 : ifNameStr2;
    (void)strToArry((*setObj).ifName, sizeof((*setObj).ifName), ifNamePtr);
    (*setObj).dtlReservedCount = count;
}
void PublishNifSetData(PublishNifT *setObj, int64_t value, int32_t count)
{
    (*setObj).ifIndex = (int32_t)(value) + 10000;
    (*setObj).tp = (int32_t)value;
    (*setObj).chipUnit = 0;
    (*setObj).odaPhyPort = 0;
    (*setObj).state = 1;
    (*setObj).devId = 0;
    (*setObj).chassisId = 0;
    (*setObj).slotId = 0;
    (*setObj).cardId = 0;
    (*setObj).unitId = 0;
    (*setObj).portId = (int32_t)value;
    (*setObj).cpuType = 0;
    char ifNameStr[64] = "GE0/0";
    // 避免对if_base产生的数据冲突
    (void)snprintf(ifNameStr, sizeof(ifNameStr), "GEAE00/%d", value);
    (void)strToArry((*setObj).ifName, sizeof((*setObj).ifName), ifNameStr);
    (void)strToArry((*setObj).type, sizeof((*setObj).type), "GE");  // 必须是GetIfType内的值
    (void)setArryByValue((*setObj).mac, sizeof((*setObj).mac), value, U8_MAX, G_MACS);
    (*setObj).dtlReservedCount = count;
}
void ConfigAttributesSetData(ConfigAttributesT *setObj, int64_t value, int32_t count)
{
    (*setObj).nsId = 0;  // 避免热补丁升级重做失败
    (*setObj).ifIndex = (int32_t)(value + G_IF_INDEXS);
    (*setObj).phyState = 1;
    (*setObj).mtu = 1500;
    (*setObj).ifCfgBandwidth = 0;
    (*setObj).mtu6 = 1500;
    (*setObj).autoneg = 1;
    (*setObj).speed = 0;
    (*setObj).duplex = 0;
    (*setObj).combo = 0;
    (*setObj).loopback = 0;
    (*setObj).hasV4Addr = 0;
    (*setObj).serviceType = 0;
    (*setObj).autonegCap = 0;
    (*setObj).autonegAdvCap = 0;
    (*setObj).medium = 0;
    (*setObj).mru = 0;
    (*setObj).linkType = 0;
    (*setObj).ethClass = 0;
    (void)setArryByValue((*setObj).mac, sizeof((*setObj).mac), 0, U8_MAX);
    (void)setArryByValue((*setObj).alias, sizeof((*setObj).alias), 0, U8_MAX);
    (*setObj).dtlReservedCount = count;
}
void ConfigIfIpv4AddrSetData(ConfigIfIpv4AddrT *setObj, int64_t value, int32_t count)
{
    (*setObj).ifIndex = (int32_t)(value);
    (*setObj).vrfIndex = 1;
    (*setObj).address = 2;
    (*setObj).maskLen = (value % 2 == 0) ? 24 : 32;
    (*setObj).type = 0;
    (*setObj).dtlReservedCount = count;
}
void ConfigIfIpLearnSetData(ConfigIfIpLearnT *setObj, int64_t value, int32_t count)
{
    (*setObj).ifIndex = (int32_t)(value);
    (*setObj).ipLearnEnable = 0;
    (*setObj).dhcpStrict = 1;
    (*setObj).addBlackList = 2;
    (*setObj).dtlReservedCount = count;
}
void ConfigProtocolEnableSetData(ConfigProtocolEnableT *setObj, int64_t value, int32_t count)
{
    (*setObj).ifIndex = (int32_t)(value);
    (*setObj).proto = 0;
    (*setObj).enable = 1;
    (*setObj).dtlReservedCount = count;
}
void ConfigLinkUpDownModeSetData(ConfigLinkUpDownModeT *setObj, int64_t value, int32_t count)
{
    (*setObj).ifIndex = (int32_t)(value);
    (*setObj).mode = 1;
    (*setObj).dtlReservedCount = count;
}
int StateSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    StateT setObj = {0};
    StateSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "state", GMC_DATATYPE_INT32, &setObj.state, sizeof(setObj.state));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[StateSetId] state: %d, ret = %d.", setObj.state, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[StateSetId] dtlReservedCount: %d, ret = %d.", setObj.dtlReservedCount, ret);
        return ret;
    }
    return ret;
}
int StateSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    StateT *obj = (StateT *)t;
    ret = GmcSetVertexProperty(stmt, "state", GMC_DATATYPE_INT32, &obj->state, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[StateSet] state: %d, ret = %d.", obj->state, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[StateSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// 设置输入表属性的值
// addr: int4, ifIndex: int4, type: int1, mac: byte6, fakeFlag: int1, vlanId: int2, workIfIndex: int4, detectCount:int4,
// agingTime: int8
int ConfigArpSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigArpT setObj = {0};
    ConfigArpSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "addr", GMC_DATATYPE_INT32, &setObj.addr, sizeof(setObj.addr));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] addr: %d, ret = %d.", setObj.addr, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] ifIndex: %d, ret = %d.", setObj.ifIndex, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT8, &setObj.type, sizeof(setObj.type));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] type: %d, ret = %d.", setObj.type, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, setObj.mac, sizeof(setObj.mac));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] mac: %d, ret = %d.", setObj.mac, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "fakeFlag", GMC_DATATYPE_INT8, &setObj.fakeFlag, sizeof(setObj.fakeFlag));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] fakeFlag: %d, ret = %d.", setObj.fakeFlag, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlanId", GMC_DATATYPE_INT16, &setObj.vlanId, sizeof(setObj.vlanId));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] vlanId: %d, ret = %d.", setObj.vlanId, ret);
        return ret;
    }
    ret =
        GmcSetVertexProperty(stmt, "workIfIndex", GMC_DATATYPE_INT32, &setObj.workIfIndex, sizeof(setObj.workIfIndex));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] workIfIndex: %d, ret = %d.", setObj.workIfIndex, ret);
        return ret;
    }
    ret =
        GmcSetVertexProperty(stmt, "detectCount", GMC_DATATYPE_INT32, &setObj.detectCount, sizeof(setObj.detectCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] detectCount: %d, ret = %d.", setObj.detectCount, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "agingTime", GMC_DATATYPE_INT64, &setObj.agingTime, sizeof(setObj.agingTime));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] agingTime: %d, ret = %d.", setObj.agingTime, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpSetId] dtlReservedCount: %d, ret = %d.", setObj.dtlReservedCount, ret);
        return ret;
    }
    return ret;
}
int ConfigArpSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    ConfigArpT *obj = (ConfigArpT *)t;
    ret = GmcSetVertexProperty(stmt, "addr", GMC_DATATYPE_INT32, &obj->addr, sizeof(obj->addr));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT8, &obj->type, sizeof(obj->type));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, obj->mac, sizeof(obj->mac));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "fakeFlag", GMC_DATATYPE_INT8, &obj->fakeFlag, sizeof(obj->fakeFlag));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vlanId", GMC_DATATYPE_INT16, &obj->vlanId, sizeof(obj->vlanId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "workIfIndex", GMC_DATATYPE_INT32, &obj->workIfIndex, sizeof(obj->workIfIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "detectCount", GMC_DATATYPE_INT32, &obj->detectCount, sizeof(obj->detectCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "agingTime", GMC_DATATYPE_INT64, &obj->agingTime, sizeof(obj->agingTime));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}

int ConfigArpUpdateId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigArpT setObj = {0};
    ConfigArpUpdateData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "addr", GMC_DATATYPE_INT32, &setObj.addr, sizeof(setObj.addr));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] addr: %d, ret = %d.", setObj.addr, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] ifIndex: %d, ret = %d.", setObj.ifIndex, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT8, &setObj.type, sizeof(setObj.type));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] type: %d, ret = %d.", setObj.type, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, setObj.mac, sizeof(setObj.mac));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] mac: %d, ret = %d.", setObj.mac, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "fakeFlag", GMC_DATATYPE_INT8, &setObj.fakeFlag, sizeof(setObj.fakeFlag));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] fakeFlag: %d, ret = %d.", setObj.fakeFlag, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlanId", GMC_DATATYPE_INT16, &setObj.vlanId, sizeof(setObj.vlanId));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] vlanId: %d, ret = %d.", setObj.vlanId, ret);
        return ret;
    }
    ret =
        GmcSetVertexProperty(stmt, "workIfIndex", GMC_DATATYPE_INT32, &setObj.workIfIndex, sizeof(setObj.workIfIndex));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] workIfIndex: %d, ret = %d.", setObj.workIfIndex, ret);
        return ret;
    }
    ret =
        GmcSetVertexProperty(stmt, "detectCount", GMC_DATATYPE_INT32, &setObj.detectCount, sizeof(setObj.detectCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] detectCount: %d, ret = %d.", setObj.detectCount, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "agingTime", GMC_DATATYPE_INT64, &setObj.agingTime, sizeof(setObj.agingTime));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] agingTime: %d, ret = %d.", setObj.agingTime, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ConfigArpUpdateId] dtlReservedCount: %d, ret = %d.", setObj.dtlReservedCount, ret);
        return ret;
    }
    return ret;
}
int ConfigIpv4FwdSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigIpv4FwdT setObj = {0};
    ConfigIpv4FwdSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &setObj.nsId, sizeof(setObj.nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &setObj.vrfId, sizeof(setObj.vrfId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstIp", GMC_DATATYPE_INT32, &setObj.dstIp, sizeof(setObj.dstIp));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_INT32, &setObj.maskLen, sizeof(setObj.maskLen));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "nhpGroupFlag", GMC_DATATYPE_INT8, &setObj.nhpGroupFlag, sizeof(setObj.nhpGroupFlag));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "routeAttr", GMC_DATATYPE_INT16, &setObj.routeAttr, sizeof(setObj.routeAttr));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "routeFlags", GMC_DATATYPE_INT16, &setObj.routeFlags, sizeof(setObj.routeFlags));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "pathFlags", GMC_DATATYPE_INT32, &setObj.pathFlags, sizeof(setObj.pathFlags));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhpGroupId", GMC_DATATYPE_INT32, &setObj.nhpGroupId, sizeof(setObj.nhpGroupId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "primaryLabel", GMC_DATATYPE_INT32, &setObj.primaryLabel, sizeof(setObj.primaryLabel));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "attributeId", GMC_DATATYPE_INT32, &setObj.attributeId, sizeof(setObj.attributeId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "qosId", GMC_DATATYPE_INT16, &setObj.qosId, sizeof(setObj.qosId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigIpv4FwdSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    ConfigIpv4FwdT *obj = (ConfigIpv4FwdT *)t;
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &obj->nsId, sizeof(obj->nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &obj->vrfId, sizeof(obj->vrfId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstIp", GMC_DATATYPE_INT32, &obj->dstIp, sizeof(obj->dstIp));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_INT32, &obj->maskLen, sizeof(obj->maskLen));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhpGroupFlag", GMC_DATATYPE_INT8, &obj->nhpGroupFlag, sizeof(obj->nhpGroupFlag));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "routeAttr", GMC_DATATYPE_INT16, &obj->routeAttr, sizeof(obj->routeAttr));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "routeFlags", GMC_DATATYPE_INT16, &obj->routeFlags, sizeof(obj->routeFlags));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "pathFlags", GMC_DATATYPE_INT32, &obj->pathFlags, sizeof(obj->pathFlags));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhpGroupId", GMC_DATATYPE_INT32, &obj->nhpGroupId, sizeof(obj->nhpGroupId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primaryLabel", GMC_DATATYPE_INT32, &obj->primaryLabel, sizeof(obj->primaryLabel));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attributeId", GMC_DATATYPE_INT32, &obj->attributeId, sizeof(obj->attributeId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "qosId", GMC_DATATYPE_INT16, &obj->qosId, sizeof(obj->qosId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigNhpGroupSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigNhpGroupT setObj = {0};
    ConfigNhpGroupSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &setObj.nsId, sizeof(setObj.nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhpGroupId", GMC_DATATYPE_INT32, &setObj.nhpGroupId, sizeof(setObj.nhpGroupId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhpNum", GMC_DATATYPE_INT32, &setObj.nhpNum, sizeof(setObj.nhpNum));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &setObj.vrfId, sizeof(setObj.vrfId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigNhpGroupNodeSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigNhpGroupNodeT setObj = {0};
    ConfigNhpGroupNodeSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &setObj.nsId, sizeof(setObj.nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhpGroupId", GMC_DATATYPE_INT32, &setObj.nhpGroupId, sizeof(setObj.nhpGroupId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "attributeId", GMC_DATATYPE_INT32, &setObj.attributeId, sizeof(setObj.attributeId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "primaryNhpId", GMC_DATATYPE_INT32, &setObj.primaryNhpId, sizeof(setObj.primaryNhpId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "primaryLabel", GMC_DATATYPE_INT32, &setObj.primaryLabel, sizeof(setObj.primaryLabel));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "backupNhpId", GMC_DATATYPE_INT32, &setObj.backupNhpId, sizeof(setObj.backupNhpId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "backupLabel", GMC_DATATYPE_INT32, &setObj.backupLabel, sizeof(setObj.backupLabel));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &setObj.vrfId, sizeof(setObj.vrfId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigNhpGroupNodeSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    ConfigNhpGroupNodeT *obj = (ConfigNhpGroupNodeT *)t;
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &obj->nsId, sizeof(obj->nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhpGroupId", GMC_DATATYPE_INT32, &obj->nhpGroupId, sizeof(obj->nhpGroupId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attributeId", GMC_DATATYPE_INT32, &obj->attributeId, sizeof(obj->attributeId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primaryNhpId", GMC_DATATYPE_INT32, &obj->primaryNhpId, sizeof(obj->primaryNhpId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primaryLabel", GMC_DATATYPE_INT32, &obj->primaryLabel, sizeof(obj->primaryLabel));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "backupNhpId", GMC_DATATYPE_INT32, &obj->backupNhpId, sizeof(obj->backupNhpId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "backupLabel", GMC_DATATYPE_INT32, &obj->backupLabel, sizeof(obj->backupLabel));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &obj->vrfId, sizeof(obj->vrfId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigNhpBasicSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigNhpBasicT setObj = {0};
    ConfigNhpBasicSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "nhpIndex", GMC_DATATYPE_INT32, &setObj.nhpIndex, sizeof(setObj.nhpIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &setObj.vrfId, sizeof(setObj.vrfId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "originNhp", GMC_DATATYPE_INT32, &setObj.originNhp, sizeof(setObj.originNhp));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "iidFlags", GMC_DATATYPE_INT32, &setObj.iidFlags, sizeof(setObj.iidFlags));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &setObj.nsId, sizeof(setObj.nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigNhpStandardSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigNhpStandardT setObj = {0};
    ConfigNhpStandardSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "nhpIndex", GMC_DATATYPE_INT32, &setObj.nhpIndex, sizeof(setObj.nhpIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nextHop", GMC_DATATYPE_INT32, &setObj.nextHop, sizeof(setObj.nextHop));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "outIfIndex", GMC_DATATYPE_INT32, &setObj.outIfIndex, sizeof(setObj.outIfIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &setObj.vrfId, sizeof(setObj.vrfId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "iidFlags", GMC_DATATYPE_INT32, &setObj.iidFlags, sizeof(setObj.iidFlags));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &setObj.nsId, sizeof(setObj.nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigVapIfSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigVapIfT setObj = {0};
    ConfigVapIfSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &setObj.nsId, sizeof(setObj.nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "radioName", GMC_DATATYPE_FIXED, setObj.radioName, sizeof(setObj.radioName));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "adminState", GMC_DATATYPE_INT8, &setObj.adminState, sizeof(setObj.adminState));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vapType", GMC_DATATYPE_INT8, &setObj.vapType, sizeof(setObj.vapType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vapIndex", GMC_DATATYPE_INT8, &setObj.vapIndex, sizeof(setObj.vapIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "linkedId", GMC_DATATYPE_INT8, &setObj.linkedId, sizeof(setObj.linkedId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, setObj.mac, sizeof(setObj.mac));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "userIdentify", GMC_DATATYPE_INT8, &setObj.userIdentify, sizeof(setObj.userIdentify));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "userService", GMC_DATATYPE_INT8, &setObj.userService, sizeof(setObj.userService));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "userAuth", GMC_DATATYPE_INT8, &setObj.userAuth, sizeof(setObj.userAuth));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "authType", GMC_DATATYPE_INT8, &setObj.authType, sizeof(setObj.authType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "isEapolKeyToCp", GMC_DATATYPE_INT8, &setObj.isEapolKeyToCp, sizeof(setObj.isEapolKeyToCp));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "protocolTunnelFwd", GMC_DATATYPE_INT8, &setObj.protocolTunnelFwd, sizeof(setObj.protocolTunnelFwd));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ssid", GMC_DATATYPE_FIXED, setObj.ssid, sizeof(setObj.ssid));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "globalIdx", GMC_DATATYPE_INT16, &setObj.globalIdx, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vapFlag", GMC_DATATYPE_INT8, &setObj.vapFlag, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "broadPeerId", GMC_DATATYPE_INT32, &setObj.broadPeerId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "broadPfeVapIdx", GMC_DATATYPE_INT32, &setObj.broadPfeVapIdx, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "broadSessionInfo", GMC_DATATYPE_INT16, &setObj.broadSessionInfo, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mainLinkFlag", GMC_DATATYPE_INT8, &setObj.mainLinkFlag, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "multiMediaEn", GMC_DATATYPE_INT8, &setObj.multiMediaEn, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tcpWndTurn", GMC_DATATYPE_INT8, &setObj.tcpWndTurn, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "arpTest", GMC_DATATYPE_INT8, &setObj.arpTest, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigVapIfSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    ConfigVapIfT *obj = (ConfigVapIfT *)t;
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &obj->nsId, sizeof(obj->nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "radioName", GMC_DATATYPE_FIXED, obj->radioName, sizeof(obj->radioName));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "adminState", GMC_DATATYPE_INT8, &obj->adminState, sizeof(obj->adminState));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vapType", GMC_DATATYPE_INT8, &obj->vapType, sizeof(obj->vapType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vapIndex", GMC_DATATYPE_INT8, &obj->vapIndex, sizeof(obj->vapIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "linkedId", GMC_DATATYPE_INT8, &obj->linkedId, sizeof(obj->linkedId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, obj->mac, sizeof(obj->mac));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "userIdentify", GMC_DATATYPE_INT8, &obj->userIdentify, sizeof(obj->userIdentify));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "userService", GMC_DATATYPE_INT8, &obj->userService, sizeof(obj->userService));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "userAuth", GMC_DATATYPE_INT8, &obj->userAuth, sizeof(obj->userAuth));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "authType", GMC_DATATYPE_INT8, &obj->authType, sizeof(obj->authType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "isEapolKeyToCp", GMC_DATATYPE_INT8, &obj->isEapolKeyToCp, sizeof(obj->isEapolKeyToCp));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "protocolTunnelFwd", GMC_DATATYPE_INT8, &obj->protocolTunnelFwd, sizeof(obj->protocolTunnelFwd));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ssid", GMC_DATATYPE_FIXED, obj->ssid, sizeof(obj->ssid));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mainLinkFlag", GMC_DATATYPE_INT8, &obj->mainLinkFlag, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "multiMediaEn", GMC_DATATYPE_INT8, &obj->multiMediaEn, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tcpWndTurn", GMC_DATATYPE_INT8, &obj->tcpWndTurn, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "arpTest", GMC_DATATYPE_INT8, &obj->arpTest, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int PublishRadioNameSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    PublishRadioNameT setObj = {0};
    PublishRadioNameSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "radioName", GMC_DATATYPE_FIXED, setObj.radioName, sizeof(setObj.radioName));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_FIXED, setObj.type, sizeof(setObj.type));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_INT32, &setObj.tp, sizeof(setObj.tp));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "phy_port", GMC_DATATYPE_INT32, &setObj.phyPort, sizeof(setObj.phyPort));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "port_id", GMC_DATATYPE_INT32, &setObj.portId, sizeof(setObj.portId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "cpu_type", GMC_DATATYPE_INT8, &setObj.cpuType, sizeof(setObj.cpuType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "drvPortType", GMC_DATATYPE_INT8, &setObj.drvPortType, sizeof(setObj.drvPortType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "drvSubType", GMC_DATATYPE_INT8, &setObj.drvSubType, sizeof(setObj.drvSubType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int PublishRadioNameSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    PublishRadioNameT *obj = (PublishRadioNameT *)t;
    ret = GmcSetVertexProperty(stmt, "radioName", GMC_DATATYPE_FIXED, obj->radioName, sizeof(obj->radioName));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_FIXED, obj->type, sizeof(obj->type));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_INT32, &obj->tp, sizeof(obj->tp));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "phy_port", GMC_DATATYPE_INT32, &obj->phyPort, sizeof(obj->phyPort));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "port_id", GMC_DATATYPE_INT32, &obj->portId, sizeof(obj->portId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "cpu_type", GMC_DATATYPE_INT8, &obj->cpuType, sizeof(obj->cpuType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "drvPortType", GMC_DATATYPE_INT8, &obj->drvPortType, sizeof(obj->drvPortType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "drvSubType", GMC_DATATYPE_INT8, &obj->drvSubType, sizeof(obj->drvSubType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigIfSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigIfT setObj = {0};
    ConfigIfSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &setObj.nsId, sizeof(setObj.nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT16, &setObj.type, sizeof(setObj.type));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "phyPort", GMC_DATATYPE_INT32, &setObj.phyPort, sizeof(setObj.phyPort));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "masterIfIndex", GMC_DATATYPE_INT32, &setObj.masterIfIndex, sizeof(setObj.masterIfIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "state", GMC_DATATYPE_INT8, &setObj.state, sizeof(setObj.state));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifName", GMC_DATATYPE_FIXED, setObj.ifName, sizeof(setObj.ifName));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigIfSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    ConfigIfT *obj = (ConfigIfT *)t;
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &obj->nsId, sizeof(obj->nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT16, &obj->type, sizeof(obj->type));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "phyPort", GMC_DATATYPE_INT32, &obj->phyPort, sizeof(obj->phyPort));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "masterIfIndex", GMC_DATATYPE_INT32, &obj->masterIfIndex, sizeof(obj->masterIfIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "state", GMC_DATATYPE_INT8, &obj->state, sizeof(obj->state));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifName", GMC_DATATYPE_FIXED, obj->ifName, sizeof(obj->ifName));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int PublishNifSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    PublishNifT setObj = {0};
    PublishNifSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tp", GMC_DATATYPE_INT32, &setObj.tp, sizeof(setObj.tp));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "chip_unit", GMC_DATATYPE_INT8, &setObj.chipUnit, sizeof(setObj.chipUnit));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "oda_phy_port", GMC_DATATYPE_INT8, &setObj.odaPhyPort, sizeof(setObj.odaPhyPort));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "state", GMC_DATATYPE_INT8, &setObj.state, sizeof(setObj.state));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dev_id", GMC_DATATYPE_INT32, &setObj.devId, sizeof(setObj.devId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "chassis_id", GMC_DATATYPE_INT32, &setObj.chassisId, sizeof(setObj.chassisId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "slot_id", GMC_DATATYPE_INT32, &setObj.slotId, sizeof(setObj.slotId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "card_id", GMC_DATATYPE_INT32, &setObj.cardId, sizeof(setObj.cardId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "unit_id", GMC_DATATYPE_INT32, &setObj.unitId, sizeof(setObj.unitId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "port_id", GMC_DATATYPE_INT32, &setObj.portId, sizeof(setObj.portId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "cpu_type", GMC_DATATYPE_INT8, &setObj.cpuType, sizeof(setObj.cpuType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifName", GMC_DATATYPE_FIXED, setObj.ifName, sizeof(setObj.ifName));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_FIXED, setObj.type, sizeof(setObj.type));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, setObj.mac, sizeof(setObj.mac));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "drvPortType", GMC_DATATYPE_INT8, &setObj.drvPortType, sizeof(setObj.drvPortType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "drvSubType", GMC_DATATYPE_INT8, &setObj.drvSubType, sizeof(setObj.drvSubType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigAttributesSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    ConfigAttributesT setObj = {0};
    ConfigAttributesSetData(&setObj, value, count);
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &setObj.nsId, sizeof(setObj.nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "phyState", GMC_DATATYPE_INT8, &setObj.phyState, sizeof(setObj.phyState));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mtu", GMC_DATATYPE_INT32, &setObj.mtu, sizeof(setObj.mtu));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "if_cfg_bandwidth", GMC_DATATYPE_INT32, &setObj.ifCfgBandwidth, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mtu6", GMC_DATATYPE_INT32, &setObj.mtu6, sizeof(setObj.mtu6));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "autoneg", GMC_DATATYPE_INT8, &setObj.autoneg, sizeof(setObj.autoneg));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "speed", GMC_DATATYPE_INT64, &setObj.speed, sizeof(setObj.speed));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "duplex", GMC_DATATYPE_INT8, &setObj.duplex, sizeof(setObj.duplex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "combo", GMC_DATATYPE_INT8, &setObj.combo, sizeof(setObj.combo));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "loopback", GMC_DATATYPE_INT8, &setObj.loopback, sizeof(setObj.loopback));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "hasV4Addr", GMC_DATATYPE_INT8, &setObj.hasV4Addr, sizeof(setObj.hasV4Addr));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "service_type", GMC_DATATYPE_INT8, &setObj.serviceType, sizeof(setObj.serviceType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "autoneg_cap", GMC_DATATYPE_INT8, &setObj.autonegCap, sizeof(setObj.autonegCap));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "autoneg_adv_cap", GMC_DATATYPE_INT16, &setObj.autonegAdvCap, sizeof(setObj.autonegAdvCap));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "medium", GMC_DATATYPE_INT8, &setObj.medium, sizeof(setObj.medium));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mru", GMC_DATATYPE_INT16, &setObj.mru, sizeof(setObj.mru));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "link_type", GMC_DATATYPE_INT8, &setObj.linkType, sizeof(setObj.linkType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "eth_class", GMC_DATATYPE_INT8, &setObj.ethClass, sizeof(setObj.ethClass));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "hasV6Addr", GMC_DATATYPE_INT8, &setObj.hasV6Addr, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, setObj.mac, sizeof(setObj.mac));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "alias", GMC_DATATYPE_FIXED, setObj.alias, sizeof(setObj.alias));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return ret;
}
int ConfigAttributesSet(GmcStmtT *stmt, void *t)
{
    ConfigAttributesT *obj = (ConfigAttributesT *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &obj->nsId, sizeof(obj->nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "phyState", GMC_DATATYPE_INT8, &obj->phyState, sizeof(obj->phyState));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mtu", GMC_DATATYPE_INT32, &obj->mtu, sizeof(obj->mtu));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "if_cfg_bandwidth", GMC_DATATYPE_INT32, &obj->ifCfgBandwidth, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mtu6", GMC_DATATYPE_INT32, &obj->mtu6, sizeof(obj->mtu6));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "autoneg", GMC_DATATYPE_INT8, &obj->autoneg, sizeof(obj->autoneg));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "speed", GMC_DATATYPE_INT64, &obj->speed, sizeof(obj->speed));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "duplex", GMC_DATATYPE_INT8, &obj->duplex, sizeof(obj->duplex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "combo", GMC_DATATYPE_INT8, &obj->combo, sizeof(obj->combo));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "loopback", GMC_DATATYPE_INT8, &obj->loopback, sizeof(obj->loopback));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "hasV4Addr", GMC_DATATYPE_INT8, &obj->hasV4Addr, sizeof(obj->hasV4Addr));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "service_type", GMC_DATATYPE_INT8, &obj->serviceType, sizeof(obj->serviceType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "autoneg_cap", GMC_DATATYPE_INT8, &obj->autonegCap, sizeof(obj->autonegCap));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "autoneg_adv_cap", GMC_DATATYPE_INT16, &obj->autonegAdvCap, sizeof(obj->autonegAdvCap));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "medium", GMC_DATATYPE_INT8, &obj->medium, sizeof(obj->medium));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mru", GMC_DATATYPE_INT16, &obj->mru, sizeof(obj->mru));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "link_type", GMC_DATATYPE_INT8, &obj->linkType, sizeof(obj->linkType));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "eth_class", GMC_DATATYPE_INT8, &obj->ethClass, sizeof(obj->ethClass));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "hasV6Addr", GMC_DATATYPE_INT8, &obj->hasV6Addr, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, obj->mac, sizeof(obj->mac));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "alias", GMC_DATATYPE_FIXED, obj->alias, sizeof(obj->alias));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return ret;
}
int ConfigIfIpv4AddrSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigIfIpv4AddrT setObj = {0};
    ConfigIfIpv4AddrSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_INT32, &setObj.vrfIndex, sizeof(setObj.vrfIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "address", GMC_DATATYPE_INT32, &setObj.address, sizeof(setObj.address));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_INT16, &setObj.maskLen, sizeof(setObj.maskLen));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT16, &setObj.type, sizeof(setObj.type));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigIfIpv4AddrSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    ConfigIfIpv4AddrT *obj = (ConfigIfIpv4AddrT *)t;
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_INT32, &obj->vrfIndex, sizeof(obj->vrfIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "address", GMC_DATATYPE_INT32, &obj->address, sizeof(obj->address));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_INT16, &obj->maskLen, sizeof(obj->maskLen));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT16, &obj->type, sizeof(obj->type));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigIfIpLearnSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigIfIpLearnT setObj = {0};
    ConfigIfIpLearnSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "ipLearnEnable", GMC_DATATYPE_INT8, &setObj.ipLearnEnable, sizeof(setObj.ipLearnEnable));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dhcpStrict", GMC_DATATYPE_INT8, &setObj.dhcpStrict, sizeof(setObj.dhcpStrict));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "addBlackList", GMC_DATATYPE_INT8, &setObj.addBlackList, sizeof(setObj.addBlackList));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigIfIpLearnSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    ConfigIfIpLearnT *obj = (ConfigIfIpLearnT *)t;
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "ipLearnEnable", GMC_DATATYPE_INT8, &obj->ipLearnEnable, sizeof(obj->ipLearnEnable));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dhcpStrict", GMC_DATATYPE_INT8, &obj->dhcpStrict, sizeof(obj->dhcpStrict));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "addBlackList", GMC_DATATYPE_INT8, &obj->addBlackList, sizeof(obj->addBlackList));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigProtocolEnableSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigProtocolEnableT setObj = {0};
    ConfigProtocolEnableSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "proto", GMC_DATATYPE_INT8, &setObj.proto, sizeof(setObj.proto));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "enable", GMC_DATATYPE_INT8, &setObj.enable, sizeof(setObj.enable));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigLinkUpDownModeSetId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    int ret = 0;
    ConfigLinkUpDownModeT setObj = {0};
    ConfigLinkUpDownModeSetData(&setObj, value, count);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &setObj.ifIndex, sizeof(setObj.ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mode", GMC_DATATYPE_INT8, &setObj.mode, sizeof(setObj.mode));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int ConfigLinkUpDownModeSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    ConfigLinkUpDownModeT *obj = (ConfigLinkUpDownModeT *)t;
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mode", GMC_DATATYPE_INT8, &obj->mode, sizeof(obj->mode));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
int PortAttrChgSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    PortAttrChgT *obj = (PortAttrChgT *)t;
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attr", GMC_DATATYPE_INT8, &obj->attr, sizeof(obj->attr));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "value", GMC_DATATYPE_INT32, &obj->value, sizeof(obj->value));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return GMERR_OK;
}
int EthTrunkSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    EthTrunkT *obj = (EthTrunkT *)t;
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &obj->nsId, sizeof(obj->nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trunkId", GMC_DATATYPE_INT32, &obj->trunkId, sizeof(obj->trunkId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "domain", GMC_DATATYPE_INT8, &obj->domain, sizeof(obj->domain));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return GMERR_OK;
}
int EthTrunkMembersSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    EthTrunkMembersT *obj = (EthTrunkMembersT *)t;
    ret = GmcSetVertexProperty(stmt, "memIfIndex", GMC_DATATYPE_INT32, &obj->memIfIndex, sizeof(obj->memIfIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trunkId", GMC_DATATYPE_INT32, &obj->trunkId, sizeof(obj->trunkId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &obj->nsId, sizeof(obj->nsId));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "weight", GMC_DATATYPE_INT16, &obj->weight, sizeof(obj->weight));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    TEST_EXPECT_INT32(GMERR_OK, ret);

    return GMERR_OK;
}
// TimeRangeCfgSet
int TimeRangeCfgSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    TimeRangeCfgT *obj = (TimeRangeCfgT *)t;
    ret = GmcSetVertexProperty(stmt, "trngId", GMC_DATATYPE_INT32, &obj->trngId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_INT32, &obj->vrId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trngInnerId", GMC_DATATYPE_INT32, &obj->trngInnerId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trngStatus", GMC_DATATYPE_INT8, &obj->trngStatus, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// PortPoolCfgSet
int PortPoolCfgSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    PortPoolCfgT *obj = (PortPoolCfgT *)t;
    ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_INT32, &obj->vrId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "poolId", GMC_DATATYPE_INT32, &obj->poolId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "portId", GMC_DATATYPE_INT32, &obj->portId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "rangeOp", GMC_DATATYPE_INT8, &obj->rangeOp, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "startPort", GMC_DATATYPE_INT16, &obj->startPort, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "endPort", GMC_DATATYPE_INT16, &obj->endPort, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// IpPoolCfgSet
int IpPoolCfgSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    IpPoolCfgT *obj = (IpPoolCfgT *)t;
    ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_INT32, &obj->vrId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "poolId", GMC_DATATYPE_INT32, &obj->poolId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ipId", GMC_DATATYPE_INT32, &obj->ipId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ipAddr", GMC_DATATYPE_INT32, &obj->ipAddr, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ipMask", GMC_DATATYPE_INT32, &obj->ipMask, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// AdvRuleSet
int AdvRuleSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    AdvRuleT *obj = (AdvRuleT *)t;
    ret = GmcSetVertexProperty(stmt, "aclGroupId", GMC_DATATYPE_INT32, &obj->aclGroupId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "aclIndex", GMC_DATATYPE_INT32, &obj->aclIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "actionType", GMC_DATATYPE_INT8, &obj->actionType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "aclCondMask", GMC_DATATYPE_INT32, &obj->aclCondMask, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcIpAddr", GMC_DATATYPE_INT32, &obj->srcIpAddr, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcIpMask", GMC_DATATYPE_INT32, &obj->srcIpMask, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcIpPool", GMC_DATATYPE_INT32, &obj->srcIpPool, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstIpAddr", GMC_DATATYPE_INT32, &obj->dstIpAddr, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstIpMask", GMC_DATATYPE_INT32, &obj->dstIpMask, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstIpPool", GMC_DATATYPE_INT32, &obj->dstIpPool, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcPortBeg", GMC_DATATYPE_INT16, &obj->srcPortBeg, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcPortEnd", GMC_DATATYPE_INT16, &obj->srcPortEnd, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcPortOp", GMC_DATATYPE_INT8, &obj->srcPortOp, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcPortPool", GMC_DATATYPE_INT32, &obj->srcPortPool, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstPortBeg", GMC_DATATYPE_INT16, &obj->dstPortBeg, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstPortEnd", GMC_DATATYPE_INT16, &obj->dstPortEnd, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstPortOp", GMC_DATATYPE_INT8, &obj->dstPortOp, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstPortPool", GMC_DATATYPE_INT32, &obj->dstPortPool, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "aclVrId", GMC_DATATYPE_INT32, &obj->aclVrId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "aclPriority", GMC_DATATYPE_INT32, &obj->aclPriority, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "logFlag", GMC_DATATYPE_INT8, &obj->logFlag, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "aclVpnIndex", GMC_DATATYPE_INT32, &obj->aclVpnIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trngStatus", GMC_DATATYPE_INT8, &obj->trngStatus, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "anyFlag", GMC_DATATYPE_INT8, &obj->anyFlag, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "protocol", GMC_DATATYPE_INT8, &obj->protocol, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "fragType", GMC_DATATYPE_INT8, &obj->fragType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tos", GMC_DATATYPE_INT8, &obj->tos, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tcpFlag", GMC_DATATYPE_INT8, &obj->tcpFlag, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "icmpType", GMC_DATATYPE_INT8, &obj->icmpType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "icmpCode", GMC_DATATYPE_INT8, &obj->icmpCode, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dscp", GMC_DATATYPE_INT8, &obj->dscp, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ipPre", GMC_DATATYPE_INT8, &obj->ipPre, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trngId", GMC_DATATYPE_INT32, &obj->trngId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "igmpType", GMC_DATATYPE_INT8, &obj->igmpType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "pktLenOp", GMC_DATATYPE_INT8, &obj->pktLenOp, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "pktLenBgn", GMC_DATATYPE_INT16, &obj->pktLenBgn, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "pktLenEnd", GMC_DATATYPE_INT16, &obj->pktLenEnd, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tcpEstablished", GMC_DATATYPE_INT8, &obj->tcpEstablished, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ttlOp", GMC_DATATYPE_INT8, &obj->ttlOp, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ttlBgn", GMC_DATATYPE_INT8, &obj->ttlBgn, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);  // 40
    ret = GmcSetVertexProperty(stmt, "ttlEnd", GMC_DATATYPE_INT8, &obj->ttlEnd, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vni", GMC_DATATYPE_INT32, &obj->vni, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tcpFlagMask", GMC_DATATYPE_INT8, &obj->tcpFlagMask, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcUclIndex", GMC_DATATYPE_INT16, &obj->srcUclIndex, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstUclIndex", GMC_DATATYPE_INT16, &obj->dstUclIndex, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "uclSrcType", GMC_DATATYPE_INT8, &obj->uclSrcType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "uclDstType", GMC_DATATYPE_INT8, &obj->uclDstType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "tableType", GMC_DATATYPE_INT8, &obj->tableType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);  // 48
    ret = GmcSetVertexProperty(stmt, "srcIpv6Addr", GMC_DATATYPE_FIXED, obj->srcIpv6Addr, sizeof(obj->srcIpv6Addr));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "srcIpv6Mask", GMC_DATATYPE_FIXED, obj->srcIpv6Mask, sizeof(obj->srcIpv6Mask));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstIpv6Addr", GMC_DATATYPE_FIXED, obj->dstIpv6Addr, sizeof(obj->dstIpv6Addr));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dstIpv6Mask", GMC_DATATYPE_FIXED, obj->dstIpv6Mask, sizeof(obj->dstIpv6Mask));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "l4Version", GMC_DATATYPE_INT8, &obj->l4Version, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BridgeSet
int BridgeSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    BridgeT *obj = (BridgeT *)t;
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &obj->nsId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &obj->brId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_name", GMC_DATATYPE_FIXED, obj->brName, sizeof(obj->brName));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vlan_filter", GMC_DATATYPE_INT8, &obj->vlanFilter, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_if_index", GMC_DATATYPE_INT32, &obj->brIfIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BridgeMacAttrSet
int BridgeMacAttrSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    BridgeMacAttrT *obj = (BridgeMacAttrT *)t;
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &obj->nsId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &obj->brId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac_age_time", GMC_DATATYPE_INT32, &obj->macAgeTime, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac_thrd_up", GMC_DATATYPE_INT8, &obj->macThrdUp, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac_thrd_down", GMC_DATATYPE_INT8, &obj->macThrdDown, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "macLimitNum", GMC_DATATYPE_INT32, &obj->macLimitNum, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BR.Port
int PortSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    PortT *obj = (PortT *)t;
    ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "port_index", GMC_DATATYPE_INT32, &obj->portIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &obj->nsId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &obj->brId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "pvid", GMC_DATATYPE_INT16, &obj->pvid, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "link_type", GMC_DATATYPE_INT8, &obj->linkType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "stp_enable", GMC_DATATYPE_INT8, &obj->stpEnable, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BR.PortIsolateGrp
int PortIsolateGrpSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    PortIsolateGrpT *obj = (PortIsolateGrpT *)t;
    ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "port_index", GMC_DATATYPE_INT32, &obj->portIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "grp", GMC_DATATYPE_INT8, &obj->grp, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BR.PortMacAttr
int PortMacAttrSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    PortMacAttrT *obj = (PortMacAttrT *)t;
    ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "port_index", GMC_DATATYPE_INT32, &obj->portIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "learn", GMC_DATATYPE_INT8, &obj->learn, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "learn_act", GMC_DATATYPE_INT8, &obj->learnAct, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "limit", GMC_DATATYPE_INT32, &obj->limit, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "limit_act", GMC_DATATYPE_INT8, &obj->limitAct, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "limit_alm", GMC_DATATYPE_INT8, &obj->limitAlm, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BR.PortSecurity
int PortSecuritySet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    PortSecurityT *obj = (PortSecurityT *)t;
    ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "port_index", GMC_DATATYPE_INT32, &obj->portIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac_sec", GMC_DATATYPE_INT8, &obj->macSec, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac_sec_max", GMC_DATATYPE_INT32, &obj->macSecMax, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac_sec_act", GMC_DATATYPE_INT8, &obj->macSecAct, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mac_sec_age_time", GMC_DATATYPE_INT32, &obj->macSecAgeTime, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BR.VlanTagPort
int VlanTagPortSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    VlanTagPortT *obj = (VlanTagPortT *)t;
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &obj->nsId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &obj->brId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vlan_id", GMC_DATATYPE_INT16, &obj->vlanId, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "port_index", GMC_DATATYPE_INT32, &obj->portIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BR.Vlan
int VlanSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    VlanT *obj = (VlanT *)t;
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &obj->nsId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &obj->brId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vlan_id", GMC_DATATYPE_INT16, &obj->vlanId, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vlan_type", GMC_DATATYPE_INT8, &obj->vlanType, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "name", GMC_DATATYPE_FIXED, obj->name, sizeof(obj->name));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "desc", GMC_DATATYPE_FIXED, obj->desc, sizeof(obj->desc));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_vlan_if", GMC_DATATYPE_INT32, &obj->brVlanIf, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "is_ctrl", GMC_DATATYPE_INT8, &obj->isCtrl, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "inst_id", GMC_DATATYPE_INT16, &obj->instId, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
// BR.VlanTagPortChange
int VlanTagPortChangeSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    VlanTagPortChangeT *obj = (VlanTagPortChangeT *)t;
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &obj->nsId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &obj->brId, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return ret;
}
/*----------------------------------------------------索引更新和索引删除--------------------------------------------*/
// 主键更新
void ConfigArpPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t addrvalue = i;
        int32_t ifIndexvalue = startnum;
        int8_t typevalue = 3;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &addrvalue, sizeof(addrvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT8, &typevalue, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &addrvalue, sizeof(addrvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT8, &typevalue, sizeof(int8_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
        }
        // arp表存在订阅关系不存在的情况，屏蔽1015002错误码
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL &&
            ret != GMERR_UNEXPECTED_NULL_VALUE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 二级索引删除
void ConfigArpIndex1Delete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    // 二级索引为index删除
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigArpIndex1Delete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
void ConfigArpIndex1Update(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t value)
{
    // 二级索引更新
    int32_t ifIndexvalue = value;
    int8_t typevalue = 3;
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 更新非主键值
    ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT8, &typevalue, sizeof(int8_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t tryCnt = 0;
    ret = GmcExecute(stmt);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 100) {
                break;
            }
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyId(stmt, 1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            // 更新非主键值
            ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT8, &typevalue, sizeof(int8_t));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcExecute(stmt);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
    }
    // arp表存在订阅关系不存在的情况，屏蔽1015002错误码
    if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL &&
        ret != GMERR_UNEXPECTED_NULL_VALUE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}
// 主键删除
void ConfigArpPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t addrvalue = i;
        int32_t ifIndexvalue = startnum;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &addrvalue, sizeof(addrvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigArpPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &addrvalue, sizeof(addrvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void ConfigIpv4FwdPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t vrfIdvalue = i;
        int32_t dstIpvalue = startnum;
        int32_t maskLenvalue = 24;
        int32_t pathFlagsvalue = 3;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &vrfIdvalue, sizeof(vrfIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &dstIpvalue, sizeof(dstIpvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &maskLenvalue, sizeof(maskLenvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "pathFlags", GMC_DATATYPE_INT32, &pathFlagsvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &vrfIdvalue, sizeof(vrfIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &dstIpvalue, sizeof(dstIpvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &maskLenvalue, sizeof(maskLenvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "pathFlags", GMC_DATATYPE_INT32, &pathFlagsvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigIpv4FwdPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t vrfIdvalue = i;
        int32_t dstIpvalue = startnum;
        int32_t maskLenvalue = 24;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &vrfIdvalue, sizeof(vrfIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &dstIpvalue, sizeof(dstIpvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &maskLenvalue, sizeof(maskLenvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigIpv4FwdPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &vrfIdvalue, sizeof(vrfIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &dstIpvalue, sizeof(dstIpvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &maskLenvalue, sizeof(maskLenvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void ConfigVapIfPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int8_t adminStatevalue = 3;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "adminState", GMC_DATATYPE_INT8, &adminStatevalue, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "adminState", GMC_DATATYPE_INT8, &adminStatevalue, sizeof(int8_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigVapIfPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = startnum;
        int32_t ifIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigVapIfPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void PublishRadioNamePKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        uint8_t radioNamevalue[64];
        (void)setArryByValue(radioNamevalue, sizeof(radioNamevalue), i, U8_MAX, G_MACS);
        int32_t tpvalue = 3;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, radioNamevalue, sizeof(radioNamevalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "port_id", GMC_DATATYPE_INT32, &tpvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, radioNamevalue, sizeof(radioNamevalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "port_id", GMC_DATATYPE_INT32, &tpvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void PublishRadioNamePKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        uint8_t radioNamevalue[64];
        (void)setArryByValue(radioNamevalue, sizeof(radioNamevalue), i, U8_MAX, G_MACS);
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, radioNamevalue, sizeof(radioNamevalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[PublishRadioNamePKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, radioNamevalue, sizeof(radioNamevalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigNhpGroupPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t nhpGroupIdvalue = startnum;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nhpGroupIdvalue, sizeof(nhpGroupIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigNhpGroupPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nhpGroupIdvalue, sizeof(nhpGroupIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigIfPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        uint8_t ifNamevalue[64];
        (void)setArryByValue(ifNamevalue, sizeof(ifNamevalue), i, U8_MAX, G_MACS);
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, ifNamevalue, sizeof(ifNamevalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigIfPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, ifNamevalue, sizeof(ifNamevalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigIfIpLearnPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, "Ifm.ConfigIfIpLearn", GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigIfIpLearnPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, "Ifm.ConfigIfIpLearn", GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void PublishNifPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndex = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndex, sizeof(ifIndex));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[PublishNifPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndex, sizeof(ifIndex));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        // 客户端异常断连，错误码由原先16004变成15002,存在1004000错误码
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL &&
            ret != GMERR_UNEXPECTED_NULL_VALUE && ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_DATA_EXCEPTION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void ConfigNhpGroupNodePKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t nhpGroupIdvalue = startnum;
        int32_t attributeIdvalue = startnum;
        int32_t primaryNhpIdvalue = startnum;
        int32_t primaryLabelvalue = startnum;
        int32_t backupNhpIdvalue = startnum;
        int32_t backupLabelvalue = startnum;
        int32_t vrfIdvalue = 3;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nhpGroupIdvalue, sizeof(nhpGroupIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &attributeIdvalue, sizeof(attributeIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &primaryNhpIdvalue, sizeof(primaryNhpIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT32, &primaryLabelvalue, sizeof(primaryLabelvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_INT32, &backupNhpIdvalue, sizeof(backupNhpIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_INT32, &backupLabelvalue, sizeof(backupLabelvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &vrfIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nhpGroupIdvalue, sizeof(nhpGroupIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &attributeIdvalue, sizeof(attributeIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &primaryNhpIdvalue, sizeof(primaryNhpIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT32, &primaryLabelvalue, sizeof(primaryLabelvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_INT32, &backupNhpIdvalue, sizeof(backupNhpIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_INT32, &backupLabelvalue, sizeof(backupLabelvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &vrfIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 二级索引删除
void ConfigNhpGroupNodeIndex1Delete(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t nhpGroupIdvalue = startnum;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nhpGroupIdvalue, sizeof(nhpGroupIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigNhpGroupNodeIndex1Delete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nhpGroupIdvalue, sizeof(nhpGroupIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void ConfigAttributesPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t ifIndexvalue = 6500;
        int32_t mtuvalue = 3;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "mtu", GMC_DATATYPE_INT32, &mtuvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "mtu", GMC_DATATYPE_INT32, &mtuvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL &&
            ret != GMERR_UNEXPECTED_NULL_VALUE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
// 另外一个进程客户端异常退出，会影响到删除操作，导致超时
void ConfigAttributesPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t ifIndexvalue = 6500;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigAttributesPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL &&
            ret != GMERR_UNEXPECTED_NULL_VALUE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void ConfigIfIpv4AddrPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int32_t vrfIndexvalue = startnum;
        int32_t addressvalue = startnum;
        int16_t maskLenvalue = 32;
        int16_t typevalue = 1;
        int16_t typeupdatevalue = 3;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &vrfIndexvalue, sizeof(vrfIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &addressvalue, sizeof(addressvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT16, &maskLenvalue, sizeof(maskLenvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT16, &typevalue, sizeof(typevalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新主键值
        ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT16, &typeupdatevalue, sizeof(int16_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &vrfIndexvalue, sizeof(vrfIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &addressvalue, sizeof(addressvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT16, &maskLenvalue, sizeof(maskLenvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT16, &typevalue, sizeof(typevalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新主键值
                ret = GmcSetVertexProperty(stmt, "type", GMC_DATATYPE_INT16, &typeupdatevalue, sizeof(int16_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 二级索引删除
void ConfigIfIpv4AddrIndex1Delete(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigIfIpv4AddrIndex1Delete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL &&
            ret != GMERR_PRIMARY_KEY_VIOLATION) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigNhpBasicPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nhpIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nhpIndexvalue, sizeof(nhpIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigNhpBasicPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nhpIndexvalue, sizeof(nhpIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigProtocolEnablePKDelete(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigProtocolEnablePKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 二级索引更新
void ConfigNhpStandardIndex1Update(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nhpIndexvalue = i;
        int32_t iidFlagsvalue = 3;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &nhpIndexvalue, sizeof(nhpIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "iidFlags", GMC_DATATYPE_INT32, &iidFlagsvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &nhpIndexvalue, sizeof(nhpIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "iidFlags", GMC_DATATYPE_INT32, &iidFlagsvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigNhpStandardPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nhpIndexvalue = i;
        int32_t nextHopvalue = i;
        int32_t outIfIndexvalue = startnum;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nhpIndexvalue, sizeof(nhpIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nextHopvalue, sizeof(nextHopvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &outIfIndexvalue, sizeof(outIfIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigNhpStandardPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nhpIndexvalue, sizeof(nhpIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nextHopvalue, sizeof(nextHopvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &outIfIndexvalue, sizeof(outIfIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 异步接口的主键删除
void ConfigNhpStandardPKDeleteAsync(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum,
    int32_t endnum)
{
    int ret = 0;
    int32_t upgradeVersionVal = -1;
    AsyncUserDataT tdata = {0};
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.deleteCb = delete_vertex_callback;
    requestCtx.userData = &tdata;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startnum; i < endnum; i++) {
        int32_t nhpIndexvalue = i;
        int32_t nextHopvalue = i;
        int32_t outIfIndexvalue = i;
        
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nhpIndexvalue, sizeof(nhpIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nextHopvalue, sizeof(nextHopvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &outIfIndexvalue, sizeof(outIfIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecuteAsync(stmt, &requestCtx);
        ret = testWaitAsyncRecv(&tdata);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (tdata.status == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigNhpStandardPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nhpIndexvalue, sizeof(nhpIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nextHopvalue, sizeof(nextHopvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &outIfIndexvalue, sizeof(outIfIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecuteAsync(stmt, &requestCtx);
                ret = testWaitAsyncRecv(&tdata);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } while (tdata.status == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (tdata.status != GMERR_OUT_OF_MEMORY && tdata.status != GMERR_LOCK_NOT_AVAILABLE &&
            tdata.status != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void ConfigLinkUpDownModePKUpdate(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int8_t modevalue = 3;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "mode", GMC_DATATYPE_INT8, &modevalue, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "mode", GMC_DATATYPE_INT8, &modevalue, sizeof(int8_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void ConfigLinkUpDownModePKDelete(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[ConfigLinkUpDownModePKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void PortAttrChgPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = 18;
        int8_t attrvalue = int8_t(-128 + i);
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT8, &attrvalue, sizeof(attrvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[PortAttrChgPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT8, &attrvalue, sizeof(attrvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void EthTrunkPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int32_t nsIdvalue = startnum;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[EthTrunkPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(ifIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(nsIdvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void EthTrunkMembersPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t memIfIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &memIfIndexvalue, sizeof(memIfIndexvalue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[EthTrunkMembersPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &memIfIndexvalue, sizeof(memIfIndexvalue));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        // 客户端异常退出，订阅通道删除订阅关系还在，删除报1015002错误码，存在1002001错误码
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL &&
            ret != GMERR_UNEXPECTED_NULL_VALUE && ret != GMERR_DATA_EXCEPTION &&
            ret != GMERR_CONNECTION_DOES_NOT_EXIST) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void TimeRangeCfgPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t vrIdvalue = i;
        int32_t trngInnerIdvalue = 30;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &vrIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "trngInnerId", GMC_DATATYPE_INT32, &trngInnerIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &vrIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "trngInnerId", GMC_DATATYPE_INT32, &trngInnerIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void TimeRangeCfgPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t vrIdvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &vrIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[TimeRangeCfgPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &vrIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void PortPoolCfgPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t vrIdvalue = i;
        int32_t poolIdvalue = i;
        int32_t portIdvalue = startnum;
        int32_t startPortvalue = 30;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &poolIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &portIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "startPort", GMC_DATATYPE_INT16, &startPortvalue, sizeof(int16_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &poolIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &portIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "startPort", GMC_DATATYPE_INT16, &startPortvalue, sizeof(int16_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void PortPoolCfgPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t vrIdvalue = i;
        int32_t poolIdvalue = i;
        int32_t portIdvalue = startnum;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &poolIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &portIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[PortPoolCfgPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &poolIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &portIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void IpPoolCfgPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t vrIdvalue = i;
        int32_t poolIdvalue = i;
        int32_t ipIdvalue = startnum;
        int32_t ipMaskvalue = 30;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &poolIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ipIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "ipMask", GMC_DATATYPE_INT32, &ipMaskvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &poolIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ipIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "ipMask", GMC_DATATYPE_INT32, &ipMaskvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void IpPoolCfgPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t vrIdvalue = i;
        int32_t poolIdvalue = i;
        int32_t ipIdvalue = startnum;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &poolIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ipIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[IpPoolCfgPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &poolIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ipIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void AdvRulePKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t aclIndexvalue = i;
        int32_t aclVrIdvalue = startnum;
        int32_t aclGroupIdvalue = i;
        int8_t tableTypevalue = 30;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &aclGroupIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &aclIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "tableType", GMC_DATATYPE_INT8, &tableTypevalue, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &aclGroupIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &aclIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "tableType", GMC_DATATYPE_INT8, &tableTypevalue, sizeof(int8_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

// 二级索引删除
void AdvRuleIndex1Delete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t aclVrIdvalue = startnum;
        int32_t aclGroupIdvalue = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &aclVrIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &aclGroupIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[AdvRuleIndex1Delete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &aclVrIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &aclGroupIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void BridgePKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t brIdvalue = i;
        int32_t brIfIndexvalue = 30;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "br_if_index", GMC_DATATYPE_INT32, &brIfIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "br_if_index", GMC_DATATYPE_INT32, &brIfIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void BridgePKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t brIdvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[BridgePKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void BridgeMacAttrPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t brIdvalue = i;
        int32_t macLimitNumvalue = 30;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "macLimitNum", GMC_DATATYPE_INT32, &macLimitNumvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "macLimitNum", GMC_DATATYPE_INT32, &macLimitNumvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void BridgeMacAttrPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = i;
        int32_t brIdvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[BridgeMacAttrPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 二级索引更新
void PortIndex1Update(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t portIndexvalue = i;
        int32_t nsIdvalue = 30;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新非主键值
        ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新非主键值
                ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void PortPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[PortPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void PortSecurityPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[PortSecurityPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void PortMacAttrPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[PortMacAttrPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void PortIsolateGrpPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t index, int32_t count)
{
    int ret = 0;
    for (int i = 0; i < count; i++) {
        int32_t ifIndexvalue = index + i;
        int32_t portIndexvalue = index + i;
        int8_t grpvalue = -128 + i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT8, &grpvalue, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[PortIsolateGrpPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT8, &grpvalue, sizeof(int8_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 二级索引更新
void VlanTagPortIndex1Update(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t ifIndexvalue = startnum;
        int32_t ifIndexUpdatevalue = 30;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新二级索引字段的值
        ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &ifIndexUpdatevalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新二级索引字段的值
                ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &ifIndexUpdatevalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void VlanTagPortPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = startnum + 1;
        int32_t brIdvalue = startnum + 1;
        int16_t vlanIdvalue = i;
        int32_t portIndexvalue = -2147352576;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &vlanIdvalue, sizeof(int16_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[VlanTagPortPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &vlanIdvalue, sizeof(int16_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键删除
void VlanTagPortPKDelete1(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = 0;
        int32_t brIdvalue = i;
        int16_t vlanIdvalue = i;
        int32_t portIndexvalue = -2147352576;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &vlanIdvalue, sizeof(int16_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[VlanTagPortPKDelete1] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &vlanIdvalue, sizeof(int16_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 主键更新
void VlanTagPortPKUpdate(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = startnum + 1;
        int32_t brIdvalue = startnum + 1;
        int16_t vlanIdvalue = i;
        int32_t portIndexvalue = -2147352576;
        int32_t ifIndexUpdatevalue = 1000;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &vlanIdvalue, sizeof(int16_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 更新二级索引字段的值
        ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &ifIndexUpdatevalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &vlanIdvalue, sizeof(int16_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT32, &portIndexvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 更新二级索引字段的值
                ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &ifIndexUpdatevalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 二级索引删除
void VlanTagPortIndex1Delete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    // 二级索引值只有startnum+100与1000
    int32_t startVal = (startnum + 100 > 1000) ? 1000 : startnum + 100;
    int32_t endVal = (startnum + 100 > 1000) ? startnum + 100 : 1000;
    for (int i = startVal; i <= endVal; i++) {
        int32_t ifIndexvalue = i;
        // 二级索引仅有这两个值
        if (ifIndexvalue == startVal || ifIndexvalue == endVal) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyId(stmt, 1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            uint32_t tryCnt = 0;
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                do {
                    usleep(10000);
                    tryCnt++;
                    if (tryCnt > 100) {
                        AW_FUN_Log(LOG_STEP, "[VlanTagPortIndex1Delete] delete try more 100, ret is %d.", ret);
                        break;
                    }
                    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyId(stmt, 1);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcExecute(stmt);
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            }
            if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    }
}
// 主键删除
void VlanPKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t nsIdvalue = 0;
        int32_t brIdvalue = i;
        int16_t vlanIdvalue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &vlanIdvalue, sizeof(int16_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[VlanPKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &brIdvalue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &vlanIdvalue, sizeof(int16_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// state表主键删除
void statePKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    int32_t stateValue = 1;
    int32_t upgradeVersionVal = -1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &stateValue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t tryCnt = 0;
    ret = GmcExecute(stmt);
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 100) {
                AW_FUN_Log(LOG_STEP, "[statePKDelete] delete try more 100, ret is %d.", ret);
                break;
            }
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyId(stmt, 0);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &stateValue, sizeof(int32_t));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcExecute(stmt);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    // 客户端异常退出，触发LswOda.EthTrunkMembers的notify表，会报1015002
    if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_SUB_PUSH_QUEUE_FULL &&
        ret != GMERR_UNEXPECTED_NULL_VALUE) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

// 批量结构化写和删除多个表
void WriteAndDelete(GmcConnT *conn, GmcStmtT *stmt, int32_t startnum, uint32_t tableRecordCount)
{
    int ret = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    // Arp.ConfigArp Fib.ConfigNhpGroup Ifm.ConfigIf
    TestLabelInfoT labelInfo1 = {(char *)"Arp.ConfigArp", 0, g_testNameSpace};
    TestLabelInfoT labelInfo2 = {(char *)"Fib.ConfigNhpGroup", 0, g_testNameSpace};
    TestLabelInfoT labelInfo3 = {(char *)"Ifm.ConfigIf", 0, g_testNameSpace};

    ConfigArpT *objIn1 = (ConfigArpT *)malloc(sizeof(ConfigArpT) * tableRecordCount);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[WriteAndDelete]objIn1 malloc failed !!!");
        return;
    }
    memset(objIn1, 0, sizeof(ConfigArpT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {  // tableRecordCount
        objIn1[i].addr = startnum + i;
        objIn1[i].ifIndex = startnum;
        objIn1[i].type = 2;
        // 设置过期字段的值为1天
        objIn1[i].agingTime = 24 * 60 * 60 * 1000;
        objIn1[i].dtlReservedCount = 1;
    }
    ConfigIfT *objIn2 = (ConfigIfT *)malloc(sizeof(ConfigIfT) * tableRecordCount);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[WriteAndDelete]objIn2 malloc failed !!!");
        free(objIn1);
        return;
    }
    memset(objIn2, 0, sizeof(ConfigIfT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {
        objIn2[i].nsId = startnum + i;
        objIn2[i].ifIndex = startnum + i;
        (void)setArryByValue(objIn2[i].ifName, sizeof(objIn2[i].ifName), startnum + i, U8_MAX, G_MACS);
        objIn2[i].dtlReservedCount = 1;
    }
    // 结构化写Fib.ConfigNhpGroup表,tableRecordCount条数据
    ConfigNhpGroupT *objIn3 = (ConfigNhpGroupT *)malloc(sizeof(ConfigNhpGroupT) * tableRecordCount);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[WriteAndDelete]objIn3 malloc failed !!!");
        free(objIn1);
        free(objIn2);
        return;
    }
    memset(objIn3, 0, sizeof(ConfigNhpGroupT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {
        objIn3[i].nsId = startnum + i;
        objIn3[i].nhpGroupId = startnum;
        objIn3[i].dtlReservedCount = 1;
    }
    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置批写的数据的记录数
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 结构化删除Arp.ConfigArp数据
    for (int i = 0; i < tableRecordCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, "Arp.ConfigArp", GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchBindStmt(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testStructSetIndexKeyWithBuf(stmt, &objIn1[i], 0, NULL, &labelInfo1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, "Fib.ConfigNhpGroup", GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < tableRecordCount; i++) {
        ret = testStructSetVertexWithBuf(stmt, &objIn3[i], &labelInfo2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 结构化删Ifm.ConfigIf
    for (int i = 0; i < tableRecordCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, "Ifm.ConfigIf", GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchBindStmt(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testStructSetIndexKeyWithBuf(stmt, &objIn2[i], 0, NULL, &labelInfo3);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 最后再执行GmcBatchExecute， 出现1005000错误码
    uint32_t tryCnt = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 100) {
                AW_FUN_Log(LOG_STEP, "[WriteAndDelete] batch try more 100, ret is %d.", ret);
                break;
            }
            // 遇到锁，重新组装报文发送
            GmcBatchUnbindStmt(batch, stmt);
            ret = GmcBatchOptionInit(&batchOption);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            // 设置批写的数据的记录数
            ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            GmcBatchDestroy(batch);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            // 结构化删除Arp.ConfigArp数据
            for (int i = 0; i < tableRecordCount; i++) {
                ret = testGmcPrepareStmtByLabelName(stmt, "Arp.ConfigArp", GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchBindStmt(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testStructSetIndexKeyWithBuf(stmt, &objIn1[i], 0, NULL, &labelInfo1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            ret = testGmcPrepareStmtByLabelName(stmt, "Fib.ConfigNhpGroup", GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchBindStmt(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (int i = 0; i < tableRecordCount; i++) {
                ret = testStructSetVertexWithBuf(stmt, &objIn3[i], &labelInfo2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            // 结构化删Ifm.ConfigIf
            for (int i = 0; i < tableRecordCount; i++) {
                ret = testGmcPrepareStmtByLabelName(stmt, "Ifm.ConfigIf", GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchBindStmt(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testStructSetIndexKeyWithBuf(stmt, &objIn2[i], 0, NULL, &labelInfo3);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            ret = GmcBatchExecute(batch, &batchRet);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
    }
    // 插入的数据可能主键冲突, 可能映射到资源表，随机操作多个进程可能出现主键冲突
    if (ret == GMERR_UNIQUE_VIOLATION || ret == GMERR_RESOURCE_POOL_NOT_ENOUGH || ret == GMERR_OUT_OF_MEMORY ||
        ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL || ret == GMERR_PRIMARY_KEY_VIOLATION) {
        // 若批操作失败，执行下面的流程删除写入的表，避免出现表记录过多的情况
        // 主键删除写入的数据Arp.ConfigArp, Ifm.ConfigIf
        ConfigArpPKDelete(conn, stmt, "Arp.ConfigArp", startnum, startnum + tableRecordCount);
        ConfigIfPKDelete(conn, stmt, "Ifm.ConfigIf", startnum, startnum + tableRecordCount);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    free(objIn1);
    free(objIn2);
    free(objIn3);
    // 主键删除Fib.ConfigNhpGroup
    ConfigNhpGroupPKDelete(conn, stmt, "Fib.ConfigNhpGroup", startnum, startnum + tableRecordCount);
}
// 批量非结构化,含多个表以及insert和delete
void WriteAndDeleteNoSturct(GmcConnT *conn, GmcStmtT *stmt, int32_t startnum, uint32_t tableRecordCount)
{
    int ret = 0;
    int32_t upgradeVersionVal = -1;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    // Arp.ConfigArp Fib.ConfigNhpBasic Ifm.ConfigIfIpLearn Ifm.ConfigProtocolEnable
    ConfigArpT *objIn1 = (ConfigArpT *)malloc(sizeof(ConfigArpT) * tableRecordCount);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[WriteAndDeleteNoSturct]objIn1 malloc failed !!!");
        return;
    }
    memset(objIn1, 0, sizeof(ConfigArpT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {  // tableRecordCount
        objIn1[i].addr = startnum + i;
        objIn1[i].ifIndex = startnum;
        objIn1[i].type = 2;
        // 设置过期字段的值为1天
        objIn1[i].agingTime = 24 * 60 * 60 * 1000;
        objIn1[i].dtlReservedCount = 1;
    }
    ConfigIfIpLearnT *objIn2 = (ConfigIfIpLearnT *)malloc(sizeof(ConfigIfIpLearnT) * tableRecordCount);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[WriteAndDeleteNoSturct]objIn2 malloc failed !!!");
        free(objIn1);
        return;
    }
    memset(objIn2, 0, sizeof(ConfigIfIpLearnT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {
        objIn2[i].ifIndex = startnum + i;
        objIn2[i].dtlReservedCount = 1;
    }
    // 非结构化写Fib.ConfigNhpBasic表,200条数据
    ConfigNhpBasicT *objIn3 = (ConfigNhpBasicT *)malloc(sizeof(ConfigNhpBasicT) * tableRecordCount);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[WriteAndDeleteNoSturct]objIn3 malloc failed !!!");
        free(objIn1);
        free(objIn2);
        return;
    }
    memset(objIn3, 0, sizeof(ConfigNhpBasicT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {
        objIn3[i].nhpIndex = startnum + i;
        objIn3[i].dtlReservedCount = 1;
    }
    // 非结构化写Ifm.ConfigProtocolEnable表，200条数据
    ConfigProtocolEnableT *objIn4 = (ConfigProtocolEnableT *)malloc(sizeof(ConfigProtocolEnableT) * tableRecordCount);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "[WriteAndDeleteNoSturct]objIn4 malloc failed !!!");
        free(objIn1);
        free(objIn2);
        free(objIn3);
        return;
    }
    memset(objIn4, 0, sizeof(ConfigProtocolEnableT) * tableRecordCount);
    for (int i = 0; i < tableRecordCount; i++) {
        objIn4[i].ifIndex = startnum + i;
        objIn4[i].dtlReservedCount = 1;
    }
    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置批写的数据的记录数为4k
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < tableRecordCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, "Arp.ConfigArp", GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &objIn1[i].addr, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &objIn1[i].ifIndex, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, "Fib.ConfigNhpBasic", GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < tableRecordCount; i++) {
        ret = GmcSetVertexProperty(stmt, "nhpIndex", GMC_DATATYPE_INT32, &objIn3[i].nhpIndex, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &objIn3[i].vrfId, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "originNhp", GMC_DATATYPE_INT32, &objIn3[i].originNhp, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "iidFlags", GMC_DATATYPE_INT32, &objIn3[i].iidFlags, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &objIn3[i].nsId, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &objIn3[i].dtlReservedCount, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 非结构化删Ifm.ConfigIfIpLearn
    for (int i = 0; i < tableRecordCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, "Ifm.ConfigIfIpLearn", GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &objIn2[i].ifIndex, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, "Ifm.ConfigProtocolEnable", GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < tableRecordCount; i++) {
        ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &objIn4[i].ifIndex, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "proto", GMC_DATATYPE_INT8, &objIn4[i].proto, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "enable", GMC_DATATYPE_INT8, &objIn4[i].enable, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &objIn4[i].dtlReservedCount, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 最后再执行GmcBatchExecute， 出现1005000错误码
    uint32_t tryCnt = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 100) {
                AW_FUN_Log(LOG_STEP, "[ WriteAndDeleteNoSturct] batch try more 100, ret is %d.", ret);
                break;
            }
            // 遇到锁需要重新组装报文，进行发送
            ret = GmcBatchOptionInit(&batchOption);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            // 设置批写的数据的记录数为4k
            ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            GmcBatchDestroy(batch);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (int i = 0; i < tableRecordCount; i++) {
                ret = testGmcPrepareStmtByLabelName(stmt, "Arp.ConfigArp", GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &objIn1[i].addr, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &objIn1[i].ifIndex, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            ret = testGmcPrepareStmtByLabelName(stmt, "Fib.ConfigNhpBasic", GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (int i = 0; i < tableRecordCount; i++) {
                ret = GmcSetVertexProperty(stmt, "nhpIndex", GMC_DATATYPE_INT32, &objIn3[i].nhpIndex, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "vrfId", GMC_DATATYPE_INT32, &objIn3[i].vrfId, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret =
                    GmcSetVertexProperty(stmt, "originNhp", GMC_DATATYPE_INT32, &objIn3[i].originNhp, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "iidFlags", GMC_DATATYPE_INT32, &objIn3[i].iidFlags, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "nsId", GMC_DATATYPE_INT32, &objIn3[i].nsId, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(
                    stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &objIn3[i].dtlReservedCount, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            // 非结构化删Ifm.ConfigIfIpLearn
            for (int i = 0; i < tableRecordCount; i++) {
                ret = testGmcPrepareStmtByLabelName(stmt, "Ifm.ConfigIfIpLearn", GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &objIn2[i].ifIndex, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            ret = testGmcPrepareStmtByLabelName(stmt, "Ifm.ConfigProtocolEnable", GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            for (int i = 0; i < tableRecordCount; i++) {
                ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &objIn4[i].ifIndex, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "proto", GMC_DATATYPE_INT8, &objIn4[i].proto, sizeof(int8_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "enable", GMC_DATATYPE_INT8, &objIn4[i].enable, sizeof(int8_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetVertexProperty(
                    stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &objIn4[i].dtlReservedCount, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            ret = GmcBatchExecute(batch, &batchRet);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL);
    }
    // 插入的数据可能主键冲突, 可能映射到资源表, Fib.ConfigNhpBasic表出现主键冲突
    if (ret == GMERR_UNIQUE_VIOLATION || ret == GMERR_RESOURCE_POOL_NOT_ENOUGH || ret == GMERR_OUT_OF_MEMORY ||
        ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_SUB_PUSH_QUEUE_FULL || ret == GMERR_PRIMARY_KEY_VIOLATION) {
        // 删除原先插入的数据
        // 避免导致之前写入的数据删不掉，导致内部数据过多
        ConfigArpPKDelete(conn, stmt, "Arp.ConfigArp", startnum, startnum + tableRecordCount);
        ConfigIfIpLearnPKDelete(conn, stmt, "Ifm.ConfigIfIpLearn", startnum, startnum + tableRecordCount);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    free(objIn1);
    free(objIn2);
    free(objIn3);
    free(objIn4);
    // 主键删除Fib.ConfigNhpBasic
    ConfigNhpBasicPKDelete(conn, stmt, "Fib.ConfigNhpBasic", startnum, startnum + tableRecordCount);
    // 主键删除Ifm.ConfigProtocolEnable
    ConfigProtocolEnablePKDelete(conn, stmt, "Ifm.ConfigProtocolEnable", startnum, startnum + tableRecordCount);
}

/*-----------------------------------------------lpm索引相关表-------------------------------------------------------*/
// 主键删除lp4索引表的数据
void InputLpm4PKDelete(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t idValue = i;
        int32_t upgradeVersionVal = -1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &idValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t tryCnt = 0;
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(10000);
                tryCnt++;
                if (tryCnt > 100) {
                    AW_FUN_Log(LOG_STEP, "[InputLpm4PKDelete] delete try more 100, ret is %d.", ret);
                    break;
                }
                ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyId(stmt, 0);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &idValue, sizeof(int32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcExecute(stmt);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        }
        if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}
// 利用lpm索引进行扫描
void InputLpm4Index1Scan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    int32_t vrIdValue = 0;
    int32_t vrfIndexValue = 0;
    int8_t maskLenValue = 32;
    for (int i = startnum; i < endnum; i++) {
        int32_t destIpAddrValue = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &vrIdValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &vrfIndexValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &destIpAddrValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT8, &maskLenValue, sizeof(int8_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (true) {
                ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (ret != GMERR_OK || isFinish) {
                    break;
                }
                cnt++;
            }
        }
    }
}
/*---------------------------------------------------------订阅和取消订阅--------------------------------------------------*/
int snCallbackNoCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
    return GMERR_OK;
}
// notify表回调
void snYlogNotifyCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    // 用户消息创建
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 用户处理错误信息回填
    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
    ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 用户信息发送
    ret = GmcSendResp(subStmt, response);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 用户信息销毁
    ret = GmcDestroyResp(subStmt, response);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = snCallbackNoCheck(subStmt, info, userData);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// 外部表回调
void snYlogExternalCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    int ret = snCallbackNoCheck(subStmt, info, userData);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// ylog中notify表
void testSubscribeYlogNotifyTable(GmcStmtT *stmt, GmcConnT *conn, const char *subName01, const char *subName02,
    const char *subName03, SnUserDataT *userData01, SnUserDataT *userData02, SnUserDataT *userData03)
{
    int ret = 0;
    uint32_t tryCnt = 0;
    // Arp.AgingDetectMessage，notify表，由于soho连接数限制，仅在euler上跑
#if defined RUN_INDEPENDENT
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[2].userEpollFd;
    ret = TestYangGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt1, g_testNameSpace);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建一个新的订阅连接
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    connOptions.connName = "subMacAgingTime";
    ret = TestYangGmcConnect(&conn_sub, &stmt_sub, GMC_CONN_TYPE_SUB, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *subInfo01 = NULL;
    readJanssonFile("./datalogFile/notify01.json", &subInfo01);
    AW_MACRO_EXPECT_NOTNULL(subInfo01);
    GmcSubConfigT tmpSubInfo01;
    tmpSubInfo01.subsName = subName01;
    tmpSubInfo01.configJson = subInfo01;
    ret = GmcSubscribe(stmt1, &tmpSubInfo01, conn_sub, snYlogNotifyCallback, userData01);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP,
                    "[testSubscribeYlogNotifyTable] Subscribe Arp.AgingDetectMessage more 1000,"
                    "ret is %d",
                    ret);
                break;
            }
            ret = GmcSubscribe(stmt1, &tmpSubInfo01, conn_sub, snYlogNotifyCallback, userData01);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    // 之前创建的订阅关系未取消成功
    if (ret != GMERR_DUPLICATE_OBJECT) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    free(subInfo01);
#endif

    char *subInfo02 = NULL;
    readJanssonFile("./datalogFile/notify02.json", &subInfo02);
    AW_MACRO_EXPECT_NOTNULL(subInfo02);
    GmcSubConfigT tmpSubInfo02;
    tmpSubInfo02.subsName = subName02;
    tmpSubInfo02.configJson = subInfo02;
    ret = GmcSubscribe(stmt, &tmpSubInfo02, conn, snYlogNotifyCallback, userData02);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP,
                    "[testSubscribeYlogNotifyTable] Subscribe LswOda.PortAttrChg more 1000,"
                    "ret is %d.",
                    ret);
                break;
            }
            ret = GmcSubscribe(stmt, &tmpSubInfo02, conn, snYlogNotifyCallback, userData02);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    // 之前创建的订阅关系未取消成功
    if (ret != GMERR_DUPLICATE_OBJECT) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    free(subInfo02);

    char *subInfo03 = NULL;
    readJanssonFile("./datalogFile/notify03.json", &subInfo03);
    AW_MACRO_EXPECT_NOTNULL(subInfo03);
    GmcSubConfigT tmpSubInfo03;
    tmpSubInfo03.subsName = subName03;
    tmpSubInfo03.configJson = subInfo03;
    ret = GmcSubscribe(stmt, &tmpSubInfo03, conn, snYlogNotifyCallback, userData03);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP,
                    "[testSubscribeYlogNotifyTable] Subscribe LswOda.EthTrunk more 1000,"
                    "ret is %d.",
                    ret);
                break;
            }
            ret = GmcSubscribe(stmt, &tmpSubInfo03, conn, snYlogNotifyCallback, userData03);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    // 之前创建的订阅关系未取消成功
    if (ret != GMERR_DUPLICATE_OBJECT) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    free(subInfo03);
}
void testCancelSubYlogNotifyTable(GmcStmtT *stmt, const char *subName01, const char *subName02, const char *subName03)
{
    int ret = GmcUnSubscribe(stmt, subName01);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName02);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName03);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// ylog中外部表
void testSubscribeYlogExternalTable(GmcStmtT *stmt, GmcConnT *conn, const char *subName05, const char *subName06,
    const char *subName07, const char *subName08, SnUserDataT *userData05, SnUserDataT *userData06,
    SnUserDataT *userData07, SnUserDataT *userData08)
{
    char *subInfo01 = NULL;
    readJanssonFile("./datalogFile/external01.json", &subInfo01);
    AW_MACRO_EXPECT_NOTNULL(subInfo01);
    uint32_t tryCnt = 0;
    GmcSubConfigT tmpSubInfo01;
    tmpSubInfo01.subsName = subName05;
    tmpSubInfo01.configJson = subInfo01;
    int ret = GmcSubscribe(stmt, &tmpSubInfo01, conn, snYlogExternalCallback, userData05);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP, "[testSubscribeYlogExternalTable] Subscribe arp more 1000, ret is %d.", ret);
                break;
            }
            ret = GmcSubscribe(stmt, &tmpSubInfo01, conn, snYlogExternalCallback, userData05);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subInfo01);

    char *subInfo02 = NULL;
    readJanssonFile("./datalogFile/external02.json", &subInfo02);
    AW_MACRO_EXPECT_NOTNULL(subInfo02);
    GmcSubConfigT tmpSubInfo02;
    tmpSubInfo02.subsName = subName06;
    tmpSubInfo02.configJson = subInfo02;
    ret = GmcSubscribe(stmt, &tmpSubInfo02, conn, snYlogExternalCallback, userData06);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP, "[testSubscribeYlogExternalTable] Subscribe if_link more 1000, ret is %d.", ret);
                break;
            }
            ret = GmcSubscribe(stmt, &tmpSubInfo02, conn, snYlogExternalCallback, userData06);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subInfo02);

    char *subInfo03 = NULL;
    readJanssonFile("./datalogFile/external03.json", &subInfo03);
    AW_MACRO_EXPECT_NOTNULL(subInfo03);
    GmcSubConfigT tmpSubInfo03;
    tmpSubInfo03.subsName = subName07;
    tmpSubInfo03.configJson = subInfo03;
    ret = GmcSubscribe(stmt, &tmpSubInfo03, conn, snYlogExternalCallback, userData07);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP, "[testSubscribeYlogExternalTable] Subscribe if_route more 1000, ret is %d.", ret);
                break;
            }
            ret = GmcSubscribe(stmt, &tmpSubInfo03, conn, snYlogExternalCallback, userData07);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subInfo03);

    char *subInfo04 = NULL;
    readJanssonFile("./datalogFile/external04.json", &subInfo04);
    AW_MACRO_EXPECT_NOTNULL(subInfo04);
    GmcSubConfigT tmpSubInfo04;
    tmpSubInfo04.subsName = subName08;
    tmpSubInfo04.configJson = subInfo04;
    ret = GmcSubscribe(stmt, &tmpSubInfo04, conn, snYlogExternalCallback, userData08);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP,
                    "[testSubscribeYlogExternalTable] Subscribe if_eth_trunk_member more 1000, "
                    "ret is %d.",
                    ret);
                break;
            }
            ret = GmcSubscribe(stmt, &tmpSubInfo04, conn, snYlogExternalCallback, userData08);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subInfo04);
}
void testCancelSubYlogExternalTable(
    GmcStmtT *stmt, const char *subName01, const char *subName02, const char *subName03, const char *subName04)
{
    uint32_t tryCnt = 0;
    int ret = GmcUnSubscribe(stmt, subName01);
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP, "[testCancelSubYlogExternalTable] UnSubscribe arp more 1000, ret is %d.", ret);
                break;
            }
            ret = GmcUnSubscribe(stmt, subName01);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName02);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP, "[testCancelSubYlogExternalTable] UnSubscribe if_link more 1000, ret is %d.", ret);
                break;
            }
            ret = GmcUnSubscribe(stmt, subName02);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName03);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP,
                    "[testCancelSubYlogExternalTable] UnSubscribe if_route more 1000,"
                    "ret is %d.",
                    ret);
                break;
            }
            ret = GmcUnSubscribe(stmt, subName03);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName04);
    tryCnt = 0;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        do {
            usleep(10000);
            tryCnt++;
            if (tryCnt > 1000) {
                AW_FUN_Log(LOG_STEP,
                    "[testCancelSubYlogExternalTable] UnSubscribe if_eth_trunk_member more 1000,"
                    "ret is %d.",
                    ret);
                break;
            }
            ret = GmcUnSubscribe(stmt, subName04);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    }
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

/*-------------------------------------------------------扫描表-----------------------------------------------------*/
// ConfigArp全表扫描
void ConfigArpFullScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// ConfigArp主键扫描
void ConfigArpPKScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t addrvalue, int32_t ifIndexvalue)
{
    int ret = 0;
    int32_t upgradeVersionVal = -1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &addrvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// ConfigArp二级索引扫描
void ConfigArpIndex1Scan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t ifIndexvalue)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// ConfigIfIpv4Addr全表扫描
void ConfigIfIpv4AddrFullScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// ConfigIfIpv4Addr主键扫描
void ConfigIfIpv4AddrPKScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t ifIndexvalue,
    int32_t vrfIndexvalue, int32_t addressvalue, int16_t maskLenvalue, int16_t typevalue)
{
    int ret = 0;
    int32_t upgradeVersionVal = -1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &vrfIndexvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &addressvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT16, &maskLenvalue, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT16, &typevalue, sizeof(int16_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// ConfigIfIpv4Addr二级索引扫描
void ConfigIfIpv4AddrIndex1Scan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t ifIndexvalue)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// ConfigAttributes全表扫描
void ConfigAttributesFullScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// ConfigAttributes主键扫描
void ConfigAttributesPKScan(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t nsIdvalue, int32_t ifIndexvalue)
{
    int ret = 0;
    int32_t upgradeVersionVal = -1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(stmt, &upgradeVersionVal);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionVal, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &nsIdvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &ifIndexvalue, sizeof(int32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// 外部表------
// if_eth_trunk_member全表扫描
void ifEthTrunkMemberFullScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// if_eth_trunk_member主键扫描
void ifEthTrunkMemberPKScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = startnum; i < endnum; i++) {
        uint32_t memberIfIndex = (uint32_t)(i);
        ret = GmcSetIndexKeyId(stmt, 0);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &memberIfIndex, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;

        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (true) {
                ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (ret != GMERR_OK || isFinish) {
                    break;
                }
                cnt++;
            }
        }
    }
}
// if_link全表扫描
void iflinkFullScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// if_link主键扫描
void iflinkPKScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t ifindexvalue)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ifindexvalue, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// if_route全表扫描
void ifrouteFullScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
// if_route主键扫描,按照schemajson定义的类型来查询
void ifroutePKScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t ifindexvalue)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ifindexvalue, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;

    ret = GmcExecute(stmt);
    if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            TEST_EXPECT_INT32(GMERR_OK, ret);
            if (ret != GMERR_OK || isFinish) {
                break;
            }
            cnt++;
        }
    }
}
/*----------------------------------------------------------查询KV表-------------------------------------------*/
void capV5KVTableScan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    ret = GmcKvPrepareStmtByLabelName(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t expectValue = 100;
    int32_t getValue = 0;
    uint32_t getValueLen = sizeof(int32_t);
    char expectValue1[32] = "aaaaaa";
    char getValue1[32] = {0};
    uint32_t getValueLen1 = strlen(expectValue1) + 1;
    char key[32] = {0};
    (void)snprintf(key, 32, "para1");
    ret = GmcKvGet(stmt, key, strlen(key) + 1, &getValue, &getValueLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(expectValue, getValue);
    // 查询para2的值
    (void)snprintf(key, 32, "para2");
    ret = GmcKvGet(stmt, key, strlen(key) + 1, getValue1, &getValueLen1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = strncmp(getValue1, expectValue1, strlen(expectValue1));
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

/*------------------------------------------------------含变长字段二级索引扫描---------------------------------------*/
void InputTableCIndex1Scan(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int32_t startnum, int32_t endnum)
{
    int ret = 0;
    for (int i = startnum; i < endnum; i++) {
        int32_t bValue = i;
        uint8_t cstrValue[10] = {0};
        (void)snprintf((char *)cstrValue, 10, "s%08d", i);
        int32_t destIpAddrValue = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &bValue, sizeof(int32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_STRING, cstrValue, strlen((char *)cstrValue));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (true) {
                ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
                TEST_EXPECT_INT32(GMERR_OK, ret);
                if (ret != GMERR_OK || isFinish) {
                    break;
                }
                cnt++;
            }
        }
    }
}
/*----------------------------------对初始导入ylog的数据进行校验-------------------------------------------------*/
// 获取文件中数据，存到buffer中
int TestViewData(char *cmd, char **result)
{
    int ret = 0;
    FILE *fd = popen(cmd, "r");
    int size = 10240;
    *result = (char *)malloc(sizeof(char) * size);
    memset(*result, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat((char *)*result, buf);
    }

    ret = pclose(fd);
    return ret;
}
// 校验数据
int ScanTableData(const char *expectOutput, const char *tableName, const char *tableNameSpace = g_testNameSpace)
{
    // 设备上，文件没权限
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s > test.txt", g_toolPath, tableName, tableNameSpace);
    int ret = system(command);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(command, 0, sizeof(command));
    char *actualResult = NULL;
    (void)snprintf(command, MAX_CMD_SIZE, "cat test.txt");
    ret = TestViewData(command, &actualResult);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 预期值属于实际值的一部分
    const char *result = strstr(actualResult, expectOutput);
    if (result != NULL) {
        EXPECT_STRNE(NULL, result);
        free(actualResult);
    } else {
        free(actualResult);
        AW_FUN_Log(LOG_STEP, "There is no data what you need!");
        return -1;
    }
    return ret;
}
// 字段值超过120个字符的数据校验
int ScanTableLongFieldData(const char *expectfilename, const char *tableName,
    const char *tableNameSpace = g_testNameSpace)
{
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s > test.txt", g_toolPath, tableName, tableNameSpace);
    int ret = system(command);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(command, 0, sizeof(command));
    (void)snprintf(command, MAX_CMD_SIZE, "cat ./datalogFile/%s.txt", expectfilename);
    char *expectResult = NULL;
    ret = TestViewData(command, &expectResult);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    char *actualResult = NULL;
    (void)snprintf(command, MAX_CMD_SIZE, "cat test.txt");
    ret = TestViewData(command, &actualResult);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    int res = strcmp(actualResult, expectResult);
    if (res != 0) {
        free(actualResult);
        free(expectResult);
        AW_FUN_Log(LOG_STEP, "There is no data what you need!");
        return -1;
    }
    free(actualResult);
    free(expectResult);
    return ret;
}
// 获取热补丁PATCH_STATE状态
int TestGetPatchStateStr(char *value, int len, char *soName)
{
    char command[1024] = {0};
    int ret = snprintf(command, sizeof(command), "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep PATCH_STATE", g_toolPath,
        g_patchViewName, g_connServer, soName);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, NULL, value, len);
    if (ret) {
        return FAILED;
    }
    return 0;
}
// 校验热补丁视图状态
int CheckPatchState(char *soName)
{
    int ret = 0;
    bool flag = false;
    char patchState[128] = {0};
    for (int i = 0; i < 1200; i++) { // 20分钟
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 重做成功
        if (strstr(patchState, "SUCCESS") != NULL) {
            ret = 0;
            flag = true;
            break;
        }
        // 重做失败
        if (strstr(patchState, "REDO_FAIL_ROLL_BACK_SUC") != NULL ||
            strstr(patchState, "REDO_FAIL_ROLL_BACK_FAIL") != NULL) {
            ret = 1;
            flag = true;
            break;
        }
        // 由于前面补丁重做失败，回滚成功；继续加载补丁，会失败
        if (strstr(patchState, "LOAD_FAIL_ROLL_BACK_SUC") != NULL) {
            ret = 2;
            flag = true;
            break;
        }
        sleep(1);
    }
    // 20分钟没重做完，打印热补丁视图状态
    if (!flag) {
        AW_FUN_Log(LOG_STEP, "PTL_DATALOG_PATCH_INFO!!!");
        system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO | grep PATCH_STATE -B 10");
    }
    return ret;
}

// 加载热补丁升级so并重试
int GmimportUpgradeSo(char *patchSoName, const char *nsName = NULL, char *soName = NULL)
{
    int ret = 0;
    bool flag = false;
    for (int i = 0; i < LOAD_HOTPATCH_MAX_SIZE; i++) {
        ret = TestLoadUpgradeSo(patchSoName, nsName, false);
        if (ret == GMERR_OK) {
            flag = true;
            break;
        }
        sleep(1);
    }
    if (!flag) {
        AW_FUN_Log(LOG_STEP, "try more LOAD_HOTPATCH_MAX_SIZE failed !!!!!!\n");
    }
    // 查看热补丁视图，等待其重做完成
    ret = CheckPatchState(soName);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "upgrade failed patchSoName is %s\n", patchSoName);
    }
    return ret;
}

// 加载热补丁降级so并重试
int GmimportRollbackSo(char *rollbackSoName, const char *nsName = NULL, char *soName = NULL)
{
    int ret = 0;
    bool flag = false;
    for (int i = 0; i < LOAD_HOTPATCH_MAX_SIZE; i++) {
        ret = TestLoadRollbackSo(rollbackSoName, nsName, false);
        if (ret == GMERR_OK) {
            flag = true;
            break;
        }
        sleep(1);
    }
    if (!flag) {
        AW_FUN_Log(LOG_STEP, "try more LOAD_HOTPATCH_MAX_SIZE failed !!!!!!\n");
    }
    // 查看热补丁视图，等待其重做完成
    ret = CheckPatchState(soName);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "rollback failed rollbackSoName is %s\n", rollbackSoName);
    }
    return ret;
}

// 预期表的记录值
const char *g_expectabstractaifnet = R"(
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 0,
    "vrf_id": 0,
    "df_flag": 0,
    "mtu_v4": 1500,
    "ipv4_state": 1
}
index = 1, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 19,
    "vrf_id": 0,
    "df_flag": 0,
    "mtu_v4": 1500,
    "ipv4_state": 0
}
index = 2, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 1,
    "vrf_id": 0,
    "df_flag": 0,
    "mtu_v4": 1500,
    "ipv4_state": 1
}
index = 3, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 9000,
    "vrf_id": 0,
    "df_flag": 0,
    "mtu_v4": 1500,
    "ipv4_state": 0
}
index = 4, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 6023,
    "vrf_id": 0,
    "df_flag": 0,
    "mtu_v4": 1500,
    "ipv4_state": 1
}
index = 5, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 18,
    "vrf_id": 0,
    "df_flag": 0,
    "mtu_v4": 1500,
    "ipv4_state": 0
}
index = 6, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 10001,
    "vrf_id": 0,
    "df_flag": 0,
    "mtu_v4": 1500,
    "ipv4_state": 1
}
index = 7, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 20,
    "vrf_id": 0,
    "df_flag": 0,
    "mtu_v4": 1500,
    "ipv4_state": 0
})";

const char *g_expectabstractaifphy = R"(
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 0,
    "if_bw": 0,
    "phy_state": 1,
    "admin_state": 1,
    "onboard_state": 0,
    "tb": 0,
    "tp": 0,
    "chip_unit": 0,
    "oda_phy_port": 0,
    "phy_mac": "0x000000000000",
    "eth_autoneg": 1,
    "eth_speed": 0,
    "eth_duplex": 0,
    "eth_combo": 0,
    "eth_loopback": 0,
    "dev_id": 0,
    "chassis_id": 0,
    "slot_id": 0,
    "card_id": 0,
    "unit_id": 0,
    "port_id": 0,
    "service_type": 0,
    "autoneg_cap": 0,
    "autoneg_adv_cap": 0,
    "medium": 0,
    "mru": 0,
    "link_type": 0,
    "eth_class": 0,
    "cpu_type": 0,
    "drvPortType": 0,
    "drvSubType": 0
}
index = 1, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 1,
    "if_bw": 0,
    "phy_state": 1,
    "admin_state": 1,
    "onboard_state": 0,
    "tb": 0,
    "tp": 0,
    "chip_unit": 0,
    "oda_phy_port": 0,
    "phy_mac": "0x000000000000",
    "eth_autoneg": 1,
    "eth_speed": 0,
    "eth_duplex": 0,
    "eth_combo": 0,
    "eth_loopback": 0,
    "dev_id": 0,
    "chassis_id": 0,
    "slot_id": 0,
    "card_id": 0,
    "unit_id": 0,
    "port_id": 0,
    "service_type": 0,
    "autoneg_cap": 0,
    "autoneg_adv_cap": 0,
    "medium": 0,
    "mru": 0,
    "link_type": 0,
    "eth_class": 0,
    "cpu_type": 0,
    "drvPortType": 0,
    "drvSubType": 0
}
index = 2, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 10001,
    "if_bw": 0,
    "phy_state": 1,
    "admin_state": 1,
    "onboard_state": 0,
    "tb": 0,
    "tp": 0,
    "chip_unit": 0,
    "oda_phy_port": 0,
    "phy_mac": "0x00c0a8647100",
    "eth_autoneg": 1,
    "eth_speed": 0,
    "eth_duplex": 0,
    "eth_combo": 0,
    "eth_loopback": 0,
    "dev_id": 0,
    "chassis_id": 0,
    "slot_id": 0,
    "card_id": 0,
    "unit_id": 0,
    "port_id": 0,
    "service_type": 0,
    "autoneg_cap": 0,
    "autoneg_adv_cap": 0,
    "medium": 0,
    "mru": 0,
    "link_type": 0,
    "eth_class": 0,
    "cpu_type": 0,
    "drvPortType": 0,
    "drvSubType": 0
}
index = 3, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 9000,
    "if_bw": 0,
    "phy_state": 1,
    "admin_state": 1,
    "onboard_state": 0,
    "tb": 0,
    "tp": 0,
    "chip_unit": 0,
    "oda_phy_port": 0,
    "phy_mac": "0x000000000000",
    "eth_autoneg": 1,
    "eth_speed": 0,
    "eth_duplex": 0,
    "eth_combo": 0,
    "eth_loopback": 0,
    "dev_id": 0,
    "chassis_id": 0,
    "slot_id": 0,
    "card_id": 0,
    "unit_id": 0,
    "port_id": 0,
    "service_type": 0,
    "autoneg_cap": 0,
    "autoneg_adv_cap": 0,
    "medium": 0,
    "mru": 0,
    "link_type": 0,
    "eth_class": 0,
    "cpu_type": 0,
    "drvPortType": 0,
    "drvSubType": 0
}
index = 4, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 6023,
    "if_bw": 0,
    "phy_state": 1,
    "admin_state": 1,
    "onboard_state": 0,
    "tb": 0,
    "tp": 0,
    "chip_unit": 0,
    "oda_phy_port": 0,
    "phy_mac": "0x000000000000",
    "eth_autoneg": 1,
    "eth_speed": 0,
    "eth_duplex": 0,
    "eth_combo": 0,
    "eth_loopback": 0,
    "dev_id": 0,
    "chassis_id": 0,
    "slot_id": 0,
    "card_id": 0,
    "unit_id": 0,
    "port_id": 0,
    "service_type": 0,
    "autoneg_cap": 0,
    "autoneg_adv_cap": 0,
    "medium": 0,
    "mru": 0,
    "link_type": 0,
    "eth_class": 0,
    "cpu_type": 0,
    "drvPortType": 0,
    "drvSubType": 0
}
index = 5, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 18,
    "if_bw": 0,
    "phy_state": 1,
    "admin_state": 1,
    "onboard_state": 0,
    "tb": 0,
    "tp": 0,
    "chip_unit": 0,
    "oda_phy_port": 0,
    "phy_mac": "0x00c0a8647100",
    "eth_autoneg": 1,
    "eth_speed": 0,
    "eth_duplex": 0,
    "eth_combo": 0,
    "eth_loopback": 0,
    "dev_id": 0,
    "chassis_id": 0,
    "slot_id": 0,
    "card_id": 0,
    "unit_id": 0,
    "port_id": 0,
    "service_type": 0,
    "autoneg_cap": 0,
    "autoneg_adv_cap": 0,
    "medium": 0,
    "mru": 0,
    "link_type": 0,
    "eth_class": 0,
    "cpu_type": 0,
    "drvPortType": 1,
    "drvSubType": 0
}
index = 6, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 19,
    "if_bw": 0,
    "phy_state": 1,
    "admin_state": 1,
    "onboard_state": 0,
    "tb": 0,
    "tp": 1,
    "chip_unit": 0,
    "oda_phy_port": 0,
    "phy_mac": "0x00c0a8647100",
    "eth_autoneg": 1,
    "eth_speed": 0,
    "eth_duplex": 0,
    "eth_combo": 0,
    "eth_loopback": 0,
    "dev_id": 0,
    "chassis_id": 0,
    "slot_id": 0,
    "card_id": 0,
    "unit_id": 0,
    "port_id": 0,
    "service_type": 0,
    "autoneg_cap": 0,
    "autoneg_adv_cap": 0,
    "medium": 0,
    "mru": 0,
    "link_type": 0,
    "eth_class": 0,
    "cpu_type": 0,
    "drvPortType": 1,
    "drvSubType": 0
}
index = 7, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 20,
    "if_bw": 0,
    "phy_state": 1,
    "admin_state": 1,
    "onboard_state": 0,
    "tb": 0,
    "tp": 2,
    "chip_unit": 0,
    "oda_phy_port": 0,
    "phy_mac": "0x00c0a8647100",
    "eth_autoneg": 1,
    "eth_speed": 0,
    "eth_duplex": 0,
    "eth_combo": 0,
    "eth_loopback": 0,
    "dev_id": 0,
    "chassis_id": 0,
    "slot_id": 0,
    "card_id": 0,
    "unit_id": 0,
    "port_id": 1,
    "service_type": 0,
    "autoneg_cap": 0,
    "autoneg_adv_cap": 0,
    "medium": 0,
    "mru": 0,
    "link_type": 0,
    "eth_class": 0,
    "cpu_type": 0,
    "drvPortType": 1,
    "drvSubType": 0
})";
// Abstract.a_if_link
const char *g_expectabstractaiflink = R"(
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 0,
    "link_type": 0,
    "link_state": 1,
    "oper_state": 1
}
index = 1, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 1,
    "link_type": 0,
    "link_state": 1,
    "oper_state": 1
}
index = 2, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 20,
    "link_type": 0,
    "link_state": 1,
    "oper_state": 1
}
index = 3, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 9000,
    "link_type": 0,
    "link_state": 1,
    "oper_state": 1
}
index = 4, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 6023,
    "link_type": 0,
    "link_state": 1,
    "oper_state": 1
}
index = 5, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 18,
    "link_type": 0,
    "link_state": 1,
    "oper_state": 1
}
index = 6, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 10001,
    "link_type": 0,
    "link_state": 1,
    "oper_state": 1
}
index = 7, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 19,
    "link_type": 0,
    "link_state": 1,
    "oper_state": 1
})";
// Abstract.a_if_net6
const char *g_expectabstractaifnet6 = R"(
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 0,
    "vrf_id": 0,
    "mtu_v6": 1500,
    "ipv6_state": 1
}
index = 1, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 1,
    "vrf_id": 0,
    "mtu_v6": 1500,
    "ipv6_state": 1
}
index = 2, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 18,
    "vrf_id": 0,
    "mtu_v6": 1500,
    "ipv6_state": 1
}
index = 3, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 19,
    "vrf_id": 0,
    "mtu_v6": 1500,
    "ipv6_state": 1
}
index = 4, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 20,
    "vrf_id": 0,
    "mtu_v6": 1500,
    "ipv6_state": 1
}
index = 5, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 10001,
    "vrf_id": 0,
    "mtu_v6": 1500,
    "ipv6_state": 1
}
index = 6, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 6023,
    "vrf_id": 0,
    "mtu_v6": 1500,
    "ipv6_state": 1
}
index = 7, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 9000,
    "vrf_id": 0,
    "mtu_v6": 1500,
    "ipv6_state": 1
})";
// Abstract.a_nhp4
const char *g_expectabstractanhp4 = R"(
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "nhp_index": 1,
    "next_hop": 2130706433,
    "out_if_index": 0,
    "vrf_id": 0,
    "iid_flags": 0,
    "res_index": 0
}
index = 1, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "nhp_index": 5,
    "next_hop": 2130706433,
    "out_if_index": 0,
    "vrf_id": 0,
    "iid_flags": 0,
    "res_index": 1
}
index = 2, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "nhp_index": 6,
    "next_hop": -1062731407,
    "out_if_index": 10001,
    "vrf_id": 0,
    "iid_flags": 0,
    "res_index": 2
}
index = 3, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "nhp_index": 4,
    "next_hop": 2130706433,
    "out_if_index": 0,
    "vrf_id": 0,
    "iid_flags": 0,
    "res_index": 3
})";
// Abstract.a_config_if_ipv4_addr
const char *g_expectabstractaconfigifipv4addr = R"(
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 0,
    "vrf_index": 0,
    "address": 2130706433,
    "mask_len": 8,
    "type": 1
}
index = 1, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 6023,
    "vrf_index": 0,
    "address": -1062666498,
    "mask_len": 32,
    "type": 1
}
index = 2, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 10001,
    "vrf_index": 0,
    "address": -1062703103,
    "mask_len": 24,
    "type": 1
}
index = 3, check_version = 0
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "if_index": 10001,
    "vrf_index": 0,
    "address": -1062731407,
    "mask_len": 24,
    "type": 1
})";
// Abstract.a_arp
const char *g_expectabstractaarp01 = R"(
{
    "vrf_id": 0,
    "ip_addr": -1067162623,
    "if_index": 20,
    "l3if_index": 10001,
    "type": 1,
    "mac": "0xb8e3b158993a",
    "fake_flag": 0,
    "vlan_id": 0,
    "detect_count": 0,
)";
const char *g_expectabstractaarp02 = R"(
    "dtlReservedCount": 1
}
index = 1, check_version = 0
{
    "vrf_id": 0,
    "ip_addr": -1067162523,
    "if_index": 20,
    "l3if_index": 10001,
    "type": 1,
    "mac": "0x0826ae3748b4",
    "fake_flag": 0,
    "vlan_id": 0,
    "detect_count": 0,
)";
// Abstract.a_vap_if
const char *g_expectabstractavapif = R"()";

// Abstract.Tbl_Bridge
const char *g_expectabstracttblbridge = R"(
{
    "dtlReservedCount": 1,
    "upgradeVersion": 0,
    "ns_id": 0,
    "br_id": 0,
    "br_name": "0x62725f6c737700000000000000000000",
    "vlan_filter": 1,
    "mac_age_time": 300,
    "mac_thrd_up": 80,
    "mac_thrd_down": 70,
    "mac_pri1_allow_flapping": 0,
    "mac_pri2_allow_flapping": 0,
    "mac_pri3_allow_flapping": 0,
    "mac_pri4_allow_flapping": 0,
    "br_if_index": -1,
    "macLimitNum": 2000
})";
// 获取导入预置数据后，表的记录数
int TestGetTableCount(int *value, const char *labelname)
{
    char command[1024] = {0};
    int ret = snprintf(command, MAX_CMD_SIZE,
        "gmsysview -q V\\$STORAGE_VERTEX_COUNT -f table=%s |"
        "grep count | awk -F ': ' '{print $2}' | sed 's/ //g'",
        labelname);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

#endif // FEATURE_DATALOG

#endif // DATALOGSUBFUC_H
