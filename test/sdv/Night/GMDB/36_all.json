{"ietf-netconf:config": {"huawei-aaa:aaa": {"lam": {"users": {"user": [{"name": "root123", "group-name": "admin", "password": "$6$EN7M$zqgmgKhl0th.IC4ji4moWYKnKjiHwCGDELc06/OvX8vHD958zCgasNFTbo.R6Kklw3K4JdFdC0RqoxQ/wW4ND.", "service-terminal": true, "service-api": true, "password-force-change": false}, {"name": "admin", "group-name": "admin", "password": "$6$JwJQUhSYr$c51ldenTNva3J74RyJmrQhGHg2F3Z8R3wT9Nmru.7Zflu.MCQASBcToRL/rp5TLP0csvR5LePwgBO0YKAD8a/1", "service-terminal": true, "service-api": true, "password-force-change": true}, {"name": "hua<PERSON>", "group-name": "admin", "password": "$6$S7C.b2ZU$xID5fb7at.KQNFzp1mJTbjDIGEi.kfb/CHiKQ0PbdOuCiMnOBqlO/3OkBaVwSoQTunLZs8htPQXEYCvHejwVA0", "service-terminal": true, "service-api": true, "password-force-change": false}, {"name": "test123", "group-name": "admin", "password": "$6$pVoV44CSyvrz$uXsgLbHICc35Pl9OhXwqsd2x3kk.5YjTpeoRcPZ9zEhomQfky.9ZpZrmj5/m4dutkKwMLo36O/WI9rTN1vXEC.", "service-terminal": true, "password-force-change": false}, {"name": "test111", "group-name": "admin", "password": "$6$ZMqu85Y8TGXF/eHW$a/kpiQ8HDQuyrnAvkdR8TYTDVP7JQYULRkEsu0WGqWDancvMt/rBYWJkvHS4lVZYNk0v3e8hO753BZz60UyPA0", "level": 3, "service-terminal": true, "service-api": true, "password-force-change": false}, {"name": "test222", "group-name": "admin", "password": "$6$7PcVyuRH0s/6UXEP$L/zmscvMqpaSOvqjyNu4ahm52QUcqqKM3Zxwz9cWCVvC2kLbsKlZBbWomSjP81JAFnr6gsWagnSOeOBhrIt/M/", "level": 3, "service-terminal": true, "service-api": true, "password-force-change": false}, {"name": "test333", "group-name": "admin", "password": "$6$6OYDcUY.8wL2as1T$yqJuxIch8jgeS6kQI8grM.DqjBLH60URPzbRr90p38yRROdbCY7.PjHXhmgzkI/4gAHQMEjIFjzL74e0oD0OR0", "level": 4, "service-terminal": true, "service-api": true, "password-force-change": false}, {"name": "test444", "group-name": "admin", "password": "$6$wgQeX39nxtz3.G7T$zRgw3Lvl9WbuOlxQfrBQncrFvyDJgGOjKRc.Lpu4IqypATvJGryGISomp1Neq9nD4AN5/QvGwur7Ot5c6YVZs.", "level": 5, "service-terminal": true, "service-api": true, "password-force-change": false}]}}}, "huawei-acl:acl": {"groups": {"group": [{"identity": "2000", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3000", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2001", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3001", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2002", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3002", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2003", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3003", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2004", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3004", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2005", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3005", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2006", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3006", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2007", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3007", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2008", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3008", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2009", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3009", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2010", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3010", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2011", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3011", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2012", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3012", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2013", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3013", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2014", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3014", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2015", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3015", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2016", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3016", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2017", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3017", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2018", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3018", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2019", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3019", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2020", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3020", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2021", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3021", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2022", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3022", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2023", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3023", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2024", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3024", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2025", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3025", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2026", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3026", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2027", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3027", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2028", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3028", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2029", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3029", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2030", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3030", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2031", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3031", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2032", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3032", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2033", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3033", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2034", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3034", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2035", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3035", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2036", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3036", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2037", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3037", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2038", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3038", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2039", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3039", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2040", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3040", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2041", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3041", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2042", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3042", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2043", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3043", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2044", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3044", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2045", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3045", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2046", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3046", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2047", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3047", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2048", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3048", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2049", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3049", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2050", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3050", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2051", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3051", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2052", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3052", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2053", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3053", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2054", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3054", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2055", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3055", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2056", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3056", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2057", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3057", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2058", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3058", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2059", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3059", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2060", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3060", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2061", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3061", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2062", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3062", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2063", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3063", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2064", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3064", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2065", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3065", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2066", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3066", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2067", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3067", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2068", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3068", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2069", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3069", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2070", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3070", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2071", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3071", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2072", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3072", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2073", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3073", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2074", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3074", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2075", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3075", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2076", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3076", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2077", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3077", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2078", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3078", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2079", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3079", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2080", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3080", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2081", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3081", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2082", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3082", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2083", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3083", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2084", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3084", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2085", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3085", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2086", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3086", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2087", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3087", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2088", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3088", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2089", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3089", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2090", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3090", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2091", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3091", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2092", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3092", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2093", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3093", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2094", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3094", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2095", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3095", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2096", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3096", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2097", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3097", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2098", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3098", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2099", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3099", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2100", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3100", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2101", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3101", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2102", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3102", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2103", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3103", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2104", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3104", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2105", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3105", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2106", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3106", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2107", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3107", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2108", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3108", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2109", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3109", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2110", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3110", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2111", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3111", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2112", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3112", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2113", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3113", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2114", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3114", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2115", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3115", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2116", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3116", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2117", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3117", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2118", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3118", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2119", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3119", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2120", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3120", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2121", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3121", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2122", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3122", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2123", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3123", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2124", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3124", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2125", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3125", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2126", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3126", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2127", "rule-basics": {"rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3127", "rule-advances": {"rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}]}}, "huawei-network-instance:network-instance": {"instances": {"instance": [{"name": "_public_", "huawei-l3vpn:afs": {"af": [{"type": "ipv4-unicast", "huawei-routing:routing": {"routing-manage": {"topologys": {"topology": [{"name": "base"}]}}}}]}}]}}, "huawei-ifm:ifm": {"interfaces": {"interface": [{"name": "Vlanif1", "class": "main-interface", "type": "<PERSON><PERSON><PERSON>", "link-protocol": "ethernet", "huawei-ip:ipv4": {"addresses": {"address": [{"ip": "192.168.112.1", "mask": "255.255.255.0", "type": "main"}]}}, "huawei-dhcp:interface-ip-pool": {"select-interface": {"dns-list": {"ip-address": ["114.114.114.114"]}}}}, {"name": "GE0/0/1", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": "1", "untag-vlans": "1"}}}}, {"name": "GE0/0/2", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": "1", "untag-vlans": "1"}}}}, {"name": "GE0/0/3", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": "1", "untag-vlans": "1"}}}}, {"name": "GE0/0/4", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": "1", "untag-vlans": "1"}}}}, {"name": "GE0/0/5", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ip:ipv4": {"addresses": {"address": [{"ip": "*************", "mask": "*********", "type": "main"}]}}}, {"name": "NULL", "class": "main-interface", "type": "NULL", "link-protocol": "ethernet"}, {"name": "lo", "class": "main-interface", "type": "LoopBack", "link-protocol": "ethernet"}]}}, "huawei-vlan:vlan": {"vlans": {"vlan": [{"id": 1}, {"id": 10}, {"id": 20}, {"id": 30}, {"id": 40}, {"id": 50}, {"id": 60}, {"id": 70}, {"id": 80}, {"id": 90}, {"id": 100}, {"id": 110}, {"id": 120}, {"id": 130}, {"id": 140}, {"id": 150}]}}, "huawei-cli:cli": {"header": {"login-text": "welcome!"}, "huawei-cli-lite:terminal": {"history-cmd-size": 200, "idle-timeout": 0, "split-screen": false}}, "huawei-dhcp:dhcp": {"common": {"global": {"enable": true}}}, "huawei-pki:pki": {"domains": {"domain": [{"name": "default"}]}}, "huawei-lldp:lldp": {}, "huawei-easyweb-netmgmt:easyweb-netmgmt": {"network": {"device-granteds": {"device-granted": [{"mac-address": "000a-43fc-7d00"}]}}}, "huawei-ftpc:ftpc": {"client": {"enabled": true}}, "huawei-host-security:host-security": {"policys": {"policy": [{"name": "webm", "cpcars": {"cpcar": [{"protocol-type": "https-server", "cir": 1200}]}, "applied-policys": {"applied-policy": [{"applied-type": "all"}]}}]}}, "huawei-ssl:ssl": {"ssl-policys": {"ssl-policy": [{"policy-name": "default", "pki-realm": "default"}, {"policy-name": "a", "pki-realm": "a"}, {"policy-name": "b", "pki-realm": "b"}, {"policy-name": "c", "pki-realm": "c"}]}}, "huawei-nat-policy:nat-policy": {"rules": {"rule": [{"name": "default", "action": {"source-nat": {"easy-ip": [null]}}, "egress-interface": ["GE0/0/5"]}]}}, "openconfig-telemetry:telemetry-system": {"sensor-groups": {"sensor-group": [{"sensor-group-id": "1", "config": {"sensor-group-id": "1"}, "sensor-paths": {"sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "2", "config": {"sensor-group-id": "2"}, "sensor-paths": {"sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "3", "config": {"sensor-group-id": "3"}, "sensor-paths": {"sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "4", "config": {"sensor-group-id": "4"}, "sensor-paths": {"sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "5", "config": {"sensor-group-id": "5"}, "sensor-paths": {"sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "6", "config": {"sensor-group-id": "6"}, "sensor-paths": {"sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "7", "config": {"sensor-group-id": "7"}, "sensor-paths": {"sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "8", "config": {"sensor-group-id": "8"}, "sensor-paths": {"sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}]}, "destination-groups": {"destination-group": [{"group-id": "1", "config": {"group-id": "1"}, "destinations": {"destination": [{"destination-address": "*************", "destination-port": "777", "config": {"destination-address": "*************", "destination-port": 777}}]}}, {"group-id": "2", "config": {"group-id": "2"}, "destinations": {"destination": [{"destination-address": "*************", "destination-port": "888", "config": {"destination-address": "*************", "destination-port": 888}}]}}]}, "subscriptions": {"persistent": {"subscription": [{"subscription-name": "sub1", "config": {"subscription-name": "sub1"}, "sensor-profiles": {"sensor-profile": [{"sensor-group": "1", "config": {"sensor-group": "1", "sample-interval": 60000}}, {"sensor-group": "2", "config": {"sensor-group": "2", "sample-interval": 60000}}, {"sensor-group": "3", "config": {"sensor-group": "3", "sample-interval": 60000}}, {"sensor-group": "4", "config": {"sensor-group": "4", "sample-interval": 60000}}, {"sensor-group": "5", "config": {"sensor-group": "5", "sample-interval": 60000}}, {"sensor-group": "6", "config": {"sensor-group": "6", "sample-interval": 60000}}, {"sensor-group": "7", "config": {"sensor-group": "7", "sample-interval": 60000}}, {"sensor-group": "8", "config": {"sensor-group": "8", "sample-interval": 60000}}]}, "destination-groups": {"destination-group": [{"group-id": "1", "config": {"group-id": "1"}}]}}, {"subscription-name": "sub2", "config": {"subscription-name": "sub2"}, "sensor-profiles": {"sensor-profile": [{"sensor-group": "1", "config": {"sensor-group": "1", "sample-interval": 60000}}, {"sensor-group": "2", "config": {"sensor-group": "2", "sample-interval": 60000}}, {"sensor-group": "3", "config": {"sensor-group": "3", "sample-interval": 60000}}, {"sensor-group": "4", "config": {"sensor-group": "4", "sample-interval": 60000}}, {"sensor-group": "5", "config": {"sensor-group": "5", "sample-interval": 60000}}, {"sensor-group": "6", "config": {"sensor-group": "6", "sample-interval": 60000}}, {"sensor-group": "7", "config": {"sensor-group": "7", "sample-interval": 60000}}, {"sensor-group": "8", "config": {"sensor-group": "8", "sample-interval": 60000}}]}, "destination-groups": {"destination-group": [{"group-id": "2", "config": {"group-id": "2"}}]}}]}}}, "huawei-sshs:sshs": {"server": {"pki-domain": "default"}, "users": {"user": [{"name": "admin", "key-name": "default", "pub-key-type": "PKI"}, {"name": "hua<PERSON>", "key-name": "a", "pub-key-type": "PKI"}, {"name": "root123", "key-name": "a", "pub-key-type": "PKI"}, {"name": "test111", "key-name": "a", "pub-key-type": "PKI"}, {"name": "test222", "key-name": "a", "pub-key-type": "PKI"}, {"name": "test333", "key-name": "a", "pub-key-type": "PKI"}, {"name": "test444", "key-name": "a", "pub-key-type": "PKI"}, {"name": "test123", "key-name": "a", "pub-key-type": "PKI"}]}, "server-enable": {"stelnet-ipv4-enable": "enable"}, "ipv4-server-sources": {"ipv4-server-source": [{"src-interface": "Vlanif1"}, {"src-interface": "GE0/0/5"}]}, "call-homes": {"call-home": [{"call-home-name": "QiankunCloudService", "end-points": {"end-point": [{"end-point-name": "DefaultEndPoint", "address": "***********", "port": 10022, "enabled": true}]}}]}}, "huawei-syslog:syslog": {"log-switch-list": {"log-switch": [{"feature-name": "MDOMU", "log-name": "SUPPRESS_DIAGLOGINFO", "suppress": true}]}, "log-storage-time": {"storage-time": 300}}, "huawei-system:system": {"system-info": {"sys-name": "HUAWEI123"}}}}