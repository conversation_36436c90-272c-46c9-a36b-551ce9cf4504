{"version": "2.0", "type": "record", "name": "fan_info", "max_record_count": 2, "fields": [{"name": "panel_name", "type": "string", "size": 64}, {"name": "type", "type": "string"}, {"name": "chip_name", "type": "string", "size": 64}, {"name": "chip_port", "type": "uint32"}, {"name": "if_index", "type": "uint32"}, {"name": "device", "type": "string", "size": 64}, {"name": "state", "type": "boolean"}], "keys": [{"name": "PK", "node": "fan_info", "fields": ["panel_name"], "index": {"type": "primary"}}]}