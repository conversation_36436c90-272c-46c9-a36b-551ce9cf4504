{"comment": "table of service scheme", "version": "2.0", "type": "record", "name": "aaa_service_scheme", "config": {"check_validity": false}, "max_record_count": 8192, "fields": [{"name": "usServiceSchemeIndex", "type": "uint16", "comment": "Index of Service Scheme"}, {"name": "ulVsysID", "type": "uint32", "comment": "Vsys ID"}, {"name": "szName", "type": "bytes", "size": 256, "comment": "Name"}, {"name": "ucIfMIBLock", "type": "uint8", "comment": "MIB Lock Flag"}, {"name": "is_reserved", "type": "uint8", "comment": "Reserved Field"}, {"name": "stAuthor_ulNextHop", "type": "uint32", "comment": "Next Hop for Author"}, {"name": "stAuthor_ulVLANID", "type": "uint32", "comment": "Author V<PERSON><PERSON><PERSON>"}, {"name": "stAuthor_ucUpPriority", "type": "uint8", "comment": "Author Up Priority"}, {"name": "stAuthor_ucDownPriority", "type": "uint8", "comment": "Author Down Priority"}, {"name": "stAuthor_ucIfSrcRoute", "type": "uint8", "comment": "Author <PERSON><PERSON>"}, {"name": "stAuthor_ucAdminLevel", "type": "uint8", "comment": "Author Admin Level"}, {"name": "stAuthor_usDhcpServerGrp", "type": "uint16", "comment": "Author Dhcp Server Grp"}, {"name": "stAuthor_bFlowStatUpFlag", "type": "uint8", "comment": "Author Flow State Up Flag"}, {"name": "stAuthor_bFlowStatDownFlag", "type": "uint8", "comment": "Author Flow State Down Flag"}, {"name": "stAuthor_ucSrvSchemeVoiceVlan", "type": "uint8", "comment": "Author <PERSON>v <PERSON> Voice Vlan"}, {"name": "stAuthor_ulAutoUpdateURLVersion", "type": "uint32", "comment": "Author Auto Update URL Version"}, {"name": "stAuthor_ulAclNum", "type": "uint32", "comment": "Author <PERSON><PERSON><PERSON>"}, {"name": "stAuthor_ulInterfaceFlag", "type": "uint32", "comment": "Author Interface Flag"}, {"name": "stAuthor_ulLocalAuthorBitMap", "type": "uint32", "comment": "Author Local Author Bit Map"}, {"name": "stAuthor_ulRemoteAuthorBitMap", "type": "uint32", "comment": "Author Remote Author Bit Map"}, {"name": "stAuthor_usUclGroupId", "type": "uint16", "comment": "Author Ucl GroupId"}, {"name": "stAuthor_ulRedirectAclId", "type": "uint32", "comment": "Author Redirect Acl Id"}, {"name": "stAuthor_ulUserNameMaxNum", "type": "uint32", "comment": "Author User Name <PERSON>"}, {"name": "stAuthor_usVlan", "type": "uint16", "comment": "Author <PERSON><PERSON>"}, {"name": "stAuthor_ucPriority", "type": "uint8", "comment": "Author Priority"}, {"name": "stAuthor_szQosProfileName", "type": "bytes", "nullable": true, "size": 256, "comment": "Author <PERSON><PERSON> Profile Name"}, {"name": "stAuthor_ulDnsIP", "type": "bytes", "nullable": true, "size": 128, "comment": "Author Dns IP"}, {"name": "stAuthor_ulWinsIP", "type": "bytes", "nullable": true, "size": 128, "comment": "Author Wins IP"}, {"name": "stAuthor_stIPPool_szIPPool", "type": "bytes", "nullable": true, "size": 4096, "comment": "Author IPPool"}, {"name": "stAuthor_szCallNum", "type": "bytes", "nullable": true, "size": 512, "comment": "Author <PERSON>"}, {"name": "stAuthor_szSubCallNum", "type": "bytes", "nullable": true, "size": 512, "comment": "Author <PERSON>"}, {"name": "stAuthor_szCallbackNum", "type": "bytes", "nullable": true, "size": 512, "comment": "Author <PERSON><PERSON>"}, {"name": "stAuthor_szAutoUpdateURL", "type": "bytes", "nullable": true, "size": 1024, "comment": "Author Auto Update URL"}, {"name": "stAuthor_szDNSDomainName", "type": "bytes", "nullable": true, "size": 1024, "comment": "Author DNS Domain Name"}, {"name": "stAuthor_ausAclId", "type": "bytes", "nullable": true, "size": 512, "comment": "Author Acl Id"}, {"name": "stAuthor_ausAclIdV6", "type": "bytes", "nullable": true, "size": 512, "comment": "Author Acl Id V6"}, {"name": "stAuthor_stIdleCut", "type": "record", "nullable": true, "comment": "Author <PERSON><PERSON> Cut Info", "fields": [{"name": "ulTime", "type": "uint32", "comment": "Time"}, {"name": "<PERSON><PERSON><PERSON>", "type": "uint32", "comment": "Flow"}, {"name": "ucIdleCutFlowDirection", "type": "uint8", "comment": "Idle Cut Flow Direction"}]}, {"name": "stAuthor_stCarInbound", "type": "record", "nullable": true, "comment": "Author Car Inbound Info", "fields": [{"name": "ucCarFlag", "type": "uint8", "comment": "Car Flag"}, {"name": "ulCir", "type": "uint32", "comment": "Cir"}, {"name": "ulCbs", "type": "uint32", "comment": "Cbs"}, {"name": "ulPir", "type": "uint32", "comment": "<PERSON><PERSON>"}, {"name": "ulPbs", "type": "uint32", "comment": "Pbs"}]}, {"name": "stAuthor_stCarOutbound", "type": "record", "nullable": true, "comment": "Author Car Outbound Info", "fields": [{"name": "ucCarFlag", "type": "uint8", "comment": "Car Flag"}, {"name": "ulCir", "type": "uint32", "comment": "Cir"}, {"name": "ulCbs", "type": "uint32", "comment": "Cbs"}, {"name": "ulPir", "type": "uint32", "comment": "<PERSON><PERSON>"}, {"name": "ulPbs", "type": "uint32", "comment": "Pbs"}]}, {"name": "stAuthor_stRemarkValue", "type": "record", "nullable": true, "comment": "Author Remark Value Info", "fields": [{"name": "ucDscpValue", "type": "uint8", "comment": "Dscp Value"}, {"name": "uc8021pValue", "type": "uint8", "comment": "8021p Value"}, {"name": "ucExpValue", "type": "uint8", "comment": "Exp Value"}, {"name": "ucLPValue", "type": "uint8", "comment": "LP Value"}]}, {"name": "stAuthor_stQosProfile", "type": "record", "nullable": true, "comment": "Author <PERSON><PERSON> Profile Info", "fields": [{"name": "ulStatEn", "type": "uint32", "comment": "StatEn"}, {"name": "stUpCarItem", "type": "record", "nullable": true, "comment": "Up Car Item Info", "fields": [{"name": "ucCarFlag", "type": "uint8", "comment": "Car Flag"}, {"name": "ulCir", "type": "uint32", "comment": "Cir"}, {"name": "ulCbs", "type": "uint32", "comment": "Cbs"}, {"name": "ulPir", "type": "uint32", "comment": "<PERSON><PERSON>"}, {"name": "ulPbs", "type": "uint32", "comment": "Pbs"}]}, {"name": "stDownCarItem", "type": "record", "nullable": true, "comment": "Down Car Item Info", "fields": [{"name": "ucCarFlag", "type": "uint8", "comment": "Car Flag"}, {"name": "ulCir", "type": "uint32", "comment": "Cir"}, {"name": "ulCbs", "type": "uint32", "comment": "Cbs"}, {"name": "ulPir", "type": "uint32", "comment": "<PERSON><PERSON>"}, {"name": "ulPbs", "type": "uint32", "comment": "Pbs"}]}, {"name": "stRemark", "type": "record", "nullable": true, "comment": "Remark Info", "fields": [{"name": "ucRemark8021p", "type": "uint8", "comment": "Remark of 8021p"}, {"name": "uc8021p", "type": "uint8", "comment": "8021p"}, {"name": "ucRemarkDscp", "type": "uint8", "comment": "Remark Dscp"}, {"name": "ucInDscp", "type": "uint8", "comment": "In Dscp"}, {"name": "ucOutDscp", "type": "uint8", "comment": "Out Dscp"}]}, {"name": "stProfileUserQueue", "type": "record", "nullable": true, "comment": "Profile User Queue Info", "fields": [{"name": "ucValid", "type": "uint8", "comment": "Valid Flag"}, {"name": "ulPir", "type": "uint8", "comment": "<PERSON><PERSON>"}, {"name": "ulCir", "type": "uint8", "comment": "Cir"}, {"name": "usFlowQueueProfileIndex", "type": "uint16", "comment": "Flow Queue Profile Index"}, {"name": "usFlowMappingProfileIndex", "type": "uint16", "comment": "Flow Mapping Profile Index"}]}]}, {"name": "astAAATariffLevel", "type": "record", "nullable": true, "array": true, "size": 128, "comment": "AAA Tariff Level Info", "fields": [{"name": "ucIsCfgFlag", "type": "uint8", "comment": "Cfg Flag"}, {"name": "ucAcctOnFlag", "type": "uint8", "comment": "Acct On Flag"}, {"name": "szQosProfileName", "type": "bytes", "nullable": true, "size": 256, "comment": "Qos Profile Name"}]}, {"name": "patch_reserved", "type": "uint32", "comment": "reserved for patch"}], "keys": [{"name": "primary_key_index", "index": {"type": "primary"}, "node": "aaa_service_scheme", "fields": ["usServiceSchemeIndex"], "constraints": {"unique": true}, "comment": "index of aaa service scheme"}, {"name": "primary_key_name", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "aaa_service_scheme", "fields": ["ulVsysID", "szName"], "constraints": {"unique": true}, "comment": "index of aaa service scheme"}]}