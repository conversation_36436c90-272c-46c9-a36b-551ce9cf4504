{"comment": "丢包可视使能配置表", "version": "2.0", "type": "record", "name": "pktevent_dropevent", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "vrid", "type": "uint32", "comment": "vrid"}, {"name": "dropEnable", "type": "uint32", "comment": "丢包使能标记"}, {"name": "resv", "type": "uint32", "comment": "预留字段"}], "keys": [{"name": "pktevent_dropevent_key", "index": {"type": "primary"}, "node": "pktevent_dropevent", "fields": ["vrid", "dropEnable"], "constraints": {"unique": true}, "comment": "丢包使能key"}]}