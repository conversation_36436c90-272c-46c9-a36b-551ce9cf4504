{"comment": "FES1413#, 1416#", "version": "2.0", "type": "record", "name": "maccfg_bd", "config": {"check_validity": false}, "max_record_count": 32768, "fields": [{"name": "vrid", "type": "uint32", "comment": "虚拟设备id"}, {"name": "bd_type", "type": "uint8", "comment": "1:VLAN, 2:VSI, 3:bridgedomain"}, {"name": "bdid", "type": "uint32", "comment": "广播域 id"}, {"name": "learning_enable", "type": "uint8", "nullable": true, "default": 1, "comment": "是否使能MAC学习"}, {"name": "limit_max_num", "type": "uint32", "nullable": true, "default": 0, "comment": "MACLimit个数配置，范围：0~32767, 0：表示不限制"}, {"name": "limit_action", "type": "uint8", "nullable": true, "default": 1, "comment": "达到limit限制后，报文的动作，1：转发，2：丢弃"}, {"name": "limit_alarm", "type": "uint8", "nullable": true, "default": 0, "comment": "超限后告警"}, {"name": "src_no_hit_drop", "type": "uint8", "nullable": true, "default": 0, "comment": "报文源MAC未命中MAC表，丢弃该报文"}, {"name": "flap_level", "type": "uint8", "nullable": true, "default": 2, "comment": "flap控制等级，1：low，2：middle，3：high"}, {"name": "miss_drop", "type": "uint8", "nullable": true, "comment": "未命中MAC表的报文丢弃，默认未知单播广播"}, {"name": "rate", "type": "uint32", "comment": "速率"}, {"name": "broadcast_enable", "type": "uint32", "comment": "板丢弃使能"}, {"name": "mac_learn_action", "type": "uint32", "comment": "mac学习动作"}, {"name": "unknown_discard", "type": "uint32", "comment": "未知丢弃"}, {"name": "mac_aging_time", "type": "uint32", "comment": "老化时间"}, {"name": "statis_discard", "type": "uint32", "comment": "丢弃统计"}, {"name": "evpn_enable", "type": "uint32", "comment": "evpn使能标志"}, {"name": "app_source_id", "type": "uint32", "comment": "上层应用组件源id"}, {"name": "app_serial_id", "type": "uint32", "comment": "上层应用组件序列化id"}, {"name": "app_obj_id", "type": "uint64", "comment": "上层应用组件目标id"}, {"name": "app_version", "type": "uint32", "comment": "上层应用组件版本号"}], "keys": [{"name": "maccfg_bd_pk", "index": {"type": "primary"}, "node": "maccfg_bd", "fields": ["vrid", "bd_type", "bdid"], "constraints": {"unique": true}, "comment": "主键"}]}