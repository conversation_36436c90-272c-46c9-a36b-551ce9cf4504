{"comment": "bfd报文模板表", "version": "2.0", "type": "record", "name": "bfd_np_send_pkt", "max_record_count": 2048, "fields": [{"name": "my_discr", "type": "uint32", "comment": ""}, {"name": "flag", "type": "uint32", "comment": "flag"}, {"name": "pkt_type", "type": "uint8", "comment": "pkt_type"}, {"name": "pkt_ext_type", "type": "uint8", "comment": "pkt_ext_type"}, {"name": "trust_qindex", "type": "uint8", "comment": "trust_qindex"}, {"name": "with_eth", "type": "uint8", "comment": "with_eth"}, {"name": "qindex", "type": "uint8", "comment": "qindex"}, {"name": "is_trunk_port", "type": "uint8", "comment": "is_trunk_port"}, {"name": "tx_index", "type": "uint16", "comment": "tx_index"}, {"name": "mod", "type": "uint16", "comment": "mod"}, {"name": "port", "type": "uint32", "comment": "port"}, {"name": "data", "type": "fixed", "size": 240, "comment": "data"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "补丁预留保留字段"}], "keys": [{"name": "bfd_np_send_pkt_pk", "index": {"type": "primary"}, "node": "bfd_np_send_pkt", "fields": ["my_discr"], "constraints": {"unique": true}}]}