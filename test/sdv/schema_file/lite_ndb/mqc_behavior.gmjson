{"version": "2.0", "type": "record", "name": "mqc_behavior", "config": {"check_validity": false}, "max_record_count": 2048, "fields": [{"name": "vr_id", "type": "uint32", "comment": "虚拟路由"}, {"name": "behavior_id", "type": "uint32", "comment": "流行为ID"}, {"name": "version", "type": "uint32", "comment": "流行为版本号"}, {"name": "behavior_name", "type": "fixed", "size": 32, "comment": "流行为名称"}, {"name": "action_mask", "type": "fixed", "size": 32, "comment": "动作掩码"}, {"name": "policy_mask", "type": "fixed", "size": 256, "comment": "策略掩码"}, {"name": "remark_dscp", "type": "uint32", "comment": "重标记dscp优先级"}, {"name": "remark_localpri", "type": "uint32", "comment": "重标记内部优先级"}, {"name": "remark_color", "type": "uint32", "comment": "重标记color"}, {"name": "remark_8021p", "type": "uint32", "comment": "重标记8021.p优先级"}, {"name": "remark_mpls_exp", "type": "uint32", "comment": "重标记MPLS报文EXP优先级"}, {"name": "mac_learn", "type": "uint32", "comment": "mac学习"}, {"name": "remark_vlanid", "type": "uint32", "comment": "重标记vlan"}, {"name": "remark_cvlanid", "type": "uint32", "comment": "重标记内层vlan"}, {"name": "nest_vlan", "type": "uint32", "comment": "嵌套vlan"}, {"name": "remark_reserved", "type": "uint32", "comment": "重标记vxlan保留字段"}, {"name": "remark_localid", "type": "uint32", "comment": "重标记本地id"}, {"name": "ingress_match", "type": "uint32", "comment": "localid上行匹配标记"}, {"name": "urpfche_disable", "type": "uint32", "comment": "urpf检查去使能"}, {"name": "statInterval", "type": "uint32", "comment": "统计时间间隔"}, {"name": "mirr_cfg", "type": "record", "comment": "镜像刷新", "fields": [{"name": "obser_index", "type": "uint32", "comment": "观察口索引"}, {"name": "obser_type", "type": "uint32", "comment": "观察口类型"}, {"name": "ifindex", "type": "uint32", "comment": "接口索引"}, {"name": "mc_id", "type": "uint32", "comment": "组播镜像的MCID"}, {"name": "sample_rate", "type": "uint32", "comment": "采样率"}, {"name": "session_id", "type": "uint32", "comment": "会话ID"}, {"name": "mirror_id", "type": "uint32", "comment": "实际下发镜像资源ID"}], "super_fields": [{"name": "mirrcfg_key", "fields": ["obser_index", "obser_type", "ifindex", "mc_id", "sample_rate", "session_id", "mirror_id"]}]}, {"name": "first_car", "type": "record", "comment": "基于Class的car", "fields": [{"name": "cir", "type": "uint32", "comment": "承诺信息速率"}, {"name": "pir", "type": "uint32", "comment": "峰值信息速率"}, {"name": "cbs", "type": "uint32", "comment": "承诺突发尺寸"}, {"name": "pbs", "type": "uint32", "comment": "峰值突发尺寸"}, {"name": "g_ser_cls", "type": "uint32", "comment": "DiffServ域中报文颜色绿"}, {"name": "y_ser_cls", "type": "uint32", "comment": "DiffServ域中报文颜色黄"}, {"name": "r_ser_cls", "type": "uint32", "comment": "DiffServ域中报文颜色红"}, {"name": "g_color", "type": "uint32", "comment": "流量监管中报文颜色绿"}, {"name": "y_color", "type": "uint32", "comment": "流量监管中报文颜色黄"}, {"name": "r_color", "type": "uint32", "comment": "流量监管中报文颜色红"}, {"name": "g_action", "type": "uint32", "comment": "报文的最终颜色绿"}, {"name": "y_action", "type": "uint32", "comment": "报文的最终颜色黄"}, {"name": "r_action", "type": "uint32", "comment": "报文的最终颜色红"}, {"name": "mode", "type": "uint32", "comment": "流量监管采取的颜色模式"}, {"name": "is_first_share", "type": "uint32", "comment": "是否有基于Class的共享CAR"}], "super_fields": [{"name": "firstcar_key", "fields": ["cir", "pir", "cbs", "pbs", "g_ser_cls", "y_ser_cls", "r_ser_cls", "g_color", "y_color", "r_color", "g_action", "y_action", "r_action", "mode", "is_first_share"]}]}, {"name": "second_car", "type": "record", "comment": "基于Policy的car", "fields": [{"name": "cir", "type": "uint32", "comment": "承诺信息速率"}, {"name": "cbs", "type": "uint32", "comment": "承诺突发尺寸"}, {"name": "share_idx", "type": "uint32", "comment": "共享CAR的索引"}, {"name": "set_flag", "type": "uint32", "comment": "是否设置了二级共享car"}], "super_fields": [{"name": "secondcar_key", "fields": ["cir", "cbs", "share_idx", "set_flag"]}]}], "keys": [{"name": "mqc_behavior_pk", "index": {"type": "primary"}, "node": "mqc_behavior", "fields": ["vr_id", "behavior_id"], "constraints": {"unique": true}, "comment": "流行为关键字名称"}, {"name": "index_behavior_id", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mqc_behavior", "fields": ["behavior_id"], "constraints": {"unique": false}, "comment": "流行为ID"}], "super_fields": [{"name": "behavior_key", "fields": ["vr_id", "behavior_id", "version", "behavior_name", "action_mask", "policy_mask", "remark_dscp", "remark_localpri", "remark_color", "remark_8021p", "remark_mpls_exp", "mac_learn", "remark_vlanid", "remark_cvlanid", "nest_vlan", "remark_reserved", "remark_localid", "ingress_match", "urpfche_disable", "statInterval"]}]}