/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 支持V1兼容图查询能力
 Author       : herui h60035902
 Modification :
 create       : 2024/10/09
**************************************************************************** */
#include "GraphQuery.h"

int ret = 0;
class V1GraphQuery_function : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        ret = TestTPC_Init();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        ret = TestTPC_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void V1GraphQuery_function::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void V1GraphQuery_function::TearDown()
{
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
// 创建10个表依次建边，使用全路径扫描
TEST_F(V1GraphQuery_function, V1Com_017_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建10个表(从1开始命名)
    uint16_t testRelId[11] = {0};
    const char tabelName[11][DB_NAME_LEN] = {0};
    DB_REL_DEF_STRU testRelDef = {0};
    for (int i = 1; i < 11; i++) {
        (void)snprintf((char *)tabelName[i], 10, "table%d", i);
        testRelDef = {0};
        TestCreateTblInitRelDef(&testRelDef, tabelName[i]);
        ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i < 10) {
            TestFreeRelDef(&testRelDef);
        }
    }
    // 建边表(从1开始命名)
    DB_EDGE_DEF_STRU edgeDef;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    char edgename[10][DB_NAME_LEN];
    for (int i = 2; i < 11; i++) {
        TestCreateEdgeDef(&edgeDef, true, testRelId[i - 1], testRelId[i], 1, 1, field1, field2);
        (void)sprintf_s((char *)edgename[i - 1], DB_NAME_LEN, "edge%d", i - 1);
        ret = TPC_CreateEdge(g_testDbId, (uint8_t *)edgename[i - 1], &edgeDef);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 十个表写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        for (int j = 1; j < 11; j++) {
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId[j], &pstDsBufSet);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);

    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;

    SetPathInfoToAll(&pstPath, testRelId, &pstFldFilter, 1, field1, field2, 9);
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId[1], &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(10, 24, pstBuff, &pstFldFilter);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建11个表依次建边，使用全路径扫描
TEST_F(V1GraphQuery_function, V1Com_017_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建11个表(从1开始命名)
    uint16_t testRelId[12] = {0};
    const char tabelName[12][DB_NAME_LEN] = {0};
    DB_REL_DEF_STRU testRelDef = {0};
    for (int i = 1; i < 12; i++) {
        (void)snprintf((char *)tabelName[i], 10, "table%d", i);
        testRelDef = {0};
        TestCreateTblInitRelDef(&testRelDef, tabelName[i]);
        ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i < 11) {
            TestFreeRelDef(&testRelDef);
        }
    }
    // 建边表(从1开始命名)
    DB_EDGE_DEF_STRU edgeDef;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    char edgename[11][DB_NAME_LEN];
    for (int i = 2; i < 12; i++) {
        TestCreateEdgeDef(&edgeDef, true, testRelId[i - 1], testRelId[i], 1, 1, field1, field2);
        (void)sprintf_s((char *)edgename[i - 1], DB_NAME_LEN, "edge%d", i - 1);
        ret = TPC_CreateEdge(g_testDbId, (uint8_t *)edgename[i - 1], &edgeDef);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 十个表写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        for (int j = 1; j < 12; j++) {
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId[j], &pstDsBufSet);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);

    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    SetPathInfoToAll(&pstPath, testRelId, &pstFldFilter, 1, field1, field2, 10);
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId[1], &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDCONDITION, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建10个表依次建边，使用单跳查询，跳到第十个表查询数据
TEST_F(V1GraphQuery_function, V1Com_017_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建10个表(从1开始命名)
    uint16_t testRelId[11] = {0};
    const char tabelName[11][DB_NAME_LEN] = {0};
    DB_REL_DEF_STRU testRelDef = {0};
    for (int i = 1; i < 11; i++) {
        (void)snprintf((char *)tabelName[i], 10, "table%d", i);
        testRelDef = {0};
        TestCreateTblInitRelDef(&testRelDef, tabelName[i]);
        ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i < 10) {
            TestFreeRelDef(&testRelDef);
        }
    }
    // 建边表(从1开始命名)
    DB_EDGE_DEF_STRU edgeDef;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    char edgename[10][DB_NAME_LEN];
    for (int i = 2; i < 11; i++) {
        TestCreateEdgeDef(&edgeDef, true, testRelId[i - 1], testRelId[i], 1, 1, field1, field2);
        (void)sprintf_s((char *)edgename[i - 1], DB_NAME_LEN, "edge%d", i - 1);
        ret = TPC_CreateEdge(g_testDbId, (uint8_t *)edgename[i - 1], &edgeDef);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 十个表写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        for (int j = 1; j < 11; j++) {
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId[j], &pstDsBufSet);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);

    // 使用单跳查询创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId[1], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    DB_EDGE_CON_STRU nextedge[9] = {0};
    for (int i = 0; i < 9; i++) {
        SetPathInfoToSingle(&nextedge[i], testRelId[i + 1], testRelId[i + 2], &pstFldFilter, 1, field1, field2);
        ret = TPC_MoveNextByEdge(phSelect, nextedge[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);

    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建11个表依次建边，使用单跳查询，跳到第十一个表查询数据
TEST_F(V1GraphQuery_function, V1Com_017_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建11个表(从1开始命名)
    uint16_t testRelId[12] = {0};
    const char tabelName[12][DB_NAME_LEN] = {0};
    DB_REL_DEF_STRU testRelDef = {0};
    for (int i = 1; i < 12; i++) {
        (void)snprintf((char *)tabelName[i], 10, "table%d", i);
        testRelDef = {0};
        TestCreateTblInitRelDef(&testRelDef, tabelName[i]);
        ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i < 11) {
            TestFreeRelDef(&testRelDef);
        }
    }
    // 建边表(从1开始命名)
    DB_EDGE_DEF_STRU edgeDef;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    char edgename[11][DB_NAME_LEN];
    for (int i = 2; i < 12; i++) {
        TestCreateEdgeDef(&edgeDef, true, testRelId[i - 1], testRelId[i], 1, 1, field1, field2);
        (void)sprintf_s((char *)edgename[i - 1], DB_NAME_LEN, "edge%d", i - 1);
        ret = TPC_CreateEdge(g_testDbId, (uint8_t *)edgename[i - 1], &edgeDef);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 十一个表写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        for (int j = 1; j < 12; j++) {
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId[j], &pstDsBufSet);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);

    // 使用单跳查询创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId[1], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_EDGE_CON_STRU nextedge[10] = {0};
    for (int i = 0; i < 10; i++) {
        SetPathInfoToSingle(&nextedge[i], testRelId[i + 1], testRelId[i + 2], &pstFldFilter, 1, field1, field2);
        ret = TPC_MoveNextByEdge(phSelect, nextedge[i]);
        if (i == 9) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_FAILURE, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C-A图，使用全路径扫描
TEST_F(V1GraphQuery_function, V1Com_017_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId2, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId3, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-3
    DB_EDGE_DEF_STRU edgeDef3;
    TestCreateEdgeDef(&edgeDef3, true, testRelId3, testRelId1, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-3", &edgeDef3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edge[2].edgeInfo[0] = edgeDef3.relInfos[0];
    pstPath.edge[2].edgeInfo[1] = edgeDef3.relInfos[1];
    pstPath.edge[2].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 3;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(4, pstBuff->realRelNum);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(10, 24, pstBuff, &pstFldFilter);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C-A图，使用单跳查询，查询到第一张表的数据后，再通过下一跳回到第一张表进行数据查询
TEST_F(V1GraphQuery_function, V1Com_017_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-3
    DB_EDGE_DEF_STRU edgeDef3;
    TestCreateEdgeDef(&edgeDef3, true, testRelId1, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-3", &edgeDef3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用单跳查询创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuff->realRelNum);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 从表1跳回表1
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    SetPathInfoToSingle(&nextedge, testRelId3, testRelId1, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuff->realRelNum);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 先不在表中写数据，开启CDB1写数据但不提交，开启CDB2查询数据，CDB1提交后开启CDB3查询数据
TEST_F(V1GraphQuery_function, V1Com_017_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启CDB1和CDB2
    uint32_t cdbID1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbID2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 在CDB1中写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(cdbID1, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(cdbID1, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(cdbID1, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 未提交CDB1时查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID2, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pstBuff->dataBuf[0].ulRecNum);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务CDB2
    ret = TPC_CommitCDB(cdbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务CDB1
    ret = TPC_CommitCDB(cdbID1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启CDB3
    uint32_t cdbID3 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 再次查询
    DB_MUTIL_BUF_STRU *pstBuff1 = NULL;

    ret = TPC_SelectAllRecByPath(cdbID3, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(3, pstBuff1->realRelNum);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff1->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff1->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff1->dataBuf[2].relId);
    TestDqlCheckData(10, 24, pstBuff1, &pstFldFilter);

    // 提交事务CDB3
    ret = TPC_CommitCDB(cdbID3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff1->memId);
}
// TPC_SelectAllRecByPath覆盖DB_OP_EQUAL过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(1, 24, pstBuff, &pstFldFilter, 6);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_LESS过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_LESS;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(6, 24, pstBuff, &pstFldFilter, 0);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_LESSEQUAL过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果(需要修改)
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(7, 24, pstBuff, &pstFldFilter, 0);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_LARGER过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_LARGER;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(3, 24, pstBuff, &pstFldFilter, 7);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_LARGEREQUAL过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(4, 24, pstBuff, &pstFldFilter, 6);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MAX_LESS过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(1, 24, pstBuff, &pstFldFilter, 5);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MAX_LESS_EQUAL过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(1, 24, pstBuff, &pstFldFilter, 6);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MIN_LARGER过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(1, 24, pstBuff, &pstFldFilter, 7);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MIN_LARGER_EQUAL过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(1, 24, pstBuff, &pstFldFilter, 6);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_NOTEQUAL过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_NOTEQUAL;
    *(int64_t *)pstCond.aCond[0].aucValue = 0;
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[1].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId3, pstBuff->dataBuf[2].relId);
    TestDqlCheckData(9, 24, pstBuff, &pstFldFilter, 1);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_BeginSelectByTopo覆盖DB_OP_HAVEPREFIX过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_HAVEPREFIX;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 1;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[0]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[0]);
    TestDqlCheckDataHavestr(fetchCount, 231, pstBuff, expectStrVal1, expectStrVal2, expectQueryVal[0]);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_BeginSelectByTopo覆盖DB_OP_NOPREFIX过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_NOPREFIX;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 1;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal2, expectQueryVal[0]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal1, expectQueryVal[0]);
    TestDqlCheckDataHavestr(fetchCount, 231, pstBuff, expectStrVal1, expectStrVal2, expectQueryVal[0]);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_BeginSelectByTopo覆盖DB_OP_PREFIX过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcde_%05u_test", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_PREFIX;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 1;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[0]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[0]);
    TestDqlCheckDataHavestr(fetchCount, 231, pstBuff, expectStrVal1, expectStrVal2, 0);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_BeginSelectByTopo覆盖DB_OP_PREFIX12过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 1;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[0]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[0]);
    TestDqlCheckDataHavestr(fetchCount, 231, pstBuff, expectStrVal1, expectStrVal2, expectQueryVal[0]);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_BeginSelectByTopo覆盖DB_OP_PREFIX21过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcde_%05u_test", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_PREFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 1;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[0]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[0]);
    TestDqlCheckDataHavestr(fetchCount, 231, pstBuff, expectStrVal1, expectStrVal2, 0);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MAX_PREFIX12过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            if (i < recordCnt / 2) {
                setSteValPtr1 = strVal1;
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MAX_PREFIX12;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[1]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[1]);
    TestDqlCheckDataHavestr(1, 231, pstBuff, expectStrVal1, expectStrVal2, expectQueryVal[1]);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MAX_PREFIX21过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcde_%05u_test", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MAX_PREFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[1]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[1]);
    TestDqlCheckDataHavestr(3, 231, pstBuff, expectStrVal1, expectStrVal2, 0);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MIN_PREFIX12过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            if (i < recordCnt / 2) {
                setSteValPtr1 = strVal1;
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MAX_PREFIX12;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[1]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[1]);
    TestDqlCheckDataHavestr(1, 231, pstBuff, expectStrVal1, expectStrVal2, expectQueryVal[1]);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MIN_PREFIX21过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcde_%05u_test", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MIN_PREFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[0]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[0]);
    TestDqlCheckDataHavestr(3, 231, pstBuff, expectStrVal1, expectStrVal2, 0);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_BeginSelectByTopo覆盖DB_OP_POSTFIX21过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "hijkabcde_%05u", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_POSTFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 1;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[0]);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[0]);
    TestDqlCheckDataHavestr(fetchCount, 231, pstBuff, expectStrVal1, expectStrVal2, 0);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_SelectAllRecByPath覆盖DB_OP_MAX_POSTFIX21过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "cde";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s", setSteValPtr1);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s", setSteValPtr2);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用单跳查询获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "hijkabcde");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MAX_POSTFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s", strVal3);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s", strVal2);
    TestDqlCheckDataHavestr(2, 231, pstBuff, expectStrVal1, expectStrVal2, expectQueryVal[0]);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_BeginSelectByTopo覆盖DB_OP_LIKE过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcd%ef%";
    const char *strVal2 = "b_cd_";
    const char *strVal3 = "abcfe";
    const char *strVal4 = "abefg";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%05u_%s", notUniqueVal, setSteValPtr1);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s", setSteValPtr2);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "%%ef%%");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 1;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%05u_%s", expectQueryVal[0], strVal1);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s", strVal2);
    TestDqlCheckDataHavestr(fetchCount, 231, pstBuff, expectStrVal1, expectStrVal2, 0);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// TPC_BeginSelectByTopo覆盖DB_OP_NOLIKE过滤算子
TEST_F(V1GraphQuery_function, V1Com_017_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefHaveStr(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {3};
    uint8_t field2[1] = {3};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 10);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);

    TblFieldData2DefT TblFieldDataDefSet;
    char setStrVal1[101] = {0};
    char setStrVal2[101] = {0};
    const char *strVal1 = "abcdef";
    const char *strVal2 = "bcd";
    const char *strVal3 = "abcde";
    const char *strVal4 = "abefg";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);

    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%05u_%s", notUniqueVal, setSteValPtr1);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s", setSteValPtr2);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    char opStrVal1[DB_ELELEN_MAX] = {0};
    sprintf_s(opStrVal1, sizeof(opStrVal1), "%%ef");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_NOTLIKE;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    // 设置查询句柄
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 1;
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    char expectStrVal1[101] = {0};
    char expectStrVal2[101] = {0};
    sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%05u_%s", notUniqueVal, strVal2);
    sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s", strVal1);
    TestDqlCheckDataHavestr(fetchCount, 231, pstBuff, expectStrVal1, expectStrVal2, expectQueryVal[0]);
    // 释放结果占用的内存
    TEST_V1_FREE(expectQueryVal);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 全路径扫描时，传入的路径和当前表无关
TEST_F(V1GraphQuery_function, V1Com_017_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 1;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDCONDITION, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 全路径扫描时，传入路径顺序有误
TEST_F(V1GraphQuery_function, V1Com_017_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDCONDITION, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 全路径扫描时，投影指定不存在的字段id
TEST_F(V1GraphQuery_function, V1Com_017_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 2;
    pstFldFilter.aucField[0] = 2;
    pstFldFilter.aucField[1] = 9;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFIELD, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 单跳查询时，投影指定不存在的字段id
TEST_F(V1GraphQuery_function, V1Com_017_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用单跳查询创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter1;
    pstFldFilter1.ucFieldNum = DB_FIELD_ALL;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 2;
    pstFldFilter.aucField[0] = 2;
    pstFldFilter.aucField[1] = 9;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter1, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳到表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFIELD, ret);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 单跳查询中，每fetch一次数据就释放一次内存
TEST_F(V1GraphQuery_function, V1Com_017_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用单跳查询创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    TPC_FreeBufMemById(pstBuff->memId);
    // 从表1跳到表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    TPC_FreeBufMemById(pstBuff->memId);
    // 从表2跳到表3
    SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表3的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    TPC_FreeBufMemById(pstBuff->memId);
    // 结束查询
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// A-B-C图中C表不写数据进行全路径查询
TEST_F(V1GraphQuery_function, V1Com_017_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描获取所有数据，创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    uint32_t recNum[3] = {10, 10, 0};
    TestDqlCheckData1(recNum, 24, pstBuff, &pstFldFilter);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// A-B-C图中C表不写数据进行单跳查询
TEST_F(V1GraphQuery_function, V1Com_017_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用单跳查询创建会话id
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(cdbID, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳到表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表2跳到表3
    SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表3的值
    ret = TPC_FetchSelectTopoRec(cdbID, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(0, 24, pstBuff, &pstFldFilter);
    TPC_FreeBufMemById(pstBuff->memId);
    // 结束查询
    ret = TPC_EndTopoSelect(cdbID, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 业务数据场景覆盖,进行单跳查询
TEST_F(V1GraphQuery_function, V1Com_017_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3(设置索引字段为非唯一索引)
    uint16_t testRelId3 = 0;
    // 设置表3的索引
    VOS_UINT32 ulNIdxs = 1;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = {"idx"};
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = {DBDDL_INDEXTYPE_TTREE};
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = {0};
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = {1};
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = {{0}};
    DB_INDEX_DEF_STRU *pstIdxLst =
        testSetIdxInfo(ulNIdxs, aucIndexNameLst, enIndexTypeLst, ucUniqueFlagLst, ucIdxFldNum, aucFieldIDLst);
    TestCreateTblInitRelDef(&testRelDef, "table3", pstIdxLst, ulNIdxs);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(testRelDef.pstFldLst);
    // 建表5
    uint16_t testRelId5 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table5", pstIdxLst, ulNIdxs);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId5);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表4(设置两个索引字段)
    uint16_t testRelId4 = 0;
    // 设置表4的索引
    VOS_UINT32 ulNIdxs1 = 2;
    char aucIndexNameLst1[ulNIdxs1][DB_IDX_NAME_LEN] = {"idx1", "idx2"};
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst1[ulNIdxs1] = {DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE};
    VOS_UINT8 ucUniqueFlagLst1[ulNIdxs1] = {0, 1};
    VOS_UINT8 ucIdxFldNum1[ulNIdxs1] = {1, 1};
    VOS_UINT8 aucFieldIDLst1[ulNIdxs1][DB_IDX_FLD_MAX] = {{0}, {5}};
    DB_INDEX_DEF_STRU *pstIdxLst1 =
        testSetIdxInfo(ulNIdxs1, aucIndexNameLst1, enIndexTypeLst1, ucUniqueFlagLst1, ucIdxFldNum1, aucFieldIDLst1);
    TestCreateTblInitRelDef(&testRelDef, "table4", pstIdxLst1, ulNIdxs1);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    uint8_t field3[1] = {0};
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field2, field3);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表3-4
    DB_EDGE_DEF_STRU edgeDef3;
    uint8_t field41[1] = {0};
    TestCreateEdgeDef(&edgeDef3, true, testRelId3, testRelId4, 1, 1, field3, field41);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge3-4", &edgeDef3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表4-5
    DB_EDGE_DEF_STRU edgeDef4;
    uint8_t field42[1] = {5};
    uint8_t field5[1] = {0};
    TestCreateEdgeDef(&edgeDef4, true, testRelId4, testRelId5, 1, 1, field42, field5);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge4-5", &edgeDef4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写表1和表2数据，数据量为128k
    VOS_UINT32 recordCnt = 128000;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    // 写表3数据
    recordCnt = 1000;
    DB_DSBUF_STRU pstDsBufSet1;
    pstDsBufSet1.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet1.usRecNum = 1;
    pstDsBufSet1.StdBuf.ulBufLen = pstDsBufSet1.usRecLen;
    pstDsBufSet1.StdBuf.ulActLen = pstDsBufSet1.usRecLen;
    VOS_UINT8 *pucDataSet1 = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet1.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet1, pstDsBufSet1.StdBuf.ulBufLen, 0x00, pstDsBufSet1.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet1;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i < 10) {
            testSetAllField(&TblFieldDataDefSet1, i, 0, 6);
        }
        if (i > 9 && i < 20) {
            testSetAllField(&TblFieldDataDefSet1, i, 0, 7);
        }
        if (i >= 20) {
            testSetAllField(&TblFieldDataDefSet1, i);
        }
        memcpy_s(pucDataSet1, pstDsBufSet1.usRecLen, &TblFieldDataDefSet1, pstDsBufSet1.usRecLen);
        pstDsBufSet1.StdBuf.pucData = pucDataSet1;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet1);
    // 写表4数据
    recordCnt = 1000;
    DB_DSBUF_STRU pstDsBufSet2;
    pstDsBufSet2.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet2.usRecNum = 1;
    pstDsBufSet2.StdBuf.ulBufLen = pstDsBufSet2.usRecLen;
    pstDsBufSet2.StdBuf.ulActLen = pstDsBufSet2.usRecLen;
    VOS_UINT8 *pucDataSet2 = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet2.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet2, pstDsBufSet2.StdBuf.ulBufLen, 0x00, pstDsBufSet2.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i < 8) {
            testSetAllField(&TblFieldDataDefSet2, i, 0, 6);
        }
        if (i > 7 && i < 14) {
            testSetAllField(&TblFieldDataDefSet2, i, 0, 7);
        }
        if (i >= 14) {
            testSetAllField(&TblFieldDataDefSet2, i);
        }
        memcpy_s(pucDataSet2, pstDsBufSet2.usRecLen, &TblFieldDataDefSet2, pstDsBufSet2.usRecLen);
        pstDsBufSet2.StdBuf.pucData = pucDataSet2;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId4, &pstDsBufSet2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet2);
    // 写表5数据
    recordCnt = 20000;
    DB_DSBUF_STRU pstDsBufSet3;
    pstDsBufSet3.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet3.usRecNum = 1;
    pstDsBufSet3.StdBuf.ulBufLen = pstDsBufSet3.usRecLen;
    pstDsBufSet3.StdBuf.ulActLen = pstDsBufSet3.usRecLen;
    VOS_UINT8 *pucDataSet3 = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet3.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet3, pstDsBufSet3.StdBuf.ulBufLen, 0x00, pstDsBufSet3.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet3;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet3, i);
        if (i < 8) {
            testSetAllField(&TblFieldDataDefSet3, i, 5, 6);
        }
        if (i > 7 && i < 14) {
            testSetAllField(&TblFieldDataDefSet3, i, 5, 7);
        }
        if (i >= 14) {
            testSetAllField(&TblFieldDataDefSet3, i);
        }
        memcpy_s(pucDataSet3, pstDsBufSet3.usRecLen, &TblFieldDataDefSet3, pstDsBufSet3.usRecLen);
        pstDsBufSet3.StdBuf.pucData = pucDataSet3;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId5, &pstDsBufSet3);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet3);
    TestFreeRelDef(&testRelDef);
    // 单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 20;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 从表1跳到表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 从表2跳到表3
    SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 1, field2, field3);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表3的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    uint32_t offset = 0;
    for (uint32_t i = 0; i < fetchCount; i++) {
        uint8_t *buf = (uint8_t *)pstBuff->dataBuf[0].pBuf + offset;
        if (i < 10) {
            V1_AW_MACRO_EXPECT_EQ_INT(6, *(uint32_t *)(buf));
        }
        if (i > 9 && i < 20) {
            V1_AW_MACRO_EXPECT_EQ_INT(7, *(uint32_t *)(buf));
        }
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)(buf + 4));
        offset += 24;
    }
    // 从表3跳到表4
    SetPathInfoToSingle(&nextedge, testRelId3, testRelId4, &pstFldFilter, 1, field3, field41);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表4的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    offset = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(14, pstBuff->dataBuf[0].ulRecNum);
    for (uint32_t i = 0; i < pstBuff->dataBuf[0].ulRecNum; i++) {
        uint8_t *buf = (uint8_t *)pstBuff->dataBuf[0].pBuf + offset;
        if (i < 8) {
            V1_AW_MACRO_EXPECT_EQ_INT(6, *(uint32_t *)(buf));
        }
        if (i > 7 && i < 14) {
            V1_AW_MACRO_EXPECT_EQ_INT(7, *(uint32_t *)(buf));
        }
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)(buf + 4));
        offset += 24;
    }
    // 从表4跳到表5
    SetPathInfoToSingle(&nextedge, testRelId4, testRelId5, &pstFldFilter, 1, field42, field5);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表5的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    offset = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(14, pstBuff->dataBuf[0].ulRecNum);
    for (uint32_t i = 0; i < pstBuff->dataBuf[0].ulRecNum; i++) {
        uint8_t *buf = (uint8_t *)pstBuff->dataBuf[0].pBuf + offset;
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)(buf));
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)(buf + 4));
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(VOS_INT8 *)(buf + 8));
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(VOS_INT8 *)(buf + 9));
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(int16_t *)(buf + 10));
        if (i < 8) {
            V1_AW_MACRO_EXPECT_EQ_INT(6, *(uint32_t *)(buf + 12));
        }
        if (i > 7 && i < 14) {
            V1_AW_MACRO_EXPECT_EQ_INT(7, *(uint32_t *)(buf + 12));
        }
        offset += 24;
    }
    TPC_FreeBufMemById(pstBuff->memId);
    // 结束查询
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 业务数据场景覆盖,进行全路径扫描
TEST_F(V1GraphQuery_function, V1Com_017_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3(设置索引字段为非唯一索引)
    uint16_t testRelId3 = 0;
    // 设置表3的索引
    VOS_UINT32 ulNIdxs = 1;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = {"idx"};
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = {DBDDL_INDEXTYPE_TTREE};
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = {0};
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = {1};
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = {{0}};
    DB_INDEX_DEF_STRU *pstIdxLst =
        testSetIdxInfo(ulNIdxs, aucIndexNameLst, enIndexTypeLst, ucUniqueFlagLst, ucIdxFldNum, aucFieldIDLst);
    TestCreateTblInitRelDef(&testRelDef, "table3", pstIdxLst, ulNIdxs);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(testRelDef.pstFldLst);
    // 建表5
    uint16_t testRelId5 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table5", pstIdxLst, ulNIdxs);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId5);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表4(设置两个索引字段)
    uint16_t testRelId4 = 0;
    // 设置表4的索引
    VOS_UINT32 ulNIdxs1 = 2;
    char aucIndexNameLst1[ulNIdxs1][DB_IDX_NAME_LEN] = {"idx1", "idx2"};
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst1[ulNIdxs1] = {DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE};
    VOS_UINT8 ucUniqueFlagLst1[ulNIdxs1] = {0, 1};
    VOS_UINT8 ucIdxFldNum1[ulNIdxs1] = {1, 1};
    VOS_UINT8 aucFieldIDLst1[ulNIdxs1][DB_IDX_FLD_MAX] = {{0}, {5}};
    DB_INDEX_DEF_STRU *pstIdxLst1 =
        testSetIdxInfo(ulNIdxs1, aucIndexNameLst1, enIndexTypeLst1, ucUniqueFlagLst1, ucIdxFldNum1, aucFieldIDLst1);
    TestCreateTblInitRelDef(&testRelDef, "table4", pstIdxLst1, ulNIdxs1);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    uint8_t field3[1] = {0};
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field2, field3);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表3-4
    DB_EDGE_DEF_STRU edgeDef3;
    uint8_t field41[1] = {0};
    TestCreateEdgeDef(&edgeDef3, true, testRelId3, testRelId4, 1, 1, field3, field41);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge3-4", &edgeDef3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表4-5
    DB_EDGE_DEF_STRU edgeDef4;
    uint8_t field42[1] = {5};
    uint8_t field5[1] = {0};
    TestCreateEdgeDef(&edgeDef4, true, testRelId4, testRelId5, 1, 1, field42, field5);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge4-5", &edgeDef4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写表1和表2数据，数据量为128k
    VOS_UINT32 recordCnt = 128000;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    // 写表3数据
    recordCnt = 1000;
    DB_DSBUF_STRU pstDsBufSet1;
    pstDsBufSet1.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet1.usRecNum = 1;
    pstDsBufSet1.StdBuf.ulBufLen = pstDsBufSet1.usRecLen;
    pstDsBufSet1.StdBuf.ulActLen = pstDsBufSet1.usRecLen;
    VOS_UINT8 *pucDataSet1 = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet1.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet1, pstDsBufSet1.StdBuf.ulBufLen, 0x00, pstDsBufSet1.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet1;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i < 10) {
            testSetAllField(&TblFieldDataDefSet1, i, 0, 6);
        }
        if (i > 9 && i < 20) {
            testSetAllField(&TblFieldDataDefSet1, i, 0, 7);
        }
        if (i >= 20) {
            testSetAllField(&TblFieldDataDefSet1, i);
        }
        memcpy_s(pucDataSet1, pstDsBufSet1.usRecLen, &TblFieldDataDefSet1, pstDsBufSet1.usRecLen);
        pstDsBufSet1.StdBuf.pucData = pucDataSet1;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet1);
    // 写表4数据
    recordCnt = 1000;
    DB_DSBUF_STRU pstDsBufSet2;
    pstDsBufSet2.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet2.usRecNum = 1;
    pstDsBufSet2.StdBuf.ulBufLen = pstDsBufSet2.usRecLen;
    pstDsBufSet2.StdBuf.ulActLen = pstDsBufSet2.usRecLen;
    VOS_UINT8 *pucDataSet2 = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet2.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet2, pstDsBufSet2.StdBuf.ulBufLen, 0x00, pstDsBufSet2.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i < 8) {
            testSetAllField(&TblFieldDataDefSet2, i, 0, 6);
        }
        if (i > 7 && i < 14) {
            testSetAllField(&TblFieldDataDefSet2, i, 0, 7);
        }
        if (i >= 14) {
            testSetAllField(&TblFieldDataDefSet2, i);
        }
        memcpy_s(pucDataSet2, pstDsBufSet2.usRecLen, &TblFieldDataDefSet2, pstDsBufSet2.usRecLen);
        pstDsBufSet2.StdBuf.pucData = pucDataSet2;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId4, &pstDsBufSet2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet2);
    // 写表5数据
    recordCnt = 20000;
    DB_DSBUF_STRU pstDsBufSet3;
    pstDsBufSet3.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet3.usRecNum = 1;
    pstDsBufSet3.StdBuf.ulBufLen = pstDsBufSet3.usRecLen;
    pstDsBufSet3.StdBuf.ulActLen = pstDsBufSet3.usRecLen;
    VOS_UINT8 *pucDataSet3 = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet3.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet3, pstDsBufSet3.StdBuf.ulBufLen, 0x00, pstDsBufSet3.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet3;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet3, i);
        if (i < 8) {
            testSetAllField(&TblFieldDataDefSet3, i, 5, 6);
        }
        if (i > 7 && i < 14) {
            testSetAllField(&TblFieldDataDefSet3, i, 5, 7);
        }
        if (i >= 14) {
            testSetAllField(&TblFieldDataDefSet3, i);
        }
        memcpy_s(pucDataSet3, pstDsBufSet3.usRecLen, &TblFieldDataDefSet3, pstDsBufSet3.usRecLen);
        pstDsBufSet3.StdBuf.pucData = pucDataSet3;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId5, &pstDsBufSet3);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet3);
    TestFreeRelDef(&testRelDef);

    // 使用全路径扫描查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(int64_t *)pstCond.aCond[0].aucValue = 6;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edge[2].edgeInfo[0] = edgeDef3.relInfos[0];
    pstPath.edge[2].edgeInfo[1] = edgeDef3.relInfos[1];
    pstPath.edge[2].pstFldFilter = &pstFldFilter;
    pstPath.edge[3].edgeInfo[0] = edgeDef4.relInfos[0];
    pstPath.edge[3].edgeInfo[1] = edgeDef4.relInfos[1];
    pstPath.edge[3].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 4;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    uint32_t offset = 0;
    for (VOS_UINT32 i = 0; i < pstBuff->realRelNum; i++) {
        if (i < 2) {
            V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuff->dataBuf[i].ulRecNum);
            uint8_t *buf0 = (uint8_t *)pstBuff->dataBuf[i].pBuf;
            V1_AW_MACRO_EXPECT_EQ_INT(6, *(uint32_t *)(buf0));
            V1_AW_MACRO_EXPECT_EQ_INT(6, *(uint32_t *)(buf0 + 4));
            V1_AW_MACRO_EXPECT_EQ_INT(6, *(VOS_INT8 *)(buf0 + 8));
        }
        if (i == 2) {
            offset = 0;
            for (uint32_t j = 0; j < 10; j++) {
                uint8_t *buf = (uint8_t *)pstBuff->dataBuf[i].pBuf + offset;
                V1_AW_MACRO_EXPECT_EQ_INT(6, *(uint32_t *)(buf));
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(uint32_t *)(buf + 4));
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(VOS_INT8 *)(buf + 8));
                offset += 24;
            }
        }
        if (i == 3) {
            offset = 0;
            for (uint32_t j = 0; j < 8; j++) {
                uint8_t *buf1 = (uint8_t *)pstBuff->dataBuf[i].pBuf + offset;
                V1_AW_MACRO_EXPECT_EQ_INT(6, *(uint32_t *)(buf1));
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(uint32_t *)(buf1 + 4));
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(VOS_INT8 *)(buf1 + 8));
                offset += 24;
            }
        }
        if (i == 4) {
            offset = 0;
            for (uint32_t j = 0; j < 8; j++) {
                uint8_t *buf2 = (uint8_t *)pstBuff->dataBuf[i].pBuf + offset;
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(uint32_t *)(buf2));
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(uint32_t *)(buf2 + 4));
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(VOS_INT8 *)(buf2 + 8));
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(VOS_INT8 *)(buf2 + 9));
                V1_AW_MACRO_EXPECT_EQ_INT(j, *(int16_t *)(buf2 + 10));
                V1_AW_MACRO_EXPECT_EQ_INT(6, *(uint32_t *)(buf2 + 12));
                offset += 24;
            }
        }
    }
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
}
// 最开始B表中有数据，但没有满足建边条件的数据，查询B表数据，更新数据使边表成立，查询B表数据，删除数据使边表不成立，查询B表数据
TEST_F(V1GraphQuery_function, V1Com_017_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 设置表2的值
    testSetAllField(&TblFieldDataDefSet, 1, 5, 11);
    memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
    pstDsBufSet.StdBuf.pucData = pucDataSet;
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(pucDataSet);

    // 使用单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳到表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(0, 24, pstBuff, &pstFldFilter);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新表2数据，使得边表成立
    DB_COND_STRU pstCond1;
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 1;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;
    *(VOS_INT32 *)pstCond1.aCond[0].aucValue = 1;

    DB_DSBUF_STRU pstDsBufSet1;
    pstDsBufSet1.usRecLen = 24;
    pstDsBufSet1.usRecNum = 1;
    pstDsBufSet1.StdBuf.ulBufLen = pstDsBufSet1.usRecLen;
    pstDsBufSet1.StdBuf.ulActLen = pstDsBufSet1.usRecLen;
    VOS_UINT8 *pucDataSet1 = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet1.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet1, pstDsBufSet1.StdBuf.ulBufLen, 0x00, pstDsBufSet1.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet1;
    testSetAllField(&TblFieldDataDefSet1, 1);
    memcpy_s(pucDataSet1, pstDsBufSet1.usRecLen, &TblFieldDataDefSet1, pstDsBufSet1.usRecLen);
    pstDsBufSet1.StdBuf.pucData = pucDataSet1;
    VOS_UINT32 udpRecNum = 0;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstCond1, &pstFldFilter, &pstDsBufSet1, &udpRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 使用单跳查询
    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳到表2
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    uint8_t *buf = (uint8_t *)pstBuff->dataBuf[0].pBuf;
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, pstBuff->dataBuf[0].relId);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuff->dataBuf[0].ulRecNum);
    if (pstBuff->dataBuf[0].ulRecNum > 0) {
        V1_AW_MACRO_EXPECT_EQ_INT(1, *(uint32_t *)(buf));
        V1_AW_MACRO_EXPECT_EQ_INT(1, *(uint32_t *)(buf + 4));
        V1_AW_MACRO_EXPECT_EQ_INT(1, *(VOS_INT8 *)(buf + 8));
        V1_AW_MACRO_EXPECT_EQ_INT(1, *(VOS_INT8 *)(buf + 9));
        V1_AW_MACRO_EXPECT_EQ_INT(1, *(int16_t *)(buf + 10));
        V1_AW_MACRO_EXPECT_EQ_INT(1, *(uint32_t *)(buf + 12));
        V1_AW_MACRO_EXPECT_EQ_INT(1, *(int64_t *)(buf + 16));
    }
    TEST_V1_FREE(pucDataSet1);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除表2数据
    DB_COND_STRU pstCond2;
    pstCond2.usCondNum = 1;
    pstCond2.aCond[0].ucFieldId = 1;
    pstCond2.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond2.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond2.aCond[0].aucValue = 1;
    VOS_UINT32 deleteRecNum = 0;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstCond2, &deleteRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 使用单跳查询
    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳到表2
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestFreeRelDef(&testRelDef);
    TestDqlCheckData(0, 24, pstBuff, &pstFldFilter);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 使用所有可以建立边表的数据类型进行建边，建边后进行单跳查询
TEST_F(V1GraphQuery_function, V1Com_017_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = {"idx1", "idx2"};
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = {DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE};
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = {1, 1};
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = {18, 9};
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = {
        {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17}, {18, 19, 20, 21, 22, 23, 24, 25, 26}};
    DB_INDEX_DEF_STRU *pstIdxLst =
        testSetIdxInfo(ulNIdxs, aucIndexNameLst, enIndexTypeLst, ucUniqueFlagLst, ucIdxFldNum, aucFieldIDLst);

    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefWithIdxLegality(&testRelDef, 2, "table1", pstIdxLst);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(testRelDef.pstFldLst);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefWithIdxLegality(&testRelDef, 2, "table2", pstIdxLst);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(testRelDef.pstFldLst);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefWithIdxLegality(&testRelDef, 2, "table3", pstIdxLst);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[18] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17};
    uint8_t field2[18] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 18, 18, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    uint8_t field22[9] = {18, 19, 20, 21, 22, 23, 24, 25, 26};
    uint8_t field3[9] = {18, 19, 20, 21, 22, 23, 24, 25, 26};
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 9, 9, field22, field3);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 1;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 28);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        InsertAllType(pucDataSet, testRelDef.pstFldLst, i, pstDsBufSet.usRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = pstDsBufSet.usRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = pstDsBufSet.usRecLen, .ulActLen = pstDsBufSet.usRecLen, .pucData = pucDataSet}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳到表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 18, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表2跳到表3
    SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 9, field22, field3);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表3的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeBufMemById(pstBuff->memId);
    // 结束查询
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 使用所有可以建立边表的数据类型进行建边，建边后进行全路径查询
TEST_F(V1GraphQuery_function, V1Com_017_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = {"idx1", "idx2"};
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = {DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE};
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = {1, 1};
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = {18, 9};
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = {
        {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17}, {18, 19, 20, 21, 22, 23, 24, 25, 26}};
    DB_INDEX_DEF_STRU *pstIdxLst =
        testSetIdxInfo(ulNIdxs, aucIndexNameLst, enIndexTypeLst, ucUniqueFlagLst, ucIdxFldNum, aucFieldIDLst);

    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefWithIdxLegality(&testRelDef, 2, "table1", pstIdxLst);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(testRelDef.pstFldLst);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDefWithIdxLegality(&testRelDef, 2, "table2", pstIdxLst);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(testRelDef.pstFldLst);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDefWithIdxLegality(&testRelDef, 2, "table3", pstIdxLst);
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[18] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17};
    uint8_t field2[18] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 18, 18, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    uint8_t field22[9] = {18, 19, 20, 21, 22, 23, 24, 25, 26};
    uint8_t field3[9] = {18, 19, 20, 21, 22, 23, 24, 25, 26};
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 9, 9, field22, field3);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 1;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 28);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        InsertAllType(pucDataSet, testRelDef.pstFldLst, i, pstDsBufSet.usRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = pstDsBufSet.usRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = pstDsBufSet.usRecLen, .ulActLen = pstDsBufSet.usRecLen, .pucData = pucDataSet}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
}
// 创建A-B-C-A图，使用单跳查询，查询到第一张表的数据后，再通过下一跳回到第一张表进行数据查询，再通过回跳回到第一张表进行数据查询
TEST_F(V1GraphQuery_function, V1Com_017_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-3
    DB_EDGE_DEF_STRU edgeDef3;
    TestCreateEdgeDef(&edgeDef3, true, testRelId1, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-3", &edgeDef3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 从表1跳回表1
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    SetPathInfoToSingle(&nextedge, testRelId3, testRelId1, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 回跳到表1
    ret = TPC_MoveBack(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_MoveBack(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_MoveBack(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter, 5);

    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C图，使用全路径扫描获取数据
TEST_F(V1GraphQuery_function, V1Com_017_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 2;
    pstFldFilter.aucField[0] = 1;
    pstFldFilter.aucField[1] = 3;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(10, 5, pstBuff, &pstFldFilter);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
    // 删边
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge1-2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge2-3");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C图，多次使用全路径扫描获取数据，获取结果一致
TEST_F(V1GraphQuery_function, V1Com_017_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    for (int i = 0; i < 4; i++) {
        ret =
            TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 验证返回结果
        TestDqlCheckData(10, 24, pstBuff, &pstFldFilter);
        // 释放结果占用的内存
        TPC_FreeBufMemById(pstBuff->memId);
    }
}
// 创建A-B-C图，使用单跳查询获取每个表的数据
TEST_F(V1GraphQuery_function, V1Com_017_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 2;
    pstFldFilter.aucField[0] = 1;
    pstFldFilter.aucField[1] = 3;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 5, pstBuff, &pstFldFilter);
    // 从表1跳到表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 5, pstBuff, &pstFldFilter);
    // 从表2跳到表3
    SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表3的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 5, pstBuff, &pstFldFilter);
    TPC_FreeBufMemById(pstBuff->memId);
    // 结束查询
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C图，使用单跳查询在两个字段间多次跳转，并查询数据
TEST_F(V1GraphQuery_function, V1Com_017_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    DB_EDGE_CON_STRU nextedge;
    for (int i = 0; i < 5; i++) {
        // 从表1跳到表2
        SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
        ret = TPC_MoveNextByEdge(phSelect, nextedge);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 查询表2的值
        ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 验证返回结果
        if (i == 0) {
            TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
        }
        if (i == 1) {
            TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter, 5);
        }
        if (i > 1) {
            TestDqlCheckData(0, 24, pstBuff, &pstFldFilter);
        }
        // 从表2回跳到表1
        ret = TPC_MoveBack(phSelect);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 查询表1的值
        ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 验证返回结果
        if (i == 0) {
            TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter, 5);
        } else {
            TestDqlCheckData(0, 24, pstBuff, &pstFldFilter);
        }
    }

    TPC_FreeBufMemById(pstBuff->memId);
    // 结束查询
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C图，多次使用单跳查询获取每个表的数据
TEST_F(V1GraphQuery_function, V1Com_017_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;
    VOS_UINT32 fetchCount = 5;
    DB_EDGE_CON_STRU nextedge;

    for (int i = 0; i < 4; i++) {
        ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 查询表1的值
        ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 验证返回结果
        TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
        // 从表1跳到表2
        SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
        ret = TPC_MoveNextByEdge(phSelect, nextedge);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 查询表2的值
        ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 验证返回结果
        TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
        // 从表2跳到表3
        SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 1, field1, field2);
        ret = TPC_MoveNextByEdge(phSelect, nextedge);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 查询表3的值
        ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 验证返回结果
        TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
        TPC_FreeBufMemById(pstBuff->memId);
        // 结束查询
        ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 创建A-B-C和A-B-D图，使用单跳查询获取每个表的数据
TEST_F(V1GraphQuery_function, V1Com_017_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表4
    uint16_t testRelId4 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table4");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-4
    DB_EDGE_DEF_STRU edgeDef3;
    TestCreateEdgeDef(&edgeDef3, true, testRelId2, testRelId4, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-4", &edgeDef3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId4, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;
    VOS_UINT32 fetchCount = 5;
    DB_EDGE_CON_STRU nextedge;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 从表1跳到表2
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 从表2跳到表3
    SetPathInfoToSingle(&nextedge, testRelId2, testRelId3, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表3的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 表3回跳到表2,再跳表4
    ret = TPC_MoveBack(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    SetPathInfoToSingle(&nextedge, testRelId2, testRelId4, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表4的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);

    TPC_FreeBufMemById(pstBuff->memId);
    // 结束查询
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C和A-B-D图，使用全路径扫描获取每个表的数据
TEST_F(V1GraphQuery_function, V1Com_017_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表4
    uint16_t testRelId4 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table4");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-4
    DB_EDGE_DEF_STRU edgeDef3;
    TestCreateEdgeDef(&edgeDef3, true, testRelId2, testRelId4, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-4", &edgeDef3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId4, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);
    // 使用全路径扫描查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径A-B-C
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(10, 24, pstBuff, &pstFldFilter);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);

    // 设置查询路径A-B-D
    DB_PATH_STRU pstPath1;
    pstPath1.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath1.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath1.edge[0].pstFldFilter = &pstFldFilter;
    pstPath1.edge[1].edgeInfo[0] = edgeDef3.relInfos[0];
    pstPath1.edge[1].edgeInfo[1] = edgeDef3.relInfos[1];
    pstPath1.edge[1].pstFldFilter = &pstFldFilter;
    pstPath1.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff1 = NULL;

    ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath1, &pstBuff1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(10, 24, pstBuff1, &pstFldFilter);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff1->memId);
}
void *pathQuery(void *args)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    QueryPathArgsT *queryPathArg = (QueryPathArgsT *)args;
    // 使用全路径扫描查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = queryPathArg->edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = queryPathArg->edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = queryPathArg->edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = queryPathArg->edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_SelectAllRecByPath(
        TPC_GLOBAL_CDB, g_testDbId, queryPathArg->testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(10, 24, pstBuff, &pstFldFilter);
    // 释放结果占用的内存
    TPC_FreeBufMemById(pstBuff->memId);
}
void *singleQuery(void *args)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    QueryArgsT *queryArg = (QueryArgsT *)args;
    // 单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;
    VOS_UINT32 fetchCount = 5;
    DB_EDGE_CON_STRU nextedge;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, queryArg->testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 从表1跳到表2
    SetPathInfoToSingle(
        &nextedge, queryArg->testRelId1, queryArg->testRelId2, &pstFldFilter, 1, queryArg->field1, queryArg->field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);
    // 从表2跳到表3
    SetPathInfoToSingle(
        &nextedge, queryArg->testRelId2, queryArg->testRelId3, &pstFldFilter, 1, queryArg->field1, queryArg->field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表3的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证返回结果
    TestDqlCheckData(fetchCount, 24, pstBuff, &pstFldFilter);

    TPC_FreeBufMemById(pstBuff->memId);
    // 结束查询
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C图，并发两个线程，一个进行全路径扫描，一个进行单跳查询
TEST_F(V1GraphQuery_function, V1Com_017_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);

    // 并发执行，一个进行全路径扫描，一个进行单跳查询
    pthread_t tid1, tid2;
    QueryArgsT queryArg;
    QueryPathArgsT queryPathArg;
    queryArg.testRelId1 = testRelId1;
    queryArg.testRelId2 = testRelId2;
    queryArg.testRelId3 = testRelId3;
    queryArg.field1 = field1;
    queryArg.field2 = field2;
    queryPathArg.testRelId1 = testRelId1;
    queryPathArg.edgeDef1 = edgeDef1;
    queryPathArg.edgeDef2 = edgeDef2;
    ret = pthread_create(&tid1, NULL, singleQuery, &queryArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid2, NULL, pathQuery, &queryPathArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = pthread_join(tid1, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid2, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C图，并发两个线程，都进行全路径扫描
TEST_F(V1GraphQuery_function, V1Com_017_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);

    // 并发执行，都进行全路径扫描
    pthread_t tid1, tid2;
    QueryPathArgsT queryPathArg;
    queryPathArg.testRelId1 = testRelId1;
    queryPathArg.edgeDef1 = edgeDef1;
    queryPathArg.edgeDef2 = edgeDef2;
    ret = pthread_create(&tid1, NULL, pathQuery, &queryPathArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid2, NULL, pathQuery, &queryPathArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = pthread_join(tid1, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid2, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 创建A-B-C图，并发两个线程，都进行单跳查询
TEST_F(V1GraphQuery_function, V1Com_017_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, "table1");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表2
    uint16_t testRelId2 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table2");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);
    // 建表3
    uint16_t testRelId3 = 0;
    TestCreateTblInitRelDef(&testRelDef, "table3");
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(testRelDef.pstFldLst, 7);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId2, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId3, &pstDsBufSet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(pucDataSet);
    TestFreeRelDef(&testRelDef);

    // 并发执行，都进行单跳查询
    pthread_t tid1, tid2;
    QueryArgsT queryArg;
    queryArg.testRelId1 = testRelId1;
    queryArg.testRelId2 = testRelId2;
    queryArg.testRelId3 = testRelId3;
    queryArg.field1 = field1;
    queryArg.field2 = field2;
    ret = pthread_create(&tid1, NULL, singleQuery, &queryArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid2, NULL, singleQuery, &queryArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = pthread_join(tid1, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid2, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
