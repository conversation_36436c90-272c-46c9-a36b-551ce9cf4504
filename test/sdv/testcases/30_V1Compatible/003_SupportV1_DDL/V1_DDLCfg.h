/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: duhu
 * Create: 2024-07-16
 */

#ifndef V1DDLCFG_H
#define V1DDLCFG_H
#include "t_rd_simplerel.h"

using namespace std;

uint32_t g_testDbId = 0;
DB_INST_CONFIG_STRU g_testDbCfg = {0};
const char g_testDbName[DB_NAME_LEN] = "testDdl";

void TestCreateTblInitRelDef(DB_REL_DEF_STRU *stRelDef, const char *tblName, bool isExecut = true)
{
    const char tableName[] = "table";
    const char indexName[] = "index";
    const uint32_t fldNum = 5;
    const uint32_t idxNum = 1;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 4, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 3;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    if (isExecut) {
    tblName == NULL ? (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN) :
                      (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    } else {
        (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    }
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestFreeRelDef(DB_REL_DEF_STRU *stRelDef)
{
    if (stRelDef->pstFldLst != NULL) {
        TEST_V1_FREE(stRelDef->pstFldLst);
        stRelDef->pstFldLst = NULL;
    }
    if (stRelDef->pstIdxLst != NULL) {
        TEST_V1_FREE(stRelDef->pstIdxLst);
        stRelDef->pstIdxLst = NULL;
    }
}

void TestCreateTblInitRelDefWithFld(DB_REL_DEF_STRU *stRelDef, const char *fldName, const char *fldName1)
{
    const char tableName[] = "table";
    const char indexName[] = "index";
    const uint32_t fldNum = 2;
    const uint32_t idxNum = 1;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8};
    const uint32_t fldSizes[fldNum] = {4, 1};
    //  initialize field definition
    (void)memcpy_s((char *)astFlds[0].aucFieldName, DB_NAME_LEN, fldName, DB_NAME_LEN);
    astFlds[0].enDataType = dataTypes[0];
    astFlds[0].usSize = fldSizes[0];
    (void)memcpy_s((char *)astFlds[1].aucFieldName, DB_NAME_LEN, fldName1, DB_NAME_LEN);
    astFlds[1].enDataType = dataTypes[1];
    astFlds[1].usSize = fldSizes[1];
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 0;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestCreateTblInitRelDefWithfldNum(DB_REL_DEF_STRU *stRelDef, uint32_t fldNum)
{
    const char tableName[] = "table";
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32};
    uint32_t fldSizes[fldNum] = {4};
    for (uint32_t i = 0; i < fldNum; i++) {
        dataTypes[i] = DBT_UINT32;
        fldSizes[i] = 4;
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = 0;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

void TestCreateTblInitRelDefWithInvalidFld(DB_REL_DEF_STRU *stRelDef)
{
    const char tableName[] = "table";
    const uint32_t fldNum = 6;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {
        DBT_VSTRING, DBT_RESERVED1, DBT_RESERVED2, DBT_RESERVED3, DBT_RESERVED4, DBT_BUTT};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 3, 4, 6};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = 0;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

void TestCreateTblInitRelDefWithValidFld(DB_REL_DEF_STRU *stRelDef)
{
    const char tableName[] = "table";
    const uint32_t fldNum = 27;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES,
        DBT_TIME, DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    const uint32_t fldSizes[fldNum] = {
        4, 4, 8, 4, 8, 4, 3, 4, 4, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 4, 4};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = 0;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

void TestCreateTblInitRelDefWithFldOfLength(
    DB_REL_DEF_STRU *stRelDef, DB_DATATYPE_ENUM_V1 *dataTypes, uint32_t *fldSizes, uint32_t fldNum)
{
    const char tableName[] = "table";
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
        astFlds[i].ulDefVal = 0xffffffff;
    }
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = 0;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

void TestCreateTblInitRelDefWithTableType(DB_REL_DEF_STRU *stRelDef,  DB_TABLE_TYPE_ENUM enTableType)
{
    const char tableName[] = "table";
    const uint32_t fldNum = 5;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 4, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = enTableType;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = 0;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

void TestCreateTblInitRelDefWithNumberOfRecords(DB_REL_DEF_STRU *stRelDef,  uint32_t ulIntialSize, uint32_t ulMaxSize)
{
    const char tableName[] = "table";
    const uint32_t fldNum = 5;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 4, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = ulIntialSize;
    stRelDef->ulMaxSize = ulMaxSize;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = 0;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

void TestCreateTblInitRelDefWithIdxName(DB_REL_DEF_STRU *stRelDef, const char *indexName, const char *indexName1)
{
    const char tableName[] = "table";
    const uint32_t fldNum = 5;
    const uint32_t idxNum = 2;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 4, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    //  initialize index definition
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 0;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    astIdx[1].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[1].aucIndexName, DB_NAME_LEN, indexName1, DB_NAME_LEN);
    astIdx[1].ucIdxFldNum = 1;
    astIdx[1].aucFieldID[0] = 1;
    astIdx[1].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestCreateTblInitRelDefWithIdxType(DB_REL_DEF_STRU *stRelDef, DBDDL_INDEXTYPE_ENUM enIndexType)
{
    const char tableName[] = "table";
    const char indexName[] = "index";
    const uint32_t fldNum = 5;
    const uint32_t idxNum = 1;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 4, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 0;
    astIdx[0].enIndexType = enIndexType;
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestCreateTblInitRelDefWithIdxUnique(DB_REL_DEF_STRU *stRelDef, uint8_t ucUniqueFlag)
{
    const char tableName[] = "table";
    const char indexName[] = "index";
    const uint32_t fldNum = 5;
    const uint32_t idxNum = 1;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 4, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = ucUniqueFlag;
    (void)memcpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 0;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestCreateTblInitRelDefWithIdxNum(DB_REL_DEF_STRU *stRelDef, uint32_t idxNum)
{
    const char tableName[] = "table";
    const uint32_t fldNum = 253;
    
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32};
    uint32_t fldSizes[fldNum] = {4};
    for (uint32_t i = 0; i < fldNum; i++) {
        dataTypes[i] = DBT_UINT32;
        fldSizes[i] = 4;
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(idxNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    for (uint32_t i = 0; i < (idxNum - 2); i++) {
        astIdx[i].ucUniqueFlag = 1;
        (void)sprintf_s((char *)astIdx[i].aucIndexName, DB_NAME_LEN, "index%d", i);
        astIdx[i].ucIdxFldNum = 1;
        astIdx[i].aucFieldID[0] = i;
        astIdx[i].enIndexType = DBDDL_INDEXTYPE_TTREE;
    }
    astIdx[253].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[253].aucIndexName, DB_NAME_LEN, "index253", DB_NAME_LEN);
    astIdx[253].ucIdxFldNum = 2;
    astIdx[253].aucFieldID[0] = 0;
    astIdx[253].aucFieldID[1] = 1;
    astIdx[253].enIndexType = DBDDL_INDEXTYPE_TTREE;
    astIdx[254].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[254].aucIndexName, DB_NAME_LEN, "index254", DB_NAME_LEN);
    astIdx[254].ucIdxFldNum = 2;
    astIdx[254].aucFieldID[0] = 2;
    astIdx[254].aucFieldID[1] = 3;
    astIdx[254].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestCreateTblInitRelDefWithIdxNumOver(DB_REL_DEF_STRU *stRelDef, uint32_t idxNum)
{
    const char tableName[] = "table";
    const uint32_t fldNum = 253;
    
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32};
    uint32_t fldSizes[fldNum] = {4};
    for (uint32_t i = 0; i < fldNum; i++) {
        dataTypes[i] = DBT_UINT32;
        fldSizes[i] = 4;
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(idxNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    for (uint32_t i = 0; i < (idxNum - 3); i++) {
        astIdx[i].ucUniqueFlag = 1;
        (void)sprintf_s((char *)astIdx[i].aucIndexName, DB_NAME_LEN, "index%d", i);
        astIdx[i].ucIdxFldNum = 1;
        astIdx[i].aucFieldID[0] = i;
        astIdx[i].enIndexType = DBDDL_INDEXTYPE_TTREE;
    }
    astIdx[253].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[253].aucIndexName, DB_NAME_LEN, "index253", DB_NAME_LEN);
    astIdx[253].ucIdxFldNum = 2;
    astIdx[253].aucFieldID[0] = 0;
    astIdx[253].aucFieldID[1] = 1;
    astIdx[253].enIndexType = DBDDL_INDEXTYPE_TTREE;
    astIdx[254].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[254].aucIndexName, DB_NAME_LEN, "index254", DB_NAME_LEN);
    astIdx[254].ucIdxFldNum = 2;
    astIdx[254].aucFieldID[0] = 2;
    astIdx[254].aucFieldID[1] = 3;
    astIdx[254].enIndexType = DBDDL_INDEXTYPE_TTREE;
    astIdx[255].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[255].aucIndexName, DB_NAME_LEN, "index255", DB_NAME_LEN);
    astIdx[255].ucIdxFldNum = 2;
    astIdx[255].aucFieldID[0] = 4;
    astIdx[255].aucFieldID[1] = 5;
    astIdx[255].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestCreateTblInitRelDefWithIdxRepFld(DB_REL_DEF_STRU *stRelDef)
{
    const char tableName[] = "table";
    const char indexName[] = "index";
    const char indexName1[] = "index1";
    const uint32_t fldNum = 5;
    const uint32_t idxNum = 2;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 4, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    //  initialize index definition
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 0;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    astIdx[1].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[1].aucIndexName, DB_NAME_LEN, indexName1, DB_NAME_LEN);
    astIdx[1].ucIdxFldNum = 1;
    astIdx[1].aucFieldID[0] = 0;
    astIdx[1].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestCreateTblInitRelDefWithIdxRepFld2(DB_REL_DEF_STRU *stRelDef)
{
    const char tableName[] = "table";
    const char indexName[] = "index";
    const char indexName1[] = "index1";
    const uint32_t fldNum = 5;
    const uint32_t idxNum = 2;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64};
    const uint32_t fldSizes[fldNum] = {4, 1, 2, 4, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    //  initialize index definition
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 5;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    astIdx[1].ucUniqueFlag = 1;
    (void)memcpy_s((char *)astIdx[1].aucIndexName, DB_NAME_LEN, indexName1, DB_NAME_LEN);
    astIdx[1].ucIdxFldNum = 1;
    astIdx[1].aucFieldID[0] = 6;
    astIdx[1].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void TestCreateTblInitRelDefWithIdxFldNum(DB_REL_DEF_STRU *stRelDef, uint8_t ucIdxFldNum)
{
    const char tableName[] = "table";
    const char indexName[] = "index";
    const uint32_t fldNum = 30;
    const uint32_t idxNum = 1;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32};
    uint32_t fldSizes[fldNum] = {4};
    for (uint32_t i = 0; i < fldNum; i++) {
        dataTypes[i] = DBT_UINT32;
        fldSizes[i] = 4;
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    if (ucIdxFldNum > 0) {
        astIdx[0].ucUniqueFlag = 1;
        (void)memcpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
        astIdx[0].ucIdxFldNum = ucIdxFldNum;
        for (uint32_t i = 0; i < ucIdxFldNum; i++) {
            astIdx[0].aucFieldID[i] = i;
        }
        astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    }
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = ucIdxFldNum == 0 ? 0 : idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = ucIdxFldNum == 0 ? NULL : astIdx;
    if (ucIdxFldNum == 0) {
        TEST_V1_FREE(astIdx);
        astIdx = NULL;
    }
}

void TestCreateTblInitRelDefWithIdxLegality(DB_REL_DEF_STRU *stRelDef, uint32_t idxNum)
{
    const char tableName[] = "table";
    const uint32_t fldNum = 27;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds ==NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES, DBT_BIT};
    const uint32_t fldSizes[fldNum] = {
        4, 4, 8, 8, 4, 3, 4, 4, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 4, 4, 4};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
        astFlds[i].ulDefVal = 0xffffffff;
    }
    DB_INDEX_DEF_STRU *astIdx = NULL;
    astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx ==NULL) {
        return;
    }
    for (uint32_t i = 0; i < idxNum; i++) {
        astIdx[i].ucUniqueFlag = 1;
        (void)sprintf_s((char *)astIdx[i].aucIndexName, DB_NAME_LEN, "index%d", i);
        astIdx[i].ucIdxFldNum = 1;
        astIdx[i].aucFieldID[0] = i;
        astIdx[i].enIndexType = DBDDL_INDEXTYPE_TTREE;
    }
    (void)memcpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 10000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

#endif
