/*
========================================= 基本功能 =========================================
1 普通DB
001 同一个DB只含所有数据类型表的导入导出，导出后closeDB再openDB导入，预期导入导出成功
002 同一个DB含表和数据的导入导出，导出后closeDB再openDB导入，预期导入导出成功
003 同一个DB只含临时表的导入导出，closeDB后再openDB导入，预期DB不含临时表
004 同一个DB只含所有数据类型表的循环导入导出，预期导入导出成功
005 同一个DB导入导出后进行dml，预期成功
006 同一个DB每一步都导出，预期成功
007 beginCDB后dml操作，未提交事务导出，提交事务再查询数据修改成功，导入后的数据不含CDB修改
008 不同DB含所有数据类型表的导入导出，预期导入导出成功
009 不同DB含表和数据的导入导出，预期导入导出成功
010 不同DB只含临时表的导入导出，预期DB2不含临时表
011 不同DB只含所有数据类型表的循环导入导出，预期导入导出成功
012 不同DB导入导出后dml操作
013 不同DB每一步都导出l后导入，预期成功
014 导入后获取desc信息，DB2设置一些desc
2 持久化DB
015 同一个持久化DB只含所有数据类型表的导入导出，导出后closeDB再openDB导入，预期导入导出成功
016 同一个持久化DB含表和数据的导入导出，导出后closeDB再openDB导入，预期导入导出成功
017 同一个持久化DB只含临时表的导入导出，closeDB后再openDB导入，预期DB不含临时表
018 同一个DB只含所有数据类型表的循环导入导出，预期导入导出成功
019 不同持久化DB只含所有数据类型表的导入导出，预期导入导出成功
020 不同持久化DB含表和数据的导入导出，预期导入导出成功
021 不同持久化DB只含临时表的导入导出，预期DB2不含临时表
022 不同DB只含所有数据类型表的循环导入导出，预期导入导出成功
023 自定义数据类型的导入导出，预期导入成功
024 导出后，重新初始化，再导入，预期导入失败，创建自定义数据类型后再导入，预期导入成功
3 普通DB和持久化DB导入导出
025 普通DB只含表导入持久化DB，预期导入导出成功
026 普通DB含表和数据导入持久化DB，预期导入导出成功
027 普通DB只含临时表导入持久化DB，预期DB2不含临时表
028 普通DB导入持久化DB完成closeDB后再次open持久化DB，预期含表
029 持久化DB只含表导入普通DB，预期导入导出成功
030 持久化DB只含表和数据导入普通DB，预期导入导出成功
031 持久化DB只含临时表导入普通DB，预期DB2不含临时表
032 持久化DB导入普通DB完成closeDB后再次openDB，预期不含表
*/

#include "ImportExport.h"

class ImportExportFunction : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ImportExportFunction::SetUpTestCase()
{}

void ImportExportFunction::TearDownTestCase()
{}

void ImportExportFunction::SetUp()
{
    // 初始化
    int ret = TestTPC_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.txt");
    system("rm -rf ./perFilePath/*.txt");
    system("rm -rf ./filePath/output");
    AW_CHECK_LOG_BEGIN();
}

void ImportExportFunction::TearDown()
{
    AW_CHECK_LOG_END();
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.txt");
    system("rm -rf ./perFilePath/*.txt");
    system("rm -rf ./filePath/output");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
}

// 1 普通DB
// 001 同一个DB只含所有数据类型表的导入导出，导出后closeDB再openDB导入，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(dbID, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 同一个DB含表和数据的导入导出，导出后closeDB再openDB导入，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(dbID, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(insertNum, pulActRec);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 同一个DB只含临时表的导入导出，closeDB后再openDB导入，预期DB不含临时表
TEST_F(ImportExportFunction, V1Com_012_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(dbID, "schemaFile/table_test_temp_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char filePath2[64] = "./filePath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath2, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_NE_STR("label1", (char *)resultBuff[0]);  // 没有表
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulTblCount);
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 同一个DB只含所有数据类型表的循环导入导出，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表、写数据、校验数据
    TestCreateTblInsertSelect();

    // 循环导入导出
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    for (uint32_t i = 0; i < 10; i++) {
        ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 同一个DB导入导出后进行dml，预期成功
TEST_F(ImportExportFunction, V1Com_012_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(dbID, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};

    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入后再dml
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 同一个DB每一步都导出，预期成功
TEST_F(ImportExportFunction, V1Com_012_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(dbID, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表后导出
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};

    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写数据后导出
    char filePath2[64] = "./filePath/export2.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // 查询数据后导出
    char filePath3[64] = "./filePath/export3.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表后导出
    char filePath4[64] = "./filePath/export4.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 beginCDB后dml操作，未提交事务导出，提交事务再查询数据修改成功，导入后的数据不含CDB修改
TEST_F(ImportExportFunction, V1Com_012_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    system("rm -rf ./filePath/export.txt");
    // 写数据后导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulActRec);  // 事务提交前导出，没有数据

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// DISCARD、REPLACE
// 008 不同DB含所有数据类型表的导入导出，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLose、DropDB1
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // db不存在导入，创建db
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 不同DB含表和数据的导入导出，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入，db不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // OpenDB后查看表
    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID2, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(insertNum, pulActRec);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 不同DB只含临时表的导入导出，预期DB2不含临时表
TEST_F(ImportExportFunction, V1Com_012_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum, 2000, true);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName2[20] = "dbName2";
    char filePath2[64] = "./filePath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_NE_STR("label1", (char *)resultBuff[0]);  // 没有表
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulTblCount);
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID2, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 不同DB只含所有数据类型表的循环导入导出，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 循环导入导出
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    for (uint32_t i = 0; i < 10; i++) {
        ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 不同DB导入导出后dml操作
TEST_F(ImportExportFunction, V1Com_012_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};

    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入后再dml
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID2, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID2, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 不同DB每一步都导出l后导入，预期成功
TEST_F(ImportExportFunction, V1Com_012_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /******************1 建表后导出 ****************/
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入到另一DB
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // OpenDB
    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表名
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./filePath/output";
    sysviewArgs.ulDbId = dbID2;
    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &sysviewArgs, &result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表名和数据量
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/output");
    ret = executeCommand(command, "tbname: label1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放result
    TPC_FreeSysviewResult(&result);
    system("rm -rf ./filePath/output");

    /******************2 写数据后导出导入 ****************/
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写数据后导出
    char filePath2[64] = "./filePath/export2.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    ret = TPC_Restore((uint8_t *)filePath2, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名和数据
    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &sysviewArgs, &result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表名和数据量
    ret = executeCommand(command, "tbname: label1", "count: 1000");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放result
    TPC_FreeSysviewResult(&result);
    system("rm -rf ./filePath/output");

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // 查询数据后导出
    char filePath3[64] = "./filePath/export3.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /******************3 删表后导出导入 ****************/
    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表后导出
    char filePath4[64] = "./filePath/export4.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    ret = TPC_Restore((uint8_t *)filePath4, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB2校验表名和数据量
    ret = executeCommand(command, "tbname: label1", "count: 1000");
    AW_MACRO_EXPECT_NE_INT(DB_SUCCESS_V1, ret);

    // 释放result
    TPC_FreeSysviewResult(&result);
    system("rm -rf ./filePath/output");

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 导入后获取desc信息，DB2设置一些desc
TEST_F(ImportExportFunction, V1Com_012_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置数据库描述信息
    char setDescInfo[] = "haha_xixi.hehe-db.v1@*$#&-version";
    ret = TPC_SetDBDesc((VOS_UINT8 *)dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};

    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入后再dml
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    char getDescInfo[100] = {0};
    VOS_UINT32 pulLen = sizeof(getDescInfo);
    ret = TPC_GetDBDesc((VOS_UINT8 *)dbName2, NULL, (VOS_VOID *)getDescInfo, &pulLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR(setDescInfo, getDescInfo);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 2 持久化DB
// 015 同一个持久化DB只含所有数据类型表的导入导出，导出后closeDB再openDB导入，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    system("echo 'This is persisten DB' > ./perFilePath/perDbPath");
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char exportFile[64] = "./perFilePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 同一个持久化DB含表和数据的导入导出，导出后closeDB再openDB导入，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    system("echo 'This is persisten DB' > ./perFilePath/perDbPath");
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(insertNum, pulActRec);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 同一个持久化DB只含临时表的导入导出，closeDB后再openDB导入，预期DB不含临时表
TEST_F(ImportExportFunction, V1Com_012_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char filePath[64] = "./perFilePath/perDbPath";
    system("echo 'This is persisten DB' > ./perFilePath/perDbPath");
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum, 2000, true);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char exportFile[64] = "./perFilePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_NE_STR("label1", (char *)resultBuff[0]);  // 没有表
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulTblCount);
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 同一个DB只含所有数据类型表的循环导入导出，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char filePath[64] = "./perFilePath/perDbPath";
    system("echo 'This is persisten DB' > ./perFilePath/perDbPath");
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 循环导入导出
    char exportFile[64] = "./perFilePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    for (uint32_t i = 0; i < 10; i++) {
        ret = TPC_BkpPhy(dbID, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 不同持久化DB只含所有数据类型表的导入导出，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char exportFile[64] = "./perFilePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLose、DropDB1
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // db不存在导入，创建db
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 不同持久化DB含表和数据的导入导出，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char exportFile[64] = "./perFilePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入，db不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // OpenDB后查看表
    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID2, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(insertNum, pulActRec);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 不同持久化DB只含临时表的导入导出，预期DB2不含临时表
TEST_F(ImportExportFunction, V1Com_012_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum, 2000, true);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char exportFile[64] = "./perFilePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName2[20] = "dbName2";
    char filePath2[64] = "./filePath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_NE_STR("label1", (char *)resultBuff[0]);  // 没有表
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulTblCount);
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID2, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 不同DB只含所有数据类型表的循环导入导出，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbName2[20] = "dbName2";
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 循环导入导出
    char exportFile[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    for (uint32_t i = 0; i < 10; i++) {
        ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_Restore(
            (uint8_t *)exportFile, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char cmd[256];
    sprintf_s(cmd, 256, "touch %s", filePath);
    system(cmd);
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 自定义数据类型的导入导出，预期导入成功
TEST_F(ImportExportFunction, V1Com_012_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名
    DB_FIELD_DEF_STRU astFlds[fileds_num];  // 声明表结构
    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REL_DEF_STRU stRelDef;  // 创建表结构体
    // 循环对表结构体创建字段
    for (uint32_t i = 0; i < fileds_num; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].ulDefVal = 0;
    }
    uint32_t index = 0;
    astFlds[index].enDataType = (DB_DATATYPE_ENUM_V1)32;
    astFlds[index++].usSize = 4;
    astFlds[index].enDataType = (DB_DATATYPE_ENUM_V1)32;
    astFlds[index++].usSize = 4;
    astFlds[index].enDataType = (DB_DATATYPE_ENUM_V1)32;
    astFlds[index++].usSize = 4;
    index = 0;
    // 使用设置好的数据初始化表
    (void)strncpy_s((char *)stRelDef.aucRelName, DB_NAME_LEN, tabelName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.pstFldLst = astFlds;
    stRelDef.ulIntialSize = 100;
    stRelDef.ulMaxSize = 1000;
    stRelDef.ulNCols = fileds_num;
    stRelDef.ulNIdxs = 0;  // 0:不使用索引;1,2,3...:使用对应数量的索引

    ret = TPC_CreateTbl(ulDbId, &stRelDef, &usRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |", *(uint32_t *)(void *)tmppBuf, *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(ulDbId, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    char filePath[64] = "./perFilePath/perDbPath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./filePath/output";
    sysviewArgs.ulDbId = dbID2;
    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &sysviewArgs, &result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表名和数据量
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/output");
    ret = executeCommand(command, "tbname: cus_tlb1", "count: 2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath/output");

    // 释放result
    TPC_FreeSysviewResult(&result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(dbID2));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName2, 1));

    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 导出后，重新初始化，再导入，预期导入失败，创建自定义数据类型后再导入，预期导入成功
TEST_F(ImportExportFunction, V1Com_012_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名
    DB_FIELD_DEF_STRU astFlds[fileds_num];  // 声明表结构
    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REL_DEF_STRU stRelDef;  // 创建表结构体
    // 循环对表结构体创建字段
    for (uint32_t i = 0; i < fileds_num; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].ulDefVal = 0;
    }
    uint32_t index = 0;
    astFlds[index].enDataType = (DB_DATATYPE_ENUM_V1)32;
    astFlds[index++].usSize = 4;
    astFlds[index].enDataType = (DB_DATATYPE_ENUM_V1)32;
    astFlds[index++].usSize = 4;
    astFlds[index].enDataType = (DB_DATATYPE_ENUM_V1)32;
    astFlds[index++].usSize = 4;
    index = 0;
    // 使用设置好的数据初始化表
    (void)strncpy_s((char *)stRelDef.aucRelName, DB_NAME_LEN, tabelName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.pstFldLst = astFlds;
    stRelDef.ulIntialSize = 100;
    stRelDef.ulMaxSize = 1000;
    stRelDef.ulNCols = fileds_num;
    stRelDef.ulNIdxs = 0;  // 0:不使用索引;1,2,3...:使用对应数量的索引

    ret = TPC_CreateTbl(ulDbId, &stRelDef, &usRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |", *(uint32_t *)(void *)tmppBuf, *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(ulDbId, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));

    // 重新初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    ret = TestTPC_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName2, NULL, &pstCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDDATATYPE, ret);  // 没有自定义数据类型导入失败
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Unable to find custom type 32 when import label.", false));

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型后导入，成功
    ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./filePath/output";
    sysviewArgs.ulDbId = dbID2;
    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &sysviewArgs, &result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表名和数据量
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/output");
    ret = executeCommand(command, "tbname: cus_tlb1", "count: 2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath/output");

    // 释放result
    TPC_FreeSysviewResult(&result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(dbID2));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName2, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 3 普通DB和持久化DB导入导出
// 025 普通DB只含表导入持久化DB，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    char filePath[64] = "./perFilePath/perDbPath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 普通DB含表和数据导入持久化DB，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    char filePath[64] = "./perFilePath/perDbPath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(insertNum, pulActRec);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 普通DB只含临时表导入持久化DB，预期DB2不含临时表
TEST_F(ImportExportFunction, V1Com_012_Func_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum, 2000, true);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    char filePath[64] = "./perFilePath/perDbPath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_NE_STR("label1", (char *)resultBuff[0]);  // 没有表
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulTblCount);
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 普通DB导入持久化DB完成closeDB后再次open持久化DB，预期含表
TEST_F(ImportExportFunction, V1Com_012_Func_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    char filePath[64] = "./perFilePath/perDbPath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)exportFile, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB((VOS_UINT8 *)exportFile, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 删表
    ret = TPC_DropTbl(dbID2, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB
    ret = TPC_OpenDB((VOS_UINT8 *)exportFile, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./filePath/output";
    sysviewArgs.ulDbId = dbID2;
    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &sysviewArgs, &result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表名和数据量
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/output");
    ret = executeCommand(command, "tbname: label1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放result
    TPC_FreeSysviewResult(&result);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 持久化DB只含表导入普通DB，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 持久化DB只含表和数据导入普通DB，预期导入导出成功
TEST_F(ImportExportFunction, V1Com_012_Func_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(insertNum, pulActRec);

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 持久化DB只含临时表导入普通DB，预期DB2不含临时表
TEST_F(ImportExportFunction, V1Com_012_Func_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum, 2000, true);

    // 建表
    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)exportFile, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_NE_STR("label1", (char *)resultBuff[0]);  // 没有表
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulTblCount);
    TEST_V1_FREE(resultBuff[0]);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(dbID, relID, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032 持久化DB导入普通DB完成closeDB后再次openDB，预期不含表
TEST_F(ImportExportFunction, V1Com_012_Func_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char filePath[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbID = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);

    uint16_t relID = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbID, (uint8_t *)exportFile);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(dbID, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入持久化DB
    char dbName2[20] = "dbName2";
    VOS_UINT32 dbID2 = 0;
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)exportFile, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(dbID2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 删表
    ret = TPC_DropTbl(dbID2, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./filePath/output";
    sysviewArgs.ulDbId = dbID2;
    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &sysviewArgs, &result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表名和数据量
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/output");
    ret = executeCommand(command, "tbname: label1");
    AW_MACRO_EXPECT_NE_INT(DB_SUCCESS_V1, ret);

    // 释放result
    TPC_FreeSysviewResult(&result);
    system("rm -rf ./filePath/output");
    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
