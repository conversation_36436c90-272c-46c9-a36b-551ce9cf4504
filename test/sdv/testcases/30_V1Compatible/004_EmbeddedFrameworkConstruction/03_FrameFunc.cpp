/*  功能测试：CreateDB，OpenDB，CloseDB，DropDB
1 CreateDB
001 创建DB，OpenDB、CloseDB和DropDB，预期成功
002 创建DB，DropDB，预期成功
003 同目录下创建同名的DB，预期创建失败
004 不同目录下创建同名的DB，预期失败
005 同目录下创建多个不同名的DB，预期创建成功
006 内存满CreateDB，预期报错，DropDB后再CreateDB，预期成功

2 OpenDB
007 创建DB1，OpenDB2（不存在的DB），预期Open失败
008 创建DB，OpenDB 2次，预期OpenDB成功

3 CloseDB
009 创建DB，OpenDB，Close不存在的DB，预期Close失败
010 创建DB1、DB2，OpenDB1，CloseDB2，预期CloseDB2失败
011 创建DB，不OpenDB，CloseDB，预期Close失败
012 创建DB，OpenDB 1次，CloseDB 2次，预期第二次CloseDB失败

4 DropDB
013 创建DB，OpenDB，CloseDB，Drop不存在的DB，预期Drop失败
014 创建DB，OpenDB，不CloseDB，DropDB，预期DropDB失败
015 创建DB，OpenDB 2次和CloseDB 1次数，DropDB，预期DropDB失败
016 DB中有表，不删表DropDB，预期Drop成功
017 DB中有表，不删表、不CloseDB，预期CloseDB失败

5 可靠可用性
018 循环创建DB，OpenDB，CloseDB，DropDB 100次
019 4个线程分别进行CreateDB，OpenDB，CloseDB和DropDB
*/

#include "Framework.h"
class FrameFunc : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void FrameFunc::SetUpTestCase()
{}

void FrameFunc::TearDownTestCase()
{}

void FrameFunc::SetUp()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    AW_CHECK_LOG_BEGIN();
}

void FrameFunc::TearDown()
{
    AW_CHECK_LOG_END();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
}

// 1 CreateDB
// 001 创建DB，OpenDB、CloseDB和DropDB，预期成功
TEST_F(FrameFunc, V1Com_004_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 创建DB，DropDB，预期成功
TEST_F(FrameFunc, V1Com_004_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 同目录下创建同名的DB，预期创建失败
TEST_F(FrameFunc, V1Com_004_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次创建相同的DB
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Nsp with the name (dbName) already exist", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 不同目录下创建同名的DB，预期失败
TEST_F(FrameFunc, V1Com_004_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = "./cfgPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 不同的Dir 再次创建相同的DB
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Nsp with the name (dbName) already exist", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 同目录下创建多个不同名的DB，预期创建成功
TEST_F(FrameFunc, V1Com_004_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbNameB[20] = "dbNameB";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)dbNameB, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbNameB, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 内存满CreateDB，预期报错，DropDB后再CreateDB，预期成功
TEST_F(FrameFunc, V1Com_004_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbNameB[20] = "dbNameB";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.ulMaxDBDescInfoSize = 0x5FEFFFFF;
    ret = TPC_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 内存满后申请创建DB，失败
    ret = TPC_CreateDB((VOS_UINT8 *)dbNameB, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Unable to alloc db desc, db name(dbNameB).", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除后申请创建DB，成功
    ret = TPC_CreateDB((VOS_UINT8 *)dbNameB, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbNameB, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 2 OpenDB
// 007 创建DB1，OpenDB2（不存在的DB），预期Open失败
TEST_F(FrameFunc, V1Com_004_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    // Open一个不存在的DB
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)"dbName2", &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Db load path is null ptr.", false));

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 创建DB，OpenDB 2次，预期OpenDB成功
TEST_F(FrameFunc, V1Com_004_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB  2次
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB  2次
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 3 CloseDB
// 009 创建DB，OpenDB，Close不存在的DB，预期Close失败
TEST_F(FrameFunc, V1Com_004_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT32 dbId2 = dbId + 1;
    // CLoseDB
    ret = TPC_CloseDB(dbId2);  // DB不存在
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (1) not exists.", false));
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 创建DB1、DB2，OpenDB1，CloseDB2，预期CloseDB2失败
TEST_F(FrameFunc, V1Com_004_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbNameB[20] = "dbNameB";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)dbNameB, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbIdA = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbNameA, &dbIdA);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CloseDB
    VOS_UINT32 dbIdB = dbIdA + 1;
    ret = TPC_CloseDB(dbIdB);  // 没有OpenDB，进行Close操作，失败
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Namespace dose not opened.", false));

    ret = TPC_CloseDB(dbIdA);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbNameB, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 创建DB，不OpenDB，CloseDB，预期Close失败
TEST_F(FrameFunc, V1Com_004_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT32 dbIdA = 0;
    // CloseDB
    ret = TPC_CloseDB(dbIdA);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Namespace dose not opened.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 创建DB，OpenDB 1次，CloseDB 2次，预期第二次CloseDB失败
TEST_F(FrameFunc, V1Com_004_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB  1次
    VOS_UINT32 dbIdA = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbNameA, &dbIdA);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CloseDB  2次
    ret = TPC_CloseDB(dbIdA);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbIdA);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Namespace dose not opened.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 4 DropDB
// 013 创建DB，OpenDB，CloseDB，Drop不存在的DB，预期Drop失败
TEST_F(FrameFunc, V1Com_004_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);  // db被删除，已经不存在
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName) is not exist.", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 创建DB，OpenDB，不CloseDB，DropDB，预期DropDB失败
TEST_F(FrameFunc, V1Com_004_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 没有CloseDB就删除DB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DROP_NOTALLOWED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Some other tasks operating on the DB(dbName).", false));

    // CLoseDB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 创建DB，OpenDB 2次和CloseDB 1次数，DropDB，预期DropDB失败
TEST_F(FrameFunc, V1Com_004_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB  2次
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CloseDB 1次就删DB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DROP_NOTALLOWED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Some other tasks operating on the DB(dbName).", false));

    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 DB中有表，不删表DropDB，预期Drop成功
TEST_F(FrameFunc, V1Com_004_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef = {0};
    TestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);

    uint16_t relId;
    char tblName[DB_NAME_LEN];
    for (uint32_t i = 0; i < 1; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s(tblName, DB_NAME_LEN, "tbl%u", i);
        (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
        EXPECT_EQ(DB_SUCCESS_V1, TPC_CreateTbl(dbId, &stRelDef, &relId));
    }
    (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
    FreeRelDef(&stRelDef);

    // CLoseDB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 DB中有表，不删表、不CloseDB，预期CloseDB失败
TEST_F(FrameFunc, V1Com_004_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef = {0};
    TestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);

    uint16_t relId;
    char tblName[DB_NAME_LEN];
    for (uint32_t i = 0; i < 1; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s(tblName, DB_NAME_LEN, "tbl%u", i);
        (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
        EXPECT_EQ(DB_SUCCESS_V1, TPC_CreateTbl(dbId, &stRelDef, &relId));
    }
    (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
    FreeRelDef(&stRelDef);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DROP_NOTALLOWED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Some other tasks operating on the DB(dbName).", false));

    // CLoseDB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 5 可靠可用性
// 018 循环创建DB，OpenDB，CloseDB，DropDB 100次
TEST_F(FrameFunc, V1Com_004_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    for (uint32_t i = 0; i < 100; i++) {
        // CreateDB
        ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // OpenDB
        VOS_UINT32 dbId = 0;
        ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // CLoseDB
        ret = TPC_CloseDB(dbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // DropDB
        ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *CreateDBThr(void *args)
{
    AW_FUN_Log(LOG_STEP, "CreateDB");
    int ret;
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    if (ret == VOS_ERRNO_DB_INVALID_DBNAME || ret == DB_SUCCESS_V1) {
        ret = DB_SUCCESS_V1;
    }
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void *OpenDBThr(void *args)
{
    AW_FUN_Log(LOG_STEP, "OpenDB");
    int ret;
    // OpenDB
    char dbName[20] = "dbName";
    VOS_UINT32 dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    if (ret == VOS_ERRNO_DB_NULLPTR || ret == DB_SUCCESS_V1) {
        ret = DB_SUCCESS_V1;
    }
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void *CloseDBThr(void *args)
{
    AW_FUN_Log(LOG_STEP, "CloseDB");
    int ret;
    VOS_UINT32 dbId = 0;
    // CLoseDB
    ret = TPC_CloseDB(dbId);
    if (ret == DB_SUCCESS_V1 || VOS_ERRNO_DB_DATABASE_NOT_OPENED) {
        ret = DB_SUCCESS_V1;
    }
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void *DropDBThr(void *args)
{
    AW_FUN_Log(LOG_STEP, "DropDB");
    int ret;
    char dbName[20] = "dbName";
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    if (ret == VOS_ERRNO_DB_INVALID_DBNAME || ret == DB_SUCCESS_V1 || VOS_ERRNO_DB_DROP_NOTALLOWED) {
        ret = DB_SUCCESS_V1;
    }
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 019 4个线程分别进行CreateDB，OpenDB，CloseDB和DropDB
TEST_F(FrameFunc, V1Com_004_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    pthread_t tid[100];
    for (uint32_t i = 0; i < 5; i++) {
        ret = pthread_create(&tid[i], NULL, CreateDBThr, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = pthread_create(&tid[i + 5], NULL, OpenDBThr, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = pthread_create(&tid[i + 10], NULL, CloseDBThr, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = pthread_create(&tid[i + 15], NULL, DropDBThr, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < 20; i++) {
        pthread_join(tid[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
