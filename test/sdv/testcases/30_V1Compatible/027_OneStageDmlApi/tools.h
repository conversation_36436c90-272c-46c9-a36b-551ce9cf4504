/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: duhu
 * Create: 2024-07-16
 */

#ifndef TOOLS_H
#define TOOLS_H
#include "t_rd_simplerel.h"

using namespace std;

// 自定义断言，规避codeClean
#define AW_MACRO_EXPECT_LE_INT(a, b) EXPECT_LE(a, b)
VOS_UINT32 g_dbID2 = 0;
DB_INST_CONFIG_STRU g_testDbCfg = {0};
const char g_testDbName[DB_NAME_LEN] = "testDdl";

void CreateV1Label(const char *tableName, DB_REL_DEF_STRU *stRelDef, DB_DATATYPE_ENUM_V1 *dataTypes, int *dataSize,
    int fldNum, int maxRecordCount = 2000)
{
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds == NULL) {
        return;
    }
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = dataSize[i];
        astFlds[i].ulDefVal = 0xffffffff;
    }

    int indexNum = 1;
    const char *indexName = "tTree";
    DB_INDEX_DEF_STRU *astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(indexNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx == NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = 0;
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 0;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)strncpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);

    (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = maxRecordCount;
    stRelDef->ulMaxSize = maxRecordCount;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = indexNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

inline static uint16_t TestGetOneFieldLen(DB_FIELD_DEF_STRU *pstFld)
{
    const uint16_t size = pstFld->usSize;
    // v1数据类型对应字段大小
    const uint32_t fieldSizes[32] = {(uint32_t)((size + 1) / 2), sizeof(float), sizeof(double), 4, sizeof(double),
        (uint32_t)(size + 2), sizeof(DB_TIME_STRU), (uint32_t)(size + 1), size, sizeof(uint8_t), sizeof(uint16_t),
        sizeof(uint32_t), sizeof(int8_t), sizeof(int16_t), sizeof(int32_t), sizeof(DB_DATE_STRU), sizeof(uint32_t), 6,
        0, sizeof(int64_t), sizeof(uint64_t), sizeof(DB_IPV4PREFIX_STRUCT), sizeof(DB_IPV6ADDRESS_STRUCT),
        sizeof(DB_IPV6ADDRESS_PREFIX_STRU), sizeof(DB_DATETIME_STRU), sizeof(DB_TIMEZONE_STRU), (uint32_t)(size + 1),
        (uint32_t)(size + 2), 0, 0, 0, 0};
    return (uint16_t)fieldSizes[pstFld->enDataType];
}

void CreateV1Label(const char *tableName, DB_REL_DEF_STRU *stRelDef, DB_DATATYPE_ENUM_V1 *dataTypes, int fldNum,
    int maxRecordCount = 2000)
{
    int dataSize[fldNum];
    for (uint32_t i = 0; i < fldNum; i++) {
        dataSize[i] = sizeof(dataTypes[i]);
    }
    CreateV1Label(tableName, stRelDef, dataTypes, dataSize, fldNum, maxRecordCount);
}

void FreeRelDef(DB_REL_DEF_STRU *stRelDef)
{
    if (stRelDef->pstFldLst != NULL) {
        TEST_V1_FREE(stRelDef->pstFldLst);
        stRelDef->pstFldLst = NULL;
    }
    if (stRelDef->pstIdxLst != NULL) {
        TEST_V1_FREE(stRelDef->pstIdxLst);
        stRelDef->pstIdxLst = NULL;
    }
}

static uint16_t GetRecLen(DB_REL_DEF_STRU *stRelDef)
{
    uint32_t recordLen = 0;
    DB_FIELD_DEF_STRU *pstFld = stRelDef->pstFldLst;
    for (uint32_t i = 0; i < stRelDef->ulNCols; i++, pstFld++) {
        recordLen += TestGetOneFieldLen(pstFld);
    }

    return recordLen;
}

void Insert(uint8_t *recBuf, uint32_t base, uint32_t bufLen, int filedNum = 7)
{
    uint32_t fldVal[filedNum];
    for (uint32_t i = 0; i < filedNum; i++) {
        fldVal[i] = base + i;
    }
    (void)memcpy_s(recBuf, bufLen, fldVal, bufLen);
}
void InsertDat(uint8_t *recBuf, DB_DATETIME_STRU base, uint32_t bufLen, int filedNum = 7)
{
    DB_DATETIME_STRU fldVal[filedNum];
    for (uint16_t i = 0; i < filedNum; i++) {
        base.usYear += i;
        fldVal[i] = base;
    }
    (void)memcpy_s(recBuf, bufLen, fldVal, bufLen);
}
void InsertSame(uint8_t *recBuf, uint32_t base, uint32_t bufLen, int filedNum = 7)
{
    uint32_t fldVal[filedNum];
    for (uint32_t i = 0; i < filedNum; i++) {
        fldVal[i] = base;
    }
    (void)memcpy_s(recBuf, bufLen, fldVal, bufLen);
}

void testSetAllField(uint8_t *recBuf, DB_FIELD_DEF_STRU *astFlds, uint32_t base, uint32_t longStringLen)
{
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", base);
    // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
    // DBT_BYTES
    const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
    char stringVal1[stringLen1] = {0};
    memset_s(stringVal1, stringLen1, 0x00, stringLen1);
    *(uint16_t *)stringVal1 = 8;
    (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", base);
    // DBT_VBYTES
    const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
    char stringVal2[stringLen2] = {0};
    memset_s(stringVal2, stringLen2, 0x00, stringLen2);
    *(uint16_t *)stringVal2 = 10;
    (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", base);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    char longStringVal1[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal1, longLen, 0x00, longLen);
    if (longStringLen > longLen) {
        AW_FUN_Log(LOG_STEP, "ERROR.insert string length exceed max limit");
    }
    memset_s(longStringVal, longLen, 'a', longStringLen - 1);
    memset_s(longStringVal1, longLen, 'a', longStringLen - 1);
    longStringVal[longStringLen - 1] = '\0';
    longStringVal1[longStringLen - 2] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    strncpy(longStringVal1, stringVal, strlen(stringVal));

    // DBT_BCD
    int16_t f0Value = base;
    // DBT_FLOAT
    float f1Value = (float)(base);
    // DBT_DOUBLE
    double f2Value = (double)(base);
    // DBT_BIT
    char *f3Value = stringVal;
    // DBT_NUMERIC
    double f4Value = (double)(base);
    // DBT_BYTES
    char *f5Value = stringVal1;
    // DBT_TIME
    char *f6Value = stringVal;
    // DBT_STRING
    char *f7Value = longStringVal;
    // DBT_BLOCK
    char *f8Value = longStringVal1;
    // DBT_UINT8
    uint8_t f9Value = base;
    // DBT_UINT16
    uint16_t f10Value = base;
    // DBT_UINT32
    uint32_t f11Value = base;
    // DBT_SINT8
    int8_t f12Value = base;
    // DBT_SINT16
    int16_t f13Value = base;
    // DBT_SINT32
    int32_t f14Value = base;
    // DBT_DATE
    char *f15Value = stringVal;
    // DBT_IP_ADDRESS
    uint32_t f16Value = base;
    // DBT_MAC_ADDRESS
    char *f17Value = stringVal;
    // DBT_INT64
    int64_t f18Value = base;
    // DBT_UINT64
    uint64_t f19Value = base;
    // DBT_IPV4PREFIX
    char *f20Value = stringVal;
    // DBT_IPV6
    char *f21Value = stringVal;
    // DBT_IPV6PREFIX
    char *f22Value = stringVal;
    // DBT_DATETIME
    char *f23Value = stringVal;
    // DBT_TIMEZONE
    char *f24Value = stringVal;
    // DBT_MIBSTR
    char *f25Value = stringVal;
    // DBT_VBYTES
    char *f26Value = stringVal2;

    uint8_t *temp = recBuf;
    uint32_t index = 0;
    (void)memcpy_s(temp, TestGetOneFieldLen(&astFlds[index]), &f0Value, TestGetOneFieldLen(&astFlds[index]));
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f1Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f2Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f3Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f4Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f5Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f6Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f7Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f8Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f9Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f10Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f11Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f12Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f13Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f14Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f15Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f16Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f17Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f18Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f19Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f20Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f21Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f22Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f23Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f24Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f25Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f26Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
}

void Update(VOS_UINT32 dbId, VOS_UINT16 usRelId, uint16_t filed, uint32_t data, uint8_t *recBuf, uint32_t *recNum,
    int mod = 0, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = filed;
    *(uint32_t *)stCond.aCond[0].aucValue = data;

    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(g_dbID2, usRelId, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = tblRecLen;
    pstBuff.ulBufLen = tblRecLen;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    int index = 2;
    uint8_t *temp = recBuf;

    DB_DSBUF_STRU udpBuf = {
        .usRecLen = pstBuff.usRecLen, .usRecNum = 3, .StdBuf = {.ulBufLen = tblRecLen * 3, .ulActLen = tblRecLen, .pucData = recBuf}};

    uint32_t udpRecNum = 0;
    if(mod = 0){
        ret = DB_UpdateRec(dbId, usRelId, &stCond, &stFldFilter, &udpBuf, &udpRecNum);
    }
    else {
        ret = DBS_LocalModRecordForDatabase(dbId, usRelId, &stCond, &stFldFilter, &udpBuf, &udpRecNum);
    }
    
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    *recNum = udpRecNum;
    TEST_V1_FREE(pstBuff.pBuf);
}

void UpdateSelfF(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, uint32_t data, uint8_t *recBuf,
    uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond.aCond[0].aucValue = data;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 28;
    pstBuff.ulBufLen = 28;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    int index = 2;
    uint8_t *temp = recBuf;
    uint32_t valueF0 = data + 10000;
    uint32_t valueF2 = data + 1000;
    (void)memcpy_s(temp, 4, &valueF0, 4);
    (void)memcpy_s(temp + index * sizeof(uint32_t), 4, &valueF2, 4);

    DB_DSBUF_STRU udpBuf = {
        .usRecLen = pstBuff.usRecLen, .usRecNum = 1, .StdBuf = {.ulBufLen = 28, .ulActLen = 28, .pucData = recBuf}};

    uint32_t udpRecNum = 0;
    ret = TPC_UpdateRec(cbdId, dbId, usRelId, &stCond, &stFldFilter, &udpBuf, &udpRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    *recNum = udpRecNum;
    TEST_V1_FREE(pstBuff.pBuf);
}

void UpdateAllType(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, uint8_t *recBuf, DB_FIELD_DEF_STRU *astFlds,
    int32_t base, uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 11;
    *(uint32_t *)stCond.aCond[0].aucValue = base + 11;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    int32_t data = base + 10000;

    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", data);

    // 2024.9.2
    // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
    // DBT_BYTES
    const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
    char stringVal1[stringLen1] = {0};
    memset_s(stringVal1, stringLen1, 0x00, stringLen1);
    *(uint16_t *)stringVal1 = 8;
    (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", data);
    // DBT_VBYTES
    const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
    char stringVal2[stringLen2] = {0};
    memset_s(stringVal2, stringLen2, 0x00, stringLen2);
    *(uint16_t *)stringVal2 = 10;
    (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", data);

    // DBT_BCD
    int16_t f0Value = data;
    // DBT_FLOAT
    float f1Value = (float)(data + 1);
    // DBT_DOUBLE
    double f2Value = (double)(data + 2);
    // DBT_BIT
    char *f3Value = stringVal;
    // DBT_NUMERIC
    double f4Value = (double)(data + 4);
    // DBT_BYTES
    char *f5Value = stringVal1;
    // DBT_TIME
    char *f6Value = stringVal;
    // DBT_STRING
    char *f7Value = stringVal;
    // DBT_BLOCK
    char *f8Value = stringVal;
    // DBT_UINT8
    uint8_t f9Value = (data + 9) % 256;
    // DBT_UINT16
    uint16_t f10Value = data + 10;
    // DBT_UINT32
    uint32_t f11Value = data + 11;
    // DBT_SINT8
    int8_t f12Value = (data + 12) % 128;
    // DBT_SINT16
    int16_t f13Value = data + 13;
    // DBT_SINT32
    int32_t f14Value = data + 14;
    // DBT_DATE
    char *f15Value = stringVal;
    // DBT_IP_ADDRESS
    uint32_t f16Value = data + 16;
    // DBT_MAC_ADDRESS
    char *f17Value = stringVal;
    // DBT_INT64
    int64_t f18Value = data + 18;
    // DBT_UINT64
    uint64_t f19Value = data + 19;
    // DBT_IPV4PREFIX
    char *f20Value = stringVal;
    // DBT_IPV6
    char *f21Value = stringVal;
    // DBT_IPV6PREFIX
    char *f22Value = stringVal;
    // DBT_DATETIME
    char *f23Value = stringVal;
    // DBT_TIMEZONE
    char *f24Value = stringVal;
    // DBT_MIBSTR
    char *f25Value = stringVal;
    // DBT_VBYTES
    char *f26Value = stringVal2;

    uint8_t *temp = recBuf;
    uint32_t pointPos = 0;
    int index = 0;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f0Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f1Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f2Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f3Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f4Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f5Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f6Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f7Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f8Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f9Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(
        temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f10Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(
        temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f11Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(
        temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f12Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(
        temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f13Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(
        temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f14Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f15Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(
        temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f16Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f17Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(
        temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f18Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(
        temp + pointPos, TestGetOneFieldLen(&astFlds[index]), &f19Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f20Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f21Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f22Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f23Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f24Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f25Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;
    (void)memcpy_s(temp + pointPos, TestGetOneFieldLen(&astFlds[index]), f26Value, TestGetOneFieldLen(&astFlds[index]));
    pointPos += TestGetOneFieldLen(&astFlds[index]);
    index++;

    DB_DSBUF_STRU udpBuf = {.usRecLen = pstBuff.usRecLen,
        .usRecNum = 1,
        .StdBuf = {.ulBufLen = pointPos, .ulActLen = pointPos, .pucData = recBuf}};

    uint32_t udpRecNum = 0;
    ret = TPC_UpdateRec(cbdId, dbId, usRelId, &stCond, &stFldFilter, &udpBuf, &udpRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    *recNum = udpRecNum;
}

void Select(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, uint32_t data, uint32_t *recNum,
    DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond.aCond[0].aucValue = data;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 28 * 1000;
    pstBuff.ulBufLen = 28 * 1000;
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(cbdId, dbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    // 最新的代码，没有数据会报错
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        *recNum = pstBuff.ulRecNum;

        uint8_t *bufCursor = (uint8_t *)pstBuff.pBuf;
        for (uint32_t i = 0; i < pstBuff.ulRecNum; i++) {
            for (uint32_t j = 0; j < 7; j++) {
                V1_AW_MACRO_EXPECT_EQ_INT(data + j, *(uint32_t *)(bufCursor + (j * sizeof(uint32_t))));
            }
            bufCursor += pstBuff.usRecLen;
        }
        *recNum = pstBuff.ulRecNum;
    } else {
        *recNum = 0;
    }
    TEST_V1_FREE(pstBuff.pBuf);
}

void SelectAfterUpdate(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, uint32_t data, uint32_t *expValue,
    uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond.aCond[0].aucValue = data;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 28 * 1000;
    pstBuff.ulBufLen = 28 * 1000;
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(cbdId, dbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        *recNum = pstBuff.ulRecNum;
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        uint8_t *bufCursor = (uint8_t *)pstBuff.pBuf;
        for (uint32_t i = 0; i < pstBuff.ulRecNum; i++) {
            for (uint32_t j = 0; j < 7; j++) {
                V1_AW_MACRO_EXPECT_EQ_INT(expValue[i * 7 + j], *(uint32_t *)(bufCursor + (j * sizeof(uint32_t))));
            }
            bufCursor += pstBuff.usRecLen;
        }
    } else {
        *recNum = 0;
    }
    TEST_V1_FREE(pstBuff.pBuf);
}

void SelectSelfF(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, uint32_t data, uint32_t *recNum,
    DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond.aCond[0].aucValue = data;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 28 * 50;
    pstBuff.ulBufLen = 28 * 50;
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(cbdId, dbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        *recNum = pstBuff.ulRecNum;
        uint8_t *bufCursor = (uint8_t *)pstBuff.pBuf;
        for (uint32_t i = 0; i < pstBuff.ulRecNum; i++) {
            for (uint32_t j = 0; j < 7; j++) {
                if (j == 0) {
                    V1_AW_MACRO_EXPECT_EQ_INT(data, *(uint32_t *)(bufCursor + (j * sizeof(uint32_t))));
                } else if (j == 2) {
                    V1_AW_MACRO_EXPECT_EQ_INT(1000 + data - 10000, *(uint32_t *)(bufCursor + (j * sizeof(uint32_t))));
                } else {
                    V1_AW_MACRO_EXPECT_EQ_INT(0, *(uint32_t *)(bufCursor + (j * sizeof(uint32_t))));
                }
            }
            bufCursor += pstBuff.usRecLen;
        }
    } else {
        *recNum = 0;
    }
    TEST_V1_FREE(pstBuff.pBuf);
}

void Delete(VOS_UINT32 dbId, VOS_UINT16 usRelId, uint16_t filed, uint32_t data, uint32_t *recNum,
    DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = filed;
    *(uint32_t *)stCond.aCond[0].aucValue = data;

    uint32_t delRecNum = 0;
    ret = DB_DeleteRec(dbId, usRelId, &stCond, &delRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    *recNum = delRecNum;
}

void SelectAllType(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, DB_FIELD_DEF_STRU *astFlds, uint32_t data,
    uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 11;
    *(uint32_t *)stCond.aCond[0].aucValue = data + 11;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(cbdId, dbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        *recNum = pstBuff.ulRecNum;
        const uint32_t stringLen = 17;
        char stringVal[stringLen];
        memset_s(stringVal, stringLen, 0x00, stringLen);
        (void)sprintf_s(stringVal, stringLen, "re%u", data);

        // 2024.9.2
        // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
        // DBT_BYTES
        const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
        char stringVal1[stringLen1] = {0};
        memset_s(stringVal1, stringLen1, 0x00, stringLen1);
        *(uint16_t *)stringVal1 = 8;
        (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", data);
        // DBT_VBYTES
        const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
        char stringVal2[stringLen2] = {0};
        memset_s(stringVal2, stringLen2, 0x00, stringLen2);
        *(uint16_t *)stringVal2 = 10;
        (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", data);

        uint8_t *bufCursor = (uint8_t *)pstBuff.pBuf;
        for (uint32_t i = 0; i < pstBuff.ulRecNum; i++) {
            int pointPos = 0;
            int index = 0;
            V1_AW_MACRO_EXPECT_EQ_INT(data, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 1, *(float *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 2, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f3Value[4 + 1] = {0};
            (void)memcpy_s(f3Value, 4, bufCursor + pointPos, 4);
            char expF3Value[4 + 1] = {0};
            (void)memcpy_s(expF3Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f3Value, expF3Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 4, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(f5Value, 8 + 2, bufCursor + pointPos, 8 + 2);
            char expF5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(expF5Value, 8 + 2, stringVal1, 8 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f5Value, expF5Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f6Value[3 + 1] = {0};
            (void)memcpy_s(f6Value, 3, bufCursor + pointPos, 3);
            char expF6Value[3 + 1] = {0};
            (void)memcpy_s(expF6Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f6Value, expF6Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f7Value[10 + 1] = {0};
            (void)memcpy_s(f7Value, 10, bufCursor + pointPos, 10);
            char expF7Value[10 + 1] = {0};
            (void)memcpy_s(expF7Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f7Value, expF7Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f8Value[1 + 1] = {0};
            (void)memcpy_s(f8Value, 1, bufCursor + pointPos, 1);
            char expF8Value[1 + 1] = {0};
            (void)memcpy_s(expF8Value, 1, stringVal, 1);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f8Value, expF8Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 9) % 256, *(uint8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 10, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 11, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 12) % 128, *(int8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 13, *(int16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 14, *(int32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f15Value[4 + 1] = {0};
            (void)memcpy_s(f15Value, 4, bufCursor + pointPos, 4);
            char expF15Value[4 + 1] = {0};
            (void)memcpy_s(expF15Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f15Value, expF15Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 16, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f17Value[6 + 1] = {0};
            (void)memcpy_s(f17Value, 6, bufCursor + pointPos, 6);
            char expF17Value[6 + 1] = {0};
            (void)memcpy_s(expF17Value, 6, stringVal, 6);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f17Value, expF17Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 18, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 19, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f20Value[5 + 1] = {0};
            (void)memcpy_s(f20Value, 5, bufCursor + pointPos, 5);
            char expF20Value[5 + 1] = {0};
            (void)memcpy_s(expF20Value, 5, stringVal, 5);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f20Value, expF20Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f21Value[16 + 1] = {0};
            (void)memcpy_s(f21Value, 16, bufCursor + pointPos, 16);
            char expF21Value[16 + 1] = {0};
            (void)memcpy_s(expF21Value, 16, stringVal, 16);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f21Value, expF21Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f22Value[17 + 1] = {0};
            (void)memcpy_s(f22Value, 17, bufCursor + pointPos, 17);
            char expF22Value[17 + 1] = {0};
            (void)memcpy_s(expF22Value, 17, stringVal, 17);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f22Value, expF22Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f23Value[7 + 1] = {0};
            (void)memcpy_s(f23Value, 7, bufCursor + pointPos, 7);
            char expF23Value[7 + 1] = {0};
            (void)memcpy_s(expF23Value, 7, stringVal, 7);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f23Value, expF23Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f24Value[3 + 1] = {0};
            (void)memcpy_s(f24Value, 3, bufCursor + pointPos, 3);
            char expF24Value[3 + 1] = {0};
            (void)memcpy_s(expF24Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f24Value, expF24Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f25Value[10 + 1] = {0};
            (void)memcpy_s(f25Value, 10, bufCursor + pointPos, 10);
            char expF25Value[10 + 1] = {0};
            (void)memcpy_s(expF25Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f25Value, expF25Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(f26Value, 10 + 2, bufCursor + pointPos, 10 + 2);
            char expF26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(expF26Value, 10 + 2, stringVal2, 10 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f26Value, expF26Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            bufCursor += pstBuff.usRecLen;
        }
    } else {
        *recNum = 0;
    }
    TEST_V1_FREE(pstBuff.pBuf);
}

uint16_t g_recLen = 0;
uint32_t g_dbID = 0;
uint16_t g_relID = 0;
void *ThrDoDmlSameTbl(void *args)
{
    int ret = 0;
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(g_recLen);
    // 规避codeclean
    if (recBuf == NULL) {
        V1_AW_MACRO_EXPECT_EQ_INT(true, false);
    }
    (void)memset_s(recBuf, g_recLen, 0x00, g_recLen);
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        Insert(recBuf, i, g_recLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = g_recLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = g_recLen, .ulActLen = g_recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbID, g_relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        Select(TPC_GLOBAL_CDB, g_dbID, g_relID, i, &recNum);
        AW_MACRO_EXPECT_LE_INT(recNum, 50);
    }

    (void)memset_s(recBuf, g_recLen, 0x00, g_recLen);
    int updateNum = insertNum;
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        UpdateSelfF(TPC_GLOBAL_CDB, g_dbID, g_relID, i, recBuf, &recNum);
        AW_MACRO_EXPECT_LE_INT(recNum, 50);
    }

    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        SelectSelfF(TPC_GLOBAL_CDB, g_dbID, g_relID, i + 10000, &recNum);
        AW_MACRO_EXPECT_LE_INT(recNum, 50);
    }

    int deleteNum = insertNum;
    for (int i = 0; i < deleteNum; i++) {
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, g_dbID, g_relID, i + 10000, &recNum);
        AW_MACRO_EXPECT_LE_INT(recNum, 50);
    }

    for (int i = 0; i < deleteNum; i++) {
        uint32_t recNum = 0;
        Select(TPC_GLOBAL_CDB, g_dbID, g_relID, i + 10000, &recNum);
        AW_MACRO_EXPECT_LE_INT(recNum, 50);
    }
    TEST_V1_FREE(recBuf);
}

uint16_t g_relIDs[50] = {0};
void *ThrDoDmlDiffTbl(void *args)
{
    uint16_t thrIndex = *((uint16_t *)args);
    int ret = 0;
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(g_recLen);
    // 规避codeclean
    if (recBuf == NULL) {
        V1_AW_MACRO_EXPECT_EQ_INT(true, false);
    }
    (void)memset_s(recBuf, g_recLen, 0x00, g_recLen);
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        Insert(recBuf, i, g_recLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = g_recLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = g_recLen, .ulActLen = g_recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbID, g_relIDs[thrIndex], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        Select(TPC_GLOBAL_CDB, g_dbID, g_relIDs[thrIndex], i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    (void)memset_s(recBuf, g_recLen, 0x00, g_recLen);
    int updateNum = insertNum;
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        UpdateSelfF(TPC_GLOBAL_CDB, g_dbID, g_relIDs[thrIndex], i, recBuf, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        SelectSelfF(TPC_GLOBAL_CDB, g_dbID, g_relIDs[thrIndex], i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    int deleteNum = insertNum;
    for (int i = 0; i < deleteNum; i++) {
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, g_dbID, g_relIDs[thrIndex], i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = 0; i < deleteNum; i++) {
        uint32_t recNum = 0;
        Select(TPC_GLOBAL_CDB, g_dbID, g_relIDs[thrIndex], i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }
}

#define MAX_DB_CNT 64
uint32_t g_dbIDs[MAX_DB_CNT] = {0};
uint16_t g_diffDBRelIDs[MAX_DB_CNT] = {0};
void *ThrDoDmlDiffDBTbl(void *args)
{
    uint16_t thrIndex = *((uint16_t *)args);
    int ret = 0;
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(g_recLen);
    // 规避codeclean
    if (recBuf == NULL) {
        V1_AW_MACRO_EXPECT_EQ_INT(true, false);
    }
    (void)memset_s(recBuf, g_recLen, 0x00, g_recLen);
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        Insert(recBuf, i, g_recLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = g_recLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = g_recLen, .ulActLen = g_recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbIDs[thrIndex], g_diffDBRelIDs[thrIndex], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        Select(TPC_GLOBAL_CDB, g_dbIDs[thrIndex], g_diffDBRelIDs[thrIndex], i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    (void)memset_s(recBuf, g_recLen, 0x00, g_recLen);
    int updateNum = insertNum;
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        UpdateSelfF(TPC_GLOBAL_CDB, g_dbIDs[thrIndex], g_diffDBRelIDs[thrIndex], i, recBuf, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        SelectSelfF(TPC_GLOBAL_CDB, g_dbIDs[thrIndex], g_diffDBRelIDs[thrIndex], i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    int deleteNum = insertNum;
    for (int i = 0; i < deleteNum; i++) {
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, g_dbIDs[thrIndex], g_diffDBRelIDs[thrIndex], i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = 0; i < deleteNum; i++) {
        uint32_t recNum = 0;
        Select(TPC_GLOBAL_CDB, g_dbIDs[thrIndex], g_diffDBRelIDs[thrIndex], i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }
    TEST_V1_FREE(recBuf);
}

typedef struct tagPvArgT {
    bool isAlreadyMalloc;
    uint32_t callCnt;
    DB_BUF_STRU *bufAddr;
} PvArgT;

DB_BUF_STRU *PfGetBufCallBack(VOS_VOID *pvArg)
{
    PvArgT *pvArgTmp = (PvArgT *)pvArg;

    DB_BUF_STRU *bufAddrTmp = pvArgTmp->bufAddr;
    pvArgTmp->callCnt++;

    // 由外部控制，直接使用外部已设置好的buf
    if (pvArgTmp->isAlreadyMalloc) {
        return bufAddrTmp;
    }
    // 如果长度是0，直接返回NULL
    if (bufAddrTmp->ulBufLen == 0) {
        return NULL;
    }
    // 回调内部进行内存申请
    bufAddrTmp->pBuf = TEST_V1_MALLOC(bufAddrTmp->ulBufLen);
    if (bufAddrTmp->pBuf == NULL) {
        return NULL;
    }

    return bufAddrTmp;
}

DB_ERR_CODE TestMallocBuf(DB_BUF_STRU *pstBufData, VOS_UINT16 usRecLen, VOS_UINT32 ulRecNum, VOS_UINT32 ulBufLen)
{
    pstBufData->usRecLen = usRecLen;
    pstBufData->ulRecNum = ulRecNum;
    pstBufData->ulBufLen = ulBufLen;
    pstBufData->pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufData->ulBufLen);
    if (pstBufData->pBuf == NULL) {
        return -1;
    }
    memset_s(pstBufData->pBuf, pstBufData->ulBufLen, 0x00, pstBufData->ulBufLen);

    return DB_SUCCESS_V1;
}
void TestFreeBuf(DB_BUF_STRU *pstBufData)
{
    TEST_V1_FREE(pstBufData->pBuf);
}
void testAllData(VOS_UINT16 usRelId, VOS_UINT32 recordsNum, DB_FIELD_DEF_STRU *astFlds, uint32_t *expectvalue,
    VOS_UINT8 *pstRecord, int Type = 0)
{
    VOS_UINT32 ulLoop;
    int ret = 0;
    uint8_t *pstRecord1;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(g_dbID2, usRelId, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *pstRecord2 = (VOS_UINT8 *)TEST_V1_MALLOC(tblRecLen * recordsNum);
    for (ulLoop = 0; ulLoop < recordsNum; ulLoop++) {
        pstRecord1 = pstRecord + tblRecLen * ulLoop;
        // 获取用户设置的数据
        if(Type == 0){
            Insert(pstRecord2, expectvalue[ulLoop], tblRecLen);
        }
        else if(Type = 1){
            InsertSame(pstRecord2, expectvalue[ulLoop], tblRecLen);
        }
        
        uint32_t index = 0;
        uint32_t failcount = 0;
        ret = -1;
        while (ret != DB_SUCCESS_V1 && failcount < recordsNum) {
            ret = memcmp(pstRecord1, pstRecord2, TestGetOneFieldLen(&astFlds[0]));
            if (ret != DB_SUCCESS_V1) {
                pstRecord1 = pstRecord + tblRecLen * (failcount);
                failcount++;
            }
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i <= 5; i++) {
            index = index + TestGetOneFieldLen(&astFlds[i]);
            ret = memcmp(pstRecord1 + index, pstRecord2 + index, TestGetOneFieldLen(&astFlds[i + 1]));
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TEST_V1_FREE(pstRecord2);
}

void testAllDataBig(VOS_UINT16 usRelId, VOS_UINT32 recordsNum, DB_FIELD_DEF_STRU *astFlds, uint32_t *expectvalue,
    VOS_UINT8 *pstRecord)
{
    VOS_UINT32 ulLoop;
    int ret = 0;
    uint8_t *pstRecord1;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(g_dbID2, usRelId, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *pstRecord2 = (VOS_UINT8 *)TEST_V1_MALLOC(tblRecLen * recordsNum);
    for (ulLoop = 0; ulLoop < recordsNum; ulLoop++) {
        pstRecord1 = pstRecord + tblRecLen * ulLoop;
        // 获取用户设置的数据
        testSetAllField(pstRecord2, astFlds, expectvalue[ulLoop], tblRecLen);
        uint32_t index = 0;
        uint32_t failcount = 0;
        ret = -1;
        while (ret != DB_SUCCESS_V1 && failcount < recordsNum) {
            ret = memcmp(pstRecord1, pstRecord2, TestGetOneFieldLen(&astFlds[0]));
            if (ret != DB_SUCCESS_V1) {
                pstRecord1 = pstRecord + tblRecLen * (failcount);
                failcount++;
            }
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i <= 25; i++) {
            index = index + TestGetOneFieldLen(&astFlds[i]);
            ret = memcmp(pstRecord1 + index, pstRecord2 + index, TestGetOneFieldLen(&astFlds[i + 1]));
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TEST_V1_FREE(pstRecord2);
}
#endif
