/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 支持V1一阶段DML接口
 Notes        : 功能测试
                接口: DB_SelectAllRecByOrder
                257-263: 无索引&&小对象
                264-270: 无索引&&大对象
                271-272: 默认值匹配查询
                273: 查询不存在的数据
                274: buff不足
 Author       : nonglibin nWX860399
 Modification :
 create       : 2025/03/03
**************************************************************************** */
#include "OneStageDmlApi.h"
#include "OneStageDmlApiFuncDef.h"

class OneStageDmlApi : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        DB_ERR_CODE ret = TestDB_Init();
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 创建自定义数据类型
        DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
        customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
        ret = DB_CreateDataTypeByID(50, customCmpFunc);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_CreateDataTypeByID(51, customCmpFunc);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        TestDB_UnInit();
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void OneStageDmlApi::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    DB_ERR_CODE ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_V1_027_TestDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_V1_027_TestDbName, &g_V1_027_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}
void OneStageDmlApi::TearDown()
{
    DB_ERR_CODE ret;
    ret = DB_CloseDB(g_V1_027_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_V1_027_TestDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
}

// 257、插入小对象数据，(uint32_t、uint64_t字段)DB_OP_EQUAL等值，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_257)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t repeatCnt = 5;
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = TestInsertDsBuffRec2(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insEndVal + 1, insEndVal + i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    uint32_t insCnt = insEndVal - insStartVal + 1 + repeatCnt;

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t conVal = insEndVal + 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    TestSetCondVal(&pstCond.aCond[1], 7, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 4 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = repeatCnt;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, insEndVal + 1, insEndVal + i);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 258、插入小对象数据，(int32_t、int64_t字段)DB_OP_LARGER&&DB_OP_LESS，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_258)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t largeConVal = 40;
    uint32_t lessConVal = 50;
    TestSetCondVal(&pstCond.aCond[0], 1, DB_OP_LARGER, (VOS_UINT8 *)&largeConVal, sizeof(largeConVal));
    TestSetCondVal(&pstCond.aCond[1], 6, DB_OP_LESS, (VOS_UINT8 *)&lessConVal, sizeof(lessConVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = lessConVal - largeConVal - 1;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        uint32_t expRecVal = largeConVal + 1 + i;
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, expRecVal, expRecVal);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 259、插入小对象数据，(DBT_FLOAT&&DBT_DOUBLE)DB_OP_LARGEREQUAL&&DB_OP_LESSEQUAL，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_259)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    float largeConVal = 40.70;
    double lessConVal = 50.00;
    TestSetCondVal(&pstCond.aCond[0], 8, DB_OP_LARGEREQUAL, (VOS_UINT8 *)&largeConVal, sizeof(largeConVal));
    TestSetCondVal(&pstCond.aCond[1], 9, DB_OP_LESSEQUAL, (VOS_UINT8 *)&lessConVal, sizeof(lessConVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = (uint32_t)lessConVal - (uint32_t)largeConVal - 1;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        uint32_t expRecVal = (uint32_t)largeConVal + 1 + i;
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, expRecVal, expRecVal);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 260、插入小对象数据，(DBT_DATETIME字段)DB_OP_MIN_LARGER_EQUAL，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_260)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t largeEqConVal = 40;
    uint32_t lessEqConVal = 50;
    uint8_t *dateTineVal1 = (uint8_t *)TEST_V1_MALLOC(sizeof(DB_DATETIME_STRU));
    if (dateTineVal1 == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(dateTineVal1, sizeof(DB_DATETIME_STRU), 0x00, sizeof(DB_DATETIME_STRU));
    TestSetDateTimeFieldVal(dateTineVal1, lessEqConVal, lessEqConVal);
    uint8_t *dateTineVal2 = (uint8_t *)TEST_V1_MALLOC(sizeof(DB_DATETIME_STRU));
    if (dateTineVal2 == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(dateTineVal2, sizeof(DB_DATETIME_STRU), 0x00, sizeof(DB_DATETIME_STRU));
    TestSetDateTimeFieldVal(dateTineVal2, largeEqConVal, largeEqConVal);
    TestSetCondVal(&pstCond.aCond[0], 22, DB_OP_MIN_LARGER_EQUAL, dateTineVal2, sizeof(DB_DATETIME_STRU));
    TestSetCondVal(&pstCond.aCond[1], 23, DB_OP_MAX_LESS_EQUAL, dateTineVal1, sizeof(DB_DATETIME_STRU));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = 1;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, 0, largeEqConVal, largeEqConVal);

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放申请的内存
    TEST_V1_FREE(dateTineVal1);
    TEST_V1_FREE(dateTineVal2);

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 261、插入小对象数据，(DBT_BCD字段)DB_OP_HAVEPREFIX&&DB_OP_PREFIX12，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_261)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insCnt = 10;
    for (uint32_t i = insCnt; i > 0; i--) {
        ret = TestInsertDsBuffRec2(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, 1, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t conVal = 1;
    VOS_UINT16 bcdFldStoreLen1 = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F26", &bcdFldStoreLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *bcdVal1 = (uint8_t *)TEST_V1_MALLOC(bcdFldStoreLen1);
    if (bcdVal1 == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(bcdVal1, bcdFldStoreLen1, 0x00, bcdFldStoreLen1);
    *(uint32_t *)bcdVal1 = conVal;
    *(uint8_t *)(bcdVal1 + sizeof(uint32_t)) = ~0;
    VOS_UINT16 bcdFldStoreLen2 = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F27", &bcdFldStoreLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *bcdVal2 = (uint8_t *)TEST_V1_MALLOC(bcdFldStoreLen2);
    if (bcdVal2 == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(bcdVal2, bcdFldStoreLen2, 0x00, bcdFldStoreLen2);
    *(uint32_t *)bcdVal2 = conVal;
    *(uint8_t *)(bcdVal2 + sizeof(uint32_t)) = ~0;
    TestSetCondVal(&pstCond.aCond[0], 26, DB_OP_HAVEPREFIX, bcdVal1, bcdFldStoreLen1);
    TestSetCondVal(&pstCond.aCond[1], 27, DB_OP_PREFIX12, bcdVal2, bcdFldStoreLen2);

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 2 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = insCnt;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, 1, i + 1);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放申请的内存
    TEST_V1_FREE(bcdVal1);
    TEST_V1_FREE(bcdVal2);

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 262、插入小对象数据，(DBT_BYTES、DBT_STRING字段)DB_OP_MAX_PREFIX12，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_262)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insCnt = 10;
    for (uint32_t i = insCnt; i > 0; i--) {
        ret = TestInsertDsBuffRec2(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, 1, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t conVal = 1;
    VOS_UINT16 strFldStoreLen = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F11", &strFldStoreLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    char *strVal = (char *)TEST_V1_MALLOC(strFldStoreLen);
    if (strVal == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(strVal, strFldStoreLen, 0x00, strFldStoreLen);
    (void)sprintf_s(strVal, strFldStoreLen, "s_%06u", conVal);
    VOS_UINT16 bytesFldStoreLen = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F13", &bytesFldStoreLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *bytesVal = (uint8_t *)TEST_V1_MALLOC(bytesFldStoreLen);
    if (bytesVal == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(bytesVal, bytesFldStoreLen, 0x00, bytesFldStoreLen);
    *((VOS_UINT16 *)bytesVal) = bytesFldStoreLen - sizeof(VOS_UINT16);
    *((VOS_UINT32 *)(bytesVal + sizeof(VOS_UINT16))) = conVal;
    memset_s(bytesVal + sizeof(VOS_UINT16) + sizeof(VOS_UINT32), bytesFldStoreLen - sizeof(VOS_UINT32) -
        sizeof(VOS_UINT16), 'b', bytesFldStoreLen - sizeof(VOS_UINT32) - sizeof(VOS_UINT16));
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_MAX_PREFIX12, (uint8_t *)strVal, strFldStoreLen);
    TestSetCondVal(&pstCond.aCond[1], 13, DB_OP_EQUAL, bytesVal, bytesFldStoreLen);

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 2 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = insCnt;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, 1, i + 1);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放申请的内存
    TEST_V1_FREE(strVal);
    TEST_V1_FREE(bytesVal);

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 263、插入小对象数据，(DBT_BYTES、DBT_STRING字段)DB_OP_LIKE，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_263)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insCnt = 10;
    for (uint32_t i = 0; i < insCnt; i++) {
        ret = TestInsertDsBuffRec2(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, 1, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t conVal = 1;
    VOS_UINT16 strFldStoreLen = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F11", &strFldStoreLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    char *strVal = (char *)TEST_V1_MALLOC(strFldStoreLen);
    if (strVal == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(strVal, strFldStoreLen, 0x00, strFldStoreLen);
    (void)sprintf_s(strVal, strFldStoreLen, "%%%06u", conVal);
    VOS_UINT16 bytesFldStoreLen = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F13", &bytesFldStoreLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *bytesVal = (uint8_t *)TEST_V1_MALLOC(bytesFldStoreLen);
    if (bytesVal == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(bytesVal, bytesFldStoreLen, 0x00, bytesFldStoreLen);
    *((VOS_UINT16 *)bytesVal) = bytesFldStoreLen - sizeof(VOS_UINT16);
    *((VOS_UINT32 *)(bytesVal + sizeof(VOS_UINT16))) = conVal;
    memset_s(bytesVal + sizeof(VOS_UINT16) + sizeof(VOS_UINT32), bytesFldStoreLen - sizeof(VOS_UINT32) -
        sizeof(VOS_UINT16), 'b', bytesFldStoreLen - sizeof(VOS_UINT32) - sizeof(VOS_UINT16));
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LIKE, (uint8_t *)strVal, strFldStoreLen);
    TestSetCondVal(&pstCond.aCond[1], 13, DB_OP_EQUAL, bytesVal, bytesFldStoreLen);

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = insCnt;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, 1, i);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放申请的内存
    TEST_V1_FREE(strVal);
    TEST_V1_FREE(bytesVal);

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 264、插入大对象数据，(uint32_t、uint64_t字段)DB_OP_EQUAL等值，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_264)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValBigObjNoHaveIndex.json", &pusRelId,
        &pstRelDef, g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t repeatCnt = 5;
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = TestInsertDsBuffRec2(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insEndVal + 1, insEndVal + 1 + i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    uint32_t insCnt = insEndVal - insStartVal + 1 + repeatCnt;

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t conVal = insEndVal + 1;
    TestSetCondVal(&pstCond.aCond[1], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    TestSetCondVal(&pstCond.aCond[0], 7, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 4 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = repeatCnt;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen,
            insEndVal + 1, insEndVal + expGetRecNum - i);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 265、插入大对象数据，(int32_t、int64_t字段)DB_OP_LARGER&&DB_OP_LESS，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_265)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValBigObjNoHaveIndex.json", &pusRelId,
        &pstRelDef, g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t largeConVal = 40;
    uint32_t lessConVal = 50;
    TestSetCondVal(&pstCond.aCond[1], 1, DB_OP_LARGER, (VOS_UINT8 *)&largeConVal, sizeof(largeConVal));
    TestSetCondVal(&pstCond.aCond[0], 6, DB_OP_LESS, (VOS_UINT8 *)&lessConVal, sizeof(lessConVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = lessConVal - largeConVal - 1;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        uint32_t expRecVal = lessConVal - 1 - i;
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, expRecVal, expRecVal);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 266、插入大对象数据，(DBT_FLOAT&&DBT_DOUBLE)DB_OP_LARGEREQUAL&&DB_OP_LESSEQUAL，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_266)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValBigObjNoHaveIndex.json", &pusRelId,
        &pstRelDef, g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    float largeConVal = 40.70;
    double lessConVal = 50.00;
    TestSetCondVal(&pstCond.aCond[1], 8, DB_OP_LARGEREQUAL, (VOS_UINT8 *)&largeConVal, sizeof(largeConVal));
    TestSetCondVal(&pstCond.aCond[0], 9, DB_OP_LESSEQUAL, (VOS_UINT8 *)&lessConVal, sizeof(lessConVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = (uint32_t)lessConVal - (uint32_t)largeConVal - 1;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        uint32_t expRecVal = (uint32_t)lessConVal - 1 - i;
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, expRecVal, expRecVal);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 267、插入大对象数据，(DBT_DATETIME字段)DB_OP_MIN_LARGER_EQUAL，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_267)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValBigObjNoHaveIndex.json", &pusRelId,
        &pstRelDef, g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t largeEqConVal = 40;
    uint32_t lessEqConVal = 50;
    uint8_t *dateTineVal1 = (uint8_t *)TEST_V1_MALLOC(sizeof(DB_DATETIME_STRU));
    if (dateTineVal1 == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(dateTineVal1, sizeof(DB_DATETIME_STRU), 0x00, sizeof(DB_DATETIME_STRU));
    TestSetDateTimeFieldVal(dateTineVal1, lessEqConVal, lessEqConVal);
    uint8_t *dateTineVal2 = (uint8_t *)TEST_V1_MALLOC(sizeof(DB_DATETIME_STRU));
    if (dateTineVal2 == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(dateTineVal2, sizeof(DB_DATETIME_STRU), 0x00, sizeof(DB_DATETIME_STRU));
    TestSetDateTimeFieldVal(dateTineVal2, largeEqConVal, largeEqConVal);
    TestSetCondVal(&pstCond.aCond[1], 22, DB_OP_MIN_LARGER_EQUAL, dateTineVal2, sizeof(DB_DATETIME_STRU));
    TestSetCondVal(&pstCond.aCond[0], 23, DB_OP_MAX_LESS_EQUAL, dateTineVal1, sizeof(DB_DATETIME_STRU));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = 1;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, 0, lessEqConVal, lessEqConVal);

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放申请的内存
    TEST_V1_FREE(dateTineVal1);
    TEST_V1_FREE(dateTineVal2);

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 268、插入大对象数据，(DBT_BCD字段)DB_OP_HAVEPREFIX&&DB_OP_PREFIX12，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_268)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValBigObjNoHaveIndex.json", &pusRelId,
        &pstRelDef, g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insCnt = 10;
    for (uint32_t i = insCnt; i > 0; i--) {
        ret = TestInsertDsBuffRec2(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, 1, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t conVal = 1;

    // 对于超过256字节的字段长度来说，条件的设置需要给条件buff的地址
    VOS_UINT16 bcdFldStoreLen1 = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F26", &bcdFldStoreLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *bcdVal1 = (uint8_t *)TEST_V1_MALLOC(bcdFldStoreLen1);
    if (bcdVal1 == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(bcdVal1, bcdFldStoreLen1, 0x00, bcdFldStoreLen1);
    *(uint32_t *)bcdVal1 = conVal;
    *(uint8_t *)(bcdVal1 + sizeof(uint32_t)) = ~0;
    VOS_UINT16 bcdFldStoreLen2 = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F27", &bcdFldStoreLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *bcdVal2 = (uint8_t *)TEST_V1_MALLOC(bcdFldStoreLen2);
    if (bcdVal2 == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(bcdVal2, bcdFldStoreLen2, 0x00, bcdFldStoreLen2);
    *(uint32_t *)bcdVal2 = conVal;
    *(uint8_t *)(bcdVal2 + sizeof(uint32_t)) = ~0;
    TestSetCondVal(&pstCond.aCond[1], 26, DB_OP_HAVEPREFIX, bcdVal1, sizeof(uint8_t *), true);
    TestSetCondVal(&pstCond.aCond[0], 27, DB_OP_PREFIX12, bcdVal2, sizeof(uint8_t *), true);

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 2 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = insCnt;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, 1, expGetRecNum - i);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放申请的内存
    TEST_V1_FREE(bcdVal1);
    TEST_V1_FREE(bcdVal2);

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 269、插入大对象数据，(DBT_BYTES、DBT_STRING字段)DB_OP_MAX_PREFIX12，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_269)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValBigObjNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insCnt = 10;
    for (uint32_t i = insCnt; i > 0; i--) {
        ret = TestInsertDsBuffRec2(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, 1, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t conVal = 1;
    VOS_UINT16 strFldStoreLen = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F11", &strFldStoreLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    char *strVal = (char *)TEST_V1_MALLOC(strFldStoreLen);
    if (strVal == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(strVal, strFldStoreLen, 0x00, strFldStoreLen);
    (void)sprintf_s(strVal, strFldStoreLen, "s_%06u", conVal);
    VOS_UINT16 bytesFldStoreLen = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F13", &bytesFldStoreLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *bytesVal = (uint8_t *)TEST_V1_MALLOC(bytesFldStoreLen);
    if (bytesVal == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(bytesVal, bytesFldStoreLen, 0x00, bytesFldStoreLen);
    *((VOS_UINT16 *)bytesVal) = bytesFldStoreLen - sizeof(VOS_UINT16);
    *((VOS_UINT32 *)(bytesVal + sizeof(VOS_UINT16))) = conVal;
    memset_s(bytesVal + sizeof(VOS_UINT16) + sizeof(VOS_UINT32), bytesFldStoreLen - sizeof(VOS_UINT32) -
        sizeof(VOS_UINT16), 'b', bytesFldStoreLen - sizeof(VOS_UINT32) - sizeof(VOS_UINT16));
    TestSetCondVal(&pstCond.aCond[1], 11, DB_OP_MAX_PREFIX12, (uint8_t *)strVal, sizeof(char *), true);
    TestSetCondVal(&pstCond.aCond[0], 13, DB_OP_EQUAL, bytesVal, sizeof(uint8_t *), true);

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 2 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = insCnt;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, 1, expGetRecNum - i);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放申请的内存
    TEST_V1_FREE(strVal);
    TEST_V1_FREE(bytesVal);

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 270、插入大对象数据，(DBT_BYTES、DBT_STRING字段)DB_OP_LIKE，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_270)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValBigObjNoHaveIndex.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insCnt = 10;
    for (uint32_t i = 0; i < insCnt; i++) {
        ret = TestInsertDsBuffRec2(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, 1, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    uint32_t conVal = 1;
    VOS_UINT16 strFldStoreLen = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F11", &strFldStoreLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    char *strVal = (char *)TEST_V1_MALLOC(strFldStoreLen);
    if (strVal == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(strVal, strFldStoreLen, 0x00, strFldStoreLen);
    (void)sprintf_s(strVal, strFldStoreLen, "%%%06u", conVal);
    VOS_UINT16 bytesFldStoreLen = 0;
    ret = TestDBGetFldStoreLen(g_V1_027_TestDbId, pusRelId, "F13", &bytesFldStoreLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *bytesVal = (uint8_t *)TEST_V1_MALLOC(bytesFldStoreLen);
    if (bytesVal == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    memset_s(bytesVal, bytesFldStoreLen, 0x00, bytesFldStoreLen);
    *((VOS_UINT16 *)bytesVal) = bytesFldStoreLen - sizeof(VOS_UINT16);
    *((VOS_UINT32 *)(bytesVal + sizeof(VOS_UINT16))) = conVal;
    memset_s(bytesVal + sizeof(VOS_UINT16) + sizeof(VOS_UINT32), bytesFldStoreLen - sizeof(VOS_UINT32) -
        sizeof(VOS_UINT16), 'b', bytesFldStoreLen - sizeof(VOS_UINT32) - sizeof(VOS_UINT16));
    TestSetCondVal(&pstCond.aCond[1], 11, DB_OP_LIKE, (uint8_t *)strVal, sizeof(char *), true);
    TestSetCondVal(&pstCond.aCond[0], 13, DB_OP_EQUAL, bytesVal, sizeof(uint8_t *), true);

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 4 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = insCnt;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < expGetRecNum; i++) {
        TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, i * recLen, 1, expGetRecNum - 1 - i);
    }

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放申请的内存
    TEST_V1_FREE(strVal);
    TEST_V1_FREE(bytesVal);

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 271、插入小对象数据，(uint32_t、uint64_t字段)DB_OP_EQUAL默认值匹配，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_271)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefVal.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 0;
    uint32_t insEndVal = 99;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 100;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = 1;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, 0, 0, 0);

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 272、插入大对象数据，(uint32_t、uint64_t字段)DB_OP_EQUALL默认值匹配，查询检验数据
TEST_F(OneStageDmlApi, V1Com_027_272)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;
    DB_DSBUF_STRU pstDsBufCheck;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefValBigObj.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 0;
    uint32_t insEndVal = 99;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 100;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 预期返回的数据量
    uint32_t expGetRecNum = 1;

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 存放预期的数据
    ret = TestMallocDsBuf(&pstDsBufCheck, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestSetDsBuf(pstRelDef.pstFldLst, pstRelDef.ulNCols, &pstDsBufCheck, 0, 0, 0);

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 检查数据
        V1_AW_MACRO_ASSERT_EQ_INT(expGetRecNum, pstDsBufGet.usRecNum);
        ret = memcmp(pstDsBufGet.StdBuf.pucData, pstDsBufCheck.StdBuf.pucData, pstDsBufGet.usRecNum * recLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);
    TestFreeDsBuf(&pstDsBufCheck);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 273、插入数据，查询不存在的记录
TEST_F(OneStageDmlApi, V1Com_027_273)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefVal.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 101;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen * insCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 274、插入数据，查询数据，但是给定的buff不足
TEST_F(OneStageDmlApi, V1Com_027_274)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBufGet;

    // 建表
    VOS_UINT16 pusRelId;
    DB_REL_DEF_STRU pstRelDef;
    ret = TestDB_CreateTbl(g_V1_027_TestDbId, "./schema_file/allTypeDefVal.json", &pusRelId, &pstRelDef,
        g_V1_027_TestTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    uint32_t recLen = 0;
    ret = TestDBGetTblRecLen(g_V1_027_TestDbId, pusRelId, &recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    uint32_t insStartVal = 1;
    uint32_t insEndVal = 100;
    uint32_t insCnt = insEndVal - insStartVal + 1;
    ret = TestInsertDsBuffRec(g_V1_027_TestDbId, pusRelId, &pstRelDef, recLen, insStartVal, insEndVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置排序方式
    DB_SORT_STRU pstSort = { .ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND };
    T_FIELD sortFlds[pstSort.ucSortNum] = { 0 };
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放获取的数据
    ret = TestMallocDsBuf(&pstDsBufGet, recLen, insCnt, recLen - 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 遍历调用同参数的接口
    for (uint32_t i = 0; i < sizeof(g_selectAllRecByOrder) / sizeof(Func_SelectAllRecByOrder); i++) {
        // 查询
        memset_s(pstDsBufGet.StdBuf.pucData, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.usRecNum = insCnt;
        ret = g_selectAllRecByOrder[i](g_V1_027_TestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    }

    // 释放获取和预期的数据
    TestFreeDsBuf(&pstDsBufGet);

    // 删表
    ret = DB_DropTbl(g_V1_027_TestDbId, pusRelId, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef);

    AW_FUN_Log(LOG_STEP, "test end.");
}

