
#include "V1_DDLCfg.h"
int ret = 0;
#define cfg1 "/cfgPath/gmserver.ini"
class DBinterface : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DBinterface::SetUpTestCase()
{}

void DBinterface::TearDownTestCase()
{}

void DBinterface::SetUp()
{
    ret = TestDB_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("mkdir cfgPath");
    system("touch cfgPath/demo.txt");
    AW_CHECK_LOG_BEGIN();
}

void DBinterface::TearDown()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
    system("rm -rf ./cfgPath");
    AW_CHECK_LOG_END();
}
class DBinterface1 : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DBinterface1::SetUpTestCase()
{}

void DBinterface1::TearDownTestCase()
{}

void DBinterface1::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void DBinterface1::TearDown()
{
    AW_CHECK_LOG_END();
}
class DBinterface2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
    };
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DBinterface2::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void DBinterface2::TearDown()
{
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    printf("\n=====================TEST:END=====================\n");
}

// 077.创建DB，OpenDB、CloseDB和DropDB，预期成功
TEST_F(DBinterface, V1Com_023_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 078.创建DB，DropDB，预期成功
TEST_F(DBinterface, V1Com_023_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 079.同目录下创建同名的DB，预期创建失败
TEST_F(DBinterface, V1Com_023_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char dbDir[256] = "./cfgPath/demo.txt";
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);

    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 080.不同目录下创建同名的DB，预期创建失败
TEST_F(DBinterface, V1Com_023_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char dbDir[256] = "./cfgPath/demo.txt";
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);

    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 081.同目录下创建多个不同名的DB，预期创建成功
TEST_F(DBinterface, V1Com_023_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbNameB[20] = "dbNameB";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDB((VOS_UINT8 *)dbNameB, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropDB((VOS_UINT8 *)dbNameB, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 082.CreateDB的pucDbName以\0开头，预期创建DB失败
TEST_F(DBinterface, V1Com_023_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char dbNameA[20] = "\0dbNameA";
    ret = DB_CreateDB((VOS_UINT8 *)dbNameA, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 083.CreateDB的pubDbDir为用户指定路径，预期创建DB成功
TEST_F(DBinterface, V1Com_023_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char dbDir[256] = "./cfgPath/demo.txt";
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 084.持久化下，CreateDB的pubDbDir为用户指定路径，预期创建DB成功
TEST_F(DBinterface, V1Com_023_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbDir[256] = "./cfgPath/demo.txt";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 085.创建DB，OpenDB 2次，预期OpenDB成功
TEST_F(DBinterface, V1Com_023_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 086.持久化下，pucDbDir和create时不一致，预期open失败
TEST_F(DBinterface, V1Com_023_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char dbDir[256] = "./cfgPath/demo.txt";
    char dberrDir[256] = "./filePath";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = DB_OpenDB((VOS_UINT8 *)dberrDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBPATH, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 087.持久化DB下，建DB后第一次open路径正确，后续多次使用错误路径open
TEST_F(DBinterface, V1Com_023_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char dbDir[256] = "./cfgPath/demo.txt";
    char dberrDir[256] = "./filePath";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB((VOS_UINT8 *)dberrDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBPATH, ret);
    ret = DB_OpenDB((VOS_UINT8 *)dberrDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBPATH, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 088.创建DB1、DB2，OpenDB1，CloseDB2，预期CloseDB2失败
TEST_F(DBinterface, V1Com_023_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbNameB[20] = "dbNameB";
    uint32_t testDbId = 1;
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDB((VOS_UINT8 *)dbNameB, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbNameA, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropDB((VOS_UINT8 *)dbNameB, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 089.创建DB，不OpenDB，CloseDB，预期Close失败
TEST_F(DBinterface, V1Com_023_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 090.创建DB，OpenDB 1次，CloseDB 2次，预期第二次CloseDB失败
TEST_F(DBinterface, V1Com_023_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbNameA, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 091.持久化DB下，建表后closeDB，再创建同名表
TEST_F(DBinterface, V1Com_023_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char dbDir[256] = "./cfgPath/demo.txt";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = DB_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建立同名表
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 092.持久化DB下，建DB后开启6次后建表，关闭5次后创建同名表
TEST_F(DBinterface, V1Com_023_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char dbDir[256] = "./cfgPath/demo.txt";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    // 开启6次
    for (int i = 0; i < 6; i++) {
        ret = DB_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭5次
    for (int i = 0; i < 5; i++) {
        ret = DB_CloseDB(g_testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 建立同名表
    uint16_t newtestRelId = 0;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &newtestRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_REL_ALREADY_EXIST, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 093.创建DB，OpenDB，不CloseDB，DropDB，预期DropDB失败
TEST_F(DBinterface, V1Com_023_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbNameA, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DROP_NOTALLOWED, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 094.创建DB，OpenDB 2次和CloseDB 1次数，DropDB，预期DropDB失败
TEST_F(DBinterface, V1Com_023_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbNameA, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbNameA, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DROP_NOTALLOWED, ret);

    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbNameA, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 095.DB中有表，不删表Droptable，预期Drop成功
TEST_F(DBinterface, V1Com_023_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef = {0};
    TestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);

    uint16_t relId;
    char tblName[DB_NAME_LEN];
    for (uint32_t i = 0; i < 1; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s(tblName, DB_NAME_LEN, "tbl%u", i);
        (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
        EXPECT_EQ(DB_SUCCESS_V1, DB_CreateTbl(dbId, &stRelDef, &relId));
    }
    (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&stRelDef);

    // CLoseDB
    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 096 DB中有表，不删表、不CloseDB，预期CloseDB失败
TEST_F(DBinterface, V1Com_023_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef = {0};
    TestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);

    uint16_t relId;
    char tblName[DB_NAME_LEN];
    for (uint32_t i = 0; i < 1; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s(tblName, DB_NAME_LEN, "tbl%u", i);
        (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
        EXPECT_EQ(DB_SUCCESS_V1, DB_CreateTbl(dbId, &stRelDef, &relId));
    }
    (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&stRelDef);

    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DROP_NOTALLOWED, ret);

    // CLoseDB
    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 097.不打开DB进行删除DB
TEST_F(DBinterface, V1Com_023_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // CreateDB
    char dbNameA[20] = "dbNameA";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbNameA, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbNameA, DROP_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
void *dropDb(void *args)
{
    signal(SIGALRM, TimeoutHandler);
    // 等待查询开启
    while (1) {
        if (g_controlFlag == true) {
            break;
        }
    }
    // 设置定时器
    alarm(2);
    // 删DB
    ret = DB_DropDB((VOS_UINT8 *)"testDdl", DROP_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 取消定时器
    alarm(0);
}
void *dmlOptionAfterDropDb(void *args)
{
    // dml操作
    sleep(1);
    g_controlFlag = true;
    // 等待删除超时,等待时间超十秒说明删除未阻塞，直接报错
    time_t startTime = time(NULL);
    while (1) {
        if (g_controlFlag == false) {
            break;
        }
        time_t currentTime = time(NULL);
        double elapsedTime = difftime(currentTime, startTime);
        if (elapsedTime >= 10.0) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
            break;
        }
    }
    // 关闭DB
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 098.多次打开DB关闭DB后删除DB
TEST_F(DBinterface, V1Com_023_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭三次打开三次
    for (int i = 0; i < 3; i++) {
        ret = DB_CloseDB(g_testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    pthread_t tid1, tid2;
    // 并发dml操作和删db操作
    g_controlFlag = false;
    ret = pthread_create(&tid1, NULL, dropDb, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid2, NULL, dmlOptionAfterDropDb, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid1, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid2, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 099.存在多个DB时删除某一个DB
TEST_F(DBinterface, V1Com_023_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testDbName2[DB_NAME_LEN] = "testDb2";
    char testDbName3[DB_NAME_LEN] = "testDb3";
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CreateDB((VOS_UINT8 *)testDbName2, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CreateDB((VOS_UINT8 *)testDbName3, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId2 = 0;
    uint32_t testDbId3 = 0;
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName2, &testDbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName3, &testDbId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB1建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB2建表
    uint16_t testRelId2 = 0;
    ret = TestDB_CreateTbl(testDbId2, "schema_file/vertexlabel1.json", &testRelId2, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB3建表
    uint16_t testRelId3 = 0;
    ret = TestDB_CreateTbl(testDbId3, "schema_file/vertexlabel1.json", &testRelId3, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    pthread_t tid1, tid2;
    // 并发dml操作和删db操作
    g_controlFlag = false;
    ret = pthread_create(&tid1, NULL, dropDb, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid2, NULL, dmlOptionAfterDropDb, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid1, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid2, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB2、3
    ret = DB_CloseDB(testDbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(testDbId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删DB2、3
    ret = DB_DropDB((VOS_UINT8 *)"testDb2", DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"testDb3", DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 100.DB_UnInit后进行CreateDB，预期createDB失败
TEST_F(DBinterface1, V1Com_023_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = TestDB_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TestDB_UnInit();

    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);// 现在会直接在函数内init，预期修改为成功

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 101.DB_Init中cfgParameter为配置文件gmserver.ini的路径
TEST_F(DBinterface1, V1Com_023_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = TestDB_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TestDB_UnInit();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 102.DB_Init中cfgParameter为非配置文件路径
TEST_F(DBinterface1, V1Com_023_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("mkdir cfgPath");
    system("cp /usr/local/file/gmserver.ini ./cfgPath");
    const char *cfg = "./cfgPath/gmserver.ini";

    ret = DB_Init(cfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_UnInit();
    system("rm -rf ./cfgPath");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 103. 用户创建gmserver.ini，预期init失败
TEST_F(DBinterface1, V1Com_023_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("mkdir cfgPath1");
    system("touch cfgPath1/gmserver.ini");
    const char *cfg = "./cfgPath1/gmserver.ini";

    ret = DB_Init(cfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_UnInit();
    system("rm -rf ./cfgPath1");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 104.DB_Init中cfgParameter为非配置文件路径
TEST_F(DBinterface1, V1Com_023_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("mkdir cfgPath");
    system("cp /usr/local/file/gmserver.ini ./cfgPath/a.ini");
    const char *cfg = "./cfgPath/a.ini";

    ret = DB_Init(cfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_UnInit();
    system("rm -rf ./cfgPath");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 105.通过建表验证重复init后，验证配置文件生效的是第一个
TEST_F(DBinterface1, V1Com_023_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_sysGMDBCfg[256] = {0};
    system("mkdir cfgPath");
    system("cp /usr/local/file/gmserver.ini ./cfgPath/b.ini");
    system("sed -i \"s/^maxNormalTableNum =.*$/maxNormalTableNum = 1000/g\" ./cfgPath/b.ini");
    const char *cfg = "./cfgPath/b.ini";

    // 初始化
    ret = DB_Init("./cfgPath/b.ini");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 再次初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 dbId = 0;
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef = {0};
    TestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);

    uint16_t relId;
    char tblName[DB_NAME_LEN];
    for (uint32_t i = 0; i < 2000; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s(tblName, DB_NAME_LEN, "tbl%u", i);
        (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
        ret = DB_CreateTbl(dbId, &stRelDef, &relId);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "ret = %#x, 建表数 %d", ret, i);
            V1_AW_MACRO_EXPECT_EQ_INT(1000, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&stRelDef);

    ret = DB_DropTbl(dbId, relId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 去初始化
    DB_UnInit();
    system("sed -i \"s/^maxNormalTableNum =.*$/maxNormalTableNum = 2000/g\" ./cfgPath/b.ini");
    system("rm -rf ./cfgPath");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 106.设置文件路径时使用绝对路径
TEST_F(DBinterface1, V1Com_023_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("mkdir /cfgPath");
    system("cp /usr/local/file/gmserver.ini /cfgPath");

    ret = DB_Init(cfg1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_UnInit();
    system("rm -rf /cfgPath");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 107.CreateDBEx的pucDbName大于16字节，预期创建DB失败
TEST_F(DBinterface, V1Com_023_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[20] = "dbNameABCDEFGHIJK\0";  // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};          // DB的相关数据等配置
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 108.CreateDBEx的pucDbName以\0开头，预期创建DB失败
TEST_F(DBinterface, V1Com_023_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[20] = "\0dbNameA";    // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 109.CreateDBEx的pucDbName为空字符串，预期创建DB失败
TEST_F(DBinterface, V1Com_023_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    ret = DB_CreateDBEx(NULL, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 110.CreateDBEx的pubDbDir为用户创建路径，预期创建DB成功
TEST_F(DBinterface, V1Com_023_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // CreateDB
    char dbName[20] = "dbName";       // DB名字
    char dbDir[256] = "./cfgPath";    // DB路径
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 111.持久化下，CreateDBEx的pubDbDir为用户创建路径，预期创建DB成功
TEST_F(DBinterface, V1Com_023_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir testDbPath");
    system("touch testDbPath/test.txt");
    // CreateDB
    char dbName[20] = "dbName";                 // DB名字
    char dbDir[256] = "./testDbPath/test.txt";  // DB路径
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 112.CreateDBEx的enStore传入DB_DATA_STORAGE_RAM，预期创建DB成功
TEST_F(DBinterface, V1Com_023_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[20] = "dbNameA";      // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 113.CreateDBEx的enStore传入DB_DATA_STORAGE_RSM，预期创建DB失败
TEST_F(DBinterface, V1Com_023_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[20] = "dbNameA";      // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RSM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 114.CreateDBEx的enStore传入DB_DATA_STORAGE_BUTT，预期创建DB失败
TEST_F(DBinterface, V1Com_023_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[20] = "dbNameA";      // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_BUTT);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 115.CreateDBEx的enStore传入枚举值为3，预期创建DB失败
TEST_F(DBinterface, V1Com_023_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[20] = "dbNameA";      // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, (DB_DATA_STORAGE_ENUM)3);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 116.循环创建DB，OpenDB，CloseDB，DropDB 100次
TEST_F(DBinterface, V1Com_023_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    for (int i = 0; i < 100; i++) {
        ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_CloseDB(g_testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}
void *CREATEDB(void *args)
{
    int res = 0;
    AW_FUN_Log(LOG_STEP, "create db start.");
    res = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, res);
    AW_FUN_Log(LOG_STEP, "create db end.");
}
void *OPENDB(void *args)
{
    int res = 0;
    AW_FUN_Log(LOG_STEP, "open db start.");
    res = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    if (res != DB_SUCCESS_V1) {
        V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, res);
    }
    AW_FUN_Log(LOG_STEP, "open db end.");
}
void *CLOSEDB(void *args)
{
    int res = 0;
    AW_FUN_Log(LOG_STEP, "close db start.");
    res = DB_CloseDB(g_testDbId);
    if (res != DB_SUCCESS_V1) {
        if (res != VOS_ERRNO_DB_DATABASE_NOT_OPENED) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, res);
        }
    }
    AW_FUN_Log(LOG_STEP, "close db end.");
}
void *DROPDB(void *args)
{
    int res = 0;
    AW_FUN_Log(LOG_STEP, "drop db start.");
    res = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    if (res != DB_SUCCESS_V1) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "drop db end.");
}
// 117.4个线程分别进行CreateDB，OpenDB，CloseDB和DropDB
TEST_F(DBinterface, V1Com_023_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t thread_num = 4;
    pthread_t client_thr[thread_num];

    ret = pthread_create(&client_thr[0], NULL, CREATEDB, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&client_thr[1], NULL, OPENDB, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    usleep(50000);
    ret = pthread_create(&client_thr[2], NULL, CLOSEDB, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&client_thr[3], NULL, DROPDB, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 118.指定表ID建表（表ID未被使用），表定义符合约束
TEST_F(DBinterface2, V1Com_023_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 3;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 指定表ID建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 119.指定表ID65535建表
TEST_F(DBinterface2, V1Com_023_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 65535;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 指定表ID建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 120.指定表ID65534建表
TEST_F(DBinterface2, V1Com_023_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 65534;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 指定表ID建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 121.删表（表存在）
TEST_F(DBinterface2, V1Com_023_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 删表
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 122.删表（表不存在）
TEST_F(DBinterface2, V1Com_023_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};

    // 删表
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
}
// 123.指定表ID建表（表ID已被使用）
TEST_F(DBinterface2, V1Com_023_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 3;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 指定表ID建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 再次建表
    const char newTblName[] = "newTable";
    TestCreateTblInitRelDef(&testRelDef, newTblName);
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RELID_ALREADY_USED, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 124.建表，表定义索引重复（索引名相同）
TEST_F(DBinterface2, V1Com_023_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char idxName[] = "sameIdxName";
    const char idxName1[] = "sameIdxName";
    TestCreateTblInitRelDefWithIdxName(&testRelDef, idxName, idxName1);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INDEX_REPEATED, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 125.建表，表定义字段名重复
TEST_F(DBinterface2, V1Com_023_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char *fldName = "SamefldName";
    const char *fldName1 = "SamefldName";
    TestCreateTblInitRelDefWithFld(&testRelDef, fldName, fldName1);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_FIELD_REPEATED, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 126.建表，存在同名表
TEST_F(DBinterface2, V1Com_023_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次建同名表
    ret = DB_CreateTblEx(g_testDbId, &testRelDef, &testRelId, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_REL_ALREADY_EXIST, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 127.建表，DB未打开
TEST_F(DBinterface2, V1Com_023_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 关闭数据库
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 打开数据库
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 128.指定表ID建表（TPC_CreateTblByIDEx）
TEST_F(DBinterface2, V1Com_023_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 3;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 指定表ID建表
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 129.建表，表定义字段数设置为0
TEST_F(DBinterface2, V1Com_023_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t fldNum = 0;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefWithfldNum(&testRelDef, fldNum);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_FIELDNUM, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 130.建表，表定义字段数设置为0
TEST_F(DBinterface2, V1Com_023_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint8_t ucUniqueFlag = 1;
    TestCreateTblInitRelDefWithIdxUnique(&testRelDef, ucUniqueFlag);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 131.建表，索引定义标识非唯一索引（ucUniqueFlag = 0）
TEST_F(DBinterface2, V1Com_023_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint8_t ucUniqueFlag = 0;
    TestCreateTblInitRelDefWithIdxUnique(&testRelDef, ucUniqueFlag);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 132.建表，索引定义标识非法（ucUniqueFlag = 2）
TEST_F(DBinterface2, V1Com_023_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint8_t ucUniqueFlag = 2;
    TestCreateTblInitRelDefWithIdxUnique(&testRelDef, ucUniqueFlag);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 133.建表，表定义索引重复（索引字段数相同&&索引类型相同&&索引中包含相同的字段）
TEST_F(DBinterface2, V1Com_023_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefWithIdxRepFld(&testRelDef);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INDEX_REPEATED, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 134.建表，索引定义中的字段无效(表定义的字段不包含该字段id)
TEST_F(DBinterface2, V1Com_023_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefWithIdxRepFld2(&testRelDef);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDIDXFLD, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 135.建表，索引定义中的字段类型合法（包含所有支持的字段类型）
TEST_F(DBinterface2, V1Com_023_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t idxNum = 26;
    TestCreateTblInitRelDefWithIdxLegality(&testRelDef, idxNum);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 136.修改表ID和表名（表名与ID不为空）
TEST_F(DBinterface2, V1Com_023_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 修改表名和表ID
    uint16_t newRelId = 3;
    const char newTblName[] = "newTblName";
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId, (VOS_UINT8 *)newTblName, newRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建新表名的表，预期失败
    TestCreateTblInitRelDef(&testRelDef, newTblName);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_REL_ALREADY_EXIST, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 新表ID删表，预期成功
    ret = DB_DropTbl(g_testDbId, newRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void *createTbl(void *args)
{
    for (int i = 0; i < 10; i++) {
        uint16_t testRelId = 0;
        DB_REL_DEF_STRU testRelDef = {0};
        TestCreateTblInitRelDef(&testRelDef, NULL);

        // 建表
        ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
        if (ret != DB_SUCCESS_V1) {
            AW_MACRO_EXPECT_NE_INT(DB_SUCCESS_V1, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 释放字段定义和索引定义的内存
        TestFreeTblStructDef(&testRelDef);
    }
}

void *createTblByID(void *args)
{
    for (int i = 0; i < 10; i++) {
        uint16_t testRelId = 3;
        DB_REL_DEF_STRU testRelDef = {0};
        TestCreateTblInitRelDef(&testRelDef, NULL);

        // 指定表ID建表
        ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
        if (ret != DB_SUCCESS_V1) {
            AW_MACRO_EXPECT_NE_INT(DB_SUCCESS_V1, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 释放字段定义和索引定义的内存
        TestFreeTblStructDef(&testRelDef);
    }
}

void *modifyTblNameAndID(void *args)
{
    for (int i = 0; i < 10; i++) {
        // 修改表名和表ID
        uint32_t newRelId = i;
        uint16_t testRelId = 0;
        const char newTblName[] = "newTblName";
        ret = DB_ModifyTblNameAndID(g_testDbId, testRelId, (VOS_UINT8 *)newTblName, newRelId);
        if (ret != DB_SUCCESS_V1) {
            AW_MACRO_EXPECT_NE_INT(DB_SUCCESS_V1, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
}

void *setMaxRecNumOfTable(void *args)
{
    for (int i = 0; i < 10; i++) {
        // 修改表的最大记录数
        uint16_t testRelId = 0;
        uint32_t ulNewMaxRecNum = 10001 + i;
        ret = DB_SetMaxRecNumOfTable(g_testDbId, testRelId, 0, 0, ulNewMaxRecNum);
        if (ret != DB_SUCCESS_V1) {
            AW_MACRO_EXPECT_NE_INT(DB_SUCCESS_V1, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
}

void *setExtendRecNum(void *args)
{
    for (int i = 0; i < 10; i++) {
        // 设置扩容
        uint16_t testRelId = 0;
        ret = DB_SetExtendRecNum(g_testDbId, testRelId, 0);
        if (ret != DB_SUCCESS_V1) {
            AW_MACRO_EXPECT_NE_INT(DB_SUCCESS_V1, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
}

void *dropTbl(void *args)
{
    for (int i = 0; i < 10; i++) {
        // 删除表
        uint16_t testRelId = 0;
        ret = DB_DropTbl(g_testDbId, testRelId, 0);
        if (ret != DB_SUCCESS_V1) {
            AW_MACRO_EXPECT_NE_INT(DB_SUCCESS_V1, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
}
// 137.并发线程循环调所有接口（同一张表），预期无core
TEST_F(DBinterface2, V1Com_023_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    pthread_t tid1, tid2, tid3, tid4, tid5, tid6;
    ret = pthread_create(&tid1, NULL, createTbl, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid2, NULL, createTblByID, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid3, NULL, modifyTblNameAndID, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid4, NULL, setMaxRecNumOfTable, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid5, NULL, setExtendRecNum, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid6, NULL, dropTbl, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = pthread_join(tid1, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid2, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid3, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid4, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid5, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid6, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 138.修改表ID（表名为null，ID不为空）
TEST_F(DBinterface2, V1Com_023_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 修改表ID
    uint16_t newRelId = 3;
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId, NULL, newRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 旧表ID删表，预期失败
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);

    // 新表ID删表，预期成功
    ret = DB_DropTbl(g_testDbId, newRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 139.修改表ID（表名为null，表ID不变）
TEST_F(DBinterface2, V1Com_023_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 修改表ID
    uint16_t newRelId = 0;
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId, NULL, newRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 新表ID删表，预期成功
    ret = DB_DropTbl(g_testDbId, newRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 140.修改表ID（表名为null，表ID为0xffff）
TEST_F(DBinterface2, V1Com_023_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 修改表ID
    uint16_t newRelId = 0xffff;
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId, NULL, newRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 新表ID删表，预期失败
    ret = DB_DropTbl(g_testDbId, newRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);

    // 旧表ID删表，预期成功
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 141.修改表ID和表名（表名非法）
TEST_F(DBinterface2, V1Com_023_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 修改表名和表ID(表名和表ID都修改失败)
    uint16_t newRelId = 3;
    const char newTblName[] = "newTblNameHaveSixteen";
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId, (VOS_UINT8 *)newTblName, newRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_RELNAME, ret);

    // 新表ID删表，预期失败
    ret = DB_DropTbl(g_testDbId, newRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);

    // 旧表ID删表，预期成功
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 142.修改表ID和表名（表名和表ID已存在）
TEST_F(DBinterface2, V1Com_023_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    DB_REL_DEF_STRU testRelDef = {0};
    DB_REL_DEF_STRU testRelDef1 = {0};
    const char testTblName[DB_NAME_LEN] = "testTbl1";
    const char testTblName1[DB_NAME_LEN] = "testTbl2";
    TestCreateTblInitRelDef(&testRelDef, testTblName);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    TestCreateTblInitRelDef(&testRelDef1, testTblName1);
    ret = DB_CreateTbl(g_testDbId, &testRelDef1, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef1);

    // 修改表名和表ID
    uint16_t newRelId = 1;
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId, (VOS_UINT8 *)testTblName1, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_REL_ALREADY_EXIST, ret);

    // 新表ID删表
    ret = DB_DropTbl(g_testDbId, testRelId1, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 旧表ID删表，预期成功
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 143.修改表名（表名不为空，ID为0xffff）
TEST_F(DBinterface2, V1Com_023_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 修改表名
    const char newTblName[] = "newTblName";
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId, (VOS_UINT8 *)newTblName, 0xffff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建新表名的表，预期失败
    TestCreateTblInitRelDef(&testRelDef, newTblName);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_REL_ALREADY_EXIST, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 144.修改表的最大记录数（ulNewMaxRecNum等于0xFFFFFFFF)
TEST_F(DBinterface2, V1Com_023_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 修改表的最大记录数
    uint32_t ulNewMaxRecNum = 4294967295;
    ret = DB_SetMaxRecNumOfTable(g_testDbId, testRelId, 0, 0, ulNewMaxRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_MAX_REC, ret);

    // 传入无效表ID
    uint16_t invalidRelId = 6;
    ret = DB_SetMaxRecNumOfTable(g_testDbId, invalidRelId, 0, 0, ulNewMaxRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 145.修改表的最大记录数（ulNewMaxRecNum小于当前最大值）
TEST_F(DBinterface2, V1Com_023_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 修改表的最大记录数
    uint32_t ulNewMaxRecNum = 10001;
    ret = DB_SetMaxRecNumOfTable(g_testDbId, testRelId, 0, 0, ulNewMaxRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulNewMaxRecNum1 = 10000;
    ret = DB_SetMaxRecNumOfTable(g_testDbId, testRelId, 0, 0, ulNewMaxRecNum1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_MAX_REC, ret);

    // 传入无效表ID
    uint16_t invalidRelId = 6;
    ret = DB_SetMaxRecNumOfTable(g_testDbId, invalidRelId, 0, 0, ulNewMaxRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 146.正常调用clone接口复制表
TEST_F(DBinterface2, V1Com_023_146)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    ret = DB_CloneTable(g_testDbId, testRelId, testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(testDbId, testRelId1, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 147.表内包含大对象，复制表
TEST_F(DBinterface2, V1Com_023_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloneTable(g_testDbId, testRelId, testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(testDbId, testRelId1, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 148.表内包含所有数据类型，复制表
TEST_F(DBinterface2, V1Com_023_148)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    ret = DB_CloneTable(g_testDbId, testRelId, testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(testDbId, testRelId1, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 149.目的库中存在同名但不同id表
TEST_F(DBinterface2, V1Com_023_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    uint16_t testRelId2 = 2;
    const char testTblName[DB_NAME_LEN] = "testTbl1";
    // 源数据库表
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, testTblName);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 目标数据库表1
    DB_REL_DEF_STRU testRelDef1 = {0};
    TestCreateTblInitRelDef(&testRelDef1, testTblName);
    ret = DB_CreateTbl(testDbId, &testRelDef1, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef1);

    ret = DB_CloneTable(g_testDbId, testRelId, testDbId, testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_REL_ALREADY_EXIST, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(testDbId, testRelId1, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 150.目的库中存在同id但不同名表
TEST_F(DBinterface2, V1Com_023_150)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    const char testTblName[DB_NAME_LEN] = "testTbl1";
    const char testTblName1[DB_NAME_LEN] = "testTbl2";
    // 源数据库表
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, testTblName);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 目标数据库表1
    DB_REL_DEF_STRU testRelDef1 = {0};
    TestCreateTblInitRelDef(&testRelDef1, testTblName1);
    ret = DB_CreateTbl(testDbId, &testRelDef1, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef1);

    ret = DB_CloneTable(g_testDbId, testRelId, testDbId, testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_REL_ALREADY_EXIST, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 151.源DBid和目的DBid相同
TEST_F(DBinterface2, V1Com_023_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    const char testTblName[DB_NAME_LEN] = "testTbl1";
    const char testTblName1[DB_NAME_LEN] = "testTbl2";
    // 源数据库表
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, testTblName);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // 目标数据库表1
    DB_REL_DEF_STRU testRelDef1 = {0};
    TestCreateTblInitRelDef(&testRelDef1, testTblName1);
    ret = DB_CreateTbl(testDbId, &testRelDef1, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef1);

    ret = DB_CloneTable(g_testDbId, testRelId, g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_OPERATION_NOTALLOWED, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 152.源数据库和目标数据库一个是持久化DB，一个是非持久化DB
TEST_F(DBinterface2, V1Com_023_152)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir testDbPath");
    system("touch testDbPath/test.txt");
    char dbDir[256] = "./testDbPath/test.txt";
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)dbDir, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    const char testTblName[DB_NAME_LEN] = "testTbl1";
    const char testTblName1[DB_NAME_LEN] = "testTbl2";

    // 源数据库表
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, testTblName);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    ret = DB_CloneTable(g_testDbId, testRelId, testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    system("rm -rf ./testDbPath");
}
// 153.持久化DB中，clone后关闭目标DB再打开，并创建同名表
TEST_F(DBinterface2, V1Com_023_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir testDbPath");
    system("touch testDbPath/test.txt");
    system("touch testDbPath/test1.txt");
    char dbDir[256] = "./testDbPath/test.txt";
    char dbDir1[256] = "./testDbPath/test1.txt";
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    const char testDbName1[DB_NAME_LEN] = "testDdl2";
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)dbDir, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t testDbId1 = 2;
    DB_INST_CONFIG_STRU testDbCfg1 = {0};
    testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName1, (VOS_UINT8 *)dbDir1, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)testDbName1, &testDbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    const char testTblName[DB_NAME_LEN] = "testTbl1";
    const char testTblName1[DB_NAME_LEN] = "testTbl2";

    // 源数据库表
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, testTblName);
    ret = DB_CreateTbl(testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    ret = DB_CloneTable(testDbId, testRelId, testDbId1, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)testDbName1, &testDbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 目标数据库表
    DB_REL_DEF_STRU testRelDef1 = {0};
    TestCreateTblInitRelDef(&testRelDef1, testTblName);
    ret = DB_CreateTbl(testDbId1, &testRelDef1, &testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef1);

    ret = DB_DropTbl(testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    system("rm -rf ./testDbPath");
}
// 154.使用DB_clone后修改目标库中的表名和表id
TEST_F(DBinterface2, V1Com_023_154)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    ret = DB_CloneTable(g_testDbId, testRelId, testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 修改表ID
    uint16_t newRelId = 3;
    const char newDbName[DB_NAME_LEN] = "newDbName";
    ret = DB_ModifyTblNameAndID(testDbId, testRelId1, (VOS_UINT8 *)newDbName, newRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(testDbId, newRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 155.使用DB_clone后对目标库进行删表关闭库，删库操作
TEST_F(DBinterface2, V1Com_023_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 1;
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testRelId = 0;
    uint16_t testRelId1 = 1;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    ret = DB_CloneTable(g_testDbId, testRelId, testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(testDbId, testRelId1, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
