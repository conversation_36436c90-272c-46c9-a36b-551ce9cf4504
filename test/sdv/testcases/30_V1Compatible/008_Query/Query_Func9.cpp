/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : V1支持数据查询
 Notes        : 功能测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2024/08/10
**************************************************************************** */
#include "QueryTest1.h"

class Query_Func9 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    };
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Query_Func9::SetUp()
{
    DB_ERR_CODE ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_queryTestDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_queryTestDbName, &g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
}
void Query_Func9::TearDown()
{
    DB_ERR_CODE ret;
    ret = TPC_CloseDB(g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_DropDB((VOS_UINT8 *)g_queryTestDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_CHECK_LOG_END();
}

typedef struct tagpthreadArgT {
    VOS_UINT16 usRelNo;
    VOS_UINT32 ulNCols;
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 recordCnt;
} pthreadArgT;

void *pthreadSelectAllRec(void *arg)
{
    DB_ERR_CODE ret;
    VOS_UINT16 pusRelId = ((pthreadArgT *)arg)->usRelNo;
    VOS_UINT32 ulNCols = ((pthreadArgT *)arg)->ulNCols;
    DB_FIELD_DEF_STRU *pstFldLst = ((pthreadArgT *)arg)->pstFldLst;
    VOS_INT32 recordCnt = ((pthreadArgT *)arg)->recordCnt;

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    char opStrVal[DB_ELELEN_MAX] = { 0 };
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 6;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 8;
    pstCond.aCond[3].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[3].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[4].ucFieldId = 9;
    pstCond.aCond[4].enOp = DB_OP_MIN_PREFIX12;
    memset_s((char *)pstCond.aCond[4].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[4].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[5].ucFieldId = 9;
    pstCond.aCond[5].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[5].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str%%_");
    strncpy((char *)pstCond.aCond[5].aucValue, opStrVal, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufGet.usRecNum = recordCnt;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.usRecLen * pstDsBufGet.usRecNum, pstDsBufGet.StdBuf.ulActLen);
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pucDataGet, &tblFieldDataDefGet, pstDsBufGet.usRecLen);
    if (opVal % 2 == 0) {
        ret = testCheckAllField(&tblFieldDataDefGet, opVal, opVal);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    } else {
        ret = testCheckAllField(&tblFieldDataDefGet, opVal - 1, opVal - 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataGet);

    pthread_exit(NULL);
}

void *pthreadSelectAllReEx(void *arg)
{
    DB_ERR_CODE ret;
    VOS_UINT16 pusRelId = ((pthreadArgT *)arg)->usRelNo;
    VOS_UINT32 ulNCols = ((pthreadArgT *)arg)->ulNCols;
    DB_FIELD_DEF_STRU *pstFldLst = ((pthreadArgT *)arg)->pstFldLst;
    VOS_INT32 recordCnt = ((pthreadArgT *)arg)->recordCnt;

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    char opStrVal[DB_ELELEN_MAX] = { 0 };
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal + 10;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 8;
    pstCond.aCond[3].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[3].aucValue, opStrVal, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_EXPECT_EQ_INT(5, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = 0; j < pstBufDataGet.ulRecNum; j++) {
            if (opVal % 2 == 0) {
                ret = testCheckAllField(&tblFieldDataDefGet, opVal + 10 + (j * 2), opVal + 10 + (j * 2));
            } else {
                ret = testCheckAllField(&tblFieldDataDefGet, opVal + 1 + 10 + (j * 2), opVal + 1 + 10 + (j * 2));
            }
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j] = true;
            break;
        }
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_EXPECT_EQ_BOOL(true, isMatch[i]);
    }
    TEST_V1_FREE(pBufGet);

    pthread_exit(NULL);
}

void *pthreadSelectAllRecByOrder(void *arg)
{
    DB_ERR_CODE ret;
    VOS_UINT16 pusRelId = ((pthreadArgT *)arg)->usRelNo;
    VOS_UINT32 ulNCols = ((pthreadArgT *)arg)->ulNCols;
    DB_FIELD_DEF_STRU *pstFldLst = ((pthreadArgT *)arg)->pstFldLst;
    VOS_UINT32 recordCnt = ((pthreadArgT *)arg)->recordCnt;

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    char opStrVal[DB_ELELEN_MAX] = { 0 };
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 6;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 11;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 8;
    pstCond.aCond[3].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[3].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[4].ucFieldId = 9;
    pstCond.aCond[4].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[4].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[4].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[5].ucFieldId = 9;
    pstCond.aCond[5].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[5].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str%%_");
    strncpy((char *)pstCond.aCond[5].aucValue, opStrVal, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufGet.usRecNum = recordCnt;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = ulNCols;
    pstSort.enSortType = 0;
    T_FIELD pSortFields[ulNCols] = { 0 };
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        pSortFields[i] = i;
    }
    pstSort.pSortFields = pSortFields;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond,
        &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_EXPECT_EQ_INT(6, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.usRecLen * pstDsBufGet.usRecNum, pstDsBufGet.StdBuf.ulActLen);

    for (VOS_UINT32 i = 0; i < pstDsBufGet.usRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pucDataGet + (pstDsBufGet.usRecLen * i), &tblFieldDataDefGet, pstDsBufGet.usRecLen);
        if ((opVal - 11) % 2 == 0) {
            ret = testCheckAllField(&tblFieldDataDefGet, opVal - 11 + (i * 2), opVal - 11 + (i * 2));
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            ret = testCheckAllField(&tblFieldDataDefGet, opVal - 11 + 1 + (i * 2), opVal + 1 - 11 + (i * 2));
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }
    }
    TEST_V1_FREE(pucDataGet);

    pthread_exit(NULL);
}

void *pthreadSelectAllRecByOrderEx(void *arg)
{
    DB_ERR_CODE ret;
    VOS_UINT16 pusRelId = ((pthreadArgT *)arg)->usRelNo;
    VOS_UINT32 ulNCols = ((pthreadArgT *)arg)->ulNCols;
    DB_FIELD_DEF_STRU *pstFldLst = ((pthreadArgT *)arg)->pstFldLst;
    VOS_UINT32 recordCnt = ((pthreadArgT *)arg)->recordCnt;

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    char opStrVal[DB_ELELEN_MAX] = { 0 };
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 6;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 11;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 8;
    pstCond.aCond[3].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[3].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[4].ucFieldId = 9;
    pstCond.aCond[4].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[4].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[4].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[5].ucFieldId = 9;
    pstCond.aCond[5].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[5].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str%%_");
    strncpy((char *)pstCond.aCond[5].aucValue, opStrVal, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;

    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = ulNCols;
    pstSort.enSortType = 0;
    T_FIELD pSortFields[ulNCols] = { 0 };
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        pSortFields[i] = i;
    }
    pstSort.pSortFields = pSortFields;
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond,
        &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_EXPECT_EQ_INT(6, pstBufDataGet.ulRecNum);

    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        if ((opVal - 11) % 2 == 0) {
            ret = testCheckAllField(&tblFieldDataDefGet, opVal - 11 + (i * 2), opVal - 11 + (i * 2));
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            ret = testCheckAllField(&tblFieldDataDefGet, opVal - 11 + 1 + (i * 2), opVal + 1 - 11 + (i * 2));
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }
    }
    TEST_V1_FREE(pBufGet);

    pthread_exit(NULL);
}

void *pthreadSelectFirstRec(void *arg)
{
    DB_ERR_CODE ret;
    VOS_UINT16 pusRelId = ((pthreadArgT *)arg)->usRelNo;
    VOS_UINT32 ulNCols = ((pthreadArgT *)arg)->ulNCols;
    DB_FIELD_DEF_STRU *pstFldLst = ((pthreadArgT *)arg)->pstFldLst;
    VOS_INT32 recordCnt = ((pthreadArgT *)arg)->recordCnt;

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal1 = rand() % recordCnt;
    VOS_INT32 opVal2 = opVal1 + 10;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal1;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal2;
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufGet.usRecNum = recordCnt;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_EXPECT_PTR_NULL(pucDataGet);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    VOS_UINT32 runCnt = 100;
    for (VOS_UINT32 i = 0; i < runCnt; i++) {
        (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.usRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.usRecLen * pstDsBufGet.usRecNum, pstDsBufGet.StdBuf.ulActLen);
        // 获取所有记录的数据
        TblFieldDataDefT tblFieldDataDefGet = {0};
        VOS_UINT32 queryVal;
        if (i == 0) {
            testGetAllField(pucDataGet, &tblFieldDataDefGet, pstDsBufGet.usRecLen);
            queryVal = tblFieldDataDefGet.f0;
        } else {
            testGetAllField(pucDataGet, &tblFieldDataDefGet, pstDsBufGet.usRecLen);
            if (queryVal % 2 == 0) {
                ret = testCheckAllField(&tblFieldDataDefGet, queryVal, queryVal, "str");
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
            } else {
                ret = testCheckAllField(&tblFieldDataDefGet, queryVal, queryVal, "mystr");
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
            }
        }
    }
    TEST_V1_FREE(pucDataGet);

    pthread_exit(NULL);
}

void *pthreadCountMatchingRecs(void *arg)
{
    DB_ERR_CODE ret;
    VOS_UINT16 pusRelId = ((pthreadArgT *)arg)->usRelNo;
    VOS_UINT32 ulNCols = ((pthreadArgT *)arg)->ulNCols;
    DB_FIELD_DEF_STRU *pstFldLst = ((pthreadArgT *)arg)->pstFldLst;
    VOS_INT32 recordCnt = ((pthreadArgT *)arg)->recordCnt;

    // 获取匹配的数据量
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal1 = rand() % 40;
    VOS_INT32 opVal2 = rand() % (recordCnt - 60) + 50;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal1;
    pstCond.aCond[1].ucFieldId = 1;
    pstCond.aCond[1].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[1].aucValue = opVal2;
    testPrintCond(&pstCond, pstFldLst);

    VOS_UINT32 pulRecNum;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

    pthread_exit(NULL);
}

void *pthreadRecordExist(void *arg)
{
    DB_ERR_CODE ret;
    VOS_UINT16 pusRelId = ((pthreadArgT *)arg)->usRelNo;
    VOS_UINT32 ulNCols = ((pthreadArgT *)arg)->ulNCols;
    DB_FIELD_DEF_STRU *pstFldLst = ((pthreadArgT *)arg)->pstFldLst;
    VOS_INT32 recordCnt = ((pthreadArgT *)arg)->recordCnt;

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    char opStrVal[DB_ELELEN_MAX] = { 0 };
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 6;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 8;
    pstCond.aCond[3].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[3].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[4].ucFieldId = 9;
    pstCond.aCond[4].enOp = DB_OP_MIN_PREFIX12;
    memset_s((char *)pstCond.aCond[4].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[4].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[5].ucFieldId = 9;
    pstCond.aCond[5].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[5].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str%%_");
    strncpy((char *)pstCond.aCond[5].aucValue, opStrVal, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件，使其过滤不到数据
    memset_s((char *)pstCond.aCond[5].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str%%_haha");
    strncpy((char *)pstCond.aCond[5].aucValue, opStrVal, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));

    pthread_exit(NULL);
}

// 覆盖查询接口，每个接口3个线程
TEST_F(Query_Func9, V1Com_008_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    VOS_UINT32 ulNCols = 10;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN];
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        sprintf_s(aucFieldNameLst[i], DB_FLD_NAME_LEN, "F%u", i);
    }
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_UINT32, DBT_UINT32, DBT_SINT32, DBT_FLOAT, DBT_DOUBLE,
        DBT_UINT16, DBT_SINT16, DBT_UINT8, DBT_STRING, DBT_STRING };
    VOS_UINT16 usSizeLst[ulNCols];
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        usSizeLst[i] = (DB_DATATYPE_ENUM_V1)testSetOneFieldLen(enDataTypeLst[i]);
    }
    DB_FIELD_DEF_STRU *pstFldLst = testSetFldInfo(ulNCols, aucFieldNameLst, enDataTypeLst, usSizeLst);
    TEST_ASSERT_PTR_NULL(pstFldLst);

    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1", "idx2" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1, 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1, 3 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    DB_INDEX_DEF_STRU *pstIdxLst = testSetIdxInfo(ulNIdxs, aucIndexNameLst, enIndexTypeLst, ucUniqueFlagLst,
        ucIdxFldNum, aucFieldIDLst);
    TEST_ASSERT_PTR_NULL(pstFldLst);

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 0;
    pstRelDef.ulMaxSize = 100000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = ulNIdxs;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    testPrintTblInfo(&pstRelDef);

    // 建表
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 2 == 0) {
            testSetAllField(&TblFieldDataDefSet, i, i, "str");
        } else {
            testSetAllField(&TblFieldDataDefSet, i, i, "mystr");
        }
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 创建线程查询数据，7个接口
    VOS_UINT32 pthreadApiCnt = 3;
    pthread_t pthId[7][pthreadApiCnt];
    pthreadArgT pthreadArg[7][pthreadApiCnt];
    for (VOS_UINT32 i = 0; i < 7; i++) {
        for (VOS_UINT32 j = 0; j < pthreadApiCnt; j++) {
            pthreadArg[i][j].usRelNo = pusRelId;
            pthreadArg[i][j].ulNCols = ulNCols;
            pthreadArg[i][j].pstFldLst = pstFldLst;
            pthreadArg[i][j].recordCnt = recordCnt;
        }
    }
    for (VOS_UINT32 i = 0; i < pthreadApiCnt; i++) {
        ret = pthread_create(&pthId[0][i], NULL, pthreadSelectAllRec, &pthreadArg[0][i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_create(&pthId[1][i], NULL, pthreadSelectAllReEx, &pthreadArg[1][i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_create(&pthId[2][i], NULL, pthreadSelectAllRecByOrder, &pthreadArg[1][i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_create(&pthId[3][i], NULL, pthreadSelectAllRecByOrderEx, &pthreadArg[1][i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_create(&pthId[4][i], NULL, pthreadSelectFirstRec, &pthreadArg[1][i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_create(&pthId[5][i], NULL, pthreadCountMatchingRecs, &pthreadArg[1][i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_create(&pthId[6][i], NULL, pthreadRecordExist, &pthreadArg[1][i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (VOS_UINT32 i = 0; i < pthreadApiCnt; i++) {
        ret = pthread_join(pthId[0][i], NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_join(pthId[1][i], NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_join(pthId[2][i], NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_join(pthId[3][i], NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_join(pthId[4][i], NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_join(pthId[5][i], NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = pthread_join(pthId[6][i], NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);
    testFreeIdxInfo(pstIdxLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRec接口测试，DB_OP_NOTEQUAL(不涉及索引), 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Query_Func9, V1Com_008_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    VOS_UINT32 ulNCols = 10;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN];
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        sprintf_s(aucFieldNameLst[i], DB_FLD_NAME_LEN, "F%u", i);
    }
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_UINT32, DBT_UINT32, DBT_SINT32, DBT_FLOAT, DBT_DOUBLE,
        DBT_UINT16, DBT_SINT16, DBT_UINT8, DBT_STRING, DBT_STRING };
    VOS_UINT16 usSizeLst[ulNCols];
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        usSizeLst[i] = (DB_DATATYPE_ENUM_V1)testSetOneFieldLen(enDataTypeLst[i]);
    }
    DB_FIELD_DEF_STRU *pstFldLst = testSetFldInfo(ulNCols, aucFieldNameLst, enDataTypeLst, usSizeLst);
    TEST_ASSERT_PTR_NULL(pstFldLst);

    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1", "idx2" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1, 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1, 3 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    DB_INDEX_DEF_STRU *pstIdxLst = testSetIdxInfo(ulNIdxs, aucIndexNameLst, enIndexTypeLst, ucUniqueFlagLst,
        ucIdxFldNum, aucFieldIDLst);
    TEST_ASSERT_PTR_NULL(pstFldLst);

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 0;
    pstRelDef.ulMaxSize = 100000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = ulNIdxs;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    testPrintTblInfo(&pstRelDef);

    // 建表
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufGet.usRecNum = recordCnt;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * (pstDsBufGet.usRecNum + 1);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - 1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(pstDsBufGet.usRecLen * pstDsBufGet.usRecNum, pstDsBufGet.StdBuf.ulActLen);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBufGet.usRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pucDataGet + (pstDsBufGet.usRecLen * i), &tblFieldDataDefGet, pstDsBufGet.usRecLen);
        if (i < opVal) {
            ret = testCheckAllField(&tblFieldDataDefGet, i, i);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            ret = testCheckAllField(&tblFieldDataDefGet, i + 1, i + 1);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }

    }
    TEST_V1_FREE(pucDataGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);
    testFreeIdxInfo(pstIdxLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRec接口测试，DB_OP_NOTEQUAL(不涉及索引), 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Query_Func9, V1Com_008_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    VOS_UINT32 ulNCols = 10;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN];
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        sprintf_s(aucFieldNameLst[i], DB_FLD_NAME_LEN, "F%u", i);
    }
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_UINT32, DBT_UINT32, DBT_SINT32, DBT_FLOAT, DBT_DOUBLE,
        DBT_UINT16, DBT_SINT16, DBT_UINT8, DBT_STRING, DBT_STRING };
    VOS_UINT16 usSizeLst[ulNCols];
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        usSizeLst[i] = (DB_DATATYPE_ENUM_V1)testSetOneFieldLen(enDataTypeLst[i]);
    }
    DB_FIELD_DEF_STRU *pstFldLst = testSetFldInfo(ulNCols, aucFieldNameLst, enDataTypeLst, usSizeLst);
    TEST_ASSERT_PTR_NULL(pstFldLst);

    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1", "idx2" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1, 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1, 3 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    DB_INDEX_DEF_STRU *pstIdxLst = testSetIdxInfo(ulNIdxs, aucIndexNameLst, enIndexTypeLst, ucUniqueFlagLst,
        ucIdxFldNum, aucFieldIDLst);
    TEST_ASSERT_PTR_NULL(pstFldLst);

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 0;
    pstRelDef.ulMaxSize = 100000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = ulNIdxs;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    testPrintTblInfo(&pstRelDef);

    // 建表
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % recordCnt;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufGet.usRecNum = recordCnt;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * (pstDsBufGet.usRecNum + 1);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - 1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(pstDsBufGet.usRecLen * pstDsBufGet.usRecNum, pstDsBufGet.StdBuf.ulActLen);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBufGet.usRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pucDataGet + (pstDsBufGet.usRecLen * i), &tblFieldDataDefGet, pstDsBufGet.usRecLen);
        if (i < opVal) {
            ret = testCheckAllField(&tblFieldDataDefGet, i, i);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            ret = testCheckAllField(&tblFieldDataDefGet, i + 1, i + 1);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }

    }
    TEST_V1_FREE(pucDataGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);
    testFreeIdxInfo(pstIdxLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRec接口测试，DB_OP_NOTEQUAL(不涉及索引), 多个过滤条件，没有索引字段
TEST_F(Query_Func9, V1Com_008_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    VOS_UINT32 ulNCols = 10;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN];
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        sprintf_s(aucFieldNameLst[i], DB_FLD_NAME_LEN, "F%u", i);
    }
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_UINT32, DBT_UINT32, DBT_SINT32, DBT_FLOAT, DBT_DOUBLE,
        DBT_UINT16, DBT_SINT16, DBT_UINT8, DBT_STRING, DBT_STRING };
    VOS_UINT16 usSizeLst[ulNCols];
    for (VOS_UINT32 i = 0; i < ulNCols; i++) {
        usSizeLst[i] = (DB_DATATYPE_ENUM_V1)testSetOneFieldLen(enDataTypeLst[i]);
    }
    DB_FIELD_DEF_STRU *pstFldLst = testSetFldInfo(ulNCols, aucFieldNameLst, enDataTypeLst, usSizeLst);
    TEST_ASSERT_PTR_NULL(pstFldLst);

    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1", "idx2" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1, 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1, 3 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    DB_INDEX_DEF_STRU *pstIdxLst = testSetIdxInfo(ulNIdxs, aucIndexNameLst, enIndexTypeLst, ucUniqueFlagLst,
        ucIdxFldNum, aucFieldIDLst);
    TEST_ASSERT_PTR_NULL(pstFldLst);

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 0;
    pstRelDef.ulMaxSize = 100000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = ulNIdxs;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    testPrintTblInfo(&pstRelDef);

    // 建表
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % recordCnt;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufGet.usRecNum = recordCnt;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * (pstDsBufGet.usRecNum + 1);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - 1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(pstDsBufGet.usRecLen * pstDsBufGet.usRecNum, pstDsBufGet.StdBuf.ulActLen);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBufGet.usRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pucDataGet + (pstDsBufGet.usRecLen * i), &tblFieldDataDefGet, pstDsBufGet.usRecLen);
        if (i < opVal) {
            ret = testCheckAllField(&tblFieldDataDefGet, i, i);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            ret = testCheckAllField(&tblFieldDataDefGet, i + 1, i + 1);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }

    }
    TEST_V1_FREE(pucDataGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);
    testFreeIdxInfo(pstIdxLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
