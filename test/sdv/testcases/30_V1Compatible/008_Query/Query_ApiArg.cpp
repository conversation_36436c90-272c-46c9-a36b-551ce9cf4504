/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : V1支持数据查询
 Notes        : 接口参数测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2024/08/10
**************************************************************************** */
#include "QueryTest.h"

#define EPSILON_FLOAT 1e-6
#define EPSILON_DOUBLE 1e-15 

int32_t DmCompareFloat(float input1, float input2)
{
    float diffValue = input1 - input2;
    if (diffValue > EPSILON_FLOAT) {
        return 1;
    } else if (diffValue < -EPSILON_FLOAT) {
        return -1;
    }
    return 0;
}

int32_t DmCompareDouble(double input1, double input2)
{
    double diffValue = input1 - input2;
    if (diffValue > EPSILON_DOUBLE) {
        return 1;
    } else if (diffValue < -EPSILON_DOUBLE) {
        return -1;
    }
    return 0;
} 

DB_ERR_CODE V1ErrCode(DB_ERR_CODE V1ErrCodeDec)
{
    if (V1ErrCodeDec != DB_SUCCESS_V1) {
        printf("V1ErrCodeHex: %#x\n", V1ErrCodeDec);
    }
    return V1ErrCodeDec;
}

class Query_ApiArg : public testing::Test {
protected:
    static void SetUpTestCase() {};
    static void TearDownTestCase() {};

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Query_ApiArg::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    DB_ERR_CODE ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=10000\"");
    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = TPC_CreateDB((VOS_UINT8 *)g_queryTestDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_queryTestDbName, &g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}
void Query_ApiArg::TearDown()
{
    DB_ERR_CODE ret;
    ret = TPC_CloseDB(g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_DropDB((VOS_UINT8 *)g_queryTestDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
}

static uint16_t DmlTestGetRecLen(DB_REL_DEF_STRU *stRelDef)
{
    uint32_t recordLen = 0;
    DB_FIELD_DEF_STRU *pstFld = stRelDef->pstFldLst;
    for (uint32_t i = 0; i < stRelDef->ulNCols; i++, pstFld++) {
        if (pstFld->enDataType == DBT_STRING || pstFld->enDataType == DBT_MIBSTR) {
            ++pstFld->usSize;
        }
        recordLen += pstFld->usSize;
    }
    return recordLen;
}

// TPC_SelectAllRec接口测试
TEST_F(Query_ApiArg, V1Com_008_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 1;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 不存在的会话id
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB + 1, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    TestSetGlobalLastErrInfo("CDB 0 is not in use.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 不存在的数据库id
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId + 1, testRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (1) not exists.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 不存在的表id
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId + 1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (1) does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 指针为NULL
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, NULL, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstCond is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, NULL, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstFldFilter is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstDsBuf is null pointer for select all record.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试
TEST_F(Query_ApiArg, V1Com_008_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_BUF_STRU pstBufGet;
    pstBufGet.usRecLen = recLen;
    pstBufGet.ulRecNum = 1;
    pstBufGet.ulBufLen = recLen;
    pstBufGet.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufGet.usRecLen);
    (void)memset_s(pstBufGet.pBuf, pstBufGet.usRecLen, 0x00, pstBufGet.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 不存在的会话id
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB + 1, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("CDB 0 is not in use.", false));
    // 不存在的数据库id
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId + 1, testRelId, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (1) not exists.", false));
    // 不存在的表id
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId + 1, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (1) does not exist.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    // 指针为NULL
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, NULL, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstCond is null pointer.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, NULL, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstFldFilter is null pointer.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstBufData is null pointer for select all record by order.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));

    TEST_V1_FREE(pstBufGet.pBuf);
    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 1;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 不存在的会话id
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB + 1, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("CDB 0 is not in use.", false));
    // 不存在的数据库id
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId + 1, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (1) not exists.", false));
    // 不存在的表id
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId + 1, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (1) does not exist.", false));
    // 指针为NULL
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, NULL, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("pstSort is null pointer."));
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, NULL, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("pstCond is null pointer."));
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, NULL, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("pstFldFilter is null pointer."));
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstDsBuf is null pointer for select all record by order.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrderEx接口测试
TEST_F(Query_ApiArg, V1Com_008_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    T_FIELD sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_BUF_STRU pstBufGet;
    pstBufGet.usRecLen = recLen;
    pstBufGet.ulRecNum = 1;
    pstBufGet.ulBufLen = recLen;
    pstBufGet.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufGet.usRecLen);
    (void)memset_s(pstBufGet.pBuf, pstBufGet.usRecLen, 0x00, pstBufGet.usRecLen);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 不存在的会话id
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB + 1, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    TestSetGlobalLastErrInfo("CDB 0 is not in use.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 不存在的数据库id
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId + 1, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (1) not exists.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 不存在的表id
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId + 1, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (1) does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 指针为NULL
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, NULL, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstSort is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, NULL, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstCond is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, NULL, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstFldFilter is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstBufData is null pointer for select all record by order.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(pstBufGet.pBuf);
    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectFirstRec接口测试
TEST_F(Query_ApiArg, V1Com_008_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 1;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 不存在的会话id
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB + 1, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    TestSetGlobalLastErrInfo("CDB 0 is not in use.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 不存在的数据库id
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_queryTestDbId + 1, testRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (1) not exists.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 不存在的表id
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId + 1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (1) does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 指针为NULL
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, NULL, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstCond is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, NULL, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstFldFilter is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, &pstFldFilter, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstDsBuf is null for select first record.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_CountMatchingRecs接口测试
TEST_F(Query_ApiArg, V1Com_008_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    VOS_UINT32 pulRecNum = 0;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 不存在的会话id
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB + 1, g_queryTestDbId, testRelId, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("CDB 0 is not in use.", false));
    // 不存在的数据库id
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_queryTestDbId + 1, testRelId, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (1) not exists.", false));
    // 不存在的表id
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId + 1, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (1) does not exist.", false));
    // 指针为NULL
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, NULL, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("pstCond is null for count matched records."));
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("pulRecNum is null for count matched records."));

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_RecordExist接口测试
TEST_F(Query_ApiArg, V1Com_008_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 不存在的会话id
    ret = TPC_RecordExist(TPC_GLOBAL_CDB + 1, g_queryTestDbId, testRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    TestSetGlobalLastErrInfo("CDB 0 is not in use.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 不存在的数据库id
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId + 1, testRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (1) not exists.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 不存在的表id
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId + 1, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (1) does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 指针为NULL
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstCond is null for check record exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SetDBDesc接口测试
TEST_F(Query_ApiArg, V1Com_008_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    char pDBDescrInfo[128] = "test db!";
    ret = TPC_SetDBDesc((unsigned char *)g_queryTestDbName, pDBDescrInfo, strlen(pDBDescrInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库名称
    unsigned char NoExistDbName[DB_NAME_LEN] = "noExistDb";
    ret = TPC_SetDBDesc(NoExistDbName, pDBDescrInfo, strlen(pDBDescrInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    TestSetGlobalLastErrInfo("Database with the given name (noExistDb) does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 传空的数据库名称
    unsigned char NullDbName[DB_NAME_LEN] = "";
    ret = TPC_SetDBDesc(NullDbName, pDBDescrInfo, strlen(pDBDescrInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    TestSetGlobalLastErrInfo("DB name len is 0.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 传空的描述信息
    char NullDbDescrInfo[8] = "";
    ret = TPC_SetDBDesc((unsigned char *)g_queryTestDbName, NullDbDescrInfo, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    TestSetGlobalLastErrInfo("Description length is 0.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetDBDesc接口测试
TEST_F(Query_ApiArg, V1Com_008_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    char pDBDescrInfo[128] = "test db!";
    ret = TPC_SetDBDesc((unsigned char *)g_queryTestDbName, pDBDescrInfo, strlen(pDBDescrInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t DbDescrInfoLen = 1024;
    char pGetDBDescrInfo[DbDescrInfoLen] = {0};
    ret = TPC_GetDBDesc((unsigned char *)g_queryTestDbName, NULL, pGetDBDescrInfo, &DbDescrInfoLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库名称
    unsigned char NoExistDbName[DB_NAME_LEN] = "noExistDb";
    ret = TPC_GetDBDesc(NoExistDbName, NULL, pGetDBDescrInfo, &DbDescrInfoLen);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, V1ErrCode(ret));
    TestSetGlobalLastErrInfo("Parameter db Dir is NULL pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 传空的数据库名称
    unsigned char NullDbName[DB_NAME_LEN] = "";
    ret = TPC_GetDBDesc(NullDbName, NULL, pGetDBDescrInfo, &DbDescrInfoLen);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, V1ErrCode(ret));
    TestSetGlobalLastErrInfo("Parameter db Dir is NULL pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetDBId接口测试
TEST_F(Query_ApiArg, V1Com_008_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t pulDbId = 0;
    ret = TPC_GetDBId((unsigned char *)g_queryTestDbName, &pulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库名称
    unsigned char NoExistDbName[DB_NAME_LEN] = "noExistDb";
    ret = TPC_GetDBId(NoExistDbName, &pulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    TestSetGlobalLastErrInfo("Database with the given name (noExistDb) does not exist.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    // 传空的数据库名称
    unsigned char NullDbName[DB_NAME_LEN] = "";
    ret = TPC_GetDBId(NoExistDbName, &pulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    TestSetGlobalLastErrInfo("Database with the given name (noExistDb) does not exist.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetDBName接口测试
TEST_F(Query_ApiArg, V1Com_008_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    unsigned char pucDbName[128] = {0};
    ret = TPC_GetDBName(g_queryTestDbId, pucDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t noExistDbId = 100;
    ret = TPC_GetDBName(noExistDbId, pucDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (100) not exists.", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetTblCount接口测试
TEST_F(Query_ApiArg, V1Com_008_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t pulRelNum = 0;
    ret = TPC_GetTblCount(g_queryTestDbId, &pulRelNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetTblCount(NoExistDbId, &pulRelNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (100) not exists.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetTblId接口测试
TEST_F(Query_ApiArg, V1Com_008_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);

    unsigned char pucRelName[] = "table";
    uint16_t pusRelId = 10;
    ret = TPC_GetTblId(g_queryTestDbId, pucRelName, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetTblId(NoExistDbId, pucRelName, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (100) not exists.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    // 传不存在的表名
    unsigned char NoExistTblName[] = "table0";
    ret = TPC_GetTblId(g_queryTestDbId, NoExistTblName, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with the given name (table0) does not exist.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    // 传空的表名
    unsigned char NullTblName[] = "";
    ret = TPC_GetTblId(g_queryTestDbId, NullTblName, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with the given name () does not exist.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetTblColInfo接口测试
TEST_F(Query_ApiArg, V1Com_008_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);

    DB_FIELD_INFO pstAllFieldInfo[5];
    ret = TPC_GetTblColInfo(g_queryTestDbId, testRelId, pstAllFieldInfo, 5 * sizeof(DB_FIELD_INFO));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetTblColInfo(NoExistDbId, testRelId, pstAllFieldInfo, 5 * sizeof(DB_FIELD_INFO));
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (100) not exists.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 传不存在的表id
    uint32_t NoExistTblId = 100;
    ret = TPC_GetTblColInfo(g_queryTestDbId, NoExistTblId, pstAllFieldInfo, 5 * sizeof(DB_FIELD_INFO));
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (100) does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 指针传NULL
    ret = TPC_GetTblColInfo(g_queryTestDbId, testRelId, NULL, 5 * sizeof(DB_FIELD_INFO));
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("Para pstAllFieldInfo is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetTblIdxInfo接口测试
TEST_F(Query_ApiArg, V1Com_008_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);

    DB_INDEX_INFO pstAllIndexInfo;
    ret = TPC_GetTblIdxInfo(g_queryTestDbId, testRelId, &pstAllIndexInfo, 5 * sizeof(DB_INDEX_INFO));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetTblIdxInfo(NoExistDbId, testRelId, &pstAllIndexInfo, 5 * sizeof(DB_INDEX_INFO));
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (100) not exists.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 传不存在的表id
    uint32_t NoExistTblId = 100;
    ret = TPC_GetTblIdxInfo(g_queryTestDbId, NoExistTblId, &pstAllIndexInfo, 5 * sizeof(DB_INDEX_INFO));
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (100) does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 指针传NULL
    ret = TPC_GetTblIdxInfo(g_queryTestDbId, testRelId, NULL, 5 * sizeof(DB_INDEX_INFO));
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("pstAllIndexInfo is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetRelActRec接口测试
TEST_F(Query_ApiArg, V1Com_008_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);

    uint32_t pulActRec = 0;
    ret = TPC_GetRelActRec(g_queryTestDbId, testRelId, &pulActRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetRelActRec(NoExistDbId, testRelId, &pulActRec);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (100) not exists.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    // 传不存在的表id
    uint32_t NoExistTblId = 100;
    ret = TPC_GetRelActRec(g_queryTestDbId, NoExistTblId, &pulActRec);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (100) does not exist.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    // 指针传NULL
    ret = TPC_GetRelActRec(g_queryTestDbId, testRelId, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("Parameter pulActRec is null pointer.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetTblInfo接口测试
TEST_F(Query_ApiArg, V1Com_008_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);

    DB_RELATION_INFO pstRelationInfo;
    ret = TPC_GetTblInfo(g_queryTestDbId, testRelId, &pstRelationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetTblInfo(NoExistDbId, testRelId, &pstRelationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (100) not exists.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 传不存在的表id
    uint32_t NoExistTblId = 100;
    ret = TPC_GetTblInfo(g_queryTestDbId, NoExistTblId, &pstRelationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    TestSetGlobalLastErrInfo("Relation with id (100) does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 指针传NULL
    ret = TPC_GetTblInfo(g_queryTestDbId, testRelId, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    TestSetGlobalLastErrInfo("Para pstRelationInfo is null pointer.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetTblNamesAndCount接口测试
TEST_F(Query_ApiArg, V1Com_008_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);

    uint32_t pulRelNum = 0;
    ret = TPC_GetTblCount(g_queryTestDbId, &pulRelNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t pulTblCount = pulRelNum;
    VOS_UINT8 *ppucRelation[pulTblCount];
    for (int i = 0; i < pulTblCount; ++i) {
        ppucRelation[i] = (VOS_UINT8 *)TEST_V1_MALLOC(16);
        memset_s(ppucRelation[i], 16, 0, 16);
    }
    ret = TPC_GetTblNamesAndCount(g_queryTestDbId, &pulTblCount, ppucRelation);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetTblNamesAndCount(NoExistDbId, &pulTblCount, ppucRelation);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id (100) not exists.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 小于数据库中实际的表数量
    pulTblCount = pulRelNum - 1;
    ret = TPC_GetTblNamesAndCount(g_queryTestDbId, &pulTblCount, ppucRelation);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    TestSetGlobalLastErrInfo("Input table count 0 is less than actual number 1 of tables in DB(QueryTestDd).");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 指针传NULL(不报错，只获取表数量)
    pulTblCount = pulRelNum;
    ret = TPC_GetTblNamesAndCount(g_queryTestDbId, &pulTblCount, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(ppucRelation[0]);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetOpenHandleInfo接口测试
TEST_F(Query_ApiArg, V1Com_008_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeRelDef(&testRelDef);

    // handle数
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_SELHANDLE phSelect;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, & pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstCond, & pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询handle信息
    DB_HANDLECTRL_INFO_STRU pstHandleCtrlInfo = {.ulHndlCnt = 0};
    ret = TPC_GetOpenHandleInfo(g_queryTestDbId, &pstHandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstHandleCtrlInfo.ulHndlCnt);
    pstHandleCtrlInfo.ulHndlCnt = 2;
    pstHandleCtrlInfo.pstHndlInfo = (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(pstHandleCtrlInfo.ulHndlCnt * sizeof(DB_HANDLEINFO_STRU));
    ret = TPC_GetOpenHandleInfo(g_queryTestDbId, &pstHandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetOpenHandleInfo(NoExistDbId, &pstHandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestSetGlobalLastErrInfo("DB with id 100 does not exist.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // handle不为0且小于实际数量
    pstHandleCtrlInfo.ulHndlCnt = 1;
    ret = TPC_GetOpenHandleInfo(g_queryTestDbId, &pstHandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, V1ErrCode(ret));
    TestSetGlobalLastErrInfo("Input ulHndlCnt 1 is less than actual open handle count 2, DB Id is 0.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(g_queryTestDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(pstHandleCtrlInfo.pstHndlInfo);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_GetActiveCDBNum接口测试
TEST_F(Query_ApiArg, V1Com_008_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    uint32_t pulActiveCDBNum = 0;
    ret = TPC_GetActiveCDBNum(g_queryTestDbId, &pulActiveCDBNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 传不存在的数据库id
    uint32_t NoExistDbId = 100;
    ret = TPC_GetActiveCDBNum(NoExistDbId, &pulActiveCDBNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, V1ErrCode(ret));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id 100 does not exist.", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    // unSortNum超过表中的字段数量
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 6;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 1;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDSORTER, ret);
    TestSetGlobalLastErrInfo("Sort number(6) of sort exceed the number of relation fldNum(5).");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    // enSortType非法
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 2;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 1;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDSORTER, ret);
    TestSetGlobalLastErrInfo("sort type(2) is incorrect.");
    ret = TestTPC_SysviewGetLastError(g_lastErrInfo, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    // pSortFields设置非法字段id
    unsigned char sortFields[6] = {0, 1, 2, 3, 4, 5};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 1;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);

    // 建表
    ret = TPC_CreateTbl(g_queryTestDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&testRelDef);
    VOS_UINT32 pucDataSet[5] = { 0, 1, 2, 3, 4 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    // pSortFields指针为NULL
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = NULL;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 0;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 1;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, testRelId, &pstSort, &pstCond, &pstFldFilter,
        &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDSORTER, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Sort fields is null pointer.", false));

    TestFreeRelDef(&testRelDef);
    ret = TPC_DropTbl(g_queryTestDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32 };
    VOS_UINT16 usSizeLst[ulNCols] = { sizeof(VOS_UINT32), sizeof(VOS_UINT32), sizeof(VOS_UINT32),
        sizeof(VOS_UINT32), sizeof(VOS_UINT32) };
    
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = sizeof(VOS_UINT32);
    }

    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1", "idx2" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1, 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 2, 3 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0, 1}, {0, 1, 2} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * 1);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * 1);
    (void)sprintf_s((char *)pstIdxLst[0].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[0]);
    pstIdxLst[0].enIndexType = enIndexTypeLst[0];
    pstIdxLst[0].ucUniqueFlag = 1;
    pstIdxLst[0].ucIdxFldNum = 1;
    pstIdxLst[0].aucFieldID[0] = 0;

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 100000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    VOS_UINT32 pucDataSet[5] = { 10, 8, 12, 3, 1 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 pucDataSet2[5] = { 9, 8, 22, 13, 11 };
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet2
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 8;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    V1_AW_MACRO_EXPECT_EQ_INT(20, pstDsBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(200, pstDsBufGet.StdBuf.ulBufLen);
    V1_AW_MACRO_EXPECT_EQ_INT(40, pstDsBufGet.StdBuf.ulActLen);
    V1_AW_MACRO_EXPECT_EQ_INT(9, pstDsBufGet.StdBuf.pucData[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(8, pstDsBufGet.StdBuf.pucData[4]);
    V1_AW_MACRO_EXPECT_EQ_INT(22, pstDsBufGet.StdBuf.pucData[8]);
    V1_AW_MACRO_EXPECT_EQ_INT(13, pstDsBufGet.StdBuf.pucData[12]);
    V1_AW_MACRO_EXPECT_EQ_INT(11, pstDsBufGet.StdBuf.pucData[16]);
    V1_AW_MACRO_EXPECT_EQ_INT(10, pstDsBufGet.StdBuf.pucData[20]);
    V1_AW_MACRO_EXPECT_EQ_INT(8, pstDsBufGet.StdBuf.pucData[24]);
    V1_AW_MACRO_EXPECT_EQ_INT(12, pstDsBufGet.StdBuf.pucData[28]);
    V1_AW_MACRO_EXPECT_EQ_INT(3, pstDsBufGet.StdBuf.pucData[32]);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.StdBuf.pucData[36]);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_STRING, DBT_STRING, DBT_STRING, DBT_STRING, DBT_STRING };
    uint32_t usSizeLst[ulNCols] = { 15, 15, 15, 15, 15 };
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = usSizeLst[i];
    }

    VOS_UINT32 ulNIdxs = 1;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    for (uint32_t i = 0; i < ulNIdxs; ++i) {
        (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[i]);
        pstIdxLst[i].enIndexType = enIndexTypeLst[i];
        pstIdxLst[i].ucUniqueFlag = ucUniqueFlagLst[i];
        pstIdxLst[i].ucIdxFldNum = ucIdxFldNum[i];
        for (uint32_t j = 0; j < ucIdxFldNum[i]; ++j) {
            pstIdxLst[i].aucFieldID[j] = aucFieldIDLst[i][j];
        }
    }

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 10000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    unsigned char pucDataSet[ulNCols][10] = { "xxxyyy123", "aaabbb123", "cccddd123", "mmmnnn123", "lllxxx123" };
    uint8_t *recBuf;
    recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    memset(recBuf, 0, recLen);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)memcpy_s(recBuf + 16 * i, 16, &pucDataSet[i], 16);
    }
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    unsigned char pucDataSet2[ulNCols][10] = { "xxxaaa123", "aaabbb123", "dddccc123", "nnnmmm123", "xxxlll123" };
    memset(recBuf, 0, recLen);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)memcpy_s(recBuf + 16 * i, 16, &pucDataSet2[i], 16);
    }
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    memcpy_s(pstCond.aCond[0].aucValue, 16, &pucDataSet[1], 16);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    V1_AW_MACRO_EXPECT_EQ_INT(80, pstDsBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(800, pstDsBufGet.StdBuf.ulBufLen);
    V1_AW_MACRO_EXPECT_EQ_INT(160, pstDsBufGet.StdBuf.ulActLen);
    uint32_t addr = 0;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[0], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[1], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[2], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[3], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[4], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[0], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[1], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[2], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[3], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[4], (char *)(pstDsBufGet.StdBuf.pucData + addr * 16));

    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_FLOAT, DBT_FLOAT, DBT_FLOAT, DBT_FLOAT, DBT_FLOAT };
    VOS_UINT16 usSizeLst[ulNCols] = { sizeof(DBT_FLOAT), sizeof(DBT_FLOAT), sizeof(DBT_FLOAT),
        sizeof(DBT_FLOAT), sizeof(DBT_FLOAT) };
    
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = sizeof(DBT_FLOAT);
    }

    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1", "idx2" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1, 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 2, 3 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0, 1}, {0, 1, 2} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * 1);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * 1);
    (void)sprintf_s((char *)pstIdxLst[0].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[0]);
    pstIdxLst[0].enIndexType = enIndexTypeLst[0];
    pstIdxLst[0].ucUniqueFlag = 1;
    pstIdxLst[0].ucIdxFldNum = 1;
    pstIdxLst[0].aucFieldID[0] = 0;

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 10000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    float pucDataSet[5] = { 10.1, 8.2, 12.3, 3.3, 1.1 };
    uint8_t *recBuf;
    recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    memset(recBuf, 0, recLen);
    (void)memcpy_s(recBuf, 4, &pucDataSet[0], 4);
    (void)memcpy_s(recBuf+4, 4, &pucDataSet[1], 4);
    (void)memcpy_s(recBuf+8, 4, &pucDataSet[2], 4);
    (void)memcpy_s(recBuf+12, 4, &pucDataSet[3], 4);
    (void)memcpy_s(recBuf+16, 4, &pucDataSet[4], 4);
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    float pucDataSet2[5] = { 9.1, 8.2, 22.0, 13.1, 11.1 };
    memset(recBuf, 0, recLen);
    (void)memcpy_s(recBuf, 4, &pucDataSet2[0], 4);
    (void)memcpy_s(recBuf+4, 4, &pucDataSet2[1], 4);
    (void)memcpy_s(recBuf+8, 4, &pucDataSet2[2], 4);
    (void)memcpy_s(recBuf+12, 4, &pucDataSet2[3], 4);
    (void)memcpy_s(recBuf+16, 4, &pucDataSet2[4], 4);
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    memcpy_s(pstCond.aCond[0].aucValue, 4, &pucDataSet[1], 4);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    V1_AW_MACRO_EXPECT_EQ_INT(20, pstDsBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(200, pstDsBufGet.StdBuf.ulBufLen);
    V1_AW_MACRO_EXPECT_EQ_INT(40, pstDsBufGet.StdBuf.ulActLen);
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[0], *(float *)pstDsBufGet.StdBuf.pucData));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[1], *(float *)(pstDsBufGet.StdBuf.pucData + 4)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[2], *(float *)(pstDsBufGet.StdBuf.pucData + 8)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[3], *(float *)(pstDsBufGet.StdBuf.pucData + 12)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[4], *(float *)(pstDsBufGet.StdBuf.pucData + 16)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[0], *(float *)(pstDsBufGet.StdBuf.pucData + 20)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[1], *(float *)(pstDsBufGet.StdBuf.pucData + 24)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[2], *(float *)(pstDsBufGet.StdBuf.pucData + 28)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[3], *(float *)(pstDsBufGet.StdBuf.pucData + 32)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[4], *(float *)(pstDsBufGet.StdBuf.pucData + 36)));

    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrder接口测试
TEST_F(Query_ApiArg, V1Com_008_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_DOUBLE, DBT_DOUBLE, DBT_DOUBLE, DBT_DOUBLE, DBT_DOUBLE };
    uint32_t usSizeLst[ulNCols] = { 8, 8, 8, 8, 8 };
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = usSizeLst[i];
    }

    VOS_UINT32 ulNIdxs = 1;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    for (uint32_t i = 0; i < ulNIdxs; ++i) {
        (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[i]);
        pstIdxLst[i].enIndexType = enIndexTypeLst[i];
        pstIdxLst[i].ucUniqueFlag = ucUniqueFlagLst[i];
        pstIdxLst[i].ucIdxFldNum = ucIdxFldNum[i];
        for (uint32_t j = 0; j < ucIdxFldNum[i]; ++j) {
            pstIdxLst[i].aucFieldID[j] = aucFieldIDLst[i][j];
        }
    }

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 10000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    double pucDataSet[2][5] = { {10.1, 8.2, 12.3, 3.3, 1.1}, {9.1, 8.2, 22.0, 13.1, 11.1} };
    uint8_t *recBuf;
    recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    memset(recBuf, 0, recLen);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)memcpy_s(recBuf + 8 * i, 8, &pucDataSet[0][i], 8);
    }
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    memset(recBuf, 0, recLen);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)memcpy_s(recBuf + 8 * i, 8, &pucDataSet[1][i], 8);
    }
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    memcpy_s(pstCond.aCond[0].aucValue, 8, &pucDataSet[0][1], 8);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = recLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = recLen * pstDsBufGet.usRecNum;
    VOS_UINT8 pucDataGet[pstDsBufGet.StdBuf.ulBufLen] = { 0 };
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    V1_AW_MACRO_EXPECT_EQ_INT(40, pstDsBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(400, pstDsBufGet.StdBuf.ulBufLen);
    V1_AW_MACRO_EXPECT_EQ_INT(80, pstDsBufGet.StdBuf.ulActLen);
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][0], *(double *)pstDsBufGet.StdBuf.pucData));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][1], *(double *)(pstDsBufGet.StdBuf.pucData + 8)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][2], *(double *)(pstDsBufGet.StdBuf.pucData + 16)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][3], *(double *)(pstDsBufGet.StdBuf.pucData + 24)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][4], *(double *)(pstDsBufGet.StdBuf.pucData + 32)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][0], *(double *)(pstDsBufGet.StdBuf.pucData + 40)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][1], *(double *)(pstDsBufGet.StdBuf.pucData + 48)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][2], *(double *)(pstDsBufGet.StdBuf.pucData + 56)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][3], *(double *)(pstDsBufGet.StdBuf.pucData + 64)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][4], *(double *)(pstDsBufGet.StdBuf.pucData + 72)));

    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrderEx接口测试
TEST_F(Query_ApiArg, V1Com_008_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32 };
    VOS_UINT16 usSizeLst[ulNCols] = { 4, 4, 4, 4, 4 };
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = usSizeLst[i];
    }

    VOS_UINT32 ulNIdxs = 1;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    for (uint32_t i = 0; i < ulNIdxs; ++i) {
        (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[i]);
        pstIdxLst[i].enIndexType = enIndexTypeLst[i];
        pstIdxLst[i].ucUniqueFlag = ucUniqueFlagLst[i];
        pstIdxLst[i].ucIdxFldNum = ucIdxFldNum[i];
        for (uint32_t j = 0; j < ucIdxFldNum[i]; ++j) {
            pstIdxLst[i].aucFieldID[j] = aucFieldIDLst[i][j];
        }
    }

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 100000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    VOS_UINT32 pucDataSet[5] = { 10, 8, 12, 3, 1 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 pucDataSet2[5] = { 9, 8, 22, 13, 11 };
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet2
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 8;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_BUF_STRU pstBufGet;
    pstBufGet.usRecLen = recLen;
    pstBufGet.ulRecNum = DB_SELECT_ALL;
    pstBufGet.ulBufLen = recLen;
    pstBufGet.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufGet.ulBufLen);
    (void)memset_s(pstBufGet.pBuf, pstBufGet.ulBufLen, 0x00, pstBufGet.ulBufLen);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    TestSetGlobalLastErrInfo("Buf is not enough to store all records when execute scan, rel name QueryTestTb.");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(g_lastErrInfo, false));
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstBufGet.ulRecNum);
    TEST_V1_FREE(pstBufGet.pBuf);

    pstBufGet.ulBufLen = recLen * pstBufGet.ulRecNum;
    pstBufGet.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufGet.ulBufLen);
    (void)memset_s(pstBufGet.pBuf, pstBufGet.ulBufLen, 0x00, pstBufGet.ulBufLen);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    V1_AW_MACRO_EXPECT_EQ_INT(20, pstBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstBufGet.ulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(recLen * pstBufGet.ulRecNum, pstBufGet.ulBufLen);
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[0], *(uint32_t *)pstBufGet.pBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[1], *((uint32_t *)pstBufGet.pBuf + 1));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[2], *((uint32_t *)pstBufGet.pBuf + 2));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[3], *((uint32_t *)pstBufGet.pBuf + 3));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[4], *((uint32_t *)pstBufGet.pBuf + 4));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[0], *((uint32_t *)pstBufGet.pBuf + 5));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[1], *((uint32_t *)pstBufGet.pBuf + 6));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[2], *((uint32_t *)pstBufGet.pBuf + 7));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[3], *((uint32_t *)pstBufGet.pBuf + 8));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[4], *((uint32_t *)pstBufGet.pBuf + 9));
    TEST_V1_FREE(pstBufGet.pBuf);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrderEx接口测试
TEST_F(Query_ApiArg, V1Com_008_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32 };
    VOS_UINT16 usSizeLst[ulNCols] = { 4, 4, 4, 4, 4 };
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = usSizeLst[i];
    }

    VOS_UINT32 ulNIdxs = 1;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    for (uint32_t i = 0; i < ulNIdxs; ++i) {
        (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[i]);
        pstIdxLst[i].enIndexType = enIndexTypeLst[i];
        pstIdxLst[i].ucUniqueFlag = ucUniqueFlagLst[i];
        pstIdxLst[i].ucIdxFldNum = ucIdxFldNum[i];
        for (uint32_t j = 0; j < ucIdxFldNum[i]; ++j) {
            pstIdxLst[i].aucFieldID[j] = aucFieldIDLst[i][j];
        }
    }

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 100000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    VOS_UINT32 pucDataSet[5] = { 10, 8, 12, 3, 1 };
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 pucDataSet2[5] = { 9, 8, 22, 13, 11 };
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = (VOS_UINT8 *)pucDataSet2
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    pstCond.aCond[0].aucValue[0] = 8;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_BUF_STRU pstBufGet;
    pstBufGet.usRecLen = recLen;
    pstBufGet.ulRecNum = 10;
    pstBufGet.ulBufLen = recLen * pstBufGet.ulRecNum;
    pstBufGet.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufGet.ulBufLen);
    (void)memset_s(pstBufGet.pBuf, pstBufGet.ulBufLen, 0x00, pstBufGet.ulBufLen);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    V1_AW_MACRO_EXPECT_EQ_INT(20, pstBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstBufGet.ulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(200, pstBufGet.ulBufLen);
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[0], *(uint32_t *)pstBufGet.pBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[1], *((uint32_t *)pstBufGet.pBuf + 1));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[2], *((uint32_t *)pstBufGet.pBuf + 2));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[3], *((uint32_t *)pstBufGet.pBuf + 3));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet2[4], *((uint32_t *)pstBufGet.pBuf + 4));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[0], *((uint32_t *)pstBufGet.pBuf + 5));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[1], *((uint32_t *)pstBufGet.pBuf + 6));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[2], *((uint32_t *)pstBufGet.pBuf + 7));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[3], *((uint32_t *)pstBufGet.pBuf + 8));
    V1_AW_MACRO_EXPECT_EQ_INT(pucDataSet[4], *((uint32_t *)pstBufGet.pBuf + 9));

    TEST_V1_FREE(pstBufGet.pBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrderEx接口测试
TEST_F(Query_ApiArg, V1Com_008_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_STRING, DBT_STRING, DBT_STRING, DBT_STRING, DBT_STRING };
    uint32_t usSizeLst[ulNCols] = { 15, 15, 15, 15, 15 };
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = usSizeLst[i];
    }

    VOS_UINT32 ulNIdxs = 1;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    for (uint32_t i = 0; i < ulNIdxs; ++i) {
        (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[i]);
        pstIdxLst[i].enIndexType = enIndexTypeLst[i];
        pstIdxLst[i].ucUniqueFlag = ucUniqueFlagLst[i];
        pstIdxLst[i].ucIdxFldNum = ucIdxFldNum[i];
        for (uint32_t j = 0; j < ucIdxFldNum[i]; ++j) {
            pstIdxLst[i].aucFieldID[j] = aucFieldIDLst[i][j];
        }
    }

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 10000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    unsigned char pucDataSet[ulNCols][10] = { "xxxyyy123", "aaabbb123", "cccddd123", "mmmnnn123", "lllxxx123" };
    uint8_t *recBuf;
    recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    memset(recBuf, 0, recLen);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)memcpy_s(recBuf + 16 * i, 16, &pucDataSet[i], 16);
    }
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    unsigned char pucDataSet2[ulNCols][10] = { "xxxaaa123", "aaabbb123", "dddccc123", "nnnmmm123", "xxxlll123" };
    memset(recBuf, 0, recLen);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)memcpy_s(recBuf + 16 * i, 16, &pucDataSet2[i], 16);
    }
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    memcpy_s(pstCond.aCond[0].aucValue, 16, &pucDataSet[1], 16);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_BUF_STRU pstBufGet;
    pstBufGet.usRecLen = recLen;
    pstBufGet.ulRecNum = 10;
    pstBufGet.ulBufLen = recLen * pstBufGet.ulRecNum;
    pstBufGet.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufGet.ulBufLen);
    (void)memset_s(pstBufGet.pBuf, pstBufGet.ulBufLen, 0x00, pstBufGet.ulBufLen);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    V1_AW_MACRO_EXPECT_EQ_INT(80, pstBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstBufGet.ulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(800, pstBufGet.ulBufLen);
    uint32_t addr = 0;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[0], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[1], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[2], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[3], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet2[4], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[0], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[1], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[2], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[3], (char *)pstBufGet.pBuf + addr * 16);
    ++addr;
    AW_MACRO_EXPECT_EQ_STR((char *)pucDataSet[4], (char *)pstBufGet.pBuf + addr * 16);

    TEST_V1_FREE(recBuf);
    TEST_V1_FREE(pstBufGet.pBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrderEx接口测试
TEST_F(Query_ApiArg, V1Com_008_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_FLOAT, DBT_FLOAT, DBT_FLOAT, DBT_FLOAT, DBT_FLOAT };
    VOS_UINT16 usSizeLst[ulNCols] = { sizeof(DBT_FLOAT), sizeof(DBT_FLOAT), sizeof(DBT_FLOAT),
        sizeof(DBT_FLOAT), sizeof(DBT_FLOAT) };
    
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = sizeof(DBT_FLOAT);
    }

    VOS_UINT32 ulNIdxs = 2;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1", "idx2" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE, DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1, 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 2, 3 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0, 1}, {0, 1, 2} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * 1);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * 1);
    (void)sprintf_s((char *)pstIdxLst[0].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[0]);
    pstIdxLst[0].enIndexType = enIndexTypeLst[0];
    pstIdxLst[0].ucUniqueFlag = 1;
    pstIdxLst[0].ucIdxFldNum = 1;
    pstIdxLst[0].aucFieldID[0] = 0;

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 10000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    float pucDataSet[5] = { 10.1, 8.2, 12.3, 3.3, 1.1 };
    uint8_t *recBuf;
    recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    memset(recBuf, 0, recLen);
    (void)memcpy_s(recBuf, 4, &pucDataSet[0], 4);
    (void)memcpy_s(recBuf+4, 4, &pucDataSet[1], 4);
    (void)memcpy_s(recBuf+8, 4, &pucDataSet[2], 4);
    (void)memcpy_s(recBuf+12, 4, &pucDataSet[3], 4);
    (void)memcpy_s(recBuf+16, 4, &pucDataSet[4], 4);
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    float pucDataSet2[5] = { 9.1, 8.2, 22.0, 13.1, 11.1 };
    memset(recBuf, 0, recLen);
    (void)memcpy_s(recBuf, 4, &pucDataSet2[0], 4);
    (void)memcpy_s(recBuf+4, 4, &pucDataSet2[1], 4);
    (void)memcpy_s(recBuf+8, 4, &pucDataSet2[2], 4);
    (void)memcpy_s(recBuf+12, 4, &pucDataSet2[3], 4);
    (void)memcpy_s(recBuf+16, 4, &pucDataSet2[4], 4);
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    memcpy_s(pstCond.aCond[0].aucValue, 4, &pucDataSet[1], 4);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_BUF_STRU pstBufGet;
    pstBufGet.usRecLen = recLen;
    pstBufGet.ulRecNum = 10;
    pstBufGet.ulBufLen = recLen * pstBufGet.ulRecNum;
    pstBufGet.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufGet.ulBufLen);
    (void)memset_s(pstBufGet.pBuf, pstBufGet.ulBufLen, 0x00, pstBufGet.ulBufLen);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    V1_AW_MACRO_EXPECT_EQ_INT(20, pstBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstBufGet.ulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(200, pstBufGet.ulBufLen);
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[0], *(float *)pstBufGet.pBuf));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[1], *((float *)pstBufGet.pBuf + 1)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[2], *((float *)pstBufGet.pBuf + 2)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[3], *((float *)pstBufGet.pBuf + 3)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet2[4], *((float *)pstBufGet.pBuf + 4)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[0], *((float *)pstBufGet.pBuf + 5)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[1], *((float *)pstBufGet.pBuf + 6)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[2], *((float *)pstBufGet.pBuf + 7)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[3], *((float *)pstBufGet.pBuf + 8)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareFloat(pucDataSet[4], *((float *)pstBufGet.pBuf + 9)));

    TEST_V1_FREE(recBuf);
    TEST_V1_FREE(pstBufGet.pBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecByOrderEx接口测试
TEST_F(Query_ApiArg, V1Com_008_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    VOS_UINT32 ulNCols = 5;
    char aucFieldNameLst[ulNCols][DB_FLD_NAME_LEN] = { "F1", "F2", "F3", "F4", "F5" };
    DB_DATATYPE_ENUM_V1 enDataTypeLst[ulNCols] = { DBT_DOUBLE, DBT_DOUBLE, DBT_DOUBLE, DBT_DOUBLE, DBT_DOUBLE };
    uint32_t usSizeLst[ulNCols] = { 8, 8, 8, 8, 8 };
    DB_FIELD_DEF_STRU *pstFldLst = NULL;
    pstFldLst = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    memset(pstFldLst, 0, sizeof(DB_FIELD_DEF_STRU) * ulNCols);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)sprintf_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, "%s", aucFieldNameLst[i]);
        pstFldLst[i].enDataType = enDataTypeLst[i];
        pstFldLst[i].usSize = usSizeLst[i];
    }

    VOS_UINT32 ulNIdxs = 1;
    char aucIndexNameLst[ulNIdxs][DB_IDX_NAME_LEN] = { "idx1" };
    DBDDL_INDEXTYPE_ENUM enIndexTypeLst[ulNIdxs] = { DBDDL_INDEXTYPE_TTREE };
    VOS_UINT8 ucUniqueFlagLst[ulNIdxs] = { 1 };
    VOS_UINT8 ucIdxFldNum[ulNIdxs] = { 1 };
    VOS_UINT8 aucFieldIDLst[ulNIdxs][DB_IDX_FLD_MAX] = { {0} };
    DB_INDEX_DEF_STRU *pstIdxLst = NULL;
    pstIdxLst = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    memset(pstIdxLst, 0, sizeof(DB_INDEX_DEF_STRU) * ulNIdxs);
    for (uint32_t i = 0; i < ulNIdxs; ++i) {
        (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_IDX_NAME_LEN, "%s", aucIndexNameLst[i]);
        pstIdxLst[i].enIndexType = enIndexTypeLst[i];
        pstIdxLst[i].ucUniqueFlag = ucUniqueFlagLst[i];
        pstIdxLst[i].ucIdxFldNum = ucIdxFldNum[i];
        for (uint32_t j = 0; j < ucIdxFldNum[i]; ++j) {
            pstIdxLst[i].aucFieldID[j] = aucFieldIDLst[i][j];
        }
    }

    DB_REL_DEF_STRU pstRelDef;
    memset_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, '\0', DB_REL_NAME_LEN);
    sprintf_s((char *)pstRelDef.aucRelName, DB_REL_NAME_LEN, "QueryTestTb");
    pstRelDef.enTableType = DB_TABLE_NORMAL;
    pstRelDef.ulIntialSize = 10000;
    pstRelDef.ulMaxSize = 10000;
    pstRelDef.ulNCols = ulNCols;
    pstRelDef.ulNIdxs = 1;
    pstRelDef.pstFldLst = pstFldLst;
    pstRelDef.pstIdxLst = pstIdxLst;
    VOS_UINT16 pusRelId;
    ret = TPC_CreateTbl(g_queryTestDbId, &pstRelDef, &pusRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pstFldLst);
    TEST_V1_FREE(pstIdxLst);

    // 写入数据
    uint16_t recLen = DmlTestGetRecLen(&pstRelDef);
    double pucDataSet[2][5] = { {10.1, 8.2, 12.3, 3.3, 1.1}, {9.1, 8.2, 22.0, 13.1, 11.1} };
    uint8_t *recBuf;
    recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    memset(recBuf, 0, recLen);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)memcpy_s(recBuf + 8 * i, 8, &pucDataSet[0][i], 8);
    }
    DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    memset(recBuf, 0, recLen);
    for (uint32_t i = 0; i < ulNCols; ++i) {
        (void)memcpy_s(recBuf + 8 * i, 8, &pucDataSet[1][i], 8);
    }
    DB_DSBUF_STRU dsBuf2 = {
            .usRecLen = recLen,
            .usRecNum = 1,
            .StdBuf = {
                .ulBufLen = recLen,
                .ulActLen = recLen,
                .pucData = recBuf
                }
            };
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 查询
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 0;
    pstSort.pSortFields = sortFields;

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 1;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    memcpy_s(pstCond.aCond[0].aucValue, 8, &pucDataSet[0][1], 8);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 5;
    pstFldFilter.aucField[0] = 0;
    pstFldFilter.aucField[1] = 1;
    pstFldFilter.aucField[2] = 2;
    pstFldFilter.aucField[3] = 3;
    pstFldFilter.aucField[4] = 4;

    DB_BUF_STRU pstBufGet;
    pstBufGet.usRecLen = recLen;
    pstBufGet.ulRecNum = 10;
    pstBufGet.ulBufLen = recLen * pstBufGet.ulRecNum;
    pstBufGet.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufGet.ulBufLen);
    (void)memset_s(pstBufGet.pBuf, pstBufGet.ulBufLen, 0x00, pstBufGet.ulBufLen);
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    V1_AW_MACRO_EXPECT_EQ_INT(40, pstBufGet.usRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pstBufGet.ulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(400, pstBufGet.ulBufLen);
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][0], *(double *)pstBufGet.pBuf));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][1], *((double *)pstBufGet.pBuf + 1)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][2], *((double *)pstBufGet.pBuf + 2)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][3], *((double *)pstBufGet.pBuf + 3)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[1][4], *((double *)pstBufGet.pBuf + 4)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][0], *((double *)pstBufGet.pBuf + 5)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][1], *((double *)pstBufGet.pBuf + 6)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][2], *((double *)pstBufGet.pBuf + 7)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][3], *((double *)pstBufGet.pBuf + 8)));
    V1_AW_MACRO_EXPECT_EQ_INT(0, DmCompareDouble(pucDataSet[0][4], *((double *)pstBufGet.pBuf + 9)));

    TEST_V1_FREE(recBuf);
    TEST_V1_FREE(pstBufGet.pBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}
