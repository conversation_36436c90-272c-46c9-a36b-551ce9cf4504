/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 Description  : 支持一阶段DB的 cfg 接口部署配置类接口兼容
 Author       : herui h60035902
 Modification :
 create       : 2025/04/02
**************************************************************************** */
#include "Cfgapi.h"

int ret = 0;
class FunofCfgApi_Test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestTPC_Init();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        ret = TestTPC_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void FunofCfgApi_Test::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    system("mkdir filePath");
}
void FunofCfgApi_Test::TearDown()
{
    system("rm -rf filePath");
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
// 010.DB_RegCheckSumFeature不启用校验和功能
TEST_F(FunofCfgApi_Test, V1Com_029_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 不启用校验和
    DBHOOK_CheckSumCalc_t pfCalcChecksum = CheckDefaultCalc;
    DBHOOK_CheckSumNegate_t pfNegateChecksum = CheckDefaultNegate;
    DBHOOK_CheckSumCompare_t pfCompChecksum = CheckSumCompare;
    // 第二个参数传NULL
    ret = DB_RegCheckSumFeature(false, NULL, pfNegateChecksum, pfCompChecksum, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {0, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    InitGlobalv();
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验钩子函数是否被调用
    for (int i = 0; i < 3; i++) {
        V1_AW_MACRO_EXPECT_EQ_INT(0, g_invokingTimes[i]);
    }
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 011.DB_RegCheckSumFeature大端启用校验和功能
TEST_F(FunofCfgApi_Test, V1Com_029_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    InitGlobalv();
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(g_testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验钩子函数是否被调用
    for (int i = 0; i < 2; i++) {
        if (g_invokingTimes[i] < 1) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
        }
    }
    V1_AW_MACRO_EXPECT_EQ_INT(0, g_invokingTimes[2]);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 012.DB_RegCheckSumFeature小端启用校验和功能
TEST_F(FunofCfgApi_Test, V1Com_029_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    InitGlobalv();
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(g_testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验钩子函数是否被调用
    for (int i = 0; i < 2; i++) {
        if (g_invokingTimes[i] < 1) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
        }
        AW_FUN_Log(LOG_STEP, "g_invokingTime%d is %d", i, g_invokingTimes[i]);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(0, g_invokingTimes[2]);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 013.钩子函数传NULL多次重复调用DB_RegCheckSumFeature
TEST_F(FunofCfgApi_Test, V1Com_029_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("touch filePath/de.txt");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 启用校验和
    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建DB
    InitGlobalv();
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验钩子函数是否被调用
    for (int i = 0; i < 3; i++) {
        V1_AW_MACRO_EXPECT_EQ_INT(0, g_invokingTimes[i]);
    }
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 014.钩子函数每次传入不同函数多次调用DB_RegCheckSumFeature
TEST_F(FunofCfgApi_Test, V1Com_029_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("touch filePath/de.txt");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 启用校验和
    DBHOOK_CheckSumCalc_t pfCalcChecksum = CheckDefaultCalc;
    DBHOOK_CheckSumCalc_t pfCalcChecksum1 = CheckDefaultCalc1;
    DBHOOK_CheckSumNegate_t pfNegateChecksum = CheckDefaultNegate;
    DBHOOK_CheckSumCompare_t pfCompChecksum = CheckSumCompare;
    for (int i = 0; i < 3; i++) {
        ret = DB_RegCheckSumFeature(true, pfCalcChecksum, pfNegateChecksum, pfCompChecksum, &pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_RegCheckSumFeature(true, pfCalcChecksum1, pfNegateChecksum, pfCompChecksum, &pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    VOS_UINT32 selectways[7] = {0, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    InitGlobalv();
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验钩子函数是否被调用
    if (g_invokingTimes[3] == 0) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(0, g_invokingTimes[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(0, g_invokingTimes[2]);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 015.作为第一个注册的功能调用DB_RegCheckSumFeature
TEST_F(FunofCfgApi_Test, V1Com_029_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("touch filePath/de.txt");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 启用校验和
    DBHOOK_CheckSumCalc_t pfCalcChecksum = CheckDefaultCalc;
    DBHOOK_CheckSumNegate_t pfNegateChecksum = CheckDefaultNegate;
    DBHOOK_CheckSumCompare_t pfCompChecksum = CheckSumCompare;
    ret = DB_RegCheckSumFeature(true, pfCalcChecksum, pfNegateChecksum, pfCompChecksum, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {0, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 016.DB_RegDbDataStorage注册时数据存储类型选择RSM
TEST_F(FunofCfgApi_Test, V1Com_029_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    DB_DATA_STORAGE_ENUM enStorage = DB_DATA_STORAGE_RSM;
    ret = DB_RegDbDataStorage(enStorage, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    VOS_UINT32 selectways[7] = {1, 0, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 017.DB_RegDbDataStorage注册时数据存储类型选择RAM
TEST_F(FunofCfgApi_Test, V1Com_029_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 018.DB_RegDbDataStorage注册时数据存储类型选择BUTT
TEST_F(FunofCfgApi_Test, V1Com_029_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    DB_DATA_STORAGE_ENUM enStorage = DB_DATA_STORAGE_BUTT;
    ret = DB_RegDbDataStorage(enStorage, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    VOS_UINT32 selectways[7] = {1, 0, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 019.多次调用DB_RegDbDataStorage修改存储类型
TEST_F(FunofCfgApi_Test, V1Com_029_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    DB_DATA_STORAGE_ENUM enStorage = DB_DATA_STORAGE_RSM;
    ret = DB_RegDbDataStorage(enStorage, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    enStorage = DB_DATA_STORAGE_BUTT;
    ret = DB_RegDbDataStorage(enStorage, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 020.作为第一个注册的功能调用DB_RegDbDataStorage
TEST_F(FunofCfgApi_Test, V1Com_029_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    DB_DATA_STORAGE_ENUM enStorage = DB_DATA_STORAGE_RAM;
    ret = DB_RegDbDataStorage(enStorage, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 0, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 021.注册时bPersistent选择持久化导入类型选择REPLACE
TEST_F(FunofCfgApi_Test, V1Com_029_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 0, 0, 1, 0, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 设置导入为持久化DB
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 修改为创建持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入类型选择REPLACE
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建DB
    // 验证创建的是持久化DB
    ret = DB_CreateDB2((VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    CreateAndOpenDB(&pstFeature, (VOS_UINT8 *)dbDir);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证导入的是持久化DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 022.注册时bPersistent选择持久化导入类型选择DISCARD
TEST_F(FunofCfgApi_Test, V1Com_029_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 0, 1, 1, 0, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 设置导入为持久化DB
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 修改为创建持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建DB
    // 验证创建的是持久化DB
    ret = DB_CreateDB2((VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    CreateAndOpenDB(&pstFeature, (VOS_UINT8 *)dbDir);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 验证导入的是持久化DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    // 导入DB
    system("touch filePath/de1.txt");
    char dbDir1[256] = "./filePath/de1.txt";
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir1, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 023.注册时bPersistent选择非持久化导入类型选择REPLACE
TEST_F(FunofCfgApi_Test, V1Com_029_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 0, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 导入类型选择REPLACE
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 024.注册时bPersistent选择非持久化导入类型选择DISCARD
TEST_F(FunofCfgApi_Test, V1Com_029_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 025.多次调用DB_RegDbRestoreConfig修改值
TEST_F(FunofCfgApi_Test, V1Com_029_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreConfig(false, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 026.作为第一个注册的功能调用DB_RegDbRestoreConfig
TEST_F(FunofCfgApi_Test, V1Com_029_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 0, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 027.不调用DB_RegDbRestoreConfig时进行导入
TEST_F(FunofCfgApi_Test, V1Com_029_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 0, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 028.DB_RegDbRestoreTypeCfg注册时导入类型选择BUTT
TEST_F(FunofCfgApi_Test, V1Com_029_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 导入类型选择BUTT
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_BUTT;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_RESTORETYPE, ret);
    VOS_UINT32 selectways1[7] = {1, 1, 1, 0, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways1);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 029.多次调用DB_RegDbRestoreTypeCfg修改导入类型
TEST_F(FunofCfgApi_Test, V1Com_029_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 导入类型选择REPLACE
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入类型选择DISCARD
    enRestoreType = DB_RESTORETYPE_DISCARD;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways1[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways1);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 030.只包含必须的两个注册函数时进行导入
TEST_F(FunofCfgApi_Test, V1Com_029_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {0, 0, 1, 1, 0, 0, 0};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 031.作为第一个注册的功能调用DB_RegDbRestoreTypeCfg
TEST_F(FunofCfgApi_Test, V1Com_029_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 导入类型选择REPLACE
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways1[7] = {1, 1, 1, 0, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways1);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 032.不调用DB_RegDbRestoreTypeCfg时进行导入
TEST_F(FunofCfgApi_Test, V1Com_029_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 0, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
DB_FldConvHook fldHookArray[28] = {NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL};
DB_FldConvHook *GetTblConvHookNoConv(VOS_UINT16 usRelId)
{
    return fldHookArray;
}
// 033.表中有DBT_BLOCK和user-define类型，但转换函数传NULL
TEST_F(FunofCfgApi_Test, V1Com_029_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 0, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    DB_GetTblConvHook pfnGetTblConvHook1 = GetTblConvHookNoConv;
    ret = DB_RegTableConvFunc(pfnGetTblConvHook1, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(g_testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 034.导出大端数据导入到小端机器上
TEST_F(FunofCfgApi_Test, V1Com_029_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 0, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    DB_GetTblConvHook pfnGetTblConvHook1 = GetTblConvHook1;
    ret = DB_RegTableConvFunc(pfnGetTblConvHook1, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(g_testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 1, testRelDef.pstFldLst, false);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 035.多次调用DB_RegTableConvFunc修改转换函数
TEST_F(FunofCfgApi_Test, V1Com_029_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    DB_GetTblConvHook pfnGetTblConvHook1 = GetTblConvHook1;
    ret = DB_RegTableConvFunc(pfnGetTblConvHook1, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_GetTblConvHook pfnGetTblConvHook2 = GetTblConvHookNoConv;
    ret = DB_RegTableConvFunc(pfnGetTblConvHook2, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    InitGlobalv();
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(g_testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(9, g_invokingTimes[4]);
    V1_AW_MACRO_EXPECT_EQ_INT(0, g_invokingTimes[5]);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 036.作为第一个注册的功能调用DB_RegTableConvFunc
TEST_F(FunofCfgApi_Test, V1Com_029_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    DB_GetTblConvHook pfnGetTblConvHook1 = GetTblConvHook1;
    ret = DB_RegTableConvFunc(pfnGetTblConvHook1, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 0, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 1, testRelDef.pstFldLst, false);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 037.DB_RegDbConfigStructure注册时enPersistent选择持久化
TEST_F(FunofCfgApi_Test, V1Com_029_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改为创建持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 0, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 验证创建的是持久化DB
    ret = DB_CreateDB2((VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    // 创建DB
    CreateAndOpenDB(&pstFeature, (VOS_UINT8 *)dbDir);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 038.DB_RegDbConfigStructure注册时enPersistent选择非持久化
TEST_F(FunofCfgApi_Test, V1Com_029_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改为创建非持久化DB
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 039.DB_RegDbConfigStructure注册时修改最大描述信息长度
TEST_F(FunofCfgApi_Test, V1Com_029_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改最大描述信息长度
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    enConfigStructure.ulMaxDBDescInfoSize = 20;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 0, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    CreateAndOpenDB(&pstFeature, (VOS_UINT8 *)dbDir);
    // 建表并写数据
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Delete(testRelId1, 0, false, 1);
    // 设置描述信息比注册时设置的大1
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"123456789012345678901";
    uint32_t ulLen = 21;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置描述信息和注册时设置的一样
    DBDescrInfo = (VOS_VOID *)"12345678901234567890";
    ulLen = 20;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    uint32_t pulLen = 20;
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(pulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)g_testDbName, NULL, getDBDescrInfo, &pulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcpy_s(getDBDescrInfo, ulLen, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(getDBDescrInfo);
}
// 040.DB_RegDbConfigStructure注册时将DB的最大描述信息长度设置为0xFFFFFFFD
TEST_F(FunofCfgApi_Test, V1Com_029_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改最大描述信息长度
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    enConfigStructure.ulMaxDBDescInfoSize = 0xFFFFFFFD;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 0, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    ret = DB_CreateDB2((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 041.DB_RegDbConfigStructure注册时将DB的最大描述信息长度设置为0xFFFFFFFF
TEST_F(FunofCfgApi_Test, V1Com_029_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改最大描述信息长度
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    enConfigStructure.ulMaxDBDescInfoSize = 0xFFFFFFFF;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 0, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    ret = DB_CreateDB2((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 042.多次调用DB_RegDbConfigStructure修改值
TEST_F(FunofCfgApi_Test, V1Com_029_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改最大描述信息长度
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_INST_CONFIG_STRU enConfigStructure2 = {0};
    enConfigStructure2.enPersistent = DB_CKP_NONE;
    ret = DB_RegDbConfigStructure(&enConfigStructure2, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 043.作为第一个注册的功能调用DB_RegDbConfigStructure
TEST_F(FunofCfgApi_Test, V1Com_029_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改最大描述信息长度
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_NONE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 0, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    CreateAndOpenDB(&pstFeature, (VOS_UINT8 *)dbDir);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 044.不调用DB_RegDbConfigStructure时进行建DB
TEST_F(FunofCfgApi_Test, V1Com_029_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 0, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    ret = DB_CreateDB2((VOS_UINT8 *)g_testDbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 045.只调用这个注册函数建DB成功
TEST_F(FunofCfgApi_Test, V1Com_029_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {0, 0, 0, 0, 0, 1, 0};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 删除DB
    CloseAndDeleteDB();
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 046.DB_RegFlexExtendFeature注册时usGrowthRate设置为0
TEST_F(FunofCfgApi_Test, V1Com_029_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改usGrowthRate为0
    ret = DB_RegFlexExtendFeature(0, 10, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 0};
    SelectcfgInterface(&pstFeature, selectways);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 047.DB_RegFlexExtendFeature注册时usGrowthRate设置为0xFFFF
TEST_F(FunofCfgApi_Test, V1Com_029_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改usGrowthRate为0xFFFF
    ret = DB_RegFlexExtendFeature(0xFFFF, 10, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 0};
    SelectcfgInterface(&pstFeature, selectways);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 048.DB_RegFlexExtendFeature注册时ulMaxStepSize设置为0
TEST_F(FunofCfgApi_Test, V1Com_029_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改ulMaxStepSize为0
    ret = DB_RegFlexExtendFeature(10, 0, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 0};
    SelectcfgInterface(&pstFeature, selectways);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 049.DB_RegFlexExtendFeature注册时ulMaxStepSize设置为0x01000001
TEST_F(FunofCfgApi_Test, V1Com_029_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    // 修改ulMaxStepSize为0x01000001
    ret = DB_RegFlexExtendFeature(10, 0x01000001, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 0};
    SelectcfgInterface(&pstFeature, selectways);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 050.usGrowthRate设置为0xFFFF-1， ulMaxStepSize设置为0x01000000调用4个建表接口分别建表并进行DML操作
TEST_F(FunofCfgApi_Test, V1Com_029_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 0};
    SelectcfgInterface(&pstFeature, selectways);
    // 修改usGrowthRate为0xFFFF-1
    ret = DB_RegFlexExtendFeature(0xFFFF - 1, 0x01000000, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 使用接口DB_CreateTblEx建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    char tableName[16] = "test_rel1";
    memcpy(testRelDef.aucRelName, tableName, strlen(tableName));
    ret = DB_CreateTblEx(g_testDbId, &testRelDef, &testRelId1, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 1);
    Delete(testRelId1, 11, false, 1);
    // 使用接口DB_CreateTblByIDEx建表
    uint16_t testRelId2 = 10;
    sprintf(tableName, "test_rel2");
    memcpy(testRelDef.aucRelName, tableName, strlen(tableName));
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId2, &testRelDef, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId2, 10, testRelDef.pstFldLst, false);
    Update(testRelId2, 0, 11, testRelDef.pstFldLst, false, 1);
    Delete(testRelId2, 11, false, 1);
    // 删除DB
    CloseAndDeleteDB();
    // 创建TPC_DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 使用接口TPC_CreateTblEx建表
    uint16_t testRelId3 = 0;
    sprintf(tableName, "test_rel3");
    memcpy(testRelDef.aucRelName, tableName, strlen(tableName));
    ret = TPC_CreateTblEx(g_testDbId, &testRelDef, &testRelId3, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId3, 10, testRelDef.pstFldLst, true);
    Update(testRelId3, 0, 11, testRelDef.pstFldLst, true, 1);
    Delete(testRelId3, 11, true, 1);
    // 使用接口TPC_CreateTblByIDEx建表
    uint16_t testRelId4 = 11;
    sprintf(tableName, "test_rel4");
    memcpy(testRelDef.aucRelName, tableName, strlen(tableName));
    ret = TPC_CreateTblByIDEx(g_testDbId, testRelId4, &testRelDef, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId4, 10, testRelDef.pstFldLst, true);
    Update(testRelId4, 0, 11, testRelDef.pstFldLst, true, 1);
    Delete(testRelId4, 11, true, 1);
    // 删除TPC_DB
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 051.作为第一个注册的功能调用DB_RegFlexExtendFeature
TEST_F(FunofCfgApi_Test, V1Com_029_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    ret = DB_RegFlexExtendFeature(10, 10, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 0};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    CreateAndOpenDB(&pstFeature);
    // 使用接口DB_CreateTblEx建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    char tableName[16] = "test_rel1";
    memcpy(testRelDef.aucRelName, tableName, strlen(tableName));
    ret = DB_CreateTblEx(g_testDbId, &testRelDef, &testRelId1, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 1);
    Delete(testRelId1, 11, false, 1);
    // 使用接口DB_CreateTblByIDEx建表
    uint16_t testRelId2 = 10;
    sprintf(tableName, "test_rel2");
    memcpy(testRelDef.aucRelName, tableName, strlen(tableName));
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId2, &testRelDef, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId2, 10, testRelDef.pstFldLst, false);
    Update(testRelId2, 0, 11, testRelDef.pstFldLst, false, 1);
    Delete(testRelId2, 11, false, 1);
    // 删除DB
    CloseAndDeleteDB();
    // 创建TPC_DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 使用接口TPC_CreateTblEx建表
    uint16_t testRelId3 = 0;
    sprintf(tableName, "test_rel3");
    memcpy(testRelDef.aucRelName, tableName, strlen(tableName));
    ret = TPC_CreateTblEx(g_testDbId, &testRelDef, &testRelId3, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId3, 10, testRelDef.pstFldLst, true);
    Update(testRelId3, 0, 11, testRelDef.pstFldLst, true, 1);
    Delete(testRelId3, 11, true, 1);
    // 使用接口TPC_CreateTblByIDEx建表
    uint16_t testRelId4 = 11;
    sprintf(tableName, "test_rel4");
    memcpy(testRelDef.aucRelName, tableName, strlen(tableName));
    ret = TPC_CreateTblByIDEx(g_testDbId, testRelId4, &testRelDef, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId4, 10, testRelDef.pstFldLst, true);
    Update(testRelId4, 0, 11, testRelDef.pstFldLst, true, 1);
    Delete(testRelId4, 11, true, 1);
    // 删除TPC_DB
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 052.不释放feature建255个DB
TEST_F(FunofCfgApi_Test, V1Com_029_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 创建DB
    for (int i = 0; i < 255; i++) {
        char dbName[20] = {0};
        sprintf(dbName, "test_db_%d", i);
        ret = DB_CreateDB2((VOS_UINT8 *)dbName, NULL, &pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 删除DB
    for (int i = 0; i < 255; i++) {
        char dbName[20] = {0};
        sprintf(dbName, "test_db_%d", i);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 053.每调用一个注册函数释放一次feature
TEST_F(FunofCfgApi_Test, V1Com_029_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    for (int i = 0; i < 7; i++) {
        VOS_UINT32 selectways[7] = {0};
        selectways[i] = 1;
        SelectcfgInterface(&pstFeature, selectways);
        ret = DB_FreeRegFeatureStruct(&pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 054.传入一个错误的指针
TEST_F(FunofCfgApi_Test, V1Com_029_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    pstFeature.ulMagicNum++;
    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_FEATURE_REG_CORRUPTED, ret);
}
// 055.DB_CreateDB2的DB名字长度大于16字节
TEST_F(FunofCfgApi_Test, V1Com_029_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    char dbName[20] = {0};
    sprintf(dbName, "db12345678901234");
    ret = DB_CreateDB2((VOS_UINT8 *)dbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 056.DB_CreateDB2的DB名字长度等于于16字节
TEST_F(FunofCfgApi_Test, V1Com_029_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    // 创建DB
    char dbName[20] = {0};
    sprintf(dbName, "db1234567890123");
    ret = DB_CreateDB2((VOS_UINT8 *)dbName, NULL, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 057.DB_CreateDB2的Dir等于256字节
TEST_F(FunofCfgApi_Test, V1Com_029_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir -p "
           "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/"
           "Directory0000000005/Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/"
           "Directory0000000010/Directory0000000011/Directory0000000012");
    system("touch "
           "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/"
           "Directory0000000005/Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/"
           "Directory0000000010/Directory0000000011/Directory0000000012/de.txt");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 修改为创建持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建DB
    char dbDir[] =
        "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/Directory0000000005/"
        "Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/Directory0000000010/"
        "Directory0000000011/Directory0000000012/de.txt";
    ret = DB_CreateDB2((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf cfgPath");
}
// 058.DB_CreateDB2的Dir大于256字节
TEST_F(FunofCfgApi_Test, V1Com_029_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir -p "
           "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/"
           "Directory0000000005/Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/"
           "Directory0000000010/Directory0000000011/Directory0000000012");
    system("touch "
           "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/"
           "Directory0000000005/Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/"
           "Directory0000000010/Directory0000000011/Directory0000000012/demo.txt");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 修改为创建持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建DB
    char dbDir[] =
        "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/Directory0000000005/"
        "Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/Directory0000000010/"
        "Directory0000000011/Directory0000000012/demo.txt";
    ret = DB_CreateDB2((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBPATH, ret);

    ret = DB_FreeRegFeatureStruct(&pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf cfgPath");
}
