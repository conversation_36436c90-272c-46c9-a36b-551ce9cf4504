/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : VistTool工具测试
 Notes        : 接口测试 + 功能测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2025/05/08
**************************************************************************** */
#include "VistTool.h"

class VistTool : public testing::Test {
protected:
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
public:
    virtual void SetUp();
    virtual void TearDown();
};
void VistTool::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    // 用于测试用例在设备失败的时候可以设置环境变量来打印一些具体的信息
    g_testcaseDebugModeVal = getenv("TESTCASE_DEBUG_MODE");
    if (g_testcaseDebugModeVal == NULL) {
        AW_FUN_Log(LOG_INFO, "if you want to printf testcase info, you can 'export TESTCASE_DEBUG_MODE=1'.");
    }
    system("rm -rf log");
}
void VistTool::TearDown()
{
    AW_CHECK_LOG_END();
}

DB_REL_DEF_STRU g_pstRelDef1;
VOS_UINT16 g_pusRelId1;
uint32_t g_recLen1 = 0;

// 001、开启cdb1和cdb2，cdb1插入数据，cdb2可以查询到
TEST_F(VistTool, V1Com_038_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    ret = TestTPC_InsertRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb2查数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、开启cdb1和cdb2，cdb1更新数据，cdb2可以查询到
TEST_F(VistTool, V1Com_038_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    ret = TestTPC_UpdateRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 10, 10, 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 110, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 110, 110);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb2查数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 110, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 110, 110);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、开启cdb1和cdb2，cdb1删除数据，cdb2可以查询不到
TEST_F(VistTool, V1Com_038_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1删除数据
    ret = TestTPC_DeleteRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, 10, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // cdb2查数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、开启cdb1和cdb2，cdb1和cdb2插入不同数据，均插入成功
TEST_F(VistTool, V1Com_038_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_BUF_STRU pstBufData;
    ret = TestMallocBuf(&pstBufData, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb2
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    ret = TestTPC_InsertRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2插入数据
    ret = TestTPC_InsertRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 12, 12);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1查询
    pstBufData.ulRecNum = 10;
    ret = TestTPC_SelectRecEx(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufData.ulRecNum);
    ret = TestCheckBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstBufData, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstBufData.ulRecNum = 10;
    ret = TestTPC_SelectRecEx(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 12, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufData.ulRecNum);
    ret = TestCheckBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstBufData, 12, 12);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb2查询
    pstBufData.ulRecNum = 10;
    ret = TestTPC_SelectRecEx(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufData.ulRecNum);
    ret = TestCheckBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstBufData, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstBufData.ulRecNum = 10;
    ret = TestTPC_SelectRecEx(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 12, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufData.ulRecNum);
    ret = TestCheckBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstBufData, 12, 12);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 12, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 12, 12);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);
    TestFreeBuf(&pstBufData);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、开启cdb1和cdb2，cdb1和cdb2插入相同数据，cdb2插入时报错主键冲突
TEST_F(VistTool, V1Com_038_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb2
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    ret = TestTPC_InsertRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2插入数据
    ret = TestTPC_InsertRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);

    // cdb1查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectAllRecByOrder(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb2查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectAllRecByOrder(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、开启cdb1和cdb2，cdb1插入和cdb2更新相同数据，均成功
TEST_F(VistTool, V1Com_038_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_BUF_STRU pstBufData;
    ret = TestMallocBuf(&pstBufData, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb2
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    ret = TestTPC_InsertRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2更新数据
    ret = TestTPC_UpdateRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 11, 11, 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1查询
    pstBufData.ulRecNum = 10;
    ret = TestTPC_SelectAllRecByOrderEx(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstBufData.ulRecNum = 10;
    ret = TestTPC_SelectAllRecByOrderEx(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 111, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufData.ulRecNum);
    ret = TestCheckBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstBufData, 111, 111);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb2查询
    pstBufData.ulRecNum = 10;
    ret = TestTPC_SelectAllRecByOrderEx(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstBufData.ulRecNum = 10;
    ret = TestTPC_SelectAllRecByOrderEx(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 111, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufData.ulRecNum);
    ret = TestCheckBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstBufData, 111, 111);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 111, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 111, 111);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);
    TestFreeBuf(&pstBufData);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、开启cdb1和cdb2，cdb1插入和cdb2删除相同数据，均成功
TEST_F(VistTool, V1Com_038_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb2
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    ret = TestTPC_InsertRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2删除数据
    ret = TestTPC_DeleteRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1查询
    ret = TestTPC_CountMatchingRecs(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // cdb2查询
    ret = TestTPC_CountMatchingRecs(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、开启cdb1和cdb2，cdb1和cdb2更新不同数据，均成功
TEST_F(VistTool, V1Com_038_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    ret = TestTPC_UpdateRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2更新数据
    ret = TestTPC_UpdateRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 2, 2, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1查询数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectFirstRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectFirstRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb2查询数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectFirstRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 2, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectFirstRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 12, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 12, 12);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 2, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 12, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 12, 12);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、开启cdb1和cdb2，cdb1和cdb2更新不同数据，均成功
TEST_F(VistTool, V1Com_038_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    ret = TestTPC_UpdateRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2更新数据
    ret = TestTPC_UpdateRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 2, 2, 9);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_CHGKEY, ret);

    // cdb1查询数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 验证接口
    ret = TestTPC_RecordExist(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    ret = TestTPC_RecordExist(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb2查询数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 2, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 验证接口
    ret = TestTPC_RecordExist(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    ret = TestTPC_RecordExist(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 2, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 2, 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、开启cdb1和cdb2，cdb1更新和cdb2删除相同数据，均成功
TEST_F(VistTool, V1Com_038_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    ret = TestTPC_UpdateRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2删除数据
    ret = TestTPC_DeleteRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, 1, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // cdb1查询数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_FetchSelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_FetchSelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb2查询数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_FetchSelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_FetchSelectRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 11, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 11, 11);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、开启cdb1和cdb2，cdb1和cdb2删除不同数据，均删除成功
TEST_F(VistTool, V1Com_038_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1删除数据
    ret = TestTPC_DeleteRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, 9, 9);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2删除数据
    ret = TestTPC_DeleteRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, 10, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1查询数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_FetchSelectRecByOrder(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 9, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_FetchSelectRecByOrder(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // cdb2查询数据
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_FetchSelectRecByOrder(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 9, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_FetchSelectRecByOrder(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 9, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、开启cdb1和cdb2，cdb1和cdb2删除相同数据，均删除成功
TEST_F(VistTool, V1Com_038_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_BUF_STRU pstBufData;
    ret = TestMallocBuf(&pstBufData, g_recLen1, 10, g_recLen1 * 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1删除数据
    ret = TestTPC_DeleteRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, 10, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // cdb2删除数据
    ret = TestTPC_DeleteRec(cdbId2, g_VistTool_TestDbId, g_pusRelId1, 10, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // cdb1查询数据
    pstBufData.ulRecNum = 10;
    ret = TestTPC_PrepareQueryPlan(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufData.ulRecNum);
    ret = TestCheckBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstBufData, 1, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstBufData.ulRecNum = 10;
    ret = TestTPC_PrepareQueryPlan(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // cdb2查询数据
    pstBufData.ulRecNum = 10;
    ret = TestTPC_PrepareQueryPlan(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 1, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufData.ulRecNum);
    ret = TestCheckBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstBufData, 1, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstBufData.ulRecNum = 10;
    ret = TestTPC_PrepareQueryPlan(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstBufData.ulRecNum = 10;
    ret = TestTPC_PrepareQueryPlan(cdbId2, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    TestFreeDsBuf(&pstDsBuf);
    TestFreeBuf(&pstBufData);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、不支持TPC_SelectCdbDiffData接口
TEST_F(VistTool, V1Com_038_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    ret = TestTPC_UpdateRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 10, 10, 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 110, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 110, 110);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取diff，不支持
    VOS_UINT32 pulInsRecNum = 0;
    VOS_VOID *ppInsRecList;
    VOS_UINT32 pulDelRecNum = 0;
    VOS_VOID *ppDelRecList;
    VOS_UINT32 pulUpdRecNum = 0;
    VOS_VOID *ppUpdRecList;
    ret = TPC_SelectCdbDiffData(cdbId1, g_pusRelId1, &pulInsRecNum, &ppInsRecList, &pulDelRecNum, &ppDelRecList,
        &pulUpdRecNum, &ppUpdRecList);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取diff，不支持
    ret = TPC_SelectCdbDiffData(TPC_GLOBAL_CDB, g_pusRelId1, &pulInsRecNum, &ppInsRecList, &pulDelRecNum,
        &ppDelRecList, &pulUpdRecNum, &ppUpdRecList);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、不支持图查询相关接口
TEST_F(VistTool, V1Com_038_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    ret = TestTPC_UpdateRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 10, 10, 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 110, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 110, 110);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 全量查询条件
    DB_COND_STRU pstCond = { .usCondNum = 0 };
    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 不支持图查询相关的接口
    ret = TPC_SelectAllRecByPath(cdbId1, g_VistTool_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter,
        NULL, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    DB_SELHANDLE phSelect;
    ret = TPC_BeginSelectByTopo(cdbId1, g_VistTool_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    DB_MUTIL_BUF_STRU *pstBuff;
    ret = TPC_FetchSelectTopoRec(cdbId1, phSelect, 1, &pstBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    ret = TPC_EndTopoSelect(cdbId1, phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 不支持图查询相关的接口
    ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter,
        NULL, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, 1, &pstBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、不支持TPC_RollbackCDB接口
TEST_F(VistTool, V1Com_038_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    ret = TestTPC_UpdateRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 10, 10, 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 110, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 110, 110);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_RollbackCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、表压缩有效果
TEST_F(VistTool, V1Com_038_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 100000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 1; i <= 100000; i++) {
        if (i % 2 == 0) {
            // cdb1删除数据
            ret = TestTPC_DeleteRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, i, i);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            pstDsBuf.usRecNum = 10;
            ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, i, &pstDsBuf);
            V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 延时
    sleep(5);

    // 表压缩
    ret = TestTPC_CompressTable(g_VistTool_TestDbId, g_pusRelId1, 1000000, 45000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、在有事务未提交的情况下，表压缩不会报错，原因：cdb操作转为RDB，不会存在编辑过记录的cdb
TEST_F(VistTool, V1Com_038_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取表的记录长度
    ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_VistTool_TestDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    ret = TestTPC_UpdateRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 10, 10, 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 10, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
    pstDsBuf.usRecNum = 10;
    ret = TestTPC_SelectRec(cdbId1, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, 110, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
    ret = TestCheckDsBuffVal(g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, 110, 110);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 表压缩
    ret = TestTPC_CompressTable(g_VistTool_TestDbId, g_pusRelId1, 10, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_VistTool_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018、vist bep一致性验证（--vist编译）
TEST_F(VistTool, V1Com_038_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t optRec[16] = { 0 };
    for (uint32_t i = 0; i < sizeof(optRec) / sizeof(uint32_t) - 1; i++) {
        optRec[i + 1] = optRec[i] + TestGetRandomRange(1, 5);
        if (g_testcaseDebugModeVal) {
            AW_FUN_Log(LOG_INFO, "optRec[%d]: %d", i, optRec[i]);
        }
    }

    uint32_t runCnt = 10;
    for (uint32_t i = 0; i < runCnt; i++) {
        // 建DB
        ret = TPC_CreateDB((VOS_UINT8 *)g_VistTool_DbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_VistTool_DbName, &g_VistTool_TestDbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 建表
        ret = TestTPC_CreateTbl(
            g_VistTool_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_VistTool_TblName1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        // 获取表的记录长度
        ret = TestGetTblRecLen(g_VistTool_TestDbId, g_pusRelId1, &g_recLen1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 用于查询数据
        DB_DSBUF_STRU pstDsBuf;
        ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 10, g_recLen1 * 10);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // CDB插入数据
        for (uint32_t j = 0; j < sizeof(optRec) / sizeof(uint32_t); j++) {
            if (g_testcaseDebugModeVal) {
                AW_FUN_Log(LOG_INFO, "insert optRec[%d]: %d", j, optRec[j]);
            }
            ret = TestTPC_InsertRec(
                TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, optRec[j], optRec[j]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            pstDsBuf.usRecNum = 10;
            ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, optRec[j], &pstDsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBuf.usRecNum);
            ret = TestCheckDsBuffVal(
                g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, optRec[j], optRec[j]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // CDB更新数据
        for (uint32_t j = 0; j < sizeof(optRec) / sizeof(uint32_t) / 2; j++) {
            if (g_testcaseDebugModeVal) {
                AW_FUN_Log(LOG_INFO, "update optRec[%d]: %d", j, optRec[j]);
            }
            ret = TestTPC_UpdateRec(
                TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, optRec[j], optRec[j], 100);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            pstDsBuf.usRecNum = 10;
            ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, optRec[j], &pstDsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
            pstDsBuf.usRecNum = 10;
            ret = TestTPC_SelectRec(
                TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, optRec[j] + 100, &pstDsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBuf.usRecNum);
            ret = TestCheckDsBuffVal(
                g_pstRelDef1.pstFldLst, g_pstRelDef1.ulNCols, g_recLen1, &pstDsBuf, optRec[j] + 100, optRec[j] + 100);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // CDB删除数据
        for (uint32_t j = sizeof(optRec) / sizeof(uint32_t) / 2 + 1; j < sizeof(optRec) / sizeof(uint32_t); j++) {
            if (g_testcaseDebugModeVal) {
                AW_FUN_Log(LOG_INFO, "delete optRec[%d]: %d", j, optRec[j]);
            }
            ret = TestTPC_DeleteRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, optRec[j], optRec[j]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            pstDsBuf.usRecNum = 10;
            ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, g_VistTool_TestDbId, g_pusRelId1, g_recLen1, optRec[j], &pstDsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
        }

        TestFreeDsBuf(&pstDsBuf);

        // 导出成文件
        char expFilePath[1024] = { 0 };
        (void)sprintf_s(expFilePath, sizeof(expFilePath), "./V1ExpData_%02u.txt", i);
        ret = TPC_BkpPhy(g_VistTool_TestDbId, (uint8_t *)expFilePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // 删表
        TestFreeTblStructDef(&g_pstRelDef1);
        ret = TPC_DropTbl(g_VistTool_TestDbId, g_pusRelId1, TPC_NOWAIT);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        // 删DB
        ret = TPC_CloseDB(g_VistTool_TestDbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_DropDB((VOS_UINT8 *)g_VistTool_DbName, 0);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 对比文件
    char expFilePath[1024] = { 0 };
    char *fileContentBuff[runCnt];
    uint32_t fileSize[runCnt];
    char xxdCmd[1024] = { 0 };
    for (uint32_t i = 0; i < runCnt; i++) {
        (void)sprintf_s(expFilePath, sizeof(expFilePath), "./V1ExpData_%02u.txt", i);
        if (g_testcaseDebugModeVal) {
            (void)sprintf_s(xxdCmd, sizeof(xxdCmd), "xxd %s > %02u.hex", expFilePath, i);
            system(xxdCmd);
        }
        ret = TestReadFileToBuf(expFilePath, &fileContentBuff[i], &fileSize[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i > 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(fileSize[i], fileSize[i - 1]);
            ret = memcmp(fileContentBuff[i], fileContentBuff[i - 1], fileSize[i]);
            if (ret != 0) {
                AW_FUN_Log(LOG_INFO, "%s, ret:%d", expFilePath, ret);
            }
            V1_AW_MACRO_ASSERT_EQ_INT(0, ret);
            TEST_V1_FREE(fileContentBuff[i - 1]);
        }
    }
    TEST_V1_FREE(fileContentBuff[runCnt - 1]);

    char cmd[1024] = { 0 };
    for (uint32_t i = 0; i < runCnt; i++) {
        (void)sprintf_s(cmd, sizeof(cmd), "rm -f ./V1ExpData_%02u.txt", i);
        system(cmd);
    }

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
