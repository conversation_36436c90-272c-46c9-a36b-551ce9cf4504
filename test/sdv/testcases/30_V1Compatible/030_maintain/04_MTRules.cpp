/*
================================ 二、规格约束 ================================
1 普通DB
001 普通DB，DB_BkpPhy导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir指定路径，预期导入成功
002 普通DB，DB_BkpPhyEx导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir不指定路径，预期导入失败
003 普通DB，DB_BkpPhyWithDataConvHook导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir指定路径，预期导入成功
004 普通DB，DB_PhyBkp2导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir不指定路径，预期导入成功
005 普通DB，DB_BkpPhy导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB一致，预期导入成功
006 普通DB，DB_BkpPhyEx导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB不一致，预期导入成功
007 普通DB，DB_BkpPhyWithDataConvHook导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB一致，预期导入成功
008 普通DB，DB_PhyBkp2导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB不一致，预期导入成功
009 普通DB，DB_BkpPhy导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir指定路径，预期导入成功
010 普通DB，DB_BkpPhyEx导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir不指定路径，预期导入失败
011 普通DB，DB_BkpPhyWithDataConvHook导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir指定路径，预期导入成功
012 普通DB，DB_PhyBkp2导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir不指定路径，预期导入成功

2 持久化DB
013 持久化DB，DB_BkpPhy导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir指定路径，预期导入成功
014 持久化DB，DB_BkpPhyEx导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir不指定路径，预期导入失败
015 持久化DB，DB_BkpPhyWithDataConvHook导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir指定路径，预期导入成功
016 持久化DB，DB_PhyBkp2导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir不指定路径，预期导入成功
017 持久化DB，DB_BkpPhy导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB一致，预期导入成功
018 持久化DB，DB_BkpPhyEx导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB不一致，预期导入成功
019 持久化DB，DB_BkpPhyWithDataConvHook导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB一致，预期导入成功
020 持久化DB，DB_PhyBkp2导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB不一致，预期导入成功
021 持久化DB，DB_BkpPhy导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir指定路径，预期导入成功
022 持久化DB，DB_BkpPhyEx导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir不指定路径，预期导入失败
023 持久化DB，DB_BkpPhyWithDataConvHook导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir指定路径，预期导入成功
024 持久化DB，DB_PhyBkp2导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir不指定路径，预期导入成功

3 维护接口
025 DB_BkpPhy导出后调用DB_CheckDataFile校验，预期校验失败
026 DB_BkpPhyEx导出后调用DB_CheckDataFile校验，预期校验失败
027 DB_BkpPhyWithDataConvHook导出后调用DB_CheckDataFile校验，预期校验失败
028 DB_PhyBkp2接口AddChecksum=false导出后调用DB_CheckDataFile校验，预期校验失败
029 DB_PhyBkp2接口AddChecksum=true导出后调用DB_CheckDataFile校验，预期校验成功
*/
#include "maintain.h"

class MaintainRules : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MaintainRules::SetUpTestCase()
{}

void MaintainRules::TearDownTestCase()
{}

void MaintainRules::SetUp()
{
    // 初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.txt");
    system("rm -rf ./perFilePath/*.txt");
    system("rm -rf ./filePath/output");
    AW_CHECK_LOG_BEGIN();
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDB((VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void MaintainRules::TearDown()
{
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.txt");
    system("rm -rf ./perFilePath/*.txt");
    system("rm -rf ./filePath/output");
    // CLoseDB
    int ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_perDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    AW_CHECK_LOG_END();
}

// 001 普通DB，DB_BkpPhy导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 普通DB，DB_BkpPhyEx导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir不指定路径，预期导入失败
TEST_F(MaintainRules, V1Com_030_Rules_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 普通DB，DB_BkpPhyWithDataConvHook导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 普通DB，DB_PhyBkp2导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir不指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 普通DB，DB_BkpPhy导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB一致，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB一致 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 普通DB，DB_BkpPhyEx导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB不一致，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB不一致 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_dbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)g_perDbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 普通DB，DB_BkpPhyWithDataConvHook导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB一致，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName2[20] = "dbName2";
    ret = DB_CreateDB((VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB一致 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 普通DB，DB_PhyBkp2导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB不一致，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName2[20] = "dbName2";
    ret = DB_CreateDB((VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_dbDir, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB不一致 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 普通DB，DB_BkpPhy导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName2[20] = "dbName2";
    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir指定路径 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 普通DB，DB_BkpPhyEx导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir不指定路径，预期导入失败
TEST_F(MaintainRules, V1Com_030_Rules_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 普通DB，DB_BkpPhyWithDataConvHook导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 普通DB，DB_PhyBkp2导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir不指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 持久化DB，DB_BkpPhy导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_perDbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 持久化DB，DB_BkpPhyEx导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir不指定路径，预期导入失败
TEST_F(MaintainRules, V1Com_030_Rules_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入持久化DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 持久化DB，DB_BkpPhyWithDataConvHook导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 持久化DB，DB_PhyBkp2导出，DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir不指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 持久化DB，DB_BkpPhy导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB一致，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_perDbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建持久化DB
    char dbName[20] = "perDB";
    ret = DB_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB一致 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 持久化DB，DB_BkpPhyEx导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB不一致，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建持久化DB
    char dbName[20] = "perDB";
    ret = DB_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir和create的DB不一致 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_dbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)g_dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_dbDir, (VOS_UINT8 *)dbName, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 持久化DB，DB_BkpPhyWithDataConvHook导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB一致，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName2[20] = "dbName2";
    ret = DB_CreateDB((VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB一致 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 持久化DB，DB_PhyBkp2导出，DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB不一致，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName2[20] = "dbName2";
    ret = DB_CreateDB((VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_dbDir, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir和create的DB不一致 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 持久化DB，DB_BkpPhy导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_perDbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName2[20] = "dbName2";
    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir指定路径 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 持久化DB，DB_BkpPhyEx导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir不指定路径，预期导入失败
TEST_F(MaintainRules, V1Com_030_Rules_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入持久化DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 持久化DB，DB_BkpPhyWithDataConvHook导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 持久化DB，DB_PhyBkp2导出，DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir不指定路径，预期导入成功
TEST_F(MaintainRules, V1Com_030_Rules_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_REPLACE导入普通DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 DB_BkpPhy导出后调用DB_CheckDataFile校验，预期校验失败
TEST_F(MaintainRules, V1Com_030_Rules_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 DB_BkpPhyEx导出后调用DB_CheckDataFile校验，预期校验失败
TEST_F(MaintainRules, V1Com_030_Rules_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 DB_BkpPhyWithDataConvHook导出后调用DB_CheckDataFile校验，预期校验失败
TEST_F(MaintainRules, V1Com_030_Rules_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 DB_PhyBkp2接口AddChecksum=false导出后调用DB_CheckDataFile校验，预期校验失败
TEST_F(MaintainRules, V1Com_030_Rules_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 DB_PhyBkp2接口AddChecksum=true导出后调用DB_CheckDataFile校验，预期校验成功
TEST_F(MaintainRules, V1Com_030_Rules_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 DB不存在，五个导入接口DB_RESTORETYPE_DISCARD导入普通DB，pucDestDir不指定路径 ========= */
    // 1 DB_Restore导入
    char dbName2[20] = "dbName2";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_DISCARD, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启DB校验数据
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DropDB
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
