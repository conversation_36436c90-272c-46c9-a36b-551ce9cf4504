/*
1 普通DB导入导出   -- 只有DB_BkpPhyWithDataConvHook和DB_BkpPhy2会处理自定义数据类型
001 同一个普通DB，DB_BkpPhy导出，五个导入接口导入，预期校验数据等成功
002 同一个普通DB，DB_BkpPhyEx导出大端数据，五个导入接口导入，预期校验数据等成功
003 同一个普通DB，DB_BkpPhyEx导出小端数据，五个导入接口导入，预期校验数据等成功
004 同一个普通DB，DB_BkpPhyEx导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
005 同一个普通DB，DB_BkpPhyWithDataConvHook导出大端数据，五个导入接口导入，预期校验数据等成功
006 同一个普通DB，DB_BkpPhyWithDataConvHook导出小端数据，五个导入接口导入，预期校验数据等成功
007 同一个普通DB，DB_BkpPhyWithDataConvHook导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
008 同一个普通DB，check为false，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
009 同一个普通DB，check为false，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
010 同一个普通DB，check为false，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
011 同一个普通DB，check为true，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
012 同一个普通DB，check为true，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
013 同一个普通DB，check为true，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
014 不同普通DB，DB_BkpPhy导出，五个导入接口导入，预期校验数据等成功
015 不同普通DB，DB_BkpPhyEx导出大端数据，五个导入接口导入，预期校验数据等成功
016 不同普通DB，DB_BkpPhyEx导出小端数据，五个导入接口导入，预期校验数据等成功
017 不同普通DB，DB_BkpPhyEx导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
018 不同普通DB，DB_BkpPhyWithDataConvHook导出大端数据，五个导入接口导入，预期校验数据等成功
019 不同普通DB，DB_BkpPhyWithDataConvHook导出小端数据，五个导入接口导入，预期校验数据等成功
020 不同普通DB，DB_BkpPhyWithDataConvHook导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
021 不同普通DB，check为false，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
022 不同普通DB，check为false，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
023 不同普通DB，check为false，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
024 不同普通DB，check为true，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
025 不同普通DB，check为true，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
026 不同普通DB，check为true，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功

2 持久化DB导入导出
027 同一个持久化DB，DB_BkpPhy导出，五个导入接口导入，预期校验数据等成功
028 同一个持久化DB，DB_BkpPhyEx导出大端数据，五个导入接口导入，预期校验数据等成功
029 同一个持久化DB，DB_BkpPhyEx导出小端数据，五个导入接口导入，预期校验数据等成功
030 同一个持久化DB，DB_BkpPhyEx导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
031 同一个持久化DB，DB_BkpPhyWithDataConvHook导出大端数据，五个导入接口导入，预期校验数据等成功
032 同一个持久化DB，DB_BkpPhyWithDataConvHook导出小端数据，五个导入接口导入，预期校验数据等成功
033 同一个持久化DB，DB_BkpPhyWithDataConvHook导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
034 同一个持久化DB，check为false，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
035 同一个持久化DB，check为false，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
036 同一个持久化DB，check为false，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
037 同一个持久化DB，check为true，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
038 同一个持久化DB，check为true，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
039 同一个持久化DB，check为true，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
040 不同持久化DB，DB_BkpPhy导出，五个导入接口导入，预期校验数据等成功
041 不同持久化DB，DB_BkpPhyEx导出大端数据，五个导入接口导入，预期校验数据等成功
042 不同持久化DB，DB_BkpPhyEx导出小端数据，五个导入接口导入，预期校验数据等成功
043 不同持久化DB，DB_BkpPhyEx导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
044 不同持久化DB，DB_BkpPhyWithDataConvHook导出大端数据，五个导入接口导入，预期校验数据等成功
045 不同持久化DB，DB_BkpPhyWithDataConvHook导出小端数据，五个导入接口导入，预期校验数据等成功
046 不同持久化DB，DB_BkpPhyWithDataConvHook导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
047 不同持久化DB，check为false，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
048 不同持久化DB，check为false，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
049 不同持久化DB，check为false，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
050 不同持久化DB，check为true，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
051 不同持久化DB，check为true，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
052 不同持久化DB，check为true，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功

3 持久化DB和普通DB相互导入导出
053 普通DB，DB_BkpPhy导出，五个导入接口导入持久化DB，预期校验数据等成功，close再次open校验数据
054 持久化DB，DB_BkpPhy导出，五个导入接口导入普通DB，预期校验数据等成功，close再次open校验数据

4 元数据接口
055 导出前查询，导出后查询进度，reset后再查询，预期导出前进度为init，导出后进度为complete，重置后进度为init

5 维护接口
056 DB_RegCheckSumFeature注册后，DB_CreateDB创建DB，建表写数据后，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期失败
057 DB_CreateDB创建DB，建表写数据后，导出，注册后调用DB_Restore，再调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期失败
058 DB_RegCheckSumFeature注册后，DB_CreateDB2创建DB，建表写数据后，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
059 DB_CreateDB创建DB，建表写数据后，导出，注册后调用DB_RestoreDB2，再调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
060 DB_CreateDB创建DB，建表写数据后，导出，注册后调用DB_RestoreDB2，进行增删改查，再调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
061 DB_RegCheckSumFeature注册后，DB_CreateDB2创建DB，建表写数据后，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功，导出后调用DB_RestoreDB2导入，再调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
062 DB_RegCheckSumFeature注册后DB_RestoreDB2导入V1不带checkSum的文件，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
063 DB_RegCheckSumFeature注册后DB_RestoreDB2导入V1带checkSum的文件，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
*/

#include "maintain.h"

class MaintainFunc : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MaintainFunc::SetUpTestCase()
{}

void MaintainFunc::TearDownTestCase()
{}

void MaintainFunc::SetUp()
{
    // 初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.txt");
    system("rm -rf ./perFilePath/*.txt");
    system("rm -rf ./filePath/output");
    AW_CHECK_LOG_BEGIN();
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName2, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDB((VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CreateDB((VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName2, &g_perDbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void MaintainFunc::TearDown()
{
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.txt");
    system("rm -rf ./perFilePath/*.txt");
    system("rm -rf ./filePath/output");
    int ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_perDbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_perDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_perDbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    AW_CHECK_LOG_END();
}

// 001 同一个普通DB，DB_BkpPhy导出，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 同一个普通DB，DB_BkpPhyEx导出大端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 同一个普通DB，DB_BkpPhyEx导出小端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 同一个普通DB，DB_BkpPhyEx导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 同一个普通DB，DB_BkpPhyWithDataConvHook导出大端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 同一个普通DB，DB_BkpPhyWithDataConvHook导出小端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 同一个普通DB，DB_BkpPhyWithDataConvHook导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 同一个普通DB，check为false，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 同一个普通DB，check为false，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 同一个普通DB，check为false，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 同一个普通DB，check为true，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 同一个普通DB，check为true，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 同一个普通DB，check为true，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName, g_dbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 不同普通DB，DB_BkpPhy导出，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 不同普通DB，DB_BkpPhyEx导出大端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 不同普通DB，DB_BkpPhyEx导出小端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 不同普通DB，DB_BkpPhyEx导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_017)

{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 不同普通DB，DB_BkpPhyWithDataConvHook导出大端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 不同普通DB，DB_BkpPhyWithDataConvHook导出小端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 不同普通DB，DB_BkpPhyWithDataConvHook导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 不同普通DB，check为false，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 不同普通DB，check为false，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 不同普通DB，check为false，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 不同普通DB，check为true，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 不同普通DB，check为true，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 不同普通DB，check为true，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 同一个持久化DB，DB_BkpPhy导出，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_perDbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 同一个持久化DB，DB_BkpPhyEx导出大端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 同一个持久化DB，DB_BkpPhyEx导出小端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 同一个持久化DB，DB_BkpPhyEx导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出DEFAULT数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 同一个持久化DB，DB_BkpPhyWithDataConvHook导出大端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出大端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032 同一个持久化DB，DB_BkpPhyWithDataConvHook导出小端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出小端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033 同一个持久化DB，DB_BkpPhyWithDataConvHook导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出DEFAULT数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034 同一个持久化DB，check为false，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出大端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035 同一个持久化DB，check为false，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出小端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036 同一个持久化DB，check为false，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出DEFAULT数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037 同一个持久化DB，check为true，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出大端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038 同一个持久化DB，check为true，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出小端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039 同一个持久化DB，check为true，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出DEFAULT数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040 不同持久化DB，DB_BkpPhy导出，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_perDbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041 不同持久化DB，DB_BkpPhyEx导出大端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042 不同持久化DB，DB_BkpPhyEx导出小端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043 不同持久化DB，DB_BkpPhyEx导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出DEFAULT数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    ret = DB_BkpPhyEx(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044 不同持久化DB，DB_BkpPhyWithDataConvHook导出大端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出大端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045 不同持久化DB，DB_BkpPhyWithDataConvHook导出小端数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出小端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046 不同持久化DB，DB_BkpPhyWithDataConvHook导出DEFAULT数据，五个导入接口导入，预期校验数据等成功
TEST_F(MaintainFunc, V1Com_030_Func_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyWithDataConvHook导出DEFAULT数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_BkpPhyWithDataConvHook(g_perDbId, (uint8_t *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047 不同持久化DB，check为false，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    
    /* ========= step1 DB_BkpPhy2导出大端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048 不同持久化DB，check为false，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出小端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049 不同持久化DB，check为false，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验失败
TEST_F(MaintainFunc, V1Com_030_Func_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出DEFAULT数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("30_V1Compatible/030_maintain/filePath/export.txt has no checksum msg.", false));

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050 不同持久化DB，check为true，DB_BkpPhy2导出大端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出大端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, true, true, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true, false);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051 不同持久化DB，check为true，DB_BkpPhy2导出小端数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出小端数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052 不同持久化DB，check为true，DB_BkpPhy2导出DEFAULT数据，五个导入接口导入，预期校验数据等成功，checkDataFile校验成功
TEST_F(MaintainFunc, V1Com_030_Func_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy2导出DEFAULT数据 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_perDbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName2, g_perDbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053 普通DB，DB_BkpPhy导出，五个导入接口导入持久化DB，预期校验数据等成功，close再次open校验数据
TEST_F(MaintainFunc, V1Com_030_Func_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_dbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_perDbName, g_perDbId, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054 持久化DB，DB_BkpPhy导出，五个导入接口导入普通DB，预期校验数据等成功，close再次open校验数据
TEST_F(MaintainFunc, V1Com_030_Func_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_perDbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_perDbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_perDbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_perDbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_perDbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_perDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(g_perDbId, usRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数以及数据检验
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 DB_RestoreDB2导入
    // 注册
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 DB_RestoreEx导入
    ret = DB_RestoreEx(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 4 DB_RestoreWithDataConvHook导入
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = DB_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 5 DB_RestoreWithDataConvHookEx导入
    ret = DB_RestoreWithDataConvHookEx((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, (VOS_UINT8 *)g_perDbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, pfnGetTblConvHook, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验DB描述信息和表个数
    ret = GetDBDescAndTblInfo(g_dbName2, g_dbId2, setDescInfo, 1, usRelId, stRelDef.pstFldLst, insertNum, false, false, true);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055 导出前查询，导出后查询进度，reset后再查询，预期导出前进度为init，导出后进度为complete，重置后进度为init
TEST_F(MaintainFunc, V1Com_030_Func_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 设置DB描述信息
    char setDescInfo[] = "This is a normal DataBase with normal table and temp table including Custom dataType.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 临时表
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef2;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_temp_001.json", &usRelId2, &stRelDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen2;
    ret = TestDBGetTblRecLen(g_dbId, usRelId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(tblRecLen2);
    (void)memset_s(recBuf2, tblRecLen2, 0x00, tblRecLen2);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, stRelDef2.pstFldLst, i, tblRecLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = (VOS_UINT16)tblRecLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen2, .ulActLen = tblRecLen2, .pucData = recBuf2}};
        ret = DB_InsertRec(g_dbId, usRelId2, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId2, stRelDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出前查询进度，预期为init
    DB_BKPSTATE_ENUM enBkpState;
    VOS_UINT32 ulProgress = 0;
    ret = DB_GetBkpProgress(g_dbId, &enBkpState, &ulProgress);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulProgress);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_BKPSTATE_INIT, enBkpState);

    /* ========= step1 DB_BkpPhy导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出后查询导出进度，预期为complete
    ret = DB_GetBkpProgress(g_dbId, &enBkpState, &ulProgress);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(100, ulProgress);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_BKPSTATE_COMPLETE, enBkpState);

    // 重置后再次查询导出进度，预期为init
    ret = DB_ResetBkpProgress(g_dbId);    
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_GetBkpProgress(g_dbId, &enBkpState, &ulProgress);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulProgress);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_BKPSTATE_INIT, enBkpState);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&stRelDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056 DB_RegCheckSumFeature注册后，DB_CreateDB创建DB，建表写数据后，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期失败
TEST_F(MaintainFunc, V1Com_030_Func_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 注册、创建DB
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName[20] = "ChecksumDB";
    VOS_UINT32 dbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 有注册，但未调用DB_CreateDB2创建DB
    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;

    ret = DB_GetTblCount(dbId, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(dbId, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_METADATA_CHECKSUM_FAILED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB not register checksum feature.", false));

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 1;
    ausRelList[0] = usRelId;
    ret = DB_CheckDBRel(dbId, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB not register checksum feature.", false));

    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(pusRelList);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057 DB_CreateDB创建DB，建表写数据后，导出，注册后调用DB_Restore，再调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期失败
TEST_F(MaintainFunc, V1Com_030_Func_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char dbName[20] = "ChecksumDB";
    VOS_UINT32 dbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 注册、导入
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 有注册，但未调用DB_RestoreDB2创建DB
    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;

    ret = DB_GetTblCount(g_perDbId2, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(g_perDbId2, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_METADATA_CHECKSUM_FAILED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB not register checksum feature.", false));

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 1;
    ausRelList[0] = usRelId;
    ret = DB_CheckDBRel(g_perDbId2, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB not register checksum feature.", false));

    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(pusRelList);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058 DB_RegCheckSumFeature注册后，DB_CreateDB2创建DB，建表写数据后，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
TEST_F(MaintainFunc, V1Com_030_Func_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 注册、创建DB
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName[20] = "ChecksumDB";
    VOS_UINT32 dbId = 0;
    ret = DB_CreateDB2((VOS_UINT8 *)dbName, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 注册，调用DB_CreateDB2创建DB
    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;

    ret = DB_GetTblCount(dbId, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(dbId, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulTblCount);

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 1;
    ausRelList[0] = usRelId;
    ret = DB_CheckDBRel(dbId, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);

    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(pusRelList);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059 DB_CreateDB创建DB，建表写数据后，导出，注册后调用DB_RestoreDB2，再调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
TEST_F(MaintainFunc, V1Com_030_Func_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char dbName[20] = "ChecksumDB";
    VOS_UINT32 dbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    
    // 注册、导入
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 注册，调用DB_RestoreDB2创建DB
    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;

    ret = DB_GetTblCount(g_perDbId2, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(g_perDbId2, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulTblCount);

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 1;
    ausRelList[0] = usRelId;
    ret = DB_CheckDBRel(g_perDbId2, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);

    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(pusRelList);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060 DB_CreateDB创建DB，建表写数据后，导出，注册后调用DB_RestoreDB2，进行增删改查，再调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
TEST_F(MaintainFunc, V1Com_030_Func_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char dbName[20] = "ChecksumDB";
    VOS_UINT32 dbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 注册、导入
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导入后进行写操作
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_perDbId2, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    
    // 删除一半数据
    uint32_t cnt = 0;
    Delete(g_perDbId2, usRelId, insertNum, &cnt);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, cnt);

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // update数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        UpdateAllType(g_perDbId2, usRelId, recBuf, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId2, usRelId, stRelDef.pstFldLst, i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 注册，调用DB_RestoreDB2创建DB
    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;

    ret = DB_GetTblCount(g_perDbId2, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(g_perDbId2, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulTblCount);

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 1;
    ausRelList[0] = usRelId;
    ret = DB_CheckDBRel(g_perDbId2, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);

    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(pusRelList);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061 DB_RegCheckSumFeature注册后，DB_CreateDB2创建DB，建表写数据后，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功，导出后调用DB_RestoreDB2导入，再调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
TEST_F(MaintainFunc, V1Com_030_Func_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 注册、创建DB
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName[20] = "ChecksumDB";
    VOS_UINT32 dbId = 0;
    ret = DB_CreateDB2((VOS_UINT8 *)dbName, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 注册，调用DB_CreateDB2创建DB
    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;

    ret = DB_GetTblCount(dbId, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(dbId, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulTblCount);

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 1;
    ausRelList[0] = usRelId;
    ret = DB_CheckDBRel(dbId, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 注册、导入
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_GetTblCount(g_perDbId2, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表是否损坏
    ret = DB_CheckDBAllRelDes(g_perDbId2, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulTblCount);

    ret = DB_CheckDBRel(g_perDbId2, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_perDbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(pusRelList);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062 DB_RegCheckSumFeature注册后DB_RestoreDB2导入V1不带checkSum的文件，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
TEST_F(MaintainFunc, V1Com_030_Func_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 注册、导入
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
   
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 不带checkSum的导入
    char filePath[64] = "./schemaFile/V1_NonCHeckSum.DB2";
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;
    ret = DB_GetTblCount(g_perDbId2, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, ulTblCount);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(g_perDbId2, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulTblCount);

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 2;
    ausRelList[0] = 0;
    ret = DB_CheckDBRel(g_perDbId2, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(pusRelList);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063 DB_RegCheckSumFeature注册后DB_RestoreDB2导入V1带checkSum的文件，调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
TEST_F(MaintainFunc, V1Com_030_Func_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 注册、导入
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
   
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 带checkSum的导入
    char filePath[64] = "./schemaFile/V1_CHeckSum.DB2";
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名和表个数
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = DB_GetTblNamesAndCount(g_perDbId2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);

    // 获取表ID
    uint16_t usRelId;
    ret = DB_GetTblId(g_perDbId2, (VOS_UINT8*)resultBuff[0], &usRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检测表是否损坏
    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;
    ret = DB_GetTblCount(g_perDbId2, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(g_perDbId2, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulTblCount);

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 1;
    ausRelList[0] = usRelId;
    ret = DB_CheckDBRel(g_perDbId2, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);

    // 查看视图
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)"./exportFile.txt";
    viewArgs.ulDbId = g_perDbId2;
    VOS_UINT8 *pucResult = NULL;

    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeSysviewResult(&pucResult);
    
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(pusRelList);
    TEST_V1_FREE(resultBuff[0]);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064 DB_RegCheckSumFeature注册后DB_RestoreDB2导入V1带checkSum的文件，增删改查后调用DB_CheckDBRel和DB_CheckDBAllRelDes，预期成功
TEST_F(MaintainFunc, V1Com_030_Func_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 注册、导入
    DB_INST_CONFIG_STRU stCfg = {0};
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
   
    ret = DB_RegDbRestoreConfig(true, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 带checkSum的导入
    char filePath[64] = "./schemaFile/V1_CHeckSum.DB2";
    ret = DB_RestoreDB2((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_perDbName2, (VOS_UINT8 *)g_perDbDir, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表名和表个数
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = DB_GetTblNamesAndCount(g_perDbId2, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);

    // 获取表ID
    uint16_t usRelId;
    ret = DB_GetTblId(g_perDbId2, (VOS_UINT8*)resultBuff[0], &usRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写数据
    TestDbMetaOneRecT recBuf = {0};
    DB_DSBUF_STRU stDsBuf = {0};
    stDsBuf.StdBuf.ulActLen = sizeof(TestDbMetaOneRecT);
    stDsBuf.StdBuf.pucData = (VOS_UINT8 *)&recBuf;
    for (VOS_UINT32 j = 101; j <= 200; j++) {
        TestAllTypeSetOneRecord(&recBuf, j);
        ret = DB_InsertRec(g_perDbId2, usRelId, &stDsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 检测表是否损坏
    VOS_UINT16 *pusRelList = NULL;
    VOS_UINT32 ulTblCount = 0;
    ret = DB_GetTblCount(g_perDbId2, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    pusRelList = (VOS_UINT16 *)TEST_V1_MALLOC(ulTblCount * sizeof(VOS_UINT16));
    ret = DB_CheckDBAllRelDes(g_perDbId2, pusRelList, &ulTblCount);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulTblCount);

    VOS_UINT16 ausRelList[5] = {0};
    VOS_UINT16 usFailRelId = 0xFFFF;
    VOS_UINT32 ulRelCount = 1;
    ausRelList[0] = usRelId;
    ret = DB_CheckDBRel(g_perDbId2, ausRelList, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);

    // 查看视图
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)"./exportFile.txt";
    viewArgs.ulDbId = g_perDbId2;
    VOS_UINT8 *pucResult = NULL;

    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeSysviewResult(&pucResult);
    
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(pusRelList);
    TEST_V1_FREE(resultBuff[0]);
    AW_FUN_Log(LOG_STEP, "test end.");
}
