/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 Description  : 支持一阶段DB的 metadata 接口兼容
 Author       : herui h60035902
 Modification :
 create       : 2025/03/03
**************************************************************************** */
#include "QueryMetadata.h"

int ret = 0;
class MetadataforDB_Test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestDB_Init();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        ret = TestDB_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MetadataforDB_Test::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void MetadataforDB_Test::TearDown()
{
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
class MetadataforTPC_Test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestTPC_Init();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        ret = TestTPC_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};
void MetadataforTPC_Test::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void MetadataforTPC_Test::TearDown()
{
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
class MetadataforMax_Test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=65535\"");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
    };
    static void TearDownTestCase()
    {
        ret = TestDB_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MetadataforMax_Test::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void MetadataforMax_Test::TearDown()
{
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
// 047.表中不存在数据调用DBDDL_GetRelActRec进行查询
TEST_F(MetadataforDB_Test, V1Com_028_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DBDDL_GetRelActRec(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ret);
}
// 048.对表中数据更新删除后调用DBDDL_GetRelActRec进行查询
TEST_F(MetadataforDB_Test, V1Com_028_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 0);
    // 删除数据
    Delete(testRelId1, 11, false, 0);
    ret = DBDDL_GetRelActRec(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(9, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 049.开启CDB后调用TPC接口进行DML操作，CDB结束前和CDB提交后调用DBDDL_GetRelActRec进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 0, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 0, g_testDbId, cdbID);
    // 查询实际数据量
    ret = DBDDL_GetRelActRec(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(10, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询实际数据量
    ret = DBDDL_GetRelActRec(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(9, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 050.开启CDB后调用TPC接口进行DML操作，CDB回滚后调用DBDDL_GetRelActRec进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 0, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 0, g_testDbId, cdbID);
    // 查询实际数据量
    ret = DBDDL_GetRelActRec(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(10, ret);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询实际数据量
    ret = DBDDL_GetRelActRec(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(10, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 051.建表成功后调用DBDDL_GetRelMaxRecNum进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表的最大记录数
    ret = DBDDL_GetRelMaxRecNum(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(128000, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 052.修改最大记录数后调用DBDDL_GetRelMaxRecNum进行查询
TEST_F(MetadataforDB_Test, V1Com_028_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 修改最大记录数
    ret = DB_SetMaxRecNumOfTable(g_testDbId, testRelId1, 0, 0, 128008);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表的最大记录数
    ret = DBDDL_GetRelMaxRecNum(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(128008, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 053.建表包含大对象成功后调用DBDDL_GetRelRecLen进行查询
TEST_F(MetadataforDB_Test, V1Com_028_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表的记录长度
    ret = DBDDL_GetRelRecLen(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(32484, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 054.建表包含全类型字段(含自定义)成功后调用DBDDL_GetRelRecLen进行查询
TEST_F(MetadataforDB_Test, V1Com_028_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表的记录长度
    ret = DBDDL_GetRelRecLen(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(32492, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 055.建表时，DBT_DOUBLE、DBT_UINT8等数据类型填写错误的size后调用DBS_GetRelRecLen进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1_1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表的记录长度
    VOS_UINT32 RecLen = 0;
    ret = DBS_GetRelRecLen(g_testDbId, testRelId1, &RecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(32484, RecLen);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 056.DBINIT_GetAllFieldInfo中ulBufSize的值小于pstAllFieldInfo指针大小
TEST_F(MetadataforDB_Test, V1Com_028_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 6 * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = {0};
    pstAllFieldInfo = (DB_FIELD_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DBINIT_GetAllFieldInfo(g_testDbId, testRelId1, pstAllFieldInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllFieldInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 057.DBINIT_GetAllFieldInfo中ulBufSize的值大于pstAllFieldInfo指针大小
TEST_F(MetadataforDB_Test, V1Com_028_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 8 * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = {0};
    pstAllFieldInfo = (DB_FIELD_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DBINIT_GetAllFieldInfo(g_testDbId, testRelId1, pstAllFieldInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验字段的详细信息是否正确
    ret = memcmp(pstAllFieldInfo[0].aucFieldName, "F0", strlen("F0"));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].ulDefaultValue, 0xffffffff);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].usFldID, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].ucDataType, DBT_UINT32);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].usDefLen, 4);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].usStoredLen, 4);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllFieldInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 058.建表包含全类型字段(含自定义)成功后调用 DBINIT_GetAllFieldInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 28 * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = {0};
    pstAllFieldInfo = (DB_FIELD_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DBINIT_GetAllFieldInfo(g_testDbId, testRelId1, pstAllFieldInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验字段的详细信息是否正确
    ret = strcmp((char *)pstAllFieldInfo[27].aucFieldName, "F27");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[27].ulDefaultValue, 0xffffffff);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[27].usFldID, 27);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[27].ucDataType, 32);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[27].usDefLen, 8);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[27].usStoredLen, 8);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllFieldInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 059.建表包含大对象成功后调用DB_GetTblColInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 28 * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = {0};
    pstAllFieldInfo = (DB_FIELD_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DB_GetTblColInfo(g_testDbId, testRelId1, pstAllFieldInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验字段的详细信息是否正确
    ret = strcmp((char *)pstAllFieldInfo[7].aucFieldName, "F7");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[7].ulDefaultValue, 0xffffffff);
    V1_AW_MACRO_EXPECT_EQ_INT(7, pstAllFieldInfo[7].usFldID);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[7].ucDataType, DBT_STRING);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[7].usDefLen, 16165);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[7].usStoredLen, 16166);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllFieldInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 060.DB_GetTblColInfo中ulBufSize的值等于pstAllFieldInfo指针大小
TEST_F(MetadataforDB_Test, V1Com_028_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 27 * sizeof(DB_FIELD_INFO);
    DB_FIELD_INFO *pstAllFieldInfo = {0};
    pstAllFieldInfo = (DB_FIELD_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DB_GetTblColInfo(g_testDbId, testRelId1, pstAllFieldInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验字段的详细信息是否正确
    ret = strcmp((char *)pstAllFieldInfo[0].aucFieldName, "F0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].ulDefaultValue, 0xffffffff);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].usFldID, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].ucDataType, DBT_BCD);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].usDefLen, 4);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllFieldInfo[0].usStoredLen, 2);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllFieldInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 061.DBINIT_GetAllIndexInfo中ulBufSize的值小于pstAllIndexInfo指针大小
TEST_F(MetadataforDB_Test, V1Com_028_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 1 * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = {0};
    pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DBINIT_GetAllIndexInfo(g_testDbId, testRelId1, pstAllIndexInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllIndexInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 062.DBINIT_GetAllIndexInfo中ulBufSize的值大于pstAllIndexInfo指针大小
TEST_F(MetadataforDB_Test, V1Com_028_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 3 * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = {0};
    pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DBINIT_GetAllIndexInfo(g_testDbId, testRelId1, pstAllIndexInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验索引的详细信息是否正确
    ret = strcmp((char *)pstAllIndexInfo[0].aucIndexName, "index");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ucType, DBDDL_INDEXTYPE_TTREE);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].usNumOfIncludedFlds, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ucUniqueFlag, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ausIncludedFieldIDs[0], 5);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllIndexInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 063.建表索引包含全类型字段(含自定义)成功后调用DBINIT_GetAllIndexInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 2 * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = {0};
    pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DBINIT_GetAllIndexInfo(g_testDbId, testRelId1, pstAllIndexInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验索引的详细信息是否正确
    ret = strcmp((char *)pstAllIndexInfo[1].aucIndexName, "idx2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[1].ucType, DBDDL_INDEXTYPE_TTREE);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[1].usNumOfIncludedFlds, 3);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[1].ucUniqueFlag, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[1].ausIncludedFieldIDs[0], 7);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[1].ausIncludedFieldIDs[1], 8);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[1].ausIncludedFieldIDs[2], 27);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllIndexInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 064.建表索引包含大对象成功后调用DB_GetTblIdxInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 2 * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = {0};
    pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DB_GetTblIdxInfo(g_testDbId, testRelId1, pstAllIndexInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验索引的详细信息是否正确
    ret = strcmp((char *)pstAllIndexInfo[0].aucIndexName, "idx1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ucType, DBDDL_INDEXTYPE_TTREE);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].usNumOfIncludedFlds, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ucUniqueFlag, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ausIncludedFieldIDs[0], 11);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllIndexInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 065.设置索引类型为hash索引后调用DB_GetTblIdxInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel065.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 1 * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = {0};
    pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DB_GetTblIdxInfo(g_testDbId, testRelId1, pstAllIndexInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验索引的详细信息是否正确
    ret = strcmp((char *)pstAllIndexInfo[0].aucIndexName, "index");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ucType, DBDDL_INDEXTYPE_HASH);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].usNumOfIncludedFlds, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ucUniqueFlag, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ausIncludedFieldIDs[0], 5);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllIndexInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 066.DB_GetTblIdxInfo中ulBufSize的值等于DBINIT_GetAllIndexInfo指针大小
TEST_F(MetadataforDB_Test, V1Com_028_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 2 * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = {0};
    pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DB_GetTblIdxInfo(g_testDbId, testRelId1, pstAllIndexInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验索引的详细信息是否正确
    ret = strcmp((char *)pstAllIndexInfo[0].aucIndexName, "index");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ucType, DBDDL_INDEXTYPE_TTREE);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].usNumOfIncludedFlds, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ucUniqueFlag, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(pstAllIndexInfo[0].ausIncludedFieldIDs[0], 5);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllIndexInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 067.没有索引字段调用DB_GetTblIdxInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel067.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表中所有字段的详细信息
    VOS_UINT32 ulBufSize = 1 * sizeof(DB_INDEX_INFO);
    DB_INDEX_INFO *pstAllIndexInfo = {0};
    pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(ulBufSize);
    ret = DB_GetTblIdxInfo(g_testDbId, testRelId1, pstAllIndexInfo, ulBufSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TEST_V1_FREE(pstAllIndexInfo);
    TestFreeTblStructDef(&testRelDef);
}
// 068.两个数据库名字仅大小写不一致后调用DBINIT_GetDatabaseId进行查询
TEST_F(MetadataforDB_Test, V1Com_028_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建一个数据库
    const char testDbName[DB_NAME_LEN] = "testDQL";
    ;
    uint32_t testDbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库id
    uint32_t dbId = 0;
    ret = DBINIT_GetDatabaseId((VOS_UINT8 *)testDbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(dbId, testDbId);
    ret = DBINIT_GetDatabaseId((VOS_UINT8 *)g_testDbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(dbId, g_testDbId);
    // 删除数据库
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 069.未打开数据库后调用DBINIT_GetDatabaseId进行查询
TEST_F(MetadataforDB_Test, V1Com_028_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建一个数据库
    const char testDbName[DB_NAME_LEN] = "testDQL";
    ;
    uint32_t testDbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库id
    uint32_t dbId = 0;
    ret = DBINIT_GetDatabaseId((VOS_UINT8 *)testDbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    // 删除数据库
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 070.关闭数据库后调用DBINIT_GetDatabaseId进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库id
    uint32_t dbId = 0;
    ret = DBINIT_GetDatabaseId((VOS_UINT8 *)g_testDbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 071.建立255个DB，依次调用DB_GetDBId查询DBid
TEST_F(MetadataforDB_Test, V1Com_028_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建255个数据库
    uint32_t dbId[254] = {0};
    for (int i = 0; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 依次调用DB_GetDBId查询DBid
    uint32_t testDbId = 0;
    for (int i = 0; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_GetDBId((VOS_UINT8 *)dbName, &testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(testDbId, dbId[i]);
    }
    // 删除所有数据库
    for (int i = 0; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CloseDB(dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 072.即有DB接口建立的数据库，也有TPC接口建立的库，调用接口DB_GetDBId进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // DB接口创建一个数据库
    const char testDbName[DB_NAME_LEN] = "testDQL";
    ;
    uint32_t testDbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库id
    uint32_t dbId = 0;
    ret = DB_GetDBId((VOS_UINT8 *)testDbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(dbId, testDbId);
    ret = DB_GetDBId((VOS_UINT8 *)g_testDbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(dbId, g_testDbId);
    // 删除数据库
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 073.输入不存在的数据库名字时调用接口DB_GetDBId进行查询
TEST_F(MetadataforDB_Test, V1Com_028_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 查询数据库id
    uint32_t dbId = 0;
    ret = DB_GetDBId((VOS_UINT8 *)"test", &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
}
// 074.即有DB接口建立的数据库，也有TPC接口建立的库，调用接口DBINIT_GetDatabaseNamesEx进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // DB接口创建一个数据库
    const char testDbName[DB_NAME_LEN] = "testDQL";
    ;
    uint32_t testDbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库名字
    VOS_UINT32 NumOfDatabases = 2;
    VOS_UINT8 *pucDbName[2];
    for (int i = 0; i < NumOfDatabases; i++) {
        pucDbName[i] = (uint8_t *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DBINIT_GetDatabaseNamesEx(&NumOfDatabases, pucDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据库名字是否正确
    ret = strcmp((char *)pucDbName[0], "testDql");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)pucDbName[1], "testDQL");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(2, NumOfDatabases);
    // 释放内存
    for (int i = 0; i < NumOfDatabases; i++) {
        TEST_V1_FREE(pucDbName[i]);
    }

    // 删除数据库
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 075.建立255个DB，只打开一半DB调用DBINIT_GetDatabaseNamesEx查询所有的数据库
TEST_F(MetadataforDB_Test, V1Com_028_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建255个数据库
    uint32_t dbId[254] = {0};
    for (int i = 0; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i < 127) {
            ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId[i]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 查询数据库名字
    VOS_UINT32 NumOfDatabases = 254;
    VOS_UINT8 *pucDbName[254];
    for (int i = 0; i < NumOfDatabases; i++) {
        pucDbName[i] = (uint8_t *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DBINIT_GetDatabaseNamesEx(&NumOfDatabases, pucDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据库名字是否正确
    for (int i = 0; i < 127; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = strcmp((char *)pucDbName[i + 1], dbName);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(128, NumOfDatabases);
    // 释放内存
    for (int i = 0; i < 254; i++) {
        TEST_V1_FREE(pucDbName[i]);
    }
    // 删除所有数据库
    for (int i = 0; i < 127; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CloseDB(dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 127; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 076.建立255个DB，删除一半DB后调用DBINIT_GetDatabaseNamesEx查询所有的数据库
TEST_F(MetadataforDB_Test, V1Com_028_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建255个数据库
    uint32_t dbId[254] = {0};
    for (int i = 0; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 删除一半数据库
    for (int i = 0; i < 127; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CloseDB(dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询数据库名字
    VOS_UINT32 NumOfDatabases = 254;
    VOS_UINT8 *pucDbName[254];
    for (int i = 0; i < NumOfDatabases; i++) {
        pucDbName[i] = (uint8_t *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DBINIT_GetDatabaseNamesEx(&NumOfDatabases, pucDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据库名字是否正确
    for (int i = 127; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = strcmp((char *)pucDbName[i - 126], dbName);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(128, NumOfDatabases);
    for (int i = 127; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CloseDB(dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 释放内存
    for (int i = 0; i < 254; i++) {
        TEST_V1_FREE(pucDbName[i]);
    }
}
// 077.没有数据库时调用接口DBINIT_GetDatabaseNamesEx进行查询
TEST_F(MetadataforDB_Test, V1Com_028_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库名字
    VOS_UINT32 NumOfDatabases = 2;
    VOS_UINT8 *pucDbName[2];
    for (int i = 0; i < NumOfDatabases; i++) {
        pucDbName[i] = (uint8_t *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DBINIT_GetDatabaseNamesEx(&NumOfDatabases, pucDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, NumOfDatabases);
    // 释放内存
    for (int i = 0; i < 2; i++) {
        TEST_V1_FREE(pucDbName[i]);
    }
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 078.pulNumOfDatabases比实际DB数量少调用接口DBINIT_GetDatabaseNamesEx进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t dbId = 0;
    char dbName[DB_NAME_LEN] = "testDQl";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库名字
    VOS_UINT32 NumOfDatabases = 1;
    VOS_UINT8 *pucDbName[1];
    for (int i = 0; i < NumOfDatabases; i++) {
        pucDbName[i] = (uint8_t *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DBINIT_GetDatabaseNamesEx(&NumOfDatabases, pucDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(2, NumOfDatabases);
    // 释放内存
    for (int i = 0; i < 1; i++) {
        TEST_V1_FREE(pucDbName[i]);
    }
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 079.pulNumOfDatabases比实际DB数量多调用接口DBINIT_GetDatabaseNamesEx进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t dbId = 0;
    char dbName[DB_NAME_LEN] = "testDQl";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库名字
    VOS_UINT32 NumOfDatabases = 3;
    VOS_UINT8 *pucDbName[3];
    for (int i = 0; i < NumOfDatabases; i++) {
        pucDbName[i] = (uint8_t *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DBINIT_GetDatabaseNamesEx(&NumOfDatabases, pucDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(2, NumOfDatabases);
    // 释放内存
    for (int i = 0; i < 3; i++) {
        TEST_V1_FREE(pucDbName[i]);
    }
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 080.即有DB接口建立的数据库，也有TPC接口建立的库，调用接口DBINIT_GetNumOfDatabases进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // DB接口创建一个数据库
    const char testDbName[DB_NAME_LEN] = "testDQL";
    ;
    uint32_t testDbId = 0;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库数量
    VOS_UINT32 NumOfDatabases = 0;
    ret = DBINIT_GetNumOfDatabases(&NumOfDatabases);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(2, NumOfDatabases);
    // 删除数据库
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 081.建立255个DB，只打开一半DB，调用接口DBINIT_GetNumOfDatabases进行查询
TEST_F(MetadataforDB_Test, V1Com_028_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建255个数据库
    uint32_t dbId[254] = {0};
    for (int i = 0; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i < 127) {
            ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId[i]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 查询数据库数量
    VOS_UINT32 NumOfDatabases = 0;
    ret = DBINIT_GetNumOfDatabases(&NumOfDatabases);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(128, NumOfDatabases);
    // 删除所有数据库
    for (int i = 0; i < 127; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CloseDB(dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 127; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 082.建立255个DB，打开一半DB后close，调用接口DBINIT_GetNumOfDatabases进行查询
TEST_F(MetadataforDB_Test, V1Com_028_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建255个数据库
    uint32_t dbId[254] = {0};
    for (int i = 0; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 关闭一半数据库
    for (int i = 0; i < 127; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CloseDB(dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询数据库数量
    VOS_UINT32 NumOfDatabases = 0;
    ret = DBINIT_GetNumOfDatabases(&NumOfDatabases);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(128, NumOfDatabases);
    // 删除所有数据库
    for (int i = 0; i < 127; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 127; i < 254; i++) {
        char dbName[DB_NAME_LEN] = {0};
        sprintf(dbName, "test%d", i);
        ret = DB_CloseDB(dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 083.没有数据库时调用接口DBINIT_GetNumOfDatabases进行查询
TEST_F(MetadataforDB_Test, V1Com_028_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据库数量
    VOS_UINT32 NumOfDatabases = 0;
    ret = DBINIT_GetNumOfDatabases(&NumOfDatabases);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, NumOfDatabases);
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 084.建立65535个表后调用接口DBINIT_GetNumOfRelations进行查询
TEST_F(MetadataforMax_Test, V1Com_028_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建立65534张表
    uint16_t testRelId[65534] = {0};
    char tableName[DB_NAME_LEN] = {0};
    for (int i = 0; i < 65534; i++) {
        DB_REL_DEF_STRU testRelDef1 = {0};
        sprintf(tableName, "label%d", i);
        TestCreateTblInitRelDef(&testRelDef1, tableName, testRelDef.pstFldLst, testRelDef.pstIdxLst, 7, 2);
        ret = DB_CreateTbl(g_testDbId, &testRelDef1, &testRelId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询表数量
    VOS_UINT32 NumOfTables = 0;
    ret = DBINIT_GetNumOfRelations(g_testDbId, &NumOfTables);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(65535, NumOfTables);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 085.建立65535个表后删除一半表调用接口DBINIT_GetNumOfRelations进行查询
TEST_F(MetadataforMax_Test, V1Com_028_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建立65534张表
    uint16_t testRelId[65534] = {0};
    char tableName[DB_NAME_LEN] = {0};
    for (int i = 0; i < 65534; i++) {
        DB_REL_DEF_STRU testRelDef1 = {0};
        sprintf(tableName, "label%d", i);
        TestCreateTblInitRelDef(&testRelDef1, tableName, testRelDef.pstFldLst, testRelDef.pstIdxLst, 7, 2);
        ret = DB_CreateTbl(g_testDbId, &testRelDef1, &testRelId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 删除一半表
    for (int i = 0; i < 32767; i++) {
        ret = DB_DropTbl(g_testDbId, testRelId[i], DROP_NOWAIT);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询表数量
    VOS_UINT32 NumOfTables = 0;
    ret = DBINIT_GetNumOfRelations(g_testDbId, &NumOfTables);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(32768, NumOfTables);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 086.建立两个DB，两个DB中的表同名同id调用接口DBINIT_GetNumOfRelations进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建数据库
    uint32_t dbId2 = 0;
    char dbName2[DB_NAME_LEN] = "testDQl2";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName2, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName2, &dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 1;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTblById(g_testDbId, "schema_file/vertexlabel3.json", testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = TestTPC_CreateTblById(dbId2, "schema_file/vertexlabel3.json", testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表数量
    VOS_UINT32 NumOfTables = 0;
    ret = DBINIT_GetNumOfRelations(g_testDbId, &NumOfTables);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, NumOfTables);
    ret = DBINIT_GetNumOfRelations(dbId2, &NumOfTables);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, NumOfTables);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 删除数据库
    ret = TPC_CloseDB(dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName2, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 087.修改表名和表id后调用接口DB_GetTblCount进行查询
TEST_F(MetadataforDB_Test, V1Com_028_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 修改表
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId1, (VOS_UINT8 *)"testDdl2", testRelId1 + 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表数量
    VOS_UINT32 NumOfTables = 0;
    ret = DB_GetTblCount(g_testDbId, &NumOfTables);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, NumOfTables);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 088.没有表时调用接口DB_GetTblCount进行查询
TEST_F(MetadataforDB_Test, V1Com_028_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 查询表数量
    VOS_UINT32 NumOfTables = 0;
    ret = DB_GetTblCount(g_testDbId, &NumOfTables);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, NumOfTables);
}
// 089.两个表名字仅大小写不一致调用接口DBINIT_GetRelationId进行查询
TEST_F(MetadataforDB_Test, V1Com_028_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 修改表
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId1, (VOS_UINT8 *)"Table1", testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表2
    uint16_t testRelId2 = 0;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId2, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表id
    uint16_t relId = 0;
    ret = DBINIT_GetRelationId(g_testDbId, (VOS_UINT8 *)"Table1", &relId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId1, relId);
    ret = DBINIT_GetRelationId(g_testDbId, (VOS_UINT8 *)"table1", &relId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId2, relId);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 090.输入的表名不存在调用接口DBINIT_GetRelationId进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t relId = 0;
    ret = DBINIT_GetRelationId(g_testDbId, (VOS_UINT8 *)"Table1", &relId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, relId);
}
// 091.建立65535个表后依次调用接口DB_GetTblId查询表id
TEST_F(MetadataforMax_Test, V1Com_028_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建立65534张表
    uint16_t testRelId[65534] = {0};
    char tableName[65534] = {0};
    for (int i = 0; i < 65534; i++) {
        DB_REL_DEF_STRU testRelDef1 = {0};
        sprintf(tableName, "label%d", i);
        TestCreateTblInitRelDef(&testRelDef1, tableName, testRelDef.pstFldLst, testRelDef.pstIdxLst, 7, 2);
        ret = DB_CreateTbl(g_testDbId, &testRelDef1, &testRelId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询表id
    for (int i = 0; i < 65534; i++) {
        uint16_t relId = 0;
        sprintf(tableName, "label%d", i);
        ret = DB_GetTblId(g_testDbId, (VOS_UINT8 *)tableName, &relId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(testRelId[i], relId);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 092.通过索引字段调用所有查询接口和更新删除操作后，调用DBS_GetIdxStatistic进行查询
TEST_F(MetadataforDB_Test, V1Com_028_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 5);
    // 删除数据
    Delete(testRelId1, 11, false, 5);
    // 查询数据
    selectDataByAllWays(testRelId1, 5, 2);

    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 093.通过索引字段调用所有查询接口后，调用DBS_GetIdxStatistic查询其他未引用索引字段
TEST_F(MetadataforDB_Test, V1Com_028_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 5);
    // 删除数据
    Delete(testRelId1, 11, false, 5);
    // 查询数据
    selectDataByAllWays(testRelId1, 5, 2);

    // 查询索引信息
    uint16_t idxId = 1;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 094.不通过索引字段调用所有查询接口后，调用DBS_GetIdxStatistic进行查询
TEST_F(MetadataforDB_Test, V1Com_028_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 0);
    // 删除数据
    Delete(testRelId1, 11, false, 0);
    // 查询数据
    selectDataByAllWays(testRelId1, 0, 2);

    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 095.开启fetchselect之前，调用DBS_GetIdxStatistic进行查询
TEST_F(MetadataforDB_Test, V1Com_028_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // beginselect
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 5;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = 2;

    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;

    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId1);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_BeginSelect(g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);

    ret = DB_FetchSelectRec(phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, DsBufGet.usRecNum);
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 096.开启fetchselect之后，结束查询之前，调用DBS_GetIdxStatistic进行查询
TEST_F(MetadataforDB_Test, V1Com_028_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // beginselect
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 5;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = 2;

    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;

    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId1);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_BeginSelect(g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, DsBufGet.usRecNum);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, IdxStatistic);

    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 097.开启CDB后调用TPC接口进行查询，CDB提交后调用DBS_GetIdxStatistic进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询数据
    selectDataByAllWays_TPC(testRelId1, 5, 2, cdbID, 2);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(3, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 098.开启CDB后调用TPC接口进行查询，CDB回滚后调用DBS_GetIdxStatistic进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询数据
    selectDataByAllWays_TPC(testRelId1, 5, 2, cdbID, 2);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(3, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 099.开启CDB后调用TPC接口进行查询，CDB结束前，更新操作前后调用DBS_GetIdxStatistic进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, IdxStatistic);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(2, IdxStatistic);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 100.不进行查询直接调用DBS_GetIdxStatistic进行查询
TEST_F(MetadataforDB_Test, V1Com_028_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 101.开启CDB后调用TPC接口进行DML操作，CDB结束前和CDB提交后调用DBS_GetRelActRec进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询索引信息
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId1, &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, actRec);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetRelActRec(g_testDbId, testRelId1, &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(9, actRec);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 102.开启CDB后调用TPC接口进行DML操作，CDB回滚后调用DBS_GetRelActRec进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询索引信息
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId1, &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, actRec);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetRelActRec(g_testDbId, testRelId1, &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, actRec);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 103.使用DB接口进行DML操作后调用DBS_GetRelActRec进行查询
TEST_F(MetadataforDB_Test, V1Com_028_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 0);
    // 删除数据
    Delete(testRelId1, 11, false, 0);
    // 查询索引信息
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId1, &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(9, actRec);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 104.表中不存在数据时调用DBS_GetRelActRec进行查询
TEST_F(MetadataforDB_Test, V1Com_028_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId1, &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, actRec);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 105.修改表id后调用DBS_GetRelActRec进行查询
TEST_F(MetadataforDB_Test, V1Com_028_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    uint16_t testRelId2 = 10;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 修改表
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId1, (VOS_UINT8 *)"testDdl2", testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId2, &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, actRec);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 106.开启CDB后调用TPC接口进行DML操作，CDB结束前和CDB提交后调用DBS_GetRelationInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询索引信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DBS_GetRelationInfo(g_testDbId, testRelId1, &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)relationInfo.aucRelName, "table1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, relationInfo.ulActualRecNum);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetRelationInfo(g_testDbId, testRelId1, &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)relationInfo.aucRelName, "table1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(9, relationInfo.ulActualRecNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 107.开启CDB后调用TPC接口进行DML操作，CDB回滚后调用DBS_GetRelationInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询索引信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DBS_GetRelationInfo(g_testDbId, testRelId1, &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)relationInfo.aucRelName, "table1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, relationInfo.ulActualRecNum);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetRelationInfo(g_testDbId, testRelId1, &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)relationInfo.aucRelName, "table1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, relationInfo.ulActualRecNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 108.修改最大记录数后调用DBS_GetRelationInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 修改最大记录数
    ret = DB_SetMaxRecNumOfTable(g_testDbId, testRelId1, 0, 0, 128008);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DBS_GetRelationInfo(g_testDbId, testRelId1, &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)relationInfo.aucRelName, "table1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, relationInfo.ulActualRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(128008, relationInfo.ulMaxRecNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 109.使用DB接口进行DML操作后调用DB_GetTblInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 0);
    // 删除数据
    Delete(testRelId1, 11, false, 0);
    // 查询索引信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DB_GetTblInfo(g_testDbId, testRelId1, &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)relationInfo.aucRelName, "table1");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(9, relationInfo.ulActualRecNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 110.修改表名和表id后调用DB_GetTblInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    uint16_t testRelId2 = 10;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 修改表
    ret = DB_ModifyTblNameAndID(g_testDbId, testRelId1, (VOS_UINT8 *)"testDdl2", testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DB_GetTblInfo(g_testDbId, testRelId2, &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)relationInfo.aucRelName, "testDdl2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, relationInfo.ulActualRecNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 111.对包含大对象和全数据类型的表调用DB_GetTblInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DB_GetTblInfo(g_testDbId, testRelId1, &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)relationInfo.aucRelName, "table2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(2000, relationInfo.ulMaxRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, relationInfo.ulActualRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(32492, relationInfo.ulRecLength);
    V1_AW_MACRO_EXPECT_EQ_INT(28, relationInfo.usFieldNum);
    V1_AW_MACRO_EXPECT_EQ_INT(2, relationInfo.usIndexNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 112.TPC接口修改最大记录数后调用DBS_GetRelMaxRecNum进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 修改最大记录数
    ret = TPC_SetMaxRecNumOfTable(g_testDbId, testRelId1, 0, 0, 128008);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    VOS_UINT32 maxRecNum = 0;
    ret = DBS_GetRelMaxRecNum(g_testDbId, testRelId1, &maxRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(128008, maxRecNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 113.DB接口修改最大记录数后调用DBS_GetRelMaxRecNum进行查询
TEST_F(MetadataforDB_Test, V1Com_028_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 修改最大记录数
    ret = DB_SetMaxRecNumOfTable(g_testDbId, testRelId1, 0, 0, 128008);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    VOS_UINT32 maxRecNum = 0;
    ret = DBS_GetRelMaxRecNum(g_testDbId, testRelId1, &maxRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(128008, maxRecNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 114.建表后调用DBS_GetRelMaxRecNum进行查询
TEST_F(MetadataforDB_Test, V1Com_028_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    VOS_UINT32 maxRecNum = 0;
    ret = DBS_GetRelMaxRecNum(g_testDbId, testRelId1, &maxRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(128000, maxRecNum);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 115.通过索引字段调用所有查询接口后，调用DBS_ResetAllIdxStat进行重置
TEST_F(MetadataforDB_Test, V1Com_028_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 5);
    // 删除数据
    Delete(testRelId1, 11, false, 5);
    // 查询数据
    selectDataByAllWays(testRelId1, 5, 2);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 重置索引信息
    ret = DBS_ResetAllIdxStat(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 116.不通过索引字段调用所有查询接口后，调用DBS_ResetAllIdxStat进行重置
TEST_F(MetadataforDB_Test, V1Com_028_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 0);
    // 删除数据
    Delete(testRelId1, 11, false, 0);
    // 查询数据
    selectDataByAllWays(testRelId1, 0, 1);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 重置索引信息
    ret = DBS_ResetAllIdxStat(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 117.开启CDB后调用TPC接口进行查询，CDB提交后调用DBS_ResetAllIdxStat进行重置
TEST_F(MetadataforTPC_Test, V1Com_028_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询数据
    selectDataByAllWays_TPC(testRelId1, 5, 2, cdbID);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 重置索引信息
    ret = DBS_ResetAllIdxStat(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 118.开启CDB后调用TPC接口进行查询，CDB回滚后调用DBS_ResetAllIdxStat进行重置
TEST_F(MetadataforTPC_Test, V1Com_028_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询数据
    selectDataByAllWays_TPC(testRelId1, 5, 2, cdbID);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 重置索引信息
    ret = DBS_ResetAllIdxStat(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 119.开启CDB后调用TPC接口进行查询，CDB结束前调用DBS_ResetAllIdxStat进行重置
TEST_F(MetadataforTPC_Test, V1Com_028_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询数据
    selectDataByAllWays_TPC(testRelId1, 5, 2, cdbID);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 重置索引信息
    ret = DBS_ResetAllIdxStat(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 120.不进行查询直接调用DBS_ResetAllIdxStat进行重置
TEST_F(MetadataforDB_Test, V1Com_028_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 重置索引信息
    ret = DBS_ResetAllIdxStat(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 121.没有索引字段的表直接调用DBS_ResetAllIdxStat进行重置
TEST_F(MetadataforDB_Test, V1Com_028_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel067.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 重置索引信息
    ret = DBS_ResetAllIdxStat(g_testDbId, testRelId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINDEX, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 122.通过索引字段调用所有查询接口后，调用DBS_ResetIdxStat进行重置
TEST_F(MetadataforDB_Test, V1Com_028_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 5);
    // 删除数据
    Delete(testRelId1, 11, false, 5);
    // 查询数据
    selectDataByAllWays(testRelId1, 5, 2);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 重置索引信息
    ret = DBS_ResetIdxStat(g_testDbId, testRelId1, idxId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 123.通过索引字段调用所有查询接口后，调用DBS_ResetIdxStat进行其他索引的重置
TEST_F(MetadataforDB_Test, V1Com_028_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 5);
    // 删除数据
    Delete(testRelId1, 11, false, 5);
    // 查询数据
    selectDataByAllWays(testRelId1, 5, 2);

    // 重置索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_ResetIdxStat(g_testDbId, testRelId1, idxId + 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId + 1, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 124.开启CDB后调用TPC接口进行查询，CDB提交后调用DBS_ResetIdxStat进行重置
TEST_F(MetadataforTPC_Test, V1Com_028_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询数据
    selectDataByAllWays_TPC(testRelId1, 5, 2, cdbID);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 重置索引信息
    ret = DBS_ResetIdxStat(g_testDbId, testRelId1, idxId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 125.开启CDB后调用TPC接口进行查询，CDB回滚后调用DBS_ResetIdxStat进行重置
TEST_F(MetadataforTPC_Test, V1Com_028_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询数据
    selectDataByAllWays_TPC(testRelId1, 5, 2, cdbID);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 重置索引信息
    ret = DBS_ResetIdxStat(g_testDbId, testRelId1, idxId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 126.开启CDB后调用TPC接口进行查询，CDB结束前调用DBS_ResetIdxStat进行重置
TEST_F(MetadataforTPC_Test, V1Com_028_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5, g_testDbId, cdbID);
    // 删除数据
    Delete(testRelId1, 11, true, 5, g_testDbId, cdbID);
    // 查询数据
    selectDataByAllWays_TPC(testRelId1, 5, 2, cdbID);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, IdxStatistic);
    // 重置索引信息
    ret = DBS_ResetIdxStat(g_testDbId, testRelId1, idxId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 127.不进行查询直接调用DBS_ResetIdxStat进行重置
TEST_F(MetadataforDB_Test, V1Com_028_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    uint16_t idxId = 0;
    VOS_UINT32 IdxStatistic = 0;
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 重置索引信息
    ret = DBS_ResetIdxStat(g_testDbId, testRelId1, idxId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询索引信息
    ret = DBS_GetIdxStatistic(g_testDbId, testRelId1, idxId, &IdxStatistic);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, IdxStatistic);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 128.输入的描述信息长度大于输入的ulLen长度
TEST_F(MetadataforDB_Test, V1Com_028_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 5;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(ulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)g_testDbName, NULL, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(5, ulLen);
    TEST_V1_FREE(getDBDescrInfo);
}
// 129.输入的描述信息长度等于输入的ulLen长度
TEST_F(MetadataforDB_Test, V1Com_028_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345";
    uint32_t ulLen = 5;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(ulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)g_testDbName, NULL, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(5, ulLen);
    TEST_V1_FREE(getDBDescrInfo);
}
// 130.设置输入的ulLen长度为0xFFFFFFFF-1
TEST_F(MetadataforDB_Test, V1Com_028_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 0xFFFFFFFF - 1;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
}
// 131.开启后关闭DB直接设置描述信息
TEST_F(MetadataforDB_Test, V1Com_028_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 关闭DB
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 132.不设置描述信息，直接获取描述信息
TEST_F(MetadataforDB_Test, V1Com_028_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    uint32_t ulLen = 0;
    ret = DB_GetDBDesc((VOS_UINT8 *)g_testDbName, NULL, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DESCINFO, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, ulLen);
}
// 133.打开持久化DB后设置描述信息，导出后从文件中读取描述信息
TEST_F(MetadataforTPC_Test, V1Com_028_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("touch filePath/de.txt");
    // 创建持久化DB
    char dbDir[256] = "./filePath/de.txt";
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(ulLen + 1);
    ret = DB_GetDBDesc((VOS_UINT8 *)testDbName, (uint8_t *)exportFile, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, ulLen);
    TEST_V1_FREE(getDBDescrInfo);
    system("rm -rf ./filePath");
}
// 134.打开普通DB后设置描述信息，导出后从文件中读取描述信息
TEST_F(MetadataforTPC_Test, V1Com_028_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("touch filePath/de.txt");
    // 创建普通DB
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(ulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)testDbName, (uint8_t *)exportFile, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, ulLen);
    TEST_V1_FREE(getDBDescrInfo);
    system("rm -rf ./filePath");
}
// 135.设置描述信息后，多次open，close同一个持久化DB后读取描述信息
TEST_F(MetadataforTPC_Test, V1Com_028_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("touch filePath/de.txt");
    // 创建持久化DB
    char dbDir[256] = "./filePath/de.txt";
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 3; i++) {
        // 关闭DB
        ret = TPC_CloseDB(testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 再次open
        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)testDbName, &testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(ulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)testDbName, NULL, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DESCINFO, ret);
    TEST_V1_FREE(getDBDescrInfo);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 136.设置描述信息后，多次open，close同一个普通DB后读取描述信息
TEST_F(MetadataforTPC_Test, V1Com_028_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建普通DB
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 3; i++) {
        // 关闭DB
        ret = TPC_CloseDB(testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 再次open
        ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(ulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)testDbName, NULL, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, ulLen);
    TEST_V1_FREE(getDBDescrInfo);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 137.多次导入导出持久化DB后读取数据库描述信息
TEST_F(MetadataforTPC_Test, V1Com_028_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("touch filePath/de.txt");
    // 创建持久化DB
    char dbDir[256] = "./filePath/de.txt";
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置DB描述信息
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    char exportFile[64] = "./filePath/export.txt";
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 3; i++) {
        // 导出DB
        ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 导入DB
        ret = TPC_Restore(
            (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(ulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)testDbName, (uint8_t *)exportFile, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, ulLen);
    TEST_V1_FREE(getDBDescrInfo);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 138.多次导入导出普通DB后读取数据库描述信息
TEST_F(MetadataforTPC_Test, V1Com_028_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建普通DB
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置DB描述信息
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char exportFile[64] = "./filePath/export.txt";
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 3; i++) {
        // 导出DB
        ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 导入DB
        ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(ulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)testDbName, NULL, getDBDescrInfo, &ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, ulLen);
    TEST_V1_FREE(getDBDescrInfo);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 139.pulLen设置小于的描述信息长度，读取数据库描述信息
TEST_F(MetadataforDB_Test, V1Com_028_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(1);
    uint32_t pulLen = 1;
    ret = DB_GetDBDesc((VOS_UINT8 *)g_testDbName, NULL, getDBDescrInfo, &pulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, pulLen);
    TEST_V1_FREE(getDBDescrInfo);
}
// 140.pulLen设置大于的描述信息长度，读取数据库描述信息
TEST_F(MetadataforDB_Test, V1Com_028_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    uint32_t pulLen = 10;
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(pulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)g_testDbName, NULL, getDBDescrInfo, &pulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, pulLen);
    TEST_V1_FREE(getDBDescrInfo);
}
// 141.pulLen设置正确的描述信息长度，读取数据库描述信息
TEST_F(MetadataforDB_Test, V1Com_028_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 设置DB描述信息
    VOS_VOID *DBDescrInfo = (VOS_VOID *)"12345678";
    uint32_t ulLen = 8;
    ret = DB_SetDBDesc((VOS_UINT8 *)g_testDbName, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB描述信息
    VOS_VOID *getDBDescrInfo = {0};
    uint32_t pulLen = 8;
    getDBDescrInfo = (VOS_VOID *)TEST_V1_MALLOC(pulLen);
    ret = DB_GetDBDesc((VOS_UINT8 *)g_testDbName, NULL, getDBDescrInfo, &pulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(getDBDescrInfo, DBDescrInfo, ulLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(8, pulLen);
    TEST_V1_FREE(getDBDescrInfo);
}
// 142.即有DB接口建立的数据库，也有TPC接口建立的库，调用接口DB_GetDBName进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建普通DB
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB名字
    VOS_UINT8 dbName[DB_NAME_LEN] = {0};
    ret = DB_GetDBName(testDbId, dbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)dbName, testDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_GetDBName(g_testDbId, dbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)dbName, g_testDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 143.建立255个DB，依次调用接口DB_GetDBName进行查询
TEST_F(MetadataforDB_Test, V1Com_028_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId[254] = {0};
    char testDbName[254][DB_NAME_LEN] = {0};
    for (int i = 0; i < 254; i++) {
        sprintf(testDbName[i], "testDdl%d", i);
        ret = DB_CreateDB((VOS_UINT8 *)testDbName[i], NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName[i], &testDbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < 254; i++) {
        // 获取DB名字
        VOS_UINT8 dbName[DB_NAME_LEN] = {0};
        ret = DB_GetDBName(testDbId[i], dbName);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = strcmp((char *)dbName, testDbName[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < 254; i++) {
        // 关闭DB
        ret = DB_CloseDB(testDbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 删除DB
        ret = DB_DropDB((VOS_UINT8 *)testDbName[i], 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 144.5次open，4次close同一个DB后调用接口DB_GetDBName进行查询
TEST_F(MetadataforDB_Test, V1Com_028_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 5次open
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 4次close
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB名字
    VOS_UINT8 dbName[DB_NAME_LEN] = {0};
    ret = DB_GetDBName(g_testDbId, dbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = strcmp((char *)dbName, g_testDbName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 145.5次open，5次close同一个DB后调用接口DB_GetDBName进行查询
TEST_F(MetadataforDB_Test, V1Com_028_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 5次open
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 5次close
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取DB名字
    VOS_UINT8 dbName[DB_NAME_LEN] = {0};
    ret = DB_GetDBName(g_testDbId, dbName);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 146.开启图查询句柄后调用接口DB_GetOpenHandleInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_146)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建三个表
    uint16_t testRelId[3] = {0};
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId[0], &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    char tableName[DB_NAME_LEN] = {0};
    for (int i = 0; i < 2; i++) {
        DB_REL_DEF_STRU testRelDef1 = {0};
        sprintf(tableName, "label%d", i);
        TestCreateTblInitRelDef(&testRelDef1, tableName, testRelDef.pstFldLst, testRelDef.pstIdxLst, 7, 2);
        ret = TPC_CreateTbl(g_testDbId, &testRelDef1, &testRelId[i + 1]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId[0], testRelId[1], 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId[1], testRelId[2], 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    for (int i = 0; i < 3; i++) {
        Insert(testRelId[i], 10, testRelDef.pstFldLst, true);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 使用单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId[0], testRelId[1], &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 1;
    HandleCtrlInfo.pstHndlInfo = (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_testDbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, HandleCtrlInfo.ulHndlCnt);
    V1_AW_MACRO_EXPECT_EQ_INT(phSelect, HandleCtrlInfo.pstHndlInfo[0].ulHndlId);
    V1_AW_MACRO_EXPECT_EQ_INT(TPC_GLOBAL_CDB, HandleCtrlInfo.pstHndlInfo[0].ulCDBId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId[0], HandleCtrlInfo.pstHndlInfo[0].ulTableId);
    // 结束查询
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 147.开启普通表查询句柄后调用接口DB_GetOpenHandleInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建普通表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId, 10, testRelDef.pstFldLst, false);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 使用普通查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    ret = DB_BeginSelect(g_testDbId, testRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = DB_FetchSelectRec(phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 1;
    HandleCtrlInfo.pstHndlInfo = (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_testDbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, HandleCtrlInfo.ulHndlCnt);
    V1_AW_MACRO_EXPECT_EQ_INT(phSelect, HandleCtrlInfo.pstHndlInfo[0].ulHndlId);
    V1_AW_MACRO_EXPECT_EQ_INT(TPC_GLOBAL_CDB, HandleCtrlInfo.pstHndlInfo[0].ulCDBId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId, HandleCtrlInfo.pstHndlInfo[0].ulTableId);
    // 结束查询
    TEST_V1_FREE(pucDataGet);
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 148.开启图查询句柄后结束该句柄调用接口DB_GetOpenHandleInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_148)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建三个表
    uint16_t testRelId[3] = {0};
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId[0], &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    char tableName[DB_NAME_LEN] = {0};
    for (int i = 0; i < 2; i++) {
        DB_REL_DEF_STRU testRelDef1 = {0};
        sprintf(tableName, "label%d", i);
        TestCreateTblInitRelDef(&testRelDef1, tableName, testRelDef.pstFldLst, testRelDef.pstIdxLst, 7, 2);
        ret = TPC_CreateTbl(g_testDbId, &testRelDef1, &testRelId[i + 1]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId[0], testRelId[1], 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId[1], testRelId[2], 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    for (int i = 0; i < 3; i++) {
        Insert(testRelId[i], 10, testRelDef.pstFldLst, true);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 使用单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 5;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId[0], testRelId[1], &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 结束查询
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 1;
    HandleCtrlInfo.pstHndlInfo = (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_testDbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, HandleCtrlInfo.ulHndlCnt);
    // 结束查询
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
}
// 149.开启普通表查询句柄后结束该句柄调用接口DB_GetOpenHandleInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建普通表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId, 10, testRelDef.pstFldLst, false);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 使用普通查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    ret = DB_BeginSelect(g_testDbId, testRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表1的值
    ret = DB_FetchSelectRec(phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 结束查询
    TEST_V1_FREE(pucDataGet);
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 1;
    HandleCtrlInfo.pstHndlInfo = (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_testDbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, HandleCtrlInfo.ulHndlCnt);
    // 结束查询
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
}
// 150.开启多个普通表查询句柄调用接口DB_GetOpenHandleInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_150)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建普通表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId, 10, testRelDef.pstFldLst, false);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 使用普通查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect[4] = {0};
    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    for (int i = 0; i < 4; i++) {
        ret = DB_BeginSelect(g_testDbId, testRelId, &pstCond, &pstFldFilter, &phSelect[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 查询表1的值
        ret = DB_FetchSelectRec(phSelect[i], &DsBufGet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 6;
    HandleCtrlInfo.pstHndlInfo =
        (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(HandleCtrlInfo.ulHndlCnt * sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_testDbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(4, HandleCtrlInfo.ulHndlCnt);
    for (int i = 0; i < 4; i++) {
        V1_AW_MACRO_EXPECT_EQ_INT(phSelect[i], HandleCtrlInfo.pstHndlInfo[i].ulHndlId);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(TPC_GLOBAL_CDB, HandleCtrlInfo.pstHndlInfo[1].ulCDBId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId, HandleCtrlInfo.pstHndlInfo[1].ulTableId);
    // 结束查询
    TEST_V1_FREE(pucDataGet);
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
    for (int i = 0; i < 4; i++) {
        ret = DB_EndSelect(phSelect[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 151.开启多个图查询句柄调用接口DB_GetOpenHandleInfo进行查询
TEST_F(MetadataforTPC_Test, V1Com_028_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建三个表
    uint16_t testRelId[3] = {0};
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId[0], &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    char tableName[DB_NAME_LEN] = {0};
    for (int i = 0; i < 2; i++) {
        DB_REL_DEF_STRU testRelDef1 = {0};
        sprintf(tableName, "label%d", i);
        TestCreateTblInitRelDef(&testRelDef1, tableName, testRelDef.pstFldLst, testRelDef.pstIdxLst, 7, 2);
        ret = TPC_CreateTbl(g_testDbId, &testRelDef1, &testRelId[i + 1]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {5};
    uint8_t field2[1] = {5};
    TestCreateEdgeDef(&edgeDef1, true, testRelId[0], testRelId[1], 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId[1], testRelId[2], 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    for (int i = 0; i < 3; i++) {
        Insert(testRelId[i], 10, testRelDef.pstFldLst, true);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 使用单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect[4] = {0};
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    for (int i = 0; i < 4; i++) {
        ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId[0], &pstCond, &pstFldFilter, &phSelect[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        VOS_UINT32 fetchCount = 5;
        // 查询表1的值
        ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect[i], fetchCount, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 从表1跳表2
        DB_EDGE_CON_STRU nextedge;
        SetPathInfoToSingle(&nextedge, testRelId[0], testRelId[1], &pstFldFilter, 1, field1, field2);
        ret = TPC_MoveNextByEdge(phSelect[i], nextedge);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect[i], fetchCount, &pstBuff);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 6;
    HandleCtrlInfo.pstHndlInfo =
        (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(HandleCtrlInfo.ulHndlCnt * sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_testDbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(4, HandleCtrlInfo.ulHndlCnt);
    for (int i = 0; i < 4; i++) {
        V1_AW_MACRO_EXPECT_EQ_INT(phSelect[i], HandleCtrlInfo.pstHndlInfo[i].ulHndlId);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(TPC_GLOBAL_CDB, HandleCtrlInfo.pstHndlInfo[1].ulCDBId);
    V1_AW_MACRO_EXPECT_EQ_INT(testRelId[0], HandleCtrlInfo.pstHndlInfo[1].ulTableId);
    // 结束查询
    TPC_FreeBufMemById(pstBuff->memId);
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
    for (int i = 0; i < 4; i++) {
        ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 152.开启多个句柄后强制关闭所有句柄再调用接口DB_GetOpenHandleInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_152)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建普通表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId, 10, testRelDef.pstFldLst, false);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 使用普通查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect[4] = {0};
    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    for (int i = 0; i < 4; i++) {
        ret = DB_BeginSelect(g_testDbId, testRelId, &pstCond, &pstFldFilter, &phSelect[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 查询表1的值
        ret = DB_FetchSelectRec(phSelect[i], &DsBufGet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 强制关闭所有句柄
    ret = DB_CloseAllHandles(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 6;
    HandleCtrlInfo.pstHndlInfo =
        (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(HandleCtrlInfo.ulHndlCnt * sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_testDbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, HandleCtrlInfo.ulHndlCnt);
    // 结束查询
    TEST_V1_FREE(pucDataGet);
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
}
// 153.没有句柄时调用接口DB_GetOpenHandleInfo进行查询
TEST_F(MetadataforDB_Test, V1Com_028_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 6;
    HandleCtrlInfo.pstHndlInfo =
        (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(HandleCtrlInfo.ulHndlCnt * sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_testDbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, HandleCtrlInfo.ulHndlCnt);
    // 结束查询
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
}
// 154.pulTblCount输入的值为0调用接口DB_GetTblNamesAndCount进行查询
TEST_F(MetadataforDB_Test, V1Com_029_154)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetTblNamesAndCount进行查询
    VOS_UINT32 pulTblCount = 0;
    const uint32_t TblName = pulTblCount;
    VOS_UINT8 *ppucTblName[TblName] = {0};
    for (int i = 0; i < TblName; i++) {
        ppucTblName[i] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DB_GetTblNamesAndCount(g_testDbId, &pulTblCount, ppucTblName);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);
    // 释放内存
    for (int i = 0; i < TblName; i++) {
        TEST_V1_FREE(ppucTblName[i]);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 155.pulTblCount输入的值大于表数量调用接口DB_GetTblNamesAndCount进行查询
TEST_F(MetadataforDB_Test, V1Com_029_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetTblNamesAndCount进行查询
    VOS_UINT32 pulTblCount = 2;
    const uint32_t TblName = pulTblCount;
    VOS_UINT8 *ppucTblName[TblName] = {0};
    for (int i = 0; i < TblName; i++) {
        ppucTblName[i] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DB_GetTblNamesAndCount(g_testDbId, &pulTblCount, ppucTblName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(ppucTblName[0], "table1", strlen("table1"));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);
    // 释放内存
    for (int i = 0; i < TblName; i++) {
        TEST_V1_FREE(ppucTblName[i]);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 156.pulTblCount输入的值等于表数量调用接口DB_GetTblNamesAndCount进行查询
TEST_F(MetadataforDB_Test, V1Com_029_156)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetTblNamesAndCount进行查询
    VOS_UINT32 pulTblCount = 1;
    const uint32_t TblName = pulTblCount;
    VOS_UINT8 *ppucTblName[TblName] = {0};
    for (int i = 0; i < TblName; i++) {
        ppucTblName[i] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DB_GetTblNamesAndCount(g_testDbId, &pulTblCount, ppucTblName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(ppucTblName[0], "table1", strlen("table1"));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);
    // 释放内存
    for (int i = 0; i < TblName; i++) {
        TEST_V1_FREE(ppucTblName[i]);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 157.建表65535个后调用接口DB_GetTblNamesAndCount进行查询
TEST_F(MetadataforMax_Test, V1Com_029_157)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId[65535] = {0};
    DB_REL_DEF_STRU *testRelDef = NULL;
    char tableName[65535][DB_NAME_LEN] = {0};
    for (int i = 0; i < 65535; i++) {
        sprintf(tableName[i], "label%d", i);
        ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId[i], testRelDef, tableName[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 调用接口DB_GetTblNamesAndCount进行查询
    VOS_UINT32 pulTblCount = 65535;
    const uint32_t TblName = pulTblCount;
    VOS_UINT8 *ppucTblName[TblName] = {0};
    for (int i = 0; i < TblName; i++) {
        ppucTblName[i] = (VOS_UINT8 *)malloc(DB_NAME_LEN);
    }
    ret = DB_GetTblNamesAndCount(g_testDbId, &pulTblCount, ppucTblName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 10; i++) {
        ret = memcmp(ppucTblName[i], tableName[i], strlen(tableName[i]));
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(65535, pulTblCount);
    // 释放内存
    for (int i = 0; i < TblName; i++) {
        free(ppucTblName[i]);
    }
}
// 158.建表65535个后删除一半表调用接口DB_GetTblNamesAndCount进行查询
TEST_F(MetadataforMax_Test, V1Com_029_158)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId[65535] = {0};
    DB_REL_DEF_STRU *testRelDef = NULL;
    char tableName[65535][DB_NAME_LEN] = {0};
    for (int i = 0; i < 65535; i++) {
        sprintf(tableName[i], "label%d", i);
        ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId[i], testRelDef, tableName[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 删除一半表
    for (int i = 0; i < 32767; i++) {
        ret = DB_DropTbl(g_testDbId, testRelId[i], DROP_NOWAIT);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 调用接口DB_GetTblNamesAndCount进行查询
    VOS_UINT32 pulTblCount = 32768;
    const uint32_t TblName = pulTblCount;
    VOS_UINT8 *ppucTblName[TblName] = {0};
    for (int i = 0; i < TblName; i++) {
        ppucTblName[i] = (VOS_UINT8 *)malloc(DB_NAME_LEN);
    }
    ret = DB_GetTblNamesAndCount(g_testDbId, &pulTblCount, ppucTblName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 32768; i++) {
        ret = memcmp(ppucTblName[i], tableName[i + 32767], strlen(tableName[i + 32767]));
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(32768, pulTblCount);
    // 释放内存
    for (int i = 0; i < TblName; i++) {
        free(ppucTblName[i]);
    }
}
// 159.持久化DB，一次open，一次close后再次open调用接口DB_GetTblNamesAndCount进行查询
TEST_F(MetadataforTPC_Test, V1Com_029_159)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("touch filePath/de.txt");
    // 创建持久化DB
    char dbDir[256] = "./filePath/de.txt";
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetTblNamesAndCount进行查询
    VOS_UINT32 pulTblCount = 1;
    const uint32_t TblName = pulTblCount;
    VOS_UINT8 *ppucTblName[TblName] = {0};
    for (int i = 0; i < TblName; i++) {
        ppucTblName[i] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DB_GetTblNamesAndCount(testDbId, &pulTblCount, ppucTblName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulTblCount);
    // 释放内存
    for (int i = 0; i < TblName; i++) {
        TEST_V1_FREE(ppucTblName[i]);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 删除DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 160.普通DB，一次open，一次close后后再次open调用接口DB_GetTblNamesAndCount进行查询
TEST_F(MetadataforDB_Test, V1Com_029_160)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetTblNamesAndCount进行查询
    VOS_UINT32 pulTblCount = 1;
    const uint32_t TblName = pulTblCount;
    VOS_UINT8 *ppucTblName[TblName] = {0};
    for (int i = 0; i < TblName; i++) {
        ppucTblName[i] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DB_GetTblNamesAndCount(g_testDbId, &pulTblCount, ppucTblName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = memcmp(ppucTblName[0], "table1", strlen("table1"));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);
    // 释放内存
    for (int i = 0; i < TblName; i++) {
        TEST_V1_FREE(ppucTblName[i]);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 161.DB中不存在表时调用接口DB_GetTblNamesAndCount进行查询
TEST_F(MetadataforDB_Test, V1Com_029_161)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 调用接口DB_GetTblNamesAndCount进行查询
    VOS_UINT32 pulTblCount = 0;
    const uint32_t TblName = pulTblCount;
    VOS_UINT8 *ppucTblName[TblName] = {0};
    for (int i = 0; i < TblName; i++) {
        ppucTblName[i] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_NAME_LEN);
    }
    ret = DB_GetTblNamesAndCount(g_testDbId, &pulTblCount, ppucTblName);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulTblCount);
    // 释放内存
    for (int i = 0; i < TblName; i++) {
        TEST_V1_FREE(ppucTblName[i]);
    }
}
// 162.导入普通DB后调用所有查询接口进行查询
TEST_F(MetadataforTPC_Test, V1Com_029_162)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5);
    // 删除数据
    Delete(testRelId1, 11, true, 5);
    // 导出DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char exportFile[64] = "./filePath/export.txt";
    // 导出DB
    ret = TPC_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用所有元数据查询接口
    useAllMetaInterface(testRelId1);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    system("rm -rf ./filePath");
}
// 163.导入持久化DB后调用所有查询接口进行查询
TEST_F(MetadataforTPC_Test, V1Com_029_163)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("touch filePath/de.txt");
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建持久化DB
    char dbDir[256] = "./filePath/de.txt";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 5);
    // 删除数据
    Delete(testRelId1, 11, true, 5);
    // 导出DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    char exportFile[64] = "./filePath/export.txt";
    // 导出DB
    ret = TPC_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用所有元数据查询接口
    useAllMetaInterface(testRelId1);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 关闭DB
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 164.clone表后调用所有查询表相关接口
TEST_F(MetadataforDB_Test, V1Com_029_164)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建普通DB
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // clone表
    uint16_t testRelId2 = 4;
    ret = DB_CloneTable(testDbId, testRelId1, g_testDbId, testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId2, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId2, 0, 11, testRelDef.pstFldLst, false, 0);
    // 删除数据
    Delete(testRelId2, 11, false, 0);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用所有元数据查询接口
    useAllMetaInterface(testRelId2);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
void *MetaSelect(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    // 调用所有元数据查询接口
    useAllMetaInterface(dqlArg->testRelId, dqlArg->selectWays);
}
// 165.每个线程调用一个查询接口进行查询
TEST_F(MetadataforDB_Test, V1Com_029_165)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 0);
    // 删除数据
    Delete(testRelId1, 11, false, 0);
    pthread_t tid[28];
    DqlArgsT dqlArg;
    dqlArg.testRelId = testRelId1;
    for (int i = 0; i < 28; i++) {
        dqlArg.selectWays = i + 1;
        ret = pthread_create(&tid[i], NULL, MetaSelect, &dqlArg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < 28; i++) {
        ret = pthread_join(tid[i], NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 166.写满内存后调用各个查询接口
TEST_F(MetadataforDB_Test, V1Com_029_166)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 修改最大记录数
    ret = DB_SetMaxRecNumOfTable(g_testDbId, testRelId1, 0, 0, 1000000);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    uint32_t count = 0;
    for (VOS_UINT32 i = 0; i < 1000000; i++) {
        testSetAllFieldOfBig(pucDataSet, testRelDef.pstFldLst, i, 16165);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = DB_InsertRec(g_testDbId, testRelId1, &pstDsBufSet);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "ret = %d, insert num:%d", ret, i);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
            TEST_V1_FREE(pucDataSet);
            count = i;
            break;
        }
    }

    // 调用所有元数据查询接口
    useAllMetaInterfaceofBig(testRelId1, count);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 167.每个查询接口重复调用100次
TEST_F(MetadataforDB_Test, V1Com_029_167)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, false, 0);
    // 删除数据
    Delete(testRelId1, 11, false, 0);
    // 调用所有元数据查询接口
    useAllMetaInterface(testRelId1, 0, 100);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
void *ImportAndExport(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    for (int i = 0; i < 1000; i++) {
        // 导出DB
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        char exportFile[64] = "./filePath/export.txt";
        ret = TPC_BkpPhy(g_testDbId, (uint8_t *)exportFile);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 导入DB
        char dbDir[256] = "./filePath/de.txt";
        ret = TPC_Restore(
            (uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
void *MetaSelectWithcount(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    // 调用所有元数据查询接口
    useAllMetaInterface(dqlArg->testRelId, 0, 1000);
}
// 170.TPC导入导出&&DB所有元数据接口并发
TEST_F(MetadataforTPC_Test, V1Com_028_170)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("touch filePath/de.txt");
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 创建持久化DB
    char dbDir[256] = "./filePath/de.txt";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    // 更新数据
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, true, 6);
    // 删除数据
    Delete(testRelId1, 11, true, 6);
    pthread_t tid[2];
    DqlArgsT dqlArg;
    dqlArg.testRelId = testRelId1;
    
    ret = pthread_create(&tid[0], NULL, ImportAndExport, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid[1], NULL, MetaSelectWithcount, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    ret = pthread_join(tid[0], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid[1], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    // 关闭DB
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除DB
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
void *DmlOption(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    // 更新数据
    Update(dqlArg->testRelId, 0, 11, dqlArg->testRelDef.pstFldLst, false, 6);
    // 删除数据
    Delete(dqlArg->testRelId, 11, false, 6);
    // 插入数据
    Insert(dqlArg->testRelId, 1, dqlArg->testRelDef.pstFldLst, false);
}
void *MetaSelectWithThead(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    // 调用所有元数据查询接口
    useAllMetaInterfaceofThread(dqlArg->testRelId, 0, 1000);
}
// 171.所有元数据接口+DB_SetDBDesc+DB_SetMaxRecNumOfTable和dml操作并发
TEST_F(MetadataforDB_Test, V1Com_028_171)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, false);
    pthread_t tid[2];
    DqlArgsT dqlArg;
    dqlArg.testRelId = testRelId1;
    dqlArg.testRelDef = testRelDef;
    
    ret = pthread_create(&tid[0], NULL, DmlOption, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid[1], NULL, MetaSelectWithThead, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    ret = pthread_join(tid[0], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid[1], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
void *DBcreate(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    for (int i = 0; i < 1000; i++) {
        ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
         // 建表
        DB_REL_DEF_STRU *testRelDef = NULL;
        ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &dqlArg->testRelId, testRelDef);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 删除表
        ret = DB_DropTbl(g_testDbId, dqlArg->testRelId, DROP_NOWAIT);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 删除库
        while (true)
        {
            ret = DB_CloseDB(g_testDbId);
            if (ret == VOS_ERRNO_DB_CLOSE_NOTALLOWED)
            {
                continue;
            }
            if (ret == DB_SUCCESS_V1)
            {
                break;
            }
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            break;
        }
        ret = DB_DropDB((VOS_UINT8 *)g_testDbName, DROP_NOWAIT);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
void *MetaSelectWithddlThead(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    // 调用所有元数据查询接口
    useAllMetaInterfaceofddlThread(dqlArg->testRelId, 0, 1000);
}
// 172.所有元数据接口+DB_SetDBDesc+DB_SetMaxRecNumOfTable和ddl操作并发
TEST_F(MetadataforDB_Test, V1Com_028_172)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    pthread_t tid[2];
    DqlArgsT dqlArg;
    dqlArg.testRelId = 0;
    ret = pthread_create(&tid[0], NULL, DBcreate, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid[1], NULL, MetaSelectWithddlThead, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    ret = pthread_join(tid[0], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid[1], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void *TPCDmlOption(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    // 更新数据
    Update(dqlArg->testRelId, 0, 11, dqlArg->testRelDef.pstFldLst, true, 6);
    // 删除数据
    Delete(dqlArg->testRelId, 11, true, 6);
    // 插入数据
    Insert(dqlArg->testRelId, 1, dqlArg->testRelDef.pstFldLst, true);
}
// 173.所有元数据接口+TPC_SetDBDesc+TPC_SetMaxRecNumOfTable和tpc的dml操作并发
TEST_F(MetadataforTPC_Test, V1Com_028_173)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);
    pthread_t tid[2];
    DqlArgsT dqlArg;
    dqlArg.testRelId = testRelId1;
    dqlArg.testRelDef = testRelDef;
    
    ret = pthread_create(&tid[0], NULL, TPCDmlOption, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid[1], NULL, MetaSelectWithThead, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    ret = pthread_join(tid[0], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid[1], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
void *TPCcreate(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    for (int i = 0; i < 1000; i++) {
        ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
         // 建表
        DB_REL_DEF_STRU *testRelDef = NULL;
        ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &dqlArg->testRelId, testRelDef);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 删除表
        ret = TPC_DropTbl(g_testDbId, dqlArg->testRelId, TPC_NOWAIT);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 删除库
        while (true)
        {
            ret = TPC_CloseDB(g_testDbId);
            if (ret == VOS_ERRNO_DB_CLOSE_NOTALLOWED)
            {
                continue;
            }
            if (ret == DB_SUCCESS_V1)
            {
                break;
            }
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            break;
        }
        ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, TPC_NOWAIT);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 174.所有元数据接口+TPC_SetDBDesc+TPC_SetMaxRecNumOfTable和tpc的ddl操作并发
TEST_F(MetadataforTPC_Test, V1Com_028_174)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    pthread_t tid[2];
    DqlArgsT dqlArg;
    dqlArg.testRelId = 0;
    ret = pthread_create(&tid[0], NULL, TPCcreate, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid[1], NULL, MetaSelectWithddlThead, &dqlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    ret = pthread_join(tid[0], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid[1], NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 175.DTS2025060325248联调问题单用例
TEST_F(MetadataforTPC_Test, V1Com_028_175)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB_BeginSelect开启查询句柄
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = 0;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;

    ret = DB_BeginSelect(g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB中执行插入
    Insert(testRelId1, 10, testRelDef.pstFldLst, true, g_testDbId, cdbID);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId1);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    ret = DB_FetchSelectRec(phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, DsBufGet.usRecNum);
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 176.DTS2025061819913联调问题单用例
TEST_F(MetadataforTPC_Test, V1Com_028_176)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 10, testRelDef.pstFldLst, true);

    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    DB_COND_STRU pstCond1;
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;

    *(uint32_t *)pstCond1.aCond[0].aucValue = 1;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 0;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = 0;
    pstDsBufSet.StdBuf.ulActLen = 0;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TblFieldDataDefT TblFieldDataDefSet;
    testSetAllField(&TblFieldDataDefSet, 1);
    TblFieldDataDefSet.f0 = 11;
    TblFieldDataDefSet.f2 = 11;
    memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
    pstDsBufSet.StdBuf.pucData = pucDataSet;
    VOS_UINT32 udpRecNum = 0;
    ret = TPC_UpdateRec(cdbID, g_testDbId, testRelId1, &pstCond1, &pstFldFilter, &pstDsBufSet, &udpRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, udpRecNum);
    TEST_V1_FREE(pucDataSet);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
