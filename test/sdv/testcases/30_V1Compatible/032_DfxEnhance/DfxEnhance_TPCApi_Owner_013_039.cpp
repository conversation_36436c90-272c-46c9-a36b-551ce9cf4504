/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : DFX增强
 Notes        : 接口测试 + 功能测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2025/04/01
**************************************************************************** */
#include "DfxEnhance.h"

DB_REL_DEF_STRU g_pstRelDef1;
DB_REL_DEF_STRU g_pstRelDef2;
VOS_UINT16 g_pusRelId1;
VOS_UINT16 g_pusRelId2;
uint32_t g_recLen1 = 0;
uint32_t g_recLen2 = 0;

class DfxEnhance : public testing::Test {
protected:
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
public:
    virtual void SetUp();
    virtual void TearDown();
};

void DfxEnhance::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    // 用于测试用例在设备失败的时候可以设置环境变量来打印一些具体的信息
    g_testcaseDebugModeVal = getenv("TESTCASE_DEBUG_MODE");
    if (g_testcaseDebugModeVal == NULL) {
        AW_FUN_Log(LOG_INFO, "if you want to printf testcase info, you can 'export TESTCASE_DEBUG_MODE=1'.");
    }
    system("rm -rf log");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = TPC_CreateDB((VOS_UINT8 *)g_V1_032_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_V1_032_DbName, &g_V1_032_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestTPC_CreateTbl(
        g_V1_032_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_V1_032_TblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录长度
    ret = TestGetTblRecLen(g_V1_032_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表2
    ret = TestTPC_CreateTbl(
        g_V1_032_TestDbId, "schema_file/default.json", &g_pusRelId2, &g_pstRelDef2, g_V1_032_TblName2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录长度
    ret = TestGetTblRecLen(g_V1_032_TestDbId, g_pusRelId2, &g_recLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId2, g_recLen2, &g_pstRelDef2, 1, 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}
void DfxEnhance::TearDown()
{
    DB_ERR_CODE ret;

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = TPC_DropTbl(g_V1_032_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&g_pstRelDef2);
    ret = TPC_DropTbl(g_V1_032_TestDbId, g_pusRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(g_V1_032_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_V1_032_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_CHECK_LOG_END();
}

// 013、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_InsertRec接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 101, 101);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_INSERT;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 102, 102);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestTPC_InsertRec(cdbId, g_V1_032_TestDbId, g_pusRelId2, g_recLen2, &g_pstRelDef2, 101, 110);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 103, 103);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_UpdateRec接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestTPC_UpdateRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 1, 1000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_UPDATE;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestTPC_UpdateRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 2, 2, 1000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestTPC_UpdateRec(cdbId, g_V1_032_TestDbId, g_pusRelId2, g_recLen2, &g_pstRelDef2, 1, 10, 1000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestTPC_UpdateRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 3, 3, 1000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_DeleteRec接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestTPC_DeleteRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, 1, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_DELETE;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestTPC_DeleteRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, 2, 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestTPC_DeleteRec(cdbId, g_V1_032_TestDbId, g_pusRelId2, 1, 10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestTPC_DeleteRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, 3, 3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_GetTblInfo接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_RELATION_INFO pstRelationInfo;
    ret = TPC_GetTblInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_TABLE_INFO;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_GetTblInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_GetTblInfo(g_V1_032_TestDbId, g_pusRelId2, &pstRelationInfo);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_GetTblInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_GetTblIdxInfo接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_INDEX_INFO *pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(sizeof(DB_INDEX_INFO) * 20);
    ret = TPC_GetTblIdxInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_INDEX_INFO;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_GetTblIdxInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_GetTblIdxInfo(g_V1_032_TestDbId, g_pusRelId2, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_GetTblIdxInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TEST_V1_FREE(pstAllIndexInfo);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_GetTblId接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    VOS_UINT16 pusRelId;
    ret = TPC_GetTblId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_TABLE_ID;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_GetTblId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_GetTblId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName2, &pusRelId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_GetTblId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_GetTblCount接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    VOS_UINT32 pulRelNum;
    ret = TPC_GetTblCount(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_TABLE_COUNT;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_GetTblCount(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_GetTblCount(g_V1_032_TestDbId, &pulRelNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_GetTblCount(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_GetTblColInfo接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_FIELD_INFO *pstAllFieldInfo = (DB_FIELD_INFO *)TEST_V1_MALLOC(sizeof(DB_FIELD_INFO) * 100);
    ret = TPC_GetTblColInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_TBLCOL_INFO;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_GetTblColInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_GetTblColInfo(g_V1_032_TestDbId, g_pusRelId2, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_GetTblColInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TEST_V1_FREE(pstAllFieldInfo);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_GetRelRecLen接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    uint32_t pulRecLen;
    ret = TPC_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1, &pulRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_RECLEN;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1, &pulRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换表再调用对应接口
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId2, &pulRecLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 提交事务，也代表其他接口
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1, &pulRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_CommitCDB接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMMIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_COMMIT;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMMIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestSetPerfTraceInfo(&perfTraceInfoExp1, cdbId, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_CommitCDB(cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMMIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 调用其它接口，不会生成日志
    uint32_t pulRecLen;
    ret = TPC_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1, &pulRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMMIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMMIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_FetchSelectRec接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    // 接口参数没有条件
    g_isSetCond = false;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_SELHANDLE phSelect;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_REC);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_FETCH_RECORD;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_REC);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    // * 2是要跳过BeginSelect的信息，不做校验
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1 * 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_BeginSelect(cdbId, g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &phSelect);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_FetchSelectRec(cdbId, phSelect, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_EndSelect(cdbId, phSelect);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_REC);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        // * 2是要跳过BeginSelect的信息，不做校验
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i * 2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_REC);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_SelectAllRec接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_SELECT;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_SelectAllRec(cdbId, g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_SelectAllRecEx接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_BUF_STRU pstBuf;
    ret = TestMallocBufEx(&pstBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_SELECT;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_SelectAllRecEx(cdbId, g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &pstBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeBufEx(&pstBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_SelectFirstRec接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_SELECT_FIRST;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_SelectFirstRec(cdbId, g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_RecordExist接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 0);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 0);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 调用对应接口，无日志生成
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_RECORD_EXSIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_RECORD_EXSIT;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_RECORD_EXSIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_RecordExist(cdbId, g_V1_032_TestDbId, g_pusRelId2, &pstCond);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_RECORD_EXSIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 0);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_RECORD_EXSIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_SelectAllRecByOrder接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 设置排序方式
    DB_SORT_STRU pstSort;
    TestSetSort(&pstSort);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = TPC_SelectAllRecByOrder(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_SELECT_ORDER;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_SelectAllRecByOrder(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_SelectAllRecByOrder(
            cdbId, g_V1_032_TestDbId, g_pusRelId2, &pstSort, &pstCond, &pstFldFilter, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_SelectAllRecByOrder(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_SelectAllRecByOrderEx接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 设置排序方式
    DB_SORT_STRU pstSort;
    TestSetSort(&pstSort);

    // 存放数据
    DB_BUF_STRU pstBuf;
    ret = TestMallocBufEx(&pstBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = TPC_SelectAllRecByOrderEx(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_SELECT_ORDER;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_SelectAllRecByOrderEx(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_SelectAllRecByOrderEx(
            cdbId, g_V1_032_TestDbId, g_pusRelId2, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_SelectAllRecByOrderEx(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeBufEx(&pstBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_SelectAllRecByPath接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    DB_EDGE_DEF_STRU edgeDef;
    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };
    TestSetEdgeDef(&edgeDef, true, g_pusRelId1, g_pusRelId2, fieldNum, fieldArray1, fieldNum, fieldArray2);

    // 建边表
    const char *edgeTblName = "edge1";
    ret = TPC_CreateEdge(g_V1_032_TestDbId, (uint8_t *)edgeTblName, &edgeDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    // 接口参数没有条件
    g_isSetCond = false;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 调用对应接口，无日志生成
    DB_PATH_STRU pstPath;
    uint32_t tblIdArray[2] = { g_pusRelId1, g_pusRelId2 };
    TestSetEdgeTblSelectPath(&pstPath, tblIdArray, 1, &pstFldFilter, fieldNum, fieldArray1, fieldArray2);
    DB_MUTIL_BUF_STRU *mutilDataBuff = NULL;
    ret = TPC_SelectAllRecByPath(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, tblIdArray[0], &pstCond, &pstFldFilter, &pstPath, &mutilDataBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeBufMemById(mutilDataBuff->memId);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_TOPO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_SELECT_TOPO;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_SelectAllRecByPath(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, tblIdArray[0], &pstCond, &pstFldFilter, &pstPath, &mutilDataBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeBufMemById(mutilDataBuff->memId);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_TOPO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_SelectAllRecByPath(
            cdbId, g_V1_032_TestDbId, tblIdArray[0], &pstCond, &pstFldFilter, &pstPath, &mutilDataBuff);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        TPC_FreeBufMemById(mutilDataBuff->memId);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_TOPO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId1, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_SelectAllRecByPath(
        TPC_GLOBAL_CDB, g_V1_032_TestDbId, tblIdArray[0], &pstCond, &pstFldFilter, &pstPath, &mutilDataBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeBufMemById(mutilDataBuff->memId);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_TOPO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    ret = TPC_DropEdge(g_V1_032_TestDbId, (uint8_t *)edgeTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_FetchSelectTopoRec接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    DB_EDGE_DEF_STRU edgeDef;
    uint32_t fieldNum = 1;
    VOS_UINT8 fieldArray1[1] = { 0 };
    VOS_UINT8 fieldArray2[1] = { 0 };
    TestSetEdgeDef(&edgeDef, true, g_pusRelId1, g_pusRelId2, fieldNum, fieldArray1, fieldNum, fieldArray2);

    // 建边表
    const char *edgeTblName = "edge1";
    ret = TPC_CreateEdge(g_V1_032_TestDbId, (uint8_t *)edgeTblName, &edgeDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    // 接口参数没有条件
    g_isSetCond = false;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 调用对应接口，无日志生成
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *mutilDataBuff = NULL;
    uint32_t fetchCount = 1;
    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &mutilDataBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeBufMemById(mutilDataBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_TOPO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_FETCH_TOPO;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &mutilDataBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeBufMemById(mutilDataBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_TOPO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_BeginSelectByTopo(cdbId, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_FetchSelectTopoRec(cdbId, phSelect, fetchCount, &mutilDataBuff);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        TPC_FreeBufMemById(mutilDataBuff->memId);
        ret = TPC_EndTopoSelect(cdbId, phSelect);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_TOPO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId1, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &mutilDataBuff);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeBufMemById(mutilDataBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_TOPO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    ret = TPC_DropEdge(g_V1_032_TestDbId, (uint8_t *)edgeTblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_CompressTable接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestTPC_CompressTable(g_V1_032_TestDbId, g_pusRelId1, 1, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMPRESS_TBL);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_COMPRESS;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestTPC_CompressTable(g_V1_032_TestDbId, g_pusRelId1, 1, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMPRESS_TBL);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TestTPC_CompressTable(g_V1_032_TestDbId, g_pusRelId2, 1, 0);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMPRESS_TBL);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestTPC_CompressTable(g_V1_032_TestDbId, g_pusRelId1, 1, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_COMPRESS_TBL);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_CountMatchingRecs接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 调用对应接口，无日志生成
    uint32_t matchRecNum;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &matchRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_COUNT;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &matchRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_CountMatchingRecs(cdbId, g_V1_032_TestDbId, g_pusRelId2, &pstCond, &matchRecNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, cdbId, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_V1_032_TestDbId, g_pusRelId1, &pstCond, &matchRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_BkpPhy接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    const char *path = "./exp.txt";
    ret = TPC_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_IMPEXP;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_BkpPhyEx接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    const char *path = "./exp.txt";
    ret = TPC_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_IMPEXP;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_PhyBkp2接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_BAKPHY_OPTIONS_STRU pstBakPhyOpts = { 0 };
    pstBakPhyOpts.pstFileSaveOpt = &saveOpt;
    const char *path = "./exp.txt";
    ret = TPC_PhyBkp2(g_V1_032_TestDbId, (uint8_t *)path, &pstBakPhyOpts);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_IMPEXP;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_PhyBkp2(g_V1_032_TestDbId, (uint8_t *)path, &pstBakPhyOpts);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_PhyBkp2(g_V1_032_TestDbId, (uint8_t *)path, &pstBakPhyOpts);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_PhyBkp2(g_V1_032_TestDbId, (uint8_t *)path, &pstBakPhyOpts);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_BkpPhyWithDataConvHook接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    const char *path = "./exp.txt";
    ret = TPC_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_IMPEXP;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    uint32_t cdbId;
    ret = TPC_BeginCDB(g_V1_032_TestDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_Restore接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 先导出数据，得到要导入的路径
    const char *path = "./exp.txt";
    ret = TPC_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    ret = TPC_Restore((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_IMPEXP;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_Restore((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_Restore((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;


    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_Restore((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039、枚举类型为枚举类型为DB_TPC_SYSVIEW_PERF_TRACE
// TPC_RestoreWithDataConvHookEx接口的性能开关，调用对应接口和其他接口，再关掉开关（DB_ PERF_TRACE_TYPE_OFF），调用对应接口
TEST_F(DfxEnhance, V1Com_032_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // 65535表示参数没有传表id的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 先导出数据，得到要导入的路径
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    const char *path = "./exp.txt";
    ret = TPC_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    ret = TPC_RestoreWithDataConvHook(
        (uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_IMPEXP;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TPC_RestoreWithDataConvHook(
        (uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = TPC_RestoreWithDataConvHook(
            (uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TPC_RestoreWithDataConvHook(
        (uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}
