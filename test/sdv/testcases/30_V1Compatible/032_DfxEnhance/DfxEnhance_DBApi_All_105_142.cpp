/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : DFX增强
 Notes        : 接口测试 + 功能测试
                统一的测试步骤：打开性能开关，调用对应接口，再关掉开关，调用对应接口
 Author       : nonglibin nWX860399
 Modification :
 create       : 2025/04/01
**************************************************************************** */
#include "DfxEnhance.h"

DB_REL_DEF_STRU g_pstRelDef1;
DB_REL_DEF_STRU g_pstRelDef2;
VOS_UINT16 g_pusRelId1;
VOS_UINT16 g_pusRelId2;
uint32_t g_recLen1 = 0;
uint32_t g_recLen2 = 0;

class DfxEnhance : public testing::Test {
protected:
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
public:
    virtual void SetUp();
    virtual void TearDown();
};

void DfxEnhance::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    // 用于测试用例在设备失败的时候可以设置环境变量来打印一些具体的信息
    g_testcaseDebugModeVal = getenv("TESTCASE_DEBUG_MODE");
    if (g_testcaseDebugModeVal == NULL) {
        AW_FUN_Log(LOG_INFO, "if you want to printf testcase info, you can 'export TESTCASE_DEBUG_MODE=1'.");
    }
    system("rm -rf log");
    DB_ERR_CODE ret;

    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestDB_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    ret = DB_CreateDB((VOS_UINT8 *)g_V1_032_DbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_V1_032_DbName, &g_V1_032_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    ret = TestDB_CreateTbl(
        g_V1_032_TestDbId, "schema_file/default.json", &g_pusRelId1, &g_pstRelDef1, g_V1_032_TblName);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录长度
    ret = TestDBGetTblRecLen(g_V1_032_TestDbId, g_pusRelId1, &g_recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 100, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表2
    ret = TestDB_CreateTbl(
        g_V1_032_TestDbId, "schema_file/default.json", &g_pusRelId2, &g_pstRelDef2, g_V1_032_TblName2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录长度
    ret = TestDBGetTblRecLen(g_V1_032_TestDbId, g_pusRelId2, &g_recLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId2, g_recLen2, &g_pstRelDef2, 1, 100, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}
void DfxEnhance::TearDown()
{
    DB_ERR_CODE ret;

    // 删表
    TestFreeTblStructDef(&g_pstRelDef1);
    ret = DB_DropTbl(g_V1_032_TestDbId, g_pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&g_pstRelDef2);
    ret = DB_DropTbl(g_V1_032_TestDbId, g_pusRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = DB_CloseDB(g_V1_032_TestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_V1_032_DbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestDB_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_CHECK_LOG_END();
}

// 105、DB_PERF_TRACE_TYPE_ALL && DB_InsertRec
TEST_F(DfxEnhance, V1Com_032_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 101, 101, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 102, 102, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId2, g_recLen2, &g_pstRelDef2, 101, 110, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 103, 103, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 106、DB_PERF_TRACE_TYPE_ALL && DBS_LocalAddRecordForDatabase
TEST_F(DfxEnhance, V1Com_032_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 101, 101, TEST_DBS_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 102, 102, TEST_DBS_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId2, g_recLen2, &g_pstRelDef2, 101, 110, TEST_DBS_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestDB_InsertRec(g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 103, 103, TEST_DBS_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_INSERT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 107、DB_PERF_TRACE_TYPE_ALL && DB_UpdateRec
TEST_F(DfxEnhance, V1Com_032_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestDB_UpdateRec(
        g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 1, 1000, TEST_DB_API_TYPE, true);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestDB_UpdateRec(
        g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 2, 2, 1000, TEST_DB_API_TYPE, true);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    ret = TestDB_UpdateRec(
        g_V1_032_TestDbId, g_pusRelId2, g_recLen2, &g_pstRelDef2, 1, 10, 1000, TEST_DB_API_TYPE, true);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestDB_UpdateRec(
        g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 3, 3, 1000, TEST_DB_API_TYPE, true);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 108、DB_PERF_TRACE_TYPE_ALL && DBS_LocalModRecordForDatabase
TEST_F(DfxEnhance, V1Com_032_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestDB_UpdateRec(
        g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 1, 1, 1000, TEST_DBS_API_TYPE, true);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestDB_UpdateRec(
        g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 2, 2, 1000, TEST_DBS_API_TYPE, true);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    ret = TestDB_UpdateRec(
        g_V1_032_TestDbId, g_pusRelId2, g_recLen2, &g_pstRelDef2, 1, 10, 1000, TEST_DBS_API_TYPE, true);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestDB_UpdateRec(
        g_V1_032_TestDbId, g_pusRelId1, g_recLen1, &g_pstRelDef1, 3, 3, 1000, TEST_DBS_API_TYPE, true);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_UPDATE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 109、DB_PERF_TRACE_TYPE_ALL && DB_DeleteRec
TEST_F(DfxEnhance, V1Com_032_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestDB_DeleteRec(g_V1_032_TestDbId, g_pusRelId1, 1, 1, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestDB_DeleteRec(g_V1_032_TestDbId, g_pusRelId1, 2, 2, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    ret = TestDB_DeleteRec(g_V1_032_TestDbId, g_pusRelId2, 1, 10, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestDB_DeleteRec(g_V1_032_TestDbId, g_pusRelId1, 3, 3, TEST_DB_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 110、DB_PERF_TRACE_TYPE_ALL && DBS_LocalDelRecordForDatabase
TEST_F(DfxEnhance, V1Com_032_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 调用对应接口，无日志生成
    ret = TestDB_DeleteRec(g_V1_032_TestDbId, g_pusRelId1, 1, 1, TEST_DBS_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = TestDB_DeleteRec(g_V1_032_TestDbId, g_pusRelId1, 2, 2, TEST_DBS_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    ret = TestDB_DeleteRec(g_V1_032_TestDbId, g_pusRelId2, 1, 10, TEST_DBS_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = TestDB_DeleteRec(g_V1_032_TestDbId, g_pusRelId1, 3, 3, TEST_DBS_API_TYPE);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DELETE);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 111、DB_PERF_TRACE_TYPE_ALL && DB_GetTblInfo
TEST_F(DfxEnhance, V1Com_032_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_RELATION_INFO pstRelationInfo;
    ret = DB_GetTblInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_GetTblInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_GetTblInfo(g_V1_032_TestDbId, g_pusRelId2, &pstRelationInfo);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_GetTblInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 112、DB_PERF_TRACE_TYPE_ALL && DBS_GetRelationInfo
TEST_F(DfxEnhance, V1Com_032_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_RELATION_INFO pstRelationInfo;
    ret = DBS_GetRelationInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBS_GetRelationInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBS_GetRelationInfo(g_V1_032_TestDbId, g_pusRelId2, &pstRelationInfo);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBS_GetRelationInfo(g_V1_032_TestDbId, g_pusRelId1, &pstRelationInfo);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 113、DB_PERF_TRACE_TYPE_ALL && DB_GetTblIdxInfo
TEST_F(DfxEnhance, V1Com_032_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_INDEX_INFO *pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(sizeof(DB_INDEX_INFO) * 20);
    ret = DB_GetTblIdxInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_GetTblIdxInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_GetTblIdxInfo(g_V1_032_TestDbId, g_pusRelId2, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_GetTblIdxInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TEST_V1_FREE(pstAllIndexInfo);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 114、DB_PERF_TRACE_TYPE_ALL && DBINIT_GetAllIndexInfo
TEST_F(DfxEnhance, V1Com_032_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_INDEX_INFO *pstAllIndexInfo = (DB_INDEX_INFO *)TEST_V1_MALLOC(sizeof(DB_INDEX_INFO) * 20);
    ret = DBINIT_GetAllIndexInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBINIT_GetAllIndexInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBINIT_GetAllIndexInfo(g_V1_032_TestDbId, g_pusRelId2, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBINIT_GetAllIndexInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllIndexInfo, sizeof(DB_INDEX_INFO) * 20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TEST_V1_FREE(pstAllIndexInfo);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_INDEX_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 115、DB_PERF_TRACE_TYPE_ALL && DB_GetTblId
TEST_F(DfxEnhance, V1Com_032_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    VOS_UINT16 pusRelId;
    ret = DB_GetTblId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_GetTblId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_GetTblId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName2, &pusRelId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_GetTblId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 116、DB_PERF_TRACE_TYPE_ALL && DBINIT_GetRelationId
TEST_F(DfxEnhance, V1Com_032_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    VOS_UINT16 pusRelId;
    ret = DBINIT_GetRelationId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBINIT_GetRelationId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBINIT_GetRelationId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName2, &pusRelId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBINIT_GetRelationId(g_V1_032_TestDbId, (uint8_t *)g_V1_032_TblName, &pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_ID);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 117、DB_PERF_TRACE_TYPE_ALL && DB_GetTblCount
TEST_F(DfxEnhance, V1Com_032_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    VOS_UINT32 pulRelNum;
    ret = DB_GetTblCount(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_GetTblCount(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_GetTblCount(g_V1_032_TestDbId, &pulRelNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_GetTblCount(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 118、DB_PERF_TRACE_TYPE_ALL && DBINIT_GetNumOfRelations
TEST_F(DfxEnhance, V1Com_032_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    VOS_UINT32 pulRelNum;
    ret = DBINIT_GetNumOfRelations(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBINIT_GetNumOfRelations(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBINIT_GetNumOfRelations(g_V1_032_TestDbId, &pulRelNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBINIT_GetNumOfRelations(g_V1_032_TestDbId, &pulRelNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 119、DB_PERF_TRACE_TYPE_ALL && DB_GetTblColInfo
TEST_F(DfxEnhance, V1Com_032_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_FIELD_INFO *pstAllFieldInfo = (DB_FIELD_INFO *)TEST_V1_MALLOC(sizeof(DB_FIELD_INFO) * 100);
    ret = DB_GetTblColInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_GetTblColInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_GetTblColInfo(g_V1_032_TestDbId, g_pusRelId2, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_GetTblColInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TEST_V1_FREE(pstAllFieldInfo);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 120、DB_PERF_TRACE_TYPE_ALL && DBINIT_GetAllFieldInfo
TEST_F(DfxEnhance, V1Com_032_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_FIELD_INFO *pstAllFieldInfo = (DB_FIELD_INFO *)TEST_V1_MALLOC(sizeof(DB_FIELD_INFO) * 100);
    ret = DBINIT_GetAllFieldInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBINIT_GetAllFieldInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBINIT_GetAllFieldInfo(g_V1_032_TestDbId, g_pusRelId2, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBINIT_GetAllFieldInfo(g_V1_032_TestDbId, g_pusRelId1, pstAllFieldInfo, sizeof(DB_FIELD_INFO) * 100);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TEST_V1_FREE(pstAllFieldInfo);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_TABLE_COL_INFO);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 121、DB_PERF_TRACE_TYPE_ALL && DBS_GetRelRecLen
TEST_F(DfxEnhance, V1Com_032_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    uint32_t pulRecLen;
    ret = DBS_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1, &pulRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBS_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1, &pulRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBS_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId2, &pulRecLen);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBS_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1, &pulRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 122、DB_PERF_TRACE_TYPE_ALL && DBDDL_GetRelRecLen
TEST_F(DfxEnhance, V1Com_032_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    T_SIZE pulRecLen;
    pulRecLen = DBDDL_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1);
    V1_AW_MACRO_ASSERT_EQ_INT(g_recLen1, pulRecLen);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    pulRecLen = DBDDL_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1);
    V1_AW_MACRO_ASSERT_EQ_INT(g_recLen1, pulRecLen);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        pulRecLen = DBDDL_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId2);
        V1_AW_MACRO_ASSERT_EQ_INT(g_recLen2, pulRecLen);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    pulRecLen = DBDDL_GetRelRecLen(g_V1_032_TestDbId, g_pusRelId1);
    V1_AW_MACRO_ASSERT_EQ_INT(g_recLen1, pulRecLen);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_GET_REC_LEN);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 123、DB_PERF_TRACE_TYPE_ALL && DB_FetchSelectRec
TEST_F(DfxEnhance, V1Com_032_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    // 接口参数没有条件
    g_isSetCond = false;

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_REC);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_BeginSelect(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_REC);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    // * 2是要跳过BeginSelect的信息，不做校验
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1 * 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_BeginSelect(g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &phSelect);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_FetchSelectRec(phSelect, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = DB_EndSelect(phSelect);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_REC);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        // * 2是要跳过BeginSelect的信息，不做校验
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i * 2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_BeginSelect(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_FETCH_REC);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 124、DB_PERF_TRACE_TYPE_ALL && DB_SelectAllRec
TEST_F(DfxEnhance, V1Com_032_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = DB_SelectAllRec(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_SelectAllRec(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_SelectAllRec(g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_SelectAllRec(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 125、DB_PERF_TRACE_TYPE_ALL && DB_SelectAllRecEx
TEST_F(DfxEnhance, V1Com_032_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_BUF_STRU pstBuf;
    ret = TestMallocBufEx(&pstBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = DB_SelectAllRecEx(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_SelectAllRecEx(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_SelectAllRecEx(g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &pstBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_SelectAllRecEx(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeBufEx(&pstBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 126、DB_PERF_TRACE_TYPE_ALL && DBS_LocalGetAllRecordForDatabase
TEST_F(DfxEnhance, V1Com_032_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = DBS_LocalGetAllRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DBS_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBS_LocalGetAllRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DBS_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBS_LocalGetAllRecordForDatabase(g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DBS_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBS_LocalGetAllRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DBS_SELECT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 127、DB_PERF_TRACE_TYPE_ALL && DB_SelectFirstRec
TEST_F(DfxEnhance, V1Com_032_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = DB_SelectFirstRec(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_SelectFirstRec(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_SelectFirstRec(g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_SelectFirstRec(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 128、DB_PERF_TRACE_TYPE_ALL && DBS_LocalGetFirstRecordForDatabase
TEST_F(DfxEnhance, V1Com_032_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = DBS_LocalGetFirstRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DBS_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBS_LocalGetFirstRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DBS_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBS_LocalGetFirstRecordForDatabase(g_V1_032_TestDbId, g_pusRelId2, &pstCond, &pstFldFilter, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DBS_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBS_LocalGetFirstRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DBS_SELECT_FIRST);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 129、DB_PERF_TRACE_TYPE_ALL && DB_RecordExist
TEST_F(DfxEnhance, V1Com_032_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 0);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 0);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 调用对应接口，无日志生成
    ret = DB_RecordExist(g_V1_032_TestDbId, g_pusRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_RECORD_EXSIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_RecordExist(g_V1_032_TestDbId, g_pusRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_RECORD_EXSIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_RecordExist(g_V1_032_TestDbId, g_pusRelId2, &pstCond);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_RECORD_EXSIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 0);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_RecordExist(g_V1_032_TestDbId, g_pusRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_RECORD_EXSIT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 130、DB_PERF_TRACE_TYPE_ALL && DB_SelectAllRecByOrder
TEST_F(DfxEnhance, V1Com_032_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 设置排序方式
    DB_SORT_STRU pstSort;
    TestSetSort(&pstSort);

    // 存放数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = DB_SelectAllRecByOrder(g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_SelectAllRecByOrder(g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_SelectAllRecByOrder(g_V1_032_TestDbId, g_pusRelId2, &pstSort, &pstCond, &pstFldFilter, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_SelectAllRecByOrder(g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_SELECT_ORDER);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 131、DB_PERF_TRACE_TYPE_ALL && DB_SelectAllRecByOrderEx
TEST_F(DfxEnhance, V1Com_032_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 设置映射字段
    DB_FIELDFILTER_STRU pstFldFilter;
    TestSetFldFilter(&pstFldFilter);

    // 设置排序方式
    DB_SORT_STRU pstSort;
    TestSetSort(&pstSort);

    // 存放数据
    DB_BUF_STRU pstBuf;
    ret = TestMallocBufEx(&pstBuf, g_recLen1, 1, g_recLen1 * 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    ret = DB_SelectAllRecByOrderEx(g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DB_SELECT_ORDER_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_SelectAllRecByOrderEx(g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DB_SELECT_ORDER_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_SelectAllRecByOrderEx(g_V1_032_TestDbId, g_pusRelId2, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DB_SELECT_ORDER_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_SelectAllRecByOrderEx(g_V1_032_TestDbId, g_pusRelId1, &pstSort, &pstCond, &pstFldFilter, &pstBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeBufEx(&pstBuf);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_DB_SELECT_ORDER_EX);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 132、DB_PERF_TRACE_TYPE_ALL && DB_CountMatchingRecs
TEST_F(DfxEnhance, V1Com_032_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 调用对应接口，无日志生成
    uint32_t matchRecNum;
    ret = DB_CountMatchingRecs(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &matchRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_CountMatchingRecs(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &matchRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_CountMatchingRecs(g_V1_032_TestDbId, g_pusRelId2, &pstCond, &matchRecNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_CountMatchingRecs(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &matchRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 133、DB_PERF_TRACE_TYPE_ALL && DBS_LocalExistRecordForDatabase
TEST_F(DfxEnhance, V1Com_032_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId1, 1, 0, 0, 1);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        g_pusRelId2, 1, 0, 0, 1);

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 1 };
    uint32_t conVal = 1;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 调用对应接口，无日志生成
    uint32_t existRecNum;
    ret = DBS_LocalExistRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &existRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DBS_LocalExistRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &existRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DBS_LocalExistRecordForDatabase(g_V1_032_TestDbId, g_pusRelId2, &pstCond, &existRecNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            g_pusRelId2, i, 0, 0, 1);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DBS_LocalExistRecordForDatabase(g_V1_032_TestDbId, g_pusRelId1, &pstCond, &existRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_COUNT);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 134、DB_PERF_TRACE_TYPE_ALL && DB_BkpPhy
TEST_F(DfxEnhance, V1Com_032_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    const char *path = "./exp.txt";
    ret = DB_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 135、DB_PERF_TRACE_TYPE_ALL && DB_PhyBkp2
TEST_F(DfxEnhance, V1Com_032_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_BAKPHY_OPTIONS_STRU pstBakPhyOpts = { 0 };
    pstBakPhyOpts.pstFileSaveOpt = &saveOpt;
    const char *path = "./exp.txt";
    ret = DB_PhyBkp2(g_V1_032_TestDbId, (uint8_t *)path, &pstBakPhyOpts);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_PhyBkp2(g_V1_032_TestDbId, (uint8_t *)path, &pstBakPhyOpts);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_PhyBkp2(g_V1_032_TestDbId, (uint8_t *)path, &pstBakPhyOpts);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_PhyBkp2(g_V1_032_TestDbId, (uint8_t *)path, &pstBakPhyOpts);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 136、DB_PERF_TRACE_TYPE_ALL && DB_BkpPhyEx
TEST_F(DfxEnhance, V1Com_032_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    const char *path = "./exp.txt";
    ret = DB_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 137、DB_PERF_TRACE_TYPE_ALL && DB_BkpPhyWithDataConvHook
TEST_F(DfxEnhance, V1Com_032_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    const char *path = "./exp.txt";
    ret = DB_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, g_V1_032_TestDbId,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_EXP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 138、DB_PERF_TRACE_TYPE_ALL && DB_Restore
TEST_F(DfxEnhance, V1Com_032_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 先导出数据，得到要导入的路径
    const char *path = "./exp.txt";
    ret = DB_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    ret = DB_Restore((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_Restore((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_Restore((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_Restore((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 139、DB_PERF_TRACE_TYPE_ALL && DB_RestoreEx
TEST_F(DfxEnhance, V1Com_032_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    const char *path = "./exp.txt";
    ret = DB_BkpPhyEx(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    ret = DB_RestoreEx((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_RestoreEx((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_RestoreEx((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_RestoreEx((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 140、DB_PERF_TRACE_TYPE_ALL && DB_RestoreWithDataConvHook
TEST_F(DfxEnhance, V1Com_032_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    const char *path = "./exp.txt";
    ret = DB_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    ret = DB_RestoreWithDataConvHook(
        (uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_RestoreWithDataConvHook(
        (uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_RestoreWithDataConvHook(
            (uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, NULL);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_RestoreWithDataConvHook(
        (uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 141、DB_PERF_TRACE_TYPE_ALL && DB_RestoreWithDataConvHookEx
TEST_F(DfxEnhance, V1Com_032_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 调用对应接口，无日志生成
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    const char *path = "./exp.txt";
    ret = DB_BkpPhyWithDataConvHook(g_V1_032_TestDbId, (uint8_t *)path, &saveOpt, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    ret = DB_RestoreWithDataConvHookEx((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE,
        &stDbConfig, NULL, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_RestoreWithDataConvHookEx((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE,
        &stDbConfig, NULL, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_RestoreWithDataConvHookEx((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE,
            &stDbConfig, NULL, DB_DATA_STORAGE_RAM);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_RestoreWithDataConvHookEx((uint8_t *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, DB_RESTORETYPE_REPLACE,
        &stDbConfig, NULL, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 142、DB_PERF_TRACE_TYPE_ALL && DB_RestoreDB2
TEST_F(DfxEnhance, V1Com_032_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    uint32_t expectFindLogCnt = 0;
    uint32_t actualFindLogCnt = 0;
    V1DfxPerfTraceInfoT perfTraceInfoGet;
    uint64_t totalTime = 0;

    // TEST_INVALID_REC_NUM为不涉及的显示值
    V1DfxPerfTraceInfoT perfTraceInfoExp1;
    TestSetPerfTraceInfo(&perfTraceInfoExp1, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);
    V1DfxPerfTraceInfoT perfTraceInfoExp2;
    TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
        TEST_INVALID_TBL_ID, 1, 0, 0, TEST_INVALID_REC_NUM);

    // 先导出数据，得到要导入的路径
    const char *path = "./exp.txt";
    ret = DB_BkpPhy(g_V1_032_TestDbId, (uint8_t *)path);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，无日志生成
    DB_REG_FEATURE_STRU stFeature = {0};
    ret = DB_RegDbRestoreConfig(false, &stFeature);  // 是否为持久化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RegDbRestoreTypeCfg(DB_RESTORETYPE_REPLACE, &stFeature);  // 导入模式
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_RestoreDB2((VOS_UINT8 *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 打开对应接口的性能开关
    DB_TPC_SYSVIEW_TYPE_ENUM sysviewType = DB_TPC_SYSVIEW_PERF_TRACE;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs;
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_ALL;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，有日志生成
    ret = DB_RestoreDB2((VOS_UINT8 *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    expectFindLogCnt += 1;

    // 检查日志，拿第1条日志
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
    ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    totalTime += perfTraceInfoGet.curTime;

    // 切换CDB、表再调用对应接口，调用对应接口，有日志生成
    for (uint32_t i = 0; i < RUN_API_DEFAULT_CNT; i++) {
        ret = DB_RestoreDB2((VOS_UINT8 *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, &stFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    expectFindLogCnt += RUN_API_DEFAULT_CNT;

    // 检查日志，有日志打印，从第2条开始拿
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);
    for (uint32_t i = 2; i <= RUN_API_DEFAULT_CNT + 1; i++) {
        // 设置预期值，count会每次调用一次增加1
        TestSetPerfTraceInfo(&perfTraceInfoExp2, TPC_GLOBAL_CDB, (char *)g_V1_032_DbName, TEST_INVALID_DB_ID,
            TEST_INVALID_TBL_ID, i, 0, 0, TEST_INVALID_REC_NUM);
        memset_s(&perfTraceInfoGet, sizeof(V1DfxPerfTraceInfoT), 0x00, sizeof(V1DfxPerfTraceInfoT));
        ret = TestGetPerfTraceInfoFromFile(NULL, &perfTraceInfoGet, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestCheckPerfTraceInfo(&perfTraceInfoGet, &perfTraceInfoExp2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        totalTime += perfTraceInfoGet.curTime;
    }

    // 关闭对应接口的性能开关
    sysviewArgs.perfTraceType = DB_PERF_TRACE_TYPE_OFF;
    ret = TPC_Sysview(sysviewType, &sysviewArgs, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 调用对应接口，日志数量不会增加
    ret = DB_RestoreDB2((VOS_UINT8 *)path, (VOS_UINT8 *)g_V1_032_DbName, NULL, &stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    ret = DB_FreeRegFeatureStruct(&stFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 检查日志数量
    actualFindLogCnt = TestGetPerfLogCnt(NULL, TEST_API_MATCH_IMP);
    V1_AW_MACRO_ASSERT_EQ_INT(expectFindLogCnt, actualFindLogCnt);

    // 删除文件
    char cmd[1024] = {0};
    sprintf_s(cmd, sizeof(cmd), "rm -f %s", path);
    system(cmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}
