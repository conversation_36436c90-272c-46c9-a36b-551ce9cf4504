
#include "ImportExport.h"

class FunctionTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void FunctionTest::SetUpTestCase()
{}

void FunctionTest::TearDownTestCase()
{}

void FunctionTest::SetUp()
{
    // 初始化
    int ret = TestTPC_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.db");
    system("rm -rf ./perFilePath/*.db");
    system("rm -rf ./filePath/*.output");
    AW_CHECK_LOG_BEGIN();
}

void FunctionTest::TearDown()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.db");
    system("rm -rf ./perFilePath/*.db");
    system("rm -rf ./filePath/*.output");
    AW_CHECK_LOG_END();
}

// 普通db + 同1个DB
// TPC_BkpPhyEx接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret); 

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllTypeBig(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 同1个DB
// TPC_BkpPhyWithDataConvHook接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 同1个DB
// TPC_PhyBkp2接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 同1个DB
// TPC_BkpPhyEx接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 同1个DB
// TPC_BkpPhyEx接口导出只含临时表数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic_temp.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(ulDbId, usRelId, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 同1个DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 普通db + 同1个DB
// beginCDB后dml操作，未提交事务TPC_BkpPhyEx接口导出，提交事务再查询数据修改成功，TPC_RestoreWithDataConvHook接口导入后的数据不含CDB修改
TEST_F(FunctionTest, V1Com_022_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_COND_STRU pstCond = {0};
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    for (int i = 0; i < insertNum; i++) {
        ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 19;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
        VOS_UINT32 pulRecNum = 0;
        ret = TPC_UpdateRec(cdbID, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE,
        &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 同1个DB
// TPC_BkpPhy接口导出表数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        ret = TPC_BkpPhy(ulDbId, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 同1个DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_Restore接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 普通db + 不同DB
// TPC_BkpPhyEx接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllTypeBig(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 不同DB
// TPC_BkpPhyWithDataConvHook接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 不同DB
// TPC_PhyBkp2接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 不同DB
// TPC_BkpPhyEx接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 不同DB
// TPC_BkpPhyEx接口导出只含临时表数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic_temp.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

   // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(ulDbId, usRelId, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 不同DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 普通db + 不同DB
// beginCDB后dml操作，未提交事务TPC_BkpPhyEx接口导出，提交事务再查询数据修改成功，TPC_RestoreWithDataConvHook接口导入后的数据不含CDB修改
TEST_F(FunctionTest, V1Com_022_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_COND_STRU pstCond = {0};
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    for (int i = 0; i < insertNum; i++) {
        ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 19;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
        VOS_UINT32 pulRecNum = 0;
        ret = TPC_UpdateRec(cdbID, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE,
        &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 不同DB
// TPC_BkpPhy接口导出表数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        ret = TPC_BkpPhy(ulDbId, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + 不同DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_Restore接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 同1个DB
// TPC_BkpPhyEx接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)dbDir, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 同1个DB
// TPC_BkpPhyWithDataConvHook接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)dbDir, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 同1个DB
// TPC_PhyBkp2接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)dbDir, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllTypeBig(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 同1个DB
// TPC_BkpPhyEx接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)dbDir, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 不同DB
// TPC_BkpPhyEx接口导出只含临时表数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic_temp.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(ulDbId, usRelId, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 同1个DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 持久化db + 同1个DB
// beginCDB后dml操作，未提交事务TPC_BkpPhyEx接口导出，提交事务再查询数据修改成功，TPC_RestoreWithDataConvHook接口导入后的数据不含CDB修改
TEST_F(FunctionTest, V1Com_022_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_COND_STRU pstCond = {0};
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    for (int i = 0; i < insertNum; i++) {
        ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 19;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
        VOS_UINT32 pulRecNum = 0;
        ret = TPC_UpdateRec(cdbID, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
        &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 同1个DB
// TPC_BkpPhy接口导出表数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB( (VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        ret = TPC_BkpPhy(ulDbId, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 同1个DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_Restore接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 持久化db + 不同DB
// TPC_BkpPhyEx接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    // 再次OpenDB后导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllTypeBig(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 不同DB
// TPC_BkpPhyWithDataConvHook接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 不同DB
// TPC_PhyBkp2接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 不同DB
// TPC_BkpPhyEx接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 不同DB
// TPC_BkpPhyEx接口导出只含临时表数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic_temp.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

   // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(ulDbId, usRelId, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 不同DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_146)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 持久化db + 不同DB
// beginCDB后dml操作，未提交事务TPC_BkpPhyEx接口导出，提交事务再查询数据修改成功，TPC_RestoreWithDataConvHook接口导入后的数据不含CDB修改
TEST_F(FunctionTest, V1Com_022_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_COND_STRU pstCond = {0};
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    for (int i = 0; i < insertNum; i++) {
        ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 19;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
        VOS_UINT32 pulRecNum = 0;
        ret = TPC_UpdateRec(cdbID, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
        &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 不同DB
// TPC_BkpPhy接口导出表数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_148)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        ret = TPC_BkpPhy(ulDbId, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + 不同DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_Restore接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// TPC_BkpPhyEx接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_150)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    // 再次OpenDB后导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllTypeBig(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// TPC_BkpPhyWithDataConvHook接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// TPC_PhyBkp2接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_152)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// TPC_BkpPhyEx接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// TPC_BkpPhyEx接口导出只含临时表数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_154)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic_temp.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

   // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(ulDbId, usRelId, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// beginCDB后dml操作，未提交事务TPC_BkpPhyEx接口导出，提交事务再查询数据修改成功，TPC_RestoreWithDataConvHook接口导入后的数据不含CDB修改
TEST_F(FunctionTest, V1Com_022_156)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_COND_STRU pstCond = {0};
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    for (int i = 0; i < insertNum; i++) {
        ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 19;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
        VOS_UINT32 pulRecNum = 0;
        ret = TPC_UpdateRec(cdbID, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
        &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// TPC_BkpPhy接口导出表数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_157)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        ret = TPC_BkpPhy(ulDbId, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通DB导入持久化DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_Restore接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_158)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        char filePath[64] = "./filePath/export.db";
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &pstFileSaveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// TPC_BkpPhyEx接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_159)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    // 再次OpenDB后导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllTypeBig(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// TPC_BkpPhyWithDataConvHook接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_160)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// TPC_PhyBkp2接口带自定义类型导出大端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_161)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// TPC_BkpPhyEx接口带自定义类型导出小端数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_162)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// TPC_BkpPhyEx接口导出只含临时表数据，TPC_RestoreWithDataConvHook接口导入数据
TEST_F(FunctionTest, V1Com_022_163)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic_temp.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

   // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录数
    VOS_UINT32 pulActRec;
    ret = TPC_GetRelActRec(ulDbId, usRelId, &pulActRec);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Relation with id (0) does not exist.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_164)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS saveOpt;
        saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &saveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &saveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &saveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// beginCDB后dml操作，未提交事务TPC_BkpPhyEx接口导出，提交事务再查询数据修改成功，TPC_RestoreWithDataConvHook接口导入后的数据不含CDB修改
TEST_F(FunctionTest, V1Com_022_165)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_COND_STRU pstCond = {0};
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    for (int i = 0; i < insertNum; i++) {
        ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 19;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
        VOS_UINT32 pulRecNum = 0;
        ret = TPC_UpdateRec(cdbID, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
        &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// TPC_BkpPhy接口导出表数据，TPC_RestoreWithDataConvHook接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_166)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1, pfnGetTblConvHook);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        ret = TPC_BkpPhy(ulDbId, (uint8_t *)filePath);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化DB导入普通DB
// TPC_BkpPhyEx、TPC_PhyBkp2、TPC_BkpPhyWithDataConvHook接口混合导出表和数据，TPC_Restore接口导入数据，进行dml操作，循环以上操作
TEST_F(FunctionTest, V1Com_022_167)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    char dbName1[20] = "dbName1";
    for (int loop = 0; loop < 3; loop++) {
        // 导入
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }

        // 更新&删除&查询数据
        DB_COND_STRU pstCond = {0};
        DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
        for (int i = 0; i < insertNum; i++) {
            ModifiedAllType(recBuf, stRelDef.pstFldLst, i + insertNum, tblRecLen);
            DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
            pstCond.usCondNum = 1;
            pstCond.aCond[0].ucFieldId = 19;
            pstCond.aCond[0].enOp = DB_OP_EQUAL;
            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19;
            VOS_UINT32 pulRecNum = 0;
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &dsBuf, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            *(uint64_t *)(pstCond.aCond[0].aucValue) = i + 19 + insertNum;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(1, pulRecNum);

            SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i + insertNum, &pulRecNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

            InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 导出
        DB_SAVE_OPTIONS saveOpt;
        saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        if (loop == 0) {
            ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &saveOpt);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else if (loop == 1)
        {
            ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &saveOpt, pfnGetTblConvHook);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        else {
            DB_BAKPHY_OPTIONS_STRU option = {0};
            option.pstFileSaveOpt = &saveOpt;
            option.pfnGetTblConvHook = pfnGetTblConvHook;
            option.bAddChecksum = false;
            ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // CLoseDB
        ret = TPC_CloseDB(ulDbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 检查v1的二进制数据
TEST_F(FunctionTest, V1Com_022_168)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char fileName[20] = "db1000v1big.db";
    char filePath[256] = {0};
    (void)sprintf(filePath, "./perFilePath/%s", fileName);

    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    snprintf(g_command, MAX_CMD_SIZE, "sh -x get_v1data.sh db1000v1big");
    system(g_command);
    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 校验TPC_PhyBkp2接口导出的不带自定义类型空DB启动数据，参数传DB_CHECK_QUICK开启CRC校验的小端数据
TEST_F(FunctionTest, V1Com_022_169)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_QUICK);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 校验TPC_PhyBkp2接口导出的不带自定义类型建表数据，参数传DB_CHECK_BASIC开启CRC校验的小端数据
TEST_F(FunctionTest, V1Com_022_170)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic3.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_BIG_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 校验TPC_PhyBkp2接口导出的不带自定义类型建表+写数据，参数传DB_CHECK_DETAILED开启CRC校验的小端数据
TEST_F(FunctionTest, V1Com_022_171)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic3.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType2(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType2(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 校验TPC_PhyBkp2接口导出的带自定义类型带表和记录，参数传DB_CHECK_DETAILED开启CRC校验的小端数据
TEST_F(FunctionTest, V1Com_022_172)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 校验TPC_PhyBkp2接口导出的带自定义类型带表和记录，参数传DB_CHECK_DETAILED开启CRC校验的大端数据
TEST_F(FunctionTest, V1Com_022_173)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
