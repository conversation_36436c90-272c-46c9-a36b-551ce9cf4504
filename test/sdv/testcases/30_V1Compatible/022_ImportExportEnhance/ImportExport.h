/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 30_V1Compatible\022_ImportExportEnhance\  支持V1导入导出增强
 * Designer: 冯小晋 00852664
 * Author: <PERSON><PERSON><PERSON>/z30021737
 * Create: 2025-1-10
 */
#ifndef IMPORT_EXPORT_H
#define IMPORT_EXPORT_H
#include <stdio.h>
#include <stdint.h>
#include <arpa/inet.h>
#include <sys/stat.h>
#include "t_rd_simplerel.h"

uint32_t g_testDbId = 0;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

// TEST_V1_FREE
void FreeRelDef(DB_REL_DEF_STRU *stRelDef)
{
    if (stRelDef->pstFldLst != NULL) {
        TEST_V1_FREE(stRelDef->pstFldLst);
        stRelDef->pstFldLst = NULL;
    }
    if (stRelDef->pstIdxLst != NULL) {
        TEST_V1_FREE(stRelDef->pstIdxLst);
        stRelDef->pstIdxLst = NULL;
    }
}

// 将文件指定位置的数据替换为modifyWord内容
void PersistModifyFileContent(const char *filePath, uint32_t seekPos, char modifyWord, uint32_t modifycnt = 1)
{
    FILE *fp = fopen(filePath, "r+");
    ASSERT_TRUE(fp != NULL);
    int ret = fseek(fp, seekPos, SEEK_SET);
    ASSERT_TRUE(ret == 0);
    size_t writeCnt = fwrite(&modifyWord, sizeof(char), modifycnt, fp);
    ASSERT_TRUE(writeCnt == modifycnt);
    ret = fflush(fp);
    ASSERT_TRUE(ret == 0);
    if (fp != NULL) {
        fclose(fp);
        fp = NULL;
    }
}

// 数据类型
static uint16_t TestGetOneFieldLen(DB_FIELD_DEF_STRU *pstFld)
{
    const uint16_t size = pstFld->usSize;
    // v1数据类型对应字段大小,自定义类型直接返回size
    if (pstFld->enDataType < 32) {
        const uint32_t fieldSizes[32] = {(uint32_t)((size + 1) / 2), sizeof(float), sizeof(double), 4, sizeof(double),
            (uint32_t)(size + 2), sizeof(DB_TIME_STRU), (uint32_t)(size + 1), size, sizeof(uint8_t), sizeof(uint16_t),
            sizeof(uint32_t), sizeof(int8_t), sizeof(int16_t), sizeof(int32_t), sizeof(DB_DATE_STRU), sizeof(uint32_t),
            6, 0, sizeof(int64_t), sizeof(uint64_t), sizeof(DB_IPV4PREFIX_STRUCT), sizeof(DB_IPV6ADDRESS_STRUCT),
            sizeof(DB_IPV6ADDRESS_PREFIX_STRU), sizeof(DB_DATETIME_STRU), sizeof(DB_TIMEZONE_STRU),
            (uint32_t)(size + 1), (uint32_t)(size + 2), 0, 0, 0, 0};
        return (uint16_t)fieldSizes[pstFld->enDataType];
    }
    return size;
}
static uint16_t GetRecLen(DB_REL_DEF_STRU *stRelDef)
{
    uint32_t recordLen = 0;
    DB_FIELD_DEF_STRU *pstFld = stRelDef->pstFldLst;
    for (uint32_t i = 0; i < stRelDef->ulNCols; i++, pstFld++) {
        recordLen += TestGetOneFieldLen(pstFld);
    }

    return recordLen;
}
// 结构化表
void CreateV1Label(const char *tableName, DB_REL_DEF_STRU *stRelDef, DB_DATATYPE_ENUM_V1 *dataTypes, int *dataSize,
    int fldNum, uint64_t maxRecordCount = 2000, bool isTemp = false)
{
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds == NULL) {
        return;
    }
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = dataSize[i];
        astFlds[i].ulDefVal = 0xFFFFFFFF;
    }

    int indexNum = 1;
    const char *indexName = "tTree";
    DB_INDEX_DEF_STRU *astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(indexNum * sizeof(DB_INDEX_DEF_STRU));
    if (astIdx == NULL) {
        return;
    }
    astIdx[0].ucUniqueFlag = 0;
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 0;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;
    (void)strncpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);

    (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    if (isTemp) {
        stRelDef->enTableType = DB_TABLE_TEMP;
    } else {
        stRelDef->enTableType = DB_TABLE_NORMAL;
    }
    stRelDef->ulIntialSize = maxRecordCount;
    stRelDef->ulMaxSize = maxRecordCount;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = indexNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

// 写数据
void InsertAllType(uint8_t *recBuf, DB_FIELD_DEF_STRU *astFlds, uint32_t base, uint32_t bufLen)
{
    (void)memset_s(recBuf, bufLen, 0x00, bufLen);

    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", base);
    // 2024.9.2
    // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
    // DBT_BYTES
    const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
    char stringVal1[stringLen1] = {0};
    memset_s(stringVal1, stringLen1, 0x00, stringLen1);
    *(uint16_t *)stringVal1 = 8;
    (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", base);
    // DBT_VBYTES
    const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
    char stringVal2[stringLen2] = {0};
    memset_s(stringVal2, stringLen2, 0x00, stringLen2);
    *(uint16_t *)stringVal2 = 10;
    (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", base);

    // DBT_BCD
    int16_t f0Value = base % 65535;
    // DBT_FLOAT
    float f1Value = (float)(base + 1);
    // DBT_DOUBLE
    double f2Value = (double)(base + 2);
    // DBT_BIT
    char *f3Value = stringVal;
    // DBT_NUMERIC
    double f4Value = (double)(base + 4);
    // DBT_BYTES
    char *f5Value = stringVal1;
    // DBT_TIME
    char *f6Value = stringVal;
    // DBT_STRING
    char *f7Value = stringVal;
    // DBT_BLOCK
    char *f8Value = stringVal;
    // DBT_UINT8
    uint8_t f9Value = (base + 9) % 256;
    // DBT_UINT16
    uint16_t f10Value = base + 10;
    // DBT_UINT32
    uint32_t f11Value = base + 11;
    // DBT_SINT8
    int8_t f12Value = (base + 12) % 128;
    // DBT_SINT16
    int16_t f13Value = base + 13;
    // DBT_SINT32
    int32_t f14Value = base + 14;
    // DBT_DATE
    char *f15Value = stringVal;
    // DBT_IP_ADDRESS
    uint32_t f16Value = base + 16;
    // DBT_MAC_ADDRESS
    char *f17Value = stringVal;
    // DBT_INT64
    int64_t f18Value = base + 18;
    // DBT_UINT64
    uint64_t f19Value = base + 19;
    // DBT_IPV4PREFIX
    char *f20Value = stringVal;
    // DBT_IPV6
    char *f21Value = stringVal;
    // DBT_IPV6PREFIX
    char *f22Value = stringVal;
    // DBT_DATETIME
    char *f23Value = stringVal;
    // DBT_TIMEZONE
    char *f24Value = stringVal;
    // DBT_MIBSTR
    char *f25Value = stringVal;
    // DBT_VBYTES
    char *f26Value = stringVal2;
    // DBT_UINT64
    uint64_t f27Value = base + 27;
    // DBT_UINT64
    uint64_t f28Value = base + 28;

    uint8_t *temp = recBuf;
    uint32_t index = 0;
    (void)memcpy_s(temp, TestGetOneFieldLen(&astFlds[index]), &f0Value, TestGetOneFieldLen(&astFlds[index]));
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f1Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f2Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f3Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f4Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f5Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f6Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f7Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f8Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f9Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f10Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f11Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f12Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f13Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f14Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f15Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f16Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f17Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f18Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f19Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f20Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f21Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f22Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f23Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f24Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f25Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f26Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f27Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f28Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
}

// 校验数据
void SelectAllType(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, DB_FIELD_DEF_STRU *astFlds, uint32_t data,
    uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 11;
    *(uint32_t *)stCond.aCond[0].aucValue = data + 11;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(cbdId, dbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        *recNum = pstBuff.ulRecNum;
        const uint32_t stringLen = 17;
        char stringVal[stringLen];
        memset_s(stringVal, stringLen, 0x00, stringLen);
        (void)sprintf_s(stringVal, stringLen, "re%u", data);

        // 2024.9.2
        // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
        // DBT_BYTES
        const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
        char stringVal1[stringLen1] = {0};
        memset_s(stringVal1, stringLen1, 0x00, stringLen1);
        *(uint16_t *)stringVal1 = 8;
        (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", data);
        // DBT_VBYTES
        const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
        char stringVal2[stringLen2] = {0};
        memset_s(stringVal2, stringLen2, 0x00, stringLen2);
        *(uint16_t *)stringVal2 = 10;
        (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", data);

        uint8_t *bufCursor = (uint8_t *)pstBuff.pBuf;
        for (uint32_t i = 0; i < pstBuff.ulRecNum; i++) {
            int pointPos = 0;
            int index = 0;
            V1_AW_MACRO_EXPECT_EQ_INT(data, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 1, *(float *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 2, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f3Value[4 + 1] = {0};
            (void)memcpy_s(f3Value, 4, bufCursor + pointPos, 4);
            char expF3Value[4 + 1] = {0};
            (void)memcpy_s(expF3Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f3Value, expF3Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 4, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(f5Value, 8 + 2, bufCursor + pointPos, 8 + 2);
            char expF5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(expF5Value, 8 + 2, stringVal1, 8 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f5Value, expF5Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f6Value[3 + 1] = {0};
            (void)memcpy_s(f6Value, 3, bufCursor + pointPos, 3);
            char expF6Value[3 + 1] = {0};
            (void)memcpy_s(expF6Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f6Value, expF6Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f7Value[10 + 1] = {0};
            (void)memcpy_s(f7Value, 10, bufCursor + pointPos, 10);
            char expF7Value[10 + 1] = {0};
            (void)memcpy_s(expF7Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f7Value, expF7Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f8Value[1 + 1] = {0};
            (void)memcpy_s(f8Value, 1, bufCursor + pointPos, 1);
            char expF8Value[1 + 1] = {0};
            (void)memcpy_s(expF8Value, 1, stringVal, 1);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f8Value, expF8Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 9) % 256, *(uint8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 10, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 11, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 12) % 128, *(int8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 13, *(int16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 14, *(int32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f15Value[4 + 1] = {0};
            (void)memcpy_s(f15Value, 4, bufCursor + pointPos, 4);
            char expF15Value[4 + 1] = {0};
            (void)memcpy_s(expF15Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f15Value, expF15Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 16, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f17Value[6 + 1] = {0};
            (void)memcpy_s(f17Value, 6, bufCursor + pointPos, 6);
            char expF17Value[6 + 1] = {0};
            (void)memcpy_s(expF17Value, 6, stringVal, 6);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f17Value, expF17Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 18, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 19, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f20Value[5 + 1] = {0};
            (void)memcpy_s(f20Value, 5, bufCursor + pointPos, 5);
            char expF20Value[5 + 1] = {0};
            (void)memcpy_s(expF20Value, 5, stringVal, 5);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f20Value, expF20Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f21Value[16 + 1] = {0};
            (void)memcpy_s(f21Value, 16, bufCursor + pointPos, 16);
            char expF21Value[16 + 1] = {0};
            (void)memcpy_s(expF21Value, 16, stringVal, 16);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f21Value, expF21Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f22Value[17 + 1] = {0};
            (void)memcpy_s(f22Value, 17, bufCursor + pointPos, 17);
            char expF22Value[17 + 1] = {0};
            (void)memcpy_s(expF22Value, 17, stringVal, 17);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f22Value, expF22Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f23Value[7 + 1] = {0};
            (void)memcpy_s(f23Value, 7, bufCursor + pointPos, 7);
            char expF23Value[7 + 1] = {0};
            (void)memcpy_s(expF23Value, 7, stringVal, 7);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f23Value, expF23Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f24Value[3 + 1] = {0};
            (void)memcpy_s(f24Value, 3, bufCursor + pointPos, 3);
            char expF24Value[3 + 1] = {0};
            (void)memcpy_s(expF24Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f24Value, expF24Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f25Value[10 + 1] = {0};
            (void)memcpy_s(f25Value, 10, bufCursor + pointPos, 10);
            char expF25Value[10 + 1] = {0};
            (void)memcpy_s(expF25Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f25Value, expF25Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(f26Value, 10 + 2, bufCursor + pointPos, 10 + 2);
            char expF26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(expF26Value, 10 + 2, stringVal2, 10 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f26Value, expF26Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 27, *(uint64_t *)(bufCursor + pointPos));
            VOS_UINT16 StoreLen = 0;
            ret = TestGetFldStoreLen(dbId, usRelId, "F27", &StoreLen);
            pointPos += StoreLen;
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 28, *(uint64_t *)(bufCursor + pointPos));
            ret = TestGetFldStoreLen(dbId, usRelId, "F28", &StoreLen);
            pointPos += StoreLen;
            index++;
            bufCursor += pstBuff.usRecLen;
        }
    } else {
        *recNum = 0;
    }
    TEST_V1_FREE(pstBuff.pBuf);
}

// 校验数据+自定义大端
void SelectAllTypeBig(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, DB_FIELD_DEF_STRU *astFlds, uint32_t data,
    uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 11;
    *(uint32_t *)stCond.aCond[0].aucValue = data + 11;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(cbdId, dbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        *recNum = pstBuff.ulRecNum;
        const uint32_t stringLen = 17;
        char stringVal[stringLen];
        memset_s(stringVal, stringLen, 0x00, stringLen);
        (void)sprintf_s(stringVal, stringLen, "re%u", data);

        // 2024.9.2
        // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
        // DBT_BYTES
        const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
        char stringVal1[stringLen1] = {0};
        memset_s(stringVal1, stringLen1, 0x00, stringLen1);
        *(uint16_t *)stringVal1 = 8;
        (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", data);
        // DBT_VBYTES
        const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
        char stringVal2[stringLen2] = {0};
        memset_s(stringVal2, stringLen2, 0x00, stringLen2);
        *(uint16_t *)stringVal2 = 10;
        (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", data);

        uint8_t *bufCursor = (uint8_t *)pstBuff.pBuf;
        for (uint32_t i = 0; i < pstBuff.ulRecNum; i++) {
            int pointPos = 0;
            int index = 0;
            V1_AW_MACRO_EXPECT_EQ_INT(data, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 1, *(float *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 2, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f3Value[4 + 1] = {0};
            (void)memcpy_s(f3Value, 4, bufCursor + pointPos, 4);
            char expF3Value[4 + 1] = {0};
            (void)memcpy_s(expF3Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f3Value, expF3Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 4, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(f5Value, 8 + 2, bufCursor + pointPos, 8 + 2);
            char expF5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(expF5Value, 8 + 2, stringVal1, 8 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f5Value, expF5Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f6Value[3 + 1] = {0};
            (void)memcpy_s(f6Value, 3, bufCursor + pointPos, 3);
            char expF6Value[3 + 1] = {0};
            (void)memcpy_s(expF6Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f6Value, expF6Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f7Value[10 + 1] = {0};
            (void)memcpy_s(f7Value, 10, bufCursor + pointPos, 10);
            char expF7Value[10 + 1] = {0};
            (void)memcpy_s(expF7Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f7Value, expF7Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f8Value[1 + 1] = {0};
            (void)memcpy_s(f8Value, 1, bufCursor + pointPos, 1);
            char expF8Value[1 + 1] = {0};
            (void)memcpy_s(expF8Value, 1, stringVal, 1);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f8Value, expF8Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 9) % 256, *(uint8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 10, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 11, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 12) % 128, *(int8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 13, *(int16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 14, *(int32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f15Value[4 + 1] = {0};
            (void)memcpy_s(f15Value, 4, bufCursor + pointPos, 4);
            char expF15Value[4 + 1] = {0};
            (void)memcpy_s(expF15Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f15Value, expF15Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 16, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f17Value[6 + 1] = {0};
            (void)memcpy_s(f17Value, 6, bufCursor + pointPos, 6);
            char expF17Value[6 + 1] = {0};
            (void)memcpy_s(expF17Value, 6, stringVal, 6);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f17Value, expF17Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 18, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 19, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f20Value[5 + 1] = {0};
            (void)memcpy_s(f20Value, 5, bufCursor + pointPos, 5);
            char expF20Value[5 + 1] = {0};
            (void)memcpy_s(expF20Value, 5, stringVal, 5);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f20Value, expF20Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f21Value[16 + 1] = {0};
            (void)memcpy_s(f21Value, 16, bufCursor + pointPos, 16);
            char expF21Value[16 + 1] = {0};
            (void)memcpy_s(expF21Value, 16, stringVal, 16);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f21Value, expF21Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f22Value[17 + 1] = {0};
            (void)memcpy_s(f22Value, 17, bufCursor + pointPos, 17);
            char expF22Value[17 + 1] = {0};
            (void)memcpy_s(expF22Value, 17, stringVal, 17);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f22Value, expF22Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f23Value[7 + 1] = {0};
            (void)memcpy_s(f23Value, 7, bufCursor + pointPos, 7);
            char expF23Value[7 + 1] = {0};
            (void)memcpy_s(expF23Value, 7, stringVal, 7);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f23Value, expF23Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f24Value[3 + 1] = {0};
            (void)memcpy_s(f24Value, 3, bufCursor + pointPos, 3);
            char expF24Value[3 + 1] = {0};
            (void)memcpy_s(expF24Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f24Value, expF24Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f25Value[10 + 1] = {0};
            (void)memcpy_s(f25Value, 10, bufCursor + pointPos, 10);
            char expF25Value[10 + 1] = {0};
            (void)memcpy_s(expF25Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f25Value, expF25Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(f26Value, 10 + 2, bufCursor + pointPos, 10 + 2);
            char expF26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(expF26Value, 10 + 2, stringVal2, 10 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f26Value, expF26Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            uint32_t f27data = data + 27;
            uint32_t big_endian_data;
            memcpy(&big_endian_data, &f27data, sizeof(f27data));
            big_endian_data = htonl(big_endian_data);
            V1_AW_MACRO_EXPECT_EQ_INT(big_endian_data, *(uint64_t *)(bufCursor + pointPos));
            VOS_UINT16 StoreLen = 0;
            ret = TestGetFldStoreLen(dbId, usRelId, "F27", &StoreLen);
            pointPos += StoreLen;
            index++;
            uint32_t f28data = data + 28;
            memcpy(&big_endian_data, &f28data, sizeof(f28data));
            big_endian_data = htonl(big_endian_data);
            V1_AW_MACRO_EXPECT_EQ_INT(big_endian_data, *(uint64_t *)(bufCursor + pointPos));
            ret = TestGetFldStoreLen(dbId, usRelId, "F28", &StoreLen);
            pointPos += StoreLen;
            index++;
            bufCursor += pstBuff.usRecLen;
        }
    } else {
        *recNum = 0;
    }
    TEST_V1_FREE(pstBuff.pBuf);
}

// 写数据带1个自定义类型
void InsertAllType1(uint8_t *recBuf, DB_FIELD_DEF_STRU *astFlds, uint32_t base, uint32_t bufLen)
{
    (void)memset_s(recBuf, bufLen, 0x00, bufLen);

    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", base);
    // 2024.9.2
    // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
    // DBT_BYTES
    const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
    char stringVal1[stringLen1] = {0};
    memset_s(stringVal1, stringLen1, 0x00, stringLen1);
    *(uint16_t *)stringVal1 = 8;
    (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", base);
    // DBT_VBYTES
    const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
    char stringVal2[stringLen2] = {0};
    memset_s(stringVal2, stringLen2, 0x00, stringLen2);
    *(uint16_t *)stringVal2 = 10;
    (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", base);

    // DBT_BCD
    int16_t f0Value = base % 65535;
    // DBT_FLOAT
    float f1Value = (float)(base + 1);
    // DBT_DOUBLE
    double f2Value = (double)(base + 2);
    // DBT_BIT
    char *f3Value = stringVal;
    // DBT_NUMERIC
    double f4Value = (double)(base + 4);
    // DBT_BYTES
    char *f5Value = stringVal1;
    // DBT_TIME
    char *f6Value = stringVal;
    // DBT_STRING
    char *f7Value = stringVal;
    // DBT_BLOCK
    char *f8Value = stringVal;
    // DBT_UINT8
    uint8_t f9Value = (base + 9) % 256;
    // DBT_UINT16
    uint16_t f10Value = base + 10;
    // DBT_UINT32
    uint32_t f11Value = base + 11;
    // DBT_SINT8
    int8_t f12Value = (base + 12) % 128;
    // DBT_SINT16
    int16_t f13Value = base + 13;
    // DBT_SINT32
    int32_t f14Value = base + 14;
    // DBT_DATE
    char *f15Value = stringVal;
    // DBT_IP_ADDRESS
    uint32_t f16Value = base + 16;
    // DBT_MAC_ADDRESS
    char *f17Value = stringVal;
    // DBT_INT64
    int64_t f18Value = base + 18;
    // DBT_UINT64
    uint64_t f19Value = base + 19;
    // DBT_IPV4PREFIX
    char *f20Value = stringVal;
    // DBT_IPV6
    char *f21Value = stringVal;
    // DBT_IPV6PREFIX
    char *f22Value = stringVal;
    // DBT_DATETIME
    char *f23Value = stringVal;
    // DBT_TIMEZONE
    char *f24Value = stringVal;
    // DBT_MIBSTR
    char *f25Value = stringVal;
    // DBT_VBYTES
    char *f26Value = stringVal2;
    // DBT_UINT64
    uint64_t f27Value = base + 27;

    uint8_t *temp = recBuf;
    uint32_t index = 0;
    (void)memcpy_s(temp, TestGetOneFieldLen(&astFlds[index]), &f0Value, TestGetOneFieldLen(&astFlds[index]));
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f1Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f2Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f3Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f4Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f5Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f6Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f7Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f8Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f9Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f10Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f11Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f12Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f13Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f14Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f15Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f16Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f17Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f18Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f19Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f20Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f21Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f22Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f23Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f24Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f25Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f26Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f27Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
}

// 写数据不带自定义类型
void InsertAllType2(uint8_t *recBuf, DB_FIELD_DEF_STRU *astFlds, uint32_t base, uint32_t bufLen)
{
    (void)memset_s(recBuf, bufLen, 0x00, bufLen);

    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", base);
    // 2024.9.2
    // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
    // DBT_BYTES
    const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
    char stringVal1[stringLen1] = {0};
    memset_s(stringVal1, stringLen1, 0x00, stringLen1);
    *(uint16_t *)stringVal1 = 8;
    (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", base);
    // DBT_VBYTES
    const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
    char stringVal2[stringLen2] = {0};
    memset_s(stringVal2, stringLen2, 0x00, stringLen2);
    *(uint16_t *)stringVal2 = 10;
    (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", base);

    // DBT_BCD
    int16_t f0Value = base % 65535;
    // DBT_FLOAT
    float f1Value = (float)(base + 1);
    // DBT_DOUBLE
    double f2Value = (double)(base + 2);
    // DBT_BIT
    char *f3Value = stringVal;
    // DBT_NUMERIC
    double f4Value = (double)(base + 4);
    // DBT_BYTES
    char *f5Value = stringVal1;
    // DBT_TIME
    char *f6Value = stringVal;
    // DBT_STRING
    char *f7Value = stringVal;
    // DBT_BLOCK
    char *f8Value = stringVal;
    // DBT_UINT8
    uint8_t f9Value = (base + 9) % 256;
    // DBT_UINT16
    uint16_t f10Value = base + 10;
    // DBT_UINT32
    uint32_t f11Value = base + 11;
    // DBT_SINT8
    int8_t f12Value = (base + 12) % 128;
    // DBT_SINT16
    int16_t f13Value = base + 13;
    // DBT_SINT32
    int32_t f14Value = base + 14;
    // DBT_DATE
    char *f15Value = stringVal;
    // DBT_IP_ADDRESS
    uint32_t f16Value = base + 16;
    // DBT_MAC_ADDRESS
    char *f17Value = stringVal;
    // DBT_INT64
    int64_t f18Value = base + 18;
    // DBT_UINT64
    uint64_t f19Value = base + 19;
    // DBT_IPV4PREFIX
    char *f20Value = stringVal;
    // DBT_IPV6
    char *f21Value = stringVal;
    // DBT_IPV6PREFIX
    char *f22Value = stringVal;
    // DBT_DATETIME
    char *f23Value = stringVal;
    // DBT_TIMEZONE
    char *f24Value = stringVal;
    // DBT_MIBSTR
    char *f25Value = stringVal;
    // DBT_VBYTES
    char *f26Value = stringVal2;

    uint8_t *temp = recBuf;
    uint32_t index = 0;
    (void)memcpy_s(temp, TestGetOneFieldLen(&astFlds[index]), &f0Value, TestGetOneFieldLen(&astFlds[index]));
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f1Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f2Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f3Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f4Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f5Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f6Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f7Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f8Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f9Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f10Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f11Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f12Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f13Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f14Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f15Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f16Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f17Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f18Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f19Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f20Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f21Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f22Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f23Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f24Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f25Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f26Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
}

// 写数据
void ModifiedAllType(uint8_t *recBuf, DB_FIELD_DEF_STRU *astFlds, uint32_t base, uint32_t bufLen)
{
    (void)memset_s(recBuf, bufLen, 0x00, bufLen);

    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", base);
    // 2024.9.2
    // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
    // DBT_BYTES
    const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
    char stringVal1[stringLen1] = {0};
    memset_s(stringVal1, stringLen1, 0x00, stringLen1);
    *(uint16_t *)stringVal1 = 8;
    (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", base);
    // DBT_VBYTES
    const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
    char stringVal2[stringLen2] = {0};
    memset_s(stringVal2, stringLen2, 0x00, stringLen2);
    *(uint16_t *)stringVal2 = 10;
    (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", base);

    // DBT_BCD
    int16_t f0Value = base % 65535;
    // DBT_FLOAT
    float f1Value = (float)(base + 1);
    // DBT_DOUBLE
    double f2Value = (double)(base + 2);
    // DBT_BIT
    char *f3Value = stringVal;
    // DBT_NUMERIC
    double f4Value = (double)(base + 4);
    // DBT_BYTES
    char *f5Value = stringVal1;
    // DBT_TIME
    char *f6Value = stringVal;
    // DBT_STRING
    char *f7Value = stringVal;
    // DBT_BLOCK
    char *f8Value = stringVal;
    // DBT_UINT8
    uint8_t f9Value = (base + 9) % 256;
    // DBT_UINT16
    uint16_t f10Value = base + 10;
    // DBT_UINT32
    uint32_t f11Value = base + 11;
    // DBT_SINT8
    int8_t f12Value = (base + 12) % 128;
    // DBT_SINT16
    int16_t f13Value = base + 13;
    // DBT_SINT32
    int32_t f14Value = base + 14;
    // DBT_DATE
    char *f15Value = stringVal;
    // DBT_IP_ADDRESS
    uint32_t f16Value = base + 16;
    // DBT_MAC_ADDRESS
    char *f17Value = stringVal;
    // DBT_INT64
    int64_t f18Value = base + 18;
    // DBT_UINT64
    uint64_t f19Value = base + 19;
    // DBT_IPV4PREFIX
    char *f20Value = stringVal;
    // DBT_IPV6
    char *f21Value = stringVal;
    // DBT_IPV6PREFIX
    char *f22Value = stringVal;
    // DBT_DATETIME
    char *f23Value = stringVal;
    // DBT_TIMEZONE
    char *f24Value = stringVal;
    // DBT_MIBSTR
    char *f25Value = stringVal;
    // DBT_VBYTES
    char *f26Value = stringVal2;
    // DBT_UINT64
    uint64_t f27Value = base + 27;
    // DBT_UINT64
    uint64_t f28Value = base + 28;

    uint8_t *temp = recBuf;
    uint32_t index = 0;
    (void)memcpy_s(temp, TestGetOneFieldLen(&astFlds[index]), &f0Value, TestGetOneFieldLen(&astFlds[index]));
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f1Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f2Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f3Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f4Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f5Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f6Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f7Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f8Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f9Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f10Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f11Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f12Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f13Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f14Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f15Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f16Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f17Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f18Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f19Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f20Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f21Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f22Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f23Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f24Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f25Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), f26Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f27Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
    ++index;
    (void)memcpy_s(temp += TestGetOneFieldLen(&astFlds[index]), TestGetOneFieldLen(&astFlds[index + 1]), &f28Value,
        TestGetOneFieldLen(&astFlds[index + 1]));
}

// 校验数据带1个自定义类型
void SelectAllType1(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, DB_FIELD_DEF_STRU *astFlds, uint32_t data,
    uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 11;
    *(uint32_t *)stCond.aCond[0].aucValue = data + 11;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(cbdId, dbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        *recNum = pstBuff.ulRecNum;
        const uint32_t stringLen = 17;
        char stringVal[stringLen];
        memset_s(stringVal, stringLen, 0x00, stringLen);
        (void)sprintf_s(stringVal, stringLen, "re%u", data);

        // 2024.9.2
        // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
        // DBT_BYTES
        const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
        char stringVal1[stringLen1] = {0};
        memset_s(stringVal1, stringLen1, 0x00, stringLen1);
        *(uint16_t *)stringVal1 = 8;
        (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", data);
        // DBT_VBYTES
        const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
        char stringVal2[stringLen2] = {0};
        memset_s(stringVal2, stringLen2, 0x00, stringLen2);
        *(uint16_t *)stringVal2 = 10;
        (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", data);

        uint8_t *bufCursor = (uint8_t *)pstBuff.pBuf;
        for (uint32_t i = 0; i < pstBuff.ulRecNum; i++) {
            int pointPos = 0;
            int index = 0;
            V1_AW_MACRO_EXPECT_EQ_INT(data, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 1, *(float *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 2, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f3Value[4 + 1] = {0};
            (void)memcpy_s(f3Value, 4, bufCursor + pointPos, 4);
            char expF3Value[4 + 1] = {0};
            (void)memcpy_s(expF3Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f3Value, expF3Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 4, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(f5Value, 8 + 2, bufCursor + pointPos, 8 + 2);
            char expF5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(expF5Value, 8 + 2, stringVal1, 8 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f5Value, expF5Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f6Value[3 + 1] = {0};
            (void)memcpy_s(f6Value, 3, bufCursor + pointPos, 3);
            char expF6Value[3 + 1] = {0};
            (void)memcpy_s(expF6Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f6Value, expF6Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f7Value[10 + 1] = {0};
            (void)memcpy_s(f7Value, 10, bufCursor + pointPos, 10);
            char expF7Value[10 + 1] = {0};
            (void)memcpy_s(expF7Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f7Value, expF7Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f8Value[1 + 1] = {0};
            (void)memcpy_s(f8Value, 1, bufCursor + pointPos, 1);
            char expF8Value[1 + 1] = {0};
            (void)memcpy_s(expF8Value, 1, stringVal, 1);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f8Value, expF8Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 9) % 256, *(uint8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 10, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 11, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 12) % 128, *(int8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 13, *(int16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 14, *(int32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f15Value[4 + 1] = {0};
            (void)memcpy_s(f15Value, 4, bufCursor + pointPos, 4);
            char expF15Value[4 + 1] = {0};
            (void)memcpy_s(expF15Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f15Value, expF15Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 16, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f17Value[6 + 1] = {0};
            (void)memcpy_s(f17Value, 6, bufCursor + pointPos, 6);
            char expF17Value[6 + 1] = {0};
            (void)memcpy_s(expF17Value, 6, stringVal, 6);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f17Value, expF17Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 18, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 19, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f20Value[5 + 1] = {0};
            (void)memcpy_s(f20Value, 5, bufCursor + pointPos, 5);
            char expF20Value[5 + 1] = {0};
            (void)memcpy_s(expF20Value, 5, stringVal, 5);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f20Value, expF20Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f21Value[16 + 1] = {0};
            (void)memcpy_s(f21Value, 16, bufCursor + pointPos, 16);
            char expF21Value[16 + 1] = {0};
            (void)memcpy_s(expF21Value, 16, stringVal, 16);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f21Value, expF21Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f22Value[17 + 1] = {0};
            (void)memcpy_s(f22Value, 17, bufCursor + pointPos, 17);
            char expF22Value[17 + 1] = {0};
            (void)memcpy_s(expF22Value, 17, stringVal, 17);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f22Value, expF22Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f23Value[7 + 1] = {0};
            (void)memcpy_s(f23Value, 7, bufCursor + pointPos, 7);
            char expF23Value[7 + 1] = {0};
            (void)memcpy_s(expF23Value, 7, stringVal, 7);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f23Value, expF23Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f24Value[3 + 1] = {0};
            (void)memcpy_s(f24Value, 3, bufCursor + pointPos, 3);
            char expF24Value[3 + 1] = {0};
            (void)memcpy_s(expF24Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f24Value, expF24Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f25Value[10 + 1] = {0};
            (void)memcpy_s(f25Value, 10, bufCursor + pointPos, 10);
            char expF25Value[10 + 1] = {0};
            (void)memcpy_s(expF25Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f25Value, expF25Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(f26Value, 10 + 2, bufCursor + pointPos, 10 + 2);
            char expF26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(expF26Value, 10 + 2, stringVal2, 10 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f26Value, expF26Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 27, *(uint64_t *)(bufCursor + pointPos));
            VOS_UINT16 StoreLen = 0;
            ret = TestGetFldStoreLen(dbId, usRelId, "F27", &StoreLen);
            pointPos += StoreLen;
            index++;
            bufCursor += pstBuff.usRecLen;
        }
    } else {
        *recNum = 0;
    }
    TEST_V1_FREE(pstBuff.pBuf);
}

// 校验数据不带自定义类型
void SelectAllType2(VOS_UINT32 cbdId, VOS_UINT32 dbId, VOS_UINT16 usRelId, DB_FIELD_DEF_STRU *astFlds, uint32_t data,
    uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_EQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 11;
    *(uint32_t *)stCond.aCond[0].aucValue = data + 11;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff = {0};
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    if (pstBuff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(cbdId, dbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    if (ret != VOS_ERRNO_DB_RECNOTEXIST) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        *recNum = pstBuff.ulRecNum;
        const uint32_t stringLen = 17;
        char stringVal[stringLen];
        memset_s(stringVal, stringLen, 0x00, stringLen);
        (void)sprintf_s(stringVal, stringLen, "re%u", data);

        // 2024.9.2
        // 兼容V1，DBT_BYTES，DBT_VBYTES，设置值时需使用前两个字节长度来确定本次设置的值的有效位数，且不能大于表定义的长度
        // DBT_BYTES
        const uint32_t stringLen1 = 8 + 2;  // 该长度为表定义长度加2
        char stringVal1[stringLen1] = {0};
        memset_s(stringVal1, stringLen1, 0x00, stringLen1);
        *(uint16_t *)stringVal1 = 8;
        (void)sprintf_s(stringVal1 + 2, stringLen1 - 2, "08re%u", data);
        // DBT_VBYTES
        const uint32_t stringLen2 = 10 + 2;  // 该长度为表定义长度加2
        char stringVal2[stringLen2] = {0};
        memset_s(stringVal2, stringLen2, 0x00, stringLen2);
        *(uint16_t *)stringVal2 = 10;
        (void)sprintf_s(stringVal2 + 2, stringLen2 - 2, "08re%u", data);

        uint8_t *bufCursor = (uint8_t *)pstBuff.pBuf;
        for (uint32_t i = 0; i < pstBuff.ulRecNum; i++) {
            int pointPos = 0;
            int index = 0;
            V1_AW_MACRO_EXPECT_EQ_INT(data, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 1, *(float *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 2, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f3Value[4 + 1] = {0};
            (void)memcpy_s(f3Value, 4, bufCursor + pointPos, 4);
            char expF3Value[4 + 1] = {0};
            (void)memcpy_s(expF3Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f3Value, expF3Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 4, *(double *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(f5Value, 8 + 2, bufCursor + pointPos, 8 + 2);
            char expF5Value[8 + 2 + 1] = {0};
            (void)memcpy_s(expF5Value, 8 + 2, stringVal1, 8 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f5Value, expF5Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f6Value[3 + 1] = {0};
            (void)memcpy_s(f6Value, 3, bufCursor + pointPos, 3);
            char expF6Value[3 + 1] = {0};
            (void)memcpy_s(expF6Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f6Value, expF6Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f7Value[10 + 1] = {0};
            (void)memcpy_s(f7Value, 10, bufCursor + pointPos, 10);
            char expF7Value[10 + 1] = {0};
            (void)memcpy_s(expF7Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f7Value, expF7Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f8Value[1 + 1] = {0};
            (void)memcpy_s(f8Value, 1, bufCursor + pointPos, 1);
            char expF8Value[1 + 1] = {0};
            (void)memcpy_s(expF8Value, 1, stringVal, 1);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f8Value, expF8Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 9) % 256, *(uint8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 10, *(uint16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 11, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT((data + 12) % 128, *(int8_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 13, *(int16_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 14, *(int32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f15Value[4 + 1] = {0};
            (void)memcpy_s(f15Value, 4, bufCursor + pointPos, 4);
            char expF15Value[4 + 1] = {0};
            (void)memcpy_s(expF15Value, 4, stringVal, 4);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f15Value, expF15Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 16, *(uint32_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f17Value[6 + 1] = {0};
            (void)memcpy_s(f17Value, 6, bufCursor + pointPos, 6);
            char expF17Value[6 + 1] = {0};
            (void)memcpy_s(expF17Value, 6, stringVal, 6);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f17Value, expF17Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 18, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            V1_AW_MACRO_EXPECT_EQ_INT(data + 19, *(uint64_t *)(bufCursor + pointPos));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f20Value[5 + 1] = {0};
            (void)memcpy_s(f20Value, 5, bufCursor + pointPos, 5);
            char expF20Value[5 + 1] = {0};
            (void)memcpy_s(expF20Value, 5, stringVal, 5);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f20Value, expF20Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f21Value[16 + 1] = {0};
            (void)memcpy_s(f21Value, 16, bufCursor + pointPos, 16);
            char expF21Value[16 + 1] = {0};
            (void)memcpy_s(expF21Value, 16, stringVal, 16);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f21Value, expF21Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f22Value[17 + 1] = {0};
            (void)memcpy_s(f22Value, 17, bufCursor + pointPos, 17);
            char expF22Value[17 + 1] = {0};
            (void)memcpy_s(expF22Value, 17, stringVal, 17);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f22Value, expF22Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f23Value[7 + 1] = {0};
            (void)memcpy_s(f23Value, 7, bufCursor + pointPos, 7);
            char expF23Value[7 + 1] = {0};
            (void)memcpy_s(expF23Value, 7, stringVal, 7);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f23Value, expF23Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f24Value[3 + 1] = {0};
            (void)memcpy_s(f24Value, 3, bufCursor + pointPos, 3);
            char expF24Value[3 + 1] = {0};
            (void)memcpy_s(expF24Value, 3, stringVal, 3);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f24Value, expF24Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f25Value[10 + 1] = {0};
            (void)memcpy_s(f25Value, 10, bufCursor + pointPos, 10);
            char expF25Value[10 + 1] = {0};
            (void)memcpy_s(expF25Value, 10, stringVal, 10);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f25Value, expF25Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            char f26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(f26Value, 10 + 2, bufCursor + pointPos, 10 + 2);
            char expF26Value[10 + 2 + 1] = {0};
            (void)memcpy_s(expF26Value, 10 + 2, stringVal2, 10 + 2);
            V1_AW_MACRO_EXPECT_EQ_INT(0, strcmp(f26Value, expF26Value));
            pointPos += TestGetOneFieldLen(&astFlds[index]);
            index++;
            bufCursor += pstBuff.usRecLen;
        }
    } else {
        *recNum = 0;
    }
    TEST_V1_FREE(pstBuff.pBuf);
}

void TestCreateTblInsertSelect(bool isPersistent = false)
{
    int ret;
    char dbName[20] = "dbName";
    char filePath[64] = "./perFilePath/perDbPath";
    VOS_UINT32 dbID = 0;
    if (isPersistent) {
        ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    } else {
        ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbID);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 定义数据类型
    const uint32_t fldNum = 27;
    DB_REL_DEF_STRU relDef = {0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD, DBT_FLOAT, DBT_DOUBLE, DBT_BIT, DBT_NUMERIC, DBT_BYTES, DBT_TIME,
        DBT_STRING, DBT_BLOCK, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_SINT32, DBT_DATE,
        DBT_IP_ADDRESS, DBT_MAC_ADDRESS, DBT_INT64, DBT_UINT64, DBT_IPV4PREFIX, DBT_IPV6, DBT_IPV6PREFIX, DBT_DATETIME,
        DBT_TIMEZONE, DBT_MIBSTR, DBT_VBYTES};
    int dataSize[fldNum] = {4, 4, 8, 4, 8, 8, 3, 10, 1, 1, 2, 4, 1, 2, 4, 4, 4, 6, 8, 8, 5, 16, 17, 7, 3, 10, 10};
    CreateV1Label("label1", &relDef, dataTypes, dataSize, fldNum);  // 结构化

    // 建表
    uint16_t relId = 0;
    ret = TPC_CreateTbl(dbID, &relDef, &relId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    if (recBuf == NULL) {
        AW_FUN_Log(LOG_DEBUG, "recBuf TEST_V1_MALLOC failed !!!");
    }
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写数据
    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbID, relId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, dbID, relId, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // CLoseDB
    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
}

VOS_UINT32 PersistEndianTransForBit32(VOS_UINT32 transVal)
{
    transVal = ((transVal << 8) & 0xFF00FF00) | ((transVal >> 8) & 0x00FF00FF);
    return (transVal >> 16) | (transVal << 16);
}

// 为label1定制
VOS_VOID CustomFld88ConvHook(VOS_UINT8 ucFldId, VOS_UINT16 usDataType, VOS_UINT32 ulStoredLen, VOS_UINT8 *pucData)
{
    // uint32_t uint32_t 分别做大小端转换
    VOS_UINT32 val1 = *((VOS_UINT32 *)pucData);
    *((VOS_UINT32 *)pucData) = PersistEndianTransForBit32(val1);
    VOS_UINT32 val2 = *((VOS_UINT32 *)(pucData + 4));
    *((VOS_UINT32 *)(pucData + 4)) = PersistEndianTransForBit32(val2);
}

uint16_t g_table1Id = 0;
DB_FldConvHook g_fldHookArray[29] = {NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CustomFld88ConvHook,
    CustomFld88ConvHook};
DB_FldConvHook *GetTblConvHook(VOS_UINT16 usRelId)
{
    if (usRelId == g_table1Id) {
        return g_fldHookArray;
    }
    return NULL;
}
DB_FldConvHook g_fldHookArrayBlock[28] = {NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CustomFld88ConvHook, NULL,
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL};
DB_FldConvHook *GetTblConvHookBlock(VOS_UINT16 usRelId)
{
    if (usRelId == g_table1Id) {
        return g_fldHookArrayBlock;
    }
    return NULL;
}
DB_FldConvHook g_fldHookArrayBlockOne[28] = {NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CustomFld88ConvHook, NULL,
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
    CustomFld88ConvHook};
DB_FldConvHook *GetTblConvHookBlockOne(VOS_UINT16 usRelId)
{
    if (usRelId == g_table1Id) {
        return g_fldHookArrayBlockOne;
    }
    return NULL;
}

DB_FldConvHook g_fldHookArrayTwo[29] = {NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CustomFld88ConvHook, NULL, NULL,
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CustomFld88ConvHook,
    CustomFld88ConvHook};
DB_FldConvHook *GetTblConvHookTwo(VOS_UINT16 usRelId)
{
    if (usRelId == g_table1Id) {
        return g_fldHookArrayTwo;
    }
    return NULL;
}

DB_FldConvHook g_fldHookArrayNull[29] = {NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL};
DB_FldConvHook *GetTblConvHookNull(VOS_UINT16 usRelId)
{
    if (usRelId == g_table1Id) {
        return g_fldHookArrayNull;
    }
    return NULL;
}
DB_FldConvHook g_fldHookArrayOne[29] = {NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CustomFld88ConvHook};
DB_FldConvHook *GetTblConvHookOne(VOS_UINT16 usRelId)
{
    if (usRelId == g_table1Id) {
        return g_fldHookArrayOne;
    }
    return NULL;
}
void TestCreateTblInitRelDefWithoutIdx(DB_REL_DEF_STRU *stRelDef, const char *tblName)
{
    // 此表定义没有设置索引
    const char tableName[] = "nameindex";
    const uint32_t fldNum = 5;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    if (astFlds == NULL) {
        return;
    }
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32};
    const uint32_t fldSizes[fldNum] = {4, 4, 4, 4, 4};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }

    tblName == NULL ? (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN) :
                      (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 100;
    stRelDef->ulMaxSize = 100;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = 0;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

long readFile(const char *path, char **buf, bool convertArray = false)
{
    char fileName[256];
    (void)snprintf(fileName, sizeof(fileName), "%s", path);

    FILE *fp;
    fp = fopen(fileName, "rb");
    if (NULL == fp) {
        printf("[readFile] open file:%s fail.\n", fileName);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        printf("[readFile] fseek file:%s to end failed.\n", fileName);
        fclose(fp);
        return -1;
    }

    long size = ftell(fp);
    if (size < 0) {
        printf("[readFile] read file size:%ld failed.\n", size);
        fclose(fp);
        return -1;
    }

    char *pBuffer = (char *)TEST_V1_MALLOC(size + 8);
    if (pBuffer == NULL) {
        printf("[readFile] TEST_V1_MALLOC memory:%ld for file:%s failed.\n", size + 4, fileName);
        fclose(fp);
        return -1;
    }
    int i = 0;
    if (convertArray) {
        i = 1;
        pBuffer[0] = ' ';
    }
    int fs = fseek(fp, 0L, SEEK_SET);
    if (fs != 0) {
        printf("[readFile] fseek file:%s to head failed.\n", fileName);
        fclose(fp);
        return -1;
    }
    long readSize = fread(pBuffer + i, 1, size, fp);
    if (readSize != size) {
        printf("[readFile] read file:%s failed, expectSize:%ld, actualSize:%ld.\n", fileName, size, readSize);
        TEST_V1_FREE(pBuffer);
        fclose(fp);
        return -1;
    }
    int fc = fclose(fp);
    if (fc != 0) {
        printf("[readFile] fclose file:%s failed.\n", fileName);
        fclose(fp);
        return -1;
    }
    if (convertArray) {
        for (; i < size; ++i) {
            if (pBuffer[i] == ' ') {
                continue;
            }
            if (pBuffer[i] == '{') {
                pBuffer[0] = '[';
                pBuffer[size + 1] = ']';
                pBuffer[size + 2] = '\0';
            } else {
                pBuffer[size + 1] = '\0';
            }
            break;
        }
    } else {
        pBuffer[size] = '\0';
    }
    *buf = pBuffer;
    return size;
}

#endif
