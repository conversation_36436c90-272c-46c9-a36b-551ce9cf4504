
#include "ImportExport.h"

class SpecificationTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SpecificationTest::SetUpTestCase()
{}

void SpecificationTest::TearDownTestCase()
{}

void SpecificationTest::SetUp()
{
    // 初始化
    int ret = TestTPC_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.db");
    system("rm -rf ./perFilePath/*.db");
    system("rm -rf ./filePath/output");
    AW_CHECK_LOG_BEGIN();
}

void SpecificationTest::TearDown()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.db");
    system("rm -rf ./perFilePath/*.db");
    system("rm -rf ./filePath/output");
    AW_CHECK_LOG_END();
}

// 普通db + DB_RESTORETYPE_DISCARD
//  045 TPC_BkpPhyEx接口导出，DbName存在，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with the same name exists. Name is dbName1.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046 TPC_BkpPhyWithDataConvHook接口导出，DbName存在，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with the same name exists. Name is dbName1.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047 TPC_PhyBkp2接口导出，DbName存在，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DbName存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with the same name exists. Name is dbName1.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)filePath,
        DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)filePath,
        DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)filePath,
        DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (0) not exists.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};

    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (0) not exists.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[256] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (0) not exists.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通db + DB_RESTORETYPE_REPLACE
//  054
//  TPC_BkpPhyEx接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DbName存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055
// TPC_BkpPhyWithDataConvHook接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056
// TPC_PhyBkp2接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057
// TPC_BkpPhyEx接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char dbDir1[64] = "./filePath/perDbPath";
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058
// TPC_BkpPhyWithDataConvHook接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char dbDir1[64] = "./filePath/perDbPath";
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059
// TPC_PhyBkp2接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    char dbDir1[64] = "./filePath/perDbPath";
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060
// TPC_BkpPhyEx接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    char dbDir1[64] = "./filePath/perDbPath";
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061
// TPC_BkpPhyWithDataConvHook接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    // DbName存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    char dbDir1[64] = "./filePath/perDbPath";
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062
// TPC_PhyBkp2接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    // DbName不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    char dbDir1[64] = "./filePath/perDbPath";
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // Db不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DbName不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 066
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName is not exist when open db.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (0) not exists.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName is not exist when open db.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (0) not exists.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 068
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName is not exist when open db.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id (0) not exists.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 069
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=flase，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 070
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=flase，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 071
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=flase，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir,
        DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化db + DB_RESTORETYPE_DISCARD
//  072 TPC_BkpPhyEx接口导出，DbName存在，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath1";
    // 重复创建使用同一个路径
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with the same name exists. Name is dbName1.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 073 TPC_BkpPhyWithDataConvHook接口导出，DbName存在，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1,
        DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with the same name exists. Name is dbName1.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 074 TPC_PhyBkp2接口导出，DbName存在，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath1";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DbName存在
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (VOS_UINT8 *)dbDir1,
        DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with the same name exists. Name is dbName1.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 075
// TPC_BkpPhyEx接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 076
// TPC_BkpPhyWithDataConvHook接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 077
// TPC_PhyBkp2接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // DbName存在
    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 078
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir,
        DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 079
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir,
        DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 080
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char dbName1[20] = "dbName1";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir,
        DB_RESTORETYPE_DISCARD, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//持久化db + DB_RESTORETYPE_REPLACE
// 081
// TPC_BkpPhyEx接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 082
// TPC_BkpPhyWithDataConvHook接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
     
    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 083
// TPC_PhyBkp2接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
     
    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 084
// TPC_BkpPhyEx接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 085
// TPC_BkpPhyWithDataConvHook接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 086
// TPC_PhyBkp2接口导出，DbName存在，pstDbConfig.bPersistent=true，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 087
// TPC_BkpPhyEx接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 088
// TPC_BkpPhyWithDataConvHook接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 089
// TPC_PhyBkp2接口导出，DbName存在，pstDbConfig.bPersistent=fasle，pucDestDir和create的DB一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 090
// TPC_BkpPhyEx接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 091
// TPC_BkpPhyWithDataConvHook接口导出，DbName存在，pstDbConfig.bPersistent=false，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 092
// TPC_PhyBkp2接口导出，DbName存在，pstDbConfig.bPersistent=fasle，pucDestDir和create的DB不一致，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 093
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 094
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 095
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 096
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 097
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 098
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=true，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("destination file pointer is NULL pointer or empty str.", false));

    ret = TPC_OpenDB((VOS_UINT8 *)dbDir1, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB name dbName1 is not exist when open db.", false));

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with name (dbName1) is not exist.", false));

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 099
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=false，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 100
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=false，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 101
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=false，pucDestDir指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, (uint8_t *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 102
// TPC_BkpPhyEx接口导出，DbName不存在，pstDbConfig.bPersistent=false，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 103
// TPC_BkpPhyWithDataConvHook接口导出，DbName不存在，pstDbConfig.bPersistent=false，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 104
// TPC_PhyBkp2接口导出，DbName不存在，pstDbConfig.bPersistent=false，pucDestDir不指定路径，TPC_RestoreWithDataConvHook接口导入
TEST_F(SpecificationTest, V1Com_022_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    char dbDir[64] = "./perFilePath/perDbPath";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // Db不存在
    char dbName1[20] = "dbName1";
    char dbDir1[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName1, (uint8_t *)dbDir1, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName1, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName1, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 105
// 定义1个DBT_BLOCK数据+1个自定义类型，不传入转换函数
TEST_F(SpecificationTest, V1Com_022_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType1(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHookNull;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt,pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 106
// 定义1个DBT_BLOCK数据+1个自定义类型，传入1个BLOCK转换函数
TEST_F(SpecificationTest, V1Com_022_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType1(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHookBlock;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt,pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 107
// 定义1个DBT_BLOCK数据+1个自定义类型，传入2个自定义类型+1个BLOCK转换函数
TEST_F(SpecificationTest, V1Com_022_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType1(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHookTwo;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt,pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 108
// 导入数据中定义1个DBT_BLOCK数据+1个自定义类型，不传入转换函数
TEST_F(SpecificationTest, V1Com_022_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType1(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHookOne;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt,pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 导入
    DB_GetTblConvHook pfnGetTblConvHook1 = GetTblConvHookNull;
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1,pfnGetTblConvHook1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, dbID2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 109
// 导入数据中定义1个DBT_BLOCK数据+1个自定义类型，传入1个BLOCK转换函数
TEST_F(SpecificationTest, V1Com_022_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType1(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHookBlockOne;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt,pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 导入
    DB_GetTblConvHook pfnGetTblConvHook1 = GetTblConvHookBlock;
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1,pfnGetTblConvHook1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, dbID2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 110
// 导入数据中定义1个DBT_BLOCK数据+1个自定义类型，传入2个自定义类型+1个BLOCK转换函数
TEST_F(SpecificationTest, V1Com_022_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    if (recBuf == NULL) {
        AW_FUN_Log(LOG_ERROR, "Failed to allocate memory for record buffer.");
        return;
    }
    ret = memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    if (ret != 0) {
        AW_FUN_Log(LOG_ERROR, "Failed to set memory for record buffer.");
        TEST_V1_FREE(recBuf);
        return;
    }

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType1(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHookBlockOne;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt,pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)filePath, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 导入
    DB_GetTblConvHook pfnGetTblConvHook1 = GetTblConvHookTwo;
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_RestoreWithDataConvHook(
        (VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1,pfnGetTblConvHook1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT32 dbID2 = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, &dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType1(TPC_GLOBAL_CDB, dbID2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // CLoseDB
    ret = TPC_CloseDB(dbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 111
// TPC_BkpPhyEx接口导出后，调用TPC_CheckDataFile接口校验数据
TEST_F(SpecificationTest, V1Com_022_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyEx(ulDbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("022_ImportExportEnhance/filePath/export.db has no checksum msg.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 112
// TPC_BkpPhyWithDataConvHook接口导出后，调用TPC_CheckDataFile接口校验数据
TEST_F(SpecificationTest, V1Com_022_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    ret = TPC_BkpPhyWithDataConvHook(ulDbId, (VOS_UINT8 *)filePath, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("022_ImportExportEnhance/filePath/export.db has no checksum msg.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 113
// TPC_PhyBkp2接口，AddChecksum=false导出数据，调用TPC_CheckDataFile接口校验数据
TEST_F(SpecificationTest, V1Com_022_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = false;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DBKNL_NO_CHECKSUM_REF, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("022_ImportExportEnhance/filePath/export.db has no checksum msg.", false));

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 114
// TPC_PhyBkp2接口，AddChecksum=true导出数据，调用TPC_CheckDataFile接口校验数据
TEST_F(SpecificationTest, V1Com_022_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建自定义类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 66;
    uint32_t dataTypeId1 = 88;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    char dbDir[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, (uint8_t *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // OpenDB
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_basic.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ulDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, ulDbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 导出
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_SAVE_OPTIONS saveOpt;
    saveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char filePath[64] = "./filePath/export.db";
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &saveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = TPC_PhyBkp2(ulDbId, (VOS_UINT8 *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = TPC_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
