/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 【V1兼容配置DB交付】支持表缩容接口
 Notes        :
 Author       : wuxueqi 00495442
 Modification :
 create       : 2025/02/26
**************************************************************************** */
#include "tools.h"

class supportTableScaling : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"enableReleaseDevice=1\"");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");

        // 创建自定义数据类型
        DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
        customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
        uint32_t dataTypeId = 100;
        int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void supportTableScaling::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl";
    int ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_compressCallbackTimes = 0;
    g_sleepOver = false;
}
void supportTableScaling::TearDown()
{
    int ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  : TPC_CompressTable的ulDbId不存在, 预期失败
 Notes        :
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId + 1, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    ret = TestTPC_SysviewGetLastError("DB with id (1) not exists.", false);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable的ulDbId存在但未Open, 预期失败
 Notes        :
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 关闭DB
    ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    ret = TestTPC_SysviewGetLastError("Namespace dose not opened.", false);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &g_dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable的usTblId不存在, 预期失败
 Notes        :
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId + 1, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
    ret = TestTPC_SysviewGetLastError("Relation with id (1) does not exist.", false);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable的pfnCompNotify参数为NULL, 预期成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 500, 0, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable操作的DB非TPC DB, 预期失败
 Notes        :
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl2";
    int ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t dbId = 0;
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)testDbName, &dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t relId = 0;
    ret = TestDB_CreateTbl(dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表压缩
    ret = TestTPC_CompressTable(dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    ret = TestTPC_SysviewGetLastError("DB is not tpc db when compress table.", false);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
}

/* ****************************************************************************
 Description  : TPC_CompressTable接口调用时DB内存在活跃的CDB, 预期失败
 Notes        :
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交cdb
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable接口调用时DB内存在活跃的CDB, 且有DML操作, 预期失败
 Notes        :
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    ret = TestTPC_SysviewGetLastError("Some active CDB operating on the DB when compress table, DB id 0.", false);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交cdb
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable接口调用时DB内存在活跃的Handle, 预期失败
 Notes        :
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t tblRecLen = 0;
    ret = TestGetTblRecLen(g_dbId, relId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 批量查询
    DB_COND_STRU cond = {0};
    cond.usCondNum = 1;
    cond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    cond.aCond[0].ucFieldId = 0;
    *(uint32_t *)cond.aCond[0].aucValue = 0;
    DB_FIELDFILTER_STRU fldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_dbId, relId, &cond, &fldFilter, &selectHdl);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_HANDLESACTIVE_FOR_REL, ret);
    ret = TestTPC_SysviewGetLastError("Some active handle operating on the rel when compress table. Rel id 0.", false);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recNum = REC_NUM_PER_PAGE * 4;
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen * recNum);
    (void)memset_s(recBuf, tblRecLen * recNum, 0x00, tblRecLen * recNum);
    DB_DSBUF_STRU dsBuf = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = recNum,
        .StdBuf = {.ulBufLen = tblRecLen * recNum, .ulActLen = tblRecLen * recNum, .pucData = recBuf}};
    for (int32_t j = 0; j < 1; j++) {
        (void)memset_s(recBuf, tblRecLen * recNum, 0x00, tblRecLen * recNum);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_HANDLESACTIVE_FOR_REL, ret);
    ret = TestTPC_SysviewGetLastError("Some active handle operating on the rel when compress table. Rel id 0.", false);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);

    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable操作的表为大对象表, 预期不报错但不触发压缩
 Notes        : 大对象的缩容兼容, 后续有别的SR实现
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tblBigObj.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int insertNum = 256;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 500, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTblBigObj(g_dbId, relId, i + 1000000);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 每条数据64B, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为128,
                预期提前结束压缩, 回调函数调用0次, 未释放页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 176, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个不含索引的表, 每条数据64B, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为128,
                预期提前结束压缩, 回调函数调用0次, 未释放页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 176, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为256,
                预期正常完成压缩, 回调函数调用256次, 释放掉1个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 177);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个不含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为256,
                预期正常完成压缩, 回调函数调用256次, 释放掉1个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 177);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为384,
                预期正常完成压缩, 回调函数调用256次, 释放掉1个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 265, 177);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个不含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为384,
                预期正常完成压缩, 回调函数调用256次, 释放掉1个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 265, 177);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为512,
                预期正常完成压缩, 回调函数调用512次, 释放掉2个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 354, 354);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个不含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为512,
                预期正常完成压缩, 回调函数调用512次, 释放掉2个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 354, 354);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为1024,
                预期正常完成压缩, 回调函数调用512次, 释放掉2个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 708, 531);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数为1024,
                预期正常完成压缩, 回调函数调用512次, 释放掉2个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 708, 531);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制4页的数据, 隔1行删除数据, 表压缩的最大记录数超出表记录数,
                预期正常完成压缩, 回调函数调用512次, 释放掉2个页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 10000, 531);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制4页的数据, 第3页隔100行删除数据, 第4页隔1行删除数据, 表压缩的最大记录数为256,
                预期提前结束压缩, 回调函数调用0次, 未释放页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    int cnt = 0;
    // 第3页隔100行删除
    for (int i = REC_NUM_PER_PAGE * 2; i < REC_NUM_PER_PAGE * 3; i = i + 100) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    // 第4页隔行删除数据
    for (int i = REC_NUM_PER_PAGE * 3; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum - cnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 4096, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = REC_NUM_PER_PAGE * 2; i < REC_NUM_PER_PAGE * 3; i = i + 100) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = REC_NUM_PER_PAGE * 3; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个不含索引的表, 预制4页的数据, 第3页隔100行删除数据, 第4页隔1行删除数据, 表压缩的最大记录数为256,
                预期提前结束压缩, 回调函数调用0次, 未释放页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    int cnt = 0;
    // 第3页隔100行删除
    for (int i = REC_NUM_PER_PAGE * 2; i < REC_NUM_PER_PAGE * 3; i = i + 100) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    // 第4页隔行删除数据
    for (int i = REC_NUM_PER_PAGE * 3; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum - cnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 4096, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = REC_NUM_PER_PAGE * 2; i < REC_NUM_PER_PAGE * 3; i = i + 100) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = REC_NUM_PER_PAGE * 3; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制4页的数据, 第1页隔100行删除数据, 第2,3,4页隔1行删除数据, 表压缩的最大记录数为256,
                预期提前结束压缩, 回调函数调用256次, 释放page2, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    int cnt = 0;
    // 第1页隔100行删除数据
    for (int i = 0; i < REC_NUM_PER_PAGE; i = i + 100) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    // 第2,3,4页隔1行删除数据
    for (int i = REC_NUM_PER_PAGE; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum - cnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 4096, 354);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < REC_NUM_PER_PAGE; i = i + 100) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = REC_NUM_PER_PAGE; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个不含索引的表, 预制4页的数据, 第1页隔100行删除数据, 第2,3,4页隔1行删除数据,表压缩的最大记录数为256,
                预期提前结束压缩, 回调函数调用256次, 释放page2, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    int cnt = 0;
    // 第1页隔100行删除数据
    for (int i = 0; i < REC_NUM_PER_PAGE; i = i + 100) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    // 第2,3,4页隔1行删除数据
    for (int i = REC_NUM_PER_PAGE; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum - cnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 4096, 354);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < REC_NUM_PER_PAGE; i = i + 100) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = REC_NUM_PER_PAGE; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个含索引的表, 预制3个device的数据, 所有页隔行删除数据, 表压缩的最大记录数为1000000,
                预期提前结束压缩, 回调函数调用>0次(有几万次), 释放页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int deviceNum = 3;
    int insertNum = REC_NUM_PER_PAGE * PAGE_NUM_PER_DEVICE * deviceNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 500000, 94114);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 1个不含索引的表, 预制3个device的数据, 所有页隔行删除数据, 表压缩的最大记录数为1000000,
                预期提前结束压缩, 回调函数调用>0次(有几万次), 释放页, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int deviceNum = 3;
    int insertNum = REC_NUM_PER_PAGE * PAGE_NUM_PER_DEVICE * deviceNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_INFO, "delete1 start");
    int cnt = 0;
    // 隔行删除数据, 无索引等值较慢, 改为隔多行删除
    for (int i = 0; i < insertNum; i = i + 10) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        cnt++;
    }
    AW_FUN_Log(LOG_INFO, "delete1 end");
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum - cnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    AW_FUN_Log(LOG_INFO, "compress start");
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 500000, 35000);
    AW_FUN_Log(LOG_INFO, "compress end");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_INFO, "insert2 start");
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 10) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_INFO, "insert2 end");
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_INFO, "delete2 start");
    // 删除
    ret = DeleteTbl64B(g_dbId, relId, 0, false, false);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_INFO, "delete2 end");
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 多个表, 部分含索引, 部分不含索引, 每个表预制3个device的数据
                (预制数据时交替插入, 如以1w条为1个循环),
                每页删除90%的数据, 表压缩的最大记录数为1000000,
                预期提前结束压缩, 回调函数调用>0次(有几万次), 释放页, device内存归还os, sysview视图验证归还情况, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建20个表
    const int tableNum = 6;
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum / 2; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = tableNum / 2; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblNoIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 所有表交替插入数据, 每轮插入insertNumPerCycle=16384条, 共交替cycleTimes=12轮
    int deviceNum = 3, insertNumPerCycle = 16384;
    int cycleTimes = REC_NUM_PER_PAGE * PAGE_NUM_PER_DEVICE * deviceNum / insertNumPerCycle;
    for (int i = 0; i < cycleTimes; i++) {
        AW_FUN_Log(LOG_INFO, "[INSERT] cycleTimes : %d", i);
        for (int j = 0; j < tableNum; j++) {
            for (int k = i * insertNumPerCycle; k < i * insertNumPerCycle + insertNumPerCycle; k++) {
                int ret = InsertTbl64B(g_dbId, relId[j], k, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    // 查询
    for (int i = 0; i < tableNum; i++) {
        AW_FUN_Log(LOG_INFO, "[QUERY 1] relId : %u", relId[i]);
        int ret = TestTPC_GetRelActRec(g_dbId, relId[i], cycleTimes * insertNumPerCycle);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 无索引单条查询很慢, 单表30+min
    for (int i = 0; i < tableNum / 2; i++) {
        AW_FUN_Log(LOG_INFO, "[QUERY 2] relId : %u", relId[i]);
        for (int j = 0; j < cycleTimes * insertNumPerCycle; j++) {
            int ret = SelectTbl64B(g_dbId, relId[i], j, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 前5个表的每页删除90%的数据, 无索引的表删除全部(单条删太慢)
    for (int i = 0; i < tableNum / 2; i++) {
        AW_FUN_Log(LOG_INFO, "[DELETE] relId : %u", relId[i]);
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = j * REC_NUM_PER_PAGE; k < (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9); k++) {
                int ret = DeleteTbl64B(g_dbId, relId[i], k, false, true);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        int ret = TestTPC_GetRelActRec(g_dbId, relId[i], cycleTimes * insertNumPerCycle - cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = tableNum / 2; i < tableNum; i++) {
        AW_FUN_Log(LOG_INFO, "[DELETE] relId : %u", relId[i]);
        int ret = DeleteTbl64B(g_dbId, relId[i], 0, false, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestTPC_GetRelActRec(g_dbId, relId[i], 0);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 更新前5个表
    for (int i = 0; i < tableNum / 2; i++) {
        AW_FUN_Log(LOG_INFO, "[UPDATE] relId : %u", relId[i]);
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9);
                 k < j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE; k++) {
                int ret = UpdateTbl64B(g_dbId, relId[i], k, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        int ret = TestTPC_GetRelActRec(g_dbId, relId[i], cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    WaitForPurgeDelete();
    int devPhySize1 = 0, devPhySize2 = 0;
    int ret =
        GetSysviewValue(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, "devMgrMemctx ctx share memory tree physics size", &devPhySize1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表压缩1轮
    int32_t lastCallbackTimes = 0, curCallbackTimes = 0;
    for (int j = 0; j < 1; j++) {
        for (int i = 0; i < tableNum; i++) {
            AW_FUN_Log(LOG_INFO, "[COMPRESS] relId : %u start, g_compressCallbackTimes : %d", relId[i],
                g_compressCallbackTimes);
            ret = TestTPC_CompressTable(g_dbId, relId[i], VOS_NULL_LONG, -1);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            AW_FUN_Log(LOG_INFO, "[COMPRESS] relId : %u end, g_compressCallbackTimes : %d, increased %d", relId[i],
                g_compressCallbackTimes, g_compressCallbackTimes - lastCallbackTimes);
            curCallbackTimes = g_compressCallbackTimes;
            if (j == 0) {
                // 第一轮压缩才判断条数
                if (i < tableNum / 2) {
                    ASSERT_GE(curCallbackTimes, lastCallbackTimes);
                } else {
                    V1_AW_MACRO_ASSERT_EQ_INT(curCallbackTimes, lastCallbackTimes);
                }
            }
            lastCallbackTimes = curCallbackTimes;
        }
    }
    ret =
        GetSysviewValue(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, "devMgrMemctx ctx share memory tree physics size", &devPhySize2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ASSERT_LT(devPhySize2, devPhySize1);
    // 看护内存压缩效果不裂化 DTS2025031327998
    ASSERT_LT(devPhySize2, 17 * 1024 * 1024);
    WriteFile(devPhySize2);
    exportDataFile(g_dbId);

    // 删除前5个表, 清空
    for (int i = 0; i < tableNum / 2; i++) {
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9);
                 k < j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE; k++) {
                ret = DeleteTbl64B(g_dbId, relId[i], k + 1000000, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
        // 查询
        ret = TestTPC_GetRelActRec(g_dbId, relId[i], 0);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}

class supportTableScalingMultiPro : public testing::Test {
protected:
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};

public:
    virtual void SetUp();
    virtual void TearDown();
};

void supportTableScalingMultiPro::SetUp()
{}
void supportTableScalingMultiPro::TearDown()
{}
/* ****************************************************************************
 Description  : 多个表, 部分含索引, 部分不含索引, 分别插入，使每张表占用来自不同的device的页。
                做大量增删查改, 表压缩后, 查看系统共享内存,
                预期与直接写入最终数据占用的内存一致
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScalingMultiPro, V1Com_025_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 各种增删改查+压缩后的数据
    int ret = system("./supTblScaling --gtest_filter=*.*V1Com_025_027 > 027.txt");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 直接写入最终数据
    ret = system("./supTblScaling --gtest_also_run_disabled_tests --gtest_filter=*.*V1Com_025_099 > 099.txt");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 回调函数中sleep 5s, 预期压缩功能正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 2;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 177, CompressTblNotifyFuncSleep);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 回调函数中修改DB ID和表ID, 预期压缩功能正常, DB ID和表ID修改不生效
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 177, 177, CompressTblNotifyFuncModify);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable和图拓扑交互
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建2个表
    const int tableNum = 2;
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    int ret = TestCreateEdge(g_dbId, relId[0], relId[1]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    for (int i = 0; i < tableNum; i++) {
        // 插入
        for (int j = 0; j < insertNum; j++) {
            ret = InsertTbl64B(g_dbId, relId[i], j, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 查询
        ret = TestTPC_GetRelActRec(g_dbId, relId[i], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int j = 0; j < insertNum; j++) {
            ret = SelectTbl64B(g_dbId, relId[i], j, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    for (int i = 0; i < tableNum; i++) {
        // 隔行删除
        for (int j = 0; j < insertNum; j = j + 2) {
            ret = DeleteTbl64B(g_dbId, relId[i], j, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 查询
        ret = TestTPC_GetRelActRec(g_dbId, relId[i], insertNum / 2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    WaitForPurgeDelete();
    for (int i = 0; i < tableNum; i++) {
        // 表压缩
        ret = TestTPC_CompressTable(g_dbId, relId[i], 4096, -1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = SelectEdge(g_dbId, relId[0], relId[1]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < tableNum; i++) {
        // 更新
        for (int j = 1; j < insertNum; j = j + 2) {
            ret = UpdateTbl64B(g_dbId, relId[i], j, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 查询
        ret = TestTPC_GetRelActRec(g_dbId, relId[i], insertNum / 2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}

/* ****************************************************************************
 Description  : TPC_CompressTable和持久化DB的导入导出交互
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateDir();
    // 创建持久化DB
    const char testDbName[DB_NAME_LEN] = "testDdl2";
    char perFilePath[64] = "./filePath/file";
    DB_INST_CONFIG_STRU testDbCfg = {0};
    testDbCfg.enPersistent = DB_CKP_COMPLETE;
    int ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)perFilePath, &testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t dbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)perFilePath, (VOS_UINT8 *)testDbName, &dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t relId = 0;
    ret = TestTPC_CreateTbl(dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(dbId, relId, 10000, 531);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(dbId, (uint8_t *)filePath);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入
    DB_RESTORE_CONFIG_STRU config = {.bPersistent = true};
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)perFilePath, DB_RESTORETYPE_REPLACE,
        &config, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取元数据
    ret = TPC_OpenDB((VOS_UINT8 *)perFilePath, (VOS_UINT8 *)testDbName, &dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_GetTblId(dbId, (uint8_t *)"tbl64BWithIndex", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable和非持久化DB的导入导出交互
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(g_dbId, relId, 10000, 531);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导出
    CreateDir();
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入
    DB_RESTORE_CONFIG_STRU config = {.bPersistent = false};
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)testDbName, NULL, DB_RESTORETYPE_REPLACE, &config, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取元数据
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &g_dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_GetTblId(g_dbId, (uint8_t *)"tbl64BWithIndex", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId, i + 1000000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : TPC_CompressTable和活跃的CDB并发
                预期beginCDB成功, DML可能被表压缩阻塞导致死等, 但不会失败
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_t tid[2];
    // 线程1循环进行DML
    bool isCdb = true;
    ret = pthread_create(&tid[0], NULL, threadCdbDML, &isCdb);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2循环进行表缩容
    ThrParaT para = {.threadId = 1, .dbId = g_dbId, .relId = relId, .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : TPC_CompressTable和活跃的Handle并发
                预期handle可以开启, 后续的拿handle进行的操作会被阻塞, 但不会失败
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行DML
    ret = pthread_create(&tid[0], NULL, threadBatchQuery, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2循环进行表缩容
    ThrParaT para = {.threadId = 1, .dbId = g_dbId, .relId = relId, .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : TPC_CompressTable和删表删DB并发
                预期两个加同一把锁, 没抢到的会死等, 假设压缩先抢到了, 则两个都成功, 假设删表删DB先抢到了, 则压缩失败
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行DML
    ret = pthread_create(&tid[0], NULL, threadDropTblAndDB, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2循环进行表缩容
    ThrParaT para = {.threadId = 1, .dbId = g_dbId, .relId = relId, .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : TPC_CompressTable和TPC_ModifyTblNameAndID并发
                预期两个加同一把锁, 没抢到的会死等, 假设压缩先抢到了, 则两个都成功, 假设改表ID先抢到了, 则压缩失败
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行DML
    ret = pthread_create(&tid[0], NULL, threadModifyTblId, NULL);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2循环进行表缩容
    ThrParaT para = {.threadId = 1, .dbId = g_dbId, .relId = relId, .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : TPC_CompressTable和RDB的增删改查并发
                预期全成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_t tid[2];
    // 线程1循环进行DML
    bool isCdb = false;
    ret = pthread_create(&tid[0], NULL, threadCdbDML, &isCdb);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2循环进行表缩容
    ThrParaT para = {.threadId = 1, .dbId = g_dbId, .relId = relId, .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : TPC_CompressTable和导入导出并发
                预期全成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行导入导出
    bool isCdb = false;
    ret = pthread_create(&tid[0], NULL, threadBkpAndRestore, &isCdb);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2循环进行表缩容
    ThrParaT para = {.threadId = 1, .dbId = g_dbId, .relId = relId, .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : 同一个表并发进行表压缩
                预期全成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();

    const int tNum = 3;
    pthread_t tid[tNum];
    ThrParaT para = {.threadId = 0, .dbId = g_dbId, .relId = relId, .compressTimes = 5000, .verifyDiffDb = false};
    // 线程循环进行表缩容
    for (int i = 0; i < tNum; i++) {
        ret = pthread_create(&tid[i], NULL, threadCompress, &para);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < tNum; i++) {
        pthread_join(tid[i], NULL);
    }
}

/* ****************************************************************************
 Description  : 同一个表并发进行表压缩, 验证加锁粒度为nsp级别
                预期全成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint16_t relId = 0;
    int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行表1缩容
    ThrParaT para1 = {.threadId = 0, .dbId = g_dbId, .relId = relId, .compressTimes = 1, .verifyDiffDb = false};
    ret = pthread_create(&tid[0], NULL, threadCompressLock, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    sleep(3);  // 确保线程1进入缩容接口
    // 线程2循环进行表2缩容
    ThrParaT para2 = {.threadId = 1, .dbId = g_dbId, .relId = relId, .compressTimes = 1, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : 同一个DB不同表并发进行表压缩
                预期全成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const int tableNum = 2;
    // 建表
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 所有表交替插入数据, 每轮插入insertNumPerCycle=16384条, 共交替cycleTimes=12轮
    int deviceNum = 3, insertNumPerCycle = 16384;
    int cycleTimes = REC_NUM_PER_PAGE * PAGE_NUM_PER_DEVICE * deviceNum / insertNumPerCycle;
    for (int i = 0; i < cycleTimes; i++) {
        AW_FUN_Log(LOG_INFO, "[INSERT] cycleTimes : %d", i);
        for (int j = 0; j < tableNum; j++) {
            for (int k = i * insertNumPerCycle; k < i * insertNumPerCycle + insertNumPerCycle; k++) {
                int ret = InsertTbl64B(g_dbId, relId[j], k, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    // 每个表的每页删除90%的数据
    for (int i = 0; i < tableNum; i++) {
        AW_FUN_Log(LOG_INFO, "[DELETE] relId : %u", relId[i]);
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = j * REC_NUM_PER_PAGE; k < (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9); k++) {
                int ret = DeleteTbl64B(g_dbId, relId[i], k, false, true);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        int ret = TestTPC_GetRelActRec(g_dbId, relId[i], cycleTimes * insertNumPerCycle - cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行表1缩容
    ThrParaT para1 = {.threadId = 0, .dbId = g_dbId, .relId = relId[0], .compressTimes = 5000, .verifyDiffDb = false};
    int ret = pthread_create(&tid[0], NULL, threadCompress, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2循环进行表2缩容
    ThrParaT para2 = {.threadId = 1, .dbId = g_dbId, .relId = relId[1], .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : 同一个DB不同表并发进行表压缩, 验证加锁粒度为nsp级别
                预期全成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const int tableNum = 2;
    // 建表
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 所有表交替插入数据, 每轮插入insertNumPerCycle=16384条, 共交替cycleTimes=12轮
    int deviceNum = 2, insertNumPerCycle = 16384;
    int cycleTimes = REC_NUM_PER_PAGE * PAGE_NUM_PER_DEVICE * deviceNum / insertNumPerCycle;
    for (int i = 0; i < cycleTimes; i++) {
        AW_FUN_Log(LOG_INFO, "[INSERT] cycleTimes : %d", i);
        for (int j = 0; j < tableNum; j++) {
            for (int k = i * insertNumPerCycle; k < i * insertNumPerCycle + insertNumPerCycle; k++) {
                int ret = InsertTbl64B(g_dbId, relId[j], k, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    // 每个表的每页删除90%的数据
    for (int i = 0; i < tableNum; i++) {
        AW_FUN_Log(LOG_INFO, "[DELETE] relId : %u", relId[i]);
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = j * REC_NUM_PER_PAGE; k < (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9); k++) {
                int ret = DeleteTbl64B(g_dbId, relId[i], k, false, true);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        int ret = TestTPC_GetRelActRec(g_dbId, relId[i], cycleTimes * insertNumPerCycle - cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行表1缩容
    ThrParaT para1 = {.threadId = 0, .dbId = g_dbId, .relId = relId[0], .compressTimes = 1, .verifyDiffDb = false};
    int ret = pthread_create(&tid[0], NULL, threadCompressLock, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    sleep(3);  // 确保线程1进入缩容接口
    // 线程2循环进行表2缩容
    ThrParaT para2 = {.threadId = 1, .dbId = g_dbId, .relId = relId[1], .compressTimes = 1, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/* ****************************************************************************
 Description  : 不同DB的表并发进行表压缩
                预期全成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateDir();
    // 创建持久化DB
    const char testDbName[DB_NAME_LEN] = "testDdl2";
    char perFilePath[64] = "./filePath/file";
    DB_INST_CONFIG_STRU testDbCfg = {0};
    testDbCfg.enPersistent = DB_CKP_COMPLETE;
    int ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)perFilePath, &testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t dbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)perFilePath, (VOS_UINT8 *)testDbName, &dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    const int tableNum = 2;
    uint32_t dbIdArr[tableNum] = {g_dbId, dbId};
    // 建表
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        ret = TestTPC_CreateTbl(dbIdArr[i], "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 所有表交替插入数据, 每轮插入insertNumPerCycle=16384条, 共交替cycleTimes=12轮
    int deviceNum = 2, insertNumPerCycle = 16384;
    int cycleTimes = REC_NUM_PER_PAGE * PAGE_NUM_PER_DEVICE * deviceNum / insertNumPerCycle;
    for (int i = 0; i < cycleTimes; i++) {
        AW_FUN_Log(LOG_INFO, "[INSERT] cycleTimes : %d", i);
        for (int j = 0; j < tableNum; j++) {
            for (int k = i * insertNumPerCycle; k < i * insertNumPerCycle + insertNumPerCycle; k++) {
                ret = InsertTbl64B(dbIdArr[j], relId[j], k, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    // 每个表的每页删除90%的数据
    for (int i = 0; i < tableNum; i++) {
        AW_FUN_Log(LOG_INFO, "[DELETE] relId : %u", relId[i]);
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = j * REC_NUM_PER_PAGE; k < (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9); k++) {
                ret = DeleteTbl64B(dbIdArr[i], relId[i], k, false, true);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        ret = TestTPC_GetRelActRec(dbIdArr[i], relId[i], cycleTimes * insertNumPerCycle - cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行表1缩容
    ThrParaT para1 = {.threadId = 0, .dbId = dbIdArr[0], .relId = relId[0], .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[0], NULL, threadCompress, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2循环进行表2缩容
    ThrParaT para2 = {.threadId = 1, .dbId = dbIdArr[1], .relId = relId[1], .compressTimes = 5000, .verifyDiffDb = false};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

/* ****************************************************************************
 Description  : 不同DB的表并发进行表压缩, 验证加锁粒度为nsp级别
                预期全成功
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScaling, V1Com_025_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateDir();
    // 创建持久化DB
    const char testDbName[DB_NAME_LEN] = "testDdl2";
    char perFilePath[64] = "./filePath/file";
    DB_INST_CONFIG_STRU testDbCfg = {0};
    testDbCfg.enPersistent = DB_CKP_COMPLETE;
    int ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)perFilePath, &testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t dbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)perFilePath, (VOS_UINT8 *)testDbName, &dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    const int tableNum = 2;
    uint32_t dbIdArr[tableNum] = {g_dbId, dbId};
    // 建表
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        ret = TestTPC_CreateTbl(dbIdArr[i], "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 所有表交替插入数据, 每轮插入insertNumPerCycle=16384条, 共交替cycleTimes=12轮
    int deviceNum = 3, insertNumPerCycle = 16384;
    int cycleTimes = REC_NUM_PER_PAGE * PAGE_NUM_PER_DEVICE * deviceNum / insertNumPerCycle;
    for (int i = 0; i < cycleTimes; i++) {
        AW_FUN_Log(LOG_INFO, "[INSERT] cycleTimes : %d", i);
        for (int j = 0; j < tableNum; j++) {
            for (int k = i * insertNumPerCycle; k < i * insertNumPerCycle + insertNumPerCycle; k++) {
                ret = InsertTbl64B(dbIdArr[j], relId[j], k, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    // 每个表的每页删除90%的数据
    for (int i = 0; i < tableNum; i++) {
        AW_FUN_Log(LOG_INFO, "[DELETE] relId : %u", relId[i]);
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = j * REC_NUM_PER_PAGE; k < (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9); k++) {
                ret = DeleteTbl64B(dbIdArr[i], relId[i], k, false, true);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        ret = TestTPC_GetRelActRec(dbIdArr[i], relId[i], cycleTimes * insertNumPerCycle - cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    WaitForPurgeDelete();

    pthread_t tid[2];
    // 线程1循环进行表1缩容
    ThrParaT para1 = {.threadId = 0, .dbId = dbIdArr[0], .relId = relId[0], .compressTimes = 1, .verifyDiffDb = true};
    ret = pthread_create(&tid[0], NULL, threadCompressLock, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    sleep(3);  // 确保线程1进入缩容接口
    // 线程2循环进行表2缩容
    ThrParaT para2 = {.threadId = 1, .dbId = dbIdArr[1], .relId = relId[1], .compressTimes = 1, .verifyDiffDb = true};
    ret = pthread_create(&tid[1], NULL, threadCompress, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
}

class supportTableScalingMemory : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"enableReleaseDevice=1\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=32\"");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    };
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void supportTableScalingMemory::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl";
    int ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_compressCallbackTimes = 0;
    g_sleepOver = false;
}
void supportTableScalingMemory::TearDown()
{
    int ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_CHECK_LOG_END();
}
/* ****************************************************************************
 Description  : maxSeMem配置为32M, 写表1直至内存满, 表2写入失败,  隔行删除表1数据, 表2能写入部分数据,
                表1进行压缩后, 表2能继续写入数据, 表1删除所有数据后, 不需压缩(可能需要压缩),
                表2能继续写入数据, 且数据量和表1写满时相差不大
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScalingMemory, V1Com_025_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建2个表
    const int tableNum = 2;
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    int insertNum1 = 0, insertNum2 = 0, ret, diff = 2000;
    // 写表1直至内存满
    while (ret == DB_SUCCESS_V1) {
        ret = InsertTbl64B(g_dbId, relId[0], insertNum1, false);
        if (ret == DB_SUCCESS_V1) {
            insertNum1++;
        }
    }
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
    AW_FUN_Log(LOG_INFO, "insertNum1 : %d", insertNum1);
    // 查询表1
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum1; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写表2, 预期写入失败
    while (ret == DB_SUCCESS_V1) {
        ret = InsertTbl64B(g_dbId, relId[1], insertNum2, false);
        if (ret == DB_SUCCESS_V1) {
            insertNum2++;
        }
    }
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(0, insertNum2);
    // 查询表2
    ret = TestTPC_GetRelActRec(g_dbId, relId[1], insertNum2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 隔行删除表1
    for (int i = 0; i < insertNum1; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId[0], i, false);
        int cnt = 0;
        while (ret == VOS_ERRNO_DB_MEMALLOCFAILURE && cnt < 1000) {
            AW_FUN_Log(LOG_DEBUG, "[DELETE1] i : %d", i);
            // 根据 DTS2025030508933 结论进行失败重试
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Out of memory", false));
            usleep(10000);
            ret = DeleteTbl64B(g_dbId, relId[0], i, false);
            cnt++;
        }
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum1 / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 再次写表2, 预期能写入部分数据
    while (ret == DB_SUCCESS_V1) {
        ret = InsertTbl64B(g_dbId, relId[1], insertNum2, false);
        if (ret == DB_SUCCESS_V1) {
            insertNum2++;
        }
    }
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(0, insertNum2);
    // 表1压缩
    ret = TestTPC_CompressTable(g_dbId, relId[0], 10000000, 90270);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 再次写表2, 预期能再写入部分数据
    while (ret == DB_SUCCESS_V1) {
        ret = InsertTbl64B(g_dbId, relId[1], insertNum2, false);
        if (ret == DB_SUCCESS_V1) {
            insertNum2++;
        }
    }
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
    AW_FUN_Log(LOG_INFO, "line%d insertNum2 : %d", __LINE__, insertNum2);
    ASSERT_LT(insertNum1 / 2 - diff, insertNum2);
    // 再次删除表1, 把表1清空
    for (int i = 1; i < insertNum1; i = i + 2) {
        ret = DeleteTbl64B(g_dbId, relId[0], i, false);
        int cnt = 0;
        while (ret == VOS_ERRNO_DB_MEMALLOCFAILURE && cnt < 1000) {
            AW_FUN_Log(LOG_DEBUG, "[DELETE2] i : %d", i);
            // 根据 DTS2025030508933 结论进行失败重试
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Out of memory", false));
            usleep(10000);
            ret = DeleteTbl64B(g_dbId, relId[0], i, false);
            cnt++;
        }
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表1压缩
    ret = TestTPC_CompressTable(g_dbId, relId[0], 10000000, 90270);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 再次写表2直至内存满, 预期数据量和表1写满时相差不大
    while (ret == DB_SUCCESS_V1) {
        ret = InsertTbl64B(g_dbId, relId[1], insertNum2, false);
        if (ret == DB_SUCCESS_V1) {
            insertNum2++;
        }
    }
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
    AW_FUN_Log(LOG_INFO, "insertNum2 : %d", insertNum2);
    ASSERT_GT(diff, abs(insertNum1 - insertNum2));
}

class supportTableScalingNotRelease : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    };
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void supportTableScalingNotRelease::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    DB_INST_CONFIG_STRU testDbCfg = {0};
    const char testDbName[DB_NAME_LEN] = "testDdl";
    int ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_compressCallbackTimes = 0;
    g_sleepOver = false;
}
void supportTableScalingNotRelease::TearDown()
{
    int ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    const char testDbName[DB_NAME_LEN] = "testDdl";
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_CHECK_LOG_END();
}
/* ****************************************************************************
 Description  : 不开启enableReleaseDevice配置项, 验证device不归还OS
                多个表, 部分含索引, 部分不含索引, 每个表预制3个device的数据
                (预制数据时交替插入, 如以1w条为1个循环),
                每页删除90%的数据, 表压缩的最大记录数为1000000,
                预期提前结束压缩, 回调函数调用>0次(有几万次), 释放页, device内存归还os, sysview视图验证归还情况, 且后续DML操作正常
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
**************************************************************************** */
TEST_F(supportTableScalingNotRelease, V1Com_025_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建20个表
    const int tableNum = 6;
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum / 2; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = tableNum / 2; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblNoIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 所有表交替插入数据, 每轮插入insertNumPerCycle=16384条, 共交替cycleTimes=12轮
    int deviceNum = 3, insertNumPerCycle = 16384;
    int cycleTimes = REC_NUM_PER_PAGE * PAGE_NUM_PER_DEVICE * deviceNum / insertNumPerCycle;
    for (int i = 0; i < cycleTimes; i++) {
        AW_FUN_Log(LOG_INFO, "[INSERT] cycleTimes : %d", i);
        for (int j = 0; j < tableNum; j++) {
            for (int k = i * insertNumPerCycle; k < i * insertNumPerCycle + insertNumPerCycle; k++) {
                int ret = InsertTbl64B(g_dbId, relId[j], k, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    // 查询
    for (int i = 0; i < tableNum; i++) {
        AW_FUN_Log(LOG_INFO, "[QUERY 1] relId : %u", relId[i]);
        int ret = TestTPC_GetRelActRec(g_dbId, relId[i], cycleTimes * insertNumPerCycle);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 无索引单条查询很慢, 单表30+min
    for (int i = 0; i < tableNum / 2; i++) {
        AW_FUN_Log(LOG_INFO, "[QUERY 2] relId : %u", relId[i]);
        for (int j = 0; j < cycleTimes * insertNumPerCycle; j++) {
            int ret = SelectTbl64B(g_dbId, relId[i], j, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 前5个表的每页删除90%的数据, 无索引的表删除全部(单条删太慢)
    for (int i = 0; i < tableNum / 2; i++) {
        AW_FUN_Log(LOG_INFO, "[DELETE] relId : %u", relId[i]);
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = j * REC_NUM_PER_PAGE; k < (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9); k++) {
                int ret = DeleteTbl64B(g_dbId, relId[i], k, false, true);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        int ret = TestTPC_GetRelActRec(g_dbId, relId[i], cycleTimes * insertNumPerCycle - cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = tableNum / 2; i < tableNum; i++) {
        AW_FUN_Log(LOG_INFO, "[DELETE] relId : %u", relId[i]);
        int ret = DeleteTbl64B(g_dbId, relId[i], 0, false, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestTPC_GetRelActRec(g_dbId, relId[i], 0);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 更新前5个表
    for (int i = 0; i < tableNum / 2; i++) {
        AW_FUN_Log(LOG_INFO, "[UPDATE] relId : %u", relId[i]);
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9);
                 k < j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE; k++) {
                int ret = UpdateTbl64B(g_dbId, relId[i], k, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        int ret = TestTPC_GetRelActRec(g_dbId, relId[i], cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    WaitForPurgeDelete();
    int devPhySize1 = 0, devPhySize2 = 0;
    int ret =
        GetSysviewValue(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, "devMgrMemctx ctx share memory tree physics size", &devPhySize1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表压缩
    int32_t lastCallbackTimes = 0, curCallbackTimes = 0;
    for (int i = 0; i < tableNum; i++) {
        AW_FUN_Log(
            LOG_INFO, "[COMPRESS] relId : %u start, g_compressCallbackTimes : %d", relId[i], g_compressCallbackTimes);
        ret = TestTPC_CompressTable(g_dbId, relId[i], VOS_NULL_LONG, -1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        AW_FUN_Log(LOG_INFO, "[COMPRESS] relId : %u end, g_compressCallbackTimes : %d, increased %d", relId[i],
            g_compressCallbackTimes, g_compressCallbackTimes - lastCallbackTimes);
        curCallbackTimes = g_compressCallbackTimes;
        if (i < tableNum / 2) {
            ASSERT_GE(curCallbackTimes, lastCallbackTimes);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(curCallbackTimes, lastCallbackTimes);
        }
        lastCallbackTimes = curCallbackTimes;
    }
    ret =
        GetSysviewValue(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, "devMgrMemctx ctx share memory tree physics size", &devPhySize2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // DTS2025031010595适配, device写成固定会归还OS, 不受配置项影响
    ASSERT_LT(devPhySize2, devPhySize1);

    // 删除前5个表, 清空
    for (int i = 0; i < tableNum / 2; i++) {
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9);
                 k < j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE; k++) {
                ret = DeleteTbl64B(g_dbId, relId[i], k + 1000000, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
        // 查询
        ret = TestTPC_GetRelActRec(g_dbId, relId[i], 0);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}

/* ****************************************************************************
 Description  : 多个表, 部分含索引, 部分不含索引, 分别插入，使每张表占用来自不同的device的页。
                做大量增删查改, 表压缩后, 查看系统共享内存,
                预期与直接写入最终数据占用的内存一致
 Notes        : 每页32K, 32K/64B=512个slot, 删除后每页有256个freeslot
                非独立运行的用例, 于V1Com_025_028中调用
**************************************************************************** */
TEST_F(supportTableScaling, DISABLED_V1Com_025_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建20个表
    const int tableNum = 3;
    uint16_t relId[tableNum] = {0};
    for (int i = 0; i < tableNum; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = tableNum; i < tableNum * 2; i++) {
        char tblName[16] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblNoIndex%02d", i);
        int ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl64BWithoutIndex.json", &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    int devPhySize1 = 0, devPhySize2 = 0;
    int ret =
        GetSysviewValue(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, "devMgrMemctx ctx share memory tree physics size", &devPhySize1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    int deviceNum = 3;
    for (int i = 0; i < tableNum; i++) {
        int cnt = 0;
        for (int j = 0; j < PAGE_NUM_PER_DEVICE * deviceNum; j++) {
            for (int k = (int)(j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE * 0.9);
                 k < j * REC_NUM_PER_PAGE + REC_NUM_PER_PAGE; k++) {
                ret = InsertTbl64B(g_dbId, relId[i], k + 1000000, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                cnt++;
            }
        }
        // 查询
        ret = TestTPC_GetRelActRec(g_dbId, relId[i], cnt);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        AW_FUN_Log(LOG_INFO, "[INSERT] relId : %u， cnt ： %d", relId[i], cnt);
    }
    ret =
        GetSysviewValue(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, "devMgrMemctx ctx share memory tree physics size", &devPhySize2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    CmpFile(devPhySize2);
    exportDataFile(g_dbId);
}
