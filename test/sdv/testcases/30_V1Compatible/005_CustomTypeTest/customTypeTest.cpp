/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: customTypeTest.cpp
 * Description: 支持V1用户自定义数据类型
 * Author: <PERSON><PERSON><PERSON><PERSON> ywx1252574
 * Create: 2024-07-17
 */
#include "t_rd_simplerel.h"

#define MAX_CMD_SIZE 1024
typedef int32_t Status;
class customTypeTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};
void customTypeTest::SetUp()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
}

void customTypeTest::TearDown()
{
    AW_CHECK_LOG_END();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
}
typedef struct inputS {
    uint32_t ulDbId;
    uint16_t usRelId;
    uint32_t F0;
    uint32_t F1;
    uint32_t F2;
    DB_DSBUF_STRU stBuff;
} inputS;
Status CpmEqual4TypeId40lencp(
    const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue + 10;
    V1_AW_MACRO_EXPECT_EQ_INT(usLen, 4);
    if (fValue < cValue) {
        // 往更大值方向去找
        *ucDir = DBTC_SEARCH_LARGE;
        *bMatch = false;
    } else if (fValue > cValue) {
        *ucDir = DBTC_SEARCH_SMALL;
        *bMatch = false;
    } else {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    }
    return DB_SUCCESS_V1;
}

Status CpmLess4TypeId40(const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue;

    if (fValue < cValue) {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    } else {
        *ucDir = DBTC_SEARCH_SMALL;
        *bMatch = false;
    }
    return DB_SUCCESS_V1;
}
Status CpmLarger4TypeId40(
    const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue;

    if (fValue > cValue) {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    } else {
        *ucDir = DBTC_SEARCH_LARGE;
        *bMatch = false;
    }
    return DB_SUCCESS_V1;
}
Status CpmLessEqual4TypeId40(const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue;

    if (fValue <= cValue) {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    } else {
        *ucDir = DBTC_SEARCH_SMALL;
        *bMatch = false;
    }
    return DB_SUCCESS_V1;
}
Status CpmLargerEqual4TypeId40(
    const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue;

    if (fValue >= cValue) {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    } else {
        *ucDir = DBTC_SEARCH_LARGE;
        *bMatch = false;
    }
    return DB_SUCCESS_V1;
}
Status CpmMaxLess4TypeId40(
    const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue;

    if (fValue < cValue) {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    } else {
        *ucDir = DBTC_SEARCH_SMALL;
        *bMatch = false;
    }
    return DB_SUCCESS_V1;
}
Status CpmMaxLessEqual4TypeId40(const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue;

    if (fValue <= cValue) {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    } else {
        *ucDir = DBTC_SEARCH_SMALL;
        *bMatch = false;
    }
    return DB_SUCCESS_V1;
}
Status CpmMinLarger4TypeId40(
    const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue;

    if (fValue > cValue) {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    } else {
        *ucDir = DBTC_SEARCH_LARGE;
        *bMatch = false;
    }
    return DB_SUCCESS_V1;
}
Status CpmMinLargerEqual4TypeId40(
    const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    uint32_t fValue = *(uint32_t *)filedValue;
    uint32_t cValue = *(uint32_t *)condValue;

    if (fValue >= cValue) {
        *ucDir = DBTC_SEARCH_ALL;
        *bMatch = true;
    } else {
        *ucDir = DBTC_SEARCH_LARGE;
        *bMatch = false;
    }
    return DB_SUCCESS_V1;
}
// 001.新增自定义类型数据时，结构体内typeId传入非法数据
TEST_F(customTypeTest, V1Com_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 7;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);  // 非法数据报错
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Invalid datatype ID", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002.新增自定义类型数据时，结构体内typeId传入重复的数据
TEST_F(customTypeTest, V1Com_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 40;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TYPID_ALREADY_USED, ret);  // typeId重复报错
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Customer type with same id already exist, id : 40", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003.新增自定义类型数据时，结构体内typeId传入32
TEST_F(customTypeTest, V1Com_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004.新增自定义类型数据时，结构体内typeId传入31
TEST_F(customTypeTest, V1Com_005_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 31;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);  // 数据不能小于32
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Invalid datatype ID", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005.新增自定义类型数据时，结构体内typeId传入负数
TEST_F(customTypeTest, V1Com_005_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = -1;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);  // 不能为负数
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Invalid datatype ID", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006.新增自定义类型数据时，结构体内typeId传入254
TEST_F(customTypeTest, V1Com_005_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 254;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007.新增自定义类型数据时，结构体内typeId传入255
TEST_F(customTypeTest, V1Com_005_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 255;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);  // 不能大于254
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Invalid datatype ID", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008.建表后验证传入的len是否一致
TEST_F(customTypeTest, V1Com_005_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    uint32_t dataTypeId = 32;
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CpmEqual4TypeId40lencp;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 1;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REL_DEF_STRU stRelDef;  // 创建表结构体
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_008.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    astFlds = stRelDef.pstFldLst;
    AW_FUN_Log(LOG_STEP, "insert");
    // 调用比较函数，然后验证比较函数内传入的len是否等于对应字段的ussize
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    const uint32_t f4ValueLen = 10;
    *(uint32_t *)value = 66;  // F0
    stBuff.StdBuf.pucData = (uint8_t *)value;
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(value);

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.新建自定义数据类型后，建表时使用的ussize为负数
TEST_F(customTypeTest, V1Com_005_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 1;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REL_DEF_STRU stRelDef;  // 创建表结构体
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_009.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    astFlds = stRelDef.pstFldLst;
    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.新增自定义类型数据时，结构体内比较函数全部为NULL
TEST_F(customTypeTest, V1Com_005_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = NULL;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 1;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REL_DEF_STRU stRelDef;  // 创建表结构体
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_010.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Equal comparison function cannot be null pointer, idx name(PKF2).", false));

    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011.新增自定义类型数据时，结构体内比较函数有23个
TEST_F(customTypeTest, V1Com_005_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = NULL;
    uint32_t dataTypeId = 32;
    
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;  // 定义23个函数
    customCmpFunc[DB_OP_NOTEQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LESS] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LESSEQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGEREQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_HAVEPREFIX] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_NOPREFIX] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LIKE] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_NOTLIKE] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_PREFIX] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_NOTEQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_NOTEQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MAX_PREFIX12] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MAX_PREFIX21] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MIN_PREFIX12] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MIN_PREFIX21] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MAX_LESS] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MAX_LESS_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MIN_LARGER] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MIN_LARGER_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_POSTFIX21] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_MAX_POSTFIX21] = CmpEqual4CustomType;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012.新增自定义类型数据时，TPC_CreateDataType接口传入NULL
TEST_F(customTypeTest, V1Com_005_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Null pointer for input", false));

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013.新增自定义类型数据时，定义其他比较函数，第一个比较函数不定义，建表，自定义类型数据建索引
TEST_F(customTypeTest, V1Com_005_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = NULL;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 1;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REL_DEF_STRU stRelDef;  // 创建表结构体
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_013.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_NOTSUPPORT, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Equal comparison function cannot be null pointer, idx name(PKF2).", false));

    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014.不调用初始化接口TPCV1Init后创建自定义数据类型
TEST_F(customTypeTest, V1Com_005_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    TPC_UnInit();

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);  // 现在会直接在函数内init，预期修改为成功

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015.使用接口新增自定义类型数据，建表只有一个字段且为此类型，插入数据，然后删除数据
TEST_F(customTypeTest, V1Com_005_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 1;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef;  // 创建表结构体
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_015.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);

    // 删除数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.使用接口新增自定义类型数据，建表有多个字段为此类型，插入数据
TEST_F(customTypeTest, V1Com_005_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_016.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据，删除数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);

    // 删除数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;                          // 查几个字段（目前只支持一个）
    pstCond.aCond[0].ucFieldId = 0;                 // 查哪一个字段
    pstCond.aCond[0].enOp = DB_OP_EQUAL;            // 调用哪个比较函数
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;  // 要比较的值
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.使用接口新增多个自定义类型数据，建表有多个字段分别为不同自定义类型，插入数据
TEST_F(customTypeTest, V1Com_005_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    uint32_t dataTypeId1 = 33;
    uint32_t dataTypeId2 = 34;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId2, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_017.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);

    // 删除数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018.使用接口新增自定义类型数据，建表时有普通类型数据字段也有自定义类型字段，插入数据，删除数据
TEST_F(customTypeTest, V1Com_005_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    uint32_t dataTypeId1 = 33;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名
    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_018.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);

    // 删除数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019.使用接口建满自定义类型，然后创建包含所有自定义类型的表
TEST_F(customTypeTest, V1Com_005_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;

    for (int i = 32; i < 255; i++) {
        ret = DB_CreateDataTypeByID(i, customCmpFunc);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 223;                   // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_018.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.建表时指定的typeId不存在
TEST_F(customTypeTest, V1Com_005_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    uint32_t dataTypeId1 = 33;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_020.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDDATATYPE, ret);  // 建表报错，typeId不存在
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Unable to check field type, field name(F1).", false));

    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021.建表时指定的usSize为0
TEST_F(customTypeTest, V1Com_005_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    uint32_t dataTypeId1 = 33;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDataTypeByID(dataTypeId1, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_021.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);  // 建表报错，ussize最少1
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Unable to check field size, field name(F0).", false));

    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022.建表时指定的usSize为65535
TEST_F(customTypeTest, V1Com_005_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 1;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_022.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023.建表时指定的usSize为65536
TEST_F(customTypeTest, V1Com_005_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_023.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);  // ussize最大为65535,超过报错
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Unable to check field size, field name(F0).", false));

    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024.建表时指定单个字段的usSize为1，插入的数据大小大于1
TEST_F(customTypeTest, V1Com_005_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 1;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_024.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen + 11);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 266;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %d |",
            *(uint8_t *)(void *)tmppBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(10,*(uint8_t *)(void *)tmppBuf);
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025.使用接口新增自定义类型数据，使用DB_OP_EQUAL对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_025.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 全部删除

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 026.使用接口新增自定义类型数据，使用DB_OP_EQUAL对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_026.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027.使用接口新增自定义类型数据，使用DB_OP_EQUAL对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_027.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    stCond= {0};
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    pstBuff = {0};
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf2 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf2,
            *(uint32_t *)(void *)(tmppBuf2 + 4),
            *(uint32_t *)(void *)(tmppBuf2 + 8));
        tmppBuf2 += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028.使用接口新增自定义类型数据，使用DB_OP_LARGEREQUAL对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;

    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_028.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 全部删除

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 029.使用接口新增自定义类型数据，使用DB_OP_LARGEREQUAL对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_029.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 删除两条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 030.使用接口新增自定义类型数据，使用DB_OP_LARGEREQUAL对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_030.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 更新两条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 删除两条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 031.使用接口新增自定义类型数据，使用DB_OP_LESS对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_031.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LARGER;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部删除

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 032.使用接口新增自定义类型数据，使用DB_OP_LESS对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_032.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LESS;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 200;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 更新两条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LESS;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 100;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 删除两条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 033.使用接口新增自定义类型数据，使用DB_OP_LESS对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_033.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LESS;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 200;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 更新两条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LESS;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 100;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 删除两条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 034.使用接口新增自定义类型数据，使用DB_OP_LESSEQUAL对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_034.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LESSEQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部删除

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 035.使用接口新增自定义类型数据，使用DB_OP_LESSEQUAL对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_035.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 更新两条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LESSEQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 删除两条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 036.使用接口新增自定义类型数据，使用DB_OP_LESSEQUAL对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_036.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 更新两条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LESSEQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(2, (int32_t)pulRecNum);  // 检查结果 删除两条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 037.使用接口新增自定义类型数据，使用DB_OP_LARGER对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_037.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LARGER;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LARGER;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部删除

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 038.使用接口新增自定义类型数据，使用DB_OP_LARGER对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_038.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LARGER;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新两条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LARGER;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除两条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 039.使用接口新增自定义类型数据，使用DB_OP_LARGER对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_039.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_LARGER;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新两条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_LARGER;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除两条数据

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 040.使用接口新增自定义类型数据，使用DB_OP_MAX_LESS对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_040.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MAX_LESS;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部删除

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);  // 预期没有数据
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 041.使用接口新增自定义类型数据，使用DB_OP_MAX_LESS对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_041.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 266;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    
    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MAX_LESS;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 100;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1),166);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 4),177);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 8),188);
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 042.使用接口新增自定义类型数据，使用DB_OP_MAX_LESS对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_042.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 188;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MAX_LESS;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1),166);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 4),177);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 8),188);
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 043.使用接口新增自定义类型数据，使用DB_OP_MAX_LESS_EQUAL对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    customCmpFunc[DB_OP_MAX_LESS_EQUAL] = CpmMaxLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_043.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部删除

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret); // 预期没有数据
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 044.使用接口新增自定义类型数据，使用DB_OP_MAX_LESS_EQUAL对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    customCmpFunc[DB_OP_MAX_LESS_EQUAL] = CpmMaxLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_044.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 100;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1),99);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 4),11);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 8),22);
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 045.使用接口新增自定义类型数据，使用DB_OP_MAX_LESS_EQUAL对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    customCmpFunc[DB_OP_MAX_LESS_EQUAL] = CpmMaxLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_045.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 188;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1),99);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 4),11);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 8),22);
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 046.使用接口新增自定义类型数据，使用DB_OP_MIN_LARGER对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER] = CpmMaxLessEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_046.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MIN_LARGER;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部删除

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret); // 预期没有数据
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 047.使用接口新增自定义类型数据，使用DB_OP_MIN_LARGER对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER] = CpmMinLarger4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_047.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MIN_LARGER;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 100;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1),266);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 4),277);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 8),288);
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 048.使用接口新增自定义类型数据，使用DB_OP_MIN_LARGER对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER] = CpmMinLarger4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_048.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 188;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MIN_LARGER;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1),166);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 4),177);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 8),188);
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 049.使用接口新增自定义类型数据，使用DB_OP_MIN_LARGER_EQUAL对应的函数进行增删改查，不带条件
TEST_F(customTypeTest, V1Com_005_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER_EQUAL] = CpmMinLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_049.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 0;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 166;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部更新
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 0;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(3, (int32_t)pulRecNum);  // 检查结果 全部删除

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret); // 预期没有数据
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 050.使用接口新增自定义类型数据，使用DB_OP_MIN_LARGER_EQUAL对应的函数进行增删改查，带条件，但是字段不是索引字段
TEST_F(customTypeTest, V1Com_005_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER_EQUAL] = CpmMinLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_050.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 66;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 100;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1),166);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 4),177);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 8),188);
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 051.使用接口新增自定义类型数据，使用DB_OP_MIN_LARGER_EQUAL对应的函数进行增删改查，带条件，且字段是索引字段
TEST_F(customTypeTest, V1Com_005_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER_EQUAL] = CpmMinLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_051.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 188;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf1 = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf1,
            *(uint32_t *)(void *)(tmppBuf1 + 4),
            *(uint32_t *)(void *)(tmppBuf1 + 8));
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1),99);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 4),11);
        EXPECT_NE((*(uint32_t *)(void *)tmppBuf1 + 8),22);
        tmppBuf1 += 12;
    }

    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 052.设定自定义类型数据为唯一二级索引，插入重复数据
TEST_F(customTypeTest, V1Com_005_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER_EQUAL] = CpmMinLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_052.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 66;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));// 字段重复报错
    TEST_V1_FREE(value1);

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 053.自定义数据类型字段写满内存，然后更新和查询和删除数据的场景
TEST_F(customTypeTest, V1Com_005_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    
    TestTPC_UnInit();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=32\"");  // maxTotalShmSize >= maxSysShmSize + maxSeMem + APP(>=12M)
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=8\"");
    TestTPC_Init();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER_EQUAL] = CpmMinLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_053.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);

    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    for(int i = 0; ; i++){
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff);
        if (ret != DB_SUCCESS_V1){
            if(ret == VOS_ERRNO_DB_FAILURE){
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("todo", false));
                AW_FUN_Log(LOG_STEP, "OUT_OF_MEMORY");
                break;
            }
            else {
                AW_FUN_Log(LOG_STEP, "insert error");
                break;
            }
        }
    }
    TEST_V1_FREE(value);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.ulRecNum = 10;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    if (ret == VOS_ERRNO_DB_FAILURE) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("todo", false));
        ret = DB_SUCCESS_V1;
    }
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);// 内存写满后可能会不能查询，错误码会变为0x202a00FF
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 166;
    uint32_t pulRecNum = 0;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond1, &pulRecNum);
    if (ret == VOS_ERRNO_DB_FAILURE) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("todo", false));
        ret = DB_SUCCESS_V1;
    }
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);// 内存写满后可能会不能删除，错误码会变为0x202a00FF

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
void *INSERT(void *args)
{
    int ret = 0;
    inputS *usrData = (inputS *)args;
    char *value = (char *)TEST_V1_MALLOC(usrData->stBuff.usRecLen);
    (void)memset_s(value, usrData->stBuff.usRecLen, 0, usrData->stBuff.usRecLen);
    *(uint32_t *)value = usrData->F0;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = usrData->F1;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = usrData->F2;  // F2
    usrData->stBuff.StdBuf.pucData = (uint8_t *)value;
    for (int i = 0; i < 20; i++) {
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, usrData->ulDbId, usrData->usRelId, &usrData->stBuff);
        if (ret == VOS_ERRNO_DB_FAILURE) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("todo", false));
            ret = DB_SUCCESS_V1;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(value);
}
// 054.并发多个线程对自定义数据类型写入数据
TEST_F(customTypeTest, V1Com_005_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER_EQUAL] = CpmMinLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_054.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;

    inputS *threadData = (inputS *)TEST_V1_MALLOC(sizeof(inputS));
    threadData->ulDbId = ulDbId;
    threadData->usRelId = usRelId;
    threadData->stBuff = stBuff;
    threadData->F0 = 66;
    threadData->F1 = 77;
    threadData->F2 = 88;

    inputS *threadData1 = (inputS *)TEST_V1_MALLOC(sizeof(inputS));
    threadData1->ulDbId = ulDbId;
    threadData1->usRelId = usRelId;
    threadData1->stBuff = stBuff;
    threadData1->F0 = 166;
    threadData1->F1 = 177;
    threadData1->F2 = 188;

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    
    ret = pthread_create(&client_thr[0], NULL, INSERT, threadData);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&client_thr[1], NULL, INSERT, threadData1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    TEST_V1_FREE(threadData);
    TEST_V1_FREE(threadData1);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 055.设置手动开启事务
TEST_F(customTypeTest, V1Com_005_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    customCmpFunc[DB_OP_LARGER] = CpmLarger4TypeId40;
    customCmpFunc[DB_OP_LESS] = CpmLess4TypeId40;
    customCmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId40;
    customCmpFunc[DB_OP_MIN_LARGER_EQUAL] = CpmMinLargerEqual4TypeId40;
    uint32_t dataTypeId = 32;
    
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t ulDbId;                        // 命名空间id
    uint16_t usRelId;                       // 表id
    int fileds_num = 3;                     // 要创建的表预期字段数
    char dbName[10] = "noidx_eq";           // 对应命名空间
    char tabelName[] = "cus_tlb1";          // 表名

    DB_INST_CONFIG_STRU pstCfg = {0};
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg);  // 创建命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);  // 使用对应命名空间
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    DB_REL_DEF_STRU stRelDef;
    DB_FIELD_DEF_STRU *astFlds = NULL;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_054.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    astFlds = stRelDef.pstFldLst;

    // 设置手动开启事务
    uint32_t cdbId;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_BeginCDB(ulDbId, &cdbId));

    // 插入数据
    DB_DSBUF_STRU stBuff;
    stBuff.usRecLen = 0;
    for (int i = 0; i < fileds_num; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value = 66;                           // F0
    *(uint32_t *)(value + sizeof(uint32_t)) = 77;      // F1
    *(uint32_t *)(value + 2 * sizeof(uint32_t)) = 88;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(cdbId, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value);
    char *value1 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value1, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value1 = 166;                           // F0
    *(uint32_t *)(value1 + sizeof(uint32_t)) = 177;      // F1
    *(uint32_t *)(value1 + 2 * sizeof(uint32_t)) = 188;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value1;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(cdbId, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value1);
    char *value2 = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    (void)memset_s(value2, stBuff.usRecLen, 0, stBuff.usRecLen);
    *(uint32_t *)value2 = 266;                           // F0
    *(uint32_t *)(value2 + sizeof(uint32_t)) = 277;      // F1
    *(uint32_t *)(value2 + 2 * sizeof(uint32_t)) = 288;  // F2
    stBuff.StdBuf.pucData = (uint8_t *)value2;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(cdbId, ulDbId, usRelId, &stBuff));
    TEST_V1_FREE(value2);

    // 修改数据
    DB_COND_STRU pstCond = {0};
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(uint32_t *)(pstCond.aCond[0].aucValue) = 188;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_DSBUF_STRU stBuff1;
    stBuff1.usRecNum = 1;
    stBuff1.StdBuf.ulActLen = 12;
    stBuff1.StdBuf.ulBufLen = 2000;
    stBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff1.StdBuf.ulBufLen);
    (void)memset_s(stBuff1.StdBuf.pucData, stBuff1.StdBuf.ulBufLen, 0, stBuff1.StdBuf.ulBufLen);
    *(uint32_t *)(stBuff1.StdBuf.pucData) = 99;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 4) = 11;
    *(uint32_t *)(stBuff1.StdBuf.pucData + 8) = 22;
    uint32_t pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_UpdateRec(cdbId, ulDbId, usRelId, &pstCond, &stFldFilter, &stBuff1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 更新一条数据
    TEST_V1_FREE(stBuff1.StdBuf.pucData);

    // 查询数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU pstBuff;
    pstBuff.usRecLen = 10000;
    pstBuff.ulBufLen = 10000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);
    (void)memset_s(pstBuff.pBuf, pstBuff.usRecLen, 0x00, pstBuff.usRecLen);

    ret = TPC_SelectAllRecEx(cdbId, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *tmppBuf = (VOS_UINT8 *)pstBuff.pBuf;
    for (uint32_t i = 0; i < pstBuff.ulRecNum; ++i) {
        AW_FUN_Log(LOG_STEP, "| %8u | %8u | %8u |",
            *(uint32_t *)(void *)tmppBuf,
            *(uint32_t *)(void *)(tmppBuf + 4),
            *(uint32_t *)(void *)(tmppBuf + 8));
        tmppBuf += 12;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    pstBuff.pBuf = NULL;

    // 删除数据
    DB_COND_STRU pstCond1 = {0};
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 0;
    pstCond1.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    *(uint32_t *)(pstCond1.aCond[0].aucValue) = 99;
    pulRecNum = 0;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DeleteRec(cdbId, ulDbId, usRelId, &pstCond1, &pulRecNum));
    V1_AW_MACRO_EXPECT_EQ_INT(1, (int32_t)pulRecNum);  // 检查结果 删除一条数据

    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_RollbackCDB(cdbId));

    TestFreeTblStructDef(&stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB((VOS_UINT8 *)dbName, 1));
    AW_FUN_Log(LOG_STEP, "test end.");
}
