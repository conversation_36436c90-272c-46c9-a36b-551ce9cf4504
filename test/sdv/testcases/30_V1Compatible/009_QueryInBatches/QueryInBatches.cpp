/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: test
 * Description:
 * Create:
 */
#include "tools.h"

uint32_t g_testcountttree = 100;
uint32_t IndexkeyNum = 4;
uint32_t count = 0;

class QueryInBatches : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    }
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void QueryInBatches::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void QueryInBatches::TearDown()
{
    AW_CHECK_LOG_END();
}

class QueryInBatches_2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=128\"");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void QueryInBatches_2::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void QueryInBatches_2::TearDown()
{
    AW_CHECK_LOG_END();
}

// TPC_BeginIdxSelectByOrder参数ulCdbId大于0，ulDbId正常打开，usTblId存在、pstSort.id小于字段个数pstSort.num小于等于字段个数，pstCond.id小于字段个数，pstCond.num小于等于字段数，pstFldFilter为简单运算符，pstFldFilter.aucField小于字段个数，pstFldFilter.ucFieldNum小于等于字段个数
TEST_F(QueryInBatches, V1Com_009_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 re9,re8,re0...
        (void)sprintf_s(f0Buf, f0BufLen, "re%u", (g_testcountttree - 1 - i));
        AW_MACRO_EXPECT_EQ_STR((char *)dsBuf2.StdBuf.pucData, f0Buf);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder参数ulCdbId为NULL、负数
TEST_F(QueryInBatches, V1Com_009_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;

    // ulCdbId为uint32，内部会转化为uint32
    ret = TPC_BeginIdxSelectByOrder(NULL, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);

    ret = TPC_BeginIdxSelectByOrder(-1, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder参数ulDbId为NULL、未打开
TEST_F(QueryInBatches, V1Com_009_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, NULL, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, 2, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623526, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder参数usTblId不存在、为NULL
TEST_F(QueryInBatches, V1Com_009_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    DB_SELHANDLE selectHdl;

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, NULL, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623426, ret);

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623426, ret);

    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder参数pstSort为NULL，pstSort.id大于等于字段个数，pstSort.num大于字段数
TEST_F(QueryInBatches, V1Com_009_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort1 = {.ucSortNum = 7, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort1.ucSortNum] = {5};
    pstSort1.pSortFields = (T_FIELD *)sortFlds;

    DB_SORT_STRU pstSort2 = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds2[pstSort2.ucSortNum] = {6};
    pstSort2.pSortFields = (T_FIELD *)sortFlds2;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, NULL, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623425, ret);

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort1, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623939, ret);

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort2, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623939, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder参数pstCond为NULL、、pstCond.id大于等于字段个数，pstCond.num大于字段数
TEST_F(QueryInBatches, V1Com_009_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;

    // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 19;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    DB_COND_STRU stCond2;
    stCond2.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond2.usCondNum; i++) {
        stCond2.aCond[i].enOp = DB_OP_LARGEREQUAL;   // 条件类型
        stCond2.aCond[i].ucFieldId = 30;             // 条件字段id
        *(uint32_t *)stCond2.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, NULL, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623425, ret);

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623435, ret);

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond2, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623430, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder参数pstFldFilter为NULL或复杂运算符，DB_OP_MAX_LESS、DB_OP_MIN_LARGER、DB_OP_MAX_PREFIX12等
TEST_F(QueryInBatches, V1Com_009_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_OPTYPE_ENUM type_name[20] = {DB_OP_MAX_PREFIX12, DB_OP_MAX_PREFIX21, DB_OP_MIN_PREFIX12, DB_OP_MIN_PREFIX21,
        DB_OP_MAX_LESS, DB_OP_MAX_LESS_EQUAL, DB_OP_MIN_LARGER, DB_OP_MIN_LARGER, DB_OP_POSTFIX21, DB_OP_MAX_POSTFIX21};
    for (uint32_t j = 0; j < 10; j++) {
        DB_COND_STRU stCond;
        stCond.usCondNum = 1;  // 条件字段数量
        for (uint32_t i = 0; i < stCond.usCondNum; i++) {
            stCond.aCond[i].enOp = type_name[j];  // 条件类型
            stCond.aCond[i].ucFieldId = 9;        // 条件字段id
            memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
            *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
        }

        // 查询
        DB_SELHANDLE selectHdl;
        ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
        if (j == 8) {
            // 数组列出的9个类型一定不支持分批查询，其他类型支不支持也要看查询的字段是否支持改类型，此处DB_OP_POSTFIX21是支持string类型字段的，如果换做条件字段是其他类型也会查询失败
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(539623494, ret);
        }
    }
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder参数pstFldFilter为NULL、pstFldFilter.aucField大于等于字段个数，pstFldFilter.ucFieldNum大于字段个数
TEST_F(QueryInBatches, V1Com_009_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 31};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;

    DB_FIELDFILTER_STRU stFldFilter2 = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter2.aucField[0] = 30;                         // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, NULL, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623425, ret);

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623476, ret);

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter2, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623430, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder参数phSelect为NULL
TEST_F(QueryInBatches, V1Com_009_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;

    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(539623425, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数ulCdbId大于0，ulDbId正常打开，usTblId存在、pstCond.id小于字段个数，pstCond.num小于等于字段数，pstFldFilter为简单运算符，pstFldFilter.aucField小于字段个数，pstFldFilter.ucFieldNum小于等于字段个数
TEST_F(QueryInBatches, V1Com_009_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 rec0,rec1,rec2...
        (void)sprintf_s(f0Buf, f0BufLen, "re%u", i);
        AW_MACRO_EXPECT_EQ_STR((char *)dsBuf2.StdBuf.pucData, f0Buf);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数ulCdbId为NULL、负数
TEST_F(QueryInBatches, V1Com_009_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(NULL, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);

    ret = TPC_BeginSelect(-1, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数ulDbId为NULL、未打开
TEST_F(QueryInBatches, V1Com_009_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, NULL, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, 2, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623526, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数usTblId不存在、为NULL
TEST_F(QueryInBatches, V1Com_009_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, NULL, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623426, ret);
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623426, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数Cond为NULL、pstCond.id大于等于字段个数，pstCond.num大于字段数
TEST_F(QueryInBatches, V1Com_009_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 19;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    DB_COND_STRU stCond2;
    stCond2.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond2.usCondNum; i++) {
        stCond2.aCond[i].enOp = DB_OP_LARGEREQUAL;   // 条件类型
        stCond2.aCond[i].ucFieldId = 30;             // 条件字段id
        *(uint32_t *)stCond2.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, NULL, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623425, ret);

    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623435, ret);

    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond2, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623430, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数pstCond为复杂运算符，DB_OP_MAX_LESS、DB_OP_MIN_LARGER、DB_OP_MAX_PREFIX12等
TEST_F(QueryInBatches, V1Com_009_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_MAX_PREFIX12;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;              // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623494, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数pstFldFilter为NULL、pstFldFilter.aucField大于等于字段个数，pstFldFilter.ucFieldNum大于字段个数
TEST_F(QueryInBatches, V1Com_009_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 31};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                           // 查询显示结果的字段id

    DB_FIELDFILTER_STRU stFldFilter2 = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter2.aucField[0] = 30;                         // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, NULL, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623425, ret);
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623476, ret);

    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter2, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623430, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数phSelect为NULL
TEST_F(QueryInBatches, V1Com_009_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(539623425, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_FetchSelectRec参数ulCdbId为NULL
TEST_F(QueryInBatches, V1Com_009_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    dsBuf2.usRecNum = 1;  // 设置每批次查询1条

    ret = TPC_FetchSelectRec(NULL, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TEST_V1_FREE(recBuf2);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_FetchSelectRec参数ulCdbId为NULL
TEST_F(QueryInBatches, V1Com_009_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    DB_SELHANDLE selectHd2 = NULL;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    dsBuf2.usRecNum = 1;  // 设置每批次查询1条

    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, NULL, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623783, ret);

    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHd2, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623783, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TEST_V1_FREE(recBuf2);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_FetchSelectRec参数ulCdbId为NULL
TEST_F(QueryInBatches, V1Com_009_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    dsBuf2.usRecNum = 1;  // 设置每批次查询1条

    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(539623425, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TEST_V1_FREE(recBuf2);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_EndSelect参数ulDbId为不存在的ulCdbId、NULL
TEST_F(QueryInBatches, V1Com_009_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 re9,re8,re0...
        (void)sprintf_s(f0Buf, f0BufLen, "re%u", (g_testcountttree - 1 - i));
        AW_MACRO_EXPECT_EQ_STR((char *)dsBuf2.StdBuf.pucData, f0Buf);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(NULL, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("CDB 0 is not in use.", false));

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_EndSelect参数hSelect为不存在的hSelect、NULL
TEST_F(QueryInBatches, V1Com_009_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    DB_SELHANDLE selectHd2;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 re9,re8,re0...
        (void)sprintf_s(f0Buf, f0BufLen, "re%u", (g_testcountttree - 1 - i));
        AW_MACRO_EXPECT_EQ_STR((char *)dsBuf2.StdBuf.pucData, f0Buf);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(539623783, ret);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHd2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623783, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_ReleaseAllHandles参数ulCdbId为NULL
TEST_F(QueryInBatches, V1Com_009_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 re9,re8,re0...
        (void)sprintf_s(f0Buf, f0BufLen, "re%u", (g_testcountttree - 1 - i));
        AW_MACRO_EXPECT_EQ_STR((char *)dsBuf2.StdBuf.pucData, f0Buf);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_ReleaseAllHandles(NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_CloseAllHandles参数ulDbId为不存在的ulDbId、NULL
TEST_F(QueryInBatches, V1Com_009_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 re9,re8,re0...
        (void)sprintf_s(f0Buf, f0BufLen, "re%u", (g_testcountttree - 1 - i));
        AW_MACRO_EXPECT_EQ_STR((char *)dsBuf2.StdBuf.pucData, f0Buf);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(2);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id 2 does not exist.", false));

    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1，排序查询句柄查询数据，全匹配(pstBuff->usRecNum 大于数据量）
TEST_F(QueryInBatches, V1Com_009_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    dsBuf2.usRecNum = 100;  // 设置每批次查询100条
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预期 99
    V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1, *(uint32_t *)dsBuf2.StdBuf.pucData);
    V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1 Fld2，排序查询句柄查询数据（pstBuff->usRecNum
// 大于数据量）,F1不同，优先按F1排序
TEST_F(QueryInBatches, V1Com_009_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 2, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    dsBuf2.usRecNum = 100;  // 设置每批次查询100条
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预期F1:99,98,97 F2: 0,1,2
    V1_AW_MACRO_EXPECT_EQ_INT(0, *(uint32_t *)dsBuf2.StdBuf.pucData);
    V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1 Fld2，排序查询句柄查询数据（pstBuff->usRecNum
// 大于数据量）,F1相同，优先按F2排序
TEST_F(QueryInBatches, V1Com_009_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, 0, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 2, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    dsBuf2.usRecNum = 100;  // 设置每批次查询100条
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预期F1:0,0,0, F2: 99,98,97
    V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1, *(uint32_t *)dsBuf2.StdBuf.pucData);
    V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1 Fld2 Fld3，排序查询句柄查询数据（pstBuff->usRecNum
// 大于数据量）F1不同，优先按F1排序
TEST_F(QueryInBatches, V1Com_009_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    dsBuf2.usRecNum = 100;  // 设置每批次查询100条
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预期F1:99,98,97 F2: 0,1,2
    V1_AW_MACRO_EXPECT_EQ_INT(0, *(uint32_t *)dsBuf2.StdBuf.pucData);
    V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1 Fld2 Fld3，条件字段Fld2 Fld3，排序查询句柄查询数据（pstBuff->usRecNum
// 大于数据量）F1相同，优先按F2排序
TEST_F(QueryInBatches, V1Com_009_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, 0, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    dsBuf2.usRecNum = 100;  // 设置每批次查询100条
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预期F1:0,0,0, F2: 99,98,97
    V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1, *(uint32_t *)dsBuf2.StdBuf.pucData);
    V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1，排序句柄分批查询数据（pstBuff->usRecNum 小于数据量）
TEST_F(QueryInBatches, V1Com_009_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 99,89,78
        V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1 - 10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1 Fld2，排序句柄分批查询数据（pstBuff->usRecNum 小于数据量）
TEST_F(QueryInBatches, V1Com_009_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 2, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:99,89.. F2: 0,10..
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1 Fld2 Fld3，排序句柄分批查询数据（pstBuff->usRecNum 小于数据量）
TEST_F(QueryInBatches, V1Com_009_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:99,98,97 F2: 0,1,2
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，条件字段Fld1 Fld2 Fld3全匹配，排序查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    dsBuf2.usRecNum = 100;  // 设置每批次查询100条
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预期F1:99,98,97 F2: 0,1,2
    V1_AW_MACRO_EXPECT_EQ_INT(0, *(uint32_t *)dsBuf2.StdBuf.pucData);
    V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，条件字段Fld1 Fld2 Fld3全匹配，排序查询句柄分批查询数据
TEST_F(QueryInBatches, V1Com_009_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:99,98,97 F2: 0,1,2
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，条件字段Fld2 Fld3全匹配，非排序查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 1; i++) {
        dsBuf2.usRecNum = 100;  // 设置每批次查询100条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 0,1...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，条件字段Fld2 Fld3全匹配，非排序查询句柄分批查询数据
TEST_F(QueryInBatches, V1Com_009_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 索引字段float类型查询--非排序查询匹配到索引，是默认按升序查询的
TEST_F(QueryInBatches, V1Com_009_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, g_testcountttree - 1 - i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (uint32_t i = 0; i < g_testcountttree; i++) {
        VOS_UINT32 ulRecNum = 0;
        ret = TestTTreeSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, 1, i, &ulRecNum, DB_OP_EQUAL);

        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, ulRecNum);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 3;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 3;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(float *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 非索引字段double类型查询--非排序查询匹配到非索引，是默认按写入顺序查询的
TEST_F(QueryInBatches, V1Com_009_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, g_testcountttree - 1 - i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 6;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 6;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 99,89...
        V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1 - 10 * i, *(double *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 索引字段string类型查询
TEST_F(QueryInBatches, V1Com_009_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 rec0,rec1,rec2...
        (void)sprintf_s(f0Buf, f0BufLen, "re%u", i);
        AW_MACRO_EXPECT_EQ_STR((char *)dsBuf2.StdBuf.pucData, f0Buf);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 索引字段float类型排序查询
TEST_F(QueryInBatches, V1Com_009_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {3};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 3;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 3;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 99,89,79...
        V1_AW_MACRO_EXPECT_EQ_INT(*(float *)dsBuf2.StdBuf.pucData, g_testcountttree - 1 - 10 * i);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 索引字段double类型排序查询
TEST_F(QueryInBatches, V1Com_009_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {4};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 4;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 4;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 99,89,79...
        V1_AW_MACRO_EXPECT_EQ_INT(*(double *)dsBuf2.StdBuf.pucData, g_testcountttree - 1 - 10 * i);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 索引字段string类型排序查询
TEST_F(QueryInBatches, V1Com_009_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {5};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 5;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 5;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // 预期 re9,re8,re0...
        (void)sprintf_s(f0Buf, f0BufLen, "re%u", (g_testcountttree - 1 - i));
        AW_MACRO_EXPECT_EQ_STR((char *)dsBuf2.StdBuf.pucData, f0Buf);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 升序查询
TEST_F(QueryInBatches, V1Com_009_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {4};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 4;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 4;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 0,10,20...
        V1_AW_MACRO_EXPECT_EQ_INT(*(double *)dsBuf2.StdBuf.pucData, 10 * i);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序数为0，条件字段Fld1 Fld2 Fld3全匹配，排序查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:0,10,20 F2: 99,89,79
        V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1 - 10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序数为0，条件字段 Fld2 Fld3全匹配，排序查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 2;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i + 1;       // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:0,10,20 F2: 99,89,79
        V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1 - 10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1 Fld2 Fld3，查询条件混合，如    DB_OP_EQUAL = 0,
    DB_OP_NOTEQUAL,
    DB_OP_LESS,
    DB_OP_LESSEQUAL,
    DB_OP_LARGER,
    DB_OP_LARGEREQUAL,
    DB_OP_HAVEPREFIX,
    DB_OP_NOPREFIX,
    DB_OP_LIKE,
    DB_OP_NOTLIKE,
    DB_OP_PREFIX,
    DB_OP_PREFIX12,
    DB_OP_PREFIX21,混合
    */
TEST_F(QueryInBatches, V1Com_009_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id

    DB_OPTYPE_ENUM type_name[20] = {
        DB_OP_NOTEQUAL,
        DB_OP_LARGER,
        DB_OP_LARGEREQUAL,
    };

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = type_name[i];  // 条件类型
        stCond.aCond[i].ucFieldId = 0;        // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 99,89...1
        V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1 - 10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        if (i == 9) {
            V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 9);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
        }
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*TTREE索引字段为"Fld1 Fld2 Fld3"，条件字段Fld1 Fld2 Fld3全匹配，查询条件混合，如    DB_OP_EQUAL = 0,
    DB_OP_NOTEQUAL,
    DB_OP_LESS,
    DB_OP_LESSEQUAL,
    DB_OP_LARGER,
    DB_OP_LARGEREQUAL,
    DB_OP_HAVEPREFIX,
    DB_OP_NOPREFIX,
    DB_OP_LIKE,
    DB_OP_NOTLIKE,
    DB_OP_PREFIX,
    DB_OP_PREFIX12,
    DB_OP_PREFIX21,混合
    */
TEST_F(QueryInBatches, V1Com_009_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_OPTYPE_ENUM type_name[20] = {
        DB_OP_NOTEQUAL,
        DB_OP_LARGER,
        DB_OP_LARGEREQUAL,
    };

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = type_name[i];  // 条件类型
        stCond.aCond[i].ucFieldId = 0;        // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 99,89...1
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i + 1, *(uint32_t *)dsBuf2.StdBuf.pucData);
        if (i == 9) {
            V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 9);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
        }
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 条件为18个，每个条件一个字段，查询数据
TEST_F(QueryInBatches, V1Com_009_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 18;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 条件为18个，所有条件都为同一个字段，查询数据
TEST_F(QueryInBatches, V1Com_009_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 18;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 0;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 释放查询句柄、释放CDB的所有句柄
TEST_F(QueryInBatches, V1Com_009_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 18;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 0;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 释放所有DB的句柄
TEST_F(QueryInBatches, V1Com_009_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 18;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 0;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(cdbId, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(cdbId, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect参数pstCond.num为NULL或0，全表查询
TEST_F(QueryInBatches, V1Com_009_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = NULL;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 0;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 插入数据0/1/2，更新0-3数据后，排序句柄查询数据
TEST_F(QueryInBatches, V1Com_009_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 3;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen);
    if (recBuf3 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(recBuf3, recLen, 0x00, recLen);
    uint32_t *v = (uint32_t *)(void *)recBuf3;
    v[0] = 3;

    DB_FIELDFILTER_STRU stFldFilter3 = {.ucFieldNum = DB_FIELD_ALL};

    DB_COND_STRU stCond3;
    stCond3.usCondNum = 1;
    stCond3.aCond[0].enOp = DB_OP_EQUAL;
    stCond3.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond3.aCond[0].aucValue = 0;

    DB_DSBUF_STRU dsBuf = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf3}};
    uint32_t updateNum = 0;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond3, &stFldFilter3, &dsBuf, &updateNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf3);

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 1/2/3
        V1_AW_MACRO_EXPECT_EQ_INT(i + 1, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 插入数据0/1/2，更新0-3数据后，非排序句柄查询数据
TEST_F(QueryInBatches, V1Com_009_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 3;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen);
    if (recBuf3 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(recBuf3, recLen, 0x00, recLen);
    uint32_t *v = (uint32_t *)(void *)recBuf3;
    v[0] = 3;

    DB_FIELDFILTER_STRU stFldFilter3 = {.ucFieldNum = DB_FIELD_ALL};

    DB_COND_STRU stCond3;
    stCond3.usCondNum = 1;
    stCond3.aCond[0].enOp = DB_OP_EQUAL;
    stCond3.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond3.aCond[0].aucValue = 0;

    DB_DSBUF_STRU dsBuf = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf3}};
    uint32_t updateNum = 0;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond3, &stFldFilter3, &dsBuf, &updateNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf3);

    //
    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 6;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期3/1/2
        if (i == 0) {
            V1_AW_MACRO_EXPECT_EQ_INT(3, *(uint32_t *)dsBuf2.StdBuf.pucData);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        }
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 插入数据1/2/3，删除2数据后，非排序句柄查询数据
TEST_F(QueryInBatches, V1Com_009_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 3;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen);
    if (recBuf3 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(recBuf3, recLen, 0x00, recLen);
    uint32_t *v = (uint32_t *)(void *)recBuf3;
    v[0] = 3;

    DB_COND_STRU stCond3;
    stCond3.usCondNum = 1;
    stCond3.aCond[0].enOp = DB_OP_EQUAL;
    stCond3.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond3.aCond[0].aucValue = 0;

    uint32_t updateNum = 0;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond3, &updateNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf3);

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 6;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);

        // 预期3/1/2
        if (i == 2) {
            V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(i + 1, *(uint32_t *)dsBuf2.StdBuf.pucData);
        }
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// RDBhandle+其他RDB、CDB提交改写数据，插入数据1/2/3，查询到1，插入0，继续查询，没匹配索引
TEST_F(QueryInBatches, V1Com_009_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 4;
    // 写入数据
    for (uint32_t i = 1; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 6;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 1; i < 2; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 1...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 继续 写入数据0,查询结果2/3、0
    DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, 0, recLen, 0);
    DB_DSBUF_STRU dsBuf = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 2; i < g_testcountttree + 1; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 2,3,0...
        if (i == 4) {
            V1_AW_MACRO_EXPECT_EQ_INT(0, *(uint32_t *)dsBuf2.StdBuf.pucData);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        }
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// RDBhandle+其他RDB、CDB提交改写数据，插入数据1/2/3，查询到1，插入0，继续查询，匹配索引
TEST_F(QueryInBatches, V1Com_009_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 4;
    // 写入数据
    for (uint32_t i = 1; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 4;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 1; i < 2; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 1,2,3...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 继续 写入数据0,查询结果2/3
    DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, 0, recLen, 0);
    DB_DSBUF_STRU dsBuf = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 2; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 2,3...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// RDBhandle+其他RDB、CDB提交改写数据，插入数据1/2/3，查询到1，更新2为0，继续查询，没匹配索引
TEST_F(QueryInBatches, V1Com_009_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 4;
    // 写入数据
    for (uint32_t i = 1; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 6;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 1; i < 2; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 1...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen);
    if (recBuf3 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(recBuf3, recLen, 0x00, recLen);
    uint32_t *v = (uint32_t *)(void *)recBuf3;
    v[0] = 0;

    DB_FIELDFILTER_STRU stFldFilter3 = {.ucFieldNum = DB_FIELD_ALL};

    DB_COND_STRU stCond3;
    stCond3.usCondNum = 1;
    stCond3.aCond[0].enOp = DB_OP_EQUAL;
    stCond3.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond3.aCond[0].aucValue = 2;

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新2为0，继续查询,查询结果0/3
    DB_DSBUF_STRU dsBuf3 = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf3}};
    uint32_t updateNum = 0;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond3, &stFldFilter3, &dsBuf3, &updateNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf3);
    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 2; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 0,3,
        if (i == 2) {
            V1_AW_MACRO_EXPECT_EQ_INT(0, *(uint32_t *)dsBuf2.StdBuf.pucData);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        }
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// RDBhandle+其他RDB、CDB提交改写数据，插入数据1/2/3，查询到1，更新2为0，继续查询，匹配索引
TEST_F(QueryInBatches, V1Com_009_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 4;
    // 写入数据
    for (uint32_t i = 1; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 4;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 1; i < 2; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 1...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen);
    if (recBuf3 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(recBuf3, recLen, 0x00, recLen);
    uint32_t *v = (uint32_t *)(void *)recBuf3;
    v[0] = 0;

    DB_FIELDFILTER_STRU stFldFilter3 = {.ucFieldNum = DB_FIELD_ALL};

    DB_COND_STRU stCond3;
    stCond3.usCondNum = 1;
    stCond3.aCond[0].enOp = DB_OP_EQUAL;
    stCond3.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond3.aCond[0].aucValue = 2;

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新2为0，继续查询,查询结果3
    DB_DSBUF_STRU dsBuf3 = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf3}};
    uint32_t updateNum = 0;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond3, &stFldFilter3, &dsBuf3, &updateNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf3);
    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 2; i < g_testcountttree - 1; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 3,
        V1_AW_MACRO_EXPECT_EQ_INT(i + 1, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// RDBhandle+其他CDB改写数据未提交，插入数据1/2/3，查询到1，插入0，继续查询，没匹配到索引
TEST_F(QueryInBatches, V1Com_009_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 4;
    // 写入数据
    for (uint32_t i = 1; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 6;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 1; i < 2; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 1...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 继续 写入数据0,查询结果2/3、0
    DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, 0, recLen, 0);
    DB_DSBUF_STRU dsBuf = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
    ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 2; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 2,3,...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表前要关闭CDB
    ret = TPC_CommitCDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// CDBhandle+其他RDB/CDB改数据，插入数据1/2/3，查询到1，插入0，继续查询，匹配到索引
TEST_F(QueryInBatches, V1Com_009_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    g_testcountttree = 4;
    // 写入数据
    for (uint32_t i = 1; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 4;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(cdbId, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 1; i < 2; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 1,2,3...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 继续 写入数据0,查询结果1、2/3
    DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, 0, recLen, 0);
    DB_DSBUF_STRU dsBuf = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 2; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 2,3...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(cdbId, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表前要关闭CDB
    ret = TPC_RollbackCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    TEST_V1_FREE(recBuf2);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// CDBhandle+当前CDB改数据，插入数据1/2/3，查询到1，插入0，继续查询，没匹配到索引
TEST_F(QueryInBatches, V1Com_009_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    g_testcountttree = 4;
    // 写入数据
    for (uint32_t i = 1; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 6;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(cdbId, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 1; i < 2; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 1...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 继续 写入数据0,查询结果2/3、0
    DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, 0, recLen, 0);
    DB_DSBUF_STRU dsBuf = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
    ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 2; i < g_testcountttree + 1; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 2,3,0...
        if (i == 4) {
            V1_AW_MACRO_EXPECT_EQ_INT(0, *(uint32_t *)dsBuf2.StdBuf.pucData);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        }
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(cdbId, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// CDBhandle+当前CDB改数据，插入数据1/2/3，查询到1，插入0，继续查询，匹配到索引
TEST_F(QueryInBatches, V1Com_009_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    g_testcountttree = 4;
    // 写入数据
    for (uint32_t i = 1; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 4;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(cdbId, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 1; i < 2; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 1,2,3...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 继续 写入数据0,查询结果1、2/3
    DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, 0, recLen, 0);
    DB_DSBUF_STRU dsBuf = {
        .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
    ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (uint32_t i = 2; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询1条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 2,3...
        V1_AW_MACRO_EXPECT_EQ_INT(i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(cdbId, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder句柄数为65534和65535
TEST_F(QueryInBatches, V1Com_009_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    DB_SELHANDLE selectHdl[65535];
    // 查询
    for (uint64_t j = 0; j < 65535; j++) {
        ret =
            TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl[j]);
        if (j == 65534) {
            V1_AW_MACRO_EXPECT_EQ_INT(539623782, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        uint16_t recLen2 = 30000;
        uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = recLen2,
            .usRecNum = 100,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

        const uint32_t f0BufLen = 16;
        char f0Buf[f0BufLen];

        for (uint32_t i = 0; i < 1; i++) {
            dsBuf2.usRecNum = 100;  // 设置每批次查询100条
            (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
            ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl[j], &dsBuf2);
            if (j == 65534) {
                V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDHANDLE, ret);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Unabel to check handle id.", false));
            } else {
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
                // 预期F1:99,98,97 F2: 0,1,2
                V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
                V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
            }
        }
        TEST_V1_FREE(recBuf2);
    }
    for (uint64_t j = 0; j < 65535; j++) {
        ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl[j]);
        if (j == 65534) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDHANDLE, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(
                DB_SUCCESS_V1, TestTPC_SysviewGetLastError("handle cdbId not match or handleInfo is null", false));
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginSelect句柄数为65534和65535
TEST_F(QueryInBatches, V1Com_009_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    DB_SELHANDLE selectHdl[65535];
    // 查询
    for (uint64_t j = 0; j < 65535; j++) {
        ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl[j]);
        if (j == 65534) {
            V1_AW_MACRO_EXPECT_EQ_INT(539623782, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        uint16_t recLen2 = 30000;
        uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = recLen2,
            .usRecNum = 10,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

        const uint32_t f0BufLen = 16;
        char f0Buf[f0BufLen];
        for (uint32_t i = 0; i < 1; i++) {
            dsBuf2.usRecNum = 100;  // 设置每批次查询100条
            (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
            ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl[j], &dsBuf2);
            if (j == 65534) {
                V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDHANDLE, ret);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Unabel to check handle id.", false));
            } else {
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
                // 预期 0,1...
                V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
                V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
            }
        }
        TEST_V1_FREE(recBuf2);
    }
    for (uint64_t j = 0; j < 65535; j++) {
        ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl[j]);
        if (j == 65534) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDHANDLE, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(
                DB_SUCCESS_V1, TestTPC_SysviewGetLastError("handle cdbId not match or handleInfo is null", false));
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// CDB在提交时有handle未释放
TEST_F(QueryInBatches, V1Com_009_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 18;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 0;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(cdbId, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(cdbId, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(539623826, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder/TPC_BeginSelect pstCond的usCondNum大于18
TEST_F(QueryInBatches, V1Com_009_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 19;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623435, ret);

    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623435, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重复调用TPC_EndSelect
TEST_F(QueryInBatches, V1Com_009_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 1; i++) {
        dsBuf2.usRecNum = 100;  // 设置每批次查询100条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 0,1...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDHANDLE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(
        DB_SUCCESS_V1, TestTPC_SysviewGetLastError("handle cdbId not match or handleInfo is null", false));

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder排序字段不是索引字段的一部分
TEST_F(QueryInBatches, V1Com_009_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {6};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623939, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_BeginIdxSelectByOrder排序字段都应是索引字段的一部分，但顺序不相同
TEST_F(QueryInBatches, V1Com_009_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {2, 1, 0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623939, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 重复释放句柄CloseAllHandles/TPC_ReleaseAllHandles
TEST_F(QueryInBatches, V1Com_009_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    VOS_UINT32 cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;

    DB_COND_STRU stCond;
    stCond.usCondNum = 18;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = 0;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(cdbId, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 F1: 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(cdbId, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);

    ret = TPC_EndSelect(cdbId, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_ReleaseAllHandles(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 释放后句柄再次fetch数据
TEST_F(QueryInBatches, V1Com_009_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 1; i++) {
        dsBuf2.usRecNum = 100;  // 设置每批次查询100条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 0,1...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次fetch
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623783, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_FetchSelectRec 查询数据量小于实际数据量，pstBuff长度不足
TEST_F(QueryInBatches, V1Com_009_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 50, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 2; i++) {
        dsBuf2.usRecNum = 50;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(539623511, ret);
    }
    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld2 Fld3，排序查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 2, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623939, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld2 Fld1，排序查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 2, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {1, 0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623939, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序Fld1 Fld3，排序查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 2, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(539623939, ret);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TTREE索引字段为"Fld1 Fld2 Fld3"，字段排序数为0，条件字段Fld1 Fld2 Fld3不全匹配，排序查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        if (i == 0) {
            stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        } else {
            stCond.aCond[i].enOp = DB_OP_LESSEQUAL;
        }                               // 条件类型
        stCond.aCond[i].ucFieldId = i;  // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 条件字段Fld1 Fld2 Fld3不全匹配，查询句柄查询数据
TEST_F(QueryInBatches, V1Com_009_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        if (i == 0) {
            stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        } else {
            stCond.aCond[i].enOp = DB_OP_LESSEQUAL;
        }
        // 条件类型
        stCond.aCond[i].ucFieldId = i;  // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 1; i++) {
        dsBuf2.usRecNum = 100;  // 设置每批次查询100条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_FetchSelectRec
// 插入1/2/3，第一次pstBuff长度够，查询1，第二次buff长度不足，查询报错，第三次pstBuff长度够，查询2，3
TEST_F(QueryInBatches, V1Com_009_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    g_testcountttree = 10;
    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    uint16_t recLen3 = 30;
    uint8_t *recBuf3 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf3 = {
        .usRecLen = recLen3, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen3, .ulActLen = recLen3, .pucData = recBuf3}};
    (void)memset_s(recBuf3, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    // 第一次pstBuff长度够，查询1
    for (uint32_t i = 0; i < 1; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 99
        V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1 - i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }

    // 第二次buff长度不足，查询报错
    dsBuf2.usRecNum = 10;  // 设置每批次查询10条
    (void)memset_s(recBuf3, recLen3, 0x00, recLen3);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf3);
    V1_AW_MACRO_EXPECT_EQ_INT(539623511, ret);

    // 第三次pstBuff长度够，查询2，3
    for (uint32_t i = 0; i < g_testcountttree - 1; i++) {
        dsBuf2.usRecNum = 1;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 9,8,7
        V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 2 - i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 1);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    TEST_V1_FREE(recBuf3);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 过滤字段为0，记录数为20，查询10条
TEST_F(QueryInBatches, V1Com_009_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;  // 条件字段数量为0，仍然是索引扫描
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 99,89,78
        V1_AW_MACRO_EXPECT_EQ_INT(
            (g_testcountttree - 1) - 10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);  // DTS2024110614430适配
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 过滤字段为0，记录数为100，查询200条
TEST_F(QueryInBatches, V1Com_009_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;  // 条件字段数量为0，相当于全表扫
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 200, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 1; i++) {
        dsBuf2.usRecNum = 200;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 99,89,78
        V1_AW_MACRO_EXPECT_EQ_INT(g_testcountttree - 1 - 10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_FetchSelectRec 查询数据量大于实际数据量，pstBuff长度不足
TEST_F(QueryInBatches, V1Com_009_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {
        .ucFieldNum =
            DB_FIELD_ALL};  // 查询显示结果的字段数量为所有，dsBuf2.StdBuf.pucData打印的结果都是第一个字段信息，除非这里指定stFldFilter.aucField[0]
                            // 或者dsBuf2.StdBuf.pucData偏移输出
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 1; i++) {
        dsBuf2.usRecNum = 1000;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(539623511, ret);
    }

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建多个查询handle，每个都去查询数据
TEST_F(QueryInBatches, V1Com_009_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    int handle_num = 10;
    DB_SELHANDLE selectHdl[handle_num];
    // 查询
    for (uint64_t j = 0; j < handle_num; j++) {
        ret =
            TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl[j]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        uint16_t recLen2 = 30000;
        uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
        DB_DSBUF_STRU dsBuf2 = {.usRecLen = recLen2,
            .usRecNum = 100,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

        const uint32_t f0BufLen = 16;
        char f0Buf[f0BufLen];

        for (uint32_t i = 0; i < 1; i++) {
            dsBuf2.usRecNum = 100;  // 设置每批次查询100条
            (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
            ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl[j], &dsBuf2);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            // 预期F1:99,98,97 F2: 0,1,2
            V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
            V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
        }
        TEST_V1_FREE(recBuf2);
    }
    for (uint64_t j = 0; j < handle_num; j++) {
        ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl[j]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *V1Com_009_084_01(void *args)
{
    int ret = 0;
    uint32_t ulDbId;
    uint16_t usRelId = 0;
    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:99,98,97 F2: 0,1,2
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
}

void *V1Com_009_084_02(void *args)
{
    int ret = 0;
    uint32_t ulDbId;
    uint16_t usRelId = 0;
    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id
    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 10, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];
    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期 0,10...
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }

    // 全部查完，后面没有记录
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
    V1_AW_MACRO_EXPECT_EQ_INT(539623510, ret);
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
}

// 并发fetch数据
TEST_F(QueryInBatches, V1Com_009_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = 0;
    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};
    int end_num = 1;
    for (int i = 0; i < end_num; i++) {
        ret = pthread_create(&client_thr_01, NULL, V1Com_009_084_01, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = pthread_create(&client_thr_02, NULL, V1Com_009_084_02, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }

    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *V1Com_009_085_01(void *args)
{
    count++;
}

void *V1Com_009_085_02(void *args)
{
    if (count == 0) {
        sleep(1);
    }
}

// DROP_WAIT：dropDB时如果有DB为open状态会sleep等待closeDB直至close
// DROP_NOWAIT: dropDB时如果有DB为open状态会报错，多线程控制
TEST_F(QueryInBatches, V1Com_009_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:99,98,97 F2: 0,1,2
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};
    int end_num = 1;
    for (int i = 0; i < end_num; i++) {
        ret = pthread_create(&client_thr_01, NULL, V1Com_009_085_01, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = pthread_create(&client_thr_02, NULL, V1Com_009_085_02, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    count = 0;
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *V1Com_009_086_01(void *args)
{
    count++;
}

void *V1Com_009_086_02(void *args)
{
    if (count == 0) {
        sleep(1);
    }
}

// closeDB的时候会校验CDB、HANDLE是否都提交了，多次openDB场景，每次close都会校验CDB，只有最后一次close会校验handle，多线程控制
TEST_F(QueryInBatches, V1Com_009_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, g_testcountttree - 1 - i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 1;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LESSEQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;           // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = g_testcountttree;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {
        .usRecLen = recLen2, .usRecNum = 100, .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < 10; i++) {
        dsBuf2.usRecNum = 10;  // 设置每批次查询10条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:99,98,97 F2: 0,1,2
        V1_AW_MACRO_EXPECT_EQ_INT(10 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 10);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);

    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};
    int end_num = 1;
    for (int i = 0; i < end_num; i++) {
        ret = pthread_create(&client_thr_01, NULL, V1Com_009_086_01, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        ret = pthread_create(&client_thr_02, NULL, V1Com_009_086_02, NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    count = 0;

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 大数据量10w，数据随机，排序句柄查询数据
TEST_F(QueryInBatches, V1Com_009_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < 1000 * g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {.usRecLen = recLen2,
        .usRecNum = 10000,
        .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 100;  // 设置每批次查询100条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:99,98,97 F2: 0,1,2
        V1_AW_MACRO_EXPECT_EQ_INT(100 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
    }

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 内存满fetch数据
TEST_F(QueryInBatches_2, V1Com_009_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);

    // 设置表结构
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    int ret = TestTPC_CreateTbl(ulDbId, "schema_file/QueryInBatches.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 写入数据
    for (uint32_t i = 0; i < 10000 * g_testcountttree; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen, i);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        if (ret != 0) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);  // 资源不足
            break;
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置排序字段
    DB_SORT_STRU pstSort = {.ucSortNum = 3, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0, 1, 2};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 设置查询条件
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 1};  // 查询显示结果的字段数量
    stFldFilter.aucField[0] = 0;                          // 查询显示结果的字段id

    DB_COND_STRU stCond;
    stCond.usCondNum = 3;  // 条件字段数量
    for (uint32_t i = 0; i < stCond.usCondNum; i++) {
        stCond.aCond[i].enOp = DB_OP_LARGEREQUAL;  // 条件类型
        stCond.aCond[i].ucFieldId = i;             // 条件字段id
        memset_s(stCond.aCond[i].aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
        *(uint32_t *)stCond.aCond[i].aucValue = 0;  // 条件字段值
    }

    // 查询
    DB_SELHANDLE selectHdl;
    ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen2 = 30000;
    uint8_t *recBuf2 = (VOS_UINT8 *)TEST_V1_MALLOC(recLen2);
    DB_DSBUF_STRU dsBuf2 = {.usRecLen = recLen2,
        .usRecNum = 10000,
        .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    const uint32_t f0BufLen = 16;
    char f0Buf[f0BufLen];

    for (uint32_t i = 0; i < g_testcountttree; i++) {
        dsBuf2.usRecNum = 100;  // 设置每批次查询100条
        (void)memset_s(recBuf2, recLen2, 0x00, recLen2);
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selectHdl, &dsBuf2);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 预期F1:99,98,97 F2: 0,1,2
        V1_AW_MACRO_EXPECT_EQ_INT(100 * i, *(uint32_t *)dsBuf2.StdBuf.pucData);
        V1_AW_MACRO_EXPECT_EQ_INT(dsBuf2.usRecNum, 100);
    }
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selectHdl);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf2);
    ret = TPC_CloseAllHandles(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
