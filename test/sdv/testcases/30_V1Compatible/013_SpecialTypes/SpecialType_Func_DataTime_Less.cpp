/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 支持V1兼容特殊类型
 Notes        : 功能测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2024/09/02
**************************************************************************** */
#include "SpecialTypeTest.h"

class SpecialType_Func_DataTime_Less : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        DB_ERR_CODE ret = TestTPC_Init();
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    };
    static void TearDownTestCase()
    {
        TestTPC_UnInit();
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SpecialType_Func_DataTime_Less::SetUp()
{
    DB_ERR_CODE ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_queryTestDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_queryTestDbName, &g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 用于测试用例在设备失败的时候可以设置环境变量来打印一些具体的信息
    g_testcaseDebugModeVal = getenv("TESTCASE_DEBUG_MODE");
    if (g_testcaseDebugModeVal == NULL) {
        AW_FUN_Log(LOG_INFO, "if you want to printf testcase info, you can 'export TESTCASE_DEBUG_MODE=1'.");
    }
}
void SpecialType_Func_DataTime_Less::TearDown()
{
    DB_ERR_CODE ret;
    TestAlarmMemLeak();
    ret = TPC_CloseDB(g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_DropDB((VOS_UINT8 *)g_queryTestDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_CHECK_LOG_END();
}

// DBT_DATETIME，DB_OP_LESS，记录和条件对比，usYear小于、其它值为随机值
TEST_F(SpecialType_Func_DataTime_Less, V1Com_013_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i == opVal) {
            continue;
        }
        testSetAllField(&pstDsBuf, i, i);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucDay = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucHour = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucMinute = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = {
        .usYear = UINT32_TO_UINT16(updateVal - 1),
        .ucMonth = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucDay = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucHour = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucMinute = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + 1 + i, opVal + 1 + i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_LESS，记录和条件对比，usYear等于、ucMonth小于、其它值为随机值
TEST_F(SpecialType_Func_DataTime_Less, V1Com_013_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i == opVal) {
            continue;
        }
        DB_DATETIME_STRU pstDateTimeWrite = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(i),
            .ucDay = UINT32_TO_UINT8(i),
            .ucHour = UINT32_TO_UINT8(i),
            .ucMinute = UINT32_TO_UINT8(i),
            .ucSecond = UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, &pstDateTimeWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucHour = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucMinute = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(i),
            .ucDay = UINT32_TO_UINT8(i),
            .ucHour = UINT32_TO_UINT8(i),
            .ucMinute = UINT32_TO_UINT8(i),
            .ucSecond = UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 70;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(updateVal - 1),
        .ucDay = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucHour = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucMinute = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal + 1 + i),
            .ucDay = UINT32_TO_UINT8(opVal + 1 + i),
            .ucHour = UINT32_TO_UINT8(opVal + 1 + i),
            .ucMinute = UINT32_TO_UINT8(opVal + 1 + i),
            .ucSecond = UINT32_TO_UINT8(opVal + 1 + i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + 1 + i, opVal + 1 + i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_LESS，记录和条件对比，usYear等于、ucMonth等于、ucDay为小于、其它值为随机值
TEST_F(SpecialType_Func_DataTime_Less, V1Com_013_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i == opVal) {
            continue;
        }
        DB_DATETIME_STRU pstDateTimeWrite = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(i),
            .ucHour = UINT32_TO_UINT8(i),
            .ucMinute = UINT32_TO_UINT8(i),
            .ucSecond = UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, &pstDateTimeWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal),
        .ucHour = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucMinute = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(i),
            .ucHour = UINT32_TO_UINT8(i),
            .ucMinute = UINT32_TO_UINT8(i),
            .ucSecond = UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 70;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(updateVal - 1),
        .ucHour = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucMinute = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal + 1 + i),
            .ucHour = UINT32_TO_UINT8(opVal + 1 + i),
            .ucMinute = UINT32_TO_UINT8(opVal + 1 + i),
            .ucSecond = UINT32_TO_UINT8(opVal + 1 + i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + 1 + i, opVal + 1 + i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_LESS，记录和条件对比，usYear等于、ucMonth等于、ucDay为等于、ucHour小于、其它值为随机值
TEST_F(SpecialType_Func_DataTime_Less, V1Com_013_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i == opVal) {
            continue;
        }
        DB_DATETIME_STRU pstDateTimeWrite = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(i),
            .ucMinute = UINT32_TO_UINT8(i),
            .ucSecond = UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, &pstDateTimeWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal),
        .ucHour = UINT32_TO_UINT8(opVal),
        .ucMinute = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(i),
            .ucMinute = UINT32_TO_UINT8(i),
            .ucSecond = UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 70;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal),
        .ucHour = UINT32_TO_UINT8(updateVal - 1),
        .ucMinute = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(opVal + 1 + i),
            .ucMinute = UINT32_TO_UINT8(opVal + 1 + i),
            .ucSecond = UINT32_TO_UINT8(opVal + 1 + i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + 1 + i, opVal + 1 + i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_LESS，记录和条件对比，
// usYear等于、ucMonth等于、ucDay为等于、ucHour等于、ucMinute小于、其它值为随机值
TEST_F(SpecialType_Func_DataTime_Less, V1Com_013_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i == opVal) {
            continue;
        }
        DB_DATETIME_STRU pstDateTimeWrite = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(opVal),
            .ucMinute = UINT32_TO_UINT8(i),
            .ucSecond = UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, &pstDateTimeWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal),
        .ucHour = UINT32_TO_UINT8(opVal),
        .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, recordCnt)) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(opVal),
            .ucMinute = UINT32_TO_UINT8(i),
            .ucSecond = UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 70;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal),
        .ucHour = UINT32_TO_UINT8(opVal),
        .ucMinute = UINT32_TO_UINT8(updateVal - 1),
        .ucSecond = UINT32_TO_UINT8(testGetRandomRange(0, (updateVal * 2))) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(opVal),
            .ucMinute = UINT32_TO_UINT8(opVal + 1 + i),
            .ucSecond = UINT32_TO_UINT8(opVal + 1 + i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + 1 + i, opVal + 1 + i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_LESS，记录和条件对比，
// usYear等于、ucMonth等于、ucDay为等于、ucHour等于、ucMinute等于、ucSecond小于、其它值为随机值
TEST_F(SpecialType_Func_DataTime_Less, V1Com_013_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i == opVal) {
            continue;
        }
        DB_DATETIME_STRU pstDateTimeWrite = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(opVal),
            .ucMinute = UINT32_TO_UINT8(opVal),
            .ucSecond = UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, &pstDateTimeWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal),
        .ucHour = UINT32_TO_UINT8(opVal),
        .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(opVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(opVal),
            .ucMinute = UINT32_TO_UINT8(opVal),
            .ucSecond = UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 70;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = {
        .usYear = UINT32_TO_UINT16(opVal),
        .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal),
        .ucHour = UINT32_TO_UINT8(opVal),
        .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(updateVal - 1) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_LESS, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        DB_DATETIME_STRU pstDateTimeCheck = {
            .usYear = UINT32_TO_UINT16(opVal),
            .ucMonth = UINT32_TO_UINT8(opVal),
            .ucDay = UINT32_TO_UINT8(opVal),
            .ucHour = UINT32_TO_UINT8(opVal),
            .ucMinute = UINT32_TO_UINT8(opVal),
            .ucSecond = UINT32_TO_UINT8(opVal + 1 + i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + 1 + i, opVal + 1 + i, &pstDateTimeCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal - 1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
