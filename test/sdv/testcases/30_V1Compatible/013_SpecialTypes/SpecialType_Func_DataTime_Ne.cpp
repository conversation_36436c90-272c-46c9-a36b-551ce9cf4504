/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 支持V1兼容特殊类型
 Notes        : 功能测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2024/09/02
**************************************************************************** */
#include "SpecialTypeTest.h"

class SpecialType_Func_DataTime_Ne : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        DB_ERR_CODE ret = TestTPC_Init();
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    };
    static void TearDownTestCase()
    {
        TestTPC_UnInit();
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SpecialType_Func_DataTime_Ne::SetUp()
{
    DB_ERR_CODE ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_queryTestDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_queryTestDbName, &g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 用于测试用例在设备失败的时候可以设置环境变量来打印一些具体的信息
    g_testcaseDebugModeVal = getenv("TESTCASE_DEBUG_MODE");
    if (g_testcaseDebugModeVal == NULL) {
        AW_FUN_Log(LOG_INFO, "if you want to printf testcase info, you can 'export TESTCASE_DEBUG_MODE=1'.");
    }
}
void SpecialType_Func_DataTime_Ne::TearDown()
{
    DB_ERR_CODE ret;
    TestAlarmMemLeak();
    ret = TPC_CloseDB(g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_DropDB((VOS_UINT8 *)g_queryTestDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_CHECK_LOG_END();
}

// DBT_DATETIME，DB_OP_EQUAL，记录和条件对比，
// usYear、ucMonth、ucDay、ucHour、ucMinute、ucSecond均相等
TEST_F(SpecialType_Func_DataTime_Ne, V1Com_013_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&pstDsBuf, i, i);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = { .usYear = UINT32_TO_UINT16(opVal), .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal), .ucHour = UINT32_TO_UINT8(opVal), .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(opVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_EQUAL, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_EQUAL, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal, opVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = { .usYear = UINT32_TO_UINT16(updateVal),
        .ucMonth = UINT32_TO_UINT8(updateVal), .ucDay = UINT32_TO_UINT8(updateVal),
        .ucHour = UINT32_TO_UINT8(updateVal), .ucMinute = UINT32_TO_UINT8(updateVal),
        .ucSecond = UINT32_TO_UINT8(updateVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_EQUAL, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_EQUAL, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, delSuccRecNum);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after delete");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_NOTEQUAL，记录和条件对比，
// usYear不相等，ucMonth相等、ucDay相等、ucHour相等，ucMinute相等、ucSecond相等
TEST_F(SpecialType_Func_DataTime_Ne, V1Com_013_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&pstDsBuf, i, i);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = { .usYear = UINT32_TO_UINT16(opVal + 1), .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal), .ucHour = UINT32_TO_UINT8(opVal), .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(opVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = { .usYear = UINT32_TO_UINT16(updateVal + 1),
        .ucMonth = UINT32_TO_UINT8(updateVal), .ucDay = UINT32_TO_UINT8(updateVal),
        .ucHour = UINT32_TO_UINT8(updateVal), .ucMinute = UINT32_TO_UINT8(updateVal),
        .ucSecond = UINT32_TO_UINT8(updateVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testSelectAllRecByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testSelectAllRecByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_NOTEQUAL，记录和条件对比，
// usYear相等，ucMonth不相等、ucDay相等、ucHour相等，ucMinute相等、ucSecond相等
TEST_F(SpecialType_Func_DataTime_Ne, V1Com_013_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&pstDsBuf, i, i);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = { .usYear = UINT32_TO_UINT16(opVal), .ucMonth = UINT32_TO_UINT8(opVal + 1),
        .ucDay = UINT32_TO_UINT8(opVal), .ucHour = UINT32_TO_UINT8(opVal), .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(opVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = { .usYear = UINT32_TO_UINT16(updateVal),
        .ucMonth = UINT32_TO_UINT8(updateVal + 1), .ucDay = UINT32_TO_UINT8(updateVal),
        .ucHour = UINT32_TO_UINT8(updateVal), .ucMinute = UINT32_TO_UINT8(updateVal),
        .ucSecond = UINT32_TO_UINT8(updateVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 查询
    DB_BUF_STRU pstBufData;
    ret = testSetBufEx(&pstBufData, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testSelectAllRecByOrderEx(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffDataEx(&pstBufData, DBT_DATETIME, "testSelectAllRecByOrderEx");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstBufData.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufData.ulRecNum; i++) {
        ret = testCheckAllFieldValEx(&pstBufData, i, updateVal, updateVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeBufEx(&pstBufData);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_NOTEQUAL，记录和条件对比，
// usYear相等，ucMonth相等、ucDay不相等、ucHour相等，ucMinute相等、ucSecond相等
TEST_F(SpecialType_Func_DataTime_Ne, V1Com_013_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&pstDsBuf, i, i);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = { .usYear = UINT32_TO_UINT16(opVal), .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal + 1), .ucHour = UINT32_TO_UINT8(opVal), .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(opVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = { .usYear = UINT32_TO_UINT16(updateVal),
        .ucMonth = UINT32_TO_UINT8(updateVal), .ucDay = UINT32_TO_UINT8(updateVal + 1),
        .ucHour = UINT32_TO_UINT8(updateVal), .ucMinute = UINT32_TO_UINT8(updateVal),
        .ucSecond = UINT32_TO_UINT8(updateVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectFirstRec");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_NOTEQUAL，记录和条件对比，
// usYear相等，ucMonth相等、ucDay相等、ucHour不相等，ucMinute相等、ucSecond相等
TEST_F(SpecialType_Func_DataTime_Ne, V1Com_013_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&pstDsBuf, i, i);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = { .usYear = UINT32_TO_UINT16(opVal), .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal), .ucHour = UINT32_TO_UINT8(opVal + 1), .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(opVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    DB_DATETIME_STRU pstDateTimeConVal2 = { .usYear = UINT32_TO_UINT16(updateVal),
        .ucMonth = UINT32_TO_UINT8(updateVal), .ucDay = UINT32_TO_UINT8(updateVal),
        .ucHour = UINT32_TO_UINT8(updateVal + 1), .ucMinute = UINT32_TO_UINT8(updateVal),
        .ucSecond = UINT32_TO_UINT8(updateVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_NOTEQUAL，记录和条件对比，
// usYear相等，ucMonth相等、ucDay相等、ucHour相等，ucMinute不相等、ucSecond相等
TEST_F(SpecialType_Func_DataTime_Ne, V1Com_013_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&pstDsBuf, i, i);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = { .usYear = UINT32_TO_UINT16(opVal), .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal), .ucHour = UINT32_TO_UINT8(opVal), .ucMinute = UINT32_TO_UINT8(opVal + 1),
        .ucSecond = UINT32_TO_UINT8(opVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = { .usYear = UINT32_TO_UINT16(updateVal),
        .ucMonth = UINT32_TO_UINT8(updateVal), .ucDay = UINT32_TO_UINT8(updateVal),
        .ucHour = UINT32_TO_UINT8(updateVal), .ucMinute = UINT32_TO_UINT8(updateVal + 1),
        .ucSecond = UINT32_TO_UINT8(updateVal) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 查询
    DB_BUF_STRU pstBufData;
    ret = testSetBufEx(&pstBufData, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffDataEx(&pstBufData, DBT_DATETIME, "TPC_SelectAllRecEx");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstBufData.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufData.ulRecNum; i++) {
        ret = testCheckAllFieldValEx(&pstBufData, i, updateVal, updateVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeBufEx(&pstBufData);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_DATETIME，DB_OP_NOTEQUAL，记录和条件对比，
// usYear相等，ucMonth相等、ucDay相等、ucHour相等，ucMinute相等、ucSecond不相等
TEST_F(SpecialType_Func_DataTime_Ne, V1Com_013_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&pstDsBuf, i, i);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    DB_DATETIME_STRU pstDateTimeConVal = { .usYear = UINT32_TO_UINT16(opVal), .ucMonth = UINT32_TO_UINT8(opVal),
        .ucDay = UINT32_TO_UINT8(opVal), .ucHour = UINT32_TO_UINT8(opVal), .ucMinute = UINT32_TO_UINT8(opVal),
        .ucSecond = UINT32_TO_UINT8(opVal + 1) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_DATETIME, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    testSetAllField(&pstDsBuf, updateVal, updateVal);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_DATETIME, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    DB_DATETIME_STRU pstDateTimeConVal2 = { .usYear = UINT32_TO_UINT16(updateVal),
        .ucMonth = UINT32_TO_UINT8(updateVal), .ucDay = UINT32_TO_UINT8(updateVal),
        .ucHour = UINT32_TO_UINT8(updateVal), .ucMinute = UINT32_TO_UINT8(updateVal),
        .ucSecond = UINT32_TO_UINT8(updateVal + 1) };
    testSetDateTimeCondVal(&(pstCond.aCond[0]), DATE_TIME_FIELD1_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testSetDateTimeCondVal(&(pstCond.aCond[1]), DATE_TIME_FIELD2_ID, DB_OP_NOTEQUAL, &pstDateTimeConVal2);
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 查询
    VOS_UINT32 pulMatchRecNum;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pulMatchRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulMatchRecNum);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
