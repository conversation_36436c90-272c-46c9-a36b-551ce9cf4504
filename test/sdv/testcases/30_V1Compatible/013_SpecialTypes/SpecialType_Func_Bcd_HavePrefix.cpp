/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 支持V1兼容特殊类型
 Notes        : 功能测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2024/09/02
**************************************************************************** */
#include "SpecialTypeTest.h"

class SpecialType_Func_Bcd_HavePrefix : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        DB_ERR_CODE ret = TestTPC_Init();
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    };
    static void TearDownTestCase()
    {
        TestTPC_UnInit();
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SpecialType_Func_Bcd_HavePrefix::SetUp()
{
    DB_ERR_CODE ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_queryTestDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_queryTestDbName, &g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 用于测试用例在设备失败的时候可以设置环境变量来打印一些具体的信息
    g_testcaseDebugModeVal = getenv("TESTCASE_DEBUG_MODE");
    if (g_testcaseDebugModeVal == NULL) {
        AW_FUN_Log(LOG_INFO, "if you want to printf testcase info, you can 'export TESTCASE_DEBUG_MODE=1'.");
    }
}
void SpecialType_Func_Bcd_HavePrefix::TearDown()
{
    DB_ERR_CODE ret;
    TestAlarmMemLeak();
    ret = TPC_CloseDB(g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_DropDB((VOS_UINT8 *)g_queryTestDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_CHECK_LOG_END();
}

// DBT_BCD，DB_OP_HAVEPREFIX，记录的结束符在第1个字节
TEST_F(SpecialType_Func_Bcd_HavePrefix, V1Com_013_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { BCD_END_HIGH, UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt + 1, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 查询
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));
    testFreeDsBuf(&pstDsBuf);

    // 删除数据
    VOS_UINT32 delSuccRecNum = 10;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(0, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_HAVEPREFIX，条件的结束符在第1个字节
TEST_F(SpecialType_Func_Bcd_HavePrefix, V1Com_013_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { BCD_END_HIGH, UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 删除数据
    VOS_UINT32 delSuccRecNum = 10;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_HAVEPREFIX，记录的结束符不在第1个字节
TEST_F(SpecialType_Func_Bcd_HavePrefix, V1Com_013_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), BCD_END_HIGH,
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt + 1, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));
    testFreeDsBuf(&pstDsBuf);

    // 删除数据
    VOS_UINT32 delSuccRecNum = 10;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(0, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_HAVEPREFIX，条件的结束符不在第1个字节
TEST_F(SpecialType_Func_Bcd_HavePrefix, V1Com_013_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt + 1, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END_HIGH,
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
            UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal, opVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt + 1, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 删除数据
    VOS_UINT32 delSuccRecNum = 10;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_HAVEPREFIX，记录和条件都没有结束符，匹配得到
TEST_F(SpecialType_Func_Bcd_HavePrefix, V1Com_013_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt + 1, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
            UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal, opVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt + 1, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 删除数据
    VOS_UINT32 delSuccRecNum = 10;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_HAVEPREFIX，记录和条件都没有结束符，匹配不到
TEST_F(SpecialType_Func_Bcd_HavePrefix, V1Com_013_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal + 1) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt / 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END,
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(0, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));
    testFreeDsBuf(&pstDsBuf);

    // 删除数据
    VOS_UINT32 delSuccRecNum = 10;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(0, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_HAVEPREFIX，记录和条件都有结束符
// 记录的结束符位置在条件的结束符后，能匹配；记录的结束符位置在条件的结束符前，不能匹配
TEST_F(SpecialType_Func_Bcd_HavePrefix, V1Com_013_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), BCD_END_EQ, BCD_END, UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), BCD_END, UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal), BCD_END_EQ, BCD_END,
            UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal, opVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), BCD_END_EQ, BCD_END,
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), BCD_END, UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));
    testFreeDsBuf(&pstDsBuf);

    // 重新设置条件
    VOS_UINT8 bcdConVal3[5] = { UINT32_TO_UINT8(updateVal), BCD_END, UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal3, sizeof(bcdConVal3));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal3, sizeof(bcdConVal3));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    updateVal = recordCnt + 3;
    VOS_UINT8 pstBcdUpdate2[5] = { BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate2);
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update2");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal4[5] = { BCD_END_HIGH, UINT32_TO_UINT8(updateVal + 1), UINT32_TO_UINT8(updateVal + 1),
        UINT32_TO_UINT8(updateVal + 1), UINT32_TO_UINT8(updateVal + 1) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_HAVEPREFIX, bcdConVal4, sizeof(bcdConVal4));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_HAVEPREFIX, bcdConVal4, sizeof(bcdConVal4));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
