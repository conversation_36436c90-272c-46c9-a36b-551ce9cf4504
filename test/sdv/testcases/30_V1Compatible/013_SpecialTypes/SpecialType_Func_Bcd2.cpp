/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 支持V1兼容特殊类型
 Notes        : 功能测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2024/09/02
**************************************************************************** */
#include "SpecialTypeTest.h"

class SpecialType_Func_Bcd2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        DB_ERR_CODE ret = TestTPC_Init();
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    };
    static void TearDownTestCase()
    {
        TestTPC_UnInit();
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SpecialType_Func_Bcd2::SetUp()
{
    DB_ERR_CODE ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_queryTestDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_queryTestDbName, &g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 用于测试用例在设备失败的时候可以设置环境变量来打印一些具体的信息
    g_testcaseDebugModeVal = getenv("TESTCASE_DEBUG_MODE");
    if (g_testcaseDebugModeVal == NULL) {
        AW_FUN_Log(LOG_INFO, "if you want to printf testcase info, you can 'export TESTCASE_DEBUG_MODE=1'.");
    }
}
void SpecialType_Func_Bcd2::TearDown()
{
    DB_ERR_CODE ret;
    TestAlarmMemLeak();
    ret = TPC_CloseDB(g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_DropDB((VOS_UINT8 *)g_queryTestDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_CHECK_LOG_END();
}

// DBT_BCD，DB_OP_LESS，条件和数据均有结束符，数据的结束符先于条件的结束符找到，数据结束符长度内，
// 数据小于条件，结果：数据小于条件
TEST_F(SpecialType_Func_Bcd2, V1Com_013_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), BCD_END_HIGH,
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt + 1;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        BCD_END_HIGH, UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LESS, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LESS, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), BCD_END_HIGH,
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt / 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal + 1), UINT32_TO_UINT8(updateVal + 1),
        UINT32_TO_UINT8(updateVal + 1), BCD_END_HIGH, UINT32_TO_UINT8(updateVal + 1) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LESS, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LESS, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
            BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_MAX_LESS_EQUAL，条件和数据均有结束符，数据的结束符先于条件的结束符找到，数据结束符长度内，
// 数据等于条件，结果：数据小于条件
TEST_F(SpecialType_Func_Bcd2, V1Com_013_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END_HIGH,
            UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
        testSetAllField(&pstDsBuf, opVal, opVal, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        BCD_END_HIGH, UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_MAX_LESS_EQUAL, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_MAX_LESS_EQUAL, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END_HIGH,
            UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal, opVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt / 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), BCD_END_HIGH, UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_MAX_LESS_EQUAL, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_MAX_LESS_EQUAL, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 查询
    DB_BUF_STRU pstBufData;
    ret = testSetBufEx(&pstBufData, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufData);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffDataEx(&pstBufData, DBT_BCD, "TPC_SelectAllRecEx");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstBufData.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufData.ulRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldValEx(&pstBufData, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeBufEx(&pstBufData);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录是不存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_LESS，条件和数据均有结束符，数据的结束符先于条件的结束符找到，数据结束符长度内，
// 数据大于条件，结果：数据小于条件
TEST_F(SpecialType_Func_Bcd2, V1Com_013_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 opVal = recordCnt / 2;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END_HIGH,
            UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
        testSetAllField(&pstDsBuf, opVal, opVal, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal - 1), UINT32_TO_UINT8(opVal - 1),
        UINT32_TO_UINT8(opVal - 1), BCD_END_HIGH, UINT32_TO_UINT8(opVal - 1) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LESS, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LESS, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END_HIGH,
            UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal, opVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 20;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal - 1), UINT32_TO_UINT8(updateVal - 1),
        UINT32_TO_UINT8(updateVal - 1), BCD_END_HIGH, UINT32_TO_UINT8(updateVal - 1) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LESS, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LESS, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
            BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_EQUAL，条件和数据均有结束符，条件的结束符和数据的结束符位置一致，
// 在第一个对比位，结果：条件和数据相等
TEST_F(SpecialType_Func_Bcd2, V1Com_013_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { BCD_END_HIGH, UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    // 写1条非结束符开头的数据
    VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(recordCnt), BCD_END_HIGH, UINT32_TO_UINT8(recordCnt),
        UINT32_TO_UINT8(recordCnt), UINT32_TO_UINT8(recordCnt) };
    testSetAllField(&pstDsBuf, recordCnt, recordCnt, NULL, pstBcdWrite);
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt + 1, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { BCD_END_HIGH, UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_EQUAL, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_EQUAL, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { BCD_END_HIGH, UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), 
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt + 1, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { BCD_END_HIGH, UINT32_TO_UINT8(updateVal - 1), UINT32_TO_UINT8(updateVal - 1),
        UINT32_TO_UINT8(updateVal - 1), UINT32_TO_UINT8(updateVal - 1) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_EQUAL, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_EQUAL, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_LARGER，条件和数据均有结束符，条件的结束符和数据的结束符位置一致，
// 在中间对比位，条件小于数据，结果：条件小于数据
TEST_F(SpecialType_Func_Bcd2, V1Com_013_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), BCD_END_HIGH,
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END_HIGH,
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LARGER, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LARGER, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal + 1 + i), UINT32_TO_UINT8(opVal + 1 + i),
            BCD_END_HIGH, UINT32_TO_UINT8(opVal + 1 + i), UINT32_TO_UINT8(opVal + 1 + i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + 1 + i, opVal + 1 + i, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal - 1), UINT32_TO_UINT8(updateVal - 1),
        BCD_END_HIGH, UINT32_TO_UINT8(updateVal - 1), UINT32_TO_UINT8(updateVal - 1) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LARGER, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LARGER, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_LARGER，条件和数据均有结束符，条件的结束符和数据的结束符位置一致，
// 在中间对比位，条件等于数据，结果：条件等于数据
TEST_F(SpecialType_Func_Bcd2, V1Com_013_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), BCD_END_HIGH,
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END_HIGH,
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_EQUAL, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_EQUAL, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
            BCD_END_HIGH, UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal, opVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        BCD_END_HIGH, UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_EQUAL, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_EQUAL, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_LARGER，条件和数据均有结束符，条件的结束符和数据的结束符位置一致，
// 在中间对比位，条件大于数据，结果：条件大于数据
TEST_F(SpecialType_Func_Bcd2, V1Com_013_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), BCD_END_HIGH,
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END_HIGH,
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LESS, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LESS, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), BCD_END_HIGH,
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i) };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal + 1), UINT32_TO_UINT8(updateVal + 1),
        BCD_END_HIGH, UINT32_TO_UINT8(updateVal + 1), UINT32_TO_UINT8(updateVal + 1) };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LESS, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LESS, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        if (i < opVal) {
            VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal + i), UINT32_TO_UINT8(opVal + i), BCD_END_HIGH,
                UINT32_TO_UINT8(opVal + i), UINT32_TO_UINT8(opVal + i) };
            ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + i, opVal + i, NULL, pstBcdCheck);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END_HIGH,
                UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal) };
            ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_LARGER，条件和数据均有结束符，条件的结束符和数据的结束符位置一致，
// 在最后对比位，条件小于数据，结果：条件小于数据
TEST_F(SpecialType_Func_Bcd2, V1Com_013_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), BCD_END };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LARGER, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LARGER, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal + 1 + i), UINT32_TO_UINT8(opVal + 1 + i),
            UINT32_TO_UINT8(opVal + 1 + i), UINT32_TO_UINT8(opVal + 1 + i), BCD_END };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + 1 + i, opVal + 1 + i, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal - 1), UINT32_TO_UINT8(updateVal - 1),
        UINT32_TO_UINT8(updateVal - 1), UINT32_TO_UINT8(updateVal - 1), BCD_END };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LARGER, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LARGER, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_LARGER，条件和数据均有结束符，条件的结束符和数据的结束符位置一致，
// 在最后对比位，条件等于数据，结果：条件等于数据
TEST_F(SpecialType_Func_Bcd2, V1Com_013_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), BCD_END };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_EQUAL, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_EQUAL, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
            UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END };
        ret = testCheckAllFieldVal(&pstDsBuf, i, opVal, opVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = {  UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_EQUAL, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_EQUAL, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
            UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END };
        ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DBT_BCD，DB_OP_LARGER，条件和数据均有结束符，条件的结束符和数据的结束符位置一致，
// 在最后对比位，条件大于数据，结果：条件大于数据
TEST_F(SpecialType_Func_Bcd2, V1Com_013_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;
    DB_DSBUF_STRU pstDsBuf;

    // 建表
    VOS_UINT16 pusRelId;
    ret = testCreateDefaultTbl(&pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 1条记录的长度
    VOS_UINT16 recLen = testGetRecLen(g_defaultTblPstFldLst, g_ulNCols);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u", recordCnt);
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        VOS_UINT8 pstBcdWrite[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), BCD_END };
        testSetAllField(&pstDsBuf, i, i, NULL, pstBcdWrite);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after write");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 设置查询条件
    VOS_UINT32 opVal = recordCnt / 2;
    DB_COND_STRU pstCond = { .usCondNum = 2 };
    VOS_UINT8 bcdConVal[BCD_SIZE] = { UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal),
        UINT32_TO_UINT8(opVal), UINT32_TO_UINT8(opVal), BCD_END };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LESS, bcdConVal, sizeof(bcdConVal));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LESS, bcdConVal, sizeof(bcdConVal));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 设置接收的条件
    DB_FIELDFILTER_STRU pstFldFilter;
    testSetFldFilter(&pstFldFilter, DB_FIELD_ALL);

    // 查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "TPC_SelectAllRec");
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(i), UINT32_TO_UINT8(i),
            UINT32_TO_UINT8(i), UINT32_TO_UINT8(i), BCD_END };
        ret = testCheckAllFieldVal(&pstDsBuf, i, i, i, NULL, pstBcdCheck);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    testFreeDsBuf(&pstDsBuf);

    // 更新
    ret = testSetDsBuf(&pstDsBuf, recLen, 1, recLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 updateVal = recordCnt + 2;
    VOS_UINT8 pstBcdUpdate[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
        UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END };
    testSetAllField(&pstDsBuf, updateVal, updateVal, NULL, pstBcdUpdate);
    VOS_UINT32 pulRecNum;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pulRecNum);
    testFreeDsBuf(&pstDsBuf);

    // 获取所有记录，用于输出到屏幕，调试用例
    ret = testScanAllData(g_queryTestDbId, pusRelId, recLen, recordCnt, DBT_BCD, "after update");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 重新设置条件
    VOS_UINT8 bcdConVal2[5] = { UINT32_TO_UINT8(updateVal + 1), UINT32_TO_UINT8(updateVal + 1),
        UINT32_TO_UINT8(updateVal + 1), UINT32_TO_UINT8(updateVal + 1), BCD_END };
    testSetBcdCondVal(&(pstCond.aCond[0]), BCD_FIELD1_ID, DB_OP_LESS, bcdConVal2, sizeof(bcdConVal2));
    testSetBcdCondVal(&(pstCond.aCond[1]), BCD_FIELD2_ID, DB_OP_LESS, bcdConVal2, sizeof(bcdConVal2));
    testPrintCond(&pstCond, g_defaultTblPstFldLst);

    // 分批查询
    ret = testSetDsBuf(&pstDsBuf, recLen, recordCnt, recLen * recordCnt);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = testBeginIdxSelectByOrder(g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBuf);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    testPrintBuffData(&pstDsBuf, DBT_BCD, "testBeginIdxSelectByOrder");
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstDsBuf.usRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstDsBuf.usRecNum; i++) {
        if (i < opVal) {
            VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(opVal + i), UINT32_TO_UINT8(opVal + i),
                UINT32_TO_UINT8(opVal + i), UINT32_TO_UINT8(opVal + i), BCD_END };
            ret = testCheckAllFieldVal(&pstDsBuf, i, opVal + i, opVal + i, NULL, pstBcdCheck);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            VOS_UINT8 pstBcdCheck[5] = { UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal),
                UINT32_TO_UINT8(updateVal), UINT32_TO_UINT8(updateVal), BCD_END };
            ret = testCheckAllFieldVal(&pstDsBuf, i, updateVal, updateVal, NULL, pstBcdCheck);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }
    }
    testFreeDsBuf(&pstDsBuf);

    // 查询记录是存在的
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 删除数据
    VOS_UINT32 delSuccRecNum;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &delSuccRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, delSuccRecNum);

    // 查询记录不存在
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(NULL, false));

    // 删表
    ret = testDropDefaultTbl(pusRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

