/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 Description  : 支持超过页大小对象的写入
 Author       : herui h60035902
 Modification :
 create       : 2025/01/03
**************************************************************************** */
#include "V1_Bigobject.h"

int ret = 0;
class Support_Bigobject : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestTPC_Init();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        ret = TestTPC_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Support_Bigobject::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void Support_Bigobject::TearDown()
{
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
// 001.RDB插入大对象
TEST_F(Support_Bigobject, V1Com_019_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 002.RDB删除大对象
TEST_F(Support_Bigobject, V1Com_019_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 删除大对象
    Delete(testRelId1, 0);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 003.RDB将普通对象更新为大对象
TEST_F(Support_Bigobject, V1Com_019_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入普通对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16000);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 004.RDB将大对象更新为普通对象
TEST_F(Support_Bigobject, V1Com_019_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 更新为普通对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16000);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 005.RDB将大对象更新为大对象
TEST_F(Support_Bigobject, V1Com_019_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 7);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 006.RDB插入删除更新全类型大对象
TEST_F(Support_Bigobject, V1Com_019_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165);
    // 删除大对象
    Delete(testRelId1, 11);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16165);
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 007.开启CDB后进行大对象的DML操作后提交
TEST_F(Support_Bigobject, V1Com_019_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对大对象进行DML操作
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, cdbID);
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16165, cdbID);
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, cdbID);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 008.基于大对象字段作为索引开启CDB后进行大对象的DML操作后回滚
TEST_F(Support_Bigobject, V1Com_019_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对大对象进行DML操作
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 7, cdbID);
    Delete(testRelId1, 11, 7, DB_OP_EQUAL, cdbID);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 009.基于普通字段作为索引开启CDB后进行大对象的DML操作后回滚
TEST_F(Support_Bigobject, V1Com_019_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对大对象进行DML操作
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, cdbID);
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, cdbID);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 010.开启CDB后进行大对象错误DML操作后回滚
TEST_F(Support_Bigobject, V1Com_019_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对大对象进行DML操作
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, cdbID);
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, cdbID);
    // 插入主键冲突数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    testSetAllField(pucDataSet, testRelDef.pstFldLst, 6, 16165);
    pstDsBufSet.StdBuf.pucData = pucDataSet;
    ret = TPC_InsertRec(cdbID, g_testDbId, testRelId1, &pstDsBufSet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
    TEST_V1_FREE(pucDataSet);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 011.对一个大小为64k的大对象进行DML操作
TEST_F(Support_Bigobject, V1Com_019_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 更新为大对象
    Update(testRelId1, 9, 11, testRelDef.pstFldLst, 32690);
    // 删除大对象
    Delete(testRelId1, 11);
    // 校验数据
    ValidateData(testRelId1, 9, testRelDef.pstFldLst, 32690);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 012.插入一个大小为64k+1的数据
TEST_F(Support_Bigobject, V1Com_019_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel4.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MAXRECLEN_EXCEEDED, ret);
}
// 013.使用DB_OP_LESS条件update
TEST_F(Support_Bigobject, V1Com_019_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 更新为大对象
    Update(testRelId1, 1, 11, testRelDef.pstFldLst, 16165, 11, TPC_GLOBAL_CDB, g_testDbId, DB_OP_LESS);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 014.用DB_OP_LESSEQUAL条件update
TEST_F(Support_Bigobject, V1Com_019_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, TPC_GLOBAL_CDB, g_testDbId, DB_OP_LESSEQUAL);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 015.用DB_OP_LARGER条件update
TEST_F(Support_Bigobject, V1Com_019_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 更新为大对象
    Update(testRelId1, 8, 11, testRelDef.pstFldLst, 16165, 11, TPC_GLOBAL_CDB, g_testDbId, DB_OP_LARGER);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 016.用DB_OP_LARGEREQUAL条件update
TEST_F(Support_Bigobject, V1Com_019_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 更新为大对象
    Update(testRelId1, 9, 11, testRelDef.pstFldLst, 16165, 7, TPC_GLOBAL_CDB, g_testDbId, DB_OP_LARGEREQUAL);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    unsigned char sortFields[1] = {1};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.pSortFields = sortFields;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(
        TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[10] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 11};
    // 校验数据
    testAllData(testRelId1, 10, testRelDef.pstFldLst, expectvalue, pucDataGet, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 017.用DB_OP_MAX_LESS条件delete
TEST_F(Support_Bigobject, V1Com_019_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 删除大对象
    Delete(testRelId1, 9, 11, DB_OP_MAX_LESS);
    // 校验数据
    ValidateData(testRelId1, 8, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 018.用DB_OP_MAX_LESS_EQUAL条件delete
TEST_F(Support_Bigobject, V1Com_019_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 删除大对象
    Delete(testRelId1, 9, 11, DB_OP_MAX_LESS_EQUAL);
    // 校验数据
    ValidateData(testRelId1, 9, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 019.用DB_OP_MIN_LARGER条件delete
TEST_F(Support_Bigobject, V1Com_019_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 删除大对象
    Delete(testRelId1, 1, 11, DB_OP_MIN_LARGER);
    // 校验数据
    ValidateData(testRelId1, 2, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 020.用DB_OP_MIN_LARGER_EQUAL条件delete
TEST_F(Support_Bigobject, V1Com_019_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 删除大对象
    Delete(testRelId1, 1, 11, DB_OP_MIN_LARGER_EQUAL);
    // 校验数据
    ValidateData(testRelId1, 1, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 021.插入大对象耗尽DB内存后更新为普通对象
TEST_F(Support_Bigobject, V1Com_019_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t count = 0;
    // 插入数据直到满内存
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 1000000; i++) {
        DB_DSBUF_STRU pstDsBufSet;
        pstDsBufSet.usRecLen = tblRecLen;
        pstDsBufSet.usRecNum = 1;
        pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
        pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
        VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
        (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
        testSetAllField(pucDataSet, testRelDef.pstFldLst, i, 16165);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "ret = %d, insert num:%d", ret, i);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
                TestTPC_SysviewGetLastError("Out of memory. LASTERR (0) reserve dev, devCnt:1, freeCnt:0", false));
            TEST_V1_FREE(pucDataSet);
            count = i;
            break;
        }
        TEST_V1_FREE(pucDataSet);
    }
    DB_COND_STRU pstCond1;
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 11;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;

    *(uint32_t *)pstCond1.aCond[0].aucValue = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufSet1;
    pstDsBufSet1.usRecLen = tblRecLen;
    pstDsBufSet1.usRecNum = 1;
    pstDsBufSet1.StdBuf.ulBufLen = pstDsBufSet1.usRecLen;
    pstDsBufSet1.StdBuf.ulActLen = pstDsBufSet1.usRecLen;
    VOS_UINT8 *pucDataSet1 = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet1.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet1, pstDsBufSet1.StdBuf.ulBufLen, 0x00, pstDsBufSet1.StdBuf.ulBufLen);
    testSetAllField(pucDataSet1, testRelDef.pstFldLst, count + 2, 16165);
    pstDsBufSet1.StdBuf.pucData = pucDataSet1;
    VOS_UINT32 udpRecNum = 0;
    ret = TPC_UpdateRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond1, &pstFldFilter, &pstDsBufSet1, &udpRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
    TEST_V1_FREE(pucDataSet1);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 022.插入大对象耗尽DB内存后删除所有数据
TEST_F(Support_Bigobject, V1Com_019_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t count = 0;
    // 插入数据直到满内存
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 1000000; i++) {
        DB_DSBUF_STRU pstDsBufSet;
        pstDsBufSet.usRecLen = tblRecLen;
        pstDsBufSet.usRecNum = 1;
        pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
        pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
        VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
        (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
        testSetAllField(pucDataSet, testRelDef.pstFldLst, i, 16165);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "ret = %d, insert num:%d", ret, i);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
                TestTPC_SysviewGetLastError("Out of memory. LASTERR (0) reserve dev, devCnt:1, freeCnt:0", false));
            TEST_V1_FREE(pucDataSet);
            count = i - 1;
            break;
        }
        TEST_V1_FREE(pucDataSet);
    }

    while (count > 0) {
        DB_COND_STRU stCond;
        stCond.usCondNum = 1;
        stCond.aCond[0].enOp = DB_OP_EQUAL;
        stCond.aCond[0].ucFieldId = 11;
        *(uint32_t *)stCond.aCond[0].aucValue = count;

        uint32_t delRecNum = 0;
        ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &stCond, &delRecNum);
        if (ret == DB_SUCCESS_V1) {
            count--;
        }
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 023.插入删除更新全类型大对象10000次
TEST_F(Support_Bigobject, V1Com_019_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    for (int i = 0; i < 10000; i++) {
        // 更新为大对象
        Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165);
        // 插入大对象
        Insert(testRelId1, 1, testRelDef.pstFldLst, 16165);
        // 删除大对象
        Delete(testRelId1, 11);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
void *insertToTbl(void *args)
{
    DdlArgsT *ddlArg = (DdlArgsT *)args;
    // 写数据范围100-200
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordData = rand() % 101 + 100;
    // 插入大对象
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ddlArg->testDbId1, ddlArg->testRelId1, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    testSetAllField(pucDataSet, ddlArg->astFlds, recordData, 16165);
    pstDsBufSet.StdBuf.pucData = pucDataSet;
    ret = TPC_InsertRec(ddlArg->cbdId, ddlArg->testDbId1, ddlArg->testRelId1, &pstDsBufSet);
    if (ret != DB_SUCCESS_V1) {
        V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
    }
    TEST_V1_FREE(pucDataSet);
}
void *updateToTbl(void *args)
{
    DdlArgsT *ddlArg = (DdlArgsT *)args;
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 oldvalue = rand() % 10;
    // 更新数据范围200-300
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 newvalue = rand() % 101 + 200;
    // 更新为大对象
    DB_COND_STRU pstCond1;
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 11;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)pstCond1.aCond[0].aucValue = oldvalue;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ddlArg->testDbId1, ddlArg->testRelId1, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    testSetAllField(pucDataSet, ddlArg->astFlds, newvalue, 16165);
    pstDsBufSet.StdBuf.pucData = pucDataSet;
    VOS_UINT32 udpRecNum = 0;
    ret = TPC_UpdateRec(
        ddlArg->cbdId, ddlArg->testDbId1, ddlArg->testRelId1, &pstCond1, &pstFldFilter, &pstDsBufSet, &udpRecNum);
    if (ret != DB_SUCCESS_V1) {
        if (ddlArg->cbdId == TPC_GLOBAL_CDB) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_CHGKEY, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
        }
    }
    TEST_V1_FREE(pucDataSet);
}
void *deleteToTbl(void *args)
{
    DdlArgsT *ddlArg = (DdlArgsT *)args;
    // 写数据范围300-400
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordData = rand() % 101 + 300;
    // 插入大对象
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(ddlArg->testDbId1, ddlArg->testRelId1, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    testSetAllField(pucDataSet, ddlArg->astFlds, recordData, 16165);
    pstDsBufSet.StdBuf.pucData = pucDataSet;
    ret = TPC_InsertRec(ddlArg->cbdId, ddlArg->testDbId1, ddlArg->testRelId1, &pstDsBufSet);
    if (ret != DB_SUCCESS_V1) {
        V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
    }
    TEST_V1_FREE(pucDataSet);
    // 删除大对象
    Delete(ddlArg->testRelId1, recordData, 11, DB_OP_EQUAL, ddlArg->cbdId, ddlArg->testDbId1);
}
// 024.并发50个线程，分别对RDB执行插入删除更新操作
TEST_F(Support_Bigobject, V1Com_019_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    pthread_t tid[50];
    // 并发插入删除更新操作
    DdlArgsT ddlArg;
    ddlArg.testRelId1 = testRelId1;
    ddlArg.testDbId1 = g_testDbId;
    ddlArg.cbdId = TPC_GLOBAL_CDB;
    ddlArg.astFlds = testRelDef.pstFldLst;
    for (int i = 0; i < 50; i++) {
        if (i < 16) {
            ret = pthread_create(&tid[i], NULL, insertToTbl, &ddlArg);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        if (i >= 16 && i < 32) {
            ret = pthread_create(&tid[i], NULL, updateToTbl, &ddlArg);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        if (i >= 32) {
            ret = pthread_create(&tid[i], NULL, deleteToTbl, &ddlArg);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    for (int i = 0; i < 50; i++) {
        ret = pthread_join(tid[i], NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 025.并发50个线程，分别开启CDB执行插入删除更新操作
TEST_F(Support_Bigobject, V1Com_019_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    pthread_t tid[50];
    // 并发插入删除更新操作
    DdlArgsT ddlArg;
    ddlArg.testRelId1 = testRelId1;
    ddlArg.testDbId1 = g_testDbId;
    ddlArg.cbdId = cdbID;
    ddlArg.astFlds = testRelDef.pstFldLst;
    for (int i = 0; i < 50; i++) {
        if (i < 16) {
            ret = pthread_create(&tid[i], NULL, insertToTbl, &ddlArg);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        if (i >= 16 && i < 32) {
            ret = pthread_create(&tid[i], NULL, updateToTbl, &ddlArg);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        if (i >= 32) {
            ret = pthread_create(&tid[i], NULL, deleteToTbl, &ddlArg);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    for (int i = 0; i < 50; i++) {
        ret = pthread_join(tid[i], NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 026.并发多个线程，分别对不同的DB进行操作
TEST_F(Support_Bigobject, V1Com_019_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testDbName1[DB_NAME_LEN] = "testDb";
    char testDbName2[DB_NAME_LEN] = "testDb2";
    char testDbName3[DB_NAME_LEN] = "testDb3";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName1, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName2, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName3, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId1 = 0;
    uint32_t testDbId2 = 0;
    uint32_t testDbId3 = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName1, &testDbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName2, &testDbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName3, &testDbId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB1建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef1 = {0};
    ret = TestTPC_CreateTbl(testDbId1, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB2建表
    uint16_t testRelId2 = 0;
    DB_REL_DEF_STRU testRelDef2 = {0};
    ret = TestTPC_CreateTbl(testDbId2, "schema_file/vertexlabel1.json", &testRelId2, &testRelDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB3建表
    uint16_t testRelId3 = 0;
    DB_REL_DEF_STRU testRelDef3 = {0};
    ret = TestTPC_CreateTbl(testDbId3, "schema_file/vertexlabel1.json", &testRelId3, &testRelDef3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef1.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId1);
    Insert(testRelId2, 10, testRelDef2.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId2);
    Insert(testRelId3, 10, testRelDef3.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId3);
    pthread_t tid1, tid2, tid3;
    // 并发插入删除更新操作
    DdlArgsT ddlArg;
    ddlArg.testRelId1 = testRelId1;
    ddlArg.testDbId1 = testDbId1;
    ddlArg.cbdId = TPC_GLOBAL_CDB;
    ddlArg.astFlds = testRelDef1.pstFldLst;
    ret = pthread_create(&tid1, NULL, insertToTbl, &ddlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DdlArgsT ddlArg1;
    ddlArg1.testRelId1 = testRelId2;
    ddlArg1.testDbId1 = testDbId2;
    ddlArg1.cbdId = TPC_GLOBAL_CDB;
    ddlArg1.astFlds = testRelDef2.pstFldLst;
    ret = pthread_create(&tid2, NULL, updateToTbl, &ddlArg1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DdlArgsT ddlArg2;
    ddlArg2.testRelId1 = testRelId3;
    ddlArg2.testDbId1 = testDbId3;
    ddlArg2.cbdId = TPC_GLOBAL_CDB;
    ddlArg2.astFlds = testRelDef3.pstFldLst;
    ret = pthread_create(&tid3, NULL, deleteToTbl, &ddlArg2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = pthread_join(tid1, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid2, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid3, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef1);
    TestFreeTblStructDef(&testRelDef2);
    TestFreeTblStructDef(&testRelDef3);
}
// 027.TPC_SelectAllRec覆盖DB_OP_EQUAL过滤算子
TEST_F(Support_Bigobject, V1Com_019_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    pstCond.aCond[0].ucFieldId = 11;
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 1;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[1] = {1};
    // 校验数据
    testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 028.TPC_SelectAllRec覆盖DB_OP_NOTEQUAL过滤算子
TEST_F(Support_Bigobject, V1Com_019_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_NOTEQUAL;
    pstCond.aCond[0].ucFieldId = 11;
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 8;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[9] = {0, 1, 2, 3, 4, 5, 6, 7, 9};
    // 校验数据
    testAllData(testRelId1, 9, testRelDef.pstFldLst, expectvalue, pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 029.TPC_SelectAllRecEx覆盖DB_OP_LESS过滤算子
TEST_F(Support_Bigobject, V1Com_019_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_LESS;
    pstCond.aCond[0].ucFieldId = 11;
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 5;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.ulRecNum = 10;
    pstDsBufGet.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.ulBufLen, 0x00, pstDsBufGet.ulBufLen);
    pstDsBufGet.pBuf = pucDataGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[5] = {0, 1, 2, 3, 4};
    // 校验数据
    testAllData(testRelId1, 5, testRelDef.pstFldLst, expectvalue, pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 030.TPC_SelectAllRecEx覆盖DB_OP_LESSEQUAL过滤算子
TEST_F(Support_Bigobject, V1Com_019_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    pstCond.aCond[0].ucFieldId = 11;
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 4;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.ulRecNum = 10;
    pstDsBufGet.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.ulBufLen, 0x00, pstDsBufGet.ulBufLen);
    pstDsBufGet.pBuf = pucDataGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[5] = {0, 1, 2, 3, 4};
    // 校验数据
    testAllData(testRelId1, 5, testRelDef.pstFldLst, expectvalue, pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 031.TPC_SelectAllRecByOrder覆盖DB_OP_LARGER过滤算子
TEST_F(Support_Bigobject, V1Com_019_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.pSortFields = sortFields;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_LARGER;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 4);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 16165);
    longStringVal[16164] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(
        TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[5] = {5, 6, 7, 8, 9};
    // 校验数据
    testAllData(testRelId1, 5, testRelDef.pstFldLst, expectvalue, pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 032.TPC_SelectAllRecByOrder覆盖DB_OP_LARGEREQUAL过滤算子
TEST_F(Support_Bigobject, V1Com_019_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = DB_SORTTYPE_DESCEND;
    pstSort.pSortFields = sortFields;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 16165);
    longStringVal[16164] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(
        TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[5] = {9, 8, 7, 6, 5};
    // 校验数据
    testAllData(testRelId1, 5, testRelDef.pstFldLst, expectvalue, pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 033.TPC_SelectAllRecByOrder覆盖DB_OP_MAX_LESS过滤算子
TEST_F(Support_Bigobject, V1Com_019_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.pSortFields = sortFields;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 16165);
    longStringVal[16164] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(
        TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[1] = {4};
    // 校验数据
    testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pucDataGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 034.TPC_SelectAllRecByOrderEx覆盖DB_OP_MAX_LESS_EQUAL过滤算子
TEST_F(Support_Bigobject, V1Com_019_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.pSortFields = sortFields;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 32690);
    longStringVal[32689] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;
    // *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 5;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.ulRecNum = 10;
    pstDsBufGet.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.ulBufLen, 0x00, pstDsBufGet.ulBufLen);
    pstDsBufGet.pBuf = pucDataGet;
    ret = TPC_SelectAllRecByOrderEx(
        TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[1] = {5};
    // 校验数据
    testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pucDataGet, 32690);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 035.TPC_SelectAllRecByOrderEx覆盖DB_OP_MIN_LARGER过滤算子
TEST_F(Support_Bigobject, V1Com_019_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.pSortFields = sortFields;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 4);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 32690);
    longStringVal[32689] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;
    // *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 5;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.ulRecNum = 10;
    pstDsBufGet.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.ulBufLen, 0x00, pstDsBufGet.ulBufLen);
    pstDsBufGet.pBuf = pucDataGet;
    ret = TPC_SelectAllRecByOrderEx(
        TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[1] = {5};
    // 校验数据
    testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pucDataGet, 32690);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 036.TPC_SelectAllRecByOrderEx覆盖DB_OP_MIN_LARGER_EQUAL过滤算子
TEST_F(Support_Bigobject, V1Com_019_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.pSortFields = sortFields;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 32690);
    longStringVal[32689] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.ulRecNum = 10;
    pstDsBufGet.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.ulBufLen, 0x00, pstDsBufGet.ulBufLen);
    pstDsBufGet.pBuf = pucDataGet;
    ret = TPC_SelectAllRecByOrderEx(
        TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[1] = {5};
    // 校验数据
    testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pucDataGet, 32690);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 037.TPC_SelectFirstRec覆盖DB_OP_HAVEPREFIX过滤算子
TEST_F(Support_Bigobject, V1Com_019_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)stringVal;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[1] = {5};
    // 校验数据
    testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pucDataGet, 32690);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 038.TPC_SelectFirstRec覆盖DB_OP_NOPREFIX过滤算子
TEST_F(Support_Bigobject, V1Com_019_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_NOPREFIX;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)stringVal;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[1] = {0};
    // 校验数据
    testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pucDataGet, 32690);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 039.TPC_SelectFirstRec覆盖DB_OP_PREFIX过滤算子
TEST_F(Support_Bigobject, V1Com_019_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_PREFIX;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)stringVal;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = 10;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * 10;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 预计匹配到的数据
    VOS_UINT32 expectvalue[1] = {5};
    // 校验数据
    testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pucDataGet, 32690);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TEST_V1_FREE(pucDataGet);
}
// 040.TPC_CountMatchingRecs覆盖DB_OP_PREFIX12过滤算子
TEST_F(Support_Bigobject, V1Com_019_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_PREFIX12;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)stringVal;

    VOS_UINT32 pulRecNum;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 041.TPC_CountMatchingRecs覆盖DB_OP_PREFIX21过滤算子
TEST_F(Support_Bigobject, V1Com_019_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_PREFIX21;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 4);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 32699);
    longStringVal[32698] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;

    VOS_UINT32 pulRecNum;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 042.TPC_CountMatchingRecs覆盖DB_OP_MAX_PREFIX12过滤算子
TEST_F(Support_Bigobject, V1Com_019_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MAX_PREFIX12;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)stringVal;

    VOS_UINT32 pulRecNum;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pulRecNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_ASSERT_EQ_INT(1, pulRecNum);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 043.TPC_RecordExist覆盖DB_OP_MAX_PREFIX21过滤算子
TEST_F(Support_Bigobject, V1Com_019_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MAX_PREFIX21;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 4);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 16199);
    longStringVal[16198] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;

    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 044.TPC_RecordExist覆盖DB_OP_MIN_PREFIX12过滤算子
TEST_F(Support_Bigobject, V1Com_019_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MIN_PREFIX12;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 5);
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)stringVal;

    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 045.TPC_RecordExist覆盖DB_OP_MIN_PREFIX21过滤算子
TEST_F(Support_Bigobject, V1Com_019_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MIN_PREFIX21;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "re%u", 4);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 16199);
    longStringVal[16198] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;

    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 046.TPC_SelectAllRecByPath覆盖DB_OP_POSTFIX21过滤算子
TEST_F(Support_Bigobject, V1Com_019_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表2
    uint16_t testRelId2 = 0;
    DB_REL_DEF_STRU testRelDef1 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1_1.json", &testRelId2, &testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表3
    uint16_t testRelId3 = 0;
    DB_REL_DEF_STRU testRelDef2 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1_2.json", &testRelId3, &testRelDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16163);
    Insert(testRelId2, 10, testRelDef.pstFldLst, 16163);
    Insert(testRelId3, 10, testRelDef.pstFldLst, 16163);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {7};
    uint8_t field2[1] = {7};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 使用全路径扫描查询
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_POSTFIX21;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "abcre%u", 4);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 16165);
    longStringVal[16165] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;
    ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuff->dataBuf[0].ulRecNum);
    // 校验数据
    VOS_UINT32 expectvalue[1] = {4};
    uint8_t *pBufGet = NULL;
    for (VOS_UINT32 i = 0; i < pstBuff->realRelNum; i++) {
        pBufGet = (uint8_t *)pstBuff->dataBuf[i].pBuf;
        testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pBufGet, 16163);
    }
    // 删边表
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge1-2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge2-3");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TestFreeTblStructDef(&testRelDef1);
    TestFreeTblStructDef(&testRelDef2);
}
// 047.TPC_SelectAllRecByPath覆盖DB_OP_MAX_POSTFIX21过滤算子
TEST_F(Support_Bigobject, V1Com_019_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表2
    uint16_t testRelId2 = 0;
    DB_REL_DEF_STRU testRelDef1 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1_1.json", &testRelId2, &testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表3
    uint16_t testRelId3 = 0;
    DB_REL_DEF_STRU testRelDef2 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1_2.json", &testRelId3, &testRelDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    Insert(testRelId2, 10, testRelDef.pstFldLst, 16165);
    Insert(testRelId3, 10, testRelDef.pstFldLst, 16165);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {7};
    uint8_t field2[1] = {7};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 使用全路径扫描查询
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_MAX_POSTFIX21;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "are%u", 4);
    // 超长字符串
    const uint32_t longLen = 65535;
    char longStringVal[longLen];
    memset_s(longStringVal, longLen, 0x00, longLen);
    memset_s(longStringVal, longLen, 'a', 16167);
    longStringVal[16167] = '\0';
    // 拼接前缀
    strncpy(longStringVal, stringVal, strlen(stringVal));
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)longStringVal;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    // 设置查询路径
    DB_PATH_STRU pstPath;
    pstPath.edge[0].edgeInfo[0] = edgeDef1.relInfos[0];
    pstPath.edge[0].edgeInfo[1] = edgeDef1.relInfos[1];
    pstPath.edge[0].pstFldFilter = &pstFldFilter;
    pstPath.edge[1].edgeInfo[0] = edgeDef2.relInfos[0];
    pstPath.edge[1].edgeInfo[1] = edgeDef2.relInfos[1];
    pstPath.edge[1].pstFldFilter = &pstFldFilter;
    pstPath.edgeNum = 2;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;
    ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &pstPath, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuff->dataBuf[0].ulRecNum);
    // 校验数据
    VOS_UINT32 expectvalue[1] = {4};
    uint8_t *pBufGet = NULL;
    for (VOS_UINT32 i = 0; i < pstBuff->realRelNum; i++) {
        pBufGet = (uint8_t *)pstBuff->dataBuf[i].pBuf;
        testAllData(testRelId1, 1, testRelDef.pstFldLst, expectvalue, pBufGet, 16165);
    }
    // 删边表
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge1-2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge2-3");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TestFreeTblStructDef(&testRelDef1);
    TestFreeTblStructDef(&testRelDef2);
}
// 048.TPC_FetchSelectTopoRec覆盖DB_OP_LIKE过滤算子
TEST_F(Support_Bigobject, V1Com_019_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表2
    uint16_t testRelId2 = 0;
    DB_REL_DEF_STRU testRelDef1 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3_1.json", &testRelId2, &testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表3
    uint16_t testRelId3 = 0;
    DB_REL_DEF_STRU testRelDef2 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3_2.json", &testRelId3, &testRelDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    Insert(testRelId2, 10, testRelDef.pstFldLst, 32690);
    Insert(testRelId3, 10, testRelDef.pstFldLst, 32690);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {7};
    uint8_t field2[1] = {7};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 使用全路径扫描查询
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_LIKE;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)strcpy_s(stringVal, stringLen, "%aaaa");
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)stringVal;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 10;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    VOS_UINT32 expectvalue[10] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    uint8_t *pBufGet = NULL;
    pBufGet = (uint8_t *)pstBuff->dataBuf[0].pBuf;
    testAllData(testRelId1, 10, testRelDef.pstFldLst, expectvalue, pBufGet, 32690);
    TPC_FreeBufMemById(pstBuff->memId);
    // 删边表
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge1-2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge2-3");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TestFreeTblStructDef(&testRelDef1);
    TestFreeTblStructDef(&testRelDef2);
}
// 049.TPC_FetchSelectTopoRec覆盖DB_OP_NOLIKE过滤算子
TEST_F(Support_Bigobject, V1Com_019_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表2
    uint16_t testRelId2 = 0;
    DB_REL_DEF_STRU testRelDef1 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3_1.json", &testRelId2, &testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表3
    uint16_t testRelId3 = 0;
    DB_REL_DEF_STRU testRelDef2 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3_2.json", &testRelId3, &testRelDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    Insert(testRelId2, 10, testRelDef.pstFldLst, 32690);
    Insert(testRelId3, 10, testRelDef.pstFldLst, 32690);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {7};
    uint8_t field2[1] = {7};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 使用全路径扫描查询
    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_NOTLIKE;
    pstCond.aCond[0].ucFieldId = 7;
    // 设置超长字符串作为索引条件
    const uint32_t stringLen = 17;
    char stringVal[stringLen];
    memset_s(stringVal, stringLen, 0x00, stringLen);
    (void)sprintf_s(stringVal, stringLen, "%aabb");
    *(VOS_UINTPTR *)pstCond.aCond[0].aucValue = (VOS_UINTPTR)stringVal;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = 10;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳表2
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, testRelId1, testRelId2, &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表2的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    VOS_UINT32 expectvalue[10] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    uint8_t *pBufGet = NULL;
    pBufGet = (uint8_t *)pstBuff->dataBuf[0].pBuf;
    testAllData(testRelId1, 10, testRelDef.pstFldLst, expectvalue, pBufGet, 32690);
    TPC_FreeBufMemById(pstBuff->memId);
    // 删边表
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge1-2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge2-3");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TestFreeTblStructDef(&testRelDef1);
    TestFreeTblStructDef(&testRelDef2);
}
// 050.普通DB导出导入后进行DML操作
TEST_F(Support_Bigobject, V1Com_019_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId);
    // 导出
    char exportFile[64] = "./filePath/export.db2";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, TPC_GLOBAL_CDB, testDbId);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId);
    // 删除大对象
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, TPC_GLOBAL_CDB, testDbId);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165, testDbId);
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"testDb", TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 051.持久化DB导出导入后进行DML操作和查询操作
TEST_F(Support_Bigobject, V1Com_019_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("mkdir perFilePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    char filePath[64] = "./perFilePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId);
    // 导出
    char exportFile[64] = "./filePath/export.db2";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE,
        &stDbConfig, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, TPC_GLOBAL_CDB, testDbId);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId);
    // 删除大对象
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, TPC_GLOBAL_CDB, testDbId);
    // 查询
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165, testDbId);
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"testDb", TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
    system("rm -rf ./perFilePath");
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 052.对数据大小都为64k的DB进行导出导入操作
TEST_F(Support_Bigobject, V1Com_019_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690, TPC_GLOBAL_CDB, testDbId);
    // 导出
    char exportFile[64] = "./filePath/export.db2";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 32690, 11, TPC_GLOBAL_CDB, testDbId);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 32690, TPC_GLOBAL_CDB, testDbId);
    // 删除大对象
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, TPC_GLOBAL_CDB, testDbId);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 32690, testDbId);
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"testDb", TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
void *bkpPhy(void *args)
{
    DdlArgsT *ddlArg = (DdlArgsT *)args;
    char exportFile[64] = "./filePath/export.db2";
    ret = TPC_BkpPhy(ddlArg->testDbId1, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void *reStore(void *args)
{
    DdlArgsT *ddlArg = (DdlArgsT *)args;
    // 导入
    char exportFile[64] = "./filePath/export.db2";
    char testDbName[DB_NAME_LEN] = "testDb";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    while (1) {
        ret = TPC_Restore(
            (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, TPC_NOWAIT);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_OPENFILE_ERROR, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            break;
        }
    }
}
// 053.导出和导入并发操作
TEST_F(Support_Bigobject, V1Com_019_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId);
    pthread_t tid1, tid2;
    // 并发dml操作和删表操作
    DdlArgsT ddlArg;
    ddlArg.testDbId1 = testDbId;
    ret = pthread_create(&tid1, NULL, reStore, &ddlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_create(&tid2, NULL, bkpPhy, &ddlArg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid1, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = pthread_join(tid2, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"testDb", TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 054.txt文件的导出导入
TEST_F(Support_Bigobject, V1Com_019_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId);
    // 导出
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, TPC_GLOBAL_CDB, testDbId);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16165, TPC_GLOBAL_CDB, testDbId);
    // 删除大对象
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, TPC_GLOBAL_CDB, testDbId);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165, testDbId);
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"testDb", TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 055.RDB对略小于32k的对象进行DML操作
TEST_F(Support_Bigobject, V1Com_019_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel5.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16055);
    // 更新大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16055);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16055);
    // 删除大对象
    Delete(testRelId1, 11, 11, DB_OP_EQUAL);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16055);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 056.开启CDB后对略小于32k的对象进行DML操作
TEST_F(Support_Bigobject, V1Com_019_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel5.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16055, TPC_GLOBAL_CDB, cdbID);
    // 更新大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16055, 11, TPC_GLOBAL_CDB, cdbID);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16055, TPC_GLOBAL_CDB, cdbID);
    // 删除大对象
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, TPC_GLOBAL_CDB, cdbID);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16055);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 057.插入大对象耗尽DB内存后查询数据
TEST_F(Support_Bigobject, V1Com_019_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t count = 0;
    // 插入数据直到满内存
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId1, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 1000000; i++) {
        DB_DSBUF_STRU pstDsBufSet;
        pstDsBufSet.usRecLen = tblRecLen;
        pstDsBufSet.usRecNum = 1;
        pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
        pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
        VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
        (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
        testSetAllField(pucDataSet, testRelDef.pstFldLst, i, 16165);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId1, &pstDsBufSet);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "ret = %d, insert num:%d", ret, i);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
                TestTPC_SysviewGetLastError("Out of memory. LASTERR (0) reserve dev, devCnt:1, freeCnt:0", false));
            TEST_V1_FREE(pucDataSet);
            count = i;
            break;
        }
        TEST_V1_FREE(pucDataSet);
    }
    // 验证数据
    ValidateData(testRelId1, count, testRelDef.pstFldLst, 16165, g_testDbId);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 058.索引字段大小超32k时使用该字段进行DML操作
TEST_F(Support_Bigobject, V1Com_019_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 更新大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 32690, 7);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 32690);
    // 删除大对象
    Delete(testRelId1, 11);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 059.使用字段大小超32k的索引建立边表
TEST_F(Support_Bigobject, V1Com_019_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表1
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表2
    uint16_t testRelId2 = 0;
    DB_REL_DEF_STRU testRelDef1 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3_1.json", &testRelId2, &testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表3
    uint16_t testRelId3 = 0;
    DB_REL_DEF_STRU testRelDef2 = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3_2.json", &testRelId3, &testRelDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    Insert(testRelId2, 10, testRelDef.pstFldLst, 32690);
    Insert(testRelId3, 10, testRelDef.pstFldLst, 32690);
    // 建边表1-2
    DB_EDGE_DEF_STRU edgeDef1;
    uint8_t field1[1] = {7};
    uint8_t field2[1] = {7};
    TestCreateEdgeDef(&edgeDef1, true, testRelId1, testRelId2, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge1-2", &edgeDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建边表2-3
    DB_EDGE_DEF_STRU edgeDef2;
    TestCreateEdgeDef(&edgeDef2, true, testRelId2, testRelId3, 1, 1, field1, field2);
    ret = TPC_CreateEdge(g_testDbId, (uint8_t *)"edge2-3", &edgeDef2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删边表
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge1-2");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropEdge(g_testDbId, (uint8_t *)"edge2-3");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    TestFreeTblStructDef(&testRelDef1);
    TestFreeTblStructDef(&testRelDef2);
}
class Support_Bigobject1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        char tempCmd[512] = {0};
        (void)snprintf(tempCmd, sizeof(tempCmd), "sh $TEST_HOME/tools/modifyCfg.sh pageSize=16");
        ret = system(tempCmd);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        system("sh $TEST_HOME/tools/start.sh");
        ret = TestTPC_Init();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestTPC_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Support_Bigobject1::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void Support_Bigobject1::TearDown()
{
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
// 060.页大小为16k时开启CDB后进行大对象的DML操作后提交
TEST_F(Support_Bigobject1, V1Com_019_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对大对象进行DML操作
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, cdbID);
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16165, cdbID);
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, cdbID);
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 061.页大小为16k时对一个大小为64k的大对象进行DML操作
TEST_F(Support_Bigobject1, V1Com_019_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 32690);
    // 更新为大对象
    Update(testRelId1, 9, 11, testRelDef.pstFldLst, 32690);
    // 删除大对象
    Delete(testRelId1, 11);
    // 校验数据
    ValidateData(testRelId1, 9, testRelDef.pstFldLst, 32690);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
class Support_Bigobject2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        char tempCmd[512] = {0};
        (void)snprintf(tempCmd, sizeof(tempCmd), "sh $TEST_HOME/tools/modifyCfg.sh pageSize=8");
        ret = system(tempCmd);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        system("sh $TEST_HOME/tools/start.sh");
        ret = TestTPC_Init();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {

        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestTPC_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Support_Bigobject2::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void Support_Bigobject2::TearDown()
{
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
//  062.页大小为8k时RDB插入删除更新全类型大对象
TEST_F(Support_Bigobject2, V1Com_019_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;

    // 创建一个全新的数据类型
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 更新为大对象
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165);
    // 删除大对象
    Delete(testRelId1, 11);
    // 插入大对象
    Insert(testRelId1, 1, testRelDef.pstFldLst, 16165);
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 063.页大小为8k时基于普通字段作为索引开启CDB后进行大对象的DML操作后回滚
TEST_F(Support_Bigobject2, V1Com_019_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入大对象
    Insert(testRelId1, 10, testRelDef.pstFldLst, 16165);
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对大对象进行DML操作
    Update(testRelId1, 0, 11, testRelDef.pstFldLst, 16165, 11, cdbID);
    Delete(testRelId1, 11, 11, DB_OP_EQUAL, cdbID);
    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    ValidateData(testRelId1, 10, testRelDef.pstFldLst, 16165);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 064.DTS2025060531530联调用例
TEST_F(Support_Bigobject, V1Com_019_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    Insert(testRelId1, 15, testRelDef.pstFldLst, 32690);
    uint32_t cdbID1 = 0;
    uint32_t cdbID2 = 1;
    ret = TPC_BeginCDBByID(g_testDbId, cdbID1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_BeginCDBByID(g_testDbId, cdbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对CDB1插入多条数据
    Insert(testRelId1, 100, testRelDef.pstFldLst, 32690, cdbID1, g_testDbId, 15);
    // 对CDB2删除一条数据
    Delete(testRelId1, 10, 11, DB_OP_EQUAL, cdbID2);
    // 提交CDB1
    ret = TPC_CommitCDB(cdbID1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 强制提交CDB2
    ret = TPC_ForceCommitCDB(cdbID2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
