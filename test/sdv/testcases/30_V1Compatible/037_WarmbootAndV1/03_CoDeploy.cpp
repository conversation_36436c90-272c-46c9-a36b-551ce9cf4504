/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 037_WarmbootAndV1
 * Author: hanyang
 * Create: 2025-05-07
 */
#include "PublicFunc.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

class CoDeploy : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void CoDeploy::SetUpTestCase()
{
    int ret;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());

    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void CoDeploy::TearDownTestCase()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
}

void CoDeploy::SetUp()
{
    int ret;

    system("ipcrm");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh defaultTablespaceMaxSize=64");
    system("${TEST_HOME}/tools/start.sh");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void CoDeploy::TearDown()
{
    AW_CHECK_LOG_END();

    int ret;

    // 删除V5表
    TestDropLabel(g_stmt, "Vertex_01");

    // 关闭连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 001.同一客户端分别创建V5表和V1 DB和表，使用阶段二接口，V5服务重启，
                重启后查询表依然存在，V1表不受V5重启影响，查询V1表也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(ulDbId, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 删除DB
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 002.同一客户端分别操作V5表和V1 DB和表，执行DML操作和扫描查询操作，使用阶段二接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 003.同一客户端分别操作V5表和V1 DB和表，开启事务，执行DML操作和扫描查询操作，使用阶段二接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(cdbID, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 004.同一客户端分别创建V5表和V1 DB和表，执行导入导出操作，使用阶段二接口，
                V5服务重启，重启后查询表依然存在，V1表不受V5重启影响，查询V1表也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    system("mkdir filePath1");
    system("rm -rf ./filePath1/*.txt");

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath1/export.txt";
    ret = TPC_BkpPhy(ulDbId, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)"DB1", &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)"DB1", NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(ulDbId, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 删除DB
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 005.同一客户端分别操作V5表和V1 DB和表，通过元数据接口查询表的字段类型，使用阶段二接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = TPC_GetTblNamesAndCount(ulDbId, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 008.同一客户端分别操作V5表和V1 DB和表，执行DML操作和扫描查询操作，使用阶段二接口，
                V5服务停止，V1表DML操作和扫描查询正常，V5服务重新启动，查询表和数据依然存在，V1表DML操作和扫描查询正常，
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "stop服务");
    (void)system("sh $TEST_HOME/tools/stop.sh -f");
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 011.同一客户端分别创建V5表和V1 DB和表，使用阶段一接口，V5服务重启，
                重启后查询表依然存在，V1表不受V5重启影响，查询V1表也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = DB_GetTblNamesAndCount(ulDbId, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 012.同一客户端分别操作V5表和V1 DB和表，执行DML操作和扫描查询操作，使用阶段一接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 013.同一客户端分别操作V5表和V1 DB和表，开启事务，执行DML操作和扫描查询操作，使用阶段一接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 014.同一客户端分别创建V5表和V1 DB和表，执行导入导出操作，使用阶段一接口，
                V5服务重启，重启后查询表依然存在，V1表不受V5重启影响，查询V1表也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    system("mkdir filePath1");
    system("rm -rf ./filePath1/*.txt");

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath1/export.txt";
    ret = DB_BkpPhy(ulDbId, (uint8_t *)filePath);  // 导出成功
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次OpenDB后导入
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)"DB1", &ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((uint8_t *)filePath, (VOS_UINT8 *)"DB1", NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = DB_GetTblNamesAndCount(ulDbId, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 删除DB
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 015.同一客户端分别操作V5表和V1 DB和表，通过元数据接口查询表的字段类型，使用阶段一接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    // 获取表名
    VOS_UINT32 pulTblCount = 10;
    VOS_UINT8 *resultBuff[1];
    resultBuff[0] = (VOS_UINT8 *)TEST_V1_MALLOC(DB_REL_NAME_LEN);
    ret = DB_GetTblNamesAndCount(ulDbId, &pulTblCount, (VOS_UINT8 **)resultBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_MACRO_EXPECT_EQ_STR("label1", (char *)resultBuff[0]);  // 表名为 label1
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulTblCount);                   // 只有1张表
    TEST_V1_FREE(resultBuff[0]);

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 018.同一客户端分别操作V5表和V1 DB和表，执行DML操作和扫描查询操作，使用阶段一接口，
                V5服务停止，V1表DML操作和扫描查询正常，V5服务重新启动，查询表和数据依然存在，V1表DML操作和扫描查询正常，
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "stop服务");
    (void)system("sh $TEST_HOME/tools/stop.sh -f");
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 021.同一客户端分别操作V5表和V1 DB和表，执行DML操作和扫描查询操作，
                同时使用阶段一和阶段二接口，V5服务重启，重启后查询表和数据依然存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeploy, V1Com_037_CoDeploy_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    /**************************阶段二*****************************/
    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /**************************阶段一*****************************/
    // 创建db
    uint32_t ulDbId1;
    TestDBCreateDB("DB3", &ulDbId1);

    // 创建表
    DB_REL_DEF_STRU stRelDef1 = {0};
    uint16_t usRelId1 = 0;
    ret = TestDB_CreateTbl(ulDbId1, "schema_file/label1.json", &usRelId1, &stRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen1 = TestGetRecLen(&stRelDef1);
    uint8_t *recBuf1 = (uint8_t *)TEST_V1_MALLOC(recLen1);
    (void)memset_s(recBuf1, recLen1, 0x00, recLen1);

    int insertNum1 = 1000;
    for (int i = 0; i < insertNum1; i++) {
        TestInsertValue(recBuf1, i, recLen1);
        DB_DSBUF_STRU dsBuf1 = {
            .usRecLen = recLen1, .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen1, .ulActLen = recLen1, .pucData = recBuf1}};
        ret = DB_InsertRec(ulDbId1, usRelId1, &dsBuf1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum1 = 0;
    for (int i = 0; i < insertNum1; i++) {
        TestDBSelectValue(ulDbId1, usRelId1, i, &recNum1);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum1);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = 0; i < insertNum1; i++) {
        TestDBSelectValue(ulDbId1, usRelId1, i, &recNum1);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum1);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf1);
    TestFreeRelDef(&stRelDef1);
    ret = DB_CloseDB(ulDbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB3", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

class CoDeployModify : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void CoDeployModify::SetUpTestCase()
{
    int ret;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());

    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void CoDeployModify::TearDownTestCase()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
}

void CoDeployModify::SetUp()
{
    int ret;

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh defaultTablespaceMaxSize=64");
    system("${TEST_HOME}/tools/start.sh");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void CoDeployModify::TearDown()
{
    AW_CHECK_LOG_END();

    int ret;

    // 删除V5表
    TestDropLabel(g_stmt, "Vertex_01");

    // 关闭连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 006.同一客户端分别操作V5表和V1 DB和表，执行V1表压缩，使用阶段二接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeployModify, V1Com_037_CoDeploy_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/tbl64BWithIndex1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 表压缩
    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(ulDbId, usRelId, i, false);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(ulDbId, usRelId, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(ulDbId, usRelId, i, false);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DeleteTbl64B(ulDbId, usRelId, i, false);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(ulDbId, usRelId, insertNum / 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestTPC_CompressTable(ulDbId, usRelId, 177, 177);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = InsertTbl64B(ulDbId, usRelId, i, false);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(ulDbId, usRelId, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    // 查询
    ret = TestTPC_GetRelActRec(ulDbId, usRelId, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除DB
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 016.同一客户端分别操作V5表和V1 DB和表，执行V1表压缩，使用阶段一接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeployModify, V1Com_037_CoDeploy_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/tbl64BWithIndex1.json", &usRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 表压缩
    int pageNum = 4;
    int insertNum = REC_NUM_PER_PAGE * pageNum;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = DBInsertTbl64B(ulDbId, usRelId, i, false);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestDBS_GetRelActRec(ulDbId, usRelId, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = DBSelectTbl64B(ulDbId, usRelId, i, false);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DBDeleteTbl64B(ulDbId, usRelId, i, false);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestDBS_GetRelActRec(ulDbId, usRelId, insertNum / 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    WaitForPurgeDelete();
    // 表压缩
    ret = TestDB_CompressTable(ulDbId, usRelId, 177, 177);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DBInsertTbl64B(ulDbId, usRelId, i, false);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestDBS_GetRelActRec(ulDbId, usRelId, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    // 查询
    ret = TestDBS_GetRelActRec(ulDbId, usRelId, insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除DB
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

class CoDeployNoInit : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void CoDeployNoInit::SetUpTestCase()
{}

void CoDeployNoInit::TearDownTestCase()
{}

void CoDeployNoInit::SetUp()
{
    int ret;

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh defaultTablespaceMaxSize=64");
    system("${TEST_HOME}/tools/start.sh");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void CoDeployNoInit::TearDown()
{
    AW_CHECK_LOG_END();

    int ret;

    // 删除V5表
    TestDropLabel(g_stmt, "Vertex_01");

    // 关闭连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 007.V1不初始化，同一客户端分别操作V5表和V1 DB和表，执行DML操作和扫描查询操作，使用阶段二接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeployNoInit, V1Com_037_CoDeploy_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 017.V1不初始化，同一客户端分别操作V5表和V1 DB和表，执行DML操作和扫描查询操作，使用阶段一接口，
                V5服务重启，重启后查询表和数据依然存在，V1表不受V5重启影响，V1表和数据也存在
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeployNoInit, V1Com_037_CoDeploy_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmboot操作 start**************************/
    // 写入数据
    uint32_t times = 100;
    uint32_t initValue = 0;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 扫描V5表
    uint32_t expectNum = 0;
    TestScanLabel(g_stmt, "Vertex_01", expectNum);
    /*********************warmboot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

class CoDeployCompact : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void CoDeployCompact::SetUpTestCase()
{
    int ret;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
}

void CoDeployCompact::TearDownTestCase()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
}

void CoDeployCompact::SetUp()
{
    int ret;

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh defaultTablespaceMaxSize=64");
    system("${TEST_HOME}/tools/modifyCfg.sh enableClusterHash=0");
    system("${TEST_HOME}/tools/modifyCfg.sh memCompactEnable=1");
    system("${TEST_HOME}/tools/modifyCfg.sh minFragmentationMemThreshold=1");
    system("${TEST_HOME}/tools/start.sh");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isCluster = false;

    // 创建V5表
    TestCreateLabel(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void CoDeployCompact::TearDown()
{
    AW_CHECK_LOG_END();

    int ret;

    // 删除V5表
    TestDropLabel(g_stmt, "Vertex_01");

    TestDropLabel(g_stmt, "var_vertex");

    // 关闭连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 009.同一客户端分别操作V5表和V1 DB和表，V5执行warmboot缩容操作，V1执行DML操作和扫描查询操作，
                使用阶段二接口，V1表不受V5影响业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeployCompact, V1Com_037_CoDeploy_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmbot操作 start**************************/
    const char *labelName = "var_vertex";
    uint32_t startIndex = 0;
    uint32_t insertCount = 10000;
    ret = CommonInsertVarTb(g_stmt, labelName, startIndex, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rmStartIndex = 1000;
    // 先更新为跳转行
    ret = NorUpdateToSrc(g_stmt, labelName, rmStartIndex, insertCount - rmStartIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 从rmStartIndex 开始异步删除normal行
    ret = AsyncTbOperate(g_conn, g_stmt, labelName, rmStartIndex, insertCount - rmStartIndex, T_RSM_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(g_thread[g_curUsedThCount - 1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(5);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据校验
    ret = LookUpVertex(g_stmt, 0, 999, true, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LookUpSrcVertex(g_stmt, rmStartIndex, g_triggerPoint + 1, false, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*********************warmbot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 019.同一客户端分别操作V5表和V1 DB和表，V5执行warmboot缩容操作，
                V1执行DML操作和扫描查询操作，使用阶段一接口，V1表不受V5影响业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeployCompact, V1Com_037_CoDeploy_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmbot操作 start**************************/
    const char *labelName = "var_vertex";
    uint32_t startIndex = 0;
    uint32_t insertCount = 10000;
    ret = CommonInsertVarTb(g_stmt, labelName, startIndex, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rmStartIndex = 1000;
    // 先更新为跳转行
    ret = NorUpdateToSrc(g_stmt, labelName, rmStartIndex, insertCount - rmStartIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 从rmStartIndex 开始异步删除normal行
    ret = AsyncTbOperate(g_conn, g_stmt, labelName, rmStartIndex, insertCount - rmStartIndex, T_RSM_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(g_thread[g_curUsedThCount - 1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(5);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建V5表
    TestCreateLabel(g_stmt);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据校验
    ret = LookUpVertex(g_stmt, 0, 999, true, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LookUpSrcVertex(g_stmt, rmStartIndex, g_triggerPoint + 1, false, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*********************warmbot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}


class CoDeployMigration : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void CoDeployMigration::SetUpTestCase()
{
    int ret;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
}

void CoDeployMigration::TearDownTestCase()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
}

void CoDeployMigration::SetUp()
{
    int ret;

    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=1\"");     // 开启后台内存整理缩容功能
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableReleaseDevice=1\"");  // 设置还表空间device给OS
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isUseRsm=1\"");             // 开启保留内存
    system("sh $TEST_HOME/tools/modifyCfg.sh \"RsmKeyRange=0,2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"deviceSize=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTablespaceMaxSize=8\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"RsmBlockSize=32\"");
    system("${TEST_HOME}/tools/start.sh");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isCluster = false;

    // 创建V5表
    TestCreateLabel(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void CoDeployMigration::TearDown()
{
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AW_CHECK_LOG_END();

    int ret;

    // 删除V5表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "var_vertex");

    // 删表空间
    ret = GmcDropTablespace(g_stmt, "tspName1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 关闭连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 010.同一客户端分别操作V5表和V1 DB和表，V5执行warmboot页搬迁操作，V1执行DML操作和扫描查询操作，
                使用阶段二接口，V1表不受V5影响业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeployMigration, V1Com_037_CoDeploy_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestTPCCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmbot操作 start**************************/
    ret = TestGetCurDevCnt();
    AW_MACRO_EXPECT_EQ_INT(2, ret);

    // 写数据
    uint32_t startNum = 0;
    uint32_t endNum = 1000000;
    ret = TestInsertVertex(g_stmt, startNum, endNum, "Vertex_02");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    ret = TestGetCurDevCnt();
    AW_MACRO_EXPECT_EQ_INT(9, ret);

    // 获取写入数据的量
    uint64_t count = 0;
    ret = GmcPrepareStmtByLabelName(g_stmt, "Vertex_02", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    AW_FUN_Log(LOG_INFO, "count:%d", count);

    // 删除大半数据，触发页搬迁
    uint64_t delCnt = count / 4;
    ret = TestDeleteVertex(g_stmt, startNum, count - delCnt, "Vertex_02");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    TestCheckCompress(g_stmt);

    ret = TestGetCurDevCnt();
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    /*********************warmbot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestSelectValue(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

/*****************************************************************************
 Description  : 020.同一客户端分别操作V5表和V1 DB和表，V5执行warmboot页搬迁操作，
                V1执行DML操作和扫描查询操作，使用阶段一接口，V1表不受V5影响业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(CoDeployMigration, V1Com_037_CoDeploy_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t ulDbId;

    // 创建db
    TestDBCreateDB("DB1", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label1.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DML操作
    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        TestInsertValue(recBuf, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    uint32_t recNum = 0;
    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /*********************warmbot操作 start**************************/
    ret = TestGetCurDevCnt();
    AW_MACRO_EXPECT_EQ_INT(2, ret);

    // 写数据
    uint32_t startNum = 0;
    uint32_t endNum = 1000000;
    ret = TestInsertVertex(g_stmt, startNum, endNum, "Vertex_02");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    ret = TestGetCurDevCnt();
    AW_MACRO_EXPECT_EQ_INT(9, ret);

    // 获取写入数据的量
    uint64_t count = 0;
    ret = GmcPrepareStmtByLabelName(g_stmt, "Vertex_02", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    AW_FUN_Log(LOG_INFO, "count:%d", count);

    // 删除大半数据，触发页搬迁
    uint64_t delCnt = count / 4;
    ret = TestDeleteVertex(g_stmt, startNum, count - delCnt, "Vertex_02");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    TestCheckCompress(g_stmt);

    ret = TestGetCurDevCnt();
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    /*********************warmbot操作 end**************************/

    for (int i = 0; i < insertNum; i++) {
        TestDBSelectValue(ulDbId, usRelId, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删除DB
    TEST_V1_FREE(recBuf);
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB1", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
