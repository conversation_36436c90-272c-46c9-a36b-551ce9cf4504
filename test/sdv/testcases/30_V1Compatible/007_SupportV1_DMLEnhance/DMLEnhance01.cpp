/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: DMLEnhance01.cpp
 * Description: V1 Dml Enhance
 * Create: ywx1157510
 */
#include "DMLEnhance.h"

class DMLEnhance : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    }
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DMLEnhance::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void DMLEnhance::TearDown()
{
    AW_CHECK_LOG_END();
}
// 001.条件涉及索引，部分更新条件为DB_OP_EQUAL
TEST_F(DMLEnhance, V1Com_007_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_EQUAL);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
    }

    for (uint32_t i = 0; i < updateNum; i++) {
        objIn1[i].f0 = i + 10000;
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.条件涉及索引，部分更新条件为DB_OP_NOTEQUAL
TEST_F(DMLEnhance, V1Com_007_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;
    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 2;
    for (int i = 0; i < updateNum; i++) {
        AW_FUN_Log(LOG_STEP, "updateNum is %d  i = %d.", updateNum, i);
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTEQUAL);  // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(999, recNum);
    }
    for (uint32_t i = 0; i < recordNum; i++) {
        objIn1[1].f0 = 10000;
        objIn1[i].f0 = 10001;
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 2; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (i == 0) {
                V1_AW_MACRO_ASSERT_EQ_INT(999, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }
    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.条件涉及索引，部分更新条件为DB_OP_LESS
TEST_F(DMLEnhance, V1Com_007_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 2000;
    }
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            }
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP, "8888888888888888888888============i %d===============8888888888888888888888888.", i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        }
        AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 2000) {
                    continue;
                }
                objIn1[i].f0 = arry[k] + 10000;
            }
        }
    }
    // 获取读数据的条数
    uint32_t selectValue[dataNum] = {0};
    for (uint32_t k = 0; k < dataNum; k++) {
        selectValue[k] = 0;
    }
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f0 == objIn1[k].f0) {
                selectValue[i]++;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(selectValue[i], ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.条件涉及索引，部分更新条件为DB_OP_LESSEQUAL
TEST_F(DMLEnhance, V1Com_007_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LESSEQUAL);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 >= num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            }
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP, "8888888888888888888888============i %d===============8888888888888888888888888.", i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        }
        AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 0) {
                    continue;
                }
                objIn1[i].f0 = arry[k] + 10000;
            }
        }
    }
    // 获取读数据的条数
    uint32_t selectValue[dataNum] = {0};
    for (uint32_t k = 0; k < dataNum; k++) {
        selectValue[k] = 0;
    }
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f0 == objIn1[k].f0) {
                selectValue[i]++;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(selectValue[i], ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.条件涉及索引，部分更新条件为DB_OP_LARGER
TEST_F(DMLEnhance, V1Com_007_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LARGER);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t i = 0; i < recordNum; i++) {
            uint32_t valueA = objIn1[0].f1;
            uint32_t valueB = objIn1[i].f1;
            GetANum(&valueA);
            GetANum(&valueB);
            if (objIn1[0].f1 < objIn1[i].f1) {
                if (valueA <= valueB) {
                    objIn1[i].f0 = 10000;
                    expectRecNum++;
                }
            }
        }

        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (expectRecNum != recNum) {
            StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
        }
        V1_AW_MACRO_EXPECT_EQ_INT(expectRecNum, recNum);
    }

    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_EXPECT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.条件涉及索引，部分更新条件为DB_OP_LARGEREQUAL
TEST_F(DMLEnhance, V1Com_007_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    uint32_t expectRecNum1 = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LARGEREQUAL);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            if (num[j] == objIn1[i].f1) {
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }
        for (uint32_t j = 0; j < recordNum; j++) {
            //  比较条件3
            if (num[expectRecNum] < objIn1[j].f1) {
                uint32_t valueA = objIn1[j].f1;
                uint32_t valueB = num[expectRecNum];
                GetANum(&valueA);
                GetANum(&valueB);
                if (valueA < valueB) {
                    expectRecNum1 += 1;
                }
            }
        }
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - expectRecNum1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] <= objIn1[i].f1) {
            uint32_t valueA = num[expectRecNum];
            uint32_t valueB = objIn1[i].f1;
            GetANum(&valueA);
            GetANum(&valueB);
            if (valueA <= valueB) {
                objIn1[i].f0 = 10000;
            }
        }
    }

    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - expectRecNum1, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.条件涉及索引，部分更新条件为DB_OP_HAVEPREFIX
TEST_F(DMLEnhance, V1Com_007_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_HAVEPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.条件涉及索引，部分更新条件为DB_OP_NOPREFIX
TEST_F(DMLEnhance, V1Com_007_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        }
        expectRecNum++;
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.条件涉及索引，部分更新条件为DB_OP_LIKE
TEST_F(DMLEnhance, V1Com_007_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_LIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.条件涉及索引，部分更新条件为DB_OP_NOTLIKE
TEST_F(DMLEnhance, V1Com_007_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        } else {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTLIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.条件涉及索引，部分更新条件为DB_OP_PREFIX
TEST_F(DMLEnhance, V1Com_007_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.条件涉及索引，部分更新条件为DB_OP_PREFIX12
TEST_F(DMLEnhance, V1Com_007_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.条件涉及索引，部分更新条件为DB_OP_PREFIX21
TEST_F(DMLEnhance, V1Com_007_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] == 11) {
            expectRecNum++;
        } else if (num[i] == 111) {
            expectRecNum++;
        } else if (num[i] == 1111) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 == 11) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 == 111) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 == 1111) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.条件涉及索引，部分更新条件为DB_OP_MAX_PREFIX12
TEST_F(DMLEnhance, V1Com_007_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 1;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (maxNumValue == 0) {
        expectRecNum = 0;
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.条件涉及索引，部分更新条件为DB_OP_MAX_PREFIX21
TEST_F(DMLEnhance, V1Com_007_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.条件涉及索引，部分更新条件为 DB_OP_MIN_PREFIX12
TEST_F(DMLEnhance, V1Com_007_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (minNumValue == 10000) {
        expectRecNum = 0;
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.条件涉及索引，部分更新条件为 DB_OP_MIN_PREFIX21
TEST_F(DMLEnhance, V1Com_007_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
    }
    if (minNumValue != 10000) {
        expectRecNum = 1;
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.条件涉及索引，部分更新条件为 DB_OP_MAX_LESS
TEST_F(DMLEnhance, V1Com_007_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    uint32_t maxLessValue = 10000;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            }
        }
        arry[i] = maxLessValue;
        AW_FUN_Log(LOG_STEP, "arry[i] = %d.  i is %d", arry[i], i);
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (maxLessValue == objIn1[i].f1 || maxLessValue == 10000) {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
        maxLessValue = 10000;
    }

    // 读数据的预期
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < 500; k++) {
            if (objIn1[i].f1 == arry[k]) {
                objIn1[i].f0 = k + 10000;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d  arry[k] %d", Indexkey, i, objIn1[i].f1,
                objIn1[i].f0, arry[i]);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.条件涉及索引，部分更新条件为 DB_OP_MAX_LESS_EQUAL
TEST_F(DMLEnhance, V1Com_007_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    uint32_t maxLessValue = 10000;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_LESS_EQUAL);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 >= num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue <= num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            }
        }
        arry[i] = maxLessValue;
        AW_FUN_Log(LOG_STEP, "arry[i] = %d.  i is %d", arry[i], i);
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (maxLessValue == 10000) {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
        maxLessValue = 10000;
    }

    // 读数据的预期
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < 500; k++) {
            if (objIn1[i].f1 == arry[k]) {
                objIn1[i].f0 = k + 10000;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d  arry[k] %d", Indexkey, i, objIn1[i].f1,
                objIn1[i].f0, arry[i]);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.条件涉及索引，部分更新条件为 DB_OP_MIN_LARGER(大于条件的最小值)
TEST_F(DMLEnhance, V1Com_007_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_LARGER);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            if ((num[j] > objIn1[i].f1) && (valueA >= valueB)) {
                // 比较string的数值大小
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (expectRecNum == 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (expectRecNum == 0) {
            break;
        }
        if (num[expectRecNum] == objIn1[i].f1) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.条件涉及索引，部分更新条件为 DB_OP_MIN_LARGER_EQUAL
TEST_F(DMLEnhance, V1Com_007_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_LARGER_EQUAL);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            if (num[j] == objIn1[i].f1) {
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] == objIn1[i].f1) {
            objIn1[i].f0 = 10000;
        }
    }

    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.条件涉及索引，部分更新条件为 DB_OP_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue || maxNumValue != 0) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.条件涉及索引，部分更新条件为 DB_OP_MAX_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.条件涉及索引，全量更新条件为DB_OP_EQUAL
TEST_F(DMLEnhance, V1Com_007_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_EQUAL);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        uint32_t updateVlaue = i + 10000;
        SetArrayValueWhichExpect(objIn1, recordNum, updateVlaue, updateVlaue, i);
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.条件涉及索引，全量更新条件为DB_OP_NOTEQUAL
TEST_F(DMLEnhance, V1Com_007_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;
    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 2;
    for (int i = 0; i < updateNum; i++) {
        AW_FUN_Log(LOG_STEP, "updateNum is %d  i = %d.", updateNum, i);
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTEQUAL);  // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        if (i == 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(999, recNum);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(1000, recNum);
        }
    }
    for (uint32_t i = 0; i < recordNum; i++) {
        uint32_t updateVlaue = 1 + 10000;
        SetArrayValueWhichExpect(objIn1, recordNum, updateVlaue, updateVlaue, i);
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 2; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1000, ulRecNum);
        }
    }
    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.条件涉及索引，全量更新条件为DB_OP_LESS
TEST_F(DMLEnhance, V1Com_007_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 全量更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 2000;
    }
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            if (arry[j] > 9999) {
                continue;
            }
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = 10000 + i;
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = 10000 + i;
                }
            }
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP, "8888888888888888888888============i %d===============8888888888888888888888888.", i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        }
        AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d objIn1[i].f0：%d expectRecNum is %d.", objIn1[i].f1, objIn1[i].f0,
            expectRecNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 2000) {
                    AW_FUN_Log(LOG_STEP, "objIn1[i].f0：%d objIn1[i].f1：%d objIn1[i].f3：%d  arry[k] is %d i is %d.",
                        objIn1[i].f0, objIn1[i].f1, objIn1[i].f3, arry[k], i);
                    break;
                } else {
                    SetArrayValueWhichExpect(objIn1, recordNum, arry[k], arry[k], i);
                }
            }
        }
    }
    // 获取读数据的条数
    uint32_t selectValue[dataNum] = {0};
    for (uint32_t k = 0; k < dataNum; k++) {
        selectValue[k] = 0;
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f0 == objIn1[k].f0) {
                selectValue[i]++;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {

            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (ret) {
                AW_FUN_Log(
                    LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            }
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(selectValue[i], ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.条件涉及索引，全量更新条件为DB_OP_LESSEQUAL
TEST_F(DMLEnhance, V1Com_007_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 全量更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LESSEQUAL);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            if (arry[j] > 9999) {
                continue;
            }
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = 10000 + i;
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 >= num[j]) {
                    expectRecNum++;
                    arry[j] = 10000 + i;
                }
            }
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP, "8888888888888888888888============i %d===============8888888888888888888888888.", i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        }
        AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d objIn1[i].f0：%d expectRecNum is %d.", objIn1[i].f1, objIn1[i].f0,
            expectRecNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 0) {
                    AW_FUN_Log(LOG_STEP, "objIn1[i].f0：%d objIn1[i].f1：%d objIn1[i].f3：%d  arry[k] is %d i is %d.",
                        objIn1[i].f0, objIn1[i].f1, objIn1[i].f3, arry[k], i);
                    break;
                } else {
                    SetArrayValueWhichExpect(objIn1, recordNum, arry[k], arry[k], i);
                }
            }
        }
    }
    // 获取读数据的条数
    uint32_t selectValue[dataNum] = {0};
    for (uint32_t k = 0; k < dataNum; k++) {
        selectValue[k] = 0;
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f0 == objIn1[k].f0) {
                selectValue[i]++;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {

            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (ret) {
                AW_FUN_Log(
                    LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            }
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(selectValue[i], ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.条件涉及索引，全量更新条件为DB_OP_LARGER
TEST_F(DMLEnhance, V1Com_007_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 全量更新数据
    uint32_t expectRecNum = 0;
    uint32_t expectRecNum1 = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LARGER);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            //  比较条件1，2
            if (num[j] == objIn1[i].f1) {

                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }
        for (uint32_t j = 0; j < recordNum; j++) {
            //  比较条件3
            if (num[expectRecNum] < objIn1[j].f1) {
                uint32_t valueA = objIn1[j].f1;
                uint32_t valueB = num[expectRecNum];
                GetANum(&valueA);
                GetANum(&valueB);
                // 这里统计的是不更新的情况(数字小于但是字符串相等时条件满足)
                if (valueA < valueB) {
                    expectRecNum1 += 1;
                }
            }

            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        AW_FUN_Log(LOG_STEP, "recordNum is %d.expectRecNum is %d.expectRecNum1 is %d.", recordNum, expectRecNum,
            expectRecNum1);
        StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
        V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - 1 - expectRecNum1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] < objIn1[i].f1) {
            uint32_t valueA = num[expectRecNum];
            uint32_t valueB = objIn1[i].f1;
            GetANum(&valueA);
            GetANum(&valueB);
            if (valueA <= valueB) {
                objIn1[i].f0 = 10000;
                SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
            }
        }
    }

    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - 1 - expectRecNum1, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.条件涉及索引，全量更新条件为DB_OP_LARGEREQUAL
TEST_F(DMLEnhance, V1Com_007_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 全量更新数据
    uint32_t expectRecNum = 0;
    uint32_t expectRecNum1 = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LARGEREQUAL);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            //  比较条件1，2
            if (num[j] == objIn1[i].f1) {

                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }
        for (uint32_t j = 0; j < recordNum; j++) {
            //  比较条件3
            if (num[expectRecNum] < objIn1[j].f1) {
                uint32_t valueA = objIn1[j].f1;
                uint32_t valueB = num[expectRecNum];
                GetANum(&valueA);
                GetANum(&valueB);
                if (valueA < valueB) {
                    expectRecNum1 += 1;
                }
            }

            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        AW_FUN_Log(LOG_STEP, "recordNum is %d.expectRecNum is %d.expectRecNum1 is %d.", recordNum, expectRecNum,
            expectRecNum1);
        StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
        V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - expectRecNum1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] <= objIn1[i].f1) {
            uint32_t valueA = num[expectRecNum];
            uint32_t valueB = objIn1[i].f1;
            GetANum(&valueA);
            GetANum(&valueB);
            if (valueA <= valueB) {
                objIn1[i].f0 = 10000;
                SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
            }
        }
    }

    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - expectRecNum1, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.条件涉及索引，全量更新条件为DB_OP_HAVEPREFIX
TEST_F(DMLEnhance, V1Com_007_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_HAVEPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.条件涉及索引，全量更新条件为DB_OP_NOPREFIX
TEST_F(DMLEnhance, V1Com_007_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        }
        expectRecNum++;
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.条件涉及索引，全量更新条件为DB_OP_LIKE
TEST_F(DMLEnhance, V1Com_007_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_LIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.条件涉及索引，全量更新条件为DB_OP_NOTLIKE
TEST_F(DMLEnhance, V1Com_007_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        } else {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTLIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.条件涉及索引，全量更新条件为DB_OP_PREFIX
TEST_F(DMLEnhance, V1Com_007_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.条件涉及索引，全量更新条件为DB_OP_PREFIX12
TEST_F(DMLEnhance, V1Com_007_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            AW_FUN_Log(LOG_STEP, "i = %d objIn1[i].f0 = %d", i, objIn1[i].f0);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_EXPECT_EQ_INT(expectRecNum, ulRecNum);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            } else {
                V1_AW_MACRO_EXPECT_EQ_INT(1, ulRecNum);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.条件涉及索引，全量更新条件为DB_OP_PREFIX21
TEST_F(DMLEnhance, V1Com_007_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] == 11) {
            expectRecNum++;
        } else if (num[i] == 111) {
            expectRecNum++;
        } else if (num[i] == 1111) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 == 11) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 == 111) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 == 1111) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.条件涉及索引，全量更新条件为DB_OP_MAX_PREFIX12
TEST_F(DMLEnhance, V1Com_007_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 1;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (maxNumValue == 0) {
        expectRecNum = 0;
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.条件涉及索引，全量更新条件为DB_OP_MAX_PREFIX21
TEST_F(DMLEnhance, V1Com_007_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 全量更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.条件涉及索引，全量更新条件为 DB_OP_MIN_PREFIX12
TEST_F(DMLEnhance, V1Com_007_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (minNumValue < 10 && minNumValue >= 1) {
            minNumValue *= 1000;
        }
        if (minNumValue < 100 && minNumValue >= 10) {
            minNumValue *= 100;
        }
        if (minNumValue < 1000 && minNumValue >= 100) {
            minNumValue *= 10;
        }

        if (num[i] < 10 && num[i] >= 1) {
            num[i] *= 1000;
        }
        if (num[i] < 100 && num[i] >= 10) {
            num[i] *= 100;
        }
        if (num[i] < 1000 && num[i] >= 100) {
            num[i] *= 10;
        }
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (minNumValue == 10000) {
        expectRecNum = 0;
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f0 == 0) {
                continue;
            }
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.条件涉及索引，全量更新条件为 DB_OP_MIN_PREFIX21
TEST_F(DMLEnhance, V1Com_007_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
    }
    if (minNumValue != 10000) {
        expectRecNum = 1;
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.条件涉及索引，全量更新条件为 DB_OP_MAX_LESS
TEST_F(DMLEnhance, V1Com_007_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 全量更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    uint32_t maxLessValue = 99999;
    for (int i = 0; i < 1; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue < num[j]) {
                        maxLessValue = num[j];
                    }
                    arry[j] = 10000 + i;
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue < num[j]) {
                        maxLessValue = num[j];
                    }
                    arry[j] = 10000 + i;
                }
            }
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP, "8888888888888888888888============i %d===============8888888888888888888888888.", i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        }
        AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d objIn1[i].f0：%d expectRecNum is %d.", objIn1[i].f1, objIn1[i].f0,
            expectRecNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 0) {
                    AW_FUN_Log(LOG_STEP, "objIn1[i].f0：%d objIn1[i].f1：%d objIn1[i].f3：%d  arry[k] is %d i is %d.",
                        objIn1[i].f0, objIn1[i].f1, objIn1[i].f3, arry[k], i);
                    break;
                } else {
                    arry[k] = 10000;
                    SetArrayValueWhichExpect(objIn1, recordNum, arry[k], arry[k], i);
                }
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {

            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (ret) {
                AW_FUN_Log(
                    LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            }
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.条件涉及索引，全量更新条件为 DB_OP_MAX_LESS_EQUAL
TEST_F(DMLEnhance, V1Com_007_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 全量更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    uint32_t maxLessValue = 10000;
    for (int i = 0; i < 1; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            }
        }
        arry[i] = maxLessValue;
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        if (maxLessValue == objIn1[i].f1 || maxLessValue == 10000) {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
    }

    // 读数据的预期
    for (uint32_t i = 0; i < dataNum; i++) {
        if (arry[0] == 0) {
            AW_FUN_Log(LOG_STEP, "objIn1[i].f0：%d objIn1[i].f1：%d objIn1[i].f3：%d  arry[0] is %d i is %d.",
                objIn1[i].f0, objIn1[i].f1, objIn1[i].f3, arry[0], i);
            break;
        } else {
            if (objIn1[i].f1 == arry[0]) {
                arry[0] = 10000;
                SetArrayValueWhichExpect(objIn1, recordNum, arry[0], arry[0], i);
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {

            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (ret) {
                AW_FUN_Log(
                    LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            }
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.条件涉及索引，全量更新条件为 DB_OP_MIN_LARGER
TEST_F(DMLEnhance, V1Com_007_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 全量更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_LARGER);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            if ((num[j] > objIn1[i].f1) && (valueA >= valueB)) {
                // 比较string的数值大小
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (expectRecNum == 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (expectRecNum == 0) {
            break;
        }
        if (num[expectRecNum] == objIn1[i].f1) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.条件涉及索引，全量更新条件为 DB_OP_MIN_LARGER_EQUAL
TEST_F(DMLEnhance, V1Com_007_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 全量更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_LARGER_EQUAL);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            if (num[j] == objIn1[i].f1) {
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] == objIn1[i].f1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }

    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.条件涉及索引，全量更新条件为 DB_OP_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 全量更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue || maxNumValue != 0) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.条件涉及索引，全量更新条件为 DB_OP_MAX_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_003.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 全量更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.条件涉及索引，删除条件为DB_OP_EQUAL
TEST_F(DMLEnhance, V1Com_007_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_EQUAL);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
    }

    for (uint32_t i = 0; i < updateNum; i++) {
        objIn1[i].f1 = i + 10000;
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.条件涉及索引，删除条件为DB_OP_NOTEQUAL
TEST_F(DMLEnhance, V1Com_007_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;
    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 2;
    for (int i = 0; i < updateNum; i++) {
        AW_FUN_Log(LOG_STEP, "updateNum is %d  i = %d.", updateNum, i);
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTEQUAL);  // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        if (i == 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(999, recNum);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 2; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
        }
    }
    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.条件涉及索引，删除条件为DB_OP_LESS
TEST_F(DMLEnhance, V1Com_007_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 删除数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 10000;
    }
    uint32_t maxValue = 0;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (arry[j] == 10000) {
                        expectRecNum++;
                        arry[j] = 1;  // 表示已被删除
                    }
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    if (arry[j] == 10000) {
                        expectRecNum++;
                        arry[j] = 1;  // 表示已被删除
                    }
                }
            }
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP,
                "8888888888888888888888=====expectRecNum != recNum=======i %d===============8888888888888888888888888.",
                i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
            StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
        }
        AW_FUN_Log(
            LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d maxValue is %d.", objIn1[i].f1, expectRecNum, maxValue);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 10000) {
                    continue;
                }
                objIn1[i].f1 = 10000;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.条件涉及索引，删除条件为DB_OP_LESSEQUAL
TEST_F(DMLEnhance, V1Com_007_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 删除数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 10000;
    }
    uint32_t maxValue = 0;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LESSEQUAL);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 >= num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            }
        }
        if (maxValue < expectRecNum) {
            uint32_t valueX = expectRecNum;
            expectRecNum -= maxValue;
            maxValue = valueX;
        } else {
            expectRecNum = 0;
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP, "8888888888888888888888============i %d===============8888888888888888888888888.", i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        }
        AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 10000) {
                    continue;
                }
                objIn1[i].f1 = arry[k] + 10000;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.条件涉及索引，删除条件为DB_OP_LARGER
TEST_F(DMLEnhance, V1Com_007_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 删除数据
    uint32_t expectRecNum = 0;
    uint32_t expectRecNum1 = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LARGER);  // 设置条件

        // 比较f1的数值大小确认当前条件能够删除的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            if (num[j] == objIn1[i].f1) {
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        for (uint32_t j = 0; j < recordNum; j++) {
            //  比较条件3
            if (num[expectRecNum] < objIn1[j].f1) {
                uint32_t valueA = objIn1[j].f1;
                uint32_t valueB = num[expectRecNum];
                GetANum(&valueA);
                GetANum(&valueB);
                if (valueA < valueB) {
                    expectRecNum1 += 1;
                }
            }

            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - expectRecNum1 - 1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] < objIn1[i].f1) {
            uint32_t valueA = num[expectRecNum];
            uint32_t valueB = objIn1[i].f1;
            GetANum(&valueA);
            GetANum(&valueB);
            if (valueA <= valueB) {
                objIn1[i].f1 = 10000;
            }
        }
    }

    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.条件涉及索引，删除条件为DB_OP_LARGEREQUAL
TEST_F(DMLEnhance, V1Com_007_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 删除数据
    uint32_t expectRecNum = 0;
    uint32_t expectRecNum1 = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LARGEREQUAL);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            if (num[j] == objIn1[i].f1) {
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }
        for (uint32_t j = 0; j < recordNum; j++) {
            //  比较条件3
            if (num[expectRecNum] < objIn1[j].f1) {
                uint32_t valueA = objIn1[j].f1;
                uint32_t valueB = num[expectRecNum];
                GetANum(&valueA);
                GetANum(&valueB);
                if (valueA < valueB) {
                    expectRecNum1 += 1;
                }
            }

            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - expectRecNum1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] <= objIn1[i].f1) {
            uint32_t valueA = num[expectRecNum];
            uint32_t valueB = objIn1[i].f1;
            GetANum(&valueA);
            GetANum(&valueB);
            if (valueA <= valueB) {
                objIn1[i].f1 = 10000;
            }
        }
    }

    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.条件涉及索引，删除条件为DB_OP_HAVEPREFIX
TEST_F(DMLEnhance, V1Com_007_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_HAVEPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.条件涉及索引，删除条件为DB_OP_NOPREFIX
TEST_F(DMLEnhance, V1Com_007_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        }
        expectRecNum++;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.条件涉及索引，删除条件为DB_OP_LIKE
TEST_F(DMLEnhance, V1Com_007_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_LIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056.条件涉及索引，删除条件为DB_OP_NOTLIKE
TEST_F(DMLEnhance, V1Com_007_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        } else {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTLIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.条件涉及索引，删除条件为DB_OP_PREFIX
TEST_F(DMLEnhance, V1Com_007_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.条件涉及索引，删除条件为DB_OP_PREFIX12
TEST_F(DMLEnhance, V1Com_007_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059.条件涉及索引，删除条件为DB_OP_PREFIX21
TEST_F(DMLEnhance, V1Com_007_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] == 11) {
            expectRecNum++;
        } else if (num[i] == 111) {
            expectRecNum++;
        } else if (num[i] == 1111) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 == 11) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 == 111) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 == 1111) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060.条件涉及索引，删除条件为DB_OP_MAX_PREFIX12
TEST_F(DMLEnhance, V1Com_007_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 1;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (maxNumValue == 0) {
        expectRecNum = 0;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061.条件涉及索引，删除条件为DB_OP_MAX_PREFIX21
TEST_F(DMLEnhance, V1Com_007_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062.条件涉及索引，删除条件为 DB_OP_MIN_PREFIX12
TEST_F(DMLEnhance, V1Com_007_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (minNumValue == 10000) {
        expectRecNum = 0;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063.条件涉及索引，删除条件为 DB_OP_MIN_PREFIX21
TEST_F(DMLEnhance, V1Com_007_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
    }
    if (minNumValue != 10000) {
        expectRecNum = 1;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064.条件涉及索引，删除条件为 DB_OP_MAX_LESS
TEST_F(DMLEnhance, V1Com_007_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 删除数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    uint32_t maxLessValue = 10000;
    bool isDelete = false;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            isDelete = false;
            for (uint32_t g = 0; g < i; g++) {
                if (valueA == arry[g]) {
                    isDelete = true;
                    break;
                }
            }
            if (isDelete) {
                continue;
            }
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            }
        }
        arry[i] = maxLessValue;
        AW_FUN_Log(LOG_STEP, "arry[i] = %d.  i is %d", arry[i], i);
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        if (maxLessValue == objIn1[i].f1 || maxLessValue == 10000) {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            for (uint32_t g = 0; g < i; g++) {
                if (maxLessValue == arry[g]) {
                    V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
                    break;
                }
                if (g + 1 == i) {
                    V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
                }
            }
        }
        maxLessValue = 10000;
    }

    // 读数据的预期
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < 500; k++) {
            if (objIn1[i].f1 == arry[k]) {
                objIn1[i].f1 = k + 10000;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d  arry[k] %d", Indexkey, i, objIn1[i].f1,
                objIn1[i].f0, arry[i]);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065.条件涉及索引，删除条件为 DB_OP_MAX_LESS_EQUAL
TEST_F(DMLEnhance, V1Com_007_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 删除数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    uint32_t maxLessValue = 10000;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_LESS_EQUAL);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 >= num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue <= num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            }
        }
        arry[i] = maxLessValue;
        AW_FUN_Log(LOG_STEP, "arry[i] = %d.  i is %d", arry[i], i);
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        if (maxLessValue == 10000) {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
        maxLessValue = 10000;
    }

    // 读数据的预期
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < 500; k++) {
            if (objIn1[i].f1 == arry[k]) {
                objIn1[i].f1 = k + 10000;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d  arry[k] %d", Indexkey, i, objIn1[i].f1,
                objIn1[i].f0, arry[i]);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 066.条件涉及索引，删除条件为 DB_OP_MIN_LARGER
TEST_F(DMLEnhance, V1Com_007_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 删除数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_LARGER);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            if ((num[j] > objIn1[i].f1) && (valueA >= valueB)) {
                // 比较string的数值大小
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (expectRecNum == 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (expectRecNum == 0) {
            break;
        }
        if (num[expectRecNum] == objIn1[i].f1) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067.条件涉及索引，删除条件为 DB_OP_MIN_LARGER_EQUAL
TEST_F(DMLEnhance, V1Com_007_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 删除数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_LARGER_EQUAL);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            if (num[j] == objIn1[i].f1) {
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] == objIn1[i].f1) {
            objIn1[i].f1 = 10000;
        }
    }

    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 068.条件涉及索引，删除条件为 DB_OP_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 删除数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue || maxNumValue != 0) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 069.条件涉及索引，删除条件为 DB_OP_MAX_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 删除数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}
