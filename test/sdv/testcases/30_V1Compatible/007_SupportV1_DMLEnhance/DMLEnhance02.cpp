/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: DMLEnhance02.cpp
 * Description: V1 Dml Enhance
 * Create: ywx1157510
 */
#include "DMLEnhance.h"

class DMLEnhance : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    }
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DMLEnhance::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void DMLEnhance::TearDown()
{
    AW_CHECK_LOG_END();
}

// 070.条件不涉及索引，部分更新条件为DB_OP_EQUAL
TEST_F(DMLEnhance, V1Com_007_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_EQUAL);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
    }

    for (uint32_t i = 0; i < updateNum; i++) {
        objIn1[i].f0 = i + 10000;
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 071.条件不涉及索引，部分更新条件为DB_OP_NOTEQUAL
TEST_F(DMLEnhance, V1Com_007_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;
    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 2;
    for (int i = 0; i < updateNum; i++) {
        AW_FUN_Log(LOG_STEP, "updateNum is %d  i = %d.", updateNum, i);
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTEQUAL);  // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(999, recNum);
    }
    for (uint32_t i = 0; i < recordNum; i++) {
        objIn1[1].f0 = 10000;
        objIn1[i].f0 = 10001;
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 2; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (i == 0) {
                V1_AW_MACRO_ASSERT_EQ_INT(999, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }
    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 072.条件不涉及索引，部分更新条件为DB_OP_LESS
TEST_F(DMLEnhance, V1Com_007_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 2000;
    }
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            }
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP, "8888888888888888888888============i %d===============8888888888888888888888888.", i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        }
        AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 2000) {
                    continue;
                }
                objIn1[i].f0 = arry[k] + 10000;
            }
        }
    }
    // 获取读数据的条数
    uint32_t selectValue[dataNum] = {0};
    for (uint32_t k = 0; k < dataNum; k++) {
        selectValue[k] = 0;
    }
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f0 == objIn1[k].f0) {
                selectValue[i]++;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(selectValue[i], ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 073.条件不涉及索引，部分更新条件为DB_OP_LESSEQUAL
TEST_F(DMLEnhance, V1Com_007_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LESSEQUAL);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 >= num[j]) {
                    expectRecNum++;
                    arry[j] = i;
                }
            }
        }
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        if (expectRecNum != recNum) {
            AW_FUN_Log(LOG_STEP, "8888888888888888888888============i %d===============8888888888888888888888888.", i);
            AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        }
        AW_FUN_Log(LOG_STEP, "objIn1[i].f1：%d expectRecNum is %d.", objIn1[i].f1, expectRecNum);
        expectRecNum = 0;
    }

    // 每个数值最后更新多少值记录在arry中，通过objIn1[i].f1和num[k]锁定数组下标
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f1 == num[k]) {
                if (arry[k] == 0) {
                    continue;
                }
                objIn1[i].f0 = arry[k] + 10000;
            }
        }
    }
    // 获取读数据的条数
    uint32_t selectValue[dataNum] = {0};
    for (uint32_t k = 0; k < dataNum; k++) {
        selectValue[k] = 0;
    }
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < dataNum; k++) {
            if (objIn1[i].f0 == objIn1[k].f0) {
                selectValue[i]++;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d", Indexkey, i, objIn1[i].f1, objIn1[i].f0);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(selectValue[i], ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 074.条件不涉及索引，部分更新条件为DB_OP_LARGER
TEST_F(DMLEnhance, V1Com_007_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LARGER);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t i = 0; i < recordNum; i++) {
            uint32_t valueA = objIn1[0].f1;
            uint32_t valueB = objIn1[i].f1;
            GetANum(&valueA);
            GetANum(&valueB);
            if (objIn1[0].f1 < objIn1[i].f1) {
                if (valueA <= valueB) {
                    objIn1[i].f0 = 10000;
                    expectRecNum++;
                }
            }
        }

        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (expectRecNum != recNum) {
            StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
        }
        V1_AW_MACRO_EXPECT_EQ_INT(expectRecNum, recNum);
    }
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_EXPECT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 075.条件不涉及索引，部分更新条件为DB_OP_LARGEREQUAL
TEST_F(DMLEnhance, V1Com_007_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;

    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    uint32_t expectRecNum1 = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_LARGEREQUAL);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            if (num[j] == objIn1[i].f1) {
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }
        for (uint32_t j = 0; j < recordNum; j++) {
            //  比较条件3
            if (num[expectRecNum] < objIn1[j].f1) {
                uint32_t valueA = objIn1[j].f1;
                uint32_t valueB = num[expectRecNum];
                GetANum(&valueA);
                GetANum(&valueB);
                if (valueA < valueB) {
                    expectRecNum1 += 1;
                }
            }

            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - expectRecNum1, recNum);
    }
    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] <= objIn1[i].f1) {
            uint32_t valueA = num[expectRecNum];
            uint32_t valueB = objIn1[i].f1;
            GetANum(&valueA);
            GetANum(&valueB);
            if (valueA <= valueB) {
                objIn1[i].f0 = 10000;
            }
        }
    }

    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(recordNum - expectRecNum - expectRecNum1, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 076.条件不涉及索引，部分更新条件为DB_OP_HAVEPREFIX
TEST_F(DMLEnhance, V1Com_007_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_HAVEPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 077.条件不涉及索引，部分更新条件为DB_OP_NOPREFIX
TEST_F(DMLEnhance, V1Com_007_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        }
        expectRecNum++;
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 078.条件不涉及索引，部分更新条件为DB_OP_LIKE
TEST_F(DMLEnhance, V1Com_007_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_LIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 079.条件不涉及索引，部分更新条件为DB_OP_NOTLIKE
TEST_F(DMLEnhance, V1Com_007_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        } else {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTLIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 080.条件不涉及索引，部分更新条件为DB_OP_PREFIX
TEST_F(DMLEnhance, V1Com_007_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 081.条件不涉及索引，部分更新条件为DB_OP_PREFIX12
TEST_F(DMLEnhance, V1Com_007_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 082.条件不涉及索引，部分更新条件为DB_OP_PREFIX21
TEST_F(DMLEnhance, V1Com_007_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] == 11) {
            expectRecNum++;
        } else if (num[i] == 111) {
            expectRecNum++;
        } else if (num[i] == 1111) {
            expectRecNum++;
        }
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 == 11) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 == 111) {
            objIn1[i].f0 = 10000;
        } else if (objIn1[i].f1 == 1111) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 083.条件不涉及索引，部分更新条件为DB_OP_MAX_PREFIX12
TEST_F(DMLEnhance, V1Com_007_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 1;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (maxNumValue == 0) {
        expectRecNum = 0;
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 084.条件不涉及索引，部分更新条件为DB_OP_MAX_PREFIX21
TEST_F(DMLEnhance, V1Com_007_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 085.条件不涉及索引，部分更新条件为 DB_OP_MIN_PREFIX12
TEST_F(DMLEnhance, V1Com_007_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (minNumValue == 10000) {
        expectRecNum = 0;
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 086.条件不涉及索引，部分更新条件为 DB_OP_MIN_PREFIX21
TEST_F(DMLEnhance, V1Com_007_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
    }
    if (minNumValue != 10000) {
        expectRecNum = 1;
    }
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 087.条件不涉及索引，部分更新条件为 DB_OP_MAX_LESS
TEST_F(DMLEnhance, V1Com_007_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    uint32_t maxLessValue = 10000;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_LESS);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            }
        }
        arry[i] = maxLessValue;
        AW_FUN_Log(LOG_STEP, "arry[i] = %d.  i is %d", arry[i], i);
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (maxLessValue == objIn1[i].f1 || maxLessValue == 10000) {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
        maxLessValue = 10000;
    }

    // 读数据的预期
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < 500; k++) {
            if (objIn1[i].f1 == arry[k]) {
                objIn1[i].f0 = k + 10000;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d  arry[k] %d", Indexkey, i, objIn1[i].f1,
                objIn1[i].f0, arry[i]);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 088.条件不涉及索引，部分更新条件为 DB_OP_MAX_LESS_EQUAL
TEST_F(DMLEnhance, V1Com_007_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 500;
    uint32_t arry[recordNum] = {0};
    for (uint32_t i = 0; i < recordNum; i++) {
        arry[i] = 0;
    }
    uint32_t maxLessValue = 10000;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_LESS_EQUAL);  // 设置条件
        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            // 比较f9大小
            if (valueB > valueA) {
                // 比较f1,f3
                if (objIn1[i].f1 > num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue < num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            } else if (valueB == valueA) {
                if (objIn1[i].f1 >= num[j]) {
                    if (maxLessValue == 10000) {
                        maxLessValue = num[j];
                    }
                    if (maxLessValue != 10000) {
                        uint32_t oldValue = maxLessValue;
                        GetANum(&maxLessValue);
                        if (oldValue <= num[j]) {
                            maxLessValue = num[j];
                        } else {
                            maxLessValue = oldValue;
                        }
                    }
                }
            }
        }
        arry[i] = maxLessValue;
        AW_FUN_Log(LOG_STEP, "arry[i] = %d.  i is %d", arry[i], i);
        AW_FUN_Log(LOG_STEP, "i = %d.", i);
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (maxLessValue == 10000) {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            AW_FUN_Log(LOG_STEP, "maxLessValue = %d.objIn1[i].f1 is %d", maxLessValue, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
        maxLessValue = 10000;
    }

    // 读数据的预期
    for (uint32_t i = 0; i < dataNum; i++) {
        for (uint32_t k = 0; k < 500; k++) {
            if (objIn1[i].f1 == arry[k]) {
                objIn1[i].f0 = k + 10000;
            }
        }
    }

    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            AW_FUN_Log(LOG_STEP, "Indexkey is %d  i = %d条件值：%d.f0：%d  arry[k] %d", Indexkey, i, objIn1[i].f1,
                objIn1[i].f0, arry[i]);
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 089.条件不涉及索引，部分更新条件为 DB_OP_MIN_LARGER
TEST_F(DMLEnhance, V1Com_007_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;

    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_LARGER);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        uint32_t valueA = 0;  // 表里面的数据
        uint32_t valueB = 0;
        valueB = objIn1[i].f1;
        GetANum(&valueB);
        for (uint32_t j = 0; j < recordNum; j++) {
            valueA = num[j];
            GetANum(&valueA);
            if ((num[j] > objIn1[i].f1) && (valueA >= valueB)) {
                // 比较string的数值大小
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        if (expectRecNum == 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(0, recNum);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (expectRecNum == 0) {
            break;
        }
        if (num[expectRecNum] == objIn1[i].f1) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 090.条件不涉及索引，部分更新条件为 DB_OP_MIN_LARGER_EQUAL
TEST_F(DMLEnhance, V1Com_007_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为预期1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 将随机生成的数据按照从小到大排序
    qsort(num, recordNum, sizeof(uint32_t), CmpFunc);
    // 部分更新数据
    uint32_t expectRecNum = 0;
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_LARGER_EQUAL);  // 设置条件

        // 比较f1的数值大小确认当前条件能够更新的数据条数
        uint32_t recNum = 0;
        for (uint32_t j = 0; j < recordNum; j++) {
            if (num[j] == objIn1[i].f1) {
                expectRecNum = j;
                AW_FUN_Log(LOG_STEP, "expectRecNum is %d  objIn1[i].f1 = %d.", expectRecNum, objIn1[i].f1);
                // break;
            }
            AW_FUN_Log(LOG_STEP, "num[j] is %d.", num[j]);
        }

        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
    }

    // 预期最多更新这么多条数据
    for (uint32_t i = 0; i < recordNum; i++) {
        if (num[expectRecNum] == objIn1[i].f1) {
            objIn1[i].f0 = 10000;
        }
    }

    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 091.条件不涉及索引，部分更新条件为 DB_OP_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue || maxNumValue != 0) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 092.条件不涉及索引，部分更新条件为 DB_OP_MAX_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 部分更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        UpdatePartial(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 093.条件不涉及索引，全量更新条件为DB_OP_NOTEQUAL
TEST_F(DMLEnhance, V1Com_007_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);
    g_isUniqueIndex = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;
    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 部分更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 2;
    for (int i = 0; i < updateNum; i++) {
        AW_FUN_Log(LOG_STEP, "updateNum is %d  i = %d.", updateNum, i);
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTEQUAL);  // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        if (i == 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(999, recNum);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(1000, recNum);
        }
    }
    for (uint32_t i = 0; i < recordNum; i++) {
        uint32_t updateVlaue = 1 + 10000;
        SetArrayValueWhichExpect(objIn1, recordNum, updateVlaue, updateVlaue, i);
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验部分更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 2; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1000, ulRecNum);
        }
    }
    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 094.条件不涉及索引，全量更新条件为DB_OP_HAVEPREFIX
TEST_F(DMLEnhance, V1Com_007_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    g_isUniqueIndex = 0;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_HAVEPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 095.条件不涉及索引，全量更新条件为DB_OP_NOPREFIX
TEST_F(DMLEnhance, V1Com_007_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    g_isUniqueIndex = 0;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        }
        expectRecNum++;
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 096.条件不涉及索引，全量更新条件为DB_OP_LIKE
TEST_F(DMLEnhance, V1Com_007_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    g_isUniqueIndex = 0;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_LIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 097.条件不涉及索引，全量更新条件为DB_OP_NOTLIKE
TEST_F(DMLEnhance, V1Com_007_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    g_isUniqueIndex = 0;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        } else {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTLIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 098.条件不涉及索引，全量更新条件为DB_OP_PREFIX
TEST_F(DMLEnhance, V1Com_007_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    g_isUniqueIndex = 0;
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 099.条件不涉及索引，全量更新条件为DB_OP_PREFIX12
TEST_F(DMLEnhance, V1Com_007_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    g_isUniqueIndex = 0;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 100.条件不涉及索引，全量更新条件为DB_OP_PREFIX21
TEST_F(DMLEnhance, V1Com_007_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    g_isUniqueIndex = 0;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] == 11) {
            expectRecNum++;
        } else if (num[i] == 111) {
            expectRecNum++;
        } else if (num[i] == 1111) {
            expectRecNum++;
        }
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 == 11) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 == 111) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        } else if (objIn1[i].f1 == 1111) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 101.条件不涉及索引，全量更新条件为DB_OP_MAX_PREFIX12
TEST_F(DMLEnhance, V1Com_007_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    g_isUniqueIndex = 0;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 1;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (maxNumValue == 0) {
        expectRecNum = 0;
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 102.条件不涉及索引，全量更新条件为DB_OP_MAX_PREFIX21
TEST_F(DMLEnhance, V1Com_007_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    g_isUniqueIndex = 0;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 全量更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 103.条件不涉及索引，全量更新条件为 DB_OP_MIN_PREFIX12
TEST_F(DMLEnhance, V1Com_007_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    g_isUniqueIndex = 0;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (minNumValue == 10000) {
        expectRecNum = 0;
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 104.条件不涉及索引，全量更新条件为 DB_OP_MIN_PREFIX21
TEST_F(DMLEnhance, V1Com_007_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    g_isUniqueIndex = 0;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
    }
    if (minNumValue != 10000) {
        expectRecNum = 1;
    }
    // 全量更新数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 105.条件不涉及索引，全量更新条件为 DB_OP_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);
    g_isUniqueIndex = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 全量更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue || maxNumValue != 0) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 106.条件不涉及索引，全量更新条件为 DB_OP_MAX_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);
    g_isUniqueIndex = 0;

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 全量更新数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Update(TPC_GLOBAL_CDB, ulDbId, usRelId, i, recBuf, &recNum, stRelDef.pstFldLst, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f0 = 10000;
            SetArrayValueWhichExpect(objIn1, recordNum, objIn1[i].f0, objIn1[i].f0, i);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验全量更新后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 107.条件不涉及索引，删除条件为DB_OP_NOTEQUAL
TEST_F(DMLEnhance, V1Com_007_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    uint32_t recordNum = 1000;
    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }
    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, num[i], &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    DB_COND_STRU stCond;
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 2;
    for (int i = 0; i < updateNum; i++) {
        AW_FUN_Log(LOG_STEP, "updateNum is %d  i = %d.", updateNum, i);
        GetCond(&stCond, 3, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTEQUAL);  // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        if (i == 0) {
            V1_AW_MACRO_ASSERT_EQ_INT(999, recNum);
        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(1, recNum);
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 2; i++) {
            // 查询
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
        }
    }
    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);

    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 108.条件不涉及索引，删除条件为DB_OP_HAVEPREFIX
TEST_F(DMLEnhance, V1Com_007_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_HAVEPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 109.条件不涉及索引，删除条件为DB_OP_NOPREFIX
TEST_F(DMLEnhance, V1Com_007_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        }
        expectRecNum++;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOPREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 110.条件不涉及索引，删除条件为DB_OP_LIKE
TEST_F(DMLEnhance, V1Com_007_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_LIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 111.条件不涉及索引，删除条件为DB_OP_NOTLIKE
TEST_F(DMLEnhance, V1Com_007_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            continue;
        } else if (num[i] >= 10 && num[i] < 20) {
            continue;
        } else if (num[i] >= 100 && num[i] < 200) {
            continue;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            continue;
        } else {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_NOTLIKE, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            continue;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            continue;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            continue;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            continue;
        } else {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 112.条件不涉及索引，删除条件为DB_OP_PREFIX
TEST_F(DMLEnhance, V1Com_007_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 113.条件不涉及索引，删除条件为DB_OP_PREFIX12
TEST_F(DMLEnhance, V1Com_007_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] >= 10 && num[i] < 20) {
            expectRecNum++;
        } else if (num[i] >= 100 && num[i] < 200) {
            expectRecNum++;
        } else if (num[i] >= 1000 && num[i] < 2000) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 10 && objIn1[i].f1 < 20) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 100 && objIn1[i].f1 < 200) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 >= 1000 && objIn1[i].f1 < 2000) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 114.条件不涉及索引，删除条件为DB_OP_PREFIX21
TEST_F(DMLEnhance, V1Com_007_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            expectRecNum++;
        } else if (num[i] == 11) {
            expectRecNum++;
        } else if (num[i] == 111) {
            expectRecNum++;
        } else if (num[i] == 1111) {
            expectRecNum++;
        }
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == 1) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 == 11) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 == 111) {
            objIn1[i].f1 = 10000;
        } else if (objIn1[i].f1 == 1111) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 115.条件不涉及索引，删除条件为DB_OP_MAX_PREFIX12
TEST_F(DMLEnhance, V1Com_007_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 1;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (maxNumValue == 0) {
        expectRecNum = 0;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 116.条件不涉及索引，删除条件为DB_OP_MAX_PREFIX21
TEST_F(DMLEnhance, V1Com_007_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 删除数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 117.条件不涉及索引，删除条件为 DB_OP_MIN_PREFIX12
TEST_F(DMLEnhance, V1Com_007_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 10 && num[i] < 20) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 100 && num[i] < 200) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] >= 1000 && num[i] < 2000) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
        expectRecNum = 1;
    }
    if (minNumValue == 10000) {
        expectRecNum = 0;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX12, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 118.条件不涉及索引，删除条件为 DB_OP_MIN_PREFIX21
TEST_F(DMLEnhance, V1Com_007_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t minNumValue = 10000;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 11) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        } else if (num[i] == 1111) {
            if (minNumValue > num[i]) {
                minNumValue = num[i];
            }
        }
    }
    if (minNumValue != 10000) {
        expectRecNum = 1;
    }
    // 删除数据
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MIN_PREFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == minNumValue) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "minNumValue is %d", minNumValue);
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 119.条件不涉及索引，删除条件为 DB_OP_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 删除数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue || maxNumValue != 0) {
            objIn1[i].f0 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            if (objIn1[i].f0 == 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 120.条件不涉及索引，删除条件为 DB_OP_MAX_POSTFIX21
TEST_F(DMLEnhance, V1Com_007_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ulDbId;
    uint32_t dataNum = 1000;
    // 创建namespace
    CommonCreateDB4Test(NULL, &ulDbId);

    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;

    // 创建一个全新的数据类型
    int ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_REL_DEF_STRU stRelDef = {0};

    uint16_t usRelId = 0;
    // 建表
    ret = TestTPC_CreateTbl(ulDbId, "schema_file/test_002.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = DmlTestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    uint32_t recordNum = 1000;

    // 生成要插入的1000条数据
    uint32_t num[recordNum] = {0};
    ret = GetRandomArrayValue(num);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置预期值
    FullPropertyT *objIn1 = (FullPropertyT *)TEST_V1_MALLOC(sizeof(FullPropertyT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 TEST_V1_MALLOC failed !!!");
    }
    // 为这1000条数据赋值
    for (uint32_t i = 0; i < recordNum; i++) {
        SetArrayValueWhichExpect(objIn1, recordNum, num[i], num[i], i);
    }

    // 插入数据
    for (uint32_t i = 0; i < dataNum; i++) {
        DmlTestInitOneRec(recBuf, stRelDef.pstFldLst, num[i], recLen, num[i]);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    std::set<uint32_t> mySet;
    mySet.insert(0);
    mySet.insert(1);
    mySet.insert(2);
    mySet.insert(5);
    mySet.insert(9);
    ShowV1TestDataStructST showInfo = {.showFieldIndexSet = mySet};
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");

    // 读取数据以第一个索引作为条件
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < dataNum; i++) {
            VOS_UINT32 ulRecNum = 0;
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
        }
    }
    DB_COND_STRU stCond;
    // 获取前缀为rel的数据有多少条
    uint32_t expectRecNum = 0;
    uint32_t maxNumValue = 0;
    for (uint32_t i = 0; i < dataNum; i++) {
        if (num[i] == 1111) {
            if (maxNumValue < num[i]) {
                maxNumValue = num[i];
            }
        }
    }
    if (maxNumValue != 0) {
        expectRecNum = 1;
    }
    // 删除数据

    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 1;
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    for (int i = 0; i < updateNum; i++) {
        GetCond(&stCond, 1, objIn1[i].f1, objIn1[i].f3, DB_OP_MAX_POSTFIX21, true);
        // 设置条件
        uint32_t recNum = 0;
        Delete(TPC_GLOBAL_CDB, ulDbId, usRelId, i, &recNum, &stCond);
        V1_AW_MACRO_ASSERT_EQ_INT(expectRecNum, recNum);
        AW_FUN_Log(LOG_INFO, "expectRecNum is %d", expectRecNum);
    }

    for (uint32_t i = 0; i < dataNum; i++) {
        if (objIn1[i].f1 == maxNumValue && maxNumValue != 0) {
            objIn1[i].f1 = 10000;
        }
    }
    StEmbShowAllData(ulDbId, usRelId, &stRelDef, &showInfo, "show data");
    AW_FUN_Log(LOG_INFO, "maxNumValue is %d", maxNumValue);
    // 校验删除后数据
    for (uint32_t Indexkey = 0; Indexkey < 1; Indexkey++) {
        for (uint32_t i = 0; i < 1; i++) {
            VOS_UINT32 ulRecNum = 0;
            AW_FUN_Log(LOG_INFO, "objIn1[i].f0 is %d", objIn1[i].f0);
            ret = TestSelect(
                TPC_GLOBAL_CDB, ulDbId, usRelId, &stRelDef, Indexkey, objIn1[i].f0, &ulRecNum, objIn1, DB_OP_EQUAL);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (objIn1[i].f1 >= 10000) {
                V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(0, ulRecNum);
            } else {
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, ulRecNum);
            }
        }
    }

    TestFreeRelDef(&stRelDef);
    ret = TPC_DropTbl(ulDbId, usRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(NULL, ulDbId);
    TEST_V1_FREE(objIn1);
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}
