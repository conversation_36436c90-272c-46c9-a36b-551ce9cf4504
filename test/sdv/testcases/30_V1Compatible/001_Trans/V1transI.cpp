/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 支持V1兼容事务能力
 Notes        : 接口测试
 Author       : liwenhai lwx1068802
 Modification :
 create       : 2024/08/10
**************************************************************************** */
#include "tools.h"

class V1_trans_interface : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    };
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void V1_trans_interface::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}
void V1_trans_interface::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001.TPC_BeginCDB中第二个参数传NULL
TEST_F(V1_trans_interface, V1Com_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_BeginCDB(dbID, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("pulCdbId is NULL"));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 002.TPC_BeginCDB传不存在的DBID
TEST_F(V1_trans_interface, V1Com_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID + 1, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id 1 does not exist."));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 003.TPC_BeginCDB传已关闭的DBID
TEST_F(V1_trans_interface, V1Com_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id 0 does not opened."));

    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 004.TPC_BeginCDBByID传不存在的DBID
TEST_F(V1_trans_interface, V1Com_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDBByID(dbID + 1, cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id 1 does not exist."));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 005.TPC_BeginCDBByID传已关闭的DBID
TEST_F(V1_trans_interface, V1Com_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDBByID(dbID, cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("DB with id 0 does not opened."));

    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 006.TPC_BeginCDBByID传不存在的CDBID
TEST_F(V1_trans_interface, V1Com_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDBByID(dbID, cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 007.TPC_BeginCDBByID传已存在的CDBID
TEST_F(V1_trans_interface, V1Com_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_BeginCDBByID(dbID, cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_CDBID_ALREADY_USED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Specified cdb id is in use", false));

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 008.TPC_BeginCDBByID传已提交的CDBID
TEST_F(V1_trans_interface, V1Com_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_BeginCDBByID(dbID, cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 009.TPC_BeginCDBByID传已回滚的CDBID
TEST_F(V1_trans_interface, V1Com_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_BeginCDBByID(dbID, cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 010.TPC_CommitCDB传不存在的CDBID
TEST_F(V1_trans_interface, V1Com_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID + 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("is not in use when get cdb block", false));

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 011.TPC_CommitCDB传大于239的CDBID
TEST_F(V1_trans_interface, V1Com_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(MAX_CDB_CNT);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("Cdb id exceeds the upper limit when get cdb block."));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 012.TPC_CommitCDB传已提交的CDBID
TEST_F(V1_trans_interface, V1Com_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("is not in use when get cdb block", false));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 013.TPC_CommitCDB传已回滚的CDBID
TEST_F(V1_trans_interface, V1Com_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("is not in use when get cdb block", false));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 014.TPC_RollbackCDB传不存在的CDBID
TEST_F(V1_trans_interface, V1Com_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_RollbackCDB(cdbID + 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("is not in use when get cdb block", false));

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 015.TPC_RollbackCDB传大于239的CDBID
TEST_F(V1_trans_interface, V1Com_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_RollbackCDB(MAX_CDB_CNT);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError("Cdb id exceeds the upper limit when get cdb block."));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 016.TPC_RollbackCDB传已提交的CDBID
TEST_F(V1_trans_interface, V1Com_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("is not in use when get cdb block", false));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 017.TPC_RollbackCDB传已回滚的CDBID
TEST_F(V1_trans_interface, V1Com_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_INVALID_CDBID, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("is not in use when get cdb block", false));

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 018.TPC_BeginCDB、TPC_CommitCDB的入参全部正确
TEST_F(V1_trans_interface, V1Com_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 019.TPC_BeginCDB、TPC_RollbackCDB的入参全部正确
TEST_F(V1_trans_interface, V1Com_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 020.TPC_BeginCDB的入参全部正确，开启240个事务，成功，开启第241个，失败
TEST_F(V1_trans_interface, V1Com_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbIDVals[MAX_CDB_CNT] = {0};
    for (int i = 0; i < MAX_CDB_CNT; i++) {
        uint32_t cdbID = 0;
        ret = TPC_BeginCDB(dbID, &cdbID);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        AW_MACRO_EXPECT_LT_INT(cdbID, MAX_CDB_CNT);
        cdbIDVals[i] = cdbID;
    }
    AW_MACRO_EXPECT_TRUE(IsUnique(cdbIDVals));

    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(dbID, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_NOCDBBLK, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("There is no free cdb left", false));

    for (int i = 0; i < MAX_CDB_CNT; i++) {
        ret = TPC_CommitCDB(cdbIDVals[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 021.TPC_BeginCDB的入参全部正确，开启240个事务，TPC_BeginCDBByID这240个事务
TEST_F(V1_trans_interface, V1Com_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbIDVals[MAX_CDB_CNT] = {0};
    for (int i = 0; i < MAX_CDB_CNT; i++) {
        uint32_t cdbID = 0;
        ret = TPC_BeginCDB(dbID, &cdbID);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        cdbIDVals[i] = cdbID;
    }

    for (int i = 0; i < MAX_CDB_CNT; i++) {
        ret = TPC_BeginCDBByID(dbID, cdbIDVals[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_CDBID_ALREADY_USED, ret);
    }
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError("Specified cdb id is in use", false));

    for (int i = 0; i < MAX_CDB_CNT; i++) {
        ret = TPC_CommitCDB(cdbIDVals[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 022.TPC_BeginCDB的入参全部正确，开启240个事务，随机提交120个ID，再TPC_BeginCDB120个
TEST_F(V1_trans_interface, V1Com_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbIDVals[MAX_CDB_CNT] = {0};
    for (int i = 0; i < MAX_CDB_CNT; i++) {
        uint32_t cdbID = 0;
        ret = TPC_BeginCDB(dbID, &cdbID);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        cdbIDVals[i] = cdbID;
    }

    for (int i = 0; i < MAX_CDB_CNT; i += 2) {
        ret = TPC_CommitCDB(cdbIDVals[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < MAX_CDB_CNT / 2; i++) {
        uint32_t cdbID = 0;
        ret = TPC_BeginCDB(dbID, &cdbID);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        AW_MACRO_EXPECT_LT_INT(cdbID, MAX_CDB_CNT);
        cdbIDVals[i * 2] = cdbID;
    }
    AW_MACRO_EXPECT_TRUE(cdbIDVals);

    for (int i = 0; i < MAX_CDB_CNT; i++) {
        ret = TPC_CommitCDB(cdbIDVals[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

// 023.TPC_BeginCDB的入参全部正确，开启240个事务，随机回滚120个ID，再TPC_BeginCDBByID120个
TEST_F(V1_trans_interface, V1Com_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t dbID = 0;
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t cdbIDVals[MAX_CDB_CNT] = {0};
    for (int i = 0; i < MAX_CDB_CNT; i++) {
        uint32_t cdbID = 0;
        ret = TPC_BeginCDB(dbID, &cdbID);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        cdbIDVals[i] = cdbID;
    }

    for (int i = 0; i < MAX_CDB_CNT; i += 2) {
        ret = TPC_RollbackCDB(cdbIDVals[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < MAX_CDB_CNT / 2; i++) {
        uint32_t cdbID = 0;
        ret = TPC_BeginCDB(dbID, &cdbID);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        AW_MACRO_EXPECT_LT_INT(cdbID, MAX_CDB_CNT);
        cdbIDVals[i * 2] = cdbID;
    }
    AW_MACRO_EXPECT_TRUE(cdbIDVals);

    for (int i = 0; i < MAX_CDB_CNT; i++) {
        ret = TPC_CommitCDB(cdbIDVals[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_CloseDB(dbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
