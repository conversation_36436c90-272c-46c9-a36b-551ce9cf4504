/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: DiskANN - GME向量占位符TEST
 * Author: 
 * Create: 2024-11-25
 */

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include <iostream>
#include <cmath>
#include <iomanip>
#include <sstream>

using namespace std;
using namespace testing::ext;

GmeConnT *conn = nullptr;
int ret;
char *configPath = EmbSqlGetConfigPath();

// L2: 0.374166 2.42899 7.275988 11.086027
// cos: 0.000000 0.008352 0.029557 0.039243
GmeSqlTestCtx sqlSetup[] = {
    {"CREATE TABLE t1(id int primary key, repr1 floatvector(3), repr2 floatvector(3));", GMERR_OK, {}},
    {"CREATE INDEX diskann_cos_repr1 ON t1 USING GSDISKANN(repr1 COSINE);", GMERR_OK, {}},
    {"CREATE INDEX diskann_l2_repr1 ON t1 USING GSDISKANN(repr1 L2);", GMERR_OK, {}},
    {"insert into t1 values (1, '[1.1, 2.2, 3.3]', '[2.3, 3.4, 4.5]');", GMERR_OK, {}},
    {"insert into t1 values (2, '[5.1, 6.2, 7.3]', '[7.3, 8.4, 9.5]');", GMERR_OK, {}},
};

GmeSqlTestCtx sqlRelease[] = {
    {"DROP TABLE t1;", GMERR_OK, {}},
};

// 函数用于将浮点数保留指定小数位
double roundTo6DecimalPlaces6(double value) {
    std::stringstream stream;
    stream << std::fixed << std::setprecision(6) << value;
    double roundedValue;
    stream >> roundedValue;
    return roundedValue;
}

double roundTo6DecimalPlaces5(double value) {
    std::stringstream stream;
    stream << std::fixed << std::setprecision(5) << value;
    double roundedValue;
    stream >> roundedValue;
    return roundedValue;
}

class sqlPlaceholder : public testing::Test {
public:
    virtual void SetUp()
    {
        system("rm -rf ./data/gmdb");
#ifdef COMPATIBILITY_2024_JULY
    EXPECT_NE(system("cp -r ../../Compatibility_history_db/July/data ."), -1);
#else
        system("mkdir -p ./data/gmdb");
#endif
        system("ipcrm -a"); 
        AW_MACRO_EXPECT_EQ_INT(T_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
        AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlStepAndCheck(conn, sqlSetup, 5));
    }

    virtual void TearDown()
    {
        AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlStepAndCheck(conn, sqlRelease, 1));
        AW_MACRO_EXPECT_EQ_INT(T_OK, GmeClose(conn));
    }
};

/*
投影列: L2 - 标量+单向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_001, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by repr1 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_002, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by ? <-> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_003, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by repr1 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_004, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by ? <=> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_005, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by repr1 <-> ?, repr2 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_006, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by ? <-> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_007, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by repr1 <=> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_008, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by ? <=> repr1, ? <=> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_009, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by repr1 <-> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: L2 - 标量+单向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_010, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ? from t1 order by ? <=> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis);
}

/*
投影列: COS - 标量+单向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_011, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by repr1 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    

    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_012, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by ? <-> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    

    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_013, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by repr1 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    

    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_014, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by ? <=> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    

    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_015, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by repr1 <-> ?, repr2 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_016, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by ? <-> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_017, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by repr1 <=> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_018, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by ? <=> repr1, ? <=> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_019, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by repr1 <-> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: COS - 标量+单向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_020, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ? from t1 order by ? <=> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis = GmeSqlColumnDouble(stmt, 1);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis = roundTo6DecimalPlaces6(dis);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis);
}

/*
投影列: L2 - 标量+多向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_021, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by repr1 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_022, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by ? <-> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_023, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by repr1 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_024, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by ? <=> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_025, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by repr1 <-> ?, repr2 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_026, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by ? <-> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_027, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by repr1 <=> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_028, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by ? <=> repr1, ? <=> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_029, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by repr1 <-> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 - 标量+多向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_030, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <-> ? from t1 order by ? <=> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_031, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by repr1 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_032, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by ? <-> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_033, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by repr1 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_034, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by ? <=> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_035, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by repr1 <-> ?, repr2 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_036, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by ? <-> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_037, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by repr1 <=> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_038, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by ? <=> repr1, ? <=> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_039, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by repr1 <-> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: COS - 标量+多向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_040, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <=> ? from t1 order by ? <=> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_041, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <=> ? from t1 order by repr1 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 单列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_042, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <-> ? from t1 order by ? <-> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_043, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <=> ? from t1 order by repr1 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 单列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_044, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <-> ? from t1 order by ? <=> repr1 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_045, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <=> ? from t1 order by repr1 <-> ?, repr2 <-> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 多列 - L2
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_046, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <-> ? from t1 order by ? <-> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_047, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <=> ? from t1 order by repr1 <=> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 多列 - COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_048, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <-> ? from t1 order by ? <=> repr1, ? <=> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_049, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <-> ?, repr2 <=> ? from t1 order by repr1 <-> ?, repr2 <=> ? LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

/*
投影列: L2 + COS - 标量+多向量
距离: 多列 - L2 + COS
*/
HWTEST_F(sqlPlaceholder, sqlPlaceholder_050, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 <=> ?, repr2 <-> ? from t1 order by ? <=> repr1, ? <-> repr2 LIMIT 1;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 1, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 4, bindVec, 3, nullptr));
    
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    dis2 = roundTo6DecimalPlaces5(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.000000, dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(2.42899, dis2);
}

// 标量覆盖 - targetlist, where, order by支持标量的表达式
HWTEST_F(sqlPlaceholder, sqlPlaceholder_051, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis1, dis2;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id+?, repr1 <-> ?, ? <=> repr2 from t1 where id+? < 3 order by id+?, repr1 <=> ?, ? <-> repr2;";
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    float bindVec[] = {1.0, 2.0, 3.0};
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindInt(stmt, 1, 0));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 2, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 3, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindInt(stmt, 4, 1));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindInt(stmt, 5, 1));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 6, bindVec, 3, nullptr));
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlBindFloatVector(stmt, 7, bindVec, 3, nullptr));

    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        id = GmeSqlColumnInt(stmt, 0);
        dis1 = GmeSqlColumnDouble(stmt, 1);
        dis2 = GmeSqlColumnDouble(stmt, 2);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeSqlFinalize(stmt));

    AW_MACRO_EXPECT_EQ_INT(1, id);
    dis1 = roundTo6DecimalPlaces6(dis1);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.374166, dis1);
    dis2 = roundTo6DecimalPlaces6(dis2);
    AW_MACRO_EXPECT_EQ_DOUBLE(0.008352, dis2);
}

// 反向约束 - 没有根据字段排序 查询失败, 返回GMERR_FEATURE_NOT_SUPPORTED
HWTEST_F(sqlPlaceholder, sqlPlaceholder_052, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    int32_t id;
    double dis;
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    char *selectSql = (char *)"select id, repr1 from t1 order by ? <-> ?;";
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
}


