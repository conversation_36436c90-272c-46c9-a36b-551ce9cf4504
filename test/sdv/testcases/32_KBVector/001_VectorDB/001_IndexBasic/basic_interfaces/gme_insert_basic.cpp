/*
* Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
* Description: 基础接口测试 - GME_VecotrTEST
* Author: swj
* Create: 2024-10-28
 */

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include <cstring>
#include <vector>
#include <random>
#include <ctime>

using namespace std;
using namespace testing::ext;

GmeSqlTestCtx sqlSetup[] = {
    {"CREATE TABLE employee (id INT, name TEXT, age INT, remark TEXT);", GMERR_OK, {}},
    {"CREATE TABLE employee_pk (id INT PRIMARY KEY, name TEXT, age INT, remark TEXT);", GMERR_OK, {}},
    {"CREATE TABLE employee_default (id INT, name TEXT, age INT DEFAULT 18, remark TEXT DEFAULT 'default remark');",
        GMERR_OK, {}},
    {"CREATE TABLE employee_not_null (id INT NOT NULL, name TEXT NOT NULL, age INT, remark TEXT);", GMERR_OK, {}}
};

GmeSqlTestCtx sqlRelease[] = {
    {"DROP TABLE employee;", GMERR_OK, {}},
    {"DROP TABLE employee_pk;", GMERR_OK, {}},
    {"DROP TABLE employee_default;", GMERR_OK, {}},
    {"DROP TABLE employee_not_null;", GMERR_OK, {}},
};

char* generateString(int maxLength) {
    std::random_device rd;  // 用于获取随机数种子
    std::mt19937 gen(rd()); // 使用Mersenne Twister算法生成随机数
    std::uniform_int_distribution<> dis(97, 122); // 生成小写字母的随机数分布

    int length = dis(gen) % maxLength + 1; // 随机生成长度（1到maxLength之间）
    char* str = new char[length + 1]; // 分配内存，+1是为了存储字符串结束符'\0'
    str[length] = '\0'; // 设置字符串结束符

    for (int i = 0; i < length; ++i) {
        str[i] = static_cast<char>(dis(gen)); // 生成随机小写字母并存储到字符串中
    }
    return str;
}

int generateRandomAge() {
    std::random_device rd; // 用于获取随机数种子
    std::mt19937 gen(rd()); // 使用Mersenne Twister算法生成随机数
    std::uniform_int_distribution<> dis(18, 65); // 生成18到65之间的随机整数分布
    return dis(gen); // 生成并返回随机数
}

class gmeInsertBasic : public testing::Test {
public:
    GmeConnT *conn = NULL;
    char *configPath = EmbSqlGetConfigPath();
    const char *dbFile = "./data/gmdb/datafile";
    virtual void SetUp()
    {
        system("rm -rf ./data/gmdb");
        system("mkdir -p ./data/gmdb");
        system("ipcrm -a");
        int ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlStepAndCheck(conn, sqlSetup, 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        int ret = GmeSqlStepAndCheck(conn, sqlRelease, 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmeClose(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 指定完整的列和值，列和值彼此对应，插入数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_001, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee (id, name, age, remark) VALUES(?, ?, ?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlBindInt(stmt, 1, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 2, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlBindInt(stmt, 3, generateRandomAge());
        ASSERT_EQ(GMERR_OK, ret);
        char *remark = generateString(10);
        ret = GmeSqlBindText(stmt, 4, remark, strlen(remark), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee", numRecord);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 指定部分列和值，列和值彼此对应，插入数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_002, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee (id, name) VALUES(?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlBindInt(stmt, 1, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 2, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee", numRecord);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 不指定列，值和全量列位置对应，插入数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_003, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee VALUES(?, ?, ?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlBindInt(stmt, 1, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 2, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlBindInt(stmt, 3, generateRandomAge());
        ASSERT_EQ(GMERR_OK, ret);
        char *remark = generateString(10);
        ret = GmeSqlBindText(stmt, 4, remark, strlen(remark), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee", numRecord);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 指定列和值，列比值多一项，插入数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_004, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee (id, name, age, remark) VALUES(?, ?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlBindInt(stmt, 1, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 2, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        ret = GmeSqlBindInt(stmt, 3, generateRandomAge());
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 指定列和值，列比值少一项，插入数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_005, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee (id, name, age) VALUES(?, ?, ?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlBindInt(stmt, 1, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 2, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        ret = GmeSqlBindInt(stmt, 3, generateRandomAge());
        ASSERT_EQ(GMERR_UNEXPECTED_NULL_VALUE, ret);
        char *remark = generateString(10);
        ret = GmeSqlBindText(stmt, 4, remark, strlen(remark), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 指定列和值，列和值数量一致，位置错乱，插入数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_006, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee (id, name, age, remark) VALUES(?, ?, ?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 1, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlBindInt(stmt, 2, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char *remark = generateString(10);
        ret = GmeSqlBindText(stmt, 3, remark, strlen(remark), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 指定列和值，列比值少一项，插入数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_007, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee VALUES(?, ?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlBindInt(stmt, 1, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 2, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        ret = GmeSqlBindInt(stmt, 3, generateRandomAge());
        ASSERT_EQ(GMERR_UNEXPECTED_NULL_VALUE, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// NOTICE SELECT及AffectRow交互
// 表定义默认值列，插入默认值数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_008, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee_default DEFAULT VALUES;";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee_default", numRecord);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 表未定义默认值列，插入默认值数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_009, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee DEFAULT VALUES;";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee", numRecord);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 表定义默认值列，指定部分列和值，默认值列不设值，写入数据
HWTEST_F(gmeInsertBasic, gmeInsertBasic_010, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee_default (id, name) VALUES(?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlBindInt(stmt, 1, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 2, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee_default", numRecord);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 表定义默认值列，且该列具备唯一性约束，写入数据数据时不设置该列的值
HWTEST_F(gmeInsertBasic, gmeInsertBasic_011, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    struct GmeSqlTestCtx sqlTests001;
    sqlTests001.sql = "CREATE UNIQUE INDEX employee_default_index ON employee_default (age, remark);";
    sqlTests001.status = GMERR_OK;
    sqlTests001.output = {};
    int32_t ret = GmeSqlStepAndCheck(conn, &sqlTests001, 1);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    char *sql = (char *)"INSERT INTO employee_default DEFAULT VALUES;";
    GmeSqlStmtT *stmt = NULL;
    ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlStep(stmt);
        if (i == 0) {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);
        }
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee_default", 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 表最右侧的列定义默认值，写入数据时不指定列，设置左侧列的值
HWTEST_F(gmeInsertBasic, gmeInsertBasic_012, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    char *sql = (char *)"INSERT INTO employee_default VALUES(?, ?);";
    GmeSqlStmtT *stmt = NULL;
    int32_t ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    int32_t numRecord = 10;
    for (int32_t i = 0; i < numRecord; i++) {
        ret = GmeSqlBindInt(stmt, 1, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        char *name = generateString(5);
        ret = GmeSqlBindText(stmt, 2, name, strlen(name), free);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);

        ret = GmeSqlStep(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
        ret = GmeSqlReset(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    ret = GmeSqlFinalize(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    ret = GmeSqlRecordCountCheck(conn, "employee_default", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
