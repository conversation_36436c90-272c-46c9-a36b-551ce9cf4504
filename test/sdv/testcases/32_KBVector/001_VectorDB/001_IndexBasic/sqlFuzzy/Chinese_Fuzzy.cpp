/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 迁移终端用例：SQL SELECT 向量查询 有where, 无order by
 * Author: qinhaoran
 * Create: 2025-02-18
 */

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"

using namespace std;
using namespace testing::ext;

int ret;
GmeConnT *conn = NULL;
GmeSqlTestCtx sqlTests;

string sqlInsert1 = "INSERT INTO t1 VALUES (1,'特仑苏'), "
    "(2,'drink特仑苏'), (3,'特仑苏abc'), (4,'特仑苏reallygood'), (5,'高质量CODE'), (6,'高质量code');";
string sqlInsert2 = "INSERT INTO 中文表 VALUES (1,'深圳市', 1, '中国');";
string sqlInsert3 = "INSERT INTO t2 VALUES (1,'牛奶占5%'), (2,'特仑苏_abc');";

GmeSqlTestCtx sqlSetup[] = {
    {"CREATE TABLE t1(id int primary key, name text);", GMERR_OK, {}},
    {"CREATE TABLE t2(id int primary key, name text);", GMERR_OK, {}},
    {"CREATE TABLE 中文表(id int primary key, 城市 text, 编号 int, 国家 text);", GMERR_OK, {}},
    {sqlInsert1, GMERR_OK, {}},
    {sqlInsert2, GMERR_OK, {}},
    {sqlInsert3, GMERR_OK, {}},
};

GmeSqlTestCtx sqlRelease[] = {
    {"DROP TABLE t1;", GMERR_OK, {}},
    {"DROP TABLE t2;", GMERR_OK, {}},
    {"DROP TABLE 中文表;", GMERR_OK, {}},
};
static char curPath[512];
class Chinese_Fuzzy : public testing::Test {
public:
    char *configPath = EmbSqlGetConfigPath();
    static void SetUpTestCase() {
        getcwd(curPath, sizeof(curPath));
    }
    virtual void SetUp()
    {
        //system("mkdir -p ./data/gmdb");
        RemoveFileDir(curPath);
        ModifyCfgFile("pageSize=32 bufferPoolSize=4096 redoPubBufSize=4096", (const char *)configPath);
        EXPECT_EQ(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
        EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, sqlSetup, 6));
    }

    virtual void TearDown()
    {
        system("ipcrm -a"); 
        EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, sqlRelease, 3));
        EXPECT_EQ(GMERR_OK, GmeClose(conn));
    }
};

// 百分号% 纯中文 LIKE '特%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_001, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '特%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号% 纯中文 LIKE '%苏'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_002, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '%苏';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号% 纯中文 LIKE '%特%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_003, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '%特%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号% 纯中文 LIKE '%仑%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_004, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '%仑%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号% 中文+英文 LIKE 'd%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_005, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE 'd%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"2", "drink特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号% 中文+英文 LIKE '%苏'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_006, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '%苏';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号% 中文+英文 LIKE '%特%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_007, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '%特%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号% 中文+英文 LIKE '%仑%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_008, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '%仑%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 纯中文 LIKE '特仑_'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_009, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '特仑_';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 纯中文 LIKE '_仑苏'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_010, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '_仑苏';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 纯中文 LIKE '_仑_'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_011, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '_仑_';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 纯中文 LIKE '__苏'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_012, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '__苏';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 纯中文 LIKE '特__'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_013, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '特__';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 中文+英文 LIKE '特仑苏ab_'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_014, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '特仑苏ab_';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 中文+英文 LIKE '_仑苏abc'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_015, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '_仑苏abc';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 中文+英文 LIKE '_仑苏ab_'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_016, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '_仑苏ab_';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 中文+英文 LIKE '___abc'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_017, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '___abc';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 中文+英文 LIKE '特仑苏___'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_018, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '特仑苏___';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 下划线_ 中文+英文 LIKE '__苏a__'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_019, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '__苏a__';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号+下划线 LIKE '__苏__ally%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_020, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '__苏__ally%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号+下划线 LIKE '%5/%' ESCAPE '/'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_021, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t2 WHERE NAME LIKE '%5/%' ESCAPE '/';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "牛奶占5%"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 百分号+下划线 LIKE '%/_a%' ESCAPE '/'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_022, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t2 WHERE NAME LIKE '%/_a%' ESCAPE '/';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"2", "特仑苏_abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号* 纯中文 GLOB '特*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_023, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '特*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号* 纯中文 GLOB '*苏'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_024, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '*苏';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号* 纯中文 GLOB '*特*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_025, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '*特*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号* 纯中文 GLOB '*仑*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_026, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '*仑*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号* 中文+英文 GLOB 'd*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_027, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB 'd*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"2", "drink特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号* 中文+英文 GLOB '*苏'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_028, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '*苏';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号* 中文+英文 GLOB '*特*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_029, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '*特*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号* 中文+英文 GLOB '*仑*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_030, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '*仑*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}, {"2", "drink特仑苏"}, {"3", "特仑苏abc"}, {"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 纯中文 GLOB '特仑?'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_031, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '特仑?';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 纯中文 GLOB '?仑苏'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_032, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '?仑苏';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 纯中文 GLOB '?仑?'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_033, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '?仑?';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 纯中文 GLOB '??苏'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_034, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '??苏';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 纯中文 GLOB '特??'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_035, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '特\?\?';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "特仑苏"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 中文+英文 GLOB '特仑苏ab?'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_036, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '特仑苏ab?';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 中文+英文 GLOB '?仑苏abc'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_037, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '?仑苏abc';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 中文+英文 GLOB '?仑苏ab?'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_038, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '?仑苏ab?';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 中文+英文 GLOB '???abc'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_039, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '???abc';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 中文+英文 GLOB '特仑苏???'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_040, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '特仑苏\?\?\?';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 问号? 中文+英文 GLOB '??苏a??'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_041, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '\?\?苏a\?\?';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 星号+问号 GLOB '??苏??ally*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_042, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '??苏??ally*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"4", "特仑苏reallygood"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 大小写敏感 GLOB '*C*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_043, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '*C*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"5", "高质量CODE"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 大小写敏感 GLOB '*c*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_044, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '*c*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"3", "特仑苏abc"}, {"6", "高质量code"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 大小写敏感 GLOB '高质????E'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_045, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '高质????E';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"5", "高质量CODE"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 大小写敏感 GLOB '高质????e'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_046, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '高质????e';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"6", "高质量code"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 大小写敏感 GLOB '高质???D*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_047, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '高质???D*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"5", "高质量CODE"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 大小写敏感 GLOB '高质???d*'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_048, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "SELECT * FROM t1 WHERE NAME GLOB '高质???d*';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"6", "高质量code"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// create 中文表名
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_049, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx ChineseTable[] = {
        {"CREATE TABLE 测试表(id int primary key, name text);", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, ChineseTable, 1));
}

// create 中文列名
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_050, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx ChineseColumn[] = {
        {"CREATE TABLE t4(id int primary key, 名字 text);", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, ChineseColumn, 1));
}

// create 中文索引名
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_051, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx CreateTable[] = {
        {"CREATE TABLE t3(id int primary key, name text);", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, CreateTable, 1));

    GmeSqlTestCtx ChineseIndex[] = {
        {"CREATE INDEX 索引名 ON t3(id);", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, ChineseIndex, 1));
}

// select - select * from 中文表 where 城市 = '深圳市';
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_052, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "select * from 中文表 where 城市 = '深圳市';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "深圳市", "1", "中国"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// insert - insert into 中文表 values(2, '广州市', 2, '中国');
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_053, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州市', 2, '中国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 1));
}

// update - update 中文表 set 城市 = '佛山市' where 城市 = '深圳市';
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_054, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx UpdateData[] = {
        {"update 中文表 set 城市 = '佛山市' where 城市 = '深圳市';", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, UpdateData, 1));
}

// delete - delete from 中文表 where 城市 = '深圳市';
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_055, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx DeleteData[] = {
        {"delete from 中文表 where 城市 = '深圳市';", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, DeleteData, 1));
}

// 创建的视图支持中文 - 需要支持
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_056, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx ViewData[] = {
        {"create view 视图 as select * from 中文表;", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, ViewData, 1));
}

// 创建的触发器支持中文 - 支持
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_057, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx TriggerData1[] = {
        {"create table 触发器表(id int, 名字 text);", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, TriggerData1, 1));
    GmeSqlTestCtx TriggerData2[] = {
        {"create trigger 触发器 insert on 触发器表 begin insert into 触发器表 values (1,'名字'); end;", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, TriggerData2, 1));
}

// 实例名支持中文 - 不需要支持
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_058, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeConnT *conn1 = NULL;
    char *configPath1 = (char *)"./数据/数据库/持久化文件";
    EXPECT_EQ(GMERR_NO_DATA, GmeOpen(configPath1, GME_OPEN_CREATE, &conn1));
    EXPECT_EQ(GMERR_UNEXPECTED_NULL_VALUE, GmeClose(conn1));
}

// 表达式> - select * from 中文表 where 编号 > 0;
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_059, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "select * from 中文表 where 编号 > 0;";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "深圳市", "1", "中国"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 表达式< - select * from 中文表 where 编号 < 2;
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_060, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "select * from 中文表 where 编号 < 2;";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "深圳市", "1", "中国"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 表达式|| - select * from 中文表 where 城市 = '深圳' || '市';
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_061, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    sqlTests.sql = "select * from 中文表 where 城市 = '深圳' || '市';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "深圳市", "1", "中国"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 表达式|| - update 中文表 set 城市 = '佛山' || '市' where 城市 = '深圳' || '市';
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_062, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx UpdateData[] = {
        {"update 中文表 set 城市 = '佛山' || '市' where 城市 = '深圳' || '市';", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, UpdateData, 1));
}

// 表达式|| - insert into 中文表 values(2, '广州' || '市', 2, '中' || '国');
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_063, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州' || '市', 2, '中' || '国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 1));
}

// 表达式|| - delete from 中文表 where 城市 = '深圳' || '市';
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_064, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx DeleteData[] = {
        {"delete from 中文表 where 城市 = '深圳' || '市';", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, DeleteData, 1));
}

// Order by - select * from 中文表 order by 编号;
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_065, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州' || '市', 2, '中' || '国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 1));

    sqlTests.sql = "select * from 中文表 order by 编号;";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "深圳市", "1", "中国"}, {"2", "广州市", "2", "中国"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// Order by - select * from 中文表 order by 城市;
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_066, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州' || '市', 2, '中' || '国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 1));

    sqlTests.sql = "select * from 中文表 order by 城市;";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"2", "广州市", "2", "中国"}, {"1", "深圳市", "1", "中国"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 投影列函数 - 标量函数ABS - select abs(编号) from 中文表;
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_067, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");

    sqlTests.sql = "select abs(编号) from 中文表;";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 投影列函数 - 标量函数ABS - select abs(城市) from 中文表; 预期报错
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_068, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");

    sqlTests.sql = "select abs(城市) from 中文表;";
    sqlTests.status = GMERR_INVALID_PARAMETER_VALUE;
    sqlTests.output = {};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 投影列函数 - 聚合函数MAX - select max(编号) from 中文表;
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_069, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州' || '市', 2, '中' || '国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 1));

    sqlTests.sql = "select max(编号) from 中文表;";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"2"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 投影列函数 - 聚合函数MAX - select max(城市) from 中文表; 预期报错
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_070, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");

    sqlTests.sql = "select max(城市) from 中文表;";
    sqlTests.status = GMERR_INVALID_PARAMETER_VALUE;
    sqlTests.output = {};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// group by 编号
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_071, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州市', 2, '中国');", GMERR_OK, {}},
        {"insert into 中文表 values(3, '佛山市', 2, '中国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 2));
    sqlTests.sql = "select 编号, count(*) from 中文表 group by 编号;";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"1", "1"}, {"2", "2"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// group by 城市
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_072, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州市', 2, '中国');", GMERR_OK, {}},
        {"insert into 中文表 values(3, '广州市', 3, '中国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 2));
    sqlTests.sql = "select 城市, count(*) from 中文表 group by 城市;";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"广州市", "2"}, {"深圳市", "1"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// IN - 支持常量绑定
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_073, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州市', 2, '中国');", GMERR_OK, {}},
        {"insert into 中文表 values(3, '佛山市', 3, '中国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 2));
    char *selectSql = (char *)"select * from 中文表 where 编号 IN (?,?,?);";
    EXPECT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    EXPECT_EQ(GMERR_OK, GmeSqlBindInt(stmt, 1, 1));
    EXPECT_EQ(GMERR_OK, GmeSqlBindInt(stmt, 2, 2));
    EXPECT_EQ(GMERR_OK, GmeSqlBindInt(stmt, 3, 3));
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK)
    {
        uint32_t columnCount = GmeSqlColumnCount(stmt);
        if (columnCount == 0) {
            break;
        }
        EXPECT_EQ(4, columnCount);

        GmeDbDataTypeE type = GmeSqlColumnType(stmt, 1);
        EXPECT_EQ(GME_DB_DATATYPE_TEXT, type);

        uint32_t bytes = GmeSqlColumnBytes(stmt, 0);
        EXPECT_EQ(8, bytes); //INT64

        const char *name = GmeSqlColumnName(stmt, 1);
        AW_MACRO_EXPECT_EQ_STR("城市", name);

        GmeDbValueT val;
        val = GmeSqlColumnValue(stmt, 0);
        printf("id = %ld\n", val.value.longValue);

        const char *city = GmeSqlColumnText(stmt, 1);
        printf("城市 = %s\n", city);

        int32_t num = GmeSqlColumnInt(stmt, 2);
        printf("编号 = %d\n", num);

        const char *country = GmeSqlColumnText(stmt, 3);
        printf("国家 = %s\n", country);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        EXPECT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
    }
    EXPECT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
}

// IN - 支持字符串绑定
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_074, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    GmeSqlTestCtx InsertData[] = {
        {"insert into 中文表 values(2, '广州市', 2, '中国');", GMERR_OK, {}},
        {"insert into 中文表 values(3, '佛山市', 3, '中国');", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, InsertData, 2));
    char *selectSql = (char *)"select * from 中文表 where 城市 IN (?,?,?);";
    EXPECT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, nullptr));
    const char *city1 = "深圳市";
    EXPECT_EQ(GMERR_OK, GmeSqlBindText(stmt, 1, city1, strlen(city1), nullptr));
    const char *city2 = "广州市";
    EXPECT_EQ(GMERR_OK, GmeSqlBindText(stmt, 2, city2, strlen(city2), nullptr));
    const char *city3 = "佛山市";
    EXPECT_EQ(GMERR_OK, GmeSqlBindText(stmt, 3, city3, strlen(city3), nullptr));

    while ((ret = GmeSqlStep(stmt)) == GMERR_OK)
    {
        uint32_t columnCount = GmeSqlColumnCount(stmt);
        if (columnCount == 0) {
            break;
        }
        EXPECT_EQ(4, columnCount);

        GmeDbDataTypeE type = GmeSqlColumnType(stmt, 1);
        EXPECT_EQ(GME_DB_DATATYPE_TEXT, type);

        uint32_t bytes = GmeSqlColumnBytes(stmt, 1);
        EXPECT_EQ(10, bytes); //3*3+1

        const char *name = GmeSqlColumnName(stmt, 1);
        AW_MACRO_EXPECT_EQ_STR("城市", name);

        GmeDbValueT val;
        val = GmeSqlColumnValue(stmt, 0);
        printf("id = %ld\n", val.value.longValue);

        const char *city = GmeSqlColumnText(stmt, 1);
        printf("城市 = %s\n", city);

        int32_t num = GmeSqlColumnInt(stmt, 2);
        printf("编号 = %d\n", num);

        const char *country = GmeSqlColumnText(stmt, 3);
        printf("国家 = %s\n", country);
    }

    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        ComWriteLog(LOG_ERROR, "GmeSqlStep: step sql failed, ret:%d", ret);
        EXPECT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
    }
    EXPECT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
}

// 改 - LIKE 百分号+下划线 LIKE '__苏__ally%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_075, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx UpdateData[] = {
        {"update t1 set name = '今天天气真hot' where name LIKE '__苏__ally%';", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, UpdateData, 1));

    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '__苏__ally%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));

    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '%__气__o%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {{"4", "今天天气真hot"}};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

// 删 - LIKE 百分号+下划线 LIKE '__苏__ally%'
HWTEST_F(Chinese_Fuzzy, Chinese_Fuzzy_076, TestSize.Level0)
{
    ComWriteLog(LOG_STEP, "test start.");
    GmeSqlTestCtx UpdateData[] = {
        {"delete from t1 where name LIKE '__苏__ally%';", GMERR_OK, {}},
    };
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, UpdateData, 1));

    sqlTests.sql = "SELECT * FROM t1 WHERE NAME LIKE '__苏__ally%';";
    sqlTests.status = GMERR_OK;
    sqlTests.output = {};
    EXPECT_EQ(GMERR_OK, GmeSqlStepAndCheck(conn, &sqlTests, 1));
}

