/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025. All rights reserved.
 * Description: 创建1025~1536维度表格，基础规格覆盖功能测试
 * Author:
 * Create: 2025-02-28
 */

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include "db_json_common.h"
#include <random>
#include <sys/time.h>
#include <stdio.h>
#include <string.h>

using namespace std;
using namespace testing::ext;

#define THREAD_NUM 2

typedef struct {
    GmeConnT *conn;
} ThreadArgsT;

static constexpr uint16_t JSON_REJECT_DUPLICATES = 0x1;

const char *configPath = EmbSqlGetConfigPath();

void InsertDataRandom(GmeConnT *conn, string tableName, int dim, int InsertNum, int startId = 0)
{
    printf("insert data to %s, count is %d, starid is %d \n", tableName.c_str(), InsertNum, startId);
    string VecStr = RandomFloat(1, 10, dim);
    for (int i = startId + 1; i <= InsertNum + startId; i++) {
        GmeSqlTestCtx InsertData[] = {
            "INSERT INTO " + tableName + " VALUES(" + to_string(i) + ",'" + VecStr + "');", GMERR_OK, {}};
        ASSERT_EQ(GmeSqlStepAndCheck(conn, InsertData, 1), T_OK);
        VecStr = RandomFloat(1, 10, dim);
    }
}

void InsertDataRandom2(
    GmeConnT *conn, string tableName, int dim1, int dim2, int dim3, int dim4, int dim5, int InsertNum, int startId = 0)
{
    printf("insert data to %s, count is %d, starid is %d \n", tableName.c_str(), InsertNum, startId);
    string VecStr1 = RandomFloat(1, 10, dim1);
    string VecStr2 = RandomFloat(1, 10, dim2);
    string VecStr3 = RandomFloat(1, 10, dim3);
    string VecStr4 = RandomFloat(1, 10, dim4);
    string VecStr5 = RandomFloat(1, 10, dim5);
    for (int i = startId + 1; i <= InsertNum + startId; i++) {
        GmeSqlTestCtx InsertData[] = {"INSERT INTO " + tableName + " VALUES(" + to_string(i) + ",'" + VecStr1 + "','" +
                                          VecStr2 + "','" + VecStr3 + "','" + VecStr4 + "','" + VecStr5 + "');",
            GMERR_OK, {}};
        ASSERT_EQ(GmeSqlStepAndCheck(conn, InsertData, 1), T_OK);
        VecStr1 = RandomFloat(1, 10, dim1);
        VecStr2 = RandomFloat(1, 10, dim2);
        VecStr3 = RandomFloat(1, 10, dim3);
        VecStr4 = RandomFloat(1, 10, dim4);
        VecStr5 = RandomFloat(1, 10, dim5);
    }
}

void InsertDataRandom3(GmeConnT *conn, string tableName, int dim1, int dim2, int dim3, int InsertNum, int startId = 0)
{
    printf("insert data to %s, count is %d, starid is %d \n", tableName.c_str(), InsertNum, startId);
    string VecStr1 = RandomFloat(1, 10, dim1);
    string VecStr2 = RandomFloat(1, 10, dim2);
    string VecStr3 = RandomFloat(1, 10, dim3);
    for (int i = startId + 1; i <= InsertNum + startId; i++) {
        GmeSqlTestCtx InsertData[] = {"INSERT INTO " + tableName + " VALUES(" + to_string(i) + ",'" + VecStr1 + "','" +
                                          VecStr2 + "','" + VecStr3 + "');",
            GMERR_OK, {}};
        ASSERT_EQ(GmeSqlStepAndCheck(conn, InsertData, 1), T_OK);
        VecStr1 = RandomFloat(1, 10, dim1);
        VecStr2 = RandomFloat(1, 10, dim2);
        VecStr3 = RandomFloat(1, 10, dim3);
    }
}

// GmeSqlExecute的回调函数实现
int SqlViewResultCallBack(void *data, uint16_t colCnt, char **colValues, char **colNames)
{
    ViewResult *resultSet = static_cast<ViewResult *>(data);
    if (resultSet->colNames.empty()) {
        for (int i = 0; i < colCnt; ++i) {
            resultSet->colNames.emplace_back(colNames[i]);
        }
    }
    for (int i = 0; i < colCnt; ++i) {
        resultSet->colValues.emplace_back(colValues[i]);
    }
    return GMERR_OK;
}

class fp16vector_func_test : public testing::Test {
public:
    GmeConnT *conn = NULL;
    char *configPath = EmbSqlGetConfigPath();
    virtual void SetUp()
    {
        system("rm -rf ./data/gmdb");
        system("mkdir ./data/gmdb");
        system("ipcrm -a");
        int ret = 0;
        ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        int ret = 0;
        ret = GmeClose(conn);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }
};

// 001.随机创建维度在1025~1536之间向量表，创建LPASMEM索引，删除索引，删除表格，循环执行10次，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_001, TestSize.Level1)
{
    int ret = 0;
    char sqlcmd[256] = {0};
    int64_t vectordim = 1025 + rand() % (1536 - 1025 + 1);
    (void)snprintf(sqlcmd, 256, "CREATE TABLE t_empty(id int primary key, repr float16vector(%d));", vectordim);
    string sqlcmd1 = sqlcmd;
    GmeSqlTestCtx sql1 = {sqlcmd1, GMERR_OK, {}};
    for (int i = 0; i < 10; i++) {
        ret = GmeSqlStepAndCheck(conn, &sql1, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建LPASMEM索引
        GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
        sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr L2) WITH (LPAS_OUT_DEGREE=89, "
                   "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除索引
        sql2.sql = "DROP INDEX t_empty.lpas_l2_idx;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删表
        sql2.sql = "DROP TABLE t_empty;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 002.创建1536维向量表，插入100组数据，创建LPASMEM索引，增删改向量表数据，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_002, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建LPASMEM索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1536;
    int insertnum = 100;
    InsertDataRandom(conn, tableName, dim1, 100);

    // 更新
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql2.sql = "DELETE FROM t_empty WHERE id <= 101;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.创建大于1536维向量表，预期建表失败，返回错误码
HWTEST_F(fp16vector_func_test, KBVector_028_003, TestSize.Level2)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {
        "CREATE TABLE t_empty(id int primary key, repr float16vector(1537));", GMERR_INVALID_VALUE, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.创建5个向量列，包含个随机1~1536维向量字段，至少有1个向量列维度大于1024，在随机1个向量字段创建索引，增删改向量表数据，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_004, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1), repr2 float16vector(10), "
                          "repr3 float16vector(100), repr4 float16vector(1024), repr5 float16vector(1536));",
        GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1;
    int dim2 = 10;
    int dim3 = 100;
    int dim4 = 1024;
    int dim5 = 1536;
    int insertnum = 100;
    InsertDataRandom2(conn, tableName, dim1, dim2, dim3, dim4, dim5, insertnum);

    // 更新
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "UPDATE t_empty SET repr1 = '[1.0]' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql2.sql = "DELETE FROM t_empty WHERE id <= 101;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.创建1536维向量表，插入100组数据，仅创建标量索引，INDEXD BY标量索引，order by向量距离，预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_005, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建标量索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX id_index ON t_empty (id);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1536;
    int insertnum = 100;
    InsertDataRandom(conn, tableName, dim1, 100);

    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql =
        "SELECT * FROM t_empty INDEXED COMBINE id_index 3 where id in (1,2,3) order by repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 006.创建1536维向量表，插入100组数据，仅创建标量索引，过滤条件和order by均包含向量距离，预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_006, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建标量索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX id_index ON t_empty (id);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1536;
    int insertnum = 100;
    InsertDataRandom(conn, tableName, dim1, 100);

    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql =
        "SELECT * FROM t_empty INDEXED COMBINE id_index 3 where id in (1,2,3) order by repr1 <=> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.创建1536维向量表，随机插入50~100组数据，创建LPASMEM索引，再删除LPASMEM索引，循环操作10次，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_007, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1536;
    int insertnum = 50 + rand() % (100 - 50 + 1);
    InsertDataRandom(conn, tableName, dim1, insertnum);

    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    for (int i = 0; i < 10; i++) {
        // 创建LPASMEM索引
        sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
                   "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除索引
        sql2.sql = "DROP INDEX t_empty.lpas_l2_idx;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.创表包含5个不同长度向量字段，均为1025~1536维度，随机一个向量字段创建和删除LPASMEM索引，循环10次，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_008, TestSize.Level1)
{
    int ret = 0;
    char sqlcmd[256] = {0};
    int64_t vectordim1 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim2 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim3 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim4 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim5 = 1025 + rand() % (1536 - 1025 + 1);
    (void)snprintf(sqlcmd, 256,
        "CREATE TABLE t_empty(id int primary key, repr1 float16vector(%d), repr2 float16vector(%d), repr3 "
        "float16vector(%d), repr4 float16vector(%d), repr5 float16vector(%d));",
        vectordim1, vectordim2, vectordim3, vectordim4, vectordim5);
    string sqlcmd1 = sqlcmd;

    GmeSqlTestCtx sql1 = {sqlcmd1, GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    for (int i = 0; i < 10; i++) {
        // 创建LPASMEM索引
        int64_t id = 1 + rand() % (5 - 1 + 1);
        sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr" + to_string(id) +
                   " L2) WITH (LPAS_OUT_DEGREE=89, LPAS_BUILD_SEARCH_LIST_SIZE =74);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除索引
        sql2.sql = "DROP INDEX t_empty.lpas_l2_idx;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.创多字段标向混合表，向量字段为1025~1536维度，创建LPASMEM索引，过滤条件和order by均包含向量距离查询，预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_009, TestSize.Level1)
{
    int ret = 0;
    // 建表
    GmeSqlTestCtx sql1 = {
        "CREATE TABLE t_empty(id int primary key, name TEXT, data BLOB, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建LPASMEM索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // selete
    int dim1 = 1536;
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "SELECT * FROM t_empty WHERE repr1 <-> '" + VecStr1 + "' ORDER BY repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 010.创多字段标向混合表，向量字段为1025~1536维度，创建LPASMEM索引，无过滤条件，order
// by向量距离+limit查询，预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_010, TestSize.Level1)
{
    int ret = 0;
    // 建表
    GmeSqlTestCtx sql1 = {
        "CREATE TABLE t_empty(id int primary key, name TEXT, data BLOB, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建LPASMEM索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // selete
    int dim1 = 1536;
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "SELECT * FROM t_empty ORDER BY repr1 <-> '" + VecStr1 + "' LIMIT 1;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 011.创多字段标向混合表，向量字段为1025~1536维度，创建标量索引和LPASMEM向量索引，过滤条件为标量，order
// by包含向量距离,预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_011, TestSize.Level1)
{
    int ret = 0;
    // 建表
    GmeSqlTestCtx sql1 = {
        "CREATE TABLE t_empty(id int primary key, name TEXT, data BLOB, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX id_index ON t_empty (id);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // selete
    int dim1 = 1536;
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "SELECT * FROM t_empty WHERE repr1 <-> '" + VecStr1 + "' ORDER BY repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 012.创建1536维向量表，随机插入50~100组数据，创建DISKANN索引，再删除DISKANN索引，循环操作10次，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_012, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1536;
    int64_t insertnum = 50 + rand() % (100 - 50 + 1);
    InsertDataRandom(conn, tableName, dim1, insertnum);

    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    for (int i = 0; i < 10; i++) {
        // 创建DISKANN索引
        sql2.sql = "CREATE INDEX diskann_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sql2.sql = "CREATE INDEX diskann_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除索引
        sql2.sql = "DROP INDEX t_empty.diskann_l2_repr1;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sql2.sql = "DROP INDEX t_empty.diskann_cosine_repr1;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 013.创表包含5个不同长度向量字段，均为1025~1536维度，随机一个向量字段创建和删除DISKANN索引，循环10次，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_013, TestSize.Level1)
{
    int ret = 0;
    char sqlcmd[256] = {0};
    int64_t vectordim1 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim2 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim3 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim4 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim5 = 1025 + rand() % (1536 - 1025 + 1);
    (void)snprintf(sqlcmd, 256,
        "CREATE TABLE t_empty(id int primary key, repr1 float16vector(%d), repr2 float16vector(%d), repr3 "
        "float16vector(%d), repr4 float16vector(%d), repr5 float16vector(%d));",
        vectordim1, vectordim2, vectordim3, vectordim4, vectordim5);
    string sqlcmd1 = sqlcmd;

    GmeSqlTestCtx sql1 = {sqlcmd1, GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    for (int i = 0; i < 10; i++) {
        // 创建索引
        int64_t id = 1 + rand() % (5 - 1 + 1);
        sql2.sql = "CREATE INDEX diskann_l2_repr1 ON t_empty USING GSDISKANN(repr" + to_string(id) + " L2);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sql2.sql = "CREATE INDEX diskann_cosine_repr1 ON t_empty USING GSDISKANN(repr" + to_string(id) + " COSINE);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除索引
        sql2.sql = "DROP INDEX t_empty.diskann_l2_repr1;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sql2.sql = "DROP INDEX t_empty.diskann_cosine_repr1;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 014.创多字段标向混合表，向量字段为1025~1536维度，创建DISKANN索引，过滤条件和order by均包含向量距离查询，预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_014, TestSize.Level1)
{
    int ret = 0;
    // 建表
    GmeSqlTestCtx sql1 = {
        "CREATE TABLE t_empty(id int primary key, name TEXT, data BLOB, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sql2.sql = "CREATE INDEX lpas_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // selete
    int dim1 = 1536;
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "SELECT * FROM t_empty WHERE repr1 <-> '" + VecStr1 + "' ORDER BY repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 015.创多字段标向混合表，向量字段为1025~1536维度，创建DISKANN索引，无过滤条件，order
// by向量距离+limit查询，预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_015, TestSize.Level1)
{
    int ret = 0;
    // 建表
    GmeSqlTestCtx sql1 = {
        "CREATE TABLE t_empty(id int primary key, name TEXT, data BLOB, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建LPASMEM索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX lpas_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // selete
    int dim1 = 1536;
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "SELECT * FROM t_empty ORDER BY repr1 <-> '" + VecStr1 + "' LIMIT 1;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 016.创多字段标向混合表，向量字段为1025~1536维度，创建标量索引和DISKANN向量索引，过滤条件为标量，order
// by包含向量距离,预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_016, TestSize.Level1)
{
    int ret = 0;
    // 建表
    GmeSqlTestCtx sql1 = {
        "CREATE TABLE t_empty(id int primary key, name TEXT, data BLOB, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建LPASMEM索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX id_index ON t_empty (id);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX lpas_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX lpas_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // selete
    int dim1 = 1536;
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "SELECT * FROM t_empty WHERE repr1 <-> '" + VecStr1 + "' ORDER BY repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 021.创多字段标向混合表，向量字段为1025~1536维度，创建标量索引和GSDISKANN向量索引，过滤条件为标量，order
// by包含向量距离,预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_021, TestSize.Level1)
{
    int ret = 0;
    // 建表
    GmeSqlTestCtx sql1 = {
        "CREATE TABLE t_empty(id int primary key, name TEXT, data BLOB, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX id_index ON t_empty (id);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX lpas_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX lpas_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // selete
    int dim1 = 1536;
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "SELECT * FROM t_empty WHERE repr1 <-> '" + VecStr1 + "' ORDER BY repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 022.创表带fp16向量字段，维度1024，增删改fp16字段向量数据，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_022, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1024));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1024;
    int insertnum = 100;
    InsertDataRandom(conn, tableName, dim1, 100);

    // 更新数据
    string VecStr1 = RandomFloat(1, 10, dim1);
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql2.sql = "DELETE FROM t_empty WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 023.创表带3个fp16向量字段，维度1~1536之间，增删改向量数据，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_023, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(10), repr2 float16vector(100), "
                          "repr3 float16vector(1024));",
        GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 10;
    int dim2 = 100;
    int dim3 = 1024;
    int insertnum = 100;
    InsertDataRandom3(conn, tableName, dim1, dim2, dim3, insertnum);

    // 更新数据
    string VecStr1 = RandomFloat(1, 10, dim1);
    string VecStr2 = RandomFloat(1, 10, dim2);
    string VecStr3 = RandomFloat(1, 10, dim3);
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "', repr2 = '" + VecStr2 + "', repr3 = '" + VecStr3 +
               "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql2.sql = "DELETE FROM t_empty WHERE id <= 101;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 024.创建4维带fp16向量字段表，查询语句通过GmeSqlBindFloat16Vector占位符绑定float16，创建disakann索引并进行向量距离查询，
// 通过GmeSqlColumnFloat16Vector获取float16向量值，通过GmeSqlFloat16VectorToFloatVector转换进行数据比对，预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_024, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1[] = {
        {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(4));", GMERR_OK, {}},
        {"INSERT INTO t_empty VALUES(1, '[2.40039,3.30078,4.10156,5.10156]');", GMERR_OK, {}},
        {"INSERT INTO t_empty VALUES(2, '[3.40039,4.30078,5.10156,6.10156]');", GMERR_OK, {}},
        {"INSERT INTO t_empty VALUES(3, '[4.40039,5.30078,6.10156,7.10156]');", GMERR_OK, {}},
        {"CREATE INDEX diskann_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);", GMERR_OK, {}},
        {"CREATE INDEX diskann_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);", GMERR_OK, {}},
    };
    ret = GmeSqlStepAndCheck(conn, sql1, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmeSqlStmtT *stmt = NULL;
    float bindVec[] = {1.0, 2.0, 3.0, 4.0};
    float16 queryVec[4] = {0};
    GmeSqlFloatVectorToFloat16Vector(bindVec, 4, queryVec);
    char *sql = (char *)"SELECT id, repr1, repr1 <-> ? FROM t_empty ORDER BY repr1 <-> ? LIMIT 1;";
    ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmeSqlBindFloat16Vector(stmt, 1, queryVec, 4, nullptr);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmeSqlBindFloat16Vector(stmt, 2, queryVec, 4, nullptr);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmeSqlStep(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    GmeDbValueT value1 = GmeSqlColumnValue(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(value1.type, GME_DB_DATATYPE_INTEGER);
    int64_t id = value1.value.longValue;
    AW_MACRO_EXPECT_EQ_INT(1, id);

    uint32_t vecDim = 0;
    const float16 *vec = GmeSqlColumnFloat16Vector(stmt, 1, &vecDim);
    float vec32[vecDim] = {0};
    GmeSqlFloat16VectorToFloatVector(vec, vecDim, vec32);

    std::ostringstream result;
    result << "[";
    for (int i = 0; i < vecDim; ++i) {
        result << vec32[i];
        if (i != vecDim - 1) {
            result << ",";  // 不是最后一个元素时添加逗号
        }
    }
    result << "]";

    ret = GmeSqlReset(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 删表
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 025.创建12维带fp16向量字段表，查询语句通过GmeSqlBindFloatVector占位符绑定float16，创建lpasmem索引并进行向量距离查询，
// 通过GmeSqlColumnFloat16Vector获取float16向量值，通过GmeSqlFloat16VectorToFloatVector转换进行数据比对，预期查询成功
HWTEST_F(fp16vector_func_test, KBVector_028_025, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1[] = {
        {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(12));", GMERR_OK, {}},
        {"INSERT INTO t_empty VALUES(1, "
         "'[1.11111,2.22222,3.33333,4.44444,5.55555,6.66666,7.77777,8.88888,9.99999,10.00000,11.11111,12.22222]');",
            GMERR_OK, {}},
        {"INSERT INTO t_empty VALUES(2, "
         "'[2.22222,3.33333,4.44444,5.55555,6.66666,7.77777,8.88888,9.99999,10.00000,11.11111,12.22222,13.33333]');",
            GMERR_OK, {}},
        {"INSERT INTO t_empty VALUES(3, "
         "'[3.33333,4.44444,5.55555,6.66666,7.77777,8.88888,9.99999,10.00000,11.11111,12.22222,13.33333,14.44444]');",
            GMERR_OK, {}},
        {"CREATE INDEX diskann_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);", GMERR_OK, {}},
        {"CREATE INDEX diskann_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);", GMERR_OK, {}},
    };
    ret = GmeSqlStepAndCheck(conn, sql1, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmeSqlStmtT *stmt = NULL;
    float bindVec[] = {1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0};
    float16 queryVec[12] = {0};
    GmeSqlFloatVectorToFloat16Vector(bindVec, 12, queryVec);
    char *sql = (char *)"SELECT id, repr1, repr1 <-> ? FROM t_empty ORDER BY repr1 <-> ? LIMIT 1;";
    ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmeSqlBindFloat16Vector(stmt, 1, queryVec, 12, nullptr);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmeSqlBindFloat16Vector(stmt, 2, queryVec, 12, nullptr);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmeSqlStep(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    GmeDbValueT value1 = GmeSqlColumnValue(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(value1.type, GME_DB_DATATYPE_INTEGER);
    int64_t id = value1.value.longValue;
    AW_MACRO_EXPECT_EQ_INT(1, id);

    uint32_t vecDim = 0;
    const float16 *vec = GmeSqlColumnFloat16Vector(stmt, 1, &vecDim);
    float vec32[vecDim] = {0};
    GmeSqlFloat16VectorToFloatVector(vec, vecDim, vec32);

    std::ostringstream result;
    result << "[";
    for (int i = 0; i < vecDim; ++i) {
        result << vec32[i];
        if (i != vecDim - 1) {
            result << ",";  // 不是最后一个元素时添加逗号
        }
    }
    result << "]";

    cout << result.str() << endl;
    ret = GmeSqlReset(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 删表
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 026.创表带fp16向量字段，维度8，随机插入50~100组数据，在同一个fp16向量字段上创建多个向量索引，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_026, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(8));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 8;
    int insertnum = 100;
    InsertDataRandom(conn, tableName, dim1, 100);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX diskann_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX diskann_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 027.创表带fp16向量字段，维度8，随机插入50~100组数据，创建diskann索引，删除索引再重建索引，循环10次，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_027, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(8));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 8;
    int insertnum = 100;
    InsertDataRandom(conn, tableName, dim1, 100);

    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    for (int i = 0; i < 10; i++) {
        // 创建索引
        sql2.sql = "CREATE INDEX diskann_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sql2.sql = "CREATE INDEX diskann_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除索引
        sql2.sql = "DROP INDEX t_empty.diskann_l2_repr1;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sql2.sql = "DROP INDEX t_empty.diskann_cosine_repr1;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 028.创表带fp16向量字段，维度20，随机插入50~100组数据，创建lpasmem索引，删除索引再重建索引，循环10次，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_028, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(20));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 20;
    int insertnum = 100;
    InsertDataRandom(conn, tableName, dim1, insertnum);

    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    for (int i = 0; i < 10; i++) {
        // 创建索引
        sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
                   "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除索引
        sql2.sql = "DROP INDEX t_empty.lpas_l2_idx;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 029.创表带fp16向量字段，维度20，随机插入50~100组数据，创建diskann索引后增删改数据，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_029, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(20));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 20;
    int insertnum = 50;
    InsertDataRandom(conn, tableName, dim1, insertnum);

    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    // 创建索引
    sql2.sql = "CREATE INDEX diskann_l2_repr1 ON t_empty USING GSDISKANN(repr1 L2);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sql2.sql = "CREATE INDEX diskann_cosine_repr1 ON t_empty USING GSDISKANN(repr1 COSINE);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    insertnum = 50;
    int startId = 50;
    InsertDataRandom(conn, tableName, dim1, insertnum, startId);

    // 更新
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql2.sql = "DELETE FROM t_empty WHERE id <= 101;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 030.创表带fp16向量字段，维度20，随机插入50~100组数据，创建lpasmem索引后增删改数据，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_030, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(20));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 20;
    int insertnum = 50;
    InsertDataRandom(conn, tableName, dim1, 50);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    insertnum = 50;
    int startId = 50;
    InsertDataRandom(conn, tableName, dim1, insertnum, startId);

    // 更新
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql2.sql = "DELETE FROM t_empty WHERE id <= 101;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 031.创表带fp16向量字段，维度1024，随机插入50~100组数据，创建lpasmem索引后增删改数据，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_031, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1024));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1024;
    int insertnum = 50;
    InsertDataRandom(conn, tableName, dim1, 50);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    insertnum = 50;
    int startId = 50;
    InsertDataRandom(conn, tableName, dim1, insertnum, startId);

    // 更新
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql2.sql = "DELETE FROM t_empty WHERE id <= 101;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 032.创表带fp16向量字段，维度1536，随机插入50~100组数据，创建lpasmem索引后增删改数据，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_032, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    char tableName[] = "t_empty";
    int dim1 = 1536;
    int insertnum = 50;
    InsertDataRandom(conn, tableName, dim1, 50);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    insertnum = 50;
    int startId = 50;
    InsertDataRandom(conn, tableName, dim1, insertnum, startId);

    // 更新
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql2.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql2.sql = "DELETE FROM t_empty WHERE id <= 101;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 033.创表带5个不同维度fp16向量字段，随机插入50~100组数据，在5个向量字段上分布创建lpasmem索引，预期成功
HWTEST_F(fp16vector_func_test, KBVector_028_033, TestSize.Level1)
{
    int ret = 0;
    char sqlcmd[256] = {0};
    int64_t vectordim1 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim2 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim3 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim4 = 1025 + rand() % (1536 - 1025 + 1);
    int64_t vectordim5 = 1025 + rand() % (1536 - 1025 + 1);
    (void)snprintf(sqlcmd, 256,
        "CREATE TABLE t_empty(id int primary key, repr1 float16vector(%d), repr2 float16vector(%d), repr3 "
        "float16vector(%d), repr4 float16vector(%d), repr5 float16vector(%d));",
        vectordim1, vectordim2, vectordim3, vectordim4, vectordim5);
    string sqlcmd1 = sqlcmd;
    GmeSqlTestCtx sql1 = {sqlcmd1, GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    for (int i = 0; i < 10; i++) {
        // 创建索引
        int64_t id = 1 + rand() % (5 - 1 + 1);
        sql2.sql = "CREATE INDEX diskann_l2_repr1 ON t_empty USING GSDISKANN(repr" + to_string(id) + " L2);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sql2.sql = "CREATE INDEX diskann_cosine_repr1 ON t_empty USING GSDISKANN(repr" + to_string(id) + " COSINE);";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除索引
        sql2.sql = "DROP INDEX t_empty.diskann_l2_repr1;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sql2.sql = "DROP INDEX t_empty.diskann_cosine_repr1;";
        ret = GmeSqlStepAndCheck(conn, &sql2, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    sql2.sql = "DROP TABLE t_empty;";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 034.创建float16vector向量表，创建lpasmeml2索引，
// 进行向量查询，通过V$CATA_VERTEX_LABEL_INFO视图SCHEMA_INFO字段查询fp16向量信息
HWTEST_F(fp16vector_func_test, KBVector_028_034, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(10));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ViewResult resultSet;
    const char *viewSql = "select * from 'V$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME = 'T_EMPTY';";
    ret = GmeSqlExecute(conn, viewSql, SqlViewResultCallBack, &resultSet, nullptr);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    string schemaInfo = GetValueByColName(&resultSet, "SCHEMA_INFO");
    char schemaInfoarr[schemaInfo.size() + 1];
    std::copy(schemaInfo.begin(), schemaInfo.end(), schemaInfoarr);
    schemaInfoarr[schemaInfo.size()] = '\0';
    char expectstring[] = "float16vector";
    EXPECT_STRNE(NULL, strstr(schemaInfoarr, expectstring));
}

void *InsertUpdateDelete1(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    // 插入数据
    int ret = 0;
    int dim1 = 256;
    int insertnum = 500;
    char tableName[] = "t_empty";
    InsertDataRandom(targs->conn, tableName, dim1, insertnum);

    // 更新
    GmeSqlTestCtx sql3 = {"", GMERR_OK, {}};
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql3.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(targs->conn, &sql3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql3.sql = "DELETE FROM t_empty WHERE id <= 500;";
    ret = GmeSqlStepAndCheck(targs->conn, &sql3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return NULL;
}

void *Selete1(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    // selete
    int ret = 0;
    int dim1 = 256;
    string VecStr1 = RandomFloat(1, 10, dim1);
    GmeSqlTestCtx sql4 = {"", GMERR_OK, {}};
    sql4.sql = "SELECT * FROM t_empty WHERE repr1 <-> '" + VecStr1 + "' ORDER BY repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(targs->conn, &sql4, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return NULL;
}

// 035.创建256维度float16vector向量表，随机插入50~500组数据，创建LPASMEM索引，起2个并发线程，1个线程增删改数据，1个线程查询数据，预期功能正常无core
HWTEST_F(fp16vector_func_test, KBVector_028_035, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(256));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    args[0].conn = conn;
    ret = pthread_create(&threads[0], NULL, InsertUpdateDelete1, &args[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&threads[1], NULL, Selete1, &args[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(threads[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(threads[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *InsertUpdateDelete2(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    // 插入数据
    int ret = 0;
    int dim1 = 1536;
    int insertnum = 500;
    char tableName[] = "t_empty";
    InsertDataRandom(targs->conn, tableName, dim1, insertnum);

    // 更新
    GmeSqlTestCtx sql3 = {"", GMERR_OK, {}};
    string VecStr1 = RandomFloat(1, 10, dim1);
    sql3.sql = "UPDATE t_empty SET repr1 = '" + VecStr1 + "' WHERE id = 100;";
    ret = GmeSqlStepAndCheck(targs->conn, &sql3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删数据
    sql3.sql = "DELETE FROM t_empty WHERE id <= 500;";
    ret = GmeSqlStepAndCheck(targs->conn, &sql3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return NULL;
}

void *Selete2(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    // selete
    int ret = 0;
    int dim1 = 1536;
    string VecStr1 = RandomFloat(1, 10, dim1);
    GmeSqlTestCtx sql4 = {"", GMERR_OK, {}};
    sql4.sql = "SELECT * FROM t_empty WHERE repr1 <-> '" + VecStr1 + "' ORDER BY repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(targs->conn, &sql4, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return NULL;
}

// 036.创建1536维度float16vector向量表，随机插入50~500组数据，创建LPASMEM索引，起2个并发线程，1个线程增删改数据，1个线程查询数据，预期功能正常无core
HWTEST_F(fp16vector_func_test, KBVector_028_036, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1536));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    args[0].conn = conn;
    ret = pthread_create(&threads[0], NULL, InsertUpdateDelete2, &args[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&threads[1], NULL, Selete2, &args[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(threads[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(threads[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *Selete3(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    // selete
    int ret = 0;
    int dim1 = 1024;
    string VecStr1 = RandomFloat(1, 10, dim1);
    GmeSqlTestCtx sql4 = {"", GMERR_OK, {}};
    sql4.sql = "SELECT * FROM t_empty WHERE repr1 <-> '" + VecStr1 + "' ORDER BY repr1 <-> '" + VecStr1 + "';";
    ret = GmeSqlStepAndCheck(targs->conn, &sql4, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return NULL;
}

// 037.创建1024维度float16vector向量表，随机插入50~500组数据，创建LPASMEM索引，起10个并发线程并行进行向量查询，预期功能正常无core
HWTEST_F(fp16vector_func_test, KBVector_028_037, TestSize.Level1)
{
    int ret = 0;
    GmeSqlTestCtx sql1 = {"CREATE TABLE t_empty(id int primary key, repr1 float16vector(1024));", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &sql1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建索引
    GmeSqlTestCtx sql2 = {"", GMERR_OK, {}};
    sql2.sql = "CREATE INDEX lpas_l2_idx ON t_empty USING gSLPASMEM(repr1 L2) WITH (LPAS_OUT_DEGREE=89, "
               "LPAS_BUILD_SEARCH_LIST_SIZE =74);";
    ret = GmeSqlStepAndCheck(conn, &sql2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadnum = 10;
    pthread_t threads[threadnum];
    ThreadArgsT args[threadnum];
    for (int i = 0; i < threadnum; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, Selete3, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < threadnum; i++) {
        ret = pthread_join(threads[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
