/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 * Description: diskann索引增量更新数据-插入更新单条数据测试 - GMETEST
 * Author: swj
 * Create: 2024-11-12
 */

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include <random>

using namespace std;
using namespace testing::ext;

GmeSqlTestCtx DropTable[] = {
    {"DROP TABLE T1;", GMERR_OK, {}},
};

const char *configPath = EmbSqlGetConfigPath();
class TestDiskAnnSingleData : public testing::Test {
public:
    GmeConnT *g_db = NULL;
    const string L2 = "L2";
    const string COS = "COSINE";
    const string tableName = "T1";
    const string indexName = "index_name";
    virtual void SetUp()
    {
        system("rm -rf ./data/gmdb");
        system("mkdir -p ./data/gmdb");
        system("ipcrm -a");
        int ret = GmeOpen(configPath, GME_OPEN_CREATE, &g_db);
        EXPECT_EQ(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        int ret = GmeSqlStepAndCheck(g_db, DropTable, 1);
        EXPECT_EQ(T_OK, ret);
        ret = GmeClose(g_db);
        EXPECT_EQ(GMERR_OK, ret);
    }
};

void CreateTableWithDim(GmeConnT *db, string tableName, int dim)
{
    struct GmeSqlTestCtx createTable;
    createTable.sql =
        "CREATE TABLE " + tableName + "(id int unique, label varchar(20), repr floatvector(" + to_string(dim) + "));";
    createTable.status = GMERR_OK;
    createTable.output = {};
    int ret = GmeSqlStepAndCheck(db, &createTable, 1);
    ASSERT_EQ(ret, T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData001
 * @tc.desc:  insert single data, dim:1.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData001, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 1);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: insert data.
     * @tc.expected: step3. Return T_OK
     */
    string InsertReprVal = RandomFloat(1, 10, 1);
    GmeSqlTestCtx InsertData[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 1), T_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: step4. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 WHERE repr <-> '" + InsertReprVal + "' < 0.1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
        {"SELECT * FROM T1 WHERE id=1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: step5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData002
 * @tc.desc:  insert single data, dim:1024.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData002, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 1024);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: insert data.
     * @tc.expected: step3. Return T_OK
     */
    string reprVal = RandomFloat(1.0, 5.0, 1024);
    GmeSqlTestCtx InsertData[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + reprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 1), T_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: step4. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 WHERE repr <=> '" + reprVal + "' < 0.1;", GMERR_OK, {{"1", "新闻", reprVal}}},
        {"SELECT * FROM T1 WHERE id=1;", GMERR_OK, {{"1", "新闻", reprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: step5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData003
 * @tc.desc:  insert single data, Same vector data with the diff id,dim:128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData003, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: insert data.
     * @tc.expected: step3. Return T_OK/GMERR_UNIQUE_VIOLATION
     */
    string reprVal = RandomFloat(1.0, 5.0, 128);
    GmeSqlTestCtx InsertData[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + reprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 1), T_OK);

    string reprVal1 = RandomFloat(1.0, 15.0, 128);
    GmeSqlTestCtx InsertData1[] = {"INSERT INTO T1 VALUES(1, '标签', '" + reprVal1 + "');", GMERR_UNIQUE_VIOLATION, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData1, 1), GMERR_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: step4. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 WHERE repr <-> '" + reprVal + "' < 0.1 AND id = 1;", GMERR_OK, {{"1", "新闻", reprVal}}},
        {"SELECT * FROM T1;", GMERR_OK, {{"1", "新闻", reprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: step5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData004
 * @tc.desc:  insert single data, Same vector data with the same id, dim:128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData004, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: insert data.
     * @tc.expected: step3. Return T_OK/GMERR_UNIQUE_VIOLATION
     */
    string reprVal = RandomFloat(1.0, 25.0, 128);
    GmeSqlTestCtx InsertData[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + reprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 1), T_OK);

    GmeSqlTestCtx InsertData1[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + reprVal + "');", GMERR_UNIQUE_VIOLATION, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData1, 1), GMERR_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: step4. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 WHERE repr <=> '" + reprVal + "' < 0.1 AND id = 1;", GMERR_OK, {{"1", "新闻", reprVal}}},
        {"SELECT * FROM T1 WHERE id=1;", GMERR_OK, {{"1", "新闻", reprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: step5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData005
 * @tc.desc:  insert single data and dim: 1/129/128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData005, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: insert data.
     * @tc.expected: step3. Return T_OK/GMERR_UNIQUE_VIOLATION
     */
    string reprVal_128 = RandomFloat(1.0, 35.0, 128);
    string reprVal_129 = RandomFloat(1.0, 5.0, 129);
    GmeSqlTestCtx InsertData[] = {
        {"INSERT INTO T1 VALUES(1, '新闻', '[1.1]');", GMERR_DATATYPE_MISMATCH, {}},
        {"INSERT INTO T1 VALUES(1, '新闻', '" + reprVal_128 + "');", GMERR_OK, {}},
        {"INSERT INTO T1 VALUES(1, '新闻', '" + reprVal_129 + "');", GMERR_DATATYPE_MISMATCH, {}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 3), T_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: step4. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 WHERE repr <-> '" + reprVal_128 + "' < 0.1;", GMERR_OK, {{"1", "新闻", reprVal_128}}},
        {"SELECT * FROM T1 WHERE id=1;", GMERR_OK, {{"1", "新闻", reprVal_128}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: step5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData006
 * @tc.desc:  empty table update data and dim: 128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData006, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: update data.
     * @tc.expected: step3. Return T_OK
     */
    string reprVal = RandomFloat(1.0, 45.0, 128);
    GmeSqlTestCtx UpdateData[] = {"UPDATE T1 SET repr = '" + reprVal + "' WHERE id = 1;", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData, 1), T_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: step4. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 ORDER BY repr <-> '" + reprVal + "' LIMIT 1;", GMERR_OK, {}},
        {"SELECT * FROM T1;", GMERR_OK, {}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: step5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData007
 * @tc.desc:  Update after delete and dim: 128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData007, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: insert data.
     * @tc.expected: step3. Return T_OK
     */
    string reprVal = RandomFloat(1.0, 25.0, 128);
    GmeSqlTestCtx InsertData[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + reprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 1), T_OK);

    /**
     * @tc.steps4: delete data.
     * @tc.expected: step4. Return T_OK
     */
    GmeSqlTestCtx DeleteData[] = {"DELETE FROM T1 WHERE id = 1;", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, DeleteData, 1), T_OK);

    /**
     * @tc.steps5: update data.
     * @tc.expected: step5. Return T_OK
     */
    string UpdateReprVal = RandomFloat(1.0, 15.0, 128);
    GmeSqlTestCtx UpdateData[] = {"UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 1;", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData, 1), T_OK);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 ORDER BY repr <=> '" + reprVal + "' LIMIT 1;", GMERR_OK, {}},
        {"SELECT * FROM T1 WHERE id=1;", GMERR_OK, {}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps7: close db then open.
     * @tc.expected: step7. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps8: check data.
     * @tc.expected: step8. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData008
 * @tc.desc:  Update data that does not exist and dim: 128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData008, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: insert data.
     * @tc.expected: step3. Return T_OK
     */
    for (int i = 1; i <= 10; i++) {
        string reprVal = RandomFloat(1.0, 45.0, 128);
        GmeSqlTestCtx InsertData[] = {
            "INSERT INTO T1 VALUES(" + to_string(i) + ", '标签', '" + reprVal + "');", GMERR_OK, {}};
        ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 1), T_OK);
    }

    /**
     * @tc.steps4: update data.
     * @tc.expected: step4. Return T_OK
     */
    string UpdateReprVal = RandomFloat(1.0, 55.0, 128);
    GmeSqlTestCtx UpdateData[] = {"UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 11;", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData, 1), T_OK);

    /**
     * @tc.steps5: check data.
     * @tc.expected: step5. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 WHERE repr <-> '" + UpdateReprVal + "' < 0.1;", GMERR_OK, {}},
        {"SELECT * FROM T1 WHERE id=11;", GMERR_OK, {}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps6: close db then open.
     * @tc.expected: step6. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps7: check data.
     * @tc.expected: step7. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData009
 * @tc.desc:  insert and update data and dim: 128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData009, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: insert data.
     * @tc.expected: step3. Return T_OK
     */
    string reprVal = RandomFloat(1.0, 25.0, 128);
    GmeSqlTestCtx InsertData[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + reprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 1), T_OK);

    /**
     * @tc.steps4: update data.
     * @tc.expected: step4. Return T_OK
     */
    string UpdateReprVal = RandomFloat(1.0, 25.0, 128);
    GmeSqlTestCtx UpdateData[] = {"UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 1;", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData, 1), T_OK);

    /**
     * @tc.steps5: check data.
     * @tc.expected: step5. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 WHERE repr <-> '" + UpdateReprVal + "' < 0.1;", GMERR_OK, {{"1", "新闻", UpdateReprVal}}},
        {"SELECT * FROM T1;", GMERR_OK, {{"1", "新闻", UpdateReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 1), T_OK);

    /**
     * @tc.steps6: close db then open.
     * @tc.expected: step6. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps7: check data.
     * @tc.expected: step7. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 1), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData010
 * @tc.desc:  update data and dim: 1/129/128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData010, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: update data, dim:1.
     * @tc.expected: step3. Return T_OK
     */
    string UpdateReprVal = RandomFloat(1, 10, 1);
    GmeSqlTestCtx UpdateData[] = {
        "UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 1;", GMERR_DATATYPE_MISMATCH, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData, 1), T_OK);

    /**
     * @tc.steps4: update data, dim:129.
     * @tc.expected: step4. Return T_OK
     */
    UpdateReprVal = RandomFloat(1.0, 65.0, 129);
    GmeSqlTestCtx UpdateData_1[] = {
        "UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 2;", GMERR_DATATYPE_MISMATCH, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData_1, 1), T_OK);

    /**
     * @tc.steps5: update data, dim:128.
     * @tc.expected: step5. Return T_OK
     */
    UpdateReprVal = RandomFloat(1.0, 75.0, 128);
    GmeSqlTestCtx UpdateData_2[] = {"UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 3;", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData_2, 1), T_OK);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 WHERE repr <=> '" + UpdateReprVal + "' < 0.1;", GMERR_OK, {}},
        {"SELECT * FROM T1;", GMERR_OK, {}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps6: close db then open.
     * @tc.expected: step6. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps7: check data.
     * @tc.expected: step7. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData011
 * @tc.desc:  update data and dim: 1/129/128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData011, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: update data, dim:1.
     * @tc.expected: step3. Return T_OK
     */
    string UpdateReprVal = RandomFloat(1, 10, 1);
    GmeSqlTestCtx UpdateData[] = {
        "UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 1;", GMERR_DATATYPE_MISMATCH, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData, 1), T_OK);

    /**
     * @tc.steps4: update data, dim:129.
     * @tc.expected: step4. Return T_OK
     */
    UpdateReprVal = RandomFloat(1.0, 85.0, 129);
    GmeSqlTestCtx UpdateData_1[] = {
        "UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 2;", GMERR_DATATYPE_MISMATCH, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData_1, 1), T_OK);

    /**
     * @tc.steps5: insert data, dim:128.
     * @tc.expected: step5. Return T_OK
     */
    string InsertReprVal = RandomFloat(1.0, 55.0, 128);
    GmeSqlTestCtx UpdateData_2[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData_2, 1), T_OK);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 ORDER BY repr <-> '" + InsertReprVal + "' LIMIT 1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
        {"SELECT * FROM T1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps7: close db then open.
     * @tc.expected: step7. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps8: check data.
     * @tc.expected: step8. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData012
 * @tc.desc:  update data and dim: 1/1024/1025.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData012, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 1024);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps3: update data, dim:1.
     * @tc.expected: step3. Return T_OK
     */
    string UpdateReprVal = RandomFloat(1, 10, 1);
    GmeSqlTestCtx UpdateData[] = {
        "UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 1;", GMERR_DATATYPE_MISMATCH, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData, 1), T_OK);

    /**
     * @tc.steps4: update data, dim:1025.
     * @tc.expected: step4. Return T_OK
     */
    UpdateReprVal = RandomFloat(1.0, 5.0, 1025);
    GmeSqlTestCtx UpdateData_1[] = {
        "UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE id = 1;", GMERR_DATATYPE_MISMATCH, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, UpdateData_1, 1), T_OK);

    /**
     * @tc.steps5: delete data.
     * @tc.expected: step5. Return T_OK
     */
    GmeSqlTestCtx DeleteData[] = {"DELETE FROM T1 WHERE id = 1;", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, DeleteData, 1), T_OK);

    /**
     * @tc.steps6: check data.
     * @tc.expected: step6. Return T_OK
     */
    UpdateReprVal = RandomFloat(1.0, 5.0, 1024);
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 ORDER BY repr <-> '" + UpdateReprVal + "' LIMIT 1;", GMERR_OK, {}},
        {"SELECT * FROM T1;", GMERR_OK, {}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 1), T_OK);

    /**
     * @tc.steps7: insert data, dim:1024.
     * @tc.expected: step7. Return T_OK
     */
    string InsertReprVal = RandomFloat(1.0, 5.0, 1024);
    GmeSqlTestCtx InsertData[] = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, InsertData, 1), T_OK);

    /**
     * @tc.steps8: check data.
     * @tc.expected: step8. Return T_OK
     */
    GmeSqlTestCtx QueryData1[] = {
        {"SELECT * FROM T1 ORDER BY repr <=> '" + InsertReprVal + "' LIMIT 1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
        {"SELECT * FROM T1 WHERE id=1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);

    /**
     * @tc.steps9: close db then open.
     * @tc.expected: step9. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps10: check data.
     * @tc.expected: step10. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData013
 * @tc.desc:  insert data and dim: 128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData013, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    struct GmeSqlTestCtx createTable;
    createTable.sql = "CREATE TABLE T1(id int, label varchar(20), repr floatvector(128));";
    createTable.status = GMERR_OK;
    createTable.output = {};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &createTable, 1), T_OK);

    /**
     * @tc.steps2: create diskann.
     * @tc.expected: step2. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, "T1", indexName, L2, COS);

    /**
     * @tc.steps3: Insert a 128-dime data repeatedly.
     * @tc.expected: step3. Return T_OK
     */
    const string InsertReprVal = RandomFloat(1.0, 10.0, 128);
    GmeSqlTestCtx insertData = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData, 1), GMERR_OK);
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData, 1), GMERR_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: steps4. Return T_OK
     */
    GmeSqlTestCtx QueryData1[] = {
        {"SELECT * FROM T1 ORDER BY repr <-> '" + InsertReprVal + "' LIMIT 2;", GMERR_OK,
            {{"1", "新闻", InsertReprVal}, {"1", "新闻", InsertReprVal}}},
        {"SELECT * FROM T1;", GMERR_OK, {{"1", "新闻", InsertReprVal}, {"1", "新闻", InsertReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: steps5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: steps6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData014
 * @tc.desc:  preset and insert data and dim: 128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData014, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: preset data.
     * @tc.expected: step2. Return T_OK
     */
    string InsertReprValPreset = RandomFloat(1.0, 10.0, 128);
    GmeSqlTestCtx insertData = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprValPreset + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData, 1), GMERR_OK);

    /**
     * @tc.steps3: create diskann.
     * @tc.expected: step3. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps4: Insert a 128-dime data.
     * @tc.expected: step4. Return T_OK
     */
    string InsertReprVal = RandomFloat(1.0, 10.0, 128);
    insertData = {"INSERT INTO T1 VALUES(2, '新闻', '" + InsertReprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData, 1), GMERR_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: steps4. Return T_OK
     */
    GmeSqlTestCtx QueryData[] = {
        {"SELECT * FROM T1 ORDER BY repr <=> '" + InsertReprVal + "' LIMIT 1;", GMERR_OK, {{"2", "新闻", InsertReprVal}}},
        {"SELECT * FROM T1;", GMERR_OK, {{"1", "新闻", InsertReprValPreset}, {"2", "新闻", InsertReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: steps5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: steps6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData015
 * @tc.desc:  preset and insert data and dim: 128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData015, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: preset data.
     * @tc.expected: step2. Return T_OK
     */
    string InsertReprVal = RandomFloat(1.0, 10.0, 128);
    GmeSqlTestCtx insertData = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData, 1), GMERR_OK);

    /**
     * @tc.steps3: create diskann.
     * @tc.expected: step3. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps4: Insert a 128-dime data.
     * @tc.expected: step4. Return T_OK
     */
    GmeSqlTestCtx insertData1 = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprVal + "');", GMERR_UNIQUE_VIOLATION, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData1, 1), GMERR_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: steps4. Return T_OK
     */
    GmeSqlTestCtx QueryData1[] = {
        {"SELECT * FROM T1 WHERE repr <=> '" + InsertReprVal + "' < 0.1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
        {"SELECT * FROM T1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: steps5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: steps6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData016
 * @tc.desc:  preset and insert data and dim: 1024.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData016, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 1024);

    /**
     * @tc.steps2: preset data.
     * @tc.expected: step2. Return T_OK
     */
    string InsertReprVal = RandomFloat(1.0, 10.0, 1024);
    GmeSqlTestCtx presetData = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &presetData, 1), GMERR_OK);

    /**
     * @tc.steps3: create diskann.
     * @tc.expected: step3. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps4: Insert a 128/1025-dime data.
     * @tc.expected: step4. Return T_OK
     */
    string ReprVal = RandomFloat(1.0, 10.0, 128);
    GmeSqlTestCtx insertData = {"INSERT INTO T1 VALUES(2, '新闻', '" + ReprVal + "');", GMERR_DATATYPE_MISMATCH, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData, 1), GMERR_OK);

    ReprVal = RandomFloat(1.0, 10.0, 1025);
    insertData = {"INSERT INTO T1 VALUES(3, '新闻', '" + ReprVal + "');", GMERR_DATATYPE_MISMATCH, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData, 1), GMERR_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: steps4. Return T_OK
     */
    GmeSqlTestCtx QueryData1[] = {
        {"SELECT * FROM T1 ORDER BY repr <=> '" + InsertReprVal + "' LIMIT 1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
        {"SELECT * FROM T1;", GMERR_OK, {{"1", "新闻", InsertReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: steps5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: steps6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);
}

/**
 * @tc.name: TestDiskAnnSingleData017
 * @tc.desc:  preset and insert data and dim: 128.
 * @tc.type: FUNC
 * @tc.author: mengchuyi
 */
HWTEST_F(TestDiskAnnSingleData, TestDiskAnnSingleData017, TestSize.Level1)
{
    /**
     * @tc.steps1: create table.
     * @tc.expected: step1. Return T_OK
     */
    CreateTableWithDim(g_db, tableName, 128);

    /**
     * @tc.steps2: preset data.
     * @tc.expected: step2. Return T_OK
     */
    string InsertReprVal = RandomFloat(1.0, 10.0, 128);
    GmeSqlTestCtx insertData = {"INSERT INTO T1 VALUES(1, '新闻', '" + InsertReprVal + "');", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &insertData, 1), GMERR_OK);

    /**
     * @tc.steps3: create diskann.
     * @tc.expected: step3. Return T_OK
     */
    int VecType = CreateDiskAnn(g_db, tableName, indexName, L2, COS);

    /**
     * @tc.steps4: update a 128-dime data.
     * @tc.expected: step4. Return T_OK
     */
    string UpdateReprVal = RandomFloat(1.0, 10.0, 128);
    GmeSqlTestCtx UpdateData = {
        "UPDATE T1 SET repr = '" + UpdateReprVal + "' WHERE repr <=> '" + InsertReprVal + "' < 0.1;", GMERR_OK, {}};
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, &UpdateData, 1), GMERR_OK);

    /**
     * @tc.steps4: check data.
     * @tc.expected: steps4. Return T_OK
     */
    GmeSqlTestCtx QueryData1[] = {
        {"SELECT * FROM T1 WHERE repr <=> '" + UpdateReprVal + "' < 0.1;", GMERR_OK, {{"1", "新闻", UpdateReprVal}}},
        {"SELECT * FROM T1;", GMERR_OK, {{"1", "新闻", UpdateReprVal}}},
    };
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);

    /**
     * @tc.steps5: close db then open.
     * @tc.expected: steps5. Return T_OK
     */
    g_db = CloseAndOpenDB(g_db);

    /**
     * @tc.steps6: check data.
     * @tc.expected: steps6. Return GMERR_OK
     */
    ASSERT_EQ(GmeSqlStepAndCheck(g_db, QueryData1, 2), T_OK);
}
