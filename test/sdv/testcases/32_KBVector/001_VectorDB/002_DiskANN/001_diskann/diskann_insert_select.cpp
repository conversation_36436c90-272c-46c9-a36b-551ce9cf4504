/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: GME SQL SELECT 向量表做DML操作后查询
 * Author: 
 * Create: 2024-11-19
 */

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"

using namespace std;
using namespace testing::ext;

GmeSqlTestCtx sqlSetup[] = {
    // 创建表,预置数据
    {"CREATE TABLE t_reprs(id int primary key, repr1 floatvector(3), repr2 floatvector(4));", GMERR_OK, {}},
    {"CREATE TABLE t_repr(id int unique, name text, repr floatvector(3));", GMERR_OK, {}},
    {"INSERT INTO t_reprs VALUES(1, '[2.4,3.3,4.1]', '[1,2,3,4]');", GMERR_OK, {}},
    {"INSERT INTO t_reprs VALUES(2, '[4.4,3.3,7.1]', '[2.0,3.0,4.0,5.0]');", GMERR_OK, {}},
    {"INSERT INTO t_reprs VALUES(3, '[3.7,46.7,24.1]', '[+3.0,4.,5,6]');", GMERR_OK, {}},
    {"INSERT INTO t_reprs VALUES(4, '[4.1,5.1,2.1]', '[2,3,5,0]');", GMERR_OK, {}},
    {"INSERT INTO t_repr VALUES(1, '0', '[2.4,3.3,4.1]');", GMERR_OK, {}},
    {"INSERT INTO t_repr VALUES(2, '1', '[4.4,3.3,7.1]');", GMERR_OK, {}},
    {"INSERT INTO t_repr VALUES(3, '2', '[3.7,46.7,24.1]');", GMERR_OK, {}},
    {"INSERT INTO t_repr VALUES(4, '3', '[4.1,5.1,2.1]');", GMERR_OK, {}},
    {"CREATE INDEX diskann_cos_idx1 ON t_reprs USING GSDISKANN(repr1 COSINE);", GMERR_OK, {}},
    {"CREATE INDEX diskann_l2_idx1 ON t_reprs USING GSDISKANN(repr2 L2);", GMERR_OK, {}},
    {"CREATE INDEX diskann_cos_idx1 ON t_repr USING GSDISKANN(repr COSINE);", GMERR_OK, {}},
    {"CREATE INDEX diskann_l2_idx1 ON t_repr USING GSDISKANN(repr L2);", GMERR_OK, {}},
};

GmeSqlTestCtx sqlRelease[] = {
    // 删除表
    {"DROP TABLE t_reprs;", GMERR_OK, {}},
    {"DROP TABLE t_repr;", GMERR_OK, {}},
};

class diskann_insert_select : public testing::Test {
public:
    GmeConnT *conn = NULL;
    char *configPath = EmbSqlGetConfigPath();
    virtual void SetUp()
    {
        system("rm -rf ./data/gmdb");
#ifdef COMPATIBILITY_2024_JULY
    EXPECT_NE(system("cp -r ../../Compatibility_history_db/July/data ."), -1);
#else
        system("mkdir -p ./data/gmdb");
#endif
        system("ipcrm -a"); 
        int ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeSqlStepAndCheck(conn, sqlSetup, 14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        int ret = GmeSqlStepAndCheck(conn, sqlRelease, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmeClose(conn);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }
};

// t_reprs表向量列1、2分别创建COSINE、L2索引，用向量列索引类型的条件升序后查找，预期成功
HWTEST_F(diskann_insert_select, INSERT_VECTOR_SELECT_WHERE_ORDERBY_001, TestSize.Level2)
{
    ComWriteLog(LOG_STEP, "test start.");
    struct GmeSqlTestCtx sqlTests001;
    sqlTests001.sql = "SELECT repr1 FROM t_reprs ORDER BY repr1 <=> '[2,3,4]' limit 3;";
    sqlTests001.status = GMERR_OK;
    sqlTests001.output = {{"[2.4,3.3,4.1]"}, {"[4.4, 3.3, 7.1]"}, {"[4.1, 5.1, 2.1]"}};
    int ret = GmeSqlStepAndCheck(conn, &sqlTests001, 1);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    ComWriteLog(LOG_STEP, "test start.");
    struct GmeSqlTestCtx sqlTests002;
    sqlTests002.sql = "SELECT repr2 FROM t_reprs ORDER BY repr2 <-> '[2,3,4,5]' limit 3;";
    sqlTests002.status = GMERR_OK;
    sqlTests002.output = {{"[2.0, 3.0, 4.0, 5.0]"}, {"[1, 2, 3, 4]"}, {"[3.0, 4, 5, 6]"}};
    ret = GmeSqlStepAndCheck(conn, &sqlTests002, 1);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
}

// t_reprs表向量列1、2分别创建COSINE、L2索引，支持非向量索引列跨distType查询，预期成功
HWTEST_F(diskann_insert_select, INSERT_VECTOR_SELECT_WHERE_ORDERBY_002, TestSize.Level2)
{
    ComWriteLog(LOG_STEP, "test start.");
    struct GmeSqlTestCtx sqlTests001;
    sqlTests001.sql = "SELECT repr1 FROM t_reprs ORDER BY repr1 <-> '[2,3,4]' limit 3;";
    sqlTests001.status = GMERR_OK;
    sqlTests001.output = {{"[2.4,3.3,4.1]"},  {"[4.1, 5.1, 2.1]"}, {"[4.4, 3.3, 7.1]"}};
    int ret = GmeSqlStepAndCheck(conn, &sqlTests001, 1);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    ComWriteLog(LOG_STEP, "test start.");
    struct GmeSqlTestCtx sqlTests002;
    sqlTests002.sql = "SELECT repr1 FROM t_reprs ORDER BY repr2 <=> '[2,3,4,5]' limit 3;";
    sqlTests002.status = GMERR_OK;
    sqlTests002.output = {{"[4.4,3.3,7.1]"},  {"[3.7,46.7,24.1]"}, {"[2.4,3.3,4.1]"}};
    ret = GmeSqlStepAndCheck(conn, &sqlTests002, 1);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
}

// t_repr表向量列创建COSINE、L2索引，按距离升序后查找，预期成功
HWTEST_F(diskann_insert_select, INSERT_VECTOR_SELECT_WHERE_ORDERBY_003, TestSize.Level2)
{
    ComWriteLog(LOG_STEP, "test start.");
    struct GmeSqlTestCtx sqlTests001;
    sqlTests001.sql = "SELECT repr FROM t_repr ORDER BY repr <-> '[2,3,4]' limit 3;";
    sqlTests001.status = GMERR_OK;
    sqlTests001.output = {{"[2.4,3.3,4.1]"}, {"[4.1,5.1,2.1]"}, {"[4.4, 3.3, 7.1]"}};
    int ret = GmeSqlStepAndCheck(conn, &sqlTests001, 1);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    ComWriteLog(LOG_STEP, "test start.");
    struct GmeSqlTestCtx sqlTests002;
    sqlTests002.sql = "SELECT repr FROM t_repr ORDER BY repr <=> '[2,3,4]' limit 3;";
    sqlTests002.status = GMERR_OK;
    sqlTests002.output = {{"[2.4,3.3,4.1]"}, {"[4.4, 3.3, 7.1]"}, {"[4.1,5.1,2.1]"}};
    ret = GmeSqlStepAndCheck(conn, &sqlTests002, 1);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
}
