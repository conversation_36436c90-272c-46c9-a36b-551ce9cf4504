/*
* Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
* Description: sift千万和亿级别
* Author: qinhaoran
* Create: 2025-05-19
*/

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include "common_perf.h"

using namespace std;
using namespace testing::ext;

enum ModeType {
    DUAL_MODE_SIFT_10M = 0,
    DUAL_MODE_SIFT_100M,
};

void ModifyConfiguration(const char *configPath, ModeType modeType, const char *dataPath)
{
    switch (modeType) {
        case DUAL_MODE_SIFT_10M:{
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("featureNames=durablememdata,bufferpool,SQL,TRM,PERSISTENCE", configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile(dataPath, configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxConnNum=256 deviceSize=1024 pageSize=32 dbFileSize=16777216 maxSeMem=151200 maxTotalShmSize=161440\
                maxTotalDynSize=1560576 maxSysDynSize=1048576 extendSize=1048576 bufferPoolSize=66060288 bufferPoolPolicy=3 enableTableLock=1\
                preFetchPagesEnable=1 maxPreFetchThreNum=43", configPath));
            break;
        }
        case DUAL_MODE_SIFT_100M:{
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("featureNames=durablememdata,bufferpool,SQL,TRM,PERSISTENCE", configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile(dataPath, configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxConnNum=256 deviceSize=1024 pageSize=64 dbFileSize=33554432 maxSeMem=716800 maxTotalShmSize=819200\
                maxTotalDynSize=1560576 maxSysDynSize=1048576 extendSize=1048576 bufferPoolSize=209715200 bufferPoolPolicy=3 enableTableLock=1\
                preFetchPagesEnable=1 maxPreFetchThreNum=43", configPath));
            break;
        }
        default:
            break;
    }
}

class SiftPerf : public testing::Test {
public:
    GmeConnT *conn = NULL;
    char *configPath = EmbSqlGetConfigPath();
    GmeConnT *g_conn_muti[MAX_THREAD] = {nullptr};

    GmeConnT **EmbSqlGetMultiConns()
    {
        return g_conn_muti;
    }
    GmeConnT *EmbSqlGetMultiConn(uint16_t i)
    {
        return g_conn_muti[i];
    }
    void EmbSqlSuitOpenMultiConn(uint16_t connNum)
    {
        for (uint16_t i = 0; i < connNum; i++) {
            EmbSqlOpenConn(&g_conn_muti[i], configPath);
        }
    }
    void EmbSqlSuitCloseMultiConn(uint16_t connNum)
    {
        for (uint16_t i = 0; i < connNum; i++) {
            EmbSqlCloseConn(g_conn_muti[i]);
        }
    }
    void MultiThreadPerfCheck(const string &tableName, QueryParamsT qParams, vector<PerfCheckContainerT> PContainer)
    {
        for (const auto& elem : PContainer) {
            EmbSqlSuitOpenMultiConn(elem.threadId);
            GmeConnT **conns = EmbSqlGetMultiConns();
            BatchQueryVectorCICheck(elem.perfExpect, elem.queryFilePath, elem.resultFilePath, tableName, &qParams, conns, elem.threadId);
            EmbSqlSuitCloseMultiConn(elem.threadId);
        }
    }
    virtual void SetUp()
    {
        system("ipcrm -a");
    }
    virtual void TearDown()
    {
        system("ipcrm -a");
    }
};

HWTEST_F(SiftPerf, Sift_10M1024D_Build, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 1024;
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    uint16_t topk = 10;
    system("rm -rf ./data_sift_10m1024d/gmdb");
    system("mkdir -p ./data_sift_10m1024d/gmdb");
    ModifyConfiguration(configPath, DUAL_MODE_SIFT_10M, "dataFileDirPath=./data_sift_10m1024d/gmdb");

    string dataSetName = "10M1024D_sift";
    char datasetPath[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train_1.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    ExtendUserSpaceForPerf(conn, 4, "data_sift_10m1024d");
    CreateTablePerf(conn, tableName, dim);
    LpasDuration loadDuration = {dataSetName, 1000000};
    LoadData(conn, datasetPath, 10, tableName, dim, loadDuration);
    LpasBuildParamsT LpasParams = {.gLpasOutDegree = 64, .gLpasBuildSearchListSize = 32};
    LpasDuration buildDuration = {dataSetName, 2520000};
    CreateIndexPerf(conn, tableName, indexName, indexType, distType, LpasParams, buildDuration);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}

// LPAS性能测试，多线程并发预加载查询，10M1024D场景SIFT数据集, 双形态模式, topk=10，L=24, 纯向量查询
HWTEST_F(SiftPerf, Sift_10M1024D_DualMode_VectorQuery, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 1024;
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    uint16_t topk = 10;
    uint16_t lpasQuerySearchListSize = 24;
    QueryParamsT params = {QueryType::VECTORQUERY, lpasQuerySearchListSize, 0, 0, topk, dim};
    ModifyConfiguration(configPath, DUAL_MODE_SIFT_10M, "dataFileDirPath=./data_sift_10m1024d/gmdb");
    
    string dataSetName = "10M1024D_sift";
    char queryFilePath[256];
    (void)snprintf(queryFilePath, sizeof(queryFilePath), "%s/%s/test.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    char resultFilePath[256];
    (void)snprintf(resultFilePath, sizeof(resultFilePath), "%s/%s/neighbors.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());

    vector<PerfCheckContainerT> PerfCheckContainerT;
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 1, {.recall = 0.95, .queryTime = 5.00, .qps = 200}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 64, {.recall = 0.95, .queryTime = 2.78, .qps = 23000}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 128, {.recall = 0.95, .queryTime = 2.56, .qps = 50000}});
    MultiThreadPerfCheck(tableName, params, PerfCheckContainerT);
}
HWTEST_F(SiftPerf, Sift_100M1024D_Load, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 1024;
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    uint16_t topk = 10;
    system("rm -rf ./data_sift_100m1024d/gmdb");
    system("mkdir -p ./data_sift_100m1024d/gmdb");
    // 双模式建图修改下面枚举值为DURABLE_AND_BUFFERPOOL_SIFT_100M
    ModifyConfiguration(configPath, DUAL_MODE_SIFT_100M, "dataFileDirPath=./data_sift_100m1024d/gmdb");

    string dataSetName = "100M1024D_bin";
    char datasetPath[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/sift100M_1024_base_SPFresh.bin", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    ExtendUserSpaceForPerf(conn, 40, "data_sift_100m1024d");
    CreateTablePerf(conn, tableName, dim);
    LoadBinData(conn, datasetPath, tableName, dim);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}
HWTEST_F(SiftPerf, Sift_100M1024D_BuildIndex, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string dataSetName = "100M1024D_bin";
    string tableName = "t_perf";
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    LpasBuildParamsT LpasParams = {.gLpasOutDegree = 64, .gLpasBuildSearchListSize = 32};
    LpasDuration buildDuration = {dataSetName, 43200000};
    ModifyConfiguration(configPath, DUAL_MODE_SIFT_100M, "dataFileDirPath=./data_sift_100m1024d/gmdb");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    CreateIndexPerf(conn, tableName, indexName, indexType, distType, LpasParams, buildDuration);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}
// LPAS性能测试，多线程并发预加载查询，100M1024D场景SIFT数据集, 双形态模式, topk=10，L=70, 纯向量查询
HWTEST_F(SiftPerf, Sift_100M1024D_DualMode_VectorQuery, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 1024;
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    uint16_t topk = 10;
    uint16_t lpasQuerySearchListSize = 70;
    QueryParamsT params = {QueryType::VECTORQUERY, lpasQuerySearchListSize, 0, 0, topk, dim};
    ModifyConfiguration(configPath, DUAL_MODE_SIFT_100M, "dataFileDirPath=./data_sift_100m1024d/gmdb");
    
    string dataSetName = "100M1024D_bin";
    char queryFilePath[256];
    (void)snprintf(queryFilePath, sizeof(queryFilePath), "%s/%s/sift_query_1024_from_sift1M.bin", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    char resultFilePath[256];
    (void)snprintf(resultFilePath, sizeof(resultFilePath), "%s/%s/sift_query_gt.bin", getenv("VECTORDATA_HOME"), dataSetName.c_str());

    vector<PerfCheckContainerT> PerfCheckContainerT;
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 1, {.recall = 0.95, .queryTime = 5.00, .qps = 200}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 64, {.recall = 0.95, .queryTime = 16.00, .qps = 4000}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 128, {.recall = 0.95, .queryTime = 12.8, .qps = 10000}});
    MultiThreadPerfCheck(tableName, params, PerfCheckContainerT);
}
// Check the number of records in the table.
uint32_t CheckCount(GmeConnT *conn, string tableName) {
    const char *viewSql = "select * from 'V$STORAGE_VERTEX_COUNT'";
    ViewResult resultSet;
    int ret = GmeSqlExecute(conn, viewSql, SqlViewResultCallBack, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    int recordNum = resultSet.colValues.size() / resultSet.colNames.size();
    string table;
    uint32_t count = 0;
    for (int i = 1; i <= recordNum; i++) {
        table = GetValueByColName(&resultSet, "table", i);
        if (table == tableName) {
            count = strtoul(GetValueByColName(&resultSet, "record count", i).c_str(), nullptr, 10);
            break;
        }
    }
    return count;
}
HWTEST_F(SiftPerf, CheckView_100m1024d, TestSize.Level3)
{   
    ModifyConfiguration(configPath, DUAL_MODE_SIFT_100M, "dataFileDirPath=./data_sift_100m1024d/gmdb/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    uint32_t recordCnt = CheckCount(conn, "T_PERF");
    cout << "*******before continue insert 'recordCnt': " << recordCnt << endl;
}
