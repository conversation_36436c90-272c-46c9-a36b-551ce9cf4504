/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 * Description: diskann索引增量更新数据-删除大数据量和功能复合测试-GMETEST
 * Author: bdl
 * Create: 2024-12-03
 */

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include <random>
#include <sys/time.h>

using namespace std;
using namespace testing::ext;

const char *configPath = EmbSqlGetConfigPath();
const char *index1 = "lpas_l2_idx1";
const char *index2 = "lpas_l2_idx2";

string vector_1024_01 = RandomFloat(1, 10, 1024);
string vector_1024_02 = RandomFloat(1, 10, 1024);
string vector_03_01 = RandomFloat(1, 10, 3);
string vector_03_02 = RandomFloat(1, 10, 3);

void InsertData(GmeConnT *conn, string tableName, int count = 1000, int startId = 0)
{
    string VecStr1 = RandomFloat(1, 10, 3);
    string VecStr2 = vector_1024_01;
    for (int i = 1; i <= count; i++) {
        GmeSqlTestCtx InsertDataStr[] = {
            "INSERT INTO " + tableName + " VALUES(" + to_string(i + startId) + ",'" + VecStr1 + "', '" + VecStr2 + "');",
            GMERR_OK,
            {}};
        ASSERT_EQ(GmeSqlStepAndCheck(conn, InsertDataStr, 1), T_OK);
        VecStr1 = RandomFloat(1, 10, 3);
        VecStr2 = RandomFloat(1, 10, 1024);
    }
}

GmeSqlTestCtx sqlSetup[] = {{"CREATE TABLE t_empty(id int primary key, repr floatvector(1024));", GMERR_OK, {}},
    {"CREATE TABLE t_default(id int primary key, repr1 floatvector(3), repr2 floatvector(1024));", GMERR_OK, {}}};
GmeSqlTestCtx createIndex[] = {
    {"CREATE INDEX lpas_l2_idx1 ON t_default USING gSLPASMEM(repr1 L2) WITH "
                                "(LPAS_OUT_DEGREE=64,LPAS_BUILD_SEARCH_LIST_SIZE=32);",
                                   GMERR_OK,
                                   {}},
    {"CREATE INDEX lpas_l2_idx2 ON t_default USING gSLPASMEM(repr2 L2) WITH "
     "(LPAS_OUT_DEGREE=64,LPAS_BUILD_SEARCH_LIST_SIZE=32);",
        GMERR_OK,
        {}}};

GmeSqlTestCtx sqlRelease[] = {
    // 删除表t1
    {"DROP TABLE t_default;", GMERR_OK, {}},
    {"DROP TABLE t_empty;", GMERR_OK, {}}};

class GmeBufferpoolResizeTest : public testing::Test {
public:
    GmeConnT *conn = NULL;
    char *configPath = EmbSqlGetConfigPath();
    virtual void SetUp()
    {
        EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxTotalDynSize=20480 maxSysDynSize=10240 bufferPoolPolicy=3 "
            "bufferPoolChunkSize=131072 bufferPoolSize=131072", configPath));
        system("rm -rf ./data/gmdb");
        system("mkdir -p ./data/gmdb/");
        system("ipcrm -a");
        int ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        int ret = GmeClose(conn);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }
};

void CreateTableAndCreateIndex(GmeConnT *conn)
{
    int ret = GmeSqlStepAndCheck(conn, sqlSetup, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    InsertData(conn, "t_default");
    ret = GmeSqlStepAndCheck(conn, createIndex, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void CleanTable(GmeConnT *conn)
{
    int ret = GmeSqlStepAndCheck(conn, sqlRelease, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

struct ViewResultSet {
    std::vector<std::string> colNames;
    std::vector<std::vector<std::string>> colValues;
    ViewResultSet() : colNames(), colValues()
    {}
};

static Status StEmbSqlViewResultSet(void *data, uint16_t colCnt, char **colValues, char **colNames)
{
    ViewResultSet *resultSet = static_cast<ViewResultSet *>(data);
    if (resultSet->colNames.empty()) {
        for (int i = 0; i < colCnt; ++i) {
            resultSet->colNames.emplace_back(colNames[i]);
        }
    }
    std::vector<std::string> currentValues;
    for (int i = 0; i < colCnt; ++i) {
        currentValues.emplace_back(colValues[i]);
    }
    resultSet->colValues.push_back(currentValues);
    return GMERR_OK;
}

static int GetIndexByColName(ViewResultSet *resultSet, std::string colName)
{
    if (resultSet == nullptr || resultSet->colNames.empty()) {
        return -1;
    }
    for (int i = 0; i < resultSet->colNames.size(); ++i) {
        if (resultSet->colNames[i] == colName) {
            return i;
        }
    }
    return -1;
}

void WaitForResizeFinish(GmeConnT *conn)
{
    const char *viewSql = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    int i = 0;
    int ret = 0;
    sleep(1);
    for (int count = 0; count < 5; count ++) {
        ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
        EXPECT_EQ(ret, GMERR_OK);
        i = GetIndexByColName(&resultSet, "RESIZE_STATUS");
        if (resultSet.colValues[0][i] == "0") {
            return;
	}
	sleep(1);
    }
    return;
}

// Description: 001 正常参数，执行正常
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_001, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 002 conn传空指针
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_002, TestSize.Level1)
{
    GmeConnT *conn1 = NULL;
    int ret = GmeBufferpoolResize(conn1, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
}

// Description: 003 conn是已关闭句柄
HWTEST_F(GmeBufferpoolResizeTest, DISABLED_KBVector_017_003, TestSize.Level1)
{
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    // 不支持此用法，用例弃用
    ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);

    // 重开连接呼避免teardown报错
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// Description: 004 newBufferpoolSize = 0，报错
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_004, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 005 newBufferpoolSize向下取整后为0，报错
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_005, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 131070);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 006 newBufferpoolSize=现有buffersize，预期后台线程检测不需要变化，不需要额外动作，状态很快变为resize finish
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_006, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 131072);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 007 newBufferpoolSize > 初始buffersize但是向下取整时相等，预期后台线程检测不需要变化，不需要额外动作。状态很快变为resize finish
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_007, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 131257);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 008 newBufferpoolSize*num = 1T，因机器规格失败
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_008, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 1073741824);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 009 newBufferpoolSize*num > 1T，预期报错
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_009, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 1073872896);
    AW_MACRO_ASSERT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 010 newBufferpoolSize*num >= maxSysDynSize，预期报错
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_010, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 10485760);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmeBufferpoolResize(conn, 10610832);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 011 dml直至返回内存满，resize一个更大的buffperpool，预期resize成功
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_011, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 012 执行resize中同一个conn句柄中执行resize，预期返回报错  GMERR_BUFFERPOOL_ALREADY_RESIZING
HWTEST_F(GmeBufferpoolResizeTest, DISABLED_KBVector_017_012, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = GmeBufferpoolResize(conn, 2097152);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ret = GmeBufferpoolResize(conn, 1008576);
    EXPECT_EQ(GMERR_BUFFERPOOL_ALREADY_RESIZING, ret);
    WaitForResizeFinish(conn);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 013 执行resize中另一个conn执行resize，预期执行成功
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_013, TestSize.Level1)
{
    // 建一个新实例
    GmeConnT *conn1 = NULL;
    char configPathConn1[32] = "./sql_persistence1.ini";
    char cmd[1024];
    system("rm -rf ./data/gmdb001/;mkdir -p ./data/gmdb001/");
    sprintf(cmd, "cp -f %s %s", configPath, configPathConn1);
    system(cmd);
    EXPECT_EQ(GMERR_OK, ModifyCfgFile("dataFileDirPath=./data/gmdb001/datafile", configPathConn1));
    int ret = GmeOpen(configPathConn1, GME_OPEN_CREATE, &conn1);

    CreateTableAndCreateIndex(conn);
    ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeBufferpoolResize(conn1, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WaitForResizeFinish(conn);
    WaitForResizeFinish(conn1);
    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    ASSERT_EQ(GmeClose(conn1), GMERR_OK);
    CleanTable(conn);
}

// Description: 014 执行成功再resize，同一个conn两倍差距连续十次调大调小
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_014, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = 0;
    for (int count = 0; count < 10; count++) {
        ret = GmeBufferpoolResize(conn, 262144);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        WaitForResizeFinish(conn);
        ret = GmeBufferpoolResize(conn, 131072);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        WaitForResizeFinish(conn);
    }

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 015 执行成功再resize，同一个conn10倍差距连续十次调大调小
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_015, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = 0;
    for (int count = 0; count < 10; count++) {
        ret = GmeBufferpoolResize(conn, 1310720);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        WaitForResizeFinish(conn);
        ret = GmeBufferpoolResize(conn, 131072);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        WaitForResizeFinish(conn);
    }

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 016 执行成功再resize，开启四个db实例，随机抽取1个实例改小再恢复，随机10次
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_016, TestSize.Level1)
{
    // 建一个新实例
    GmeConnT *newConn[4] = { NULL };
    char newConfigPath[4][64];
    char cmd[1024];
    for (int findex = 0; findex < 4; findex++)
    {
        sprintf(newConfigPath[findex], "./sql_persistence%d.ini", findex);
        // clean datafile
        sprintf(cmd, "rm -rf ./data/gmdb00%d;mkdir -p ./data/gmdb00%d", findex, findex);
        system(cmd);
        // touch config file
        sprintf(cmd, "cp -f %s %s", configPath, newConfigPath[findex]);
        system(cmd);
        sprintf(cmd, "dataFileDirPath=./data/gmdb/gmdb00%d/datafile", findex);
        EXPECT_EQ(GMERR_OK, ModifyCfgFile(cmd, newConfigPath[findex]));
	EXPECT_EQ(GMERR_OK, GmeOpen(newConfigPath[findex], GME_OPEN_CREATE, &newConn[findex]));
    }

    CreateTableAndCreateIndex(conn);
    int ret = 0;
    for (int count = 0; count < 10; count++) {
	int random_number = rand() % 4;
        ret = GmeBufferpoolResize(newConn[random_number], 262144);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        WaitForResizeFinish(newConn[random_number]);
        ret = GmeBufferpoolResize(newConn[random_number], 131072);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        WaitForResizeFinish(newConn[random_number]);
    }

    ASSERT_EQ(GmeClose(newConn[0]), GMERR_OK);
    ASSERT_EQ(GmeClose(newConn[1]), GMERR_OK);
    ASSERT_EQ(GmeClose(newConn[2]), GMERR_OK);
    ASSERT_EQ(GmeClose(newConn[3]), GMERR_OK);

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

// Description: 017 resize过程中关库，重开一个库，然后在resize，重复10次
HWTEST_F(GmeBufferpoolResizeTest, KBVector_017_017, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn);
    int ret = 0;
    for (int count = 0; count < 10; count++) {
        ret = GmeBufferpoolResize(conn, 262144);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
	ret = GmeClose(conn);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
        ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    CleanTable(conn);
}

