/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 * Description: diskann索引增量更新数据-删除大数据量和功能复合测试-GMETEST
 * Author: bdl
 * Create: 2024-12-03
 */

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include <random>
#include <sys/time.h>

using namespace std;
using namespace testing::ext;

#define THREAD_NUM 16

const char *configPath = EmbSqlGetConfigPath();
const char *index1 = "lpas_l2_idx1";
const char *index2 = "lpas_l2_idx2";

string vector_1024_01 = RandomFloat(1, 10, 1024);
string vector_1024_02 = RandomFloat(1, 10, 1024);
string vector_03_01 = RandomFloat(1, 10, 3);
string vector_03_02 = RandomFloat(1, 10, 3);

void InsertData(GmeConnT *conn, string tableName, int dataCount, int start_id = 0)
{
    string VecStr1 = RandomFloat(1, 10, 3);
    string VecStr2 = vector_1024_01;
    for (int i = 1; i <= dataCount; i++) {
        GmeSqlTestCtx InsertData[] = {
            "INSERT INTO " + tableName + " VALUES(" + to_string(i + start_id) + ",'" + VecStr1 + "', '" + VecStr2 + "');",
            GMERR_OK,
            {}};
        ASSERT_EQ(GmeSqlStepAndCheck(conn, InsertData, 1), T_OK);
        VecStr1 = RandomFloat(1, 10, 3);
        VecStr2 = RandomFloat(1, 10, 1024);
    }
}

GmeSqlTestCtx sqlSetup[] = {{"CREATE TABLE t_empty(id int, repr floatvector(1024));", GMERR_OK, {}},
    {"CREATE TABLE t_default(id int, repr1 floatvector(3), repr2 floatvector(1024));", GMERR_OK, {}}};
GmeSqlTestCtx createIndex[] = {
    {"CREATE INDEX lpas_l2_idx1 ON t_default USING gSLPASMEM(repr1 L2) WITH "
                                "(LPAS_OUT_DEGREE=64,LPAS_BUILD_SEARCH_LIST_SIZE=32);",
                                   GMERR_OK,
                                   {}},
    {"CREATE INDEX lpas_l2_idx2 ON t_default USING gSLPASMEM(repr2 L2) WITH "
     "(LPAS_OUT_DEGREE=64,LPAS_BUILD_SEARCH_LIST_SIZE=32);",
        GMERR_OK,
        {}}};

GmeSqlTestCtx sqlRelease[] = {
    // 删除表t1
    {"DROP TABLE t_default;", GMERR_OK, {}},
    {"DROP TABLE t_empty;", GMERR_OK, {}}};

class bufferPoolResizeMutualTest : public testing::Test {
public:
    GmeConnT *conn = NULL;
    char *configPath = EmbSqlGetConfigPath();
    virtual void SetUp()
    {
        EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxTotalDynSize=20480 maxSysDynSize=10240 bufferPoolPolicy=3 "
            "bufferPoolPriorityRatio=80 loadTablePriorityRatio=40 bufferPoolChunkSize=131072 bufferPoolSize=131072",
            configPath));
        system("rm -rf ./data/gmdb");
        system("mkdir -p ./data/gmdb/");
        system("ipcrm -a");
        int ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        int ret = GmeClose(conn);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }
};

void CreateTableAndCreateIndex(GmeConnT *conn, int dataCount = 1000)
{
    int ret = GmeSqlStepAndCheck(conn, sqlSetup, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    InsertData(conn, "t_default", dataCount);
    ret = GmeSqlStepAndCheck(conn, createIndex, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void CleanTable(GmeConnT *conn)
{
    int ret = GmeSqlStepAndCheck(conn, sqlRelease, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

struct ViewResultSet {
    std::vector<std::string> colNames;
    std::vector<std::vector<std::string>> colValues;
    ViewResultSet() : colNames(), colValues()
    {}
};

static Status StEmbSqlViewResultSet(void *data, uint16_t colCnt, char **colValues, char **colNames)
{
    ViewResultSet *resultSet = static_cast<ViewResultSet *>(data);
    if (resultSet->colNames.empty()) {
        for (int i = 0; i < colCnt; ++i) {
            resultSet->colNames.emplace_back(colNames[i]);
        }
    }
    std::vector<std::string> currentValues;
    for (int i = 0; i < colCnt; ++i) {
        currentValues.emplace_back(colValues[i]);
    }
    resultSet->colValues.push_back(currentValues);
    return GMERR_OK;
}

static int GetIndexByColName(ViewResultSet *resultSet, std::string colName)
{
    if (resultSet == nullptr || resultSet->colNames.empty()) {
        return -1;
    }
    for (int i = 0; i < resultSet->colNames.size(); ++i) {
        if (resultSet->colNames[i] == colName) {
            return i;
        }
    }
    return -1;
}

static int SdvParseViewInfo(GmeConnT *conn)
{
    const char *viewSql = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    int ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);

    if (resultSet.colNames.empty()) {
        return -1;
    }
    for (int i = 0; i < resultSet.colNames.size(); ++i) {
        std::cout << resultSet.colNames[i] << ": " << resultSet.colValues[0][i] << std::endl;
    }
    return -1;
}

string GetDataFromViewBufferpoolStat(GmeConnT *conn, string filedName)
{
    const char *viewSql = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    int ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    int i = GetIndexByColName(&resultSet, filedName);
    return resultSet.colValues[0][i].c_str();
}

void WaitForResizeFinish(GmeConnT *conn)
{
    sleep(1);
    for (int count = 0; count < 10; count ++) {
	if (GetDataFromViewBufferpoolStat(conn, "RESIZE_STATUS") == "0") {
            return;
        }
        sleep(1);
    }
    return;
}

void SelectTableWithIndex(GmeConnT *conn)
{
    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    // 指定查询向量查询,预期加载查询成功
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]' FROM t_default ORDER BY repr1 <-> '[5.2, 5.4, 2.9]' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "SELECT *, repr1 <-> '[5.2, 5.4, 2.9]', repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
}

// Description: 001 写数据，索引占据优先页，resize，检查是否resize成功，resize成功后重新加载数据
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_027, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn, 5000);
    SdvParseViewInfo(conn);
    int ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    SdvParseViewInfo(conn);
    SelectTableWithIndex(conn);
    SdvParseViewInfo(conn);
    CleanTable(conn);
}

// Description: 002 写数据，表占据优先页，resize，检查是否resize成功，resize成功后重新加载数据
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_028, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn, 5000);
    SdvParseViewInfo(conn);
    
    // 加载表
    int ret = GmeTableLoad(conn, "t_default");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeTableLoad(conn, "t_default");
    EXPECT_EQ(ret, GMERR_OK);
    GmeTableLoadStatus loadStatus = GME_LOAD_STATUS_BUTT;
    sleep(5);
    ret = GmeGetTableLoadStatus(conn, "t_default", &loadStatus);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(loadStatus, GME_LOAD_COMPLETED);

    // resize，预期会把load数据释放
    ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    loadStatus = GME_LOAD_STATUS_BUTT;
    ret = GmeGetTableLoadStatus(conn, "t_default", &loadStatus);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(loadStatus, GME_LOAD_COMPLETED);

    // 再次加载正常
    ret = GmeTableLoad(conn, "t_default");
    EXPECT_EQ(ret, GMERR_OK);
    loadStatus = GME_LOAD_STATUS_BUTT;
    sleep(1);
    ret = GmeGetTableLoadStatus(conn, "t_default", &loadStatus);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(loadStatus, GME_LOAD_COMPLETED);

    ret = GmeTableUnload(conn, "t_default");
    EXPECT_EQ(ret, GMERR_OK);
    SdvParseViewInfo(conn);
    SelectTableWithIndex(conn);
    SdvParseViewInfo(conn);
    CleanTable(conn);
}

// Description: 003 bufferpoolnum为1，调大调小size
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_029, TestSize.Level1)
{
    // 关库，修改参数再重开
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolNum=1", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);

    CreateTableAndCreateIndex(conn);
    ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
}

// Description: 004 bufferpoolnum为8，调大调小size
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_030, TestSize.Level1)
{
    // 关库，修改参数再重开
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolNum=8", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);

    CreateTableAndCreateIndex(conn);
    ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
}

// Description: 005 bufferpoolnum为16，调大调小size
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_031, TestSize.Level1)
{
    // 关库，修改参数再重开
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolNum=16", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);

    CreateTableAndCreateIndex(conn);
    ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
}

// Description: 006 bufferpoolnum为32，调大调小size
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_032, TestSize.Level1)
{
    // 关库，修改参数再重开
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolNum=32 maxSysDynSize=16384", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);

    CreateTableAndCreateIndex(conn);
    ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
}

// Description: 007 resize与静态bufferpoolszie比较
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_033, TestSize.Level1)
{
    int ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    string BUFFERPOOL_SIZE = GetDataFromViewBufferpoolStat(conn, "BUFFERPOOL_SIZE");
    string CAPACITY = GetDataFromViewBufferpoolStat(conn, "CAPACITY");
    string PRIORITY_LIST_MIN = GetDataFromViewBufferpoolStat(conn, "PRIORITY_LIST_MIN");
    ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    // 静态buffersize
    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolSize=262144", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);

    EXPECT_EQ(BUFFERPOOL_SIZE, GetDataFromViewBufferpoolStat(conn, "BUFFERPOOL_SIZE"));
    EXPECT_EQ(CAPACITY, GetDataFromViewBufferpoolStat(conn, "CAPACITY"));
    EXPECT_EQ(PRIORITY_LIST_MIN, GetDataFromViewBufferpoolStat(conn, "PRIORITY_LIST_MIN"));
}

// Description: 008 BUFFERPOOL_SIZE字段校验正确
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_034, TestSize.Level1)
{
    EXPECT_EQ(GetDataFromViewBufferpoolStat(conn, "BUFFERPOOL_SIZE"), "131072");
    int ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    EXPECT_EQ(GetDataFromViewBufferpoolStat(conn, "BUFFERPOOL_SIZE"), "262144");
}

// Description: 009 CAPACITY 字段校验正常 
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_035, TestSize.Level1)
{
    string PAGE_SIZE = GetDataFromViewBufferpoolStat(conn, "PAGE_SIZE");
    EXPECT_EQ(stoi(GetDataFromViewBufferpoolStat(conn, "CAPACITY")), 131072 / stoi(PAGE_SIZE));
    int ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    EXPECT_EQ(stoi(GetDataFromViewBufferpoolStat(conn, "CAPACITY")), 262144 / stoi(PAGE_SIZE));
}

// Description: 010 PRIORITY_LIST_MIN字段校验正常
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_036, TestSize.Level1)
{
    string PAGE_SIZE = GetDataFromViewBufferpoolStat(conn, "PAGE_SIZE");
    EXPECT_EQ(stoi(GetDataFromViewBufferpoolStat(conn, "PRIORITY_LIST_MIN")), 131072 * 8 / 10 / stoi(PAGE_SIZE));
    int ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WaitForResizeFinish(conn);

    EXPECT_EQ(stoi(GetDataFromViewBufferpoolStat(conn, "PRIORITY_LIST_MIN")), 262144 * 8 / 10 / stoi(PAGE_SIZE));
}

// Description: 011 无resize场景下，查询视图，RESIZE_STATUS 状态为无resize操作0
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_037, TestSize.Level1)
{
    EXPECT_EQ(GetDataFromViewBufferpoolStat(conn, "RESIZE_STATUS"), "0");
}

// Description: 012 当前实例执行resize后，查询视图，RESIZE_STATUS状态为rezise中
HWTEST_F(bufferPoolResizeMutualTest, DISABLED_KBVector_017_038, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn, 50000);
    EXPECT_EQ(GetDataFromViewBufferpoolStat(conn, "RESIZE_STATUS"), "0");
    int ret = GmeBufferpoolResize(conn, 393216);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(GetDataFromViewBufferpoolStat(conn, "RESIZE_STATUS"), "1");
    WaitForResizeFinish(conn);
    //    SelectTableWithIndex(conn);
    EXPECT_EQ(GetDataFromViewBufferpoolStat(conn, "RESIZE_STATUS"), "0");
    CleanTable(conn);
}

// Description: 013 resize成功后，查询视图，RESIZE_STATUS状态为resize结束
HWTEST_F(bufferPoolResizeMutualTest, DISABLED_KBVector_017_039, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn, 50000);
    EXPECT_EQ(GetDataFromViewBufferpoolStat(conn, "RESIZE_STATUS"), "0");
    int ret = GmeBufferpoolResize(conn, 393216);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 函数中已验证状态2
    WaitForResizeFinish(conn);

    //  SelectTableWithIndex(conn);
    EXPECT_EQ(GetDataFromViewBufferpoolStat(conn, "RESIZE_STATUS"), "2");
    CleanTable(conn);
}

typedef struct {
    GmeConnT *conn;
} ThreadArgsT;

void *LpasSelect(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    GmeSqlTestCtx sqlL2distSelect[] = {
        {"SELECT id, repr2 <-> '" + vector_1024_01 +
                  "' FROM t_default ORDER BY repr2 <-> '" + vector_1024_01 + "' limit 10;", GMERR_OK, {}},
    };
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlStepAndCheck(targs->conn, sqlL2distSelect, 1));
    return NULL;
}

void *LpasUpdate(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    string vector_1024 = RandomFloat(1, 10, 1024);
    int index = RandomInt(1,5000);

    GmeSqlTestCtx sqlUpdate[] = {
        {"UPDATE t_default set repr2 = '" + vector_1024 + "' where id =" + to_string(index) +";", GMERR_OK, {}},
    };
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlStepAndCheck(targs->conn, sqlUpdate, 1));
    return NULL;
}

// Description: 014 进行多并发索引检索后，执行resize 
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_040, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn, 5000);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    int ret = 0;
    // 创建16个线程查数据
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, LpasSelect, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }

    ret = GmeBufferpoolResize(conn, 393216);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 等待线程结束
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

void *LpasDelete(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    int index = RandomInt(1,5000);

    GmeSqlTestCtx sqlDelete[] = {
        {"DELETE FROM t_default where id =" + to_string(index) +";", GMERR_OK, {}},
    };
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlStepAndCheck(targs->conn, sqlDelete, 1));
    return NULL;
}

// Description: 015 删除大量数据后，执行resize 
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_041, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn, 5000);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    int ret = 0;
    // 多线程删数据
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, LpasDelete, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }

    ret = GmeBufferpoolResize(conn, 655360);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待线程结束
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

// Description: 016 更新大量数据后，执行resize 
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_042, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn, 5000);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    int ret = 0;
    // 多线程更新数据
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, LpasUpdate, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }

    ret = GmeBufferpoolResize(conn, 393216);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 等待线程结束
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

void *LpasInsert(void *args)
{
    ThreadArgsT *targs = (ThreadArgsT *)args;
    string vector1 = RandomFloat(1, 10, 3);
    string vector2 = RandomFloat(1, 10, 1024);
    int index = RandomInt(1,5000);

    GmeSqlTestCtx sqlInsert[] = {
        {"INSERT INTO t_default VALUES(" + to_string(index + 10000) + ",'" + vector1 + "','"+ vector2 +"');", GMERR_OK, {}},
    };
    AW_MACRO_EXPECT_EQ_INT(T_OK, GmeSqlStepAndCheck(targs->conn, sqlInsert, 1));
    return NULL;
}

// Description: 017 新增大量数据后，执行resize
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_043, TestSize.Level1)
{
    CreateTableAndCreateIndex(conn, 5000);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    int ret = 0;
    // 多线程更新数据
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, LpasInsert, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }

    SelectTableWithIndex(conn);

    ret = GmeBufferpoolResize(conn, 524288);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 等待线程结束
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }
    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

// Description: 018 执行异步resize后，进行多并发索引检索
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_044, TestSize.Level1)
{
    // 测试resize调小，重开库规避初始size过小的问题
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolSize=393216", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    CreateTableAndCreateIndex(conn, 5000);

    ret = GmeBufferpoolResize(conn, 131072);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    // 多线程并发检索
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, LpasSelect, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }

     // 等待线程结束
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

// Description: 019 执行异步resize后，进行大量数据删除
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_045, TestSize.Level1)
{
    // 测试resize调小，重开库规避初始size过小的问题
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolSize=655360", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    CreateTableAndCreateIndex(conn, 5000);

    ret = GmeBufferpoolResize(conn, 393216);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    // 多线程删数据
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, LpasDelete, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }

     // 等待线程结束
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

// Description: 020 执行异步resize后，进行大量数据更新
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_046, TestSize.Level1)
{
    // 测试resize调小，重开库规避初始size过小的问题
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolSize=917504", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    CreateTableAndCreateIndex(conn, 5000);

    ret = GmeBufferpoolResize(conn, 655360);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    // 多线程更新数据
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, LpasUpdate, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }

     // 等待线程结束
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

// Description: 021 执行异步resize后，进行大量数据插入
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_047, TestSize.Level1)
{
    // 测试resize调小，重开库规避初始size过小的问题
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolSize=524288", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    CreateTableAndCreateIndex(conn, 5000);
    ret = GmeBufferpoolResize(conn, 131072);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_t threads[THREAD_NUM];
    ThreadArgsT args[THREAD_NUM];
    // 多线程新增数据
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        args[i].conn = conn;
        ret = pthread_create(&threads[i], NULL, LpasInsert, &args[i]);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }

     // 等待线程结束
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    WaitForResizeFinish(conn);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

// Description: 022 执行resize后，进行索引构建
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_048, TestSize.Level1)
{
    // 测试resize调小，重开库规避初始size过小的问题
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolSize=524288", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    CreateTableAndCreateIndex(conn, 5000);
    GmeSqlTestCtx testCtx = {"", GMERR_OK, {}};
    testCtx.sql = "DROP INDEX t_default.lpas_l2_idx1;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);
    testCtx.sql = "DROP INDEX t_default.lpas_l2_idx2;";
    ASSERT_EQ(GmeSqlStepAndCheck(conn, &testCtx, 1), GMERR_OK);

    ret = GmeBufferpoolResize(conn, 131072);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WaitForResizeFinish(conn);
    ret = GmeSqlStepAndCheck(conn, createIndex, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

// Description: 023 执行resize后，建表建索引
HWTEST_F(bufferPoolResizeMutualTest, KBVector_017_049, TestSize.Level1)
{
    // 测试resize调小，重开库规避初始size过小的问题
    int ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);

    EXPECT_EQ(GMERR_OK, ModifyCfgFile("bufferPoolSize=524288", configPath));
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmeBufferpoolResize(conn, 262144);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WaitForResizeFinish(conn);

    CreateTableAndCreateIndex(conn, 5000);

    SelectTableWithIndex(conn);
    CleanTable(conn);
}

