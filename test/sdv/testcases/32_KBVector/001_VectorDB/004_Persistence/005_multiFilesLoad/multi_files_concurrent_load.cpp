/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 持久化支持多文件多目录后台并发加载
 * Author: qinhaoran
 * Create: 2025-02-27
 */
#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include <thread>

using namespace std;
using namespace testing::ext;

#define TEST_MAX_ROWS 100000

static char curPath[512];
int ret;
int startPos = 0;
int endPos = 500;

void  ResetPos() {
    startPos = 0;
    endPos = 500;
}
class Multi_Files_Concurrent_Load : public ::testing::Test {
protected:
    char *configPath = NULL;
    static void SetUpTestCase() {
        getcwd(curPath, sizeof(curPath));
    }
    void SetUp() {
        configPath = EmbSqlGetConfigPath();
        system("mkdir -p ./data/gmdb");
    }
    void TearDown() {
        system("ipcrm -a");
        RemoveFileDir(curPath);
        ResetPos();
    }
};

// 开库后，执行创建文件SQL，filePath为指定目录+文件名，预期成功，对应目录下生成文件，关库重开，结合并发加载，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_001, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    system("mkdir -p ./data/gmdb/newFileDir");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';", GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
int InsertDataForMultiFiles(GmeConn *conn, string tableName, int dim, int &insertNum, bool notFull = false) {
    int ret = 0;
    GmeSqlTestCtx InsertData;
    string floatVector = RandomFloat(1, 10, dim); 
    while(ret == GMERR_OK) {
        float avgLatency;
        Timer timer;
        for (int i = startPos; i < endPos; i++) {
            InsertData = {"INSERT INTO " + tableName + " VALUES(" + to_string(i) + ",'" + floatVector + "');", GMERR_OK, {} };
            ret = GmeSqlStepAndCheck(conn, &InsertData, 1);
            if (ret != GMERR_OK) {
                avgLatency = timer.GetLatency() / (float)(i - startPos);
                startPos = i;
                insertNum = i;
                break;
            }
            floatVector = RandomFloat(1, 10, dim);
        }
        if (ret == GMERR_OK){
            avgLatency = timer.GetLatency() / (float)(endPos - startPos);
        }
        // 一轮插入500条
        if (ret == GMERR_OK) {
            startPos = endPos;
            endPos += 500;
            if (notFull) {
                insertNum = endPos - 500;
                return ret;
            }
        }
    }
    return ret;
}
// 开库、创表后，执行创建文件SQL，filePath为指定目录+文件名，并插入数据，关库重开，结合并发加载，预期成功，对应目录下生成文件，且文件存有数据
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_002, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=8192", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    system("mkdir -p ./data/gmdb/newFileDir");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 循环插入数据，至两个文件满（1个默认文件+1个新建文件）
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    EXPECT_EQ(insertNum, 1743);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表、插入数据后，执行创建文件SQL，filePath为指定目录+文件名，关库重开，结合并发加载，预期成功，对应目录下生成文件
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_003, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据至默认文件满
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    EXPECT_EQ(insertNum, 868);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    system("mkdir -p ./data/gmdb/newFileDir");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 可以再次插入数据
    string floatVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx InsertData = {
        "INSERT INTO " + tables.at(0) + " VALUES(" + to_string(startPos) + ",'" + floatVector + "');", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &InsertData, 1);
    EXPECT_EQ(ret, GMERR_OK);      
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表、插入、创索引后，执行创建文件SQL，filePath为指定目录+文件名，关库重开，结合并发加载，预期成功，对应目录下生成文件
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_004, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(insertNum, 500);
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    system("mkdir -p ./data/gmdb/newFileDir");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表、插入、创索引、查询后，执行创建文件SQL，filePath为指定目录+文件名，关库重开，结合并发加载，预期成功，对应目录下生成文件
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_005, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(insertNum, 500);
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    system("mkdir -p ./data/gmdb/newFileDir");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表、插入、创索引、查询、删表后，执行创建文件SQL，filePath为指定目录+文件名，关库重开，结合并发加载，预期成功，对应目录下生成文件
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_006, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(insertNum, 500);
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    system("mkdir -p ./data/gmdb/newFileDir");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
void ThreadCreatFiles1(GmeConn *conn, int i, int *statusCode){
    // 准备目录
    char createFileDir[256];
    snprintf(createFileDir, sizeof(createFileDir), "mkdir -p %s/data/gmdb/newFileDir%d", curPath, i);
    system(createFileDir);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir%d/test_file_%d", curPath, i, i);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    *statusCode = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(*statusCode, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
}
/* 开库、创表后，起8个并发，执行创建文件SQL，filePath为指定目录+文件名，且每个并发是不同目录和文件名，
插入数据，关库重开，结合并发加载，预期成功，对应目录下生成文件，且文件存有数据 */
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_007, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=11 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    int threadNum = 8;
    int statusCode[threadNum];
    thread creatFiles[threadNum];
    for (int i = 0; i < threadNum; i++) {
        creatFiles[i] = thread(ThreadCreatFiles1, conn, i, &statusCode[i]);
    }
    for (int i = 0; i < threadNum; i++) {
        creatFiles[i] .join();
        EXPECT_EQ(statusCode[i], GMERR_OK);
    }
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    EXPECT_EQ(insertNum, 7917);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
void ThreadCreatFiles2(GmeConn *conn, int *statusCode){
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    *statusCode = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
}
/* 开库、创表后，起2个并发，执行创建文件SQL，filePath为指定目录+文件名，2个filePath相同，
插入数据，关库重开，结合并发加载，预期1个线程创文件报错，对应目录下生成1个文件，且文件存有数据 */
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_008, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 准备目录
    system("mkdir -p ./data/gmdb/newFileDir");
    int threadNum = 2;
    int statusCode[threadNum];
    thread creatFiles[threadNum];
    int failNum = 0;
    for (int i = 0; i < threadNum; i++) {
        creatFiles[i] = thread(ThreadCreatFiles2, conn, &statusCode[i]);
    }
    for (int i = 0; i < threadNum; i++) {
        creatFiles[i].join();
        if (statusCode[i] == GMERR_OK) {
            failNum++;
        }
    }
    EXPECT_EQ(failNum, 1);
    // 检查文件是否存在
    EXPECT_EQ(access("./data/gmdb/newFileDir/test_file_0", F_OK), GMERR_OK);
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    cout << "insertNum: " << insertNum << endl;
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
void ThreadCreatFiles3(GmeConn *conn, int i, int *statusCode){
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir%d/test_file_%d", curPath, i, i);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    *statusCode = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
}
void ConcurrentInsert(GmeConn *conn, string tableName, int start, int end, int dim, int *statusCode) {
    int ret = 0;
    GmeSqlTestCtx InsertData;
    string floatVector = RandomFloat(1, 10, dim);
    for (int i = start; i < end; i++) {
        InsertData = {"INSERT INTO " + tableName + " VALUES(" + to_string(i) + ",'" + floatVector + "');", GMERR_OK, {} };
        *statusCode = GmeSqlStepAndCheck(conn, &InsertData, 1);
        if (*statusCode != GMERR_OK) {
            break;
        }
        floatVector = RandomFloat(1, 10, dim);
    }
}
/*
开库、创表后，起4个并发，执行创建文件SQL，filePath为指定目录+文件名，另起4个并发插入数据，
至2个文件满，关库重开，结合并发加载，预期成功，对应目录下生成文件，且文件存有数据
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_009, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=7 preFetchPagesEnable=0 dbFileSize=12288", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    int threadNum = 4;
    int statusCode[threadNum];
    thread creatFiles[threadNum];
    system("mkdir -p ./data/gmdb/newFileDir0 ./data/gmdb/newFileDir1 ./data/gmdb/newFileDir2 ./data/gmdb/newFileDir3");
    for (int i = 0; i < threadNum; i++) {
        creatFiles[i] = thread(ThreadCreatFiles3, conn, i, &statusCode[i]);
    }
    for (int i = 0; i < threadNum; i++) {
        creatFiles[i] .join();
        EXPECT_EQ(statusCode[i], GMERR_OK);
    }
    // 插入数据
    int singleThrNum = 6000 / 4;
    int insertThreadNum = 4;
    int insertCode[insertThreadNum];
    thread insertData[insertThreadNum];
    int insertStartPos = 0;
    for (int i = 0; i < insertThreadNum; i++) {
        insertData[i] = thread(ConcurrentInsert, conn, tables.at(0), i * singleThrNum, i * singleThrNum + singleThrNum, dim, &insertCode[i]);
    }
    for (int i = 0; i < insertThreadNum; i++) {
        insertData[i] .join();
        EXPECT_EQ(insertCode[i], GMERR_OK);
    }
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

void ThreadCreatFiles4(GmeConn *conn, int i, int *statusCode){
    // 创建文件
    char fileName[256];
    snprintf(fileName, sizeof(fileName), "test_file_%d", i);
    string filePathStr(fileName);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    *statusCode = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(*statusCode, GMERR_OK);
    // 检查文件是否存在
    snprintf(fileName, sizeof(fileName), "./data/gmdb%d/test_file_%d", i, i);
    EXPECT_EQ(access(fileName, F_OK), GMERR_OK);
}
void thread_singl_instance_opr(SingleInstanceParams params, int *statusCode) {
    *statusCode = SingleInstanceOpr(params);
}
void thread_concurrent_load(GmeConn *conn, int i, int *statusCode) {
    char cfgPara[256] = {0};
    snprintf(cfgPara, sizeof(cfgPara), "./sql_persistence%d.ini", i);
    // 关库
    *statusCode = GmeClose(conn);
    EXPECT_EQ(*statusCode, GMERR_OK);
    // 重开库，并发加载
    *statusCode = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", cfgPara);
    EXPECT_EQ(*statusCode, GMERR_OK);
    *statusCode = GmeOpen(cfgPara, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    *statusCode = GmeClose(conn);
    EXPECT_EQ(*statusCode, GMERR_OK);
}
/*
起4个实例，每个实例，开库，起4个并发，执行创建文件SQL，filePath为文件名，
创表，插入数据，创索引，查询，关库重开，结合并发加载，预期成功，对应目录下生成文件，且文件存有数据
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_010, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConnT *conn[4] = {0};
    int ret;
    int instanceNum = sizeof(conn) / sizeof(conn[0]);
    ret = CreateInstance(conn, curPath, instanceNum, false);
    EXPECT_EQ(GMERR_OK, ret);
    char cfgPara[256] = {0};
    for (int i = 0; i < instanceNum; i++) {
        snprintf(cfgPara, sizeof(cfgPara), "./sql_persistence%d.ini", i);
        ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0", (const char *)cfgPara);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeOpen(cfgPara, GME_OPEN_CREATE, &conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 创建文件
    int createStatusCode[instanceNum];
    thread creatFiles[instanceNum];
    for (int i = 0; i < instanceNum; i++) {
        creatFiles[i] = thread(ThreadCreatFiles4, conn[i], i, &createStatusCode[i]);
    }
    for (int i = 0; i < instanceNum; i++) {
        creatFiles[i] .join();
        EXPECT_EQ(createStatusCode[i], GMERR_OK);
    }
    thread instanceOpr[instanceNum];
    int statusCode[instanceNum];
    SingleInstanceParams params[instanceNum] = {};
    for (int i = 0; i < instanceNum; i++) {
        params[i].conn = conn[i];
        params[i].idxTypes.push_back(LPAS_MEM);
        params[i].idxTypes.push_back(DISKANN);
        params[i].notDeleteTable = 1;
        instanceOpr[i] = thread(thread_singl_instance_opr, params[i], &statusCode[i]);
    }
    for (int i = 0; i < instanceNum; i++) {
        instanceOpr[i].join();
        EXPECT_EQ(GMERR_OK, statusCode[i]);
    }
    int loadStatusCode[instanceNum];
    thread concuLoad[instanceNum];
    for (int i = 0; i < instanceNum; i++) {
        concuLoad[i] = thread(thread_concurrent_load, conn[i], i, &loadStatusCode[i]);
    }
    for (int i = 0; i < instanceNum; i++) {
        concuLoad[i].join();
        EXPECT_EQ(GMERR_OK, loadStatusCode[i]);
    }
}
void ThreadCreatFiles5(GmeConn *conn, int i, int *statusCode){
    char filePath[256];
    // 准备目录
    char createFileDir[256];
    snprintf(createFileDir, sizeof(createFileDir), "mkdir -p %s/data/gmdb%d/newFileDir", curPath, i);
    system(createFileDir);
    if (i > 1) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb%d/newFileDir/test_file_0", curPath, i);
    } else {
        snprintf(filePath, sizeof(filePath), "test_file_%d", i);
    }
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    *statusCode = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    // 检查文件是否存在
    if (i > 1) {
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }else {
        snprintf(filePath, sizeof(filePath), "./data/gmdb%d/test_file_%d", i, i);
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
}
/*
起4个实例，每个实例，开库，起4个并发，执行创建文件SQL，其中2个filePath为文件名,
2个为指定目录+文件名，且文件名相同，创表，插入大量数据，关库重开，结合并发加载，预期成功，对应目录下生成文件，且文件存有数据
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_011, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConnT *conn[4] = {0};
    int ret;
    int instanceNum = sizeof(conn) / sizeof(conn[0]);
    ret = CreateInstance(conn, curPath, instanceNum, false);
    EXPECT_EQ(GMERR_OK, ret);
    char cfgPara[256] = {0};
    for (int i = 0; i < instanceNum; i++) {
        snprintf(cfgPara, sizeof(cfgPara), "./sql_persistence%d.ini", i);
        ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", (const char *)cfgPara);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeOpen(cfgPara, GME_OPEN_CREATE, &conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 创建文件
    int createStatusCode[instanceNum];
    thread creatFiles[instanceNum];
    for (int i = 0; i < instanceNum; i++) {
        creatFiles[i] = thread(ThreadCreatFiles5, conn[i], i, &createStatusCode[i]);
    }
    for (int i = 0; i < instanceNum; i++) {
        creatFiles[i] .join();
        EXPECT_EQ(createStatusCode[i], GMERR_OK);
    }
    thread instanceOpr[instanceNum];
    int statusCode[instanceNum];
    SingleInstanceParams params[instanceNum] = {};
    for (int i = 0; i < instanceNum; i++) {
        params[i].conn = conn[i];
        params[i].idxTypes.push_back(LPAS_MEM);
        params[i].idxTypes.push_back(DISKANN);
        params[i].notDeleteTable = 1;
        instanceOpr[i] = thread(thread_singl_instance_opr, params[i], &statusCode[i]);
    }
    for (int i = 0; i < instanceNum; i++) {
        instanceOpr[i].join();
        EXPECT_EQ(GMERR_OK, statusCode[i]);
    }
    int loadStatusCode[instanceNum];
    thread concuLoad[instanceNum];
    for (int i = 0; i < instanceNum; i++) {
        concuLoad[i] = thread(thread_concurrent_load, conn[i], i, &loadStatusCode[i]);
    }
    for (int i = 0; i < instanceNum; i++) {
        concuLoad[i].join();
        EXPECT_EQ(GMERR_OK, loadStatusCode[i]);
    }
}
/*
开库、创表后，插入数据至userSpace满后，创建文件1，filePath为指定目录+文件名，再插入数据至新文件满，
创建文件2后，插入10条数据，关库重开，结合并发加载，预期两次文件满时报错，其余情况均成功，对应目录下生成文件，且文件存有数据
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_012, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=5 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 循环插入数据，至默认文件满
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    EXPECT_EQ(insertNum, 868);
    // 创建文件1
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    system("mkdir -p ./data/gmdb/newFileDir");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 循环插入数据，至文件1满
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    EXPECT_EQ(insertNum, 1743);
    // 创建文件2
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_1", curPath);
    string filePathStr1(filePath);
    createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr1 +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
void ConfigTestFunc(GmeConn *conn, char *configPath){
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=20 preFetchPagesEnable=0 bufferPoolSize=40960 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    system("mkdir -p ./data/gmdb/newFileDir");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 并发加载开关取值异常preFetchPagesEnable=2，并发加载线程数maxPreFetchThreNum=16，预期开库失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_013, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    ConfigTestFunc(conn, configPath);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=2 maxPreFetchThreNum=16", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_EXCEPTION);
}
// 并发加载开关取值异常preFetchPagesEnable=-1，并发加载线程数maxPreFetchThreNum=16，预期开库失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_014, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    ConfigTestFunc(conn, configPath);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=-1 maxPreFetchThreNum=16", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_EXCEPTION);
}
// 并发加载开关取值异常preFetchPagesEnable=0，并发加载线程数maxPreFetchThreNum=0，预期开库失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_015, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    ConfigTestFunc(conn, configPath);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=0 maxPreFetchThreNum=0", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_EXCEPTION);
}
// 并发加载线程数取值异常maxPreFetchThreNum=257，不开启并发加载preFetchPagesEnable=0，预期开库失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_016, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    ConfigTestFunc(conn, configPath);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=0 maxPreFetchThreNum=257", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_EXCEPTION);
}
// 不开启并发加载preFetchPagesEnable=0，但配置并发加载线程数maxPreFetchThreNum=16，预期开库成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_017, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    ConfigTestFunc(conn, configPath);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=0 maxPreFetchThreNum=16", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开启并发加载preFetchPagesEnable=1，配置并发加载线程数maxPreFetchThreNum=0，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_018, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    ConfigTestFunc(conn, configPath);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=0", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_EXCEPTION);
}
/*
开启并发加载preFetchPagesEnable=1，配置并发加载线程数maxPreFetchThreNum=1，
新增用户文件数为1，向原有表插入数据，创新表、插入数据、创索引、删表、关库，预期成功
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_019, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    ConfigTestFunc(conn, configPath);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1  maxPreFetchThreNum=1", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    int insertNum = 0;
    // 插入数据
    ret = InsertDataForMultiFiles(conn, "test_table_0", dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创新表
    vector<string> tables;
    string tableName = "test_table_1";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    dropSqlCtx = {"Drop TABLE test_table_0 ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开启并发加载preFetchPagesEnable=1，配置并发加载线程数maxPreFetchThreNum=8，新增用户文件数为8，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_020, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    ConfigTestFunc(conn, configPath);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 再新增7个文件
    char filePath[256];
    for (int i = 1; i < 7; i++) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=8", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insetNum = 0;
    ret = InsertDataForMultiFiles(conn, "test_table_0", dim, insetNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创新表
    vector<string> tables;
    string tableName = "test_table_1";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insetNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    dropSqlCtx = {"Drop TABLE test_table_0 ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开启并发加载preFetchPagesEnable=1，配置并发加载线程数maxPreFetchThreNum=4，新增用户文件数为2，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_021, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    ConfigTestFunc(conn, configPath);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 再新增1个文件
    char filePath[256];
    for (int i = 1; i < 8; i++) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insetNum = 0;
    ret = InsertDataForMultiFiles(conn, "test_table_0", dim, insetNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创新表
    vector<string> tables;
    string tableName = "test_table_1";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insetNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开启并发加载preFetchPagesEnable=1，配置并发加载线程数maxPreFetchThreNum=128，新增用户文件数为3，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_022, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    int dim = 1024;
    GmeConn *conn = NULL;
    ConfigTestFunc(conn, configPath);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 再新增2个文件
    char filePath[256];
    for (int i = 1; i < 2; i++) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=128", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insetNum = 0;
    ret = InsertDataForMultiFiles(conn, "test_table_0", dim, insetNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创新表
    vector<string> tables;
    string tableName = "test_table_1";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insetNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开启并发加载preFetchPagesEnable=1，配置并发加载线程数maxPreFetchThreNum=256，新增用户文件数为3，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_023, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    ConfigTestFunc(conn, configPath);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 再新增2个文件
    char filePath[256];
    for (int i = 1; i < 2; i++) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=256", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insetNum = 0;
    ret = InsertDataForMultiFiles(conn, "test_table_0", dim, insetNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创新表
    vector<string> tables;
    string tableName = "test_table_1";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insetNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

float ReturnHWMRatio(GmeConn *conn) {
    const char *viewSql = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResult resultSet;
    int ret = GmeSqlExecute(conn, viewSql, SqlViewResultCallBack, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    string capacity = GetValueByColName(&resultSet, "CAPACITY");
    string hwm = GetValueByColName(&resultSet, "HWM");
    float percentage = stof(hwm) * 100 / stof(capacity);
    return percentage;
}
/*
开库、新建3个文件，创表、并插入一定量数据，开启并发加载设置线程为1，重开库，sleep1秒，
查询buffer pool视图记录水位线，关库，开启并发加载设置线程为4，再重开库，
sleep1秒，查询buffer pool视图记录水位线，预期第二次使用buffer pool大于第一次
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_024, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=6 preFetchPagesEnable=1 maxPreFetchThreNum=1 dbFileSize=40960 bufferPoolSize=40960", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建3个文件
    char filePath[256];
    system("mkdir -p ./data/gmdb/newFileDir");
    for (int i = 0; i < 3; i++) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=1", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询buffer pool视图，记录水位线
    this_thread::sleep_for(chrono::milliseconds(3));
    float ratio1 = ReturnHWMRatio(conn);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询buffer pool视图，记录水位线
    this_thread::sleep_for(chrono::milliseconds(3));
    float ratio2 = ReturnHWMRatio(conn);
    // 比较两次水位线，第二次大于第一次
    cout << "ratio1: " << ratio1 << " ratio2: " << ratio2 << endl;
    EXPECT_GT(ratio2, ratio1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
/*
开库、新建3个文件，创表、并插入一定量数据，不开启并发加载重开库，重开库，
sleep1秒，查询buffer pool视图记录水位线，关库，开启并发加载设置线程为4，
再重开库，查询buffer pool视图记录水位线，预期第二次使用buffer pool大小远大于第一次
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_025, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=6 preFetchPagesEnable=1 maxPreFetchThreNum=1 dbFileSize=40960 bufferPoolSize=40960", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建3个文件
    char filePath[256];
    system("mkdir -p ./data/gmdb/newFileDir");
    for (int i = 0; i < 3; i++) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=0 maxPreFetchThreNum=1", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询buffer pool视图，记录水位线
    sleep(1);
    float ratio1 = ReturnHWMRatio(conn);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询buffer pool视图，记录水位线
    sleep(1);
    float ratio2 = ReturnHWMRatio(conn);
    // 比较两次水位线，第二次大于第一次
    cout << "ratio1: " << ratio1 << " ratio2: " << ratio2 << endl;
    EXPECT_GT(ratio2, ratio1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 重开库并发加载后，调用load按表换入、查load状态、sleep2秒后、删表、关库，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_026, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=6 preFetchPagesEnable=1 maxPreFetchThreNum=1 dbFileSize=40960", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建3个文件
    char filePath[256];
    system("mkdir -p ./data/gmdb/newFileDir");
    for (int i = 0; i < 3; i++) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4 bufferPoolPolicy=3 "
        "bufferPoolPriorityRatio=80 loadTablePriorityRatio=40", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeTableLoad(conn, "test_table_0");
    EXPECT_EQ(ret, GMERR_OK);
    GmeTableLoadStatus loadStatus = GME_LOAD_STATUS_BUTT;
    ret = GmeGetTableLoadStatus(conn, "test_table_0", &loadStatus);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(2);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，spaceName为dbSystemSpace，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_027, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_table_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbSystemSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_INVALID_NAME);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，spaceName为dbUndoSpace，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_028, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_table_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUndoSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_INVALID_NAME);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，spaceName为异常字符，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_029, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_table_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE @! ADD DATAFILE '"+ filePathStr +"';", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，spaceName为无效名dbInvalidSpace，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_030, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbInvalidSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_INVALID_NAME);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，filePath为绝对路径"/"，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_031, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '/';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access("/dbUserSpace_1", F_OK), GMERR_OK);
    ret = GmeClose(conn);
    system("rm -rf /dbUserSpace_1");
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，filePath为不存在路径"/abc/"，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_032, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '/abc/';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_INTERNAL_ERROR);
    // 检查文件是否存在
    EXPECT_EQ(access("/abc/dbUserSpace_0", F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// （当前失败，待回归后适配）开库、创表后，执行创建文件SQL，filePath为根路径下文件"/abc"，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_033, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '/abc';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access("/abc", F_OK), GMERR_OK);
    system("rm -rf /abc");
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，filePath为带后缀"userSpace.cpp"，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_034, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据至默认文件满
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/userSpace.cpp", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 再插入数据能插入成功
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=2", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// （待验证）开库、创表后，执行创建文件SQL，filePath为带后缀"userSpace.txt"，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_035, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据至默认文件满
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/userSpace.txt", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 再插入数据能插入成功
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，filePath为和原有文件重名"dbUserSpace"，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_036, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/dbUserSpace", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_FILE_OPERATE_FAILED);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表后，执行创建文件SQL，filePath为和原有文件重名，但不同目录"gmdb/newFileDir0/dbUserSpace"，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_037, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir0/dbUserSpace", curPath);
    system("mkdir -p ./data/gmdb/newFileDir0 ");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
void thread_create_file(char *cfgParameter, string tableName, int *statusCode) {
    GmeConn *conn = NULL;
    int ret;
    ret = GmeOpen(cfgParameter, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    GmeSqlTestCtx threadCreateFile = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + tableName + "';", GMERR_OK, {}};
    *statusCode = GmeSqlStepAndCheck(conn, &threadCreateFile, 1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库后，执行创建文件SQL，并发创建两个与spaceName相同的的文件，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_038, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=5 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/dbUserSpace", curPath);
    string filePathStr(filePath);
    int threadNum = 2;
    int failNum = 0;
    thread conCurrentCreate[threadNum];
    int createStatus[threadNum];
    for (int i = 0; i < threadNum; i++) {
        conCurrentCreate[i] = thread(thread_create_file, configPath, filePathStr, &createStatus[i]);
    }
    for (int i = 0; i < threadNum; i++) {
        conCurrentCreate[i].join();
        if (createStatus[i] != GMERR_OK) {
            failNum++;
        }
    }
    EXPECT_EQ(failNum, 2);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库后，执行创建文件SQL，先不指定文件路径创建文件，再指定文件路径创建同默认文件名相同的文件，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_039, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=54 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件1
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE './data/gmdb/';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件1同名文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/dbUserSpace_1", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles2 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles2, 1);
    EXPECT_EQ(ret, GMERR_FILE_OPERATE_FAILED);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库后，执行创建文件SQL，filePath对应文件名长度等于31，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_040, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    char fileName[32] = {0};
    memset(fileName, 'a', 31);
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/%s", curPath, fileName);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库后，执行创建文件SQL，filePath对应文件名长度等于32，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_041, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    char fileName[33] = {0};
    memset(fileName, 'a', 32);
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/%s", curPath, fileName);
    string filePathStr(filePath);
    cout << "filePathStr size :" << filePathStr.length() << endl;
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_INVALID_NAME);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库后，执行创建文件SQL，filePath绝对目录长度等于255个字符"/disk1/.../"，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_042, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    int dirNameSize = 255 - 1; //减去最后一个字符'/'
    char filePath[512];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/", curPath);
    string filePathStr(filePath);
    int dirSize = dirNameSize - filePathStr.length();
    char dirName[dirSize]; 
    memset(dirName, 'a', dirSize);
    string filePathStr1(dirName);
    // 创建目录
    char cmd[512];
    snprintf(cmd, sizeof(cmd), "mkdir -p %s%s", filePath, dirName);
    system(cmd);
    EXPECT_EQ(filePathStr1.length() + filePathStr.length(), dirNameSize);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr +  filePathStr1 + "/test_table_0';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    string fileStr = filePathStr +  filePathStr1 + "/test_table_0";
    EXPECT_EQ(access(fileStr.c_str(), F_OK), GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库后，执行创建文件SQL，filePath绝对目录长度为256个字符"/disk1/.../"，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_043, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    int dirNameSize = 256 - 1; // 减去最后一个字符'/'
    char filePath[512];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/", curPath);
    string filePathStr(filePath);
    int dirSize = dirNameSize - filePathStr.length();
    char dirName[dirSize]; 
    memset(dirName, 'a', dirSize);
    string filePathStr1(dirName);
    // 创建目录
    char cmd[512];
    snprintf(cmd, sizeof(cmd), "mkdir -p %s%s", filePath, dirName);
    system(cmd);
    EXPECT_EQ(filePathStr1.length() + filePathStr.length(), dirNameSize);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr +  filePathStr1 + "/test_table_0';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_INVALID_NAME);
    string fileStr = filePathStr +  filePathStr1 + "/test_table_0";
    // 检查文件是否存在
    EXPECT_EQ(access(fileStr.c_str(), F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库后，执行创建文件SQL，filePath对应文件名为中文字符"文件1"，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_044, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/%s", curPath, "文件1");
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 创表
    vector<string> tables;
    string tableName = "test_table_0";
    tables.push_back(tableName);
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据至默认文件满
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重启加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=1", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// dbFilesMaxCnt等于3，开库后，执行创建文件SQL，新创建1个文件，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_045, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=3 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// dbFilesMaxCnt等于4，开库后，执行创建文件SQL，新创建2个文件，预期第1个成功，第2个失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_046, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件1
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 创建文件2
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_1", curPath);
    string filePathStr1(filePath);
    GmeSqlTestCtx createMultiFiles2 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr1 + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles2, 1);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    EXPECT_EQ(access(filePath, F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// dbFilesMaxCnt等于1024，开库后，执行创建文件SQL，新创建1022个文件，预期第1021个成功，第1022个失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_047, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=1024 preFetchPagesEnable=0 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    for (int i = 0; i < 1021; i++) {
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_%d", curPath, i);
        string filePathStr(filePath);
        GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_1022", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles2 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '" + filePathStr + "';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles2, 1);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    EXPECT_EQ(access(filePath, F_OK), -1);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 重开库时，减小dbFileSize，预期开库失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_048, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = ModifyCfgFile("dbFileSize=8192", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_CONFIG_ERROR);
}
// 开库，创表，创建1个新文件后，调用rm命令删除文件，插入数据至第1个文件满，预期再插入报错
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_049, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 dbFileSize=12288", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insertNume = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNume);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 删除文件
    system("rm -rf ./data/gmdb/test_file_0");
    sleep(1);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNume);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重启加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=1", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_CORRUPTION);
}
// 开库，创表，创建1个新文件后，调用rm命令删除文件，再创建相同文件名的文件，预期报错
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_050, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=5 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 删除文件
    system("rm -rf ./data/gmdb/test_file_0");
    // 再创建相同文件名的文件
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr1(filePath);
    GmeSqlTestCtx createMultiFiles2 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr1 +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles2, 1);
    // db感知不到文件删除，创表会因重复报错，实际上没有文件
    EXPECT_EQ(ret, GMERR_INVALID_NAME);
    EXPECT_EQ(access(filePath, F_OK), -1);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
/*
开库，创表，插入数据至第1个文件满，创建1个新文件后，调用rm命令删除文件，再创建不同文件名的文件，
再插入数据，关库，重开，预期重开库报错
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_051, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=5 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    int insertNume = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNume);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 删除文件
    system("rm -rf ./data/gmdb/test_file_0");
    // 再创建不同名文件
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_1", curPath);
    string filePathStr1(filePath);
    GmeSqlTestCtx createMultiFiles2 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr1 +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles2, 1);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 再插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNume);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_CORRUPTION);
}
// 开库，创建1个文件，关库后，修改持久化文件，重开库，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_052, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 修改持久化文件
    system("pwd >> ./data/gmdb/test_file_0");
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_CORRUPTION);
}
// 开库，创建1个文件，关库后，移除持久化文件，重开库，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_053, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=4 dbFileSize=4096", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles1 = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 删除文件
    system("rm -rf ./data/gmdb/test_file_0");
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_DATA_CORRUPTION);
}
// 配置redoPubBufSize=4096，开库、创表、循环执行插满数据、创文件操作16次，创索引、查询、删表、关库，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_054, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=19 redoPubBufSize=4096 dbFileSize=4096 preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    int fileNum = 16;
    char filePath[256];
    GmeSqlTestCtx createMultiFiles;
    system("mkdir -p ./data/gmdb/newFileDir");
    for (int i = 0; i < fileNum; i++) {
        // 插入数据
        int insertNum = 0;
        ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
        EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
        // 创建文件
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, 1030000);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
/*
配置redoPubBufSize=4096，开库、创表、插满数据，创文件、插数据、在同一向量字段创2个lpas
索引、删表、关库，预期失败，看护redoPubBufSize较小场景，保证不core
*/ 
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_055, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=19 redoPubBufSize=4096 dbFileSize=4096 preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插满数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 创建文件
    char filePath[256];
    system("mkdir -p ./data/gmdb/newFileDir");
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK); 
    // 创建lpas索引
    GmeSqlTestCtx createIndexs[3] = {
    {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}} ,
    {"CREATE INDEX lpas_l2_idx2 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}}
    };
    ret = GmeSqlStepAndCheck(conn, createIndexs, 2);
    EXPECT_EQ(ret, 1030000);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
int InsertFullData(GmeConn *conn, int dim, string tableName, bool notFull = false)
{
    int ret = 0;
    GmeSqlTestCtx InsertData;
    string floatVector = RandomFloat(1, 10, dim); 
    while(ret == GMERR_OK) {
        for (int i = startPos; i < endPos; i++) {
            InsertData = {"INSERT INTO " + tableName + " VALUES(" + to_string(i) + "," + to_string(i) + "," + 
            to_string(i) + "," + to_string(i) + "," + to_string(i) + ",'" + floatVector + "', '" + floatVector + "');", GMERR_OK, {}};
            ret = GmeSqlStepAndCheck(conn, &InsertData, 1);
            if (ret != GMERR_OK) {
                startPos = i;
                cout << "已插入条数：" << i << endl;
                return ret;
            }
            floatVector = RandomFloat(1, 10, dim);
        }
        if (ret == GMERR_OK) {
            cout << "已插入条数：" << endPos << endl;
            startPos = endPos;
            endPos += 300;
            if (notFull) {
                return ret;
            }
        }
    }
    return ret;
}
// 开库、创表，除主键外包含4个btree索引的标量字段和2个lpas索引向量字段，插满数据、创文件、插数据，创索引、查询、关库、预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_056, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=10 dbFileSize=4096 preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    GmeSqlTestCtx createTableCtx = {"CREATE TABLE " + tables.at(0) +
        "(id int primary key, scale1 int, scale2 int, scale3 int, scale4 int, "
        "repr1 floatvector(" + to_string(dim) + "), repr2 floatvector(" + to_string(dim) + "));",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createTableCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    GmeSqlTestCtx createScaleIndexs[4] = {
        {"CREATE INDEX id_index1 ON " + tables.at(0) + " (scale1);", GMERR_OK, {}},
        {"CREATE INDEX id_index2 ON " + tables.at(0) + " (scale2);", GMERR_OK, {}},
        {"CREATE INDEX id_index3 ON " + tables.at(0) + " (scale3);", GMERR_OK, {}},
        {"CREATE INDEX id_index4 ON " + tables.at(0) + " (scale4);", GMERR_OK, {}}
    };
    ret = GmeSqlStepAndCheck(conn, createScaleIndexs, 4);
    EXPECT_EQ(ret, GMERR_OK);
    // 插满数据
    ret = InsertFullData(conn, dim, tables.at(0));
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 创建文件
    char filePath[256];
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 再插满数据
    ret = InsertFullData(conn, dim, tables.at(0), true);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建索引
    GmeSqlTestCtx createIndexs[2] = {
        {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
            "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}},
        {"CREATE INDEX lpas_l2_idx2 ON " + tables.at(0) + " USING gslpasmem(repr2 l2) WITH "
            "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}}
    };
    ret = GmeSqlStepAndCheck(conn, createIndexs, 2);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr2 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 配置较大的bufferPoolSize=204800，开库、创表、循环执行插满数据并创文件，关库，重启，sleep5秒，关库，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_057, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=60 dbFileSize=40960 bufferPoolSize=204800 preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    int fileNum = 10;
    char filePath[256];
    GmeSqlTestCtx createMultiFiles;
    system("mkdir -p ./data/gmdb/newFileDir");
    for (int i = 0; i < fileNum; i++) {
        // 插入数据
        int insertNum = 0;
        ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
        EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
        // 创建文件
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 并发加载
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(5);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表、插满数据，创文件、插数据、在同一向量字段创2个lpas，索引、删表、关库，重开库，预期失败，文件大小仍不够创建索引
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_058, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=19 dbFileSize=4096 preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插满数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 创建文件
    char filePath[256];
    system("mkdir -p ./data/gmdb/newFileDir");
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 插入数据
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK); 
    // 创建lpas索引
    GmeSqlTestCtx createIndexs[3] = {
    {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}} ,
    {"CREATE INDEX lpas_l2_idx2 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}}
    };
    ret = GmeSqlStepAndCheck(conn, createIndexs, 2);
    EXPECT_EQ(ret, 1030000);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 开库、创表、插满数据，创文件、插入200条数据、在同一向量字段创2个lpas，索引、删表、关库，重开库，预期成功
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_059, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=19 redoPubBufSize=4096 dbFileSize=4096 preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    // 插满数据
    int insertNum = 0;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
    // 创建文件
    char filePath[256];
    system("mkdir -p ./data/gmdb/newFileDir");
    snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_0", curPath);
    string filePathStr(filePath);
    GmeSqlTestCtx createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
    ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 检查文件是否存在
    EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    // 插入数据
    endPos -= 300;
    ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum, true);
    EXPECT_EQ(ret, GMERR_OK); 
    // 创建lpas索引
    GmeSqlTestCtx createIndexs[3] = {
    {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}} ,
    {"CREATE INDEX lpas_l2_idx2 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}}
    };
    ret = GmeSqlStepAndCheck(conn, createIndexs, 2);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
/*
配置redoPubBufSize为4096、bufferPoolSize为40960，开库、创表、
循环执行插满数据（仅前3个文件插满）、创文件操作16次后，创索引、查询、删表、关库，预期成功
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_060, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=19 redoPubBufSize=4096 dbFileSize=4096 bufferPoolSize=40960 preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    int fileNum = 16;
    char filePath[256];
    GmeSqlTestCtx createMultiFiles;
    system("mkdir -p ./data/gmdb/newFileDir");
    int insertNum = 0;
    for (int i = 0; i < fileNum; i++) {
        if (i < 3){
            // 插入数据
            ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
            EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
        }
        // 创建文件
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// 配置redoPubBufSize=16384，开库、创表、循环执行插满数据、创文件操作16次，创索引、查询、删表、关库，预期失败
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_061, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=19 dbFileSize=4096 redoPubBufSize=16384 preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    int fileNum = 16;
    char filePath[256];
    GmeSqlTestCtx createMultiFiles;
    system("mkdir -p ./data/gmdb/newFileDir");
    for (int i = 0; i < fileNum; i++) {
        // 插入数据
        int insertNum = 0;
        ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
        EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
        // 创建文件
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, 1030000);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
/*
配置redoPubBufSize=16384、bufferPoolSize为40960，开库、创表、
循环执行插满数据（仅前3个文件插满）、创文件操作16次，创索引、查询、删表、关库，预期成功
*/
HWTEST_F(Multi_Files_Concurrent_Load, KBVector_018_062, TestSize.Level1) {
    ComWriteLog(LOG_STEP, "test start.");
    GmeConn *conn = NULL;
    int dim = 1024;
    // 修改配置项
    ret = ModifyCfgFile("dbFilesMaxCnt=19 dbFileSize=4096 redoPubBufSize=16384 preFetchPagesEnable=1 maxPreFetchThreNum=4 bufferPoolSize=40960", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    // 开库
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 创表
    vector<string> tables;
    tables.push_back("test_table_0");
    ret = CreateTable(conn, tables, dim);
    EXPECT_EQ(ret, GMERR_OK);
    int fileNum = 16;
    char filePath[256];
    GmeSqlTestCtx createMultiFiles;
    system("mkdir -p ./data/gmdb/newFileDir");
    for (int i = 0; i < fileNum; i++) {
        if (i < 3){
            // 插入数据
            int insertNum = 0;
            ret = InsertDataForMultiFiles(conn, tables.at(0), dim, insertNum);
            EXPECT_EQ(ret, GMERR_INSUFFICIENT_RESOURCES);
        }
        // 创建文件
        snprintf(filePath, sizeof(filePath), "%s/data/gmdb/newFileDir/test_file_%d", curPath, i);
        string filePathStr(filePath);
        createMultiFiles = {"ALTER TABLESPACE dbUserSpace ADD DATAFILE '"+ filePathStr +"';",GMERR_OK,{}};
        ret = GmeSqlStepAndCheck(conn, &createMultiFiles, 1);
        EXPECT_EQ(ret, GMERR_OK);
        // 检查文件是否存在
        EXPECT_EQ(access(filePath, F_OK), GMERR_OK);
    }
    // 创建lpas索引
    GmeSqlTestCtx createLpasIndex = {"CREATE INDEX lpas_l2_idx1 ON " + tables.at(0) + " USING gslpasmem(repr1 l2) WITH "
        "(LPAS_OUT_DEGREE=64, LPAS_BUILD_SEARCH_LIST_SIZE=32);",GMERR_OK,{}
    };
    ret = GmeSqlStepAndCheck(conn, &createLpasIndex, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询
    string queryVector = RandomFloat(1, 10, dim); 
    GmeSqlTestCtx qrySqlCtx = {
        "SELECT * FROM " + tables.at(0) + " ORDER BY repr1 <->'" + queryVector + "' LIMIT 10;", GMERR_OK, {}
    };
    ret = GmeSqlStepAndCheck(conn, &qrySqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 删表
    GmeSqlTestCtx dropSqlCtx = {"Drop TABLE " + tables.at(0) + " ;", GMERR_OK, {}};
    ret = GmeSqlStepAndCheck(conn, &dropSqlCtx, 1);
    EXPECT_EQ(ret, GMERR_OK);
    // 关库
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 重开库，并发加载
    ret = ModifyCfgFile("preFetchPagesEnable=1 maxPreFetchThreNum=4", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
