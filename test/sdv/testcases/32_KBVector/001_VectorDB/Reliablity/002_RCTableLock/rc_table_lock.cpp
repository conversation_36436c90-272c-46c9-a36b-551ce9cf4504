/*
* Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
* Description: LPAS RC表锁模式 加固测试
* Author: suweijian
* Create: 2025-05-22
*/

#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"
#include "common_perf.h"

using namespace std;
using namespace testing::ext;

const int PERF_LOOP = 3;

class TestRcTableLock : public testing::Test {
public:
    int ret;
    GmeConnT *conn = NULL;
    char *configPath = EmbSqlGetConfigPath();
    GmeConnT *g_conn_muti[MAX_THREAD] = {nullptr};

    GmeConnT **EmbSqlGetMultiConns()
    {
        return g_conn_muti;
    }
    GmeConnT *EmbSqlGetMultiConn(uint16_t i)
    {
        return g_conn_muti[i];
    }
    void EmbSqlSuitOpenMultiConn(uint16_t connNum)
    {
        for (uint16_t i = 0; i < connNum; i++) {
            EmbSqlOpenConn(&g_conn_muti[i], configPath);
        }
    }
    void EmbSqlSuitCloseMultiConn(uint16_t connNum)
    {
        for (uint16_t i = 0; i < connNum; i++) {
            EmbSqlCloseConn(g_conn_muti[i]);
        }
    }
    void MultiThreadPerfCheck(const string &tableName, QueryParamsT qParams, vector<PerfCheckContainerT> PContainer)
    {
        for (const auto& elem : PContainer) {
            EmbSqlSuitOpenMultiConn(elem.threadId);
            GmeConnT **conns = EmbSqlGetMultiConns();
            BatchQueryVectorCICheck(elem.perfExpect, elem.queryFilePath, elem.resultFilePath, tableName, &qParams, conns, elem.threadId, PERF_LOOP);
            EmbSqlSuitCloseMultiConn(elem.threadId);
        }
    }
    virtual void SetUp()
    {
        system("ipcrm -a");
    }
    virtual void TearDown()
    {
        system("ipcrm -a");
    }
};

enum ModeType {
    // 双形态(DURABLE_AND_BUFFERPOOL) 归一后配置
    DualMode_POC_UNIFY = 0,
    DualMode_POC_UNIFY_WithTableLock,
    DualMode_POC_UNIFY_100M,
    // DurableMemData 归一后配置
    Durable_POC_UNIFY,
    Durable_POC_UNIFY_WithTableLock
};

void ModifyConfiguration(const char *configPath, ModeType modeType, const char *dataPath)
{
    switch (modeType) {
        case DualMode_POC_UNIFY:{
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("featureNames=durablememdata,bufferpool,SQL,TRM,PERSISTENCE", configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile(dataPath, configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxConnNum=256 deviceSize=1024 pageSize=32 dbFileSize=16777216 maxSeMem=151200 maxTotalShmSize=161440\
                maxTotalDynSize=1560576 maxSysDynSize=1048576 extendSize=1048576 bufferPoolSize=66060288 bufferPoolPolicy=3", configPath));
            break;
        }
        case DualMode_POC_UNIFY_WithTableLock:{
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("featureNames=durablememdata,bufferpool,SQL,TRM,PERSISTENCE", configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile(dataPath, configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxConnNum=256 deviceSize=1024 pageSize=32 dbFileSize=16777216 maxSeMem=151200 maxTotalShmSize=161440\
                maxTotalDynSize=1560576 maxSysDynSize=1048576 extendSize=1048576 bufferPoolSize=66060288 bufferPoolPolicy=3 enableTableLock=1", configPath));
            break;
        }
        case DualMode_POC_UNIFY_100M:{
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("featureNames=durablememdata,bufferpool,SQL,TRM,PERSISTENCE", configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile(dataPath, configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxConnNum=256 deviceSize=1024 pageSize=64 dbFileSize=33554432 maxSeMem=716800 maxTotalShmSize=819200\
                maxTotalDynSize=1560576 maxSysDynSize=1048576 extendSize=1048576 bufferPoolSize=209715200 bufferPoolPolicy=3", configPath));
            break;
        }
        case Durable_POC_UNIFY:{
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("featureNames=durablememdata,SQL,TRM,PERSISTENCE", configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile(dataPath, configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxConnNum=256 deviceSize=1024 pageSize=32 dbFileSize=16777216 maxSeMem=151200 maxTotalShmSize=161440\
                maxTotalDynSize=1560576 maxSysDynSize=1048576 extendSize=1048576", configPath));
            break;
        }
        case Durable_POC_UNIFY_WithTableLock:{
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("featureNames=durablememdata,SQL,TRM,PERSISTENCE", configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile(dataPath, configPath));
            EXPECT_EQ(GMERR_OK, ModifyCfgFile("maxConnNum=256 deviceSize=1024 pageSize=32 dbFileSize=16777216 maxSeMem=151200 maxTotalShmSize=161440\
                maxTotalDynSize=1560576 maxSysDynSize=1048576 extendSize=1048576 enableTableLock=1", configPath));
            break;
        }
        default:
            break;
    }
}

// Check the number of records in the table.
uint32_t CheckCount(GmeConnT *conn, string tableName) {
    const char *viewSql = "select * from 'V$STORAGE_VERTEX_COUNT'";
    ViewResult resultSet;
    int ret = GmeSqlExecute(conn, viewSql, SqlViewResultCallBack, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    int recordNum = resultSet.colValues.size() / resultSet.colNames.size();
    string table;
    uint32_t count = 0;
    for (int i = 1; i <= recordNum; i++) {
        table = GetValueByColName(&resultSet, "table", i);
        if (table == tableName) {
            count = strtoul(GetValueByColName(&resultSet, "record count", i).c_str(), nullptr, PERF_LOOP);
            break;
        }
    }
    return count;
}

// RC表锁对比测试，1M768D_cohere, 加载数据，单线程非RC表锁模式
HWTEST_F(TestRcTableLock, Load_Data_1M768D_NormalMode, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string dataSetName = "1M768D_cohere";
    system("rm -rf ./data_1m768d/gmdb");
    system("mkdir -p ./data_1m768d/gmdb");
    char datasetPath[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());

    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_1m768d/gmdb");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    CreateTablePerf(conn, tableName, dim);
    LpasDuration loadDuration = {dataSetName, 200000};
    LoadData(conn, datasetPath, 1, tableName, dim, loadDuration);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}

// RC表锁对比测试，1M768D_cohere, 加载数据，RC表锁模式续写(16并发)
HWTEST_F(TestRcTableLock, Continue_Load_Data_1M768D_with_RCTableLock, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string dataSetName = "1M768D_cohere";
    char datasetPath[256];
    char cmd[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());

    (void)snprintf(cmd, sizeof(cmd), "kill -9 $(ps -ef |grep -w %s|grep -v grep|awk -F' ' '{print $2}')", "Load_Data_1M768D_NormalMode");
    system(cmd);
    sleep(2);
    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_1m768d/gmdb");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));

    uint32_t recordCnt = CheckCount(conn, "t_perf");
    cout << "*******[1M768D_cohere]: before continue insert 'recordCnt': " << recordCnt << endl;
    if (recordCnt >= 1000000) {
        return;
    } else {
        LpasDuration loadDuration = {dataSetName, 1000000};
        LpasMultiThrLoad multiThrLoad = {.thrNum = 16, .configPath = configPath, .fileStartPoint = recordCnt};
        LoadData(conn, datasetPath, 1, tableName, dim, loadDuration, multiThrLoad);
        recordCnt = CheckCount(conn, "t_perf");
        cout << "*******[1M768D_cohere]: continue load data with rctalelock - 32ThrNum"<< endl;
        cout << "*******[1M768D_cohere]: after continue insert 'recordCnt': " << recordCnt << endl;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
    }
}

// RC表锁对比测试，1M768D_cohere, 加载数据, 32线程并发RC表锁模式
HWTEST_F(TestRcTableLock, Load_Data_1M768D_RCLockMode_32ThrNum, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string dataSetName = "1M768D_cohere";
    system("rm -rf ./data_1m768d/gmdb");
    system("mkdir -p ./data_1m768d/gmdb");
    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_1m768d/gmdb");

    char datasetPath[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    CreateTablePerf(conn, tableName, dim);
    LpasDuration loadDuration = {dataSetName, 200000};
    uint32_t recordCnt = CheckCount(conn, "t_perf");
    LpasMultiThrLoad multiThrLoad = {.thrNum = 32, .configPath = configPath, .fileStartPoint = recordCnt};
    LoadData(conn, datasetPath, 1, tableName, dim, loadDuration, multiThrLoad);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}

// RC表锁对比测试，1M768D_cohere, 加载数据，单线程非RC表锁模式续写
HWTEST_F(TestRcTableLock, Continue_Load_Data_with_NormalMode, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string dataSetName = "1M768D_cohere";
    char datasetPath[256];
    char cmd[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());

    (void)snprintf(cmd, sizeof(cmd), "kill -9 $(ps -ef |grep -w %s|grep -v grep|awk -F' ' '{print $2}')", "Load_Data_1M768D_RCLockMode_32ThrNum");
    system(cmd);
    sleep(2);
    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_1m768d/gmdb");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));

    uint32_t recordCnt = CheckCount(conn, "t_perf");
    cout << "*******[1M768D_cohere]: before continue insert 'recordCnt': " << recordCnt << endl;
    if (recordCnt >= 1000000) {
        return;
    } else {
        LpasDuration loadDuration = {dataSetName, 1000000};
        LpasMultiThrLoad multiThrLoad = {.thrNum = 1, .configPath = configPath, .fileStartPoint = recordCnt};
        LoadData(conn, datasetPath, 1, tableName, dim, loadDuration, multiThrLoad);
        recordCnt = CheckCount(conn, "t_perf");
        cout << "*******[1M768D_cohere]: continue load data with 1ThrNum"<< endl;
        cout << "*******[1M768D_cohere]: after continue insert 'recordCnt': " << recordCnt << endl;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
    }
}

// RC表锁对比测试，1M768D_cohere, 加载数据, 64线程并发RC表锁模式
HWTEST_F(TestRcTableLock, Load_Data_1M768D_RCLockMode_64ThrNum, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string dataSetName = "1M768D_cohere";
    system("rm -rf ./data_1m768d/gmdb");
    system("mkdir -p ./data_1m768d/gmdb");
    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_1m768d/gmdb");

    char datasetPath[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    CreateTablePerf(conn, tableName, dim);
    LpasDuration loadDuration = {dataSetName, 200000};
    uint32_t recordCnt = CheckCount(conn, "t_perf");
    LpasMultiThrLoad multiThrLoad = {.thrNum = 64, .configPath = configPath, .fileStartPoint = recordCnt};
    LoadData(conn, datasetPath, 1, tableName, dim, loadDuration, multiThrLoad);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}

// RC表锁对比测试，1M768D_cohere, 加载数据, 128线程并发RC表锁模式
HWTEST_F(TestRcTableLock, Load_Data_1M768D_RCLockMode_128ThrNum, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string dataSetName = "1M768D_cohere";
    system("rm -rf ./data_1m768d/gmdb");
    system("mkdir -p ./data_1m768d/gmdb");
    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_1m768d/gmdb");

    char datasetPath[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    CreateTablePerf(conn, tableName, dim);
    LpasDuration loadDuration = {dataSetName, 200000};
    uint32_t recordCnt = CheckCount(conn, "t_perf");
    LpasMultiThrLoad multiThrLoad = {.thrNum = 128, .configPath = configPath, .fileStartPoint = recordCnt};
    LoadData(conn, datasetPath, 1, tableName, dim, loadDuration, multiThrLoad);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}

// RC表锁对比测试，1M768D_cohere, 预加载数据后LPAS建图
HWTEST_F(TestRcTableLock, Load_Data_1M768D_LPAS_Build, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string dataSetName = "1M768D_cohere";
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_1m768d/gmdb");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    LpasDuration buildDuration = {dataSetName, 10000};
    CreateIndexPerf(conn, tableName, indexName, indexType, distType, g_lpasParams, buildDuration);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}

// RC表锁对比测试，多线程并发预加载查询，1M768D_cohere, topk=100，L=128, 纯向量查询
HWTEST_F(TestRcTableLock, Test_1M768D_vectorquery, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    uint16_t topk = 100;
    uint16_t lpasQuerySearchListSize = 128;
    QueryParamsT params = {QueryType::VECTORQUERY, lpasQuerySearchListSize, 0, 0, topk, dim};
    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_1m768d/gmdb");

    string dataSetName = "1M768D_cohere";
    char queryFilePath[256];
    (void)snprintf(queryFilePath, sizeof(queryFilePath), "%s/%s/test.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    char resultFilePath[256];
    (void)snprintf(resultFilePath, sizeof(resultFilePath), "%s/%s/neighbors.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());

    // 添加性能预期，结构-{查询线程，{预期召回率，预期时延，预期每秒相应数}}
    vector<PerfCheckContainerT> PerfCheckContainerT;
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 1, {.recall = 0.95, .queryTime = 3.3, .qps = 300}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 64, {.recall = 0.95, .queryTime = 4.6, .qps = 14000}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 128, {.recall = 0.95, .queryTime = 5.2, .qps = 24594}});
    MultiThreadPerfCheck(tableName, params, PerfCheckContainerT);
}

// RC表锁对比测试，多线程并发预加载查询，1M768D_cohere, topk=100，L=12, 99%前过滤查询
HWTEST_F(TestRcTableLock, Test_1M768D_99PercentFilter, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    uint16_t topk = 100;
    uint16_t lpasQuerySearchListSize = 12;
    QueryParamsT params = {QueryType::NINTYNINEPERCENTFILTER, lpasQuerySearchListSize, 10000, 990000, topk, dim};
    ModifyConfiguration(configPath, DualMode_POC_UNIFY_WithTableLock, "dataFileDirPath=./data_1m768d/gmdb");

    string dataSetName = "1M768D_cohere";
    char queryFilePath[256];
    (void)snprintf(queryFilePath, sizeof(queryFilePath), "%s/%s/test.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    char resultFilePath[256];
    (void)snprintf(resultFilePath, sizeof(resultFilePath), "%s/%s/gt_L2_30000_tail_1p.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());

    // 添加性能预期，结构-{查询线程，{预期召回率，预期时延，预期每秒相应数}}
    vector<PerfCheckContainerT> PerfCheckContainerT;
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 1, {.recall = 0.95, .queryTime = 12.19, .qps = 82}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 64, {.recall = 0.95, .queryTime = 17.20, .qps = 3720}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 128, {.recall = 0.95, .queryTime = 27.67, .qps = 4625}});
    MultiThreadPerfCheck(tableName, params, PerfCheckContainerT);
}

// RC表锁对比测试，千万数据集10M768D_cohere,  Lpas建图, 32线程并发RC表锁模式
HWTEST_F(TestRcTableLock, Test_10M768D_Load_Data_32ThrNum, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    uint16_t topk = 100;
    system("rm -rf ./data_10m768d/gmdb");
    system("mkdir -p ./data_10m768d/gmdb");
    ModifyConfiguration(configPath, DualMode_POC_UNIFY, "dataFileDirPath=./data_10m768d/gmdb");

    string dataSetName = "10M768D_cohere";
    char datasetPath[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train_1.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeOpen(configPath, GME_OPEN_CREATE, &conn));
    ExtendUserSpaceForPerf(conn, 3, "data_10m768d");
    CreateTablePerf(conn, tableName, dim);
    LpasDuration loadDuration = {dataSetName, 130000};
    uint32_t recordCnt = CheckCount(conn, "t_perf");
    LpasMultiThrLoad multiThrLoad = {.thrNum = 32, .configPath = configPath, .fileStartPoint = recordCnt};
    LoadData(conn, datasetPath, 10, tableName, dim, loadDuration, multiThrLoad);
    LpasBuildParamsT LpasParams = {.gLpasOutDegree = 128, .gLpasBuildSearchListSize = 64};
    LpasDuration buildDuration = {dataSetName, 1700000};
    CreateIndexPerf(conn, tableName, indexName, indexType, distType, LpasParams, buildDuration);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeFlushData(conn, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmeClose(conn));
}

// RC表锁对比测试，千万数据集10M768D_cohere, 99%过滤查询
HWTEST_F(TestRcTableLock, Test_10M768D_99PercentFilter_query, TestSize.Level3)
{
    ComWriteLog(LOG_STEP, "test start.");
    string tableName = "t_perf";
    uint16_t dim = 768;
    string indexType = "GSLPASMEM";
    string distType = "L2";
    string indexName = "lpas_idx";
    uint16_t topk = 100;
    uint16_t lpasQuerySearchListSize = 115;
    QueryParamsT params = {QueryType::NINTYNINEPERCENTFILTER, lpasQuerySearchListSize, 100000, 9900000, topk, dim};
    ModifyConfiguration(configPath, DualMode_POC_UNIFY_WithTableLock, "dataFileDirPath=./data_10m768d/gmdb");

    string dataSetName = "10M768D_cohere";
    char datasetPath[256];
    (void)snprintf(datasetPath, sizeof(datasetPath), "%s/%s/shuffle_train_1.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    char queryFilePath[256];
    (void)snprintf(queryFilePath, sizeof(queryFilePath), "%s/%s/test.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());
    char resultFilePath[256];
    (void)snprintf(resultFilePath, sizeof(resultFilePath), "%s/%s/gt_L2_30000_tail_1p.parquet", getenv("VECTORDATA_HOME"), dataSetName.c_str());

    // 添加性能预期，结构-{查询线程，{预期召回率，预期时延，预期每秒相应数}}
    vector<PerfCheckContainerT> PerfCheckContainerT;
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 1, {.recall = 0.95, .queryTime = 90.90, .qps = 11}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 64, {.recall = 0.95, .queryTime = 91.42, .qps = 700}});
    PerfCheckContainerT.push_back({queryFilePath, resultFilePath, 128, {.recall = 0.95, .queryTime = 94.60, .qps = 1353}});
    MultiThreadPerfCheck(tableName, params, PerfCheckContainerT);
}
