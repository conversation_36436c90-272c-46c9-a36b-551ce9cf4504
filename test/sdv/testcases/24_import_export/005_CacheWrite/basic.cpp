/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 基础功能验证
 * Author: lushiguang
 * Create: 2023-11-6
 */


#include "../common/warm_reboot_common.h"

GmcConnT *g_conn2 = NULL;
GmcStmtT *g_stmt2 = NULL;
const char *g_nameSpace = (const char *)"basic_nsp";

class Basic : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxWriteCacheSize=500\"");
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void Basic::SetUp()
{
    printf("[INFO] check interface Start.\n");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt2, g_nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt2, g_nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void Basic::TearDown()
{
    int ret = GmcDropNamespace(g_stmt2, g_nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    printf("[INFO] check interface End.\n");
}

// db不存在数据，写缓存
TEST_F(Basic, imexport_005_02_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000,
            "use_write_cache": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    int ret = CommonCreateTable(g_stmt, g_specComplexLabel, (char *)"../schema/vl_special_complex.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    ret = GmcPrepareStmtByLabelName(g_stmt, g_specComplexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int beginVal = 0;
    int endVal = 1000;
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_complexSetLt4k, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    int expectCount = endVal - beginVal;
    ret = WaitCacheWriteDB(g_stmt, g_specComplexLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexRecord(g_stmt, g_specComplexLabel, g_indexName, g_cond, g_expect, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// db存在冲突数据，写缓存，mergePolicy配置GMC_MERGE_REPORT_ERROR
TEST_F(Basic, imexport_005_02_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000,
            "use_write_cache": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    int ret = CommonCreateTable(g_stmt, g_specComplexLabel, (char *)"../schema/vl_special_complex.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    ret = GmcPrepareStmtByLabelName(g_stmt, g_specComplexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int beginVal = 0;
    int endVal = 1000;
    ret = TestInsVertexSync(g_stmt, g_specComplexLabel, g_complexSetLt4k, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    endVal = endVal + 500;
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_complexSetLt4k, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    
    int expectCount = endVal - beginVal;
    ret = WaitCacheWriteDB(g_stmt, g_specComplexLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PRIMARY_KEY_VIOLATION);
    AW_CHECK_LOG_EXIST(SERVER, 1, "Unable to insert vertex when merging request");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写缓存写入大对象 预期：不能超过pageSize，超过写缓存接口返回错误
TEST_F(Basic, imexport_005_02_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000,
            "use_write_cache": true
        }
    )";
    char *g_complexSetGt32k = (char *)R"({
    "A0": %i{1},
    "A1": %i{10},
    "A2": %i{100},
    "A3": %i{1000},
    "A4": %i{1,100000,0.5},
    "A5": %i{10,100000,0.5},
    "A6": "%f{16}0",
    "A7": "0x%f{32}0",
    "A8": "0x%f{32}0",
    "A9": "%f{5000}c",
    "M0": [
    { "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{10000}t1" },
    { "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{10000}t2" },
    { "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{10000}t3" }
    ]
    })";
    (void)GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    int ret = CommonCreateTable(g_stmt, g_specComplexLabel, (char *)"../schema/vl_special_complex.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    ret = GmcPrepareStmtByLabelName(g_stmt, g_specComplexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int beginVal = 0;
    int endVal = 1000;
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_complexSetGt32k, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    }
    
    int expectCount = 0;
    ret = WaitCacheWriteDB(g_stmt, g_specComplexLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多表写缓存
TEST_F(Basic, imexport_005_02_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000,
            "use_write_cache": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_specComplexLabel, (char *)"../schema/vl_special_complex.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../schema/vl_simple.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int beginVal = 0;
    int endVal = 1000;
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};

    ret = GmcPrepareStmtByLabelName(g_stmt, g_specComplexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_complexSetLt4k, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcPrepareStmtByLabelName(g_stmt, g_simpleLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_simpleSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    

    int expectCount = endVal - beginVal;
    ret = WaitCacheWriteDB(g_stmt, g_specComplexLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitCacheWriteDB(g_stmt, g_simpleLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不同命名空间表写缓存
TEST_F(Basic, imexport_005_02_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000,
            "use_write_cache": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_specComplexLabel, (char *)"../schema/vl_special_complex.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt2, g_simpleLabel, (char *)"../schema/vl_simple.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int beginVal = 0;
    int endVal = 1000;
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};

    ret = GmcPrepareStmtByLabelName(g_stmt, g_specComplexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_complexSetLt4k, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcPrepareStmtByLabelName(g_stmt2, g_simpleLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_simpleSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt2, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    

    int expectCount = endVal - beginVal;
    ret = WaitCacheWriteDB(g_stmt, g_specComplexLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitCacheWriteDB(g_stmt2, g_simpleLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt2, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 表配置use_write_cache为false，调用写缓存接口写数据(现无独立缓存写接口，仅由表配置决定)
TEST_F(Basic, imexport_005_02_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000,
            "use_write_cache": false
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    int ret = CommonCreateTable(g_stmt, g_specComplexLabel, (char *)"../schema/vl_special_complex.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    ret = GmcPrepareStmtByLabelName(g_stmt, g_specComplexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int beginVal = 0;
    int endVal = 1000;
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_complexSetLt4k, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    
    int expectCount = endVal - beginVal;
    ret = WaitCacheWriteDB(g_stmt, g_specComplexLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 表配置use_write_cache缺省，调用写缓存接口写数据
TEST_F(Basic, imexport_005_02_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    int ret = CommonCreateTable(g_stmt, g_specComplexLabel, (char *)"../schema/vl_special_complex.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    ret = GmcPrepareStmtByLabelName(g_stmt, g_specComplexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int beginVal = 0;
    int endVal = 1000;
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_complexSetLt4k, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    
    int expectCount = endVal - beginVal;
    ret = WaitCacheWriteDB(g_stmt, g_specComplexLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_specComplexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写缓存后，缓存还未写DB，删表
TEST_F(Basic, imexport_005_02_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000,
            "use_write_cache": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../schema/vl_simple.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    ret = GmcPrepareStmtByLabelName(g_stmt, g_simpleLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int beginVal = 0;
    int endVal = 100000;
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_simpleSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写缓存后，缓存还未写DB，删表后重建同名表，重新写入
TEST_F(Basic, imexport_005_02_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *config = (char *)R"(
        {
            "max_record_count": 1000000,
            "use_write_cache": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../schema/vl_simple.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int beginVal = 0;
    int endVal = 100000;

    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    ret = GmcPrepareStmtByLabelName(g_stmt, g_simpleLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_simpleSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 删表后重建表，重新写数据
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../schema/vl_simple.gmjson", config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    endVal = 1000;

    ret = GmcPrepareStmtByLabelName(g_stmt, g_simpleLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(g_simpleSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        json_decref(dataJson);
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    int expectCount = endVal - beginVal;
    ret = WaitCacheWriteDB(g_stmt, g_simpleLabel, expectCount, beginVal, endVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

