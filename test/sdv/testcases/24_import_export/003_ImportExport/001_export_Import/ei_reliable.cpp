/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 异常可靠场景测试
 * Author: lushiguang
 * Create: 2023-10-16
 */

#include "../../common/warm_reboot_common.h"

int g_beginIndex = 0;
int g_endIndex = 500;
char g_exportDir[512] = {0};

class Reliable : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        (void)system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void Reliable::SetUp()
{
    printf("[INFO] check Reliable Start.\n");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(g_exportDir, "%s/export_dir", pwdDir);
    (void)Rmdir(g_exportDir);
    int ret = mkdir(g_exportDir, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void Reliable::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    printf("[INFO] check Reliable End.\n");
}

// 001.重复导入
TEST_F(Reliable, imexport_003_001_04_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char expect1[] = "successfully";
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, expect1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重复导入
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.导入时，表不存在
TEST_F(Reliable, imexport_003_001_04_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 3", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 2", g_exportDir);
    system(tempCmd);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 2");
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);
    // 建表后重新导入
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.导入时，表存在，namespace不同
TEST_F(Reliable, imexport_003_001_04_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *nameSpace = (const char *)"nspbep";
    // 清理
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char expect1[200] = {0};
    (void)snprintf(expect1, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk", g_exportDir);
    system(tempCmd);
    ret = ExecuteAndExpect(tempCmd, expect1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    // 查询数据
    int expectCount = 0;
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.开启crc校验，未破坏文件，验证导入
TEST_F(Reliable, imexport_003_001_04_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s", g_exportDir);
    system(tempCmd);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.开启crc校验，破坏导出的文件，验证导入
TEST_F(Reliable, imexport_003_001_04_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 截断其中一个文件
    char dbFile[512] = {0};
    (void)sprintf(dbFile, "%s/vl_general_complex public vl_general_complex 0.bin_data", g_exportDir);
    float percentage = 0.5;
    char direction = (char)'a';
    ret = TruncateFile(dbFile, percentage, direction);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s", g_exportDir);
    system(tempCmd);
    ret = ExecuteAndExpect(tempCmd, (char *)"prepare bin file unsucc, ret = 1015001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 破坏的文件导入失败
    int expectCount = 0;
    ret = TestSelVertexCount(g_stmt, g_complexLabel, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.导入时，内存不足
TEST_F(Reliable, imexport_003_001_04_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 5000;
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    (void)system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=16\"");
    ret = RestartServerAndConn();
    (void)system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s", g_exportDir);
    system(tempCmd);
    ret = ExecuteAndExpect(tempCmd, (char *)"unsucc");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OUT_OF_MEMORY);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.写满内存时，导出
TEST_F(Reliable, imexport_003_001_04_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=16\"");
    int ret = RestartServerAndConn();
    (void)system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    g_endIndex = 5000;
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = 0;
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    ret = GmcPrepareStmtByLabelName(g_stmt, g_complexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = g_beginIndex; i < g_endIndex; i++) {
        ret = GenFormatDataByIndex(g_complexSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        free(jStr);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            AW_FUN_Log(LOG_STEP, "GMERR_OUT_OF_MEMORY.");
            expectCount = i;
            break;
        }
    }
    json_decref(dataJson);

    // 导出
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OUT_OF_MEMORY);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.多次异常导出，查看是是否异常
TEST_F(Reliable, imexport_003_001_04_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    int ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f xxx%s -st disk -rc crc", g_exportDir);
    int exportTime = 100;
    for (int i = 0; i < exportTime; i++) {
        ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
        AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);
    }

    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.多次异常导入，查看是是否异常
TEST_F(Reliable, imexport_003_001_04_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    int ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char expect2[200] = {0};
    (void)snprintf(expect2, 200, "export label %s successfully", g_complexLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, expect2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f xxxx%s", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"1015000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int importTime = 100;
    for (int i = 0; i < importTime; i++) {
        ret = ExecuteAndExpect(tempCmd, (char *)"1015000");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 正确导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_complexLabel, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.正常导出，异常导出场景下模拟磁盘卸载验证文件句柄是否关闭, 手工用例
TEST_F(Reliable, DISABLED_imexport_003_001_04_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 模拟挂载用例目录
    // dd if=/dev/zero of=exportdir.img bs=25M count=1"
    // losetup /dev/loop2 exportdir.img
    // mkfs.xfs /dev/loop2
    // losetup -d /dev/loop2
    // mount -o loop exportdir.img 003_export_Import

    // 模拟卸载
    // umount 003_export_Import
    // rm -rf exportdir.img

    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_complexLabel, (char *)"../../schema/vl_general_complex.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_endIndex = 20;
    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int endIndex = 6000;  // ~30M
    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_endIndex, endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, g_endIndex, endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出
    ret = ExecuteAndExpect(tempCmd, (char *)"successfully");
    // 手动测试时模拟磁盘空间不足时，此处修改为失败
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

