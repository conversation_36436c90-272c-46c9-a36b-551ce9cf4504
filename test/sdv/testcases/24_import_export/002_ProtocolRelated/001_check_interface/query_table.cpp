/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 查询表功能场景测试
 * Author: lushiguang
 * Create: 2023-10-16
 */


#include "../../common/warm_reboot_common.h"

class QueryTable : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void QueryTable::SetUp()
{
    printf("[INFO] check interface Start.\n");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void QueryTable::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    printf("[INFO] check interface End.\n");
}

// 001.创建vertex表，查询表是否存在
TEST_F(QueryTable, imexport_002_02_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, g_simpleLabel, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, true);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.创建kv表，查询表是否存在
TEST_F(QueryTable, imexport_002_02_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *configJson = (char *)R"({
    "name": "system_info",
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        },
        {
        "name": "sys_name",
        "type": "string"
        },
        {
        "name": "ls_id",
        "type": "uint32"
        }
    ]
    })";

    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_stmt, kvTableName);
    int ret = GmcKvCreateTable(g_stmt, kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, kvTableName, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, true);

    ret = GmcKvDropTable(g_stmt, kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.删除vertex表，查询表是否存在
TEST_F(QueryTable, imexport_002_02_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, g_simpleLabel, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.删除kv表，查询表是否存在
TEST_F(QueryTable, imexport_002_02_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *configJson = (char *)R"({
    "name": "system_info",
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        },
        {
        "name": "sys_name",
        "type": "string"
        },
        {
        "name": "ls_id",
        "type": "uint32"
        }
    ]
    })";

    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_stmt, kvTableName);
    int ret = GmcKvCreateTable(g_stmt, kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, kvTableName, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, false);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.删除后重建同名kv表，查询表是否存在
TEST_F(QueryTable, imexport_002_02_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, g_simpleLabel, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, true);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.删除后重建同名vertex表，查询表是否存在
TEST_F(QueryTable, imexport_002_02_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *configJson = (char *)R"({
    "name": "system_info",
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        },
        {
        "name": "sys_name",
        "type": "string"
        },
        {
        "name": "ls_id",
        "type": "uint32"
        }
    ]
    })";

    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_stmt, kvTableName);
    int ret = GmcKvCreateTable(g_stmt, kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmt, kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, kvTableName, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, true);
    ret = GmcKvDropTable(g_stmt, kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.相似名称查询表是否存在
TEST_F(QueryTable, imexport_002_02_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *similarName = (char *)"vl_simple1";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, similarName, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, false);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.非默认命名空间下表查询
TEST_F(QueryTable, imexport_002_02_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *nameSpace = (const char *)"nspbep1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, g_simpleLabel, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, true);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.跨命名空间表查询
TEST_F(QueryTable, imexport_002_02_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *nameSpace = (const char *)"nspbep2";
    ret = GmcCreateNamespace(stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isExist = false;
    ret = GmcLabelIsExist(stmt, g_simpleLabel, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, false);

    ret = CommonCreateTable(stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcLabelIsExist(stmt, g_simpleLabel, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, true);

    ret = GmcDropVertexLabel(stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.Edgelabel表查询
TEST_F(QueryTable, imexport_002_02_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema = NULL;
    char *schema2 = NULL;
    char *edgeSchema = NULL;
    char labelName[] = "T20";
    char labelName2[] = "T21";
    char edgeLabelName[] = "from_T20_to_T21";

    readJanssonFile("../../schema/byte_bitfield_type_continuous_schema.gmjson", &schema);
    AW_MACRO_ASSERT_NOTNULL(schema);
    readJanssonFile("../../schema/byte_bitfield_type_discontinuous_schema.gmjson", &schema2);
    AW_MACRO_ASSERT_NOTNULL(schema2);
    readJanssonFile("../../schema/edge_and_byte_bitfield.gmjson", &edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(edgeSchema);
    char labelConfig[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";
    int ret = GmcCreateVertexLabel(g_stmt, schema, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema2, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, edgeSchema, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema);
    schema = NULL;
    free(schema2);
    schema2 = NULL;
    free(edgeSchema);
    edgeSchema = NULL;
    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, edgeLabelName, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, false);
    ret = GmcDropEdgeLabel(g_stmt, edgeLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 非normal表查询
TEST_F(QueryTable, imexport_002_02_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = (const char *)"nspbep3";
    nspCfg.trxCfg.trxType = GMC_OPTIMISTIC_TRX;
    nspCfg.trxCfg.isolationLevel = GMC_TX_ISOLATION_REPEATABLE;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = NULL;

    char vertexLabel[20] = "Container1";
    char labelJson[1024] = "[{\"type\":\"container\", \"name\":\"Container1\","
        "\"fields\":[{\"name\":\"ID\", \"type\":\"uint32\", \"nullable\":false},"
        "{\"name\":\"PID\", \"type\":\"uint32\", \"nullable\":false},"
        "{\"name\":\"F0\", \"type\":\"string\", \"size\":200, \"nullable\":false},"
        "{\"name\":\"F1\", \"type\":\"int32\", \"nullable\":false}],"
        "\"keys\":[{\"node\":\"Container1\", \"name\":\"T1_1.PK\", \"fields\":[\"ID\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    const char *vertexConfJson = R"({"max_record_count":1000, "isFastReadUncommitted":false, "yang_model":1})";
    (void)GmcUseNamespace(g_stmt, nspCfg.namespaceName);
    (void)GmcDropVertexLabel(g_stmt, vertexLabel);
    (void)GmcDropNamespace(g_stmt, nspCfg.namespaceName);

    int ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nspCfg.namespaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, vertexConfJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isExist = false;
    ret = GmcLabelIsExist(g_stmt, vertexLabel, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isExist, true);
    ret = GmcDropVertexLabel(g_stmt, vertexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nspCfg.namespaceName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_OBJECT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.异步连接查询
TEST_F(QueryTable, imexport_002_02_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isExist = false;
    ret = GmcLabelIsExist(g_stmtAsync, g_simpleLabel, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 传入系统表名查询
TEST_F(QueryTable, imexport_002_02_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isExist = false;
    char *sysLable = (char *)"GM_SYS_NSP";
    ret = GmcLabelIsExist(g_stmt, sysLable, &isExist);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无权限
    AW_MACRO_EXPECT_EQ_BOOL(isExist, false);

    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

