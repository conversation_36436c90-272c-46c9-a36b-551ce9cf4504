/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 表多实例
 * Author: lushiguang
 * Create: 2023-10-16
 */


#include "../../common/warm_reboot_common.h"

class Interface : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void Interface::SetUp()
{
    printf("[INFO] check interface Start.\n");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void Interface::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    printf("[INFO] check interface End.\n");
}

// 001.GmcDuplicateVertexLabelWithName， 参数正确
TEST_F(Interface, imexport_004_001_01_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *newLableName = (char *)"new_vl_simple";
    ret = GmcDuplicateVertexLabelWithName(g_stmt, g_simpleLabel, newLableName, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, newLableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.GmcDuplicateVertexLabelWithName，stmt为NULL
TEST_F(Interface, imexport_004_001_01_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *newLableName = (char *)"new_vl_simple";
    ret = GmcDuplicateVertexLabelWithName(NULL, g_simpleLabel, newLableName, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.GmcDuplicateVertexLabelWithName，originLabel为NULL
TEST_F(Interface, imexport_004_001_01_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *newLableName = (char *)"new_vl_simple";
    ret = GmcDuplicateVertexLabelWithName(g_stmt, NULL, newLableName, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.GmcDuplicateVertexLabelWithName，newLabel为NULL
TEST_F(Interface, imexport_004_001_01_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDuplicateVertexLabelWithName(g_stmt, g_simpleLabel, NULL, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.GmcDuplicateVertexLabelWithName，config为NULL
TEST_F(Interface, imexport_004_001_01_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *newLableName = (char *)"new_vl_simple";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, newLableName);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDuplicateVertexLabelWithName(g_stmt, g_simpleLabel, newLableName, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *checkConfig = (char *)"MAX_RECORD_COUNT: 1000000";
    ret = executeCommand(
        (char *)"gmsysview -q \"V\\$CATA_VERTEX_LABEL_INFO\"|grep -A10 'VERTEX_LABEL_NAME: new_vl_simple'",
        checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, newLableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.GmcDuplicateVertexLabelWithName，config为非json
TEST_F(Interface, imexport_004_001_01_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *newLableName = (char *)"new_vl_simple";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, newLableName);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *config = (char *)R"(
        {
            "max_record_count":1000000,,
        }
    )";
    ret = GmcDuplicateVertexLabelWithName(g_stmt, g_simpleLabel, newLableName, config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_JSON_CONTENT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.GmcDuplicateVertexLabelWithName，newLabel超长
TEST_F(Interface, imexport_004_001_01_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *newLableName = (char *)"new_vl_simple";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, newLableName);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char lableName[513] = {0};
    (void)memset(lableName, 'a', 512);
    lableName[512] = '\0';

    ret = GmcDuplicateVertexLabelWithName(g_stmt, g_simpleLabel, lableName, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_VALUE, ret);

    (void)memset(lableName, 'a', 511);
    lableName[511] = '\0';

    ret = GmcDuplicateVertexLabelWithName(g_stmt, g_simpleLabel, lableName, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, lableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_VALUE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.GmcDuplicateVertexLabelWithName，newLabel命名非法
TEST_F(Interface, imexport_004_001_01_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *newLableName = (char *)"^&&##@!(&($!@##)!@*#*!)0#&*";
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, newLableName);
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDuplicateVertexLabelWithName(g_stmt, g_simpleLabel, newLableName, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_NAME);
    AW_FUN_Log(LOG_STEP, "test end.");
}

