/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : GMDBV502 迭代一 支持多事务配置模式下共实例执行
 Notes        : 本文件主要针对yang模式+RC模式+lite表锁开，三种事务配置同时存在的dml操作验证（yang模式就是RR+乐观）
 History      :
 Author       : jiangshan/j30011431
 Modification : [2022.08.09]
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "MultiTransInstance_common.h"


class MultiTransInstance_test_007_001 : public testing::Test {
public:
    virtual void SetUp()
    {
        printf("\n======================TEST:BEGIN======================\n");
        // 建立同步连接
        int ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 建立异步连接
        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        testGmcDisconnect(g_conn_async);
        testGmcDisconnect(g_conn);
        printf("\n======================TEST:END========================\n");
    }

protected:
    static void SetUpTestCase()
    {
        // 修改配置项，关闭轻量化
        system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");  // 恢复默认配置
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

class MultiTransInstance_test_007_002 : public testing::Test {
public:
    virtual void SetUp()
    {
        printf("\n======================TEST:BEGIN======================\n");
        // 建立同步连接
        int ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 建立异步连接
        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        testGmcDisconnect(g_conn_async);
        testGmcDisconnect(g_conn);
        printf("\n======================TEST:END========================\n");
    }

protected:
    static void SetUpTestCase()
    {
        // 修改配置项，关闭轻量化
        system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=1\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=1\"");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");  // 恢复默认配置
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

/*****************************************************************************
Description  : 001.namespaceA表1，namespaceB表2，namespaceC表3；线程1：yang模式DML（namespace级显示开启事务）;
线程2：RC模式（namespace级）；线程3：LiteTrx表锁开（系统级）
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_002, Multi_Instance_002_007_001)
{
    int ret;
    int operNum = 1000;

    // 使用默认namespace建lite表锁开
    createIp4forwardLabelAsync(g_stmt_async, g_trAndTrConfig);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RC+悲观，并且建RC表
    createNameSpaceWithCfgRcAndPeAsync(g_stmt_async, g_namespace03, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadRcPeReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadReplaceIpforwardTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;
    ret = useNameSpace(g_stmt, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    ret = useNameSpace(g_stmt, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    ret = useNameSpaceAsync(g_stmt_async, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace03);
}

/*****************************************************************************
Description  : 002.namespaceA表1，namespaceB表2，namespaceC表3；线程1：yang模式DML（namespace级显示开启事务）;
线程2：RC模式（namespace级显示开启事务）；线程3：LiteTrx表锁开（系统级）
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_002, Multi_Instance_002_007_002)
{
    int ret;
    int operNum = 1000;

    // 使用默认namespace建lite表锁开
    createIp4forwardLabelAsync(g_stmt_async, g_trAndTrConfig);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RC+悲观，并且建RC表
    createNameSpaceWithCfgRcAndPeAsync(g_stmt_async, g_namespace03, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadTrxRcPeReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadReplaceIpforwardTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    ret = useNameSpace(g_stmt, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    ret = useNameSpaceAsync(g_stmt_async, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace03);
}

/*****************************************************************************
Description  : 003.namespaceA表1，namespaceB表2，namespaceC表3；线程1：yang模式DML（namespace级显示开启事务）;
线程2：RC模式（系统级）；线程3：LiteTrx表锁开（表级）
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_001, Multi_Instance_002_007_003)
{
    int ret;
    int operNum = 1000;

    // 使用默认namespace，默认RC模式
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    // 创建一个namespce，使用namespace建lite表锁开
    createNameSpaceAsync(g_stmt_async, g_namespace04, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace04);
    createIp4forwardLabelAsync(g_stmt_async, g_trAndTrConfig);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadLiteCloseReplaceIpforwardTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    ret = useNameSpace(g_stmt, g_namespace04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace04);
}

/*****************************************************************************
Description  : 004.namespaceA表1，namespaceB表2，namespaceC表3；线程1：yang模式DML（namespace级显示开启事务）;
线程2：RC模式（系统级显示开启事务）；线程3：LiteTrx表锁开（表级）
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_001, Multi_Instance_002_007_004)
{
    int ret;
    int operNum = 1000;

    // 使用默认namespace，默认RC模式
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);
    
    // 创建一个namespce，使用namespace建lite表锁开
    createNameSpaceAsync(g_stmt_async, g_namespace04, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace04);
    createIp4forwardLabelAsync(g_stmt_async, g_trAndTrConfig);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadTrxReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadLiteCloseReplaceIpforwardTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    ret = useNameSpace(g_stmt, g_namespace04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace04);
}

/*****************************************************************************
Description  : 005.namespaceA表1，namespaceB表2，namespaceC表3；线程1：yang模式DML（namespace级显示开启事务）;
线程2：RC模式（namespace级）；线程3：LiteTrx表锁开（表级）
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_002, Multi_Instance_002_007_005)
{
    int ret;
    int operNum = 1000;

    // 创建1个namespace RC+悲观，并且建RC表
    createNameSpaceWithCfgRcAndPeAsync(g_stmt_async, g_namespace03, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    // 创建一个namespce，使用namespace建lite表锁开
    createNameSpaceAsync(g_stmt_async, g_namespace04, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace04);
    createIp4forwardLabelAsync(g_stmt_async, g_trAndTrConfig);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadRcPeReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadLiteCloseReplaceIpforwardTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    ret = useNameSpace(g_stmt, g_namespace04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace03);
    dropNameSpaceAsync(g_stmt_async, g_namespace04);
}

/*****************************************************************************
Description  : 006.namespaceA表1，namespaceB表2，namespaceC表3；线程1：yang模式DML（namespace级显示开启事务）;
线程2：RC模式（namespace级显示开启事务）；线程3：LiteTrx表锁开（表级）
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_002, Multi_Instance_002_007_006)
{
    int ret;
    int operNum = 1000;

    // 创建1个namespace RC+悲观，并且建RC表
    createNameSpaceWithCfgRcAndPeAsync(g_stmt_async, g_namespace03, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    // 创建一个namespce，使用namespace建lite表锁开
    createNameSpaceAsync(g_stmt_async, g_namespace04, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace04);
    createIp4forwardLabelAsync(g_stmt_async, g_trAndTrConfig);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadTrxRcPeReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadLiteCloseReplaceIpforwardTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    ret = useNameSpace(g_stmt, g_namespace04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace03);
    dropNameSpaceAsync(g_stmt_async, g_namespace04);
}

/*****************************************************************************
Description  : 007.namespaceA表1，namespaceB表2和表3；线程1：yang模式DML（namespace级显示开启事务）；
线程2：RC模式（系统级）；线程3：LiteTrx表锁开（表级）；
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_001, Multi_Instance_002_007_007)
{
    int ret;
    int operNum = 1000;

    // 使用默认namespace，默认RC模式、lite表锁关
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);
    createCustomerLabelAsync(g_stmt_async, g_trAndTrConfig);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadReplaceCustomTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_customerName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(operNum, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropCustomerLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
}

/*****************************************************************************
Description  : 008.namespaceA表1，namespaceB表2和表3；线程1：yang模式DML（namespace级显示开启事务）；
线程2：RC模式（系统级显示开启事务）；线程3：LiteTrx表锁开（表级）；
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_001, Multi_Instance_002_007_008)
{
    int ret;
    int operNum = 1000;

    // 使用默认namespace，默认RC模式、lite表锁关
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);
    createCustomerLabelAsync(g_stmt_async, g_trAndTrConfig);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadTrxReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadReplaceCustomTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_customerName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(operNum, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_defaultNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropCustomerLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
}

/*****************************************************************************
Description  : 009.namespaceA表1，namespaceB表2和表3,；线程1：yang模式DML（namespace级显示开启事务）；
线程2：RC模式（namespace级）；线程3：LiteTrx表锁开（表级）；
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_002, Multi_Instance_002_007_009)
{
    int ret;
    int operNum = 1000;

    // 创建1个namespace RC+悲观，并且建RC表、lite表锁关
    createNameSpaceWithCfgRcAndPeAsync(g_stmt_async, g_namespace03, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);
    createCustomerLabelAsync(g_stmt_async, g_trAndTrConfig);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadRcPeReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadRcPeReplaceCustomTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_customerName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(operNum, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropCustomerLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace03);
}

/*****************************************************************************
Description  : 010.namespaceA表1，namespaceB表2和表3,；线程1：yang模式DML（namespace级显示开启事务）；
线程2：RC模式（namespace级显示开启事务）；线程3：LiteTrx表锁开（表级）；
Notes        : 
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.11]
*****************************************************************************/
TEST_F(MultiTransInstance_test_007_002, Multi_Instance_002_007_010)
{
    int ret;
    int operNum = 1000;

    // 创建1个namespace RC+悲观，并且建RC表、lite表锁开
    createNameSpaceWithCfgRcAndPeAsync(g_stmt_async, g_namespace03, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    createIp4forwardLabelAsync(g_stmt_async, g_falseConfigJson);
    createCustomerLabelAsync(g_stmt_async, g_trAndTrConfig);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);

    int thrNum = 3;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadTrxRcPeReplaceIpforwardTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, ThreadRcPeReplaceCustomTest, (void *)&index[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);
    pthread_join(thr_arr[2], &thr_ret[2]);

    // 查询yang表和lite表数据，插入成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    ret = useNameSpace(g_stmt, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    scanIp4fowardLabel(g_stmt, operNum);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_customerName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(operNum, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dropIp4forwardLabelAsync(g_stmt_async);
    dropCustomerLabelAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace03);
}
