/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : GMDBV502 迭代一 支持多事务配置模式下共实例执行
 Notes        : 本文件主要针对yang模式+yang模式，两种事务配置同时存在的dml操验证（yang模式就是RR+乐观）
 History      :
 Author       : jiangshan/j30011431
 Modification : [2022.08.09]
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "MultiTransInstance_common.h"


class MultiTransInstance_test_002_001 : public testing::Test {
public:
    virtual void SetUp()
    {
        printf("\n======================TEST:BEGIN======================\n");
        // 建立同步连接
        int ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 建立异步连接
        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
        const int errCodeLen = 1024;
        char errorMsg1[errCodeLen] = {0};
        (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    }
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        testGmcDisconnect(g_conn_async);
        testGmcDisconnect(g_conn);
        printf("\n======================TEST:END========================\n");
    }

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

class MultiTransInstance_test_002_002 : public testing::Test {
public:
    virtual void SetUp()
    {
        printf("\n======================TEST:BEGIN======================\n");
        // 建立同步连接
        int ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 建立异步连接
        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    }
    virtual void TearDown()
    {
        testGmcDisconnect(g_conn_async);
        testGmcDisconnect(g_conn);
        printf("\n======================TEST:END========================\n");
    }

protected:
    static void SetUpTestCase()
    {
        // 修改配置项，乐观+RR
        system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=1\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");  // 恢复默认配置
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

/*****************************************************************************
Description  : 001.同一namespace下，操作同一张表，系统级yang模式；线程1：yang模式DML（显示开启事务），
线程2：yang模式DML（显示开启事务）；
Notes        : 先提交的先成功，使用事务先后commit方式执行校验
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.09]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_001)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace,事务2：replace*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 需要增加异步subtree查询操作

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 需要增加异步subtree查询操作

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    uint32_t PID = 2;

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 002.同一namespace下，操作同一张表，namespace级yang模式；线程1：yang模式DML（显示开启事务）；
线程2：yang模式DML（显示开启事务）；
Notes        : 先提交的先成功，使用事务先后commit方式执行校验
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.09]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_002)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 需要增加异步subtree查询操作

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 需要增加异步subtree查询操作

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);

    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 2;
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(1, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace01, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 003.同一namespace下，操作不同表，系统级yang模式；线程1：表1，yang模式DML（显示开启事务）；
线程2：表2，yang模式DML（显示开启事务）；
Notes        : 先提交的先成功，使用事务先后commit方式执行校验
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.09]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_003)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表 1
    createYangConConAsync(g_stmt_async);
    // 建yang表 2
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 开启事务1,操作表1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConConAsync(connAsync[0], stmtAsync[0]);

    // 开启事务2，操作表2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2查询插入的数据成功
    uint32_t PID = 1;

    // 表1有记录
    uint64_t count = 1;
    ret = GmcGetVertexCount(g_stmt, g_nameConConRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(1, count);

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    deleteNamespaceData2Async(g_conn_async,g_stmt_async);
    deleteYangConConAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 004.同一namespace下，操作不同表，namespace级yang模式；线程1：表1，yang模式DML（显示开启事务）；
线程2：表2，yang模式DML（显示开启事务）；
Notes        : 先提交的先成功，使用事务先后commit方式执行校验
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.09]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_004)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    AsyncUserDataT data = {0};

    // 创建1个namespace RR+乐观，并且建两个yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建yang表 1
    createYangConConAsync(g_stmt_async);
    // 建yang表 2
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1,操作表1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConConAsync(connAsync[0], stmtAsync[0]);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2，操作表2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2查询插入的数据成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;
    // 表1有记录
    uint64_t count = 1;
    ret = GmcGetVertexCount(g_stmt, g_nameConConRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(1, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace01, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 005.不同namespace下，操作不同表，表1属于namespaceA，表2属于namespaceB，系统级yang模式；
线程1：namespace A 表1，yang模式DML（系统级显示开启事务)；线程2：namespaceB 表2，yang模式DML（系统级显示开启事务）；
Notes        : 没有冲突使用并发的方式验证
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.09]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_005)
{
    int ret;

    // 创建2个namespace RR+乐观，并且分别建两个yang表
    // 创建namespace A和yang表1
    createNameSpaceAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace B和yang表2
    createNameSpaceAsync(g_stmt_async, g_namespace02, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);
    AW_FUN_Log(LOG_STEP, "建表");

    int thrNum = 2;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadReplaceConChoicsCaseTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);

    // 表2查询插入的数据成功
    ret = useNameSpace(g_stmt, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    // 表1插入数据成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PID = 1;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace02);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace02);
}

/*****************************************************************************
Description  : 006.不同namespace下，操作不同表，表1属于namespaceA，表2属于namespaceB，系统级yang模式；
线程1：namespace A 表1，yang模式DML（系统级显示开启事务)；线程2：namespaceB 表2，yang模式DML（namespace级显示开启事务）；
Notes        : 没有冲突使用并发的方式验证
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.09]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_006)
{
    int ret;

    // 创建2个namespace RR+乐观，并且分别建两个yang表
    // 创建namespace A和yang表1
    createNameSpaceAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建namespace B和yang表2
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace02, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    int thrNum = 2;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadReplaceConChoicsCaseTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);

    // 表2查询插入的数据成功
    ret = useNameSpace(g_stmt, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    // 表1插入数据成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PID = 1;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace02);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace02);
}

/*****************************************************************************
Description  : 007.不同namespace下，操作不同表，表1属于namespaceA，表2属于namespaceB，系统级yang模式；
线程1：namespace A 表1，yang模式DML（namespace级显示开启事务)；线程2：namespaceB 表2，yang模式DML（namespace级显示开启事务）；
Notes        :
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.09]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_007)
{
    int ret;

    // 创建2个namespace RR+乐观，并且分别建两个yang表
    // 创建namespace A和yang表1
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace B和yang表2
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace02, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);
    AW_FUN_Log(LOG_STEP, "建表");

    int thrNum = 2;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadReplaceConChoicsCaseTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);

    // 表2查询插入的数据成功
    ret = useNameSpace(g_stmt, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    // 表1插入数据成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PID = 1;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace02);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace02);
}

/*****************************************************************************
Description  : 008.同一namespace下，操作同一张表，系统级yang模式；线程1：yang模式DML（显示开启事务），
线程2：yang模式DML（显示开启事务），主动回滚；
Notes        : 补充异常场景
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.22]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_008)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先回滚
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务1操作成功
    uint32_t PID = 1;

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 009.同一namespace下，操作同一张表，namespace级yang模式；线程1：yang模式DML（显示开启事务）；
线程2：yang模式DML（显示开启事务），主动回滚；
Notes        :  补充异常场景
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.22]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_009)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先回滚
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务1操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 010.同一namespace下，操作不同表，系统级yang模式；线程1：表1，yang模式DML（显示开启事务）；
线程2：表2，yang模式DML（显示开启事务），主动回滚；
Notes        : 补充异常场景
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.22]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_010)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表 1
    createYangConConAsync(g_stmt_async);
    // 建yang表 2
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 开启事务1,操作表1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConConAsync(connAsync[0], stmtAsync[0]);

    // 开启事务2，操作表2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先回滚
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1查询插入的数据成功
    uint32_t PID = 1;

    // 表2无记录
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    deleteNamespaceDataAsync(g_conn_async, g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    deleteNamespaceData2Async(g_conn_async, g_stmt_async);
    deleteYangConConAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 011.同一namespace下，操作不同表，namespace级yang模式；线程1：表1，yang模式DML（显示开启事务）；
线程2：表2，yang模式DML（显示开启事务），主动回滚；
Notes        : 补充异常场景
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.22]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_011)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建两个yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建yang表 1
    createYangConConAsync(g_stmt_async);
    // 建yang表 2
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1,操作表1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConConAsync(connAsync[0], stmtAsync[0]);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2，操作表2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先回滚
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1查询插入的数据成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;

    // 表2无记录
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    deleteNamespaceData2Async(g_conn_async,g_stmt_async);
    deleteYangConConAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 012.不同namespace下，操作不同表，表1属于namespaceA，表2属于namespaceB，系统级yang模式；
线程1：namespace A 表1，yang模式DML（系统级显示开启事务)；
线程2：namespaceB 表2，yang模式DML（系统级显示开启事务），主动回滚；
Notes        : 补充异常场景
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.22]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_012)
{
    int ret;

    // 创建2个namespace RR+乐观，并且分别建两个yang表
    // 创建namespace A和yang表1
    createNameSpaceAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace B和yang表2
    createNameSpaceAsync(g_stmt_async, g_namespace02, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);
    AW_FUN_Log(LOG_STEP, "建表");

    int thrNum = 2;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadReplaceRollbackConChoicsCaseTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);

    // 表2无记录
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    // 表1插入数据成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace02);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace02);
}

/*****************************************************************************
Description  : 013.不同namespace下，操作不同表，表1属于namespaceA，表2属于namespaceB，系统级yang模式；
线程1：namespace A 表1，yang模式DML（系统级显示开启事务)；
线程2：namespaceB 表2，yang模式DML（namespace级显示开启事务），主动回滚；
Notes        : 补充异常场景
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.22]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_013)
{
    int ret;

    // 创建2个namespace RR+乐观，并且分别建两个yang表
    // 创建namespace A和yang表1
    createNameSpaceAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "建表");

    // 创建namespace B和yang表2
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace02, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    int thrNum = 2;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadReplaceRollbackConChoicsCaseTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);

    // 表2无记录
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    // 表1插入数据成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace02);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace02);
}

/*****************************************************************************
Description  : 014.不同namespace下，操作不同表，表1属于namespaceA，表2属于namespaceB，系统级yang模式；
线程1：namespace A 表1，yang模式DML（namespace级显示开启事务)；
线程2：namespaceB 表2，yang模式DML（namespace级显示开启事务），主动回滚；
Notes        :
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.22]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_014)
{
    int ret;

    // 创建2个namespace RR+乐观，并且分别建两个yang表
    // 创建namespace A和yang表1
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    createYangConConAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace B和yang表2
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace02, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);
    AW_FUN_Log(LOG_STEP, "建表");

    int thrNum = 2;
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    int index[thrNum] = {0};

    ret = pthread_create(&thr_arr[0], NULL, ThreadReplaceConConTest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadReplaceRollbackConChoicsCaseTest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    pthread_join(thr_arr[1], &thr_ret[1]);

    // 表2无记录
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    // 表1插入数据成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testClearNamespace(g_stmt_async, g_namespace01);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testClearNamespace(g_stmt_async, g_namespace02);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    dropNameSpaceAsync(g_stmt_async, g_namespace02);
}

/*****************************************************************************
Description  : 015.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：create，事务2：create；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_015)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：create,事务2：create*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    uint32_t PID = 2;


    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 016.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：merge，事务2：merge；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_016)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：merge,事务2：merge*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    uint32_t PID = 2;


    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 017.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：delete，事务2：delete；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_017)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);
    
    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预制数据
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    uint32_t PID = 1;

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：delete,事务2：delete*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_DELETE_GRAPH);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_DELETE_GRAPH);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 018.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：remove，事务2：remove；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_018)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预制数据
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    uint32_t PID = 1;


    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：remove,事务2：remove*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REMOVE_GRAPH);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REMOVE_GRAPH);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 019.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：merge-update，事务2：merge-update；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_019)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预制数据
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t PID = 1;
    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：merge-update,事务2：merge-update*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功，merge操作不删除数据，pid还是1
    PID = 1;


    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 020.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：replace-update，事务2：replace-update；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_020)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预制数据
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t PID = 1;
    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace-update,事务2：replace-update*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功，replace是先删除数据再插入，所以pid会增加
    PID = 3;


    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 021.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：replace-update，事务2：delete，事务2提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_021)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预制数据
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t PID = 1;
    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace-update,事务2：delete*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_DELETE_GRAPH);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 022.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：replace-update，事务2：delete，事务1提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_022)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预制数据
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t PID = 1;
    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace-update,事务2：delete*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_DELETE_GRAPH);

    // 事务1先提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2后提交，预期失败
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务1操作成功
    PID = 2;

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 023.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：replace和update，事务2：replace，事务1提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_023)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace和update,事务2：replace*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务1先提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2后提交，预期失败
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务1操作成功
    uint32_t PID = 2;

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 024.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：replace和update，事务2：replace，事务2提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_024)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace和update,事务2：replace*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    uint32_t PID = 3;


    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 025.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：replace和update和delete，事务2：replace，事务1提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_025)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace和update和delete,事务2：replace*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_DELETE_GRAPH);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务1先提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2后提交，预期失败
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务1操作成功
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 026.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：replace和update和delete，事务2：replace，事务2提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_026)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace和update和delete,事务2：replace*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_DELETE_GRAPH);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    uint32_t PID = 3;


    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 027.同一namespace下，操作同一张表，系统级yang模式；yang模式+yang模式，事务1：replace和update和delete，事务2：replace和update和delete；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.27]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_002, Multi_Instance_002_002_027)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];
    // 建yang表
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    /***************事务1：replace和update和delete,事务2：replace和update和delete*************************/

    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_DELETE_GRAPH);

    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_DELETE_GRAPH);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 028.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：create，事务2：create；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_028)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 2;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 029.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：merge，事务2：merge；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_029)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 2;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 030.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：delete，事务2：delete；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_030)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_DELETE_GRAPH);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_DELETE_GRAPH);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 031.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：remove，事务2：remove；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_031)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REMOVE_GRAPH);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REMOVE_GRAPH);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 032.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：merge-update，事务2：merge-update；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_032)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 1;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 033.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：replace-update，事务2：replace-update；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_033)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 3;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 034.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：replace-update，事务2：delete,事务2提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_034)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_DELETE_GRAPH);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 035.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：replace-update，事务2：delete,事务1提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_035)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 开启事务
    ret = startTransRrAndOpAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(g_conn_async, g_stmt_async, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 提交事务
    ret = commitTransAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_DELETE_GRAPH);

    // 事务1先提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2后提交，预期失败
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务1操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 2;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 036.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：replace和update，事务2：replace,事务1提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_036)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务1先提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2后提交，预期失败
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务1操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 2;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 037.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：replace和update，事务2：replace,事务2提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_037)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 3;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 038.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：replace和update和delete，事务2：replace,事务1提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_038)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_DELETE_GRAPH);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务1先提交，预期成功
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2后提交，预期失败
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务1操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
/*****************************************************************************
Description  : 039.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：replace和update和delete，事务2：replace,事务2提交成功；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_039)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_DELETE_GRAPH);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t PID = 3;


    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}

/*****************************************************************************
Description  : 040.同一namespace下，操作同一张表，namespace级yang模式；yang模式+yang模式，事务1：replace和update和delete，事务2：replace和update和delete；
Notes        : 补充五原语操作
History      :
Author       : jiangshan/j30011431
Modification : [2022.08.29]
*****************************************************************************/
TEST_F(MultiTransInstance_test_002_001, Multi_Instance_002_002_040)
{
    int ret;
    int connNum = 2;
    GmcConnT *connAsync[connNum];
    GmcStmtT *stmtAsync[connNum];

    // 创建1个namespace RR+乐观，并且建yang表
    createNameSpaceWithCfgRrAndOpAsync(g_stmt_async, g_namespace01, g_userName);
    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    createYangConChoicsCaseAsync(g_stmt_async);

    // 建立两个异步连接
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&connAsync[i], &stmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "建立异步连接");

    // 连接1使用namespace A
    ret = useNameSpaceAsync(stmtAsync[0], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务1
    ret = startTransRrAndOpAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testDeleteYangLabelConChoicCaseAsync(connAsync[0], stmtAsync[0], GMC_OPERATION_DELETE_GRAPH);

    // 连接2使用namespace A
    ret = useNameSpaceAsync(stmtAsync[1], g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务2
    ret = startTransRrAndOpAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testReplaceYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testUpdateYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testDeleteYangLabelConChoicCaseAsync(connAsync[1], stmtAsync[1], GMC_OPERATION_DELETE_GRAPH);

    // 事务2先提交，预期成功
    ret = commitTransAsync(connAsync[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1后提交，预期失败
    ret = commitTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = rollbackTransAsync(connAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证事务2操作成功
    ret = useNameSpace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, g_nameConChoiceCaseRoot, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = useNameSpaceAsync(g_stmt_async, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    deleteNamespaceDataAsync(g_conn_async,g_stmt_async);
    deleteYangConChoicsCaseAsync(g_stmt_async);
    dropNameSpaceAsync(g_stmt_async, g_namespace01);
    for (int i = 0; i < connNum; i++) {
        testGmcDisconnect(connAsync[i]);
    }
}
