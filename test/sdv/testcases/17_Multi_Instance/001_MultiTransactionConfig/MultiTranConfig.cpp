/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <iostream>
#include <cmath>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include "gtest/gtest.h"
#include <stdint.h>
#include <typeinfo>
#include <limits.h>

#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn1 = NULL;
GmcStmtT *g_stmt1 = NULL;

AsyncUserDataT asyncData;
AsyncUserDataT data1 = {0};
GmcTxConfigT MSTrxConfig;
GmcTxConfigT MSTrxConfig1;
char *yang_vertex_schema = NULL;
char *normal_vertex_schema = NULL;

// 清空nsp中所有元数据，包括数据和各种表
void testClearNsp(GmcStmtT *stmt, const char *namespaceName)
{
    AsyncUserDataT asyncData;
    memset(&asyncData, 0, sizeof(AsyncUserDataT));
    int ret = GmcClearNamespaceAsync(stmt, namespaceName, drop_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *ConfigJsonLiteWithEnableTableLock =
    "{\"isFastReadUncommitted\":true, \"enableTableLock\":true, \"auto_increment\": 1}";
const char *ConfigJsonLiteWithDisableTableLock =
    "{\"fasisFastReadUncommitted\":true, \"enableTableLock\":false, \"auto_increment\": 1}";
const char *ConfigJsonWithDisableAll =
    "{\"isFastReadUncommitted\":false, \"enableTableLock\":false, \"auto_increment\": 1, \"yang_model\":1}";
const char *ConfigJsonWithNormal = "{\"auto_increment\": 1}";
class MultiTransactionConfig : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&asyncData, 0, sizeof(AsyncUserDataT));
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        // 配置ns级别，RC+悲观事务模式
        GmcNspCfgT normalNspCfg;
        normalNspCfg.namespaceName = "normal";
        normalNspCfg.tablespaceName = NULL;
        normalNspCfg.userName = "bcd";
        normalNspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt1, &normalNspCfg, create_namespace_callback, &data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
        GmcNspCfgT yangNspCfg1 = {};
        yangNspCfg1.namespaceName = "yang1";
        yangNspCfg1.tablespaceName = NULL;
        yangNspCfg1.userName = "abc1";
        yangNspCfg1.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg1, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        GmcNspCfgT yangNspCfg2 = {};
        yangNspCfg2.namespaceName = "yang2";
        yangNspCfg2.tablespaceName = NULL;
        yangNspCfg2.userName = "abc2";
        yangNspCfg2.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg2, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        ret = GmcDropNamespaceAsync(g_stmt, "yang1", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        ret = GmcDropNamespaceAsync(g_stmt, "yang2", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        ret = GmcDropNamespaceAsync(g_stmt1, "normal", drop_namespace_callback, &data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};

// 001 RR+乐观事务模式下支持Yang模式表
TEST_F(MultiTransactionConfig, Multi_Instance_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    testClearNsp(g_stmt, "yang");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 RR+乐观事务模式下建表参数不能开启轻量化事务及表锁
TEST_F(MultiTransactionConfig, Multi_Instance_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建LiteTrx表
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}
void *gmsysview_thread(void *args)
{
    system("gmsysview count T0 -ns yang");
    return NULL;
}
void *gmsysview_thread1(void *args)
{
    system("gmsysview count normal_vertex -ns normal");
    return NULL;
}
void *gmsysview_thread2(void *args)
{
    system("gmsysview count T0 -ns public");
    return NULL;
}

#ifdef FEATURE_YANG
int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}
void *ddl_thread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT user_data = {0};
    int res = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    res = GmcUseNamespaceAsync(stmt, "yang", use_namespace_callback, &user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    res = testWaitAsyncRecv(&user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, user_data.status);
    res = GmcTransStartAsync(conn, &MSTrxConfig, trans_start_callback, &user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    res = testWaitAsyncRecv(&user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    // 设置批处理batch参数和开启diff
    res = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, user_data.status);
    res = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    res = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);

    // 添加DML操作
    res = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    res = GmcBatchExecuteAsync(batch, batch_execute_callback, &user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    res = testWaitAsyncRecv(&user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, user_data.status);
    AW_MACRO_EXPECT_EQ_INT(1, user_data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, user_data.succNum);
    res = GmcTransCommitAsync(conn, trans_commit_callback, &user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    res = testWaitAsyncRecv(&user_data);
    user_data.status = (user_data.status == GMERR_RESTRICT_VIOLATION ? GMERR_OK : user_data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, user_data.status);
    res = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res);
    return NULL;
}
#endif
void *ddl_thread1(void *args)
{
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcBatchT *batch1 = NULL;
    AsyncUserDataT user_data1 = {0};
    int res1 = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res1);
    res1 = GmcUseNamespaceAsync(stmt1, "normal", use_namespace_callback, &user_data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res1);
    res1 = testWaitAsyncRecv(&user_data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, user_data1.status);
    res1 = GmcTransStartAsync(conn1, &MSTrxConfig1, trans_start_callback, &user_data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res1);
    res1 = testWaitAsyncRecv(&user_data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res1);
    res1 = testGmcPrepareStmtByLabelName(stmt1, "normal_vertex", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res1);

    res1 = GmcTransCommitAsync(conn1, trans_commit_callback, &user_data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res1);
    res1 = testWaitAsyncRecv(&user_data1);
    user_data1.status = (user_data1.status == GMERR_RESTRICT_VIOLATION ? GMERR_OK : user_data1.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, user_data1.status);
    res1 = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, res1);
    return NULL;
}

// 022 gmsysview访问Yang namespace和Lite namespace
TEST_F(MultiTransactionConfig, Multi_Instance_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1001000");
    int ret;
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // create vertex
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));
    system("gmsysview count T0 -ns yang");
    system("gmsysview count T0 -ns normal");

    // 删除yang表
    testClearNsp(g_stmt, "yang");

    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // drop vertex
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 dlopen的方式访问Yang namespace和Lite namespace
#ifdef FEATURE_YANG
TEST_F(MultiTransactionConfig, Multi_Instance_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1003000");
    int ret;
    GmcBatchT *batch = NULL;
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 开启事务
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = testBatchPrepareAndSetDiff(g_conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_MACRO_EXPECT_EQ_INT(1, asyncData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, asyncData.succNum);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, asyncData.status);

    // 删除图模型
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcClearNamespaceAsync(g_stmt, "yang", ClearNSCallbak, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 gmsysview工具与dlopen的方式调用并发
TEST_F(MultiTransactionConfig, Multi_Instance_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建yang模型
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    pthread_t tid[4];
    ret = pthread_create(&tid[0], NULL, gmsysview_thread1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, ddl_thread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[2], NULL, gmsysview_thread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[3], NULL, ddl_thread1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    pthread_join(tid[3], NULL);
    // 删除图模型
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcClearNamespaceAsync(g_stmt, "yang", ClearNSCallbak, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcClearNamespaceAsync(g_stmt, "normal", ClearNSCallbak, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}
#endif
class MultiTransactionConfig_08 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=350\"");
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        // 配置ns级别，RC+悲观事务模式
        GmcNspCfgT normalNspCfg;
        normalNspCfg.namespaceName = "normal";
        normalNspCfg.tablespaceName = NULL;
        normalNspCfg.userName = "bcd";
        normalNspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt1, &normalNspCfg, create_namespace_callback, &data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
        GmcNspCfgT yangNspCfg1 = {};
        yangNspCfg1.namespaceName = "yang1";
        yangNspCfg1.tablespaceName = NULL;
        yangNspCfg1.userName = "abc1";
        yangNspCfg1.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg1, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        GmcNspCfgT yangNspCfg2 = {};
        yangNspCfg2.namespaceName = "yang2";
        yangNspCfg2.tablespaceName = NULL;
        yangNspCfg2.userName = "abc2";
        yangNspCfg2.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg2, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        ret = GmcDropNamespaceAsync(g_stmt, "yang1", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        ret = GmcDropNamespaceAsync(g_stmt, "yang2", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        ret = GmcDropNamespaceAsync(g_stmt1, "normal", drop_namespace_callback, &data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};
// 026 RR+乐观&&RR+乐观&&RR+乐观模式下，建超规格的表
TEST_F(MultiTransactionConfig_08, Multi_Instance_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1011000");
    int ret;
    char vertexLabelJson[1024];
    char vertexLabelName[1024];
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (uint i = 0; i < 1000; i++) {
        snprintf(vertexLabelJson, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i, i);
        sprintf(vertexLabelName, "T%u", i);
        ret = GmcCreateVertexLabelAsync(
            g_stmt, vertexLabelJson, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang1", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (uint i = 2; i < 502; i++) {
        snprintf(vertexLabelJson, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i, i);
        ret = GmcCreateVertexLabelAsync(
            g_stmt, vertexLabelJson, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang2", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (uint i = 0; i < 500; i++) {
        snprintf(vertexLabelJson, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i, i);
        ret = GmcCreateVertexLabelAsync(
            g_stmt, vertexLabelJson, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, asyncData.status);
    for (uint i = 0; i < 500; i++) {
        sprintf(vertexLabelName, "T%u", i);
        ret = GmcDropVertexLabelAsync(g_stmt, vertexLabelName, drop_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (uint i = 0; i < 1000; i++) {
        sprintf(vertexLabelName, "T%u", i);
        ret = GmcDropVertexLabelAsync(g_stmt, vertexLabelName, drop_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang1", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    testClearNsp(g_stmt, "yang1");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028 RR+乐观&&RR+乐观&&RR+乐观模式下，建超规格的连接
TEST_F(MultiTransactionConfig, Multi_Instance_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(4, "GMERR-1015002", "GMERR-1010002", "GMERR-1015004", "GMERR-1010000");
    int ret;
    GmcConnT *conn[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt[MAX_CONN_SIZE] = {0};
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (int i = 0; i < 340; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang1", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (int i = 340; i < 680; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang2", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (int i = 680; i < MAX_CONN_SIZE - 2; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcConnect(&conn[MAX_CONN_SIZE - 2], &stmt[MAX_CONN_SIZE - 2]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_TOO_MANY_CONNECTIONS, ret);
    for (int i = 0; i < MAX_CONN_SIZE - 2; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// RR+乐观&&RR+乐观
class MultiTransactionConfig_01 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

        GmcNspCfgT yangNspCfg1 = {};
        yangNspCfg1.namespaceName = "yang1";
        yangNspCfg1.tablespaceName = NULL;
        yangNspCfg1.userName = "abc1";
        yangNspCfg1.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg1, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }

    virtual void TearDown()
    {
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

        ret = GmcDropNamespaceAsync(g_stmt, "yang1", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};
// 004 RR+乐观&&RR+乐观 多事务模式建表
TEST_F(MultiTransactionConfig_01, Multi_Instance_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    ret = GmcUseNamespaceAsync(g_stmt, "yang1", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang1");

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005 RR+乐观&&RR+乐观 事务外切换namespace并建表
TEST_F(MultiTransactionConfig_01, Multi_Instance_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcUseNamespaceAsync(g_stmt, "yang1", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang1");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006 RR+乐观&&RR+乐观 事务内切换namespace并建表
TEST_F(MultiTransactionConfig_01, Multi_Instance_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 事务一
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    //
    ret = GmcUseNamespaceAsync(g_stmt, "yang1", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang1");

    AW_FUN_Log(LOG_STEP, "test end.");
}
// RR+乐观&&RC+悲观
class MultiTransactionConfig_02 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

        // 配置ns级别，RC+悲观事务模式
        GmcNspCfgT normalNspCfg;
        normalNspCfg.namespaceName = "normal";
        normalNspCfg.tablespaceName = NULL;
        normalNspCfg.userName = "bcd";
        normalNspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt1, &normalNspCfg, create_namespace_callback, &data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    }

    virtual void TearDown()
    {
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

        ret = GmcDropNamespaceAsync(g_stmt, "normal", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};
// 007 RR+乐观&&RC+悲观 多事务模式建表
TEST_F(MultiTransactionConfig_02, Multi_Instance_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // create vertex
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // drop vertex
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008 RR+乐观&&RC+悲观 事务外切换namespace并建表
TEST_F(MultiTransactionConfig_02, Multi_Instance_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 乐观切换悲观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // create vertex
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // drop vertex
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // 悲观切换乐观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 RR+乐观&&RC+悲观 事务内切换namespace并建表
TEST_F(MultiTransactionConfig_02, Multi_Instance_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 事务一
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换悲观
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 事务二
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig1, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换乐观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// RR+乐观&&LiteTrx(表锁开)
class MultiTransactionConfig_03 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/stop.sh -f");
            system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=1\" \"enableTableLock=1\"");
            system("sh $TEST_HOME/tools/start.sh");
        }
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        }
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }

    virtual void TearDown()
    {
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};
// 010 RR+乐观&&LiteTrx(表锁开) 多事务模式建表
TEST_F(MultiTransactionConfig_03, Multi_Instance_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 轻量化模式
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithNormal, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 乐观模式
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    // 删除图模型
    testClearNsp(g_stmt, "yang");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 RR+乐观&&LiteTrx(表锁开) 事务外切换namespace并建表
TEST_F(MultiTransactionConfig_03, Multi_Instance_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 轻量化切换乐观

    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 乐观切换轻量化
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 RR乐观事务内切换RC悲观，事务内切换namespace并建表
TEST_F(MultiTransactionConfig_03, Multi_Instance_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 事务一
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换轻量化
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 事务二
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig1, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换乐观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// RR+乐观&&LiteTrx(表锁关)
class MultiTransactionConfig_04 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/stop.sh -f");
            system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=1\" \"enableTableLock=0\"");
            system("sh $TEST_HOME/tools/start.sh");
        }
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        }
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }

    virtual void TearDown()
    {
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};
// 013 RR+乐观&&LiteTrx(表锁关) 多事务模式建表
TEST_F(MultiTransactionConfig_04, Multi_Instance_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 轻量化模式
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithNormal, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 乐观模式
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    // 删除图模型
    testClearNsp(g_stmt, "yang");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 RR+乐观&&LiteTrx(表锁关) 事务外切换namespace并建表
TEST_F(MultiTransactionConfig_04, Multi_Instance_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 轻量化切换乐观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 乐观切换轻量化
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 RR+乐观&&LiteTrx(表锁关) 事务内切换namespace并建表
TEST_F(MultiTransactionConfig_04, Multi_Instance_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 事务一
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换轻量化
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 事务二
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig1, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换乐观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// RR+乐观&&RC+悲观&&LiteTrx(表锁开)
class MultiTransactionConfig_05 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/stop.sh -f");
            system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=1\" \"maxSysShmSize=350\" "
                   "\"enableTableLock=1\"");
            system("sh $TEST_HOME/tools/start.sh");
        }
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        }
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        // 配置ns级别，RC+悲观事务模式
        GmcNspCfgT normalNspCfg;
        normalNspCfg.tablespaceName = NULL;
        normalNspCfg.namespaceName = "normal";
        normalNspCfg.userName = "bcd";
        normalNspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt1, &normalNspCfg, create_namespace_callback, &data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    }

    virtual void TearDown()
    {
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        ret = GmcDropNamespaceAsync(g_stmt, "normal", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};
// 016 RR+乐观&&RC+悲观&&LiteTrx(表锁开) 多事务模式建表
TEST_F(MultiTransactionConfig_05, Multi_Instance_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 轻量化模式

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithNormal, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 乐观模式
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 悲观模式
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithNormal, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 RR+乐观&&RC+悲观&&LiteTrx(表锁开) 事务外切换namespace并建表
TEST_F(MultiTransactionConfig_05, Multi_Instance_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 轻量化切换乐观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 乐观切换悲观
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 悲观切轻量化
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 RR+乐观&&RC+悲观&&LiteTrx(表锁开) 事务外切换namespace并建表
TEST_F(MultiTransactionConfig_05, Multi_Instance_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 事务一
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换轻量化/悲观
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 事务二
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig1, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换乐观/悲观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 事务三
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig1, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换乐观/轻量化
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 RR+乐观&&RC+悲观&&LiteTrx（表锁开）模式下，建超规格的表
TEST_F(MultiTransactionConfig_05, Multi_Instance_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char vertexLabelJson[1024];
    char vertexLabelName[1024];

    // 轻量化create 1000
    for (uint i = 0; i < 1000; i++) {
        snprintf(vertexLabelJson, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i, i);
        sprintf(vertexLabelName, "T%u", i);
        ret = GmcCreateVertexLabelAsync(g_stmt, vertexLabelJson, NULL, create_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // RC悲观create 1000
    for (uint i = 0; i < 1000; i++) {
        snprintf(vertexLabelJson, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i, i);
        ret = GmcCreateVertexLabelAsync(g_stmt, vertexLabelJson, NULL, create_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, asyncData.status);
    for (uint i = 0; i < 1000; i++) {
        sprintf(vertexLabelName, "T%u", i);
        ret = GmcDropVertexLabelAsync(g_stmt, vertexLabelName, drop_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (uint i = 0; i < 1000; i++) {
        sprintf(vertexLabelName, "T%u", i);
        ret = GmcDropVertexLabelAsync(g_stmt, vertexLabelName, drop_vertex_label_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    testClearNsp(g_stmt, "yang");

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027 RR+乐观&&RC+悲观&&LiteTrx（表锁开）模式下，建超规格的连接
TEST_F(MultiTransactionConfig_05, Multi_Instance_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcConnT *conn[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt[MAX_CONN_SIZE] = {0};
    for (int i = 0; i < 340; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (int i = 340; i < 680; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    for (int i = 680; i < MAX_CONN_SIZE - 2; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcConnect(&conn[MAX_CONN_SIZE - 2], &stmt[MAX_CONN_SIZE - 2]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_TOO_MANY_CONNECTIONS, ret);
    for (int i = 0; i < MAX_CONN_SIZE - 2; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// RR+乐观&&RC+悲观&&LiteTrx(表锁关)
class MultiTransactionConfig_06 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/stop.sh -f");
            system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=1\" \"enableTableLock=0\"");
            system("sh $TEST_HOME/tools/start.sh");
        }
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        }
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        // 配置ns级别，RC+悲观事务模式
        GmcNspCfgT normalNspCfg;
        normalNspCfg.namespaceName = "normal";
        normalNspCfg.userName = "bcd";
        normalNspCfg.tablespaceName = NULL;
        normalNspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt1, &normalNspCfg, create_namespace_callback, &data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    }

    virtual void TearDown()
    {
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        ret = GmcDropNamespaceAsync(g_stmt, "normal", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};

// 019 RR+乐观&&RC+悲观&&LiteTrx(表锁关) 多事务模式建表
TEST_F(MultiTransactionConfig_06, Multi_Instance_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 轻量化模式
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithNormal, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 乐观模式
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 悲观模式
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonWithNormal, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 RR+乐观&&RC+悲观&&LiteTrx(表锁关) 事务外切换namespace并建表
TEST_F(MultiTransactionConfig_06, Multi_Instance_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 轻量化切换乐观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 乐观切换悲观
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 悲观切轻量化
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 RR+乐观&&RC+悲观&&LiteTrx(表锁关) 事务外切换namespace并建表
TEST_F(MultiTransactionConfig_06, Multi_Instance_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 事务一
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换轻量化/悲观
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    // 切换失败事务回滚
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);

    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    // 事务二
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig1, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换乐观/悲观
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 事务三
    ret = GmcUseNamespaceAsync(g_stmt, "normal", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransStartAsync(g_conn, &MSTrxConfig1, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 切换乐观/轻量化
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcUseNamespaceAsync(g_stmt, "public", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    ret = GmcDropVertexLabelAsync(g_stmt, "normal_vertex", drop_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}
class MultiTransactionConfig_07 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        if (g_envType != 2) {
            // 库级配置RR+乐观
            system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=1\" \"defaultIsolationLevel=2\"");
        }
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        }
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn1, &g_stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        MSTrxConfig.readOnly = false;
        MSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

        MSTrxConfig1.transMode = GMC_TRANS_USED_IN_CS;
        MSTrxConfig1.type = GMC_TX_ISOLATION_COMMITTED;
        MSTrxConfig1.readOnly = false;
        MSTrxConfig1.trxType = GMC_PESSIMISITIC_TRX;
        readJanssonFile("schema_file/container_container.gmjson", &yang_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(yang_vertex_schema);
        readJanssonFile("schema_file/normal_vertex.gmjson", &normal_vertex_schema);
        AW_MACRO_ASSERT_NOTNULL(normal_vertex_schema);

        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt, &yangNspCfg, create_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    }

    virtual void TearDown()
    {
        // 删除ns
        int ret = GmcDropNamespaceAsync(g_stmt, "yang", drop_namespace_callback, &asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        // 断开连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1, g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(yang_vertex_schema);
        free(normal_vertex_schema);
    }
};
// 003 配置文件默认配置RR+乐观
TEST_F(MultiTransactionConfig_07, Multi_Instance_001_003)
{
#ifdef DIRECT_WRITE
#else
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = GmcUseNamespaceAsync(g_stmt, "yang", use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    // 创建yang模型
    ret = GmcCreateVertexLabelAsync(
        g_stmt, yang_vertex_schema, ConfigJsonWithDisableAll, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithEnableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, asyncData.status);
    ret = GmcCreateVertexLabelAsync(
        g_stmt, normal_vertex_schema, ConfigJsonLiteWithDisableTableLock, create_vertex_label_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status == GMERR_INVALID_PARAMETER_VALUE ? GMERR_OK : asyncData.status);

    // 删除图模型
    testClearNsp(g_stmt, "yang");

    AW_FUN_Log(LOG_STEP, "test end.");
#endif
}
