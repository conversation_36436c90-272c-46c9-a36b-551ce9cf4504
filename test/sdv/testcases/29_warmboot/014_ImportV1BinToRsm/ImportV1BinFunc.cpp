#include "ImportV1BinCommon.h"

class ImportV1BinFunc : public testing::Test {
public:
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ImportV1BinFunc::SetUpTestCase()
{
    system("sh getV1BIn.sh");
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void ImportV1BinFunc::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void ImportV1BinFunc::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_INVALID_PROPERTY);
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void ImportV1BinFunc::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    AW_CHECK_LOG_END();
    int ret;
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 009 导入空库的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/empty_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 0", "create table num: 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询,没有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_TYPE=VERTEX_TYPE_NORMAL | grep VERTEX_LABEL_NAME | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 导入建表，写数据，删除据，删表后的空库v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/createDropTbl_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 0", "create table num: 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询,没有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_TYPE=VERTEX_TYPE_NORMAL | grep VERTEX_LABEL_NAME | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 导入只有表，没有数据的的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/onetable_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table01 | grep 'VERTEX_LABEL_NAME: table01' | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据为0
    const char *tableName = "table01";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, tableName);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 导入建表写数据，然后删掉所有数据，只保留表的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/insertDelete_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table01 | grep 'VERTEX_LABEL_NAME: table01' | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据为0
    const char *tableName = "table01";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, tableName);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 导入覆盖bim经典模型的表和数据的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/insert_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table01 | grep 'VERTEX_LABEL_NAME: table01' | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据为0
    const char *tableName = "table01";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, tableName);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint32_t addVal = 0;
    const char *pkName = "f3Idx";
    TestScanLabelByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 导入覆盖全部字段类型的表和数据的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/allType_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(
        cmd, "Total table num: 1", "create table num: 1", "total record num: 100, insert record num: 100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table02AllType | grep 'VERTEX_LABEL_NAME: table02AllType' | wc -l",
        g_toolPath, viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    const char *tableName = "table02AllType";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, tableName);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint32_t addVal = 0;
    const char *pkName = "allTypeIdx";
    TestScanTable2ByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 导入自定义字段类型的表和数据的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/userdefinefield_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table04 | grep 'VERTEX_LABEL_NAME: table04' | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "table04";
    const char *pkName = "tbl4_idx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
    system("gmsysview record table04");
    TestScanTable4ByPk(g_stmtSync, pkName, 0, 100, tableName);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 导入建表，写数据，更新数据的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/insertUpdate_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table01 | grep 'VERTEX_LABEL_NAME: table01' | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    const char *tableName = "table01";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, tableName);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint32_t addVal = 100;
    const char *pkName = "f3Idx";
    TestScanLabelByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 导入建表，写数据，更新数据后删除更新数据的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/DeleteUpdate_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table01 | grep 'VERTEX_LABEL_NAME: table01' | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据为0
    const char *tableName = "table01";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, tableName);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "50");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint32_t addVal = 0;
    const char *pkName = "f3Idx";
    TestScanLabelByPk(g_stmtSync, pkName, 50, 100, addVal, tableName);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 导入建表，写数据，更新数据后,删除写数据的v1.db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/DeleteInsert_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table01 | grep 'VERTEX_LABEL_NAME: table01' | wc -l", g_toolPath,
        viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据为0
    const char *tableName = "table01";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, tableName);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "50");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint32_t addVal = 100;
    const char *pkName = "f3Idx";
    TestScanLabelByPk(g_stmtSync, pkName, 0, 50, addVal, tableName);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 导入小端环境导出的大端v1二进制，然后在导入到小端环境
TEST_F(ImportV1BinFunc, warmboot_014_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/allTypeBigEndianv1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(
        cmd, "Total table num: 1", "create table num: 1", "total record num: 100, insert record num: 100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=table02AllType | grep 'VERTEX_LABEL_NAME: table02AllType' | wc -l",
        g_toolPath, viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据为0
    const char *tableName = "table02AllType";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, tableName);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint32_t addVal = 0;
    const char *pkName = "allTypeIdx";
    TestScanTable2ByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 导入bim典型模型的v1二进制到warmboot后，update、主键读，主键delete；
TEST_F(ImportV1BinFunc, warmboot_014_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    int addVal = 100;
    TestUpdateVertexSys(g_stmtSync, pkName, 0, 175, addVal, tableName);
    TestReadVertexSysByPk(g_stmtSync, pkName, 0, 175, addVal, tableName);
    TestDeletedVertexSysByKey(g_stmtSync, pkName, 0, 175, tableName);
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 导入bim典型模型的v1二进制到warmboot后，replace-update、全表扫，前台Truncate；
TEST_F(ImportV1BinFunc, warmboot_014_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    int addVal = 200;
    TestReplaceUpdateVertexSys(g_stmtSync, 0, 175, addVal, tableName);
    TestScanVertexSys(g_stmtSync, 0, 175, tableName);
    ret = GmcTruncateVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexCount(g_stmtSync, tableName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 导入自动以字段的v1二进制到warmboot后，merge-update、后台Truncate
TEST_F(ImportV1BinFunc, warmboot_014_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/userdefinefield_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "table04";
    const char *pkName = "tbl4_idx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
    system("gmsysview record table04");
    TestScanTable4ByPk(g_stmtSync, pkName, 0, 100, tableName);

    int addVal = 100;
    TestMergeTable4(g_stmtSync, pkName, 0, 100, addVal, tableName);
    TestScanTable4ByPk(g_stmtSync, pkName, 0, 100, tableName, addVal);

    ret = GmcDeleteAllFast(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexCount(g_stmtSync, tableName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 导入自定义字段的v1二进制到warmboot后，批量更新、删；
TEST_F(ImportV1BinFunc, warmboot_014_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/userdefinefield_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "table04";
    const char *pkName = "tbl4_idx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
    TestScanTable4ByPk(g_stmtSync, pkName, 0, 100, tableName);

    int addVal = 100;
    TestBatchMergeTable4(g_connSync, g_stmtSync, pkName, 0, 100, addVal, tableName);
    TestScanTable4ByPk(g_stmtSync, pkName, 0, 100, tableName, addVal);
    TestBatchDeleteTable4(g_connSync, g_stmtSync, pkName, 0, 100, tableName);

    ret = GmcGetVertexCount(g_stmtSync, tableName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 导入全量类型的v1二进制到warmboot后，异步批量更新，批量删；
TEST_F(ImportV1BinFunc, warmboot_014_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/allType_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(
        cmd, "Total table num: 1", "create table num: 1", "total record num: 100, insert record num: 100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "table02AllType";
    const char *pkName = "allTypeIdx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);

    uint32_t addVal = 0;
    TestScanTable2ByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);

    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_connAsync = NULL;
    g_stmtAsync = NULL;
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    TestBatchUpdateTable2Async(g_connAsync, g_stmtAsync, pkName, 0, 100, addVal, tableName);
    TestScanTable2ByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);
    TestBatchDeleteTable2Async(g_connAsync, g_stmtAsync, pkName, 0, 100, tableName);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexCount(g_stmtSync, tableName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 导入v1二进制到warmboot后，开启全表对账，更新部分数据，异常结束对账；
TEST_F(ImportV1BinFunc, warmboot_014_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    ret = GmcBeginCheck(g_stmtSync, tableName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int addVal = 0;
    TestUpdateVertexSys(g_stmtSync, pkName, 0, 100, addVal, tableName);
    // 异常结束对账
    ret = GmcEndCheck(g_stmtSync, tableName, 0xff, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestScanVertexSys(g_stmtSync, 0, 175, tableName);
    ret = GmcGetVertexCount(g_stmtSync, tableName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    ret = GmcDeleteAllFast(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 导入v1二进制到warmboot后，开启全表对账，更新部分数据，正常结束对账；
TEST_F(ImportV1BinFunc, warmboot_014_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    ret = GmcBeginCheck(g_stmtSync, tableName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int addVal = 0;
    TestReplaceUpdateVertexSys(g_stmtSync, 0, 100, addVal, tableName);
    // 正常结束对账
    ret = GmcEndCheck(g_stmtSync, tableName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestReadVertexSysByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);
    ret = GmcGetVertexCount(g_stmtSync, tableName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);

    ret = GmcTruncateVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 导入v1.db二进制文件后，恢复重启，等恢复完成后读取数据，与恢复重启前一致；
TEST_F(ImportV1BinFunc, warmboot_014_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview record %s > record_before.txt", g_toolPath, tableName);
    system(cmd);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testEnvInit();
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview record %s > record_after.txt", g_toolPath, tableName);
    system(cmd);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    readJanssonFile("record_before.txt", &expect);
    EXPECT_NE((void *)NULL, expect);
    readJanssonFile("record_after.txt", &actual);
    EXPECT_NE((void *)NULL, actual);

    AW_MACRO_EXPECT_EQ_INT(0, strcmp(expect, actual));
    free(expect);
    free(actual);

    ret = GmcTruncateVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 导入v1二进制到warmboot后，升级2个版本，然后更新部分数据到高版本，再次降级2个版本，然后删除数据；
TEST_F(ImportV1BinFunc, warmboot_014_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    // gmddl 工具升级表操作
    const char *filePath1 = "alterSchema/sysTable0Alter1.gmjson";
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, tableName, filePath1);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int addVal = 100;
    TestUpdateVertexSys(g_stmtSync, pkName, 0, 50, addVal, tableName, 1);

    const char *filePath2 = "alterSchema/sysTable0Alter2.gmjson";
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, tableName, filePath2);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    addVal = 200;
    TestUpdateVertexSys(g_stmtSync, pkName, 50, 100, addVal, tableName, 2);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, tableName, filePath1);
    ret = executeCommand(cmd, "upgrade successfully");
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, tableName, filePath2);
    ret = executeCommand(cmd, "upgrade successfully");
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testEnvInit();
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    addVal = 100;
    TestReadVertexSysByPk(g_stmtSync, pkName, 0, 50, addVal, tableName, 1);
    addVal = 200;
    TestReadVertexSysByPk(g_stmtSync, pkName, 50, 100, addVal, tableName, 2);
    ret = GmcTruncateVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029  导入v1二进制到warmboot后，老订阅
TEST_F(ImportV1BinFunc, warmboot_014_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");

    create_epoll_thread();
    int chanRingLen = 256;
    GmcConnT *connOldSub = NULL;
    GmcStmtT *stmtOldSub = NULL;
    const char *oldSubConnName = "oldSubConn";
    ret = testSubConnect(&connOldSub, &stmtOldSub, 1, g_epoll_reg_info, oldSubConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&oldSubData, 175);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 新建老订阅条件订阅
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "fullConditionSub";
    oldSubInfo.configJson = g_oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "fullConditionSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(oldSubData);
    close_epoll_thread();
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class ImportV1BinFuncLoadAll : public testing::Test {
public:
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ImportV1BinFuncLoadAll::SetUpTestCase()
{
    system("sh getV1BIn.sh");
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void ImportV1BinFuncLoadAll::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void ImportV1BinFuncLoadAll::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh schemaLoader=1");
    const char *schemaDir = "./testcases/29_warmboot/014_ImportV1BinToRsm/emptySchema";
    char cmd[1024];
    // 没有开启鉴权模式，故只需配置4个路径即可，不需要保证用户白名单，系统权限，对象权限3个路径下权限的正确性
    (void)snprintf(cmd, 1024, "sh $TEST_HOME/tools/modifyCfg.sh \"schemaPath=%s;%s;%s;%s;\"", schemaDir, schemaDir,
        schemaDir, schemaDir);
    system(cmd);
    system("${TEST_HOME}/tools/start.sh");
    int ret;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void ImportV1BinFuncLoadAll::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    AW_CHECK_LOG_END();
    int ret;
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

//  开启全量导表后，导入v1二进制到warmboot后，导入后dml
TEST_F(ImportV1BinFuncLoadAll, warmboot_014_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    int addVal = 100;
    TestUpdateVertexSys(g_stmtSync, pkName, 0, 175, addVal, tableName);
    TestReadVertexSysByPk(g_stmtSync, pkName, 0, 175, addVal, tableName);
    TestDeletedVertexSysByKey(g_stmtSync, pkName, 0, 175, tableName);
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class ImportV1BinFuncLoadNeed : public testing::Test {
public:
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ImportV1BinFuncLoadNeed::SetUpTestCase()
{
    system("sh getV1BIn.sh");
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void ImportV1BinFuncLoadNeed::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void ImportV1BinFuncLoadNeed::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh schemaLoader=2");
    const char *schemaDir = "./testcases/29_warmboot/014_ImportV1BinToRsm/emptySchema";
    char cmd[1024];
    // 没有开启鉴权模式，故只需配置4个路径即可，不需要保证用户白名单，系统权限，对象权限3个路径下权限的正确性
    (void)snprintf(cmd, 1024, "sh $TEST_HOME/tools/modifyCfg.sh \"schemaPath=%s;%s;%s;%s;\"", schemaDir, schemaDir,
        schemaDir, schemaDir);
    system(cmd);
    system("${TEST_HOME}/tools/start.sh");
    int ret;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void ImportV1BinFuncLoadNeed::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    AW_CHECK_LOG_END();
    int ret;
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 031 开启按需导表后，导入v1二进制到warmboot，导入后dml操作
TEST_F(ImportV1BinFuncLoadNeed, warmboot_014_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    int addVal = 200;
    TestReplaceUpdateVertexSys(g_stmtSync, 0, 175, addVal, tableName);
    TestScanVertexSys(g_stmtSync, 0, 175, tableName);
    ret = GmcTruncateVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexCount(g_stmtSync, tableName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032 导入v1二进制到warmboot，根据导入的表创建多表实例
TEST_F(ImportV1BinFunc, warmboot_014_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./RelEnvV1Bin/sysdb";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    uint64_t vertexCount = 0;
    const char *tableName = "sysTable0";
    const char *pkName = "Index";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);

    const char *tableName2 = "sysTable0Duplicate";
    ret = GmcDuplicateVertexLabelWithName(g_stmtSync, tableName, tableName2, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, tableName2, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    // DML操作复制表数据
    int addVal = 200;
    TestReplaceUpdateVertexSys(g_stmtSync, 0, 175, addVal, tableName2);
    ret = GmcGetVertexCount(g_stmtSync, tableName2, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(175, vertexCount);
    TestReadVertexSysByPk(g_stmtSync, pkName, 0, 175, addVal, tableName2);
    ret = GmcTruncateVertexLabel(g_stmtSync, tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmtSync, tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class ImportV1BinFuncCluster : public testing::Test {
public:
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ImportV1BinFuncCluster::SetUpTestCase()
{
    system("sh getV1BIn.sh");
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void ImportV1BinFuncCluster::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void ImportV1BinFuncCluster::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableClusterHash=1");
    system("${TEST_HOME}/tools/modifyCfg.sh memCompactEnable=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableReleaseDevice=1");
    system("sh $TEST_HOME/tools/modifyCfg.sh RsmKeyRange=0,1");
    system("sh $TEST_HOME/tools/modifyCfg.sh deviceSize=1");
    system("sh $TEST_HOME/tools/modifyCfg.sh RsmBlockSize=32");
    system("sh $TEST_HOME/tools/modifyCfg.sh defaultTablespaceMaxSize=16");
    system("${TEST_HOME}/tools/start.sh");
    int ret;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void ImportV1BinFuncCluster::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    AW_CHECK_LOG_END();
    int ret;
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 033 开启聚簇容器，导入v1二进制到warmboot，导入后删除触发页搬迁
TEST_F(ImportV1BinFuncCluster, warmboot_014_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/record100000_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "table04";
    const char *pkName = "tbl4_idx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100000, vertexCount);

    uint32_t deviceCount = 0;
    deviceCount = GetViewValueByField("STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT");
    AW_FUN_Log(LOG_INFO, "deviceCount = %d", deviceCount);
    AW_MACRO_EXPECT_EQ_INT(deviceCount, 16);

    TestDeletedTable4(g_stmtSync, pkName, 0, 100000, tableName);
    GmcAlarmDataT alarmData;
    ret = GmcGetAlarmData(g_stmtSync, GMC_ALARM_RSM_TABLE_SPACE_USED_INFO, &alarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int sleepCount = 0;
    while (true) {
        if (sleepCount > 120) {
            AW_FUN_Log(LOG_ERROR, "wait move device task compress timeout.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            break;
        }
        sleep(1);
        sleepCount++;
        char command[1024];
        (void)sprintf_s(command, sizeof(command), "gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -f TASK_TYPE=2");
        ret = executeCommand(command, "SUB_TASK_STATUS: compress_finish");
        if (ret == GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "compress_finish");
            break;
        }
    };

    system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT");
    deviceCount = 0;
    deviceCount = GetViewValueByField("STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT");
    AW_FUN_Log(LOG_INFO, "deviceCount = %d", deviceCount);
    EXPECT_GE(deviceCount, 2);
    EXPECT_LE(deviceCount, 4);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035 开启聚簇容器，导入v1二进制到warmboot，导入后不能删除触发缩容,
// 因为不能配置表的缩容配置defragmentation，所以不能触发缩容
TEST_F(ImportV1BinFuncCluster, warmboot_014_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/record100000_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "table04";
    const char *pkName = "tbl4_idx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100000, vertexCount);

    TestDeletedTable4(g_stmtSync, pkName, 0, 70000, tableName);
    uint32_t scaleTimes = 0;
    uint32_t waitCount = 0;
    while ((scaleTimes < 1) && (waitCount < 120)) {
        scaleTimes = GetViewValueByField(
            "STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT -f LABEL_NAME=table04", "SCALE_IN_ACTUAL_BEGIN_COUNT");
        sleep(1);
        waitCount++;
    };
    AW_MACRO_EXPECT_EQ_INT(scaleTimes, 0);

    ret = GmcDeleteAllFast(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class ImportV1BinFuncHeap : public testing::Test {
public:
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ImportV1BinFuncHeap::SetUpTestCase()
{
    system("sh getV1BIn.sh");
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void ImportV1BinFuncHeap::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void ImportV1BinFuncHeap::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableClusterHash=0");
    system("${TEST_HOME}/tools/modifyCfg.sh memCompactEnable=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableReleaseDevice=1");
    system("sh $TEST_HOME/tools/modifyCfg.sh RsmKeyRange=0,1");
    system("sh $TEST_HOME/tools/modifyCfg.sh deviceSize=1");
    system("sh $TEST_HOME/tools/modifyCfg.sh RsmBlockSize=32");
    system("sh $TEST_HOME/tools/modifyCfg.sh defaultTablespaceMaxSize=16");
    system("${TEST_HOME}/tools/start.sh");
    int ret;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void ImportV1BinFuncHeap::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    AW_CHECK_LOG_END();
    int ret;
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 034 开启heap，导入v1二进制到warmboot，导入后删除触发业页搬迁
TEST_F(ImportV1BinFuncHeap, warmboot_014_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/100000nonfixed_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "table01";
    const char *pkName = "f3Idx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100000, vertexCount);

    uint32_t deviceCount = 0;
    deviceCount = GetViewValueByField("STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT");
    AW_FUN_Log(LOG_INFO, "deviceCount = %d", deviceCount);
    AW_MACRO_EXPECT_EQ_INT(deviceCount, 8);

    TestDeletedTable1(g_stmtSync, pkName, 0, 80000, tableName);
    GmcAlarmDataT alarmData;
    ret = GmcGetAlarmData(g_stmtSync, GMC_ALARM_RSM_TABLE_SPACE_USED_INFO, &alarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int sleepCount = 0;
    while (true) {
        if (sleepCount > 120) {
            AW_FUN_Log(LOG_ERROR, "wait move device task compress timeout.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            break;
        }
        sleep(1);
        sleepCount++;
        char command[1024];
        (void)sprintf_s(command, sizeof(command), "gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -f TASK_TYPE=2");
        ret = executeCommand(command, "SUB_TASK_STATUS: compress_finish");
        if (ret == GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "compress_finish");
            break;
        }
    };

    system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT");
    deviceCount = 0;
    deviceCount = GetViewValueByField("STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT");
    AW_FUN_Log(LOG_INFO, "deviceCount = %d", deviceCount);
    EXPECT_GE(deviceCount, 2);
    EXPECT_LE(deviceCount, 4);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036 开启heap，导入v1二进制到warmboot，导入后删除触发缩容
// 因为不能配置表的缩容配置defragmentation，所以不能触发缩容
TEST_F(ImportV1BinFuncHeap, warmboot_014_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/100000nonfixed_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1", "create table num: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "table01";
    const char *pkName = "f3Idx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100000, vertexCount);

    TestDeletedTable1(g_stmtSync, pkName, 0, 70000, tableName);
    uint32_t scaleTimes = 0;
    uint32_t waitCount = 0;
    while ((scaleTimes < 1) && (waitCount < 120)) {
        scaleTimes = GetViewValueByField(
            "STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT -f LABEL_NAME=table01", "SCALE_IN_ACTUAL_BEGIN_COUNT");
        sleep(1);
        waitCount++;
    };
    AW_MACRO_EXPECT_EQ_INT(scaleTimes, 0);

    ret = GmcDeleteAllFast(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044 导入一张表包含多个Vbytes字段的V1 db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/MultiVbytesTable_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(
        cmd, "Total table num: 1", "create table num: 1", "total record num: 100, insert record num: 100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // gmsysview 视图查询有用户创建地表
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(cmd, MAX_CMD_SIZE,
        "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=tableVbytes01 | grep 'VERTEX_LABEL_NAME: tableVbytes01' | wc -l",
        g_toolPath, viewVertexInfo);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "tableVbytes01";
    const char *pkName = "vbytesIdx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);

    uint32_t addVal = 0;
    TestScanVbytesTableByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045 导入多张表包含Vbytes字段的V1 db二进制文件
TEST_F(ImportV1BinFunc, warmboot_014_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 导入v1二进制文件
    const char *v1BinPath = "./FuncV1Bin/MultiTableIncludeVbytes_v1.db";
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_bin -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(
        cmd, "Total table num: 2", "create table num: 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);

    // count 表中的数据
    uint64_t vertexCount = 0;
    const char *tableName = "tableVbytes01";
    const char *pkName = "vbytesIdx";
    ret = GmcGetVertexCount(g_stmtSync, tableName, pkName, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);

    uint64_t vertexCount2 = 0;
    const char *tableName2 = "tableVbytes02";
    const char *pkName2 = "vbytesIdx";
    ret = GmcGetVertexCount(g_stmtSync, tableName2, pkName2, &vertexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount2);

    uint32_t addVal = 0;
    TestScanVbytesTableByPk(g_stmtSync, pkName, 0, 100, addVal, tableName);
    TestScanVbytesTableByPk(g_stmtSync, pkName2, 0, 100, addVal, tableName2);

    ret = GmcDropVertexLabel(g_stmtSync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

