#include "RsmCommon.h"

class RsmClusterVertex : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void RsmClusterVertex::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void RsmClusterVertex::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void RsmClusterVertex::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableClusterHash=1");
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
}

void RsmClusterVertex::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，主键插入，更新，读，删除
TEST_F(RsmClusterVertex, warmboot_008_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterName);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestUpdateVertexLabelByKey(g_stmtSync, g_clusterPk, 0, g_recordCount, 10000, g_clusterName);
    TestReplaceVertexLabel(g_stmtSync, 0, g_recordCount, 20000, g_clusterName);
    TestScanLabelByKey(g_stmtSync, g_clusterPk, 0, g_recordCount, 20000, g_clusterName);
    TestDeleteLabelByKey(g_stmtSync, g_clusterPk, 0, g_recordCount, 20000, g_clusterName);

    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，唯一local hash，扫描、删除、更新
TEST_F(RsmClusterVertex, warmboot_008_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterName);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestUpdateVertexLabelByKey(g_stmtSync, g_clusterLocalhash, 0, g_recordCount, 10000, g_clusterName);
    TestScanLabelByKey(g_stmtSync, g_clusterLocalhash, 0, g_recordCount, 10000, g_clusterName);
    TestDeleteLabelByKey(g_stmtSync, g_clusterLocalhash, 0, g_recordCount, 10000, g_clusterName);

    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，非唯一local hash，扫描、删除、更新
TEST_F(RsmClusterVertex, warmboot_008_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterNotuniquePath, g_clusterNameNotUnique, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterNameNotUnique);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterNotuniquePath, g_clusterNameNotUnique, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterNameNotUnique, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestUpdateVertexLabelByKey(g_stmtSync, g_clusterLocalhash, 0, g_recordCount, 10000, g_clusterNameNotUnique);
    TestScanLabelByKey(g_stmtSync, g_clusterLocalhash, 0, g_recordCount, 10000, g_clusterNameNotUnique);
    TestDeleteLabelByKey(g_stmtSync, g_clusterLocalhash, 0, g_recordCount, 10000, g_clusterNameNotUnique);

    ret = GmcGetVertexCount(g_stmtSync, g_clusterNameNotUnique, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterNameNotUnique);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，唯一local，等值的查询、更新、删除， 范围查询和范围删除
TEST_F(RsmClusterVertex, warmboot_008_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterName);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestUpdateVertexLabelByKey(g_stmtSync, g_clusterLocal, 0, g_recordCount, 10000, g_clusterName);
    TestScanLabelByKey(g_stmtSync, g_clusterLocal, 0, g_recordCount, 10000, g_clusterName);
    TestDeleteLabelByKey(g_stmtSync, g_clusterLocal, 0, g_recordCount, 10000, g_clusterName);
    TestReplaceVertexLabel(g_stmtSync, 0, g_recordCount, 20000, g_clusterName);
    TestRangeScanLabelByLocal(g_stmtSync, g_clusterLocal, 0, g_recordCount, 20000, g_clusterName);
    TestRangeDeleteLabelByLocal(g_stmtSync, g_clusterLocal, 0, g_recordCount, 20000, g_clusterName);

    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，非唯一local，等值的查询、更新、删除， 范围查询和范围删除
TEST_F(RsmClusterVertex, warmboot_008_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterNotuniquePath, g_clusterNameNotUnique, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterNameNotUnique);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterNotuniquePath, g_clusterNameNotUnique, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterNameNotUnique, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestUpdateVertexLabelByKey(g_stmtSync, g_clusterLocal, 0, g_recordCount, 10000, g_clusterNameNotUnique);
    TestScanLabelByKey(g_stmtSync, g_clusterLocal, 0, g_recordCount, 10000, g_clusterNameNotUnique);
    TestDeleteLabelByKey(g_stmtSync, g_clusterLocal, 0, g_recordCount, 10000, g_clusterNameNotUnique);
    TestReplaceVertexLabel(g_stmtSync, 0, g_recordCount, 20000, g_clusterNameNotUnique);
    TestRangeScanLabelByLocal(g_stmtSync, g_clusterLocal, 0, g_recordCount, 20000, g_clusterNameNotUnique);
    TestRangeDeleteLabelByLocal(g_stmtSync, g_clusterLocal, 0, g_recordCount, 20000, g_clusterNameNotUnique);

    ret = GmcGetVertexCount(g_stmtSync, g_clusterNameNotUnique, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterNameNotUnique);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，唯一hashcluster，扫描、删除、更新
TEST_F(RsmClusterVertex, warmboot_008_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterName);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestUpdateVertexLabelByKey(g_stmtSync, g_clusterHashcluster, 0, g_recordCount, 10000, g_clusterName);
    TestScanLabelByKey(g_stmtSync, g_clusterHashcluster, 0, g_recordCount, 10000, g_clusterName);
    TestDeleteLabelByKey(g_stmtSync, g_clusterHashcluster, 0, g_recordCount, 10000, g_clusterName);

    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，非唯一hashcluster，扫描、删除、更新
TEST_F(RsmClusterVertex, warmboot_008_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterNotuniquePath, g_clusterNameNotUnique, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterNameNotUnique);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterNotuniquePath, g_clusterNameNotUnique, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterNameNotUnique, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestUpdateVertexLabelByKey(g_stmtSync, g_clusterHashcluster, 0, g_recordCount, 10000, g_clusterNameNotUnique);
    TestScanLabelByKey(g_stmtSync, g_clusterHashcluster, 0, g_recordCount, 10000, g_clusterNameNotUnique);
    TestDeleteLabelByKey(g_stmtSync, g_clusterHashcluster, 0, g_recordCount, 10000, g_clusterNameNotUnique);

    ret = GmcGetVertexCount(g_stmtSync, g_clusterNameNotUnique, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterNameNotUnique);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，lpm4，单点查询，范围查询
TEST_F(RsmClusterVertex, warmboot_008_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterName);
    TestRangeScanLabelByLpm(g_stmtSync, g_clusterLpm4, 0, g_recordCount, 0, g_clusterName);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestScanLabelByLpm(g_stmtSync, g_clusterLpm4, 0, g_recordCount, 0, g_clusterName);
    TestRangeScanLabelByLpm(g_stmtSync, g_clusterLpm4, 0, g_recordCount, 0, g_clusterName);

    ret = GmcDeleteAllFast(g_stmtSync, g_clusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，聚簇容器表，-rb reboot数据恢复后，lpm6，单点查询，范围查询
TEST_F(RsmClusterVertex, warmboot_008_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterNotuniquePath, g_clusterNameNotUnique, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterNameNotUnique);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterNotuniquePath, g_clusterNameNotUnique, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterNameNotUnique, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    TestScanLabelByLpm(g_stmtSync, g_clusterLpm6, 0, g_recordCount, 0, g_clusterNameNotUnique);
    TestRangeScanLabelByLpm(g_stmtSync, g_clusterLpm6, 0, g_recordCount, 0, g_clusterNameNotUnique);

    ret = GmcDeleteAllFast(g_stmtSync, g_clusterNameNotUnique);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexCount(g_stmtSync, g_clusterNameNotUnique, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterNameNotUnique);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT新增字段
TEST_F(RsmClusterVertex, warmboot_008_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_clusterName);

    const char *viewClusteredHashInfo = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT ";
    AW_FUN_Log(LOG_STEP, "查询视图1: %s", viewClusteredHashInfo);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    uint64_t supportRsm = 0;
    supportRsm = GetViewValueByField("STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SUPPORT_RESERVED_MEMORY");
    AW_MACRO_EXPECT_EQ_INT(supportRsm, 1);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_clusterPath, g_clusterName, 0, g_clusterConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);

    AW_FUN_Log(LOG_STEP, "查询视图1: %s", viewClusteredHashInfo);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    supportRsm = 0;
    supportRsm = GetViewValueByField("STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SUPPORT_RESERVED_MEMORY");
    AW_MACRO_EXPECT_EQ_INT(supportRsm, 1);

    ret = GmcDeleteAllFast(g_stmtSync, g_clusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexCount(g_stmtSync, g_clusterName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_clusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}
