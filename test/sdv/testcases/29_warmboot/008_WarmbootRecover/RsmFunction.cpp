#include "RsmCommon.h"

class RsmFunction : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void RsmFunction::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void RsmFunction::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void RsmFunction::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
}

void RsmFunction::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 保留内存，默认rsmtsp中创建支持保留内存的kv表
TEST_F(RsmFunction, warmboot_008_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    const char *kvName = "Warmboot08Kv";
    const char *kvConfig = "{\"max_record_count\":10000, \"is_support_reserved_memory\":true}";
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmtSync, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestScanKV(g_stmtSync, kvName, g_recordCount);
    TestRemoveKV(g_stmtSync, kvName, g_recordCount);
    TestBatchSetKV(g_connSync, g_stmtSync, kvName, g_recordCount);
    TestBatchDeleteKV(g_connSync, g_stmtSync, kvName, g_recordCount);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，tsp中创建不支持保留内存的表
TEST_F(RsmFunction, warmboot_008_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "tspB",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *kvName = "Warmboot08Kv";
    const char *kvConfig = "{\"max_record_count\":10000, \"tablespace_name\": \"tspB\"}";
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, g_recordCount, 0);

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestPrepareEnvAndConn();
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 0", "RECOVERY_LABEL_CNT: 0", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmtSync, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  
    ret =  GmcDropTablespace(g_stmtSync, "tspB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，创建rsmTsp，在rsmTsp中创建支持保留内存的vertex表,异步
TEST_F(RsmFunction, warmboot_008_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelConfig[] = "{\"max_record_count\":100000, \"auto_increment\":1, "
                         "\"is_support_reserved_memory\":true, \"rsm_tablespace_name\": \"rsmTspA\"}";
    TestCreateLabelAsync(g_stmtAsync, g_labelName, g_schemaPath, labelConfig);
    TestBatchReplaceAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCreateLabelAsync(g_stmtAsync, g_labelName, g_schemaPath, labelConfig);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestBatchMergeAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 100);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, 100);
    }
    TestBatchDeleteAsync(g_connAsync, g_stmtAsync, 0, g_recordCount);
    TestDropLabelAsync(g_stmtAsync, g_labelName);
    ret =  GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，创建rsmTsp，在rsmTsp中创建支持保留内存的kv表，异步
TEST_F(RsmFunction, warmboot_008_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    const char *kvName = "Warmboot08Kv";
    const char *kvConfig =
        "{\"max_record_count\":10000,  \"rsm_tablespace_name\": \"rsmTspA\", \"is_support_reserved_memory\":true}";
    ret = GmcKvCreateTableAsync(g_stmtAsync, kvName, kvConfig, create_kv_table_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    TestSetKvAsync(g_stmtAsync, kvName, g_recordCount);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTableAsync(g_stmtAsync, kvName, kvConfig, create_kv_table_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmtSync, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestScanKV(g_stmtSync, kvName, g_recordCount);
    TestRemoveKvAsync(g_stmtAsync, kvName, g_recordCount / 2);
    ret = GmcKvTruncateTableAsync(g_stmtAsync, kvName, truncate_kv_table_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcKvDropTableAsync(g_stmtAsync, kvName, drop_kv_table_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret =  GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，创建相同nsp，在nsp中创建支持保留内存的vertex表，异步
TEST_F(RsmFunction, warmboot_008_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = NULL;
    nspCfg.namespaceName = "nspA";
    nspCfg.trxCfg.isolationLevel = GMC_TX_ISOLATION_COMMITTED;
    nspCfg.trxCfg.trxType = GMC_PESSIMISITIC_TRX;
    AsyncUserDataT data = {0};
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmtAsync, "nspA", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    char labelConfig[] = "{\"max_record_count\":100000, \"auto_increment\":1, "
                         "\"is_support_reserved_memory\":true}";
    TestCreateLabelAsync(g_stmtAsync, g_labelName, g_schemaPath, labelConfig);
    TestBatchReplaceAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespace(g_stmtSync, "nspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(g_stmtAsync, "nspA", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    TestCreateLabelAsync(g_stmtAsync, g_labelName, g_schemaPath, labelConfig);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestBatchReplaceAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 0);
    TestBatchMergeAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 100);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, 100);
    }
    TestBatchDeleteAsync(g_connAsync, g_stmtAsync, 0, g_recordCount);
    TestDropLabelAsync(g_stmtAsync, g_labelName);

    ret = GmcDropNamespaceAsync(g_stmtAsync, "nspA", drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，创建相同nsp，在nsp中创建支持保留内存的kv表，异步
TEST_F(RsmFunction, warmboot_008_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = NULL;
    nspCfg.namespaceName = "nspA";
    nspCfg.trxCfg.isolationLevel = GMC_TX_ISOLATION_COMMITTED;
    nspCfg.trxCfg.trxType = GMC_PESSIMISITIC_TRX;
    AsyncUserDataT data = {0};
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmtAsync, "nspA", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    const char *kvName = "Warmboot08Kv";
    const char *kvConfig = "{\"max_record_count\":10000, \"is_support_reserved_memory\":true}";
    ret = GmcKvCreateTableAsync(g_stmtAsync, kvName, kvConfig, create_kv_table_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    TestSetKvAsync(g_stmtAsync, kvName, g_recordCount);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespace(g_stmtSync, "nspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(g_stmtAsync, "nspA", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcKvCreateTableAsync(g_stmtAsync, kvName, kvConfig, create_kv_table_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmtSync, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestScanKV(g_stmtSync, kvName, g_recordCount);
    TestRemoveKvAsync(g_stmtAsync, kvName, g_recordCount / 2);
    ret = GmcKvTruncateTableAsync(g_stmtAsync, kvName, truncate_kv_table_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcKvDropTableAsync(g_stmtAsync, kvName, drop_kv_table_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(g_stmtAsync, "nspA", drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，恢复过程中，DQL查询已经恢复的表和查询恢复的表的视图----所有表的数据，全部恢复后才能查询，场景变更
TEST_F(RsmFunction, warmboot_008_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    char labelName[MAX_NAME_LENGTH];
    for (int i = 0; i < 2; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, g_labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSingleWrite(0, g_recordCount, 0, labelName);
    }

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();

    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 2", "RECOVERY_FINISHED: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, "Warmboot08Vertex0", 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, "Warmboot08Vertex1", 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DQL已经恢复的表
    system("gmsysview count Warmboot08Vertex0");
    const char *expectString = "1000";
    ret = executeCommand((char *)"gmsysview count Warmboot08Vertex0", expectString);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, "Warmboot08Vertex0", NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, g_recordCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, "Warmboot08Vertex0", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < g_recordCount; j++) {
        TestScanAndCheckVertexProperty(g_stmtSync, "Warmboot08Vertex0", g_lablePk, j);
    }

    for (int i = 0; i < 2; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = GmcDeleteAllFast(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

class RsmFunction2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void RsmFunction2::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void RsmFunction2::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void RsmFunction2::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh userPolicyMode=2");
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
}

void RsmFunction2::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 保留内存，恢复过程中，gmrule导入用户
TEST_F(RsmFunction2, warmboot_008_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_FUN_Log(LOG_STEP, "导入用户和group");
    TestImportGroupAndUser();
    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret)
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    system("gmsysview -q V\\$DB_SERVER");
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    TestImportGroupAndUser();
    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    TestCleanEnvAndConn();
    TestRemoveGroupAndUser();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，恢复过程中，gmrule导入用户并鉴权
TEST_F(RsmFunction2, warmboot_008_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_FUN_Log(LOG_STEP, "导入用户和group,及系统权限");
    system("gmsysview -q V\\$PRIVILEGE_USER_STAT > 1.txt");
    TestImportGroupAndUser();
    TestImportPolicy();
    TestPrepareEnvAndConn();

    char labelConfig[] = "{\"max_record_count\":100000, \"auto_increment\":1, "
                         "\"is_support_reserved_memory\":true}";
    TestCreateLabelAsync(g_stmtAsync, g_labelName, g_schemaPath, labelConfig);
    TestBatchReplaceAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    system("gmsysview -q V\\$DB_SERVER");
    system("gmsysview -q V\\$PRIVILEGE_USER_STAT > 2.txt");

    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_FUN_Log(LOG_STEP, "重启后，导入用户和group,及系统权限");
    TestImportGroupAndUser();
    TestImportPolicy();
    TestPrepareEnvAndConn();

    TestCreateLabelAsync(g_stmtAsync, g_labelName, g_schemaPath, labelConfig);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestBatchReplaceAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 0);
    TestBatchMergeAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 100);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, 100);
    }
    TestBatchDeleteAsync(g_connAsync, g_stmtAsync, 0, g_recordCount);
    TestDropLabelAsync(g_stmtAsync, g_labelName);

    TestCleanEnvAndConn();
    TestRemovePolicy();
    TestRemoveGroupAndUser();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，恢复过程中，gmrule导入group并鉴权
TEST_F(RsmFunction2, warmboot_008_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_FUN_Log(LOG_STEP, "导入group,及系统权限");
    system("gmsysview -q V\\$PRIVILEGE_USER_STAT > 1.txt");
    const char *goupFile = "./userFile/group.gmuser";
    const char *goupPolicyFile = "./policyFile/groupAndUserPolicy.gmpolicy";
    TestImportGroupAndUser(goupFile);
    TestImportPolicy(goupPolicyFile);
    TestPrepareEnvAndConn();

    char labelConfig[] = "{\"max_record_count\":100000, \"auto_increment\":1, "
                         "\"is_support_reserved_memory\":true}";
    TestCreateLabelAsync(g_stmtAsync, g_labelName, g_schemaPath, labelConfig);
    TestBatchReplaceAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    system("gmsysview -q V\\$DB_SERVER");
    system("gmsysview -q V\\$PRIVILEGE_USER_STAT > 2.txt");

    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_FUN_Log(LOG_STEP, "重启后，导入用户和group,及系统权限");
    TestImportGroupAndUser(goupFile);
    TestImportPolicy(goupPolicyFile);
    TestPrepareEnvAndConn();

    TestCreateLabelAsync(g_stmtAsync, g_labelName, g_schemaPath, labelConfig);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestBatchReplaceAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 0);
    TestBatchMergeAsync(g_connAsync, g_stmtAsync, 0, g_recordCount, 100);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, 100);
    }
    TestBatchDeleteAsync(g_connAsync, g_stmtAsync, 0, g_recordCount);
    TestDropLabelAsync(g_stmtAsync, g_labelName);

    TestCleanEnvAndConn();
    TestRemovePolicy(goupPolicyFile);
    TestRemoveGroupAndUser(goupFile);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，恢复过程中，进行恢复升级操作
TEST_F(RsmFunction, warmboot_008_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    char labelName[MAX_NAME_LENGTH];
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "schemaFile/AllTypeSchemaV1Alter.gmjson";
    for (int i = 0; i < 100; i++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, g_labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSingleWrite(0, g_recordCount, 0, labelName);
        // gmddl 工具升级表操作
        (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, labelName, filePath);
        ret = executeCommand(cmd, "upgrade successfully");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();

    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 100", "RECOVERY_FINISHED: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, g_labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // gmddl 工具升级表操作
        (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, labelName, filePath);
        ret = executeCommand(cmd, "upgrade successfully");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t recoverFinish = 0;
    uint32_t waitCount = 0;
    while ((recoverFinish != 1) && (waitCount < 120)) {
        recoverFinish = GetViewValueByField("CATA_LABEL_RECOVERY_INFO", "RECOVERY_FINISHED");
        sleep(1);
        waitCount++;
    }
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    AW_FUN_Log(LOG_INFO, "recover succ, recoverFinish is %d, use time : %d s.", recoverFinish, waitCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(recoverFinish, 1);

    for (int i = 0; i < 100; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = GmcDeleteAllFast(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，恢复完成后，新建tsp和表并进行dml操作
TEST_F(RsmFunction, warmboot_008_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    char labelName[MAX_NAME_LENGTH];
    for (int i = 0; i < 10; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, g_labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSingleWrite(0, g_recordCount, 0, labelName);
    }

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();

    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 10", "RECOVERY_FINISHED: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 10; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, g_labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t recoverFinish = 0;
    uint32_t waitCount = 0;
    while ((recoverFinish != 1) && (waitCount < 120)) {
        recoverFinish = GetViewValueByField("CATA_LABEL_RECOVERY_INFO", "RECOVERY_FINISHED");
        sleep(1);
        waitCount++;
    }
    AW_FUN_Log(LOG_INFO, "recover succ, recoverFinish is %d, use time : %d s.", recoverFinish, waitCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(recoverFinish, 1);

    for (int i = 0; i < 10; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = GmcDeleteAllFast(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "-rb reboot数据恢复后新建rsmTsp和表，并dml");
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    char labelConfig[] = "{\"max_record_count\":1000000, \"auto_increment\":1, \"is_support_reserved_memory\":true, "
                         "\"rsm_tablespace_name\": \"rsmTspA\"}";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 10; i < 20; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSingleWrite(0, g_recordCount, 0, labelName);
        TestSingleUpdate(0, g_recordCount, 1000, labelName);
        TestSingleDelete(0, g_recordCount, labelName);
        ret = GmcDropVertexLabel(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret =  GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，恢复完成后，新建nsp和表并进行dml操作
TEST_F(RsmFunction, warmboot_008_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    char labelName[MAX_NAME_LENGTH];
    char cmd[MAX_CMD_SIZE] = {0};
    for (int i = 0; i < 10; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, g_labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSingleWrite(0, g_recordCount, 0, labelName);
    }

    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();

    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 10", "RECOVERY_FINISHED: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 10; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, g_labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t recoverFinish = 0;
    uint32_t waitCount = 0;
    while ((recoverFinish != 1) && (waitCount < 120)) {
        recoverFinish = GetViewValueByField("CATA_LABEL_RECOVERY_INFO", "RECOVERY_FINISHED");
        sleep(1);
        waitCount++;
    }
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    AW_FUN_Log(LOG_INFO, "recover succ, recoverFinish is %d, use time : %d s.", recoverFinish, waitCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(recoverFinish, 1);

    for (int i = 0; i < 10; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = GmcDeleteAllFast(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "-rb reboot数据恢复后新建Nsp和表，并dml");
    GmcNspCfgT normalNspCfg = {};
    normalNspCfg.tablespaceName = NULL;
    normalNspCfg.namespaceName = "nspA";
    normalNspCfg.trxCfg.isolationLevel = GMC_TX_ISOLATION_COMMITTED;
    normalNspCfg.trxCfg.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcCreateNamespaceWithCfg(g_stmtSync, &normalNspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, "nspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 10; i < 20; i ++) {
        (void)sprintf_s(labelName, sizeof(labelName), "Warmboot08Vertex%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName, 0, g_labelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSingleWrite(0, g_recordCount, 0, labelName);
        TestSingleUpdate(0, g_recordCount, 1000, labelName);
        TestSingleDelete(0, g_recordCount, labelName);
        ret = GmcDropVertexLabel(g_stmtSync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret =  GmcDropNamespace(g_stmtSync, "nspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，工具导入vertex
TEST_F(RsmFunction, warmboot_008_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    AW_FUN_Log(LOG_STEP, "导入vertex");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)sprintf_s(
        cmd, MAX_CMD_SIZE, "%s/gmimport -c vschema -t %s -f %s", g_toolPath, g_labelName, g_schemaPath);
    ret = executeCommand(cmd, "Import single file", "schemaFile/AllTypeSchemaV1.gmjson", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = executeCommand(cmd, "Import single file", "schemaFile/AllTypeSchemaV1.gmjson", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，工具导入kv表
TEST_F(RsmFunction, warmboot_008_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    AW_FUN_Log(LOG_STEP, "导入kv表");
    const char *kvName = "Warmboot08Kv";
    const char *confPath = "schemaFile/Warmboot08Kv.gmconfig";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)sprintf_s(
        cmd, MAX_CMD_SIZE, "%s/gmimport -c kvtable -t %s -f %s", g_toolPath, kvName, confPath);
    ret = executeCommand(cmd, "Import single file", "schemaFile/Warmboot08Kv.gmconfig", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = executeCommand(cmd, "Import single file", "schemaFile/Warmboot08Kv.gmconfig", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmtSync, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestScanKV(g_stmtSync, kvName, g_recordCount);
    TestRemoveKV(g_stmtSync, kvName, g_recordCount);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，工具导入表和接口创建表混用
TEST_F(RsmFunction, warmboot_008_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    AW_FUN_Log(LOG_STEP, "导入vertex");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)sprintf_s(
        cmd, MAX_CMD_SIZE, "%s/gmimport -c vschema -t %s -f %s", g_toolPath, g_labelName, g_schemaPath);
    ret = executeCommand(cmd, "Import single file", "schemaFile/AllTypeSchemaV1.gmjson", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，工具导入数据
TEST_F(RsmFunction, warmboot_008_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    AW_FUN_Log(LOG_STEP, "导入vertex");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)sprintf_s(
        cmd, MAX_CMD_SIZE, "%s/gmimport -c vschema -t %s -f %s", g_toolPath, g_labelName, g_schemaPath);
    ret = executeCommand(cmd, "Import single file", "schemaFile/AllTypeSchemaV1.gmjson", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "导入数据");
    char cmdImpData[MAX_CMD_SIZE] = {0};
    const char *dataPath = "schemaFile/Warmboot08Vertex1.gmdata";
    (void)sprintf_s(
        cmdImpData, MAX_CMD_SIZE, "%s/gmimport -c vdata  -t %s -f %s", g_toolPath, g_labelName, dataPath);
    ret = executeCommand(cmdImpData, "Import single file", "schemaFile/Warmboot08Vertex1.gmdata", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

