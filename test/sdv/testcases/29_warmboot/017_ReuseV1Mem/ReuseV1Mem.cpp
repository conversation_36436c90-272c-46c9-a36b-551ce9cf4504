/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 017_ReuseV1Mem
 * Author: hanyang
 * Create: 2025-03-31
 */
#include "ReuseV1Mem.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

class ReuseV1Mem : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ReuseV1Mem::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
    system("sh getV1MemBIn.sh");
}

void ReuseV1Mem::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void ReuseV1Mem::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh defaultTablespaceMaxSize=64");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmExtendBlockSize=180");
    system("${TEST_HOME}/tools/start.sh");
    int ret;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void ReuseV1Mem::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    AW_CHECK_LOG_END();
    int ret;

    // 删除表
    TestDropVertexLabelAll(g_stmt);

    // 关闭连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 001.gmimport -c rsm_migration -f 绝对路径，正常迁移
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath =
        "${GMDB_HOME}/test/sdv/testcases/29_warmboot/017_ReuseV1Mem/V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    const char *v1MemPath =
        "${GMDB_HOME}/test/sdv/testcases/29_warmboot/017_ReuseV1Mem/V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 002.gmimport -c rsm_migration -f 相对路径，正常迁移
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 003.gmimport -c rsm_migration -f 指定路径不存在，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    const char *v1MemPath ="./V1MemPathFail/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "get realpath from ./V1MemPathFail/ unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 004.gmimport -c rsm_migration -f 指定为文件路径，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Dir not exist");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 008.gmimport -h查看帮助提示
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // gmimport -h包含rsm_migration的提示
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -h", g_toolPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "| rsm_migration");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 009.gmimport -c rsm_migration，指定正确的-s参数
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s -s %s", g_toolPath, v1MemPath, g_connServer);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 010.gmimport -c rsm_migration，指定的-s参数错误，非法名字，错误的server locator等，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    const char *serverLocater = "usocket:/run/verona/unix_emserverxxxx";
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s -s %s", g_toolPath, v1MemPath, serverLocater);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "[ERROR] gmimport connect server unsucc", "ret = 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    serverLocater = "1111usocket:/run/verona/unix_emserver";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s -s %s", g_toolPath, v1MemPath, serverLocater);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "[ERROR] gmimport connect server unsucc", "ret = 1004004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 011.gmimport -c rsm_migration，指定的-u参数错误，非法名字，错误的用户等，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    const char *nspOwnerUser = "defaultxxxx";
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s -u %s", g_toolPath, v1MemPath, nspOwnerUser);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);
    // 不解析-u参数，会忽略此参数

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 013.gmimport -c rsm_migration不加-f参数直接指定路径，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "The option(\"-c\") must input 1 parameter(s).");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 018.导入只有系统表的文件，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem13";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 2", "create table num: 2",
        "sysDB0, total record num: 0, insert record num: 0,",
        "sysTable0, total record num: 0, insert record num: 0,");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 019.导入包含系统表和单个业务表数据的文件，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(100, scanNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 020.导入包含系统表和多个业务表数据的文件，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(100, scanNum);

    scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(100, scanNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 021.导入包含系统表和多个业务表数据的文件，启动迁移，扫描表数据，并查询视图，
                重启服务，再次扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(100, scanNum);

    scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(100, scanNum);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(true);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 022.导入V1高端内存文件后，insert数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 023.导入V1高端内存文件后，update数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // update
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 024.导入V1高端内存文件后，delete数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 主键delete
    for (uint32_t i = 200; i < 250; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(150, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 025.导入V1高端内存文件后，replace-insert数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // replace-insert
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(200, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 026.导入V1高端内存文件后，replace-update数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // replace-update
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));

        ret = GmcSetVertexProperty(g_stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 027.导入V1高端内存文件后，merge-insert数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // merge-insert
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(200, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 028.导入V1高端内存文件后，merge-update数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // merge-update
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 029.导入V1高端内存文件后，主键读数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 800);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 800; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 030.导入V1高端内存文件后，全表扫描数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 800);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(700, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 031.导入V1高端内存文件后，批量插入数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量插入
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 300; i++) {
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint32_t totalNum = 0, succNum = 0;
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &succNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, totalNum);
    AW_MACRO_EXPECT_EQ_INT(100, succNum);
    GmcBatchDestroy(batch);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(200, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 032.导入V1高端内存文件后，批量更新数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 批量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 300; i++) {
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint32_t totalNum = 0, succNum = 0;
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &succNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, totalNum);
    AW_MACRO_EXPECT_EQ_INT(100, succNum);
    GmcBatchDestroy(batch);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 033.导入V1高端内存文件后，批量删除数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 批量删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 250; i++) {
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint32_t totalNum = 0, succNum = 0;
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &succNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(50, totalNum);
    AW_MACRO_EXPECT_EQ_INT(50, succNum);
    GmcBatchDestroy(batch);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(150, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 034.导入V1高端内存文件后，异步insert数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步写入
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt_async, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.insertCb = insert_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &RequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 035.导入V1高端内存文件后，异步update数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 异步更新
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table1", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.insertCb = update_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &RequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 036.导入V1高端内存文件后，异步delete数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 异步删除
    for (uint32_t i = 200; i < 250; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table1", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.insertCb = delete_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &RequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);
    }

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(150, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 037.导入V1高端内存文件后，异步批量插入数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步批量插入
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 300; i++) {
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt_async, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(100, data.succNum);
    GmcBatchDestroy(batch);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 038.导入V1高端内存文件后，异步批量更新数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 异步批量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table1", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 300; i++) {
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(100, data.succNum);
    GmcBatchDestroy(batch);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 039.导入V1高端内存文件后，异步批量删除数据，启动迁移，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 异步批量删除
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table1", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 250; i++) {
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(50, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(50, data.succNum);
    GmcBatchDestroy(batch);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(150, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 040.导入V1高端内存文件，写入数据，启动迁移，扫描表数据，并查询视图，
                重启服务，再次扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 查询视图
    TestQueryBlockStat(false);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(true);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 041.导入V1高端内存文件，写入大量数据，启动迁移，扫描表数据，并查询视图，
                重启服务，再次扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(false);

    // insert
    TestInsertVertex1Big(g_stmt, "table1");

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(true);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 042.导入包含系统表和1000个业务表数据的文件，启动迁移，扫描表数据
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem37";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 1002", "create table num: 1002",
        "sysTable0, total record num: 1000, insert record num: 1000",
        "Create table: table1000, total record num: 1, insert record num: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(false);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1000");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1000", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestDropVertexLabelAll(g_stmt, 1000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 043.导入V1高端内存文件，查询视图，启动迁移，扫描表数据，并查询视图，
                重启服务，再次扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(false);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(100, scanNum);

    scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(100, scanNum);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(true);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 044.导入V1高端内存文件后，启动迁移，insert数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(200, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 045.导入V1高端内存文件后，启动迁移，update数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // update
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 046.导入V1高端内存文件后，启动迁移，delete数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // delete
    for (uint32_t i = 200; i < 250; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(150, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 047.导入V1高端内存文件后，启动迁移，replace-insert数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // replace-insert
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(200, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 048.导入V1高端内存文件后，启动迁移，replace-update数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // replace-update
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));

        ret = GmcSetVertexProperty(g_stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 049.导入V1高端内存文件后，启动迁移，merge-insert数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // merge-insert
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(200, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 050.导入V1高端内存文件后，启动迁移，merge-update数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // merge-update
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 051.导入V1高端内存文件后，启动迁移，主键读数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 800);

    // 主键读
    for (uint32_t i = 200; i < 800; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 052.导入V1高端内存文件后，启动迁移，全表扫描数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 800);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(700, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 053.导入V1高端内存文件后，启动迁移，批量插入数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 批量插入
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 300; i++) {
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint32_t totalNum = 0, succNum = 0;
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &succNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, totalNum);
    AW_MACRO_EXPECT_EQ_INT(100, succNum);
    GmcBatchDestroy(batch);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(200, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 054.导入V1高端内存文件后，启动迁移，批量更新数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // 批量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 300; i++) {
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint32_t totalNum = 0, succNum = 0;
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &succNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, totalNum);
    AW_MACRO_EXPECT_EQ_INT(100, succNum);
    GmcBatchDestroy(batch);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 055.导入V1高端内存文件后，启动迁移，批量删除数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // 批量删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 250; i++) {
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint32_t totalNum = 0, succNum = 0;
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &succNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(50, totalNum);
    AW_MACRO_EXPECT_EQ_INT(50, succNum);
    GmcBatchDestroy(batch);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(150, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 056.导入V1高端内存文件后，启动迁移，异步insert数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 异步写入
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt_async, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.insertCb = insert_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &RequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);
    }

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 057.导入V1高端内存文件后，启动迁移，异步update数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // 异步更新
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table2", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.insertCb = update_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &RequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);
    }

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 058.导入V1高端内存文件后，启动迁移，异步delete数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // 异步删除
    for (uint32_t i = 200; i < 250; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table2", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.insertCb = delete_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &RequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);
    }

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(150, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 059.导入V1高端内存文件后，启动迁移，异步批量插入数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 异步批量插入
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 300; i++) {
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(g_stmt_async, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(100, data.succNum);
    GmcBatchDestroy(batch);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 060.导入V1高端内存文件后，启动迁移，异步批量更新数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // 异步批量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table2", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 300; i++) {
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(100, data.succNum);
    GmcBatchDestroy(batch);

    // 主键读
    for (uint32_t i = 200; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验值
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = queryPropertyAndCompare(g_stmt, "Value", GMC_DATATYPE_BYTES, valueV1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 061.导入V1高端内存文件后，启动迁移，异步批量删除数据，扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // insert
    TestInsertVertex2(g_stmt, "table2", 200, 300);

    // 异步批量删除
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "table2", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 200; i < 250; i++) {
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "Index0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", (i + 100));
        ret = GmcSetVertexProperty(g_stmt_async, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(50, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(50, data.succNum);
    GmcBatchDestroy(batch);

    // 全表扫描
    uint32_t scanNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "table2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        scanNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(150, scanNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 062.导入V1高端内存文件，写入数据，启动迁移，扫描表数据，并查询视图，
                重启服务，再次写入数据，再次扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 200, 300);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1(g_stmt, "table1", 300, 400);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "300");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 063.导入V1高端内存文件，写入大量数据，启动迁移，扫描表数据，并查询视图，
                重启服务，再次写入大量数据，超过高端内存配置值，再次扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex1Big(g_stmt, "table1");

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    TestInsertVertex2Big(g_stmt, "table2");

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 064.导入V1高端内存文件，多次启动迁移，扫描表数据，并查询视图，
                重启服务，再次启动迁移，再次扫描表数据，并查询视图
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem15";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 4", "create table num: 4",
        "table1, total record num: 100, insert record num: 100",
        "table2, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询视图
    TestQueryBlockStat(false);

    for (uint32_t i = 0; i < 10; i++) {
        // 数据迁移
        const char *v1MemPath ="./V1MemPath/";
        (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
        ret = executeCommand(cmd, "Rsm migration successfully");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(cmd, 0, MAX_CMD_SIZE);
    }

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 清理环境
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 从保留内存重启服务
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table2");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table2", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 065.目录没有写权限，设为高端内存路径，并启动迁移，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1Mem, warmboot_017_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    system("mkdir V1MemPathReadOnly");
    system("chmod -R 444 V1MemPathReadOnly/");

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    system("chmod -R 777 V1MemPathReadOnly/");
    system("rm -rf V1MemPathReadOnly/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

class ReuseV1MemModify : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ReuseV1MemModify::SetUpTestCase()
{
}

void ReuseV1MemModify::TearDownTestCase()
{
}

void ReuseV1MemModify::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void ReuseV1MemModify::TearDown()
{
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AW_CHECK_LOG_END();
}

/*****************************************************************************
 Description  : 005.RsmExtendBlockSize 配置为0，迁移数据，报错
 Author       : hanyang7

*****************************************************************************/
TEST_F(ReuseV1MemModify, warmboot_017_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh getV1MemBIn.sh");

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmExtendBlockSize=0");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Unable to rsm migration, cfg extend block size: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(false);

    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 006.RsmExtendBlockSize 配置为512，迁移数据，正常迁移
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1MemModify, warmboot_017_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh getV1MemBIn.sh");

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmExtendBlockSize=512");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(false);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 007.RsmExtendBlockSize 配置数据小于deviceSize，启动服务，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1MemModify, warmboot_017_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmExtendBlockSize=1");

    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);

    ret = executeCommand(cmd, "Error: start service failure");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 012.关闭rsm开关，使用gmimport -c rsm_migration迁移数据，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1MemModify, warmboot_017_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh getV1MemBIn.sh");

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=0");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "[ERROR] Unable to import v1 bin file", "only support in rsm mode");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 014.配置RsmExtendBlockSize大于RsmBlockSize1倍，启动服务，迁移数据成功
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1MemModify, warmboot_017_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh getV1MemBIn.sh");

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmExtendBlockSize=256");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmBlockSize=128");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(false);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 015.配置RsmExtendBlockSize大于RsmBlockSize8倍，启动服务，迁移数据成功
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1MemModify, warmboot_017_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh getV1MemBIn.sh");

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmExtendBlockSize=512");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmBlockSize=64");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(false);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 016.配置RsmExtendBlockSize等于RsmBlockSize，启动服务，迁移数据成功
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1MemModify, warmboot_017_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh getV1MemBIn.sh");

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmExtendBlockSize=128");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmBlockSize=128");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(false);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 Description  : 017.配置RsmExtendBlockSize小于RsmBlockSize，启动服务，迁移数据成功
 Author       : hanyang
*****************************************************************************/
TEST_F(ReuseV1MemModify, warmboot_017_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh getV1MemBIn.sh");

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmExtendBlockSize=64");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmBlockSize=128");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入v1二进制文件
    const char *v1BinPath = "./V1MemFile/DB_RsdMem14";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c v1_rsm -f %s", g_toolPath, v1BinPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Total table num: 3", "create table num: 3",
        "sysDB0, total record num: 1, insert record num: 1,",
        "sysTable0, total record num: 1, insert record num: 1",
        "table1, total record num: 100, insert record num: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(false);

    // 数据迁移
    const char *v1MemPath ="./V1MemPath/";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c rsm_migration -f %s", g_toolPath, v1MemPath);
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    ret = executeCommand(cmd, "Rsm migration successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    // 查询视图
    TestQueryBlockStat(true);

    // 查询导入表中的数据
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysDB0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysDB0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "sysTable0");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "sysTable0", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, "table1");
    AW_FUN_Log(LOG_INFO, "cmd:%s", cmd);
    system(cmd);
    ret = executeCommand(cmd, "table1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}
