#include "RsmDdlCommon.h"

class RsmLoadMode1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void RsmLoadMode1::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void RsmLoadMode1::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

char file_path1[200] = "./testcases/29_warmboot/012_RsmSupportDdl/schemaFile";
void RsmLoadMode1::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh schemaLoader=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableClusterHash=0");
    char cmd[1024];
    // 没有开启鉴权模式，故只需配置4个路径即可，不需要保证用户白名单，系统权限，对象权限3个路径下权限的正确性
    (void)snprintf(cmd, 1024, "sh $TEST_HOME/tools/modifyCfg.sh \"schemaPath=%s;%s;%s;%s;\"", g_schemaDir, g_schemaDir,
        g_schemaDir, g_schemaDir);
    system(cmd);
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
}

void RsmLoadMode1::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 全量导表kv表非写时重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *kvName = "kvTable012";
    system("gmsysview -q V\\$CATA_KV_TABLE_INFO | grep kvTable012 | grep -v SOURCE_LABEL_NAME");
    const char *viewKvTableInfo = "V\\$CATA_KV_TABLE_INFO";
    int kvTableNum = TestGetViewCount(viewKvTableInfo, "kvTable012 | grep -v SOURCE_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(kvTableNum, 1);

    TestPrepareEnvAndConn();
    TestSetKV(g_stmtSync, kvName, 0, g_recordCount, 0);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    kvTableNum = TestGetViewCount(viewKvTableInfo, "kvTable012 | grep -v SOURCE_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(kvTableNum, 1);

    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmtSync, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestSetKV(g_stmtSync, kvName, 0, g_recordCount, g_recordCount);
    TestRemoveKV(g_stmtSync, kvName, 0, g_recordCount);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表heap表非写时重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "Warmboot012Vertex";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep Warmboot012Vertex");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "Warmboot012Vertex | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestSingleWrite(0, g_recordCount, 0, vertexName);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexNum = TestGetViewCount(viewTableInfo, "Warmboot012Vertex | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestSingleUpdate(0, g_recordCount, g_recordCount, vertexName);
    TestSingleDelete(0, g_recordCount, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

class RsmLoadMode1Cluster : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void RsmLoadMode1Cluster::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void RsmLoadMode1Cluster::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void RsmLoadMode1Cluster::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableClusterHash=1");
    system("${TEST_HOME}/tools/modifyCfg.sh memCompactEnable=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableReleaseDevice=1");
    system("sh $TEST_HOME/tools/modifyCfg.sh RsmKeyRange=0,1");
    system("sh $TEST_HOME/tools/modifyCfg.sh deviceSize=1");
    system("sh $TEST_HOME/tools/modifyCfg.sh RsmBlockSize=32");
    system("sh $TEST_HOME/tools/modifyCfg.sh defaultTablespaceMaxSize=16");
    system("${TEST_HOME}/tools/modifyCfg.sh schemaLoader=1");
    char cmd[1024];
    // 没有开启鉴权模式，故只需配置4个路径即可，不需要保证用户白名单，系统权限，对象权限3个路径下的正确性
    (void)snprintf(cmd, 1024, "sh $TEST_HOME/tools/modifyCfg.sh \"schemaPath=%s;%s;%s;%s;\"", g_schemaDir, g_schemaDir,
        g_schemaDir, g_schemaDir);
    system(cmd);
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
}

void RsmLoadMode1Cluster::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 全量导表普通表开聚簇容器非写时重启，数据后台恢复
TEST_F(RsmLoadMode1Cluster, warmboot_012_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexCluster012";
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexCluster012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexName);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexNum = TestGetViewCount(viewTableInfo, "VertexCluster012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestUpdateVertexLabelByKey(g_stmtSync, "PK", 0, 1000, g_recordCount, vertexName);
    TestScanLabelByKey(g_stmtSync, "PK", 0, 1000, g_recordCount, vertexName);
    TestDeleteLabelByKey(g_stmtSync, "PK", 0, 1000, g_recordCount, vertexName);

    TestUpdateVertexLabelByKey(g_stmtSync, "localhashkey", 1000, 2000, g_recordCount, vertexName);
    TestScanLabelByKey(g_stmtSync, "localhashkey", 1000, 2000, g_recordCount, vertexName);
    TestDeleteLabelByKey(g_stmtSync, "localhashkey", 1000, 2000, g_recordCount, vertexName);

    TestUpdateVertexLabelByKey(g_stmtSync, "localkey", 2000, 3000, g_recordCount, vertexName);
    TestScanLabelByKey(g_stmtSync, "localkey", 2000, 3000, g_recordCount, vertexName);
    TestDeleteLabelByKey(g_stmtSync, "localkey", 2000, 3000, g_recordCount, vertexName);

    TestReplaceVertexLabel(g_stmtSync, 3000, 4000, g_recordCount, vertexName);
    TestRangeScanLabelByLocal(g_stmtSync, "localkey", 3000, 4000, g_recordCount, vertexName);
    TestRangeDeleteLabelByLocal(g_stmtSync, "localkey", 3000, 4000, g_recordCount, vertexName);

    TestUpdateVertexLabelByKey(g_stmtSync, "hashclusterkey", 4000, 5000, g_recordCount, vertexName);
    TestScanLabelByKey(g_stmtSync, "hashclusterkey", 4000, 5000, g_recordCount, vertexName);
    TestDeleteLabelByKey(g_stmtSync, "hashclusterkey", 4000, 5000, g_recordCount, vertexName);

    TestScanLabelByLpm(g_stmtSync, "lpm4key", 5000, g_recordCount, 0, vertexName);
    TestRangeScanLabelByLpm(g_stmtSync, "lpm4key", 5000, g_recordCount, 0, vertexName);

    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, count);

    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表状态合并订阅表非写时重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexStmg012";
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexStmg012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn(true);
    TestSingleWrite(0, g_recordCount, 0, vertexName);
    TestCleanEnvAndConn(true);

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn(true);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexNum = TestGetViewCount(viewTableInfo, "VertexStmg012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestBatchReplaceAsync(g_connAsync, g_stmtAsync, 0, 1000, 0, vertexName);
    TestBatchMergeAsync(g_connAsync, g_stmtAsync, 0, 1000, 1000, vertexName);
    TestBatchDeleteAsync(g_connAsync, g_stmtAsync, 0, 1000, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn(true);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表Tree表非写时重启，数据后台恢
TEST_F(RsmLoadMode1, warmboot_012_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexTree012";
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexTree012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexName, true);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexNum = TestGetViewCount(viewTableInfo, "VertexTree012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);

    // member key 操作
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, vertexName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t pkVal = 0;
    ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmtSync, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root = NULL;
    GmcNodeT *t1V = NULL;
    ret = GmcGetRootNode(g_stmtSync, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1_V", &t1V);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // apend
    GmcNodeT *item = NULL;
    ret = GmcNodeAppendElement(t1V, &item);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value = 10;
    ret = GmcNodeSetPropertyByName(item, (char *)"V1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(item, (char *)"V2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    // update
    GmcIndexKeyT *key;
    ret = GmcNodeAllocKey(t1V, "memkey", &key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 0;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(t1V, key, &item);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 100000;
    ret = GmcNodeSetPropertyByName(item, "V2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // remove
    value = 1;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(t1V, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeFreeKey(key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtSync, vertexName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmtSync, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    ret = GmcFetch(g_stmtSync, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmtSync, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1_V", &t1V);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t itemCount = 0;
    ret = GmcNodeGetElementCount(t1V, &itemCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(itemCount, 10);

    value = 1;
    ret = GmcNodeAllocKey(t1V, "memkey", &key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret)
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(t1V, key, &item);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);

    value = 0;
    bool null = true;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(t1V, key, &item);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(item, "V2", &value, sizeof(uint32_t), &null);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(value, 100000);
    EXPECT_FALSE(null);

    value = 10;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(t1V, key, &item);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(item, "V2", &value, sizeof(uint32_t), &null);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(value, 10);
    EXPECT_FALSE(null);
    ret = GmcNodeFreeKey(key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表heap表升级重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "Warmboot012Vertex";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep Warmboot012Vertex");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "Warmboot012Vertex | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestSingleWrite(0, 1000, 0, vertexName);
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "alterSchema/VertexHeapAlter.gmjson";
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, vertexName, filePath);
    AW_FUN_Log(LOG_STEP, "cmd = %s", cmd);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(1000, 2000, 0, vertexName, 1);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    vertexNum = TestGetViewCount(viewTableInfo, "Warmboot012Vertex | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, vertexName, filePath);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(2000, 3000, 0, vertexName);
    TestSingleWrite(3000, 4000, 0, vertexName, 1);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(4000, count);
    TestSingleDelete(0, 4000, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表heap表升级后降级重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "Warmboot012Vertex";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep Warmboot012Vertex");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "Warmboot012Vertex | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestSingleWrite(0, 1000, 0, vertexName);
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "alterSchema/VertexHeapAlter.gmjson";
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, vertexName, filePath);
    AW_FUN_Log(LOG_STEP, "cmd = %s", cmd);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(1000, 2000, 0, vertexName, 1);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -v %d -t %s -d sync", g_toolPath, 0, vertexName);
    ret = executeCommand(cmd, "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(2000, 3000, 0, vertexName);

    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    vertexNum = TestGetViewCount(viewTableInfo, "Warmboot012Vertex | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, vertexName, filePath);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(3000, 4000, 0, vertexName, 1);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -v %d -t %s -d sync", g_toolPath, 0, vertexName);
    ret = executeCommand(cmd, "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(4000, 5000, 0, vertexName);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, count);
    TestSingleDelete(0, 5000, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表普通表开聚簇容器升级重启，数据后台恢复
TEST_F(RsmLoadMode1Cluster, warmboot_012_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexCluster012";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep VertexCluster012");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexCluster012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestInsertVertexLabel(g_stmtSync, 0, 1000, 0, vertexName);
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "alterSchema/VertexClusterAlter.gmjson";
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, vertexName, filePath);
    AW_FUN_Log(LOG_STEP, "cmd = %s", cmd);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 1000, 2000, 0, vertexName, false, 1);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    vertexNum = TestGetViewCount(viewTableInfo, "VertexCluster012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, vertexName, filePath);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 2000, 3000, 0, vertexName);
    TestInsertVertexLabel(g_stmtSync, 3000, 4000, 0, vertexName, false, 1);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(4000, count);
    TestDeleteLabelByKey(g_stmtSync, "PK", 0, 4000, 0, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表普通表开聚簇容器升级后降级重启，数据后台恢复
TEST_F(RsmLoadMode1Cluster, warmboot_012_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexCluster012";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep VertexCluster012");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexCluster012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestInsertVertexLabel(g_stmtSync, 0, 1000, 0, vertexName);
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "alterSchema/VertexClusterAlter.gmjson";
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, vertexName, filePath);
    AW_FUN_Log(LOG_STEP, "cmd = %s", cmd);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 1000, 2000, 0, vertexName, false, 1);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -v %d -t %s -d sync", g_toolPath, 0, vertexName);
    ret = executeCommand(cmd, "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 2000, 3000, 0, vertexName);

    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    vertexNum = TestGetViewCount(viewTableInfo, "VertexCluster012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, vertexName, filePath);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 3000, 4000, 0, vertexName, false, 1);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -v %d -t %s -d sync", g_toolPath, 0, vertexName);
    ret = executeCommand(cmd, "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 4000, 5000, 0, vertexName);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, count);
    TestDeleteLabelByKey(g_stmtSync, "PK", 0, 5000, 0, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表状态合并订阅表升级重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexStmg012";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep VertexStmg012");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexStmg012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestSingleWrite(0, 1000, 0, vertexName);
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "alterSchema/VertexStmgAlter.gmjson";
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, vertexName, filePath);
    AW_FUN_Log(LOG_STEP, "cmd = %s", cmd);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(1000, 2000, 0, vertexName, 1);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    vertexNum = TestGetViewCount(viewTableInfo, "VertexStmg012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, vertexName, filePath);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(2000, 3000, 0, vertexName);
    TestSingleWrite(3000, 4000, 0, vertexName, 1);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(4000, count);
    TestSingleDelete(0, 4000, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表状态合并订阅表升级后降级重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexStmg012";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep VertexStmg012");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexStmg012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestSingleWrite(0, 1000, 0, vertexName);
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "alterSchema/VertexStmgAlter.gmjson";
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, vertexName, filePath);
    AW_FUN_Log(LOG_STEP, "cmd = %s", cmd);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(1000, 2000, 0, vertexName, 1);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -v %d -t %s -d sync", g_toolPath, 0, vertexName);
    ret = executeCommand(cmd, "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(2000, 3000, 0, vertexName);

    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    vertexNum = TestGetViewCount(viewTableInfo, "VertexStmg012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, vertexName, filePath);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(3000, 4000, 0, vertexName, 1);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -v %d -t %s -d sync", g_toolPath, 0, vertexName);
    ret = executeCommand(cmd, "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(4000, 5000, 0, vertexName);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, count);
    TestSingleDelete(0, 5000, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表Tree表升级重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexTree012";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep VertexTree012");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexTree012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestInsertVertexLabel(g_stmtSync, 0, 1000, 0, vertexName, true);
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "alterSchema/VertexTreeAlter.gmjson";
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, vertexName, filePath);
    AW_FUN_Log(LOG_STEP, "cmd = %s", cmd);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 1000, 2000, 0, vertexName, true, 1);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    vertexNum = TestGetViewCount(viewTableInfo, "VertexTree012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, vertexName, filePath);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 2000, 3000, 0, vertexName, true);
    TestInsertVertexLabel(g_stmtSync, 3000, 4000, 0, vertexName, true, 1);
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(4000, count);
    TestDeleteLabelByKey(g_stmtSync, "PK", 0, 4000, 0, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表Tree表升级后降级重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexTree012";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep VertexTree012");
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexTree012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

    TestPrepareEnvAndConn();
    TestInsertVertexLabel(g_stmtSync, 0, 1000, 0, vertexName, true);
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath = "alterSchema/VertexTreeAlter.gmjson";
    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, vertexName, filePath);
    AW_FUN_Log(LOG_STEP, "cmd = %s", cmd);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 1000, 2000, 0, vertexName, true, 1);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -v %d -t %s -d sync", g_toolPath, 0, vertexName);
    ret = executeCommand(cmd, "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 2000, 3000, 0, vertexName, true);

    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    vertexNum = TestGetViewCount(viewTableInfo, "VertexTree012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // gmddl 工具升级表操作
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s  -u online", g_toolPath, vertexName, filePath);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 3000, 4000, 0, vertexName, true, 1);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -v %d -t %s -d sync", g_toolPath, 0, vertexName);
    ret = executeCommand(cmd, "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 4000, 5000, 0, vertexName, true);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, count);
    TestDeleteLabelByKey(g_stmtSync, "PK", 0, 5000, 0, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表kv表写时重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *viewKvTableInfo = "V\\$CATA_KV_TABLE_INFO";
    const char *kvName = "kvTable012";
    int kvTableNum = TestGetViewCount(viewKvTableInfo, "kvTable012 | grep -v SOURCE_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(kvTableNum, 1);
    TestPrepareEnvAndConn();
    TestSetKV(g_stmtSync, kvName, 0, g_recordCount, 0);

    for (int i = 0; i < CYCLE_NUM; i++) {
        pthread_t thrStopServer, thrSetKv;
        pthread_barrier_init(&g_barrier, NULL, 2);
        ret = pthread_create(&thrSetKv, NULL, ThreadSetKv, (void *)&g_recordCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thrStopServer, NULL, ThreadStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(thrSetKv, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thrStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        pthread_barrier_destroy(&g_barrier);
        TestCleanEnvAndConn();

        (void)system("sh $TEST_HOME/tools/start.sh -f -rb");
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        kvTableNum = TestGetViewCount(viewKvTableInfo, "kvTable012 | grep -v SOURCE_LABEL_NAME");
        AW_MACRO_EXPECT_EQ_INT(kvTableNum, 1);

        TestPrepareEnvAndConn();
        uint32_t count = 0;
        ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (g_isExist) {
            // 如果线程setkv成功，则数据需要恢复
            ret = GmcKvTableRecordCount(g_stmtSync, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount + 1, count);
        } else {
            // 如果线程setkv失败，则数据不需要恢复
            ret = GmcKvTableRecordCount(g_stmtSync, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
        }

        if (g_isExist) {
            TestRemoveKV(g_stmtSync, kvName, g_recordCount, g_recordCount + 1);
        }
    }

    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表heap表写时重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "Warmboot012Vertex";
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "Warmboot012Vertex | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    TestPrepareEnvAndConn();
    TestSingleWrite(0, g_recordCount, 0, vertexName);

    for (int i = 0; i < CYCLE_NUM; i++) {
        pthread_t thrStopServer, thrWriteVertex;
        pthread_barrier_init(&g_barrier, NULL, 2);
        PthargT thrArgs;
        thrArgs.index = g_recordCount;
        thrArgs.schemaPath = vertexName;
        ret = pthread_create(&thrWriteVertex, NULL, ThreadWrtieVertex, (void *)&thrArgs);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thrStopServer, NULL, ThreadStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(thrWriteVertex, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thrStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        pthread_barrier_destroy(&g_barrier);
        TestCleanEnvAndConn();

        (void)system("sh $TEST_HOME/tools/start.sh -f -rb");
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        vertexNum = TestGetViewCount(viewTableInfo, "Warmboot012Vertex | grep VERTEX_LABEL_NAME");
        AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

        TestPrepareEnvAndConn();
        uint64_t count = 0;
        if (g_isExist) {
            // 如果线程write vertex成功，则数据需要恢复
            ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount + 1, count);
        } else {
            // 如果线程write vertex失败，则数据不需要恢复
            ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
        }

        if (g_isExist) {
            TestSingleDelete(g_recordCount, g_recordCount + 1, vertexName);
        }
    }

    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表普通表开聚簇容器写时重启，数据后台恢复
TEST_F(RsmLoadMode1Cluster, warmboot_012_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexCluster012";
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexCluster012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    TestPrepareEnvAndConn();
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexName);

    for (int i = 0; i < CYCLE_NUM; i++) {
        pthread_t thrStopServer, thrWriteVertex;
        pthread_barrier_init(&g_barrier, NULL, 2);
        PthargT thrArgs;
        thrArgs.index = g_recordCount;
        thrArgs.schemaPath = vertexName;
        ret = pthread_create(&thrWriteVertex, NULL, ThreadWrtieVertex, (void *)&thrArgs);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thrStopServer, NULL, ThreadStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(thrWriteVertex, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thrStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        pthread_barrier_destroy(&g_barrier);
        TestCleanEnvAndConn();

        (void)system("sh $TEST_HOME/tools/start.sh -f -rb");
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        vertexNum = TestGetViewCount(viewTableInfo, "VertexCluster012 | grep VERTEX_LABEL_NAME");
        AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

        TestPrepareEnvAndConn();
        uint64_t count = 0;
        if (g_isExist) {
            // 如果线程write vertex成功，则数据需要恢复
            ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount + 1, count);
        } else {
            // 如果线程write vertex失败，则数据不需要恢复
            ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
        }

        if (g_isExist) {
            TestDeleteLabelByKey(g_stmtSync, "PK", g_recordCount, g_recordCount + 1, 0, vertexName);
        }
    }

    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表状态合并订阅表写时重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexStmg012";
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexStmg012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    TestPrepareEnvAndConn();
    TestSingleWrite(0, g_recordCount, 0, vertexName);

    for (int i = 0; i < CYCLE_NUM; i++) {
        pthread_t thrStopServer, thrWriteVertex;
        pthread_barrier_init(&g_barrier, NULL, 2);
        PthargT thrArgs;
        thrArgs.index = g_recordCount;
        thrArgs.schemaPath = vertexName;
        ret = pthread_create(&thrWriteVertex, NULL, ThreadWrtieVertex, (void *)&thrArgs);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thrStopServer, NULL, ThreadStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(thrWriteVertex, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thrStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        pthread_barrier_destroy(&g_barrier);
        TestCleanEnvAndConn();

        (void)system("sh $TEST_HOME/tools/start.sh -f -rb");
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        vertexNum = TestGetViewCount(viewTableInfo, "VertexStmg012 | grep VERTEX_LABEL_NAME");
        AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

        TestPrepareEnvAndConn();
        uint64_t count = 0;
        if (g_isExist) {
            // 如果线程write vertex成功，则数据需要恢复
            ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount + 1, count);
        } else {
            // 如果线程write vertex失败，则数据不需要恢复
            ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
        }

        if (g_isExist) {
            TestSingleDelete(g_recordCount, g_recordCount + 1, vertexName);
        }
    }

    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表Tree表写时重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *vertexName = "VertexTree012";
    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = TestGetViewCount(viewTableInfo, "VertexTree012 | grep VERTEX_LABEL_NAME");
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);
    TestPrepareEnvAndConn();
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexName, true);

    for (int i = 0; i < CYCLE_NUM; i++) {
        pthread_t thrStopServer, thrWriteVertex;
        pthread_barrier_init(&g_barrier, NULL, 2);
        PthargT thrArgs;
        thrArgs.index = g_recordCount;
        thrArgs.schemaPath = vertexName;
        ret = pthread_create(&thrWriteVertex, NULL, ThreadWrtieVertex, (void *)&thrArgs);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thrStopServer, NULL, ThreadStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(thrWriteVertex, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thrStopServer, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        pthread_barrier_destroy(&g_barrier);
        TestCleanEnvAndConn();

        (void)system("sh $TEST_HOME/tools/start.sh -f -rb");
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        vertexNum = TestGetViewCount(viewTableInfo, "VertexTree012 | grep VERTEX_LABEL_NAME");
        AW_MACRO_EXPECT_EQ_INT(vertexNum, 1);

        TestPrepareEnvAndConn();
        uint64_t count = 0;
        if (g_isExist) {
            // 如果线程write vertex成功，则数据需要恢复
            ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount + 1, count);
        } else {
            // 如果线程write vertex失败，则数据不需要恢复
            ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
        }

        if (g_isExist) {
            TestDeleteLabelByKey(g_stmtSync, "PK", g_recordCount, g_recordCount + 1, 0, vertexName);
        }
    }

    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

class RsmLoadMode1Heap : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void RsmLoadMode1Heap::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void RsmLoadMode1Heap::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void RsmLoadMode1Heap::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableClusterHash=0");
    system("${TEST_HOME}/tools/modifyCfg.sh memCompactEnable=1");
    system("${TEST_HOME}/tools/modifyCfg.sh enableReleaseDevice=1");
    system("sh $TEST_HOME/tools/modifyCfg.sh RsmKeyRange=0,1");
    system("sh $TEST_HOME/tools/modifyCfg.sh deviceSize=1");
    system("sh $TEST_HOME/tools/modifyCfg.sh RsmBlockSize=32");
    system("sh $TEST_HOME/tools/modifyCfg.sh defaultTablespaceMaxSize=16");
    system("${TEST_HOME}/tools/modifyCfg.sh schemaLoader=1");
    char cmd[1024];
    // 没有开启鉴权模式，故只需配置4个路径即可，不需要保证用户白名单，系统权限，对象权限3个路径下的正确性
    (void)snprintf(cmd, 1024, "sh $TEST_HOME/tools/modifyCfg.sh \"schemaPath=%s;%s;%s;%s;\"", g_schemaDir, g_schemaDir,
        g_schemaDir, g_schemaDir);
    system(cmd);
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
}

void RsmLoadMode1Heap::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 全量导表heap表触发页搬迁
TEST_F(RsmLoadMode1Heap, warmboot_012_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    int ret = 0;
    TestPrepareEnvAndConn();
    const char *vertexName = "Warmboot012Vertex";
    TestSingleWrite(0, g_recordCount * 4, 0, vertexName);
    system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT");
    uint32_t deviceCount = 0;
    deviceCount = GetViewValueByField("STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT");
    AW_FUN_Log(LOG_INFO, "deviceCount = %d", deviceCount);
    // reservedMemPublic 6 + rsmTspA 1
    AW_MACRO_EXPECT_EQ_INT(deviceCount, 7);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    TestSingleDelete(0, g_recordCount * 3, vertexName);
    GmcAlarmDataT alarmData;
    ret = GmcGetAlarmData(g_stmtSync, GMC_ALARM_RSM_TABLE_SPACE_USED_INFO, &alarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int sleepCount = 0;
    while (true) {
        if (sleepCount > 120) {
            AW_FUN_Log(LOG_ERROR, "wait move device task compress timeout.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            break;
        }
        sleep(1);
        sleepCount++;
        char command[1024];
        (void)sprintf_s(command, sizeof(command), "gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -f TASK_TYPE=2");
        ret = executeCommand(command, "SUB_TASK_STATUS: compress_finish");
        if (ret == GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "compress_finish");
            break;
        }
    };
    // reservedMemPublic 1 + rsmTspA 1, reservedMemPublic可能存在其它表的元数据
    system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT");
    deviceCount = 0;
    deviceCount = GetViewValueByField("STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT");
    AW_FUN_Log(LOG_INFO, "deviceCount = %d", deviceCount);
    AW_MACRO_EXPECT_EQ_INT(deviceCount, 3);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表聚簇容器表触发页搬迁
TEST_F(RsmLoadMode1Cluster, warmboot_012_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    int ret = 0;
    TestPrepareEnvAndConn();
    const char *vertexName = "VertexCluster012";
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount * 2, 0, vertexName);
    uint32_t deviceCount = 0;
    deviceCount = GetViewValueByField("STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT");
    AW_FUN_Log(LOG_INFO, "deviceCount = %d", deviceCount);
    // reservedMemPublic 16 + rsmTspA 1
    AW_MACRO_EXPECT_EQ_INT(deviceCount, 17);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    TestDeleteLabelByKey(g_stmtSync, "PK", 0, g_recordCount * 2, 0, vertexName);
    GmcAlarmDataT alarmData;
    ret = GmcGetAlarmData(g_stmtSync, GMC_ALARM_RSM_TABLE_SPACE_USED_INFO, &alarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int sleepCount = 0;
    while (true) {
        if (sleepCount > 120) {
            AW_FUN_Log(LOG_ERROR, "wait move device task compress timeout.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            break;
        }
        sleep(1);
        sleepCount++;
        char command[1024];
        (void)sprintf_s(command, sizeof(command), "gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -f TASK_TYPE=2");
        ret = executeCommand(command, "SUB_TASK_STATUS: compress_finish");
        if (ret == GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "compress_finish");
            break;
        }
    };
    // reservedMemPublic 1 + rsmTspA 1, reservedMemPublic可能存在其它表的元数据
    system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT");
    deviceCount = 0;
    deviceCount = GetViewValueByField("STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT");
    AW_FUN_Log(LOG_INFO, "deviceCount = %d", deviceCount);
    EXPECT_GE(deviceCount, 2);
    EXPECT_LE(deviceCount, 4);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表分区对账中重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    TestPrepareEnvAndConn();
    const char *vertexName = "Warmboot012Vertex";
    TestSingleWrite(0, g_recordCount, 0, vertexName);

    ret = GmcBeginCheck(g_stmtSync, vertexName, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新1000，其中100条 分区0
    TestSingleUpdate(0, 1000, g_recordCount, vertexName);
    system("gmsysview -q V\\$QRY_AGE_TASK");

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);

    ret = GmcBeginCheck(g_stmtSync, vertexName, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新1000，其中100条 分区1
    TestSingleUpdate(1000, 2000, g_recordCount, vertexName, false);
    ret = GmcEndCheck(g_stmtSync, vertexName, 1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$QRY_AGE_TASK");

    count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(9100, count);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表对账中重启，数据后台恢复
TEST_F(RsmLoadMode1Cluster, warmboot_012_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    TestPrepareEnvAndConn();
    const char *vertexName = "VertexCluster012";
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexName);
    ret = GmcBeginCheck(g_stmtSync, vertexName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUpdateVertexLabelByKey(g_stmtSync, "PK", 0, 5000, g_recordCount, vertexName);
    system("gmsysview -q V\\$QRY_AGE_TASK");

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcBeginCheck(g_stmtSync, vertexName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUpdateVertexLabelByKey(g_stmtSync, "PK", 0, 3000, g_recordCount, vertexName);
    ret = GmcEndCheck(g_stmtSync, vertexName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$QRY_AGE_TASK");

    count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3000, count);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表分区对账完成重启，数据后台恢复
TEST_F(RsmLoadMode1, warmboot_012_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    TestPrepareEnvAndConn();
    const char *vertexName = "Warmboot012Vertex";
    TestSingleWrite(0, g_recordCount, 0, vertexName);

    ret = GmcBeginCheck(g_stmtSync, vertexName, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新1000，其中100条 分区0
    TestSingleUpdate(0, 1000, g_recordCount, vertexName);
    ret = GmcEndCheck(g_stmtSync, vertexName, 0, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$QRY_AGE_TASK");

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(9100, count);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(9100, count);

    ret = GmcBeginCheck(g_stmtSync, vertexName, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新1000，其中100条 分区1
    TestSingleUpdate(1000, 2000, g_recordCount, vertexName, false);
    ret = GmcEndCheck(g_stmtSync, vertexName, 1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$QRY_AGE_TASK");

    count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(8200, count);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表对账完成重启，数据后台恢复
TEST_F(RsmLoadMode1Cluster, warmboot_012_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    TestPrepareEnvAndConn();
    const char *vertexName = "VertexCluster012";
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexName);
    ret = GmcBeginCheck(g_stmtSync, vertexName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUpdateVertexLabelByKey(g_stmtSync, "PK", 0, 5000, g_recordCount, vertexName);
    ret = GmcEndCheck(g_stmtSync, vertexName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$QRY_AGE_TASK");

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, count);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcBeginCheck(g_stmtSync, vertexName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUpdateVertexLabelByKey(g_stmtSync, "PK", 0, 3000, g_recordCount, vertexName);
    ret = GmcEndCheck(g_stmtSync, vertexName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$QRY_AGE_TASK");

    count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3000, count);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表触发缩容
TEST_F(RsmLoadMode1Cluster, warmboot_012_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    const char *vertexName = "VertexCluster012";
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexName);
    TestDeleteLabelByKey(g_stmtSync, "PK", 3000, g_recordCount, 0, vertexName);
    uint32_t scaleTimes = 0;
    uint32_t waitCount = 0;
    while ((scaleTimes < 1) && (waitCount < 120)) {
        scaleTimes = GetViewValueByField(
            "STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT -f LABEL_NAME=VertexCluster012", "SCALE_IN_ACTUAL_BEGIN_COUNT");
        sleep(1);
        waitCount++;
    };
    AW_MACRO_EXPECT_EQ_INT(scaleTimes, 1);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3000, count);
    TestDeleteLabelByKey(g_stmtSync, "PK", 1000, 3000, 0, vertexName);

    scaleTimes = 0;
    waitCount = 0;
    while ((scaleTimes < 1) && (waitCount < 120)) {
        scaleTimes = GetViewValueByField(
            "STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT -f LABEL_NAME=VertexCluster012", "SCALE_IN_ACTUAL_BEGIN_COUNT");
        sleep(1);
        waitCount++;
    };
    AW_MACRO_EXPECT_EQ_INT(scaleTimes, 1);

    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, count);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全量导表多张表重启，后台数据恢复
TEST_F(RsmLoadMode1Cluster, warmboot_012_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    TestPrepareEnvAndConn();
    const char *kvName = "kvTable012";
    TestSetKV(g_stmtSync, kvName, 0, g_recordCount, 0);

    const char *vertexName = "Warmboot012Vertex";
    TestSingleWrite(0, g_recordCount, 0, vertexName);

    const char *vertexClusterName = "VertexCluster012";
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexClusterName);
    TestCleanEnvAndConn();

    TestStartDbWarmReboot();
    TestPrepareEnvAndConn();
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmtSync, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, count);
    TestSetKV(g_stmtSync, kvName, 0, g_recordCount, g_recordCount);
    TestRemoveKV(g_stmtSync, kvName, 0, g_recordCount);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t countVertex = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexName, NULL, &countVertex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, countVertex);
    TestSingleUpdate(0, g_recordCount, g_recordCount, vertexName);
    TestSingleDelete(0, g_recordCount, vertexName);
    ret = GmcDropVertexLabel(g_stmtSync, vertexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t countCluster = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexClusterName, NULL, &countCluster);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, countCluster);
    TestUpdateVertexLabelByKey(g_stmtSync, "PK", 0, 1000, g_recordCount, vertexClusterName);
    TestScanLabelByKey(g_stmtSync, "PK", 0, 1000, g_recordCount, vertexClusterName);
    TestDeleteLabelByKey(g_stmtSync, "PK", 0, 1000, g_recordCount, vertexClusterName);

    TestUpdateVertexLabelByKey(g_stmtSync, "localhashkey", 1000, 2000, g_recordCount, vertexClusterName);
    TestScanLabelByKey(g_stmtSync, "localhashkey", 1000, 2000, g_recordCount, vertexClusterName);
    TestDeleteLabelByKey(g_stmtSync, "localhashkey", 1000, 2000, g_recordCount, vertexClusterName);

    TestUpdateVertexLabelByKey(g_stmtSync, "localkey", 2000, 3000, g_recordCount, vertexClusterName);
    TestScanLabelByKey(g_stmtSync, "localkey", 2000, 3000, g_recordCount, vertexClusterName);
    TestDeleteLabelByKey(g_stmtSync, "localkey", 2000, 3000, g_recordCount, vertexClusterName);

    TestReplaceVertexLabel(g_stmtSync, 3000, 4000, g_recordCount, vertexClusterName);
    TestRangeScanLabelByLocal(g_stmtSync, "localkey", 3000, 4000, g_recordCount, vertexClusterName);
    TestRangeDeleteLabelByLocal(g_stmtSync, "localkey", 3000, 4000, g_recordCount, vertexClusterName);

    TestUpdateVertexLabelByKey(g_stmtSync, "hashclusterkey", 4000, 5000, g_recordCount, vertexClusterName);
    TestScanLabelByKey(g_stmtSync, "hashclusterkey", 4000, 5000, g_recordCount, vertexClusterName);
    TestDeleteLabelByKey(g_stmtSync, "hashclusterkey", 4000, 5000, g_recordCount, vertexClusterName);

    TestScanLabelByLpm(g_stmtSync, "lpm4key", 5000, g_recordCount, 0, vertexClusterName);
    TestRangeScanLabelByLpm(g_stmtSync, "lpm4key", 5000, g_recordCount, 0, vertexClusterName);

    ret = GmcGetVertexCount(g_stmtSync, vertexClusterName, NULL, &countCluster);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, countCluster);
    ret = GmcDropVertexLabel(g_stmtSync, vertexClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}
