/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 留内存支持heap容器的DML
 Notes        : RsmSupportHeapTest_014 变长表批量删除数据过程，跳转行
                RsmSupportHeapTest_015 变长表批量删除数据过程，正常行
                RsmSupportHeapTest_016 定长表批量删除数据过程
 History      :
 Author       : 农黎宾 nWX860399
 Modification :
 Date         : 2024/7/16
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "RsmSupportHeap.h"
#include "PthreadFunc.h"

class RsmSupportHeapTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"isUseRsm=1\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=64\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
        system("sh $TEST_HOME/tools/start.sh");
        int32_t ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void RsmSupportHeapTest::SetUp()
{
    int ret = 0;

    ret = sem_init(&g_teseSem[0], 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = sem_init(&g_teseSem[1], 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, g_localNamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void RsmSupportHeapTest::TearDown()
{
    int32_t ret = 0;
    if (g_runCnt != g_actualRunCnt) {
        AW_FUN_Log(LOG_INFO, "g_runCnt: %u, but failed on %u\n", g_runCnt, g_actualRunCnt);
    }

    ret = GmcDropVertexLabel(g_stmt, g_variableTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_fixedTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = sem_destroy(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = sem_destroy(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 变长表批量删除数据过程，跳转行，服务端异常退出，数据可恢复, 其它表不能被影响
TEST_F(RsmSupportHeapTest, RsmSupportHeapTest_014)
{
    int32_t ret = 0;

    // 建表
    ret = testCreateLabel(g_stmt, "schema_file/variable_table.gmjson", g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testCreateLabel(g_stmt, "schema_file/fixed_table.gmjson", g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isSetBigString = true;
    uint32_t shortBigStringSize = 500;
    uint32_t longBigStringSize = 1000;
    for (uint32_t loop = 0; loop < g_runCnt; loop++) {
        g_actualRunCnt++;
        uint32_t variableTableWriteCnt = 1000;
        for (uint32_t i = 1 ; i <= variableTableWriteCnt; i++) {
            ret = testInsertDataToVariableTable(g_stmt, i, i, isSetBigString, shortBigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testUpdateDataToVariableTable(g_stmt, i, i, isSetBigString, longBigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        uint32_t fixedTableWriteCnt = 1000;
        for (uint32_t i = 1 ; i <= fixedTableWriteCnt; i++) {
            ret = testInsertDataToFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // kill服务和dml并发
        pthread_t pth1;
        ret = pthread_create(&pth1, NULL, PthreadKillDb, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        pthread_t pth2;
        uint32_t pkFieldVal = 1;
        ptharg_t pth2arg = { .pkFieldVal = pkFieldVal, .otherFieldVal = pkFieldVal,
            .dataNum = 500, .succCnt = 0, .isSetBigString = isSetBigString, .bigStringSize = longBigStringSize };
        ret = pthread_create(&pth2, NULL, PthreadBatchDeleteMultiDataToVariableTable, &pth2arg);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(pth1, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(pth2, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_INFO, "g_actualRunCnt: %u, pth2arg.succCnt: %u.", g_actualRunCnt, pth2arg.succCnt);

        // 断链
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();

        // WarmReboot重启
        testStartDbWarmReboot();
        ret = testWaitDbWarmRebootStartFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 重新建链，并建数据集和表
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, g_localNamespace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testCreateLabel(g_stmt, "schema_file/variable_table.gmjson", g_labelConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testCreateLabel(g_stmt, "schema_file/fixed_table.gmjson", g_labelConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 等待数据恢复
        uint32_t totalLabelCnt;
        uint32_t recoverLabelCnt;
        uint32_t recoverFinish;
        ret = testWaitDbWarmRebootRecoverDataFinish(&totalLabelCnt, &recoverLabelCnt, &recoverFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(2, totalLabelCnt);
        AW_MACRO_ASSERT_EQ_INT(2, recoverLabelCnt);
        AW_MACRO_ASSERT_EQ_INT(1, recoverFinish);

        // 获取记录数
        uint32_t variableTableRecordCnt = testGetRecordCnt(g_localNamespace, g_variableTable);
        ASSERT_GE(variableTableRecordCnt, variableTableWriteCnt - pth2arg.dataNum);
        ASSERT_LE(variableTableRecordCnt, variableTableWriteCnt);
        uint32_t fixedTableRecordCnt = testGetRecordCnt(g_localNamespace, g_fixedTable);
        AW_MACRO_ASSERT_EQ_INT(fixedTableWriteCnt, fixedTableRecordCnt);

        // 删除失败有可能会在数据恢复阶段恢复或者不恢复
        // 做了操作的表，数据恢复后，操作旧的数据
        uint32_t newVal = 10000;
        for (uint32_t i = 1; i <= variableTableWriteCnt; i++) {
            // 校验恢复后的数据
            if (i <= variableTableWriteCnt - variableTableRecordCnt) {
                ret = testGetAndCheckDataByVariableTable(g_stmt, i, i, isSetBigString, longBigStringSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_NO_DATA, ret);
            } else {
                ret = testGetAndCheckDataByVariableTable(g_stmt, i, i, isSetBigString, longBigStringSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 更新恢复后的数据
                newVal = 10000;
                ret = testUpdateDataToVariableTable(g_stmt, i, newVal, isSetBigString, longBigStringSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 校验更新后的数据
                ret = testGetAndCheckDataByVariableTable(g_stmt, i, newVal, isSetBigString, longBigStringSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 删除数据
                ret = testDelDataByVariableTable(g_stmt, i);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
        }

       // 做了操作的表，数据恢复后，操作新的数据
        uint32_t InsertPkValStart = 1;
        uint32_t InsertPkValEnd = InsertPkValStart + 1000;
        for (uint32_t i = InsertPkValStart; i <= InsertPkValEnd; i++) {
            ret = testInsertDataToVariableTable(g_stmt, i, i, isSetBigString, longBigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByVariableTable(g_stmt, i, i, isSetBigString, longBigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToVariableTable(g_stmt, i, newVal, isSetBigString, longBigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByVariableTable(g_stmt, i, newVal, isSetBigString, longBigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testDelDataByVariableTable(g_stmt, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 没做操作的表，数据恢复后，操作旧的数据
        for (uint32_t i = 1 ; i <= fixedTableWriteCnt; i++) {
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 没做操作的表，数据恢复后，操作新的数据
        InsertPkValStart = fixedTableWriteCnt + 1;
        InsertPkValEnd = InsertPkValStart + 1000;
        for (uint32_t i = InsertPkValStart ; i <= InsertPkValEnd; i++) {
            ret = testInsertDataToFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

         // 没做操作的表，删除所有数据
        for (uint32_t i = 1 ; i <= InsertPkValEnd; i++) {
            newVal = 10000;
            ret = testDelDataByFixedTable(g_stmt, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// 变长表批量删除数据过程，正常行，服务端异常退出，数据可恢复, 其它表不能被影响
TEST_F(RsmSupportHeapTest, RsmSupportHeapTest_015)
{
    int32_t ret = 0;

    // 建表
    ret = testCreateLabel(g_stmt, "schema_file/variable_table.gmjson", g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testCreateLabel(g_stmt, "schema_file/fixed_table.gmjson", g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isSetBigString = true;
    uint32_t bigStringSize = 500;
    for (uint32_t loop = 0; loop < g_runCnt; loop++) {
        g_actualRunCnt++;
        uint32_t variableTableWriteCnt = 1000;
        for (uint32_t i = 1 ; i <= variableTableWriteCnt; i++) {
            ret = testInsertDataToVariableTable(g_stmt, i, i, isSetBigString, bigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        uint32_t fixedTableWriteCnt = 1000;
        for (uint32_t i = 1 ; i <= fixedTableWriteCnt; i++) {
            ret = testInsertDataToFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // kill服务和dml并发
        pthread_t pth1;
        ret = pthread_create(&pth1, NULL, PthreadKillDb, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        pthread_t pth2;
        uint32_t pkFieldVal = 1;
        ptharg_t pth2arg = { .pkFieldVal = pkFieldVal, .otherFieldVal = pkFieldVal,
            .dataNum = 500, .succCnt = 0, .isSetBigString = isSetBigString, .bigStringSize = bigStringSize };
        ret = pthread_create(&pth2, NULL, PthreadDeleteMultiDataToVariableTable, &pth2arg);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(pth1, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(pth2, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_INFO, "g_actualRunCnt: %u, pth2arg.succCnt: %u.", g_actualRunCnt, pth2arg.succCnt);

        // 断链
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();

        // WarmReboot重启
        testStartDbWarmReboot();
        ret = testWaitDbWarmRebootStartFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 重新建链，并建数据集和表
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, g_localNamespace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testCreateLabel(g_stmt, "schema_file/variable_table.gmjson", g_labelConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testCreateLabel(g_stmt, "schema_file/fixed_table.gmjson", g_labelConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 等待数据恢复
        uint32_t totalLabelCnt;
        uint32_t recoverLabelCnt;
        uint32_t recoverFinish;
        ret = testWaitDbWarmRebootRecoverDataFinish(&totalLabelCnt, &recoverLabelCnt, &recoverFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(2, totalLabelCnt);
        AW_MACRO_ASSERT_EQ_INT(2, recoverLabelCnt);
        AW_MACRO_ASSERT_EQ_INT(1, recoverFinish);

        // 获取记录数
        uint32_t variableTableRecordCnt = testGetRecordCnt(g_localNamespace, g_variableTable);
        ASSERT_GE(variableTableRecordCnt, variableTableWriteCnt - pth2arg.dataNum);
        ASSERT_LE(variableTableRecordCnt, variableTableWriteCnt);
        uint32_t fixedTableRecordCnt = testGetRecordCnt(g_localNamespace, g_fixedTable);
        AW_MACRO_ASSERT_EQ_INT(fixedTableWriteCnt, fixedTableRecordCnt);

        // 删除失败有可能会在数据恢复阶段恢复或者不恢复
        // 做了操作的表，数据恢复后，操作旧的数据
        uint32_t newVal = 10000;
        for (uint32_t i = 1; i <= variableTableWriteCnt; i++) {
            // 校验恢复后的数据
            if (i <= variableTableWriteCnt - variableTableRecordCnt) {
                ret = testGetAndCheckDataByVariableTable(g_stmt, i, i, isSetBigString, bigStringSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_NO_DATA, ret);
            } else {
                ret = testGetAndCheckDataByVariableTable(g_stmt, i, i, isSetBigString, bigStringSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 更新恢复后的数据
                newVal = 10000;
                ret = testUpdateDataToVariableTable(g_stmt, i, newVal, isSetBigString, bigStringSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 校验更新后的数据
                ret = testGetAndCheckDataByVariableTable(g_stmt, i, newVal, isSetBigString, bigStringSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 删除数据
                ret = testDelDataByVariableTable(g_stmt, i);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
        }

        // 做了操作的表，数据恢复后，操作新的数据
        uint32_t InsertPkValStart = 1;
        uint32_t InsertPkValEnd = InsertPkValStart + 1000;
        for (uint32_t i = InsertPkValStart; i <= InsertPkValEnd; i++) {
            ret = testInsertDataToVariableTable(g_stmt, i, i, isSetBigString, bigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByVariableTable(g_stmt, i, i, isSetBigString, bigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToVariableTable(g_stmt, i, newVal, isSetBigString, bigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByVariableTable(g_stmt, i, newVal, isSetBigString, bigStringSize);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testDelDataByVariableTable(g_stmt, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 没做操作的表，数据恢复后，操作旧的数据
        for (uint32_t i = 1 ; i <= fixedTableWriteCnt; i++) {
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 没做操作的表，数据恢复后，操作新的数据
        InsertPkValStart = fixedTableWriteCnt + 1;
        InsertPkValEnd = InsertPkValStart + 1000;
        for (uint32_t i = InsertPkValStart ; i <= InsertPkValEnd; i++) {
            ret = testInsertDataToFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 没做操作的表，删除所有数据
        for (uint32_t i = 1 ; i <= InsertPkValEnd; i++) {
            newVal = 10000;
            ret = testDelDataByFixedTable(g_stmt, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// 定长表批量删除数据过程，服务端异常退出，数据可恢复, 其它表不能被影响
TEST_F(RsmSupportHeapTest, RsmSupportHeapTest_016)
{
    int32_t ret = 0;

    // 建表
    ret = testCreateLabel(g_stmt, "schema_file/variable_table.gmjson", g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testCreateLabel(g_stmt, "schema_file/fixed_table.gmjson", g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (uint32_t loop = 0; loop < g_runCnt; loop++) {
        g_actualRunCnt++;
        uint32_t variableTableWriteCnt = 1000;
        for (uint32_t i = 1 ; i <= variableTableWriteCnt; i++) {
            ret = testInsertDataToVariableTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        uint32_t fixedTableWriteCnt = 1000;
        for (uint32_t i = 1 ; i <= fixedTableWriteCnt; i++) {
            ret = testInsertDataToFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // kill服务和dml并发
        pthread_t pth1;
        ret = pthread_create(&pth1, NULL, PthreadKillDb, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        pthread_t pth2;
        uint32_t pkFieldVal = 1;
        ptharg_t pth2arg = { .pkFieldVal = pkFieldVal, .otherFieldVal = pkFieldVal,
            .dataNum = 500, .succCnt = 0, .isSetBigString = false };
        ret = pthread_create(&pth2, NULL, PthreadDeleteMultiDataToFixedTable, &pth2arg);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(pth1, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(pth2, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_INFO, "g_actualRunCnt: %u, pth2arg.succCnt: %u.", g_actualRunCnt, pth2arg.succCnt);

        // 断链
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();

        // WarmReboot重启
        testStartDbWarmReboot();
        ret = testWaitDbWarmRebootStartFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 重新建链，并建数据集和表
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, g_localNamespace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testCreateLabel(g_stmt, "schema_file/variable_table.gmjson", g_labelConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testCreateLabel(g_stmt, "schema_file/fixed_table.gmjson", g_labelConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 等待数据恢复
        uint32_t totalLabelCnt;
        uint32_t recoverLabelCnt;
        uint32_t recoverFinish;
        ret = testWaitDbWarmRebootRecoverDataFinish(&totalLabelCnt, &recoverLabelCnt, &recoverFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(2, totalLabelCnt);
        AW_MACRO_ASSERT_EQ_INT(2, recoverLabelCnt);
        AW_MACRO_ASSERT_EQ_INT(1, recoverFinish);

        // 获取记录数
        uint32_t variableTableRecordCnt = testGetRecordCnt(g_localNamespace, g_variableTable);
        AW_MACRO_ASSERT_EQ_INT(variableTableWriteCnt, variableTableRecordCnt);
        uint32_t fixedTableRecordCnt = testGetRecordCnt(g_localNamespace, g_fixedTable);
        ASSERT_GE(fixedTableRecordCnt, fixedTableWriteCnt - pth2arg.dataNum);
        ASSERT_LE(fixedTableRecordCnt, fixedTableWriteCnt);

        // 删除失败有可能会在数据恢复阶段恢复或者不恢复
        // 做了操作的表，数据恢复后，操作旧的数据
        uint32_t newVal = 10000;
        uint32_t deleteFailedPkVal = pkFieldVal + pth2arg.succCnt;
        for (uint32_t i = 1; i <= fixedTableWriteCnt; i++) {
            // 校验恢复后的数据
            if (i <= fixedTableWriteCnt - fixedTableRecordCnt) {
                ret = testGetAndCheckDataByFixedTable(g_stmt, i, i);
                AW_MACRO_ASSERT_EQ_INT(GMERR_NO_DATA, ret);
            } else {
                ret = testGetAndCheckDataByFixedTable(g_stmt, i, i);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 更新恢复后的数据
                newVal = 10000;
                ret = testUpdateDataToFixedTable(g_stmt, i, newVal);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 校验更新后的数据
                ret = testGetAndCheckDataByFixedTable(g_stmt, i, newVal);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                // 删除数据
                ret = testDelDataByFixedTable(g_stmt, i);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
        }

        // 做了操作的表，数据恢复后，操作新的数据
        uint32_t InsertPkValStart = 1;
        uint32_t InsertPkValEnd = InsertPkValStart + 1000;
        for (uint32_t i = InsertPkValStart; i <= InsertPkValEnd; i++) {
            ret = testInsertDataToFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByFixedTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testDelDataByFixedTable(g_stmt, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 没做操作的表，数据恢复后，操作旧的数据
        for (uint32_t i = 1 ; i <= variableTableWriteCnt; i++) {
            ret = testGetAndCheckDataByVariableTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToVariableTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByVariableTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 没做操作的表，数据恢复后，操作新的数据
        InsertPkValStart = variableTableWriteCnt + 1;
        InsertPkValEnd = InsertPkValStart + 1000;
        for (uint32_t i = InsertPkValStart ; i <= InsertPkValEnd; i++) {
            ret = testInsertDataToVariableTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByVariableTable(g_stmt, i, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            newVal = 10000;
            ret = testUpdateDataToVariableTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = testGetAndCheckDataByVariableTable(g_stmt, i, newVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 没做操作的表，删除所有数据
        for (uint32_t i = 1 ; i <= InsertPkValEnd; i++) {
            ret = testDelDataByVariableTable(g_stmt, i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
}
