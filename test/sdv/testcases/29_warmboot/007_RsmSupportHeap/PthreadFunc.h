/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 留内存支持heap容器的DML
 Notes        : 头文件
 History      :
 Author       : 农黎宾 nWX860399
 Modification :
 Date         : 2024/7/16
*****************************************************************************/

#ifndef PTHREAD_FUNC_H
#define PTHREAD_FUNC_H

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "RsmSupportHeap.h"

typedef struct tagptharg_t {
    uint32_t pkFieldVal;
    uint32_t otherFieldVal;
    uint32_t dataNum;
    uint32_t succCnt;
    bool isSetBigString;
    uint32_t bigStringSize;
} ptharg_t;

sem_t g_teseSem[10];
void *PthreadKillDb(void *arg)
{
    int32_t ret;
    // 保证kill服务线程先进来
    ret = sem_post(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待其他操作线程准备好调用函数
    ret = sem_wait(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("ps -ef | grep -v grep | grep gmserver | awk '{ print $2 }' | xargs kill -9");
    pthread_exit(NULL);
}

void *PthreadInsertDataToVariableTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T0 = NULL;
    ret = GmcGetRootNode(stmt, &T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetPkFieldVal(T0, ptharg->pkFieldVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetT0NodeFieldVal(T0, ptharg->otherFieldVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1 = NULL;
    ret = GmcNodeGetChild(T0, "T1", &T1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetT1NodeFieldVal(stmt, T1, ptharg->otherFieldVal, ptharg->isSetBigString, ptharg->bigStringSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t execRet = GmcExecute(stmt);
    if (execRet != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, execRet);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadInsertDataFailedToVariableTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T0 = NULL;
    ret = GmcGetRootNode(stmt, &T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetPkFieldVal(T0, ptharg->pkFieldVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetT0NodeFieldVal(T0, ptharg->otherFieldVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1 = NULL;
    ret = GmcNodeGetChild(T0, "T1", &T1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetT1NodeFieldVal(stmt, T1, ptharg->otherFieldVal, ptharg->isSetBigString, ptharg->bigStringSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t execRet = GmcExecute(stmt);
    if (execRet != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_BOOL(true, execRet == GMERR_CONNECTION_RESET_BY_PEER ||
            execRet == GMERR_PRIMARY_KEY_VIOLATION);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadInsertMultiDataToVariableTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T0", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *T0 = NULL;
        ret = GmcGetRootNode(stmt[i], &T0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetPkFieldVal(T0, ptharg->pkFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT0NodeFieldVal(T0, ptharg->otherFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *T1 = NULL;
        ret = GmcNodeGetChild(T0, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT1NodeFieldVal(stmt[i], T1, ptharg->otherFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        int32_t execRet = GmcExecute(stmt[i]);
        if (execRet != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, execRet);
        } else {
            succCnt++;
        }
    }
    ptharg->succCnt = succCnt;

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadUpdateMultiDataToVariableTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T0", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *T0 = NULL;
        ret = GmcGetRootNode(stmt[i], &T0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT0NodeFieldVal(T0, ptharg->otherFieldVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *T1 = NULL;
        ret = GmcNodeGetChild(T0, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT1NodeFieldVal(stmt[i], T1, ptharg->otherFieldVal, ptharg->isSetBigString, ptharg->bigStringSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t pkFieldVal = ptharg->pkFieldVal + i;
        ret = GmcSetIndexKeyValue(stmt[i], 0, GMC_DATATYPE_UINT32, &pkFieldVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt[i], "T0.pk");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        int32_t execRet = GmcExecute(stmt[i]);
        if (execRet != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, execRet);
        } else {
            succCnt++;
        }
    }
    ptharg->succCnt = succCnt;

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadDeleteMultiDataToVariableTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T0", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t pkFieldVal = ptharg->pkFieldVal + i;
        ret = GmcSetIndexKeyValue(stmt[i], 0, GMC_DATATYPE_UINT32, &pkFieldVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt[i], "T0.pk");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        int32_t execRet = GmcExecute(stmt[i]);
        if (execRet != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, execRet);
        } else {
            succCnt++;
        }
    }
    ptharg->succCnt = succCnt;

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadBatchDeleteMultiDataToVariableTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(conn, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T0", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t pkFieldVal = ptharg->pkFieldVal + i;
        ret = GmcSetIndexKeyValue(stmt[i], 0, GMC_DATATYPE_UINT32, &pkFieldVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt[i], "T0.pk");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    int32_t batchexecRet = GmcBatchExecute(batch, &batchRet);
    if (batchexecRet != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, batchexecRet);
    } else {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        succCnt = succCnt + successNum;
    }
    ptharg->succCnt = succCnt;
    GmcBatchDestroy(batch);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadBatchInsertMultiDataToVariableTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(conn, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T0", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *T0 = NULL;
        ret = GmcGetRootNode(stmt[i], &T0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetPkFieldVal(T0, ptharg->pkFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT0NodeFieldVal(T0, ptharg->otherFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *T1 = NULL;
        ret = GmcNodeGetChild(T0, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT1NodeFieldVal(stmt[i], T1, ptharg->otherFieldVal + i, ptharg->isSetBigString,
            ptharg->bigStringSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    int32_t batchexecRet = GmcBatchExecute(batch, &batchRet);
    if (batchexecRet != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, batchexecRet);
    } else {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        succCnt = succCnt + successNum;
    }
    ptharg->succCnt = succCnt;
    GmcBatchDestroy(batch);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadInsertDataToFixedTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2 = NULL;
    ret = GmcGetRootNode(stmt, &T2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetPkFieldVal(T2, ptharg->pkFieldVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetT2NodeFieldVal(T2, ptharg->otherFieldVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t execRet = GmcExecute(stmt);
    if (execRet != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, execRet);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadInsertDataFailedToFixedTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2 = NULL;
    ret = GmcGetRootNode(stmt, &T2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetPkFieldVal(T2, ptharg->pkFieldVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetT2NodeFieldVal(T2, ptharg->otherFieldVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t execRet = GmcExecute(stmt);
    if (execRet != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_BOOL(true, execRet == GMERR_CONNECTION_RESET_BY_PEER ||
            execRet == GMERR_PRIMARY_KEY_VIOLATION);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadInsertMultiDataToFixedTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *T2 = NULL;
        ret = GmcGetRootNode(stmt[i], &T2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetPkFieldVal(T2, ptharg->pkFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT2NodeFieldVal(T2, ptharg->otherFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        int32_t execRet = GmcExecute(stmt[i]);
        if (execRet != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, execRet);
        } else {
            succCnt++;
        }
    }
    ptharg->succCnt = succCnt;

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadUpdateMultiDataToFixedTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T2", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *T2 = NULL;
        ret = GmcGetRootNode(stmt[i], &T2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT2NodeFieldVal(T2, ptharg->otherFieldVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t pkFieldVal = ptharg->pkFieldVal + i;
        ret = GmcSetIndexKeyValue(stmt[i], 0, GMC_DATATYPE_UINT32, &pkFieldVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt[i], "T2.pk");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        int32_t execRet = GmcExecute(stmt[i]);
        if (execRet != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, execRet);
        } else {
            succCnt++;
        }
    }
    ptharg->succCnt = succCnt;

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadDeleteMultiDataToFixedTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T2", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t pkFieldVal = ptharg->pkFieldVal + i;
        ret = GmcSetIndexKeyValue(stmt[i], 0, GMC_DATATYPE_UINT32, &pkFieldVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt[i], "T2.pk");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        int32_t execRet = GmcExecute(stmt[i]);
        if (execRet != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, execRet);
        } else {
            succCnt++;
        }
    }
    ptharg->succCnt = succCnt;

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadBatchDeleteMultiDataToFixedTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(conn, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T2", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t pkFieldVal = ptharg->pkFieldVal + i;
        ret = GmcSetIndexKeyValue(stmt[i], 0, GMC_DATATYPE_UINT32, &pkFieldVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt[i], "T2.pk");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    int32_t batchexecRet = GmcBatchExecute(batch, &batchRet);
    if (batchexecRet != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, batchexecRet);
    } else {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        succCnt = succCnt + successNum;
    }
    ptharg->succCnt = succCnt;
    GmcBatchDestroy(batch);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

void *PthreadBatchInsertMultiDataToFixedTable(void *arg)
{
    int32_t ret;

    ptharg_t *ptharg = (ptharg_t *)arg;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt[ptharg->dataNum];
    ret = testGmcConnect(&conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(conn, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt[i], "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *T2 = NULL;
        ret = GmcGetRootNode(stmt[i], &T2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetPkFieldVal(T2, ptharg->pkFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetT2NodeFieldVal(T2, ptharg->otherFieldVal + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 保证kill服务线程先进去，而不是操作操作线程自己先执行完了
    ret = sem_post(&g_teseSem[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 下执行操作，同时kill服务线程往下走，进行kill服务
    ret = sem_wait(&g_teseSem[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t succCnt = 0;
    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    int32_t batchexecRet = GmcBatchExecute(batch, &batchRet);
    if (batchexecRet != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, batchexecRet);
    } else {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        succCnt = succCnt + successNum;
    }
    ptharg->succCnt = succCnt;
    GmcBatchDestroy(batch);

    for (uint32_t i = 0; i < ptharg->dataNum; i++) {
        GmcFreeStmt(stmt[i]);
    }
    ret = testGmcDisconnect(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_exit(NULL);
}

#endif
