/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :保留内存支持老化对账
 Author       : youwanyong wx1157510
 Modification :
 Date         : 2024/07/17
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include "gtest/gtest.h"
#include "AgeAndRelege.h"

int end_num = 100;
const char *g_subConnName = "subConnName";

char cmd[512];
#define FULLTABLE 0xff

class GeneralTable : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GeneralTable::SetUpTestCase()
{}

void GeneralTable::TearDownTestCase()
{}

void GeneralTable::SetUp()
{
    // 建连
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
}

void GeneralTable::TearDown()
{
    AW_CHECK_LOG_END();
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
 Description  : 019.前8个分区，开启对账，写数据插入100条数据，服务重启，预期数据写入成功，预置数据未老化
 ok
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_019)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 100, 200, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(200, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 200;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 020.前8个分区，开启对账，写数据插入100条数据，结束对账，服务重启，预期数据写入成功，预置数据老化
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_020)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 100, 200, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(148, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 148;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :021.前8个分区，开启对账，写数据插入100条数据，异常结束对账，服务重启，预期数据写入成功，预置数据未老化
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_021)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 100, 200, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, true);
        ;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(200, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 200;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 022.前8个分区，开启对账，对预置数据进行部分update更新，服务重启，更新成功，预置数据未老化
 ok
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_022)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 50, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 100;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :
023.前8个分区，开启对账，，对预置数据进行部分update更新，结束对账，服务重启，更新成功，未更新数据老化成功
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_023)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 50, 150, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(74, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 74;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 13");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :024.前8个分区，开启对账，，对预置数据进行部分update更新，异常结束对账，服务重启，更新成功，预置数据未老化
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_024)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 50, 150, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, true);
        ;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 100;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 025.前8个分区，开启对账，对预置数据进行部分merge更新，服务重启，更新成功，预置数据未老化
 ok
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_025)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 50, 100, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true, true);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 100;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 026.前8个分区，开启对账，，对预置数据进行部分merge更新，结束对账，服务重启，更新成功，未更新数据老化成功
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_026)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 50, 150, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(124, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 124;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :027.前8个分区，开启对账，，对预置数据进行部分merge更新，异常结束对账，服务重启，更新成功，预置数据未老化
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_027)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 50, 150, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, true);
        ;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(150, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 150;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 028.前8个分区，开启对账，，对预置数据进行部分replace更新，服务重启，更新成功，预置数据未老化
 ok
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_028)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 50, 100, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 100;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :
029.前8个分区，开启对账，，对预置数据进行部分replace更新，结束对账，服务重启，更新成功，未更新数据老化成功
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_029)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 50, 150, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(124, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 124;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :030.前8个分区，开启对账，对预置数据进行部分replace更新，异常结束对账，服务重启，更新成功，预置数据未老化
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_030)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 50, 150, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, true);
        ;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(150, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 150;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 031.前8个分区，开启对账，对预置数据进行部分delete删除，服务重启，删除成功，预置数据未老化
 ok
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_031)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // delete 50 data
    TestGeneralDelete(g_stmt, labelName, 50, 100, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(50, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 50;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 032.前8个分区，开启对账，，对预置数据进行部分delete删除，结束对账，服务重启，删除成功，预置数据老化
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_032)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // delete 50 data
    TestGeneralDelete(g_stmt, labelName, 50, 150, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(24, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 24;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :033.前8个分区，开启对账，，对预置数据进行部分delete删除，异常结束对账，服务重启，删除成功，预置数据未老化
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_033)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // delete 100 data
    TestGeneralDelete(g_stmt, labelName, 50, 150, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, true);
        ;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(50, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 50;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :
034.前8个分区，预置100条数据，开启对账，插入20条新数据，update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，replace更新20条旧数据，5条新数据。删除20条旧数据，服务重启，预期预置数据不老化DML成功
 ok
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_034)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);
    // 插入20条新数据，replace更新20条旧数据，5条新数据
    // update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，
    // 删除20条旧数据

    // 插入20条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 20, (char *)"string", schemaVersion,
        GMC_OPERATION_INSERT, true, true);

    // replace更新20条旧数据，5条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 25, (char *)"string", schemaVersion,
        GMC_OPERATION_REPLACE, true, true);
    // update更新20条旧数据，10条新数据
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 80, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 125, 135, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    // merge更新20条旧数据，5条新数据，
    TestGeneralT1UpdateOrMerge(g_stmt, labelName, startValue + 105, endValue + 30, (char *)"string", schemaVersion,
        GMC_OPERATION_MERGE, true, true);
    // 删除20条旧数据
    TestGeneralDelete(g_stmt, labelName, 20, 40, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(110, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 110;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :
035.前8个分区，预置100条数据，开启对账，插入20条新数据，update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，replace更新20条旧数据，5条新数据。删除20条旧数据，结束对账，服务重启，预期预置数据老化，DML成功
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_035)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 插入20条新数据，replace更新20条旧数据，5条新数据
    // update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，
    // 删除20条旧数据

    // 插入20条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 20, (char *)"string", schemaVersion,
        GMC_OPERATION_INSERT, true, true);

    // replace更新20条旧数据，5条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 25, (char *)"string", schemaVersion,
        GMC_OPERATION_REPLACE, true, true);
    // update更新20条旧数据，10条新数据
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 80, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 125, 135, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    // merge更新20条旧数据，5条新数据，
    TestGeneralT1UpdateOrMerge(g_stmt, labelName, startValue + 105, endValue + 30, (char *)"string", schemaVersion,
        GMC_OPERATION_MERGE, true, true);
    // 删除20条旧数据
    TestGeneralDelete(g_stmt, labelName, 20, 40, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(82, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 82;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description
:036.前8个分区，预置100条数据，开启对账，插入20条新数据，update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，replace更新20条旧数据，5条新数据。删除20条旧数据，异常结束对账，服务重启，预期预置数据不老化，DML成功
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_036)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 8;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 插入20条新数据，replace更新20条旧数据，5条新数据
    // update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，
    // 删除20条旧数据

    // 插入20条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 20, (char *)"string", schemaVersion,
        GMC_OPERATION_INSERT, true, true);

    // replace更新20条旧数据，5条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 25, (char *)"string", schemaVersion,
        GMC_OPERATION_REPLACE, true, true);
    // update更新20条旧数据，10条新数据
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 80, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 125, 135, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    // merge更新20条旧数据，5条新数据，
    TestGeneralT1UpdateOrMerge(g_stmt, labelName, startValue + 105, endValue + 30, (char *)"string", schemaVersion,
        GMC_OPERATION_MERGE, true, true);
    // 删除20条旧数据
    TestGeneralDelete(g_stmt, labelName, 20, 40, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, true);
        ;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(110, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 110;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :
037.预置100条数据，开启对账对所有分区进行对账，插入20条新数据，update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，replace更新20条旧数据，5条新数据。删除20条旧数据，结束对账，服务重启，预期预置数据老化，DML成功
 ok
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_037)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 16;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);
    // 插入20条新数据，replace更新20条旧数据，5条新数据
    // update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，
    // 删除20条旧数据

    // 插入20条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 20, (char *)"string", schemaVersion,
        GMC_OPERATION_INSERT, true, true);

    // replace更新20条旧数据，5条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 25, (char *)"string", schemaVersion,
        GMC_OPERATION_REPLACE, true, true);
    // update更新20条旧数据，10条新数据
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 80, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 125, 135, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    // merge更新20条旧数据，5条新数据，
    TestGeneralT1UpdateOrMerge(g_stmt, labelName, startValue + 105, endValue + 30, (char *)"string", schemaVersion,
        GMC_OPERATION_MERGE, true, true);
    // 删除20条旧数据
    TestGeneralDelete(g_stmt, labelName, 20, 40, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(110, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 110;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :
038.预置100条数据，开启对账对所有分区进行对账，插入20条新数据，update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，replace更新20条旧数据，5条新数据。删除20条旧数据，服务重启，预期预置数据不老化DML成功
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_038)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 16;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 插入20条新数据，replace更新20条旧数据，5条新数据
    // update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，
    // 删除20条旧数据

    // 插入20条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 20, (char *)"string", schemaVersion,
        GMC_OPERATION_INSERT, true, true);

    // replace更新20条旧数据，5条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 25, (char *)"string", schemaVersion,
        GMC_OPERATION_REPLACE, true, true);
    // update更新20条旧数据，10条新数据
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 80, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 125, 135, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    // merge更新20条旧数据，5条新数据，
    TestGeneralT1UpdateOrMerge(g_stmt, labelName, startValue + 105, endValue + 30, (char *)"string", schemaVersion,
        GMC_OPERATION_MERGE, true, true);
    // 删除20条旧数据
    TestGeneralDelete(g_stmt, labelName, 20, 40, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(50, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 50;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description
:039.预置100条数据，开启对账对所有分区进行对账，插入20条新数据，update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，replace更新20条旧数据，5条新数据。删除20条旧数据，异常结束对账，服务重启，预期预置数据不老化，DML成功
**************************************************************************** */
TEST_F(GeneralTable, warmboot_003_039)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账

    AW_FUN_Log(LOG_STEP, "开启对账");
    uint8_t par_count = 16;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(g_stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 插入20条新数据，replace更新20条旧数据，5条新数据
    // update更新20条旧数据，10条新数据，merge更新20条旧数据，5条新数据，
    // 删除20条旧数据

    // 插入20条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 20, (char *)"string", schemaVersion,
        GMC_OPERATION_INSERT, true, true);

    // replace更新20条旧数据，5条新数据
    TestGeneralT1InsertOrReplace(g_stmt, labelName, startValue + 100, endValue + 25, (char *)"string", schemaVersion,
        GMC_OPERATION_REPLACE, true, true);
    // update更新20条旧数据，10条新数据
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 80, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 125, 135, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);

    // merge更新20条旧数据，5条新数据，
    TestGeneralT1UpdateOrMerge(g_stmt, labelName, startValue + 105, endValue + 30, (char *)"string", schemaVersion,
        GMC_OPERATION_MERGE, true, true);
    // 删除20条旧数据
    TestGeneralDelete(g_stmt, labelName, 20, 40, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(g_stmt, labelName, partition, true);
        ;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(110, vertexCount);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot(GMCONFIG_PATH);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    system("gmsysview -q V\\$QRY_AGE_TASK");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan
    int32_t record = 110;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 200");
    // queryOldStatusView

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (user_data) {
        testSnFreeUserData(user_data);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_end");
}
