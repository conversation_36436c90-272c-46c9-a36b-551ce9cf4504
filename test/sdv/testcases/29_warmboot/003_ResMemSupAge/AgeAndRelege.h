/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#ifndef AGEANDRELEGE_H
#define AGEANDRELEGE_H

#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
#define MAX_NAME_LENGTH 128
char g_configJson[128] = "{\"max_record_count\" : 1000000, \"is_support_reserved_memory\":true}";
bool isAgeBegin = false;
int g_subIndex = 0, g_maxRecordCount = 100000;
SnUserDataT *user_data;
GmcStmtT *g_stmt_sub = NULL;
int g_chanRingLen = 256;
#define RECORDCOUNTSTART 0
#define RECORDCOUNTEND 100
#define GMCONFIG_PATH "/usr/local/file/gmserver.ini"

int TestUpdateVertexLabel(char *schemaPath, char *expectValue, const char *labelName = NULL,
    char *uWay = (char *)"online", char *nsName = g_testNameSpace)
{
    char *schema = NULL;
    readJanssonFile(schemaPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    free(schema);
    // gmddl工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    if (labelName) {
        // 对已存在表进行升级
        (void)snprintf(
            cmd, 512, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath, labelName, schemaPath, uWay, nsName);
    } else {
        // 使用表文件进行升级
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -f %s -u %s -ns %s", g_toolPath, schemaPath, uWay, nsName);
    }
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

// 查询老化视图
void queryOldStatusView(
    const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL, const char *v4 = NULL, const char *v5 = NULL)
{
    AW_FUN_Log(LOG_STEP, "query OldStatusView");
    sleep(1);
    char *viewName1 = (char *)"V\\$QRY_AGE_TASK";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q %s", viewName1);
    int ret = executeCommand(cmd, v1, v2, v3, v4);
    ASSERT_EQ(GMERR_OK, ret);
}

void queryOldInfoView(
    const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL, const char *v4 = NULL, const char *v5 = NULL)
{
    AW_FUN_Log(LOG_STEP, "query OldInfoView");
    sleep(1);
    char *viewName1 = (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO";
    char cmd[MAX_CMD_SIZE] = {0};
    memset(cmd, 0, sizeof(cmd));
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q %s -f VERTEX_LABEL_NAME=%s", viewName1, v1);
    int ret = executeCommand(cmd, v1, v2, v3, v4);
    ASSERT_EQ(GMERR_OK, ret);
}

// 查询表的版本号
void querySchemaVersionView(
    const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL, const char *v4 = NULL, const char *v5 = NULL)
{
    AW_FUN_Log(LOG_STEP, "query SchemaVersionView");
    sleep(1);
    char *viewName1 = (char *)"V\\$CATA_VERTEX_LABEL_INFO";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q %s -f VERTEX_LABEL_NAME=%s", viewName1, v1);
    int ret = executeCommand(cmd, v1, v2, v3, v4);
    ASSERT_EQ(GMERR_OK, ret);
}

int TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}
/*
  General table
*/

// vectoryT1
void TestGeneralT1ldVersionSetCommonProperty_T1_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f2Value[12] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_STRING, f2Value, strlen(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f3Value[12] = "fixed";
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_FIXED, f3Value, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f4Value[10] = "bytes";
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_BYTES, f4Value, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetCommonProperty_T2_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f4Value[12] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_STRING, f4Value, strlen(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f5Value[12] = "fixed";
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_FIXED, &f5Value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// // version 2 新增字段
void TestGeneralT1SetAddBigObject_Root(GmcNodeT *node, char bigstring)
{
    int ret = 0;
    char string13k[1024 * 13] = {0};
    memset(string13k, bigstring, sizeof(string13k));
    string13k[1024 * 13 - 1] = '\0';
    ret = GmcNodeSetPropertyByName(node, (char *)"F23", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F24", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F25", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F26", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F27", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F28", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F29", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F30", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F31", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F32", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F33", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F34", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F35", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F36", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F37", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F38", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F39", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F40", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F41", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F42", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F43", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F44", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F45", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F46", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F47", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F48", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F49", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F50", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F51", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F52", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F53", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F54", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F55", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F56", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F57", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F58", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F59", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F60", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F61", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F62", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F63", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F64", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F65", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F66", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F67", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F68", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F69", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F70", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F71", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F72", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F73", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F74", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F75", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F76", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F77", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F78", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F79", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F80", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F81", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F82", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F83", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F84", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F85", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F86", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F87", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F88", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F89", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F90", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F91", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F92", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F93", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F94", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F95", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F96", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F97", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F98", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F99", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F100", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// version 2 新增字段
void TestGeneralT1ldVersionSetAllFieldsProperty_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;

    int64_t f14Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_INT64, &f14Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f15Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_UINT64, &f15Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f16Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_INT32, &f16Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f17Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_UINT32, &f17Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t f18Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_INT16, &f18Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f19Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_UINT16, &f19Value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t f20Value = i % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F20", GMC_DATATYPE_INT8, &f20Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f21Value = i % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F21", GMC_DATATYPE_UINT8, &f21Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f22Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_TIME, &f22Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f23Value[8] = "fixed";
    ret = GmcNodeSetPropertyByName(node, (char *)"F23", GMC_DATATYPE_FIXED, &f23Value, 8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f24Value[20] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F24", GMC_DATATYPE_STRING, &f24Value, strlen(f24Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f25Value = (i)&0x1f;
    ret = GmcNodeSetPropertyByName(node, (char *)"F25", GMC_DATATYPE_BITFIELD8, &f25Value, sizeof(f25Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f26Value = (i)&0x1ffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F26", GMC_DATATYPE_BITFIELD16, &f26Value, sizeof(f26Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f27Value = (i)&0x1ffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F27", GMC_DATATYPE_BITFIELD32, &f27Value, sizeof(f27Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f28Value = (i)&0x1ffffffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F28", GMC_DATATYPE_BITFIELD64, &f28Value, sizeof(f28Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool f29Value = true;
    ret = GmcNodeSetPropertyByName(node, (char *)"F29", GMC_DATATYPE_BOOL, &f29Value, sizeof(f29Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float f30Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F30", GMC_DATATYPE_FLOAT, &f30Value, sizeof(f30Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double f31Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F31", GMC_DATATYPE_DOUBLE, &f31Value, sizeof(f31Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f32ValueBits[1] = {0xff};
    GmcBitMapT f32Value = {0};
    f32Value.beginPos = 0;
    f32Value.endPos = 8 - 1;
    f32Value.bits = f32ValueBits;
    ret = GmcNodeSetPropertyByName(node, (char *)"F32", GMC_DATATYPE_BITMAP, &f32Value, sizeof(f32Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// version 1 新增字段
void TestGeneralT1ldVersionSetAddStringPropertyV1_Root(GmcNodeT *node, char *string)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置属性
void TestGeneralSetCommonProperty_Root(
    GmcNodeT *node, int64_t i, char *string, uint8_t *wrFixed, bool isDefaultValue = true)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint8_t f6Value = i;
    uint64_t f7Value = i;

    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;

        ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, wrFixed, 16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置索引
void TestGeneralT1SetPk(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 向一般复杂表中插入数据
void TestGeneralT1InsertOrReplace(GmcStmtT *stmt, const char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true,
    bool isParttion = false)
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "GmcPrepareStmtByLabelNameWithVersion.");
        }

        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1SetPk(root, i);

        if (isParttion) {
            uint8_t partition = i % 16;
            ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        // 设置根节点公共属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);
        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "GmcExecute.");
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);  // replace存在两条
    }
}
// 设置主键
void TestGeneralT1PkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 向一般复杂表中更新数据
void TestGeneralT1UpdateOrMerge(GmcStmtT *stmt, const char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true,
    bool isParttion = false)
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "GmcPrepareStmtByLabelNameWithVersion.");
        }

        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1PkIndexSet(stmt, i);
        if (isParttion && operationType == GMC_OPERATION_MERGE) {
            uint8_t partition = i % 16;
            ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        // 设置根节点公共属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);
        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i + 1);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i + 1);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "GmcExecute.");
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);  // replace存在两条
    }
}

// 主键删除数据
void TestGeneralDelete(GmcStmtT *stmt, const char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, char bigstring = 'a')
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "GmcPrepareStmtByLabelNameWithVersion.");
        }

        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);  // replace存在两条
    }
}

// 查询根节点公共部分
void TestGeneralT1GetCommonProperty_Root(GmcNodeT *node, int64_t i, char *string, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint8_t f6Value = i;
    uint64_t f7Value = i;

    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i;
    uint8_t f13Value = i & 0xf;

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f1Value, f1Value2);

    int32_t f2Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f2Value, f2Value2);

    uint32_t f3Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f3Value, f3Value2);

    int16_t f4Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f4Value, f4Value2);

    uint16_t f5Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f5Value, f5Value2);

    uint64_t f7Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f7Value, f7Value2);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F9", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(string) + 1);
    char f9Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9Value2, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string, f9Value2), 0);

    if (!isDefaultValue) {
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;

        uint8_t f9Value2;
        ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f9Value, sizeof(uint8_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(f9Value, f9Value2);

        uint16_t f10Value2;
        ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f10Value, sizeof(uint16_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(f10Value, f10Value2);
    }
}
// 查询根节点LMP6索引
void TestGeneralT1GetLpm6Property(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i;

    uint32_t vrid2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(vrid, vrid2);

    uint32_t vrfIndex2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(vrfIndex, vrfIndex2);

    uint8_t wr_fixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};

    ret = GmcNodeGetPropertyByName(node, (char *)"F8", wr_fixed, 16, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t maskLen2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(maskLen, maskLen2);
}

void TestGeneralT1GetAddStringProperty_Root(GmcNodeT *node, char *string)
{
    int ret = 0;
    unsigned int propSize;
    bool isNull;

    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(string) + 1);
    char f14Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &f14Value2, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string, f14Value2), 0);
}

// 校验不同节点node
void TestGeneralT1GetAllField_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    int64_t Value = i;

    int64_t f14Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &f14Value2, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f14Value2);

    uint64_t f15Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &f15Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f15Value2);

    int32_t f16Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &f16Value2, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f16Value2);

    uint32_t f17Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f17Value2);

    int16_t f18Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18Value2, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f18Value2);

    uint16_t f19Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f19Value2);

    int8_t f20Value2;
    int8_t f20Value = i % 128;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20Value2, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f20Value, f20Value2);

    uint8_t f21Value2;
    uint8_t f21Value = i % 256;
    ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value2, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f21Value, f21Value2);

    uint64_t f22Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F22", &f22Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f22Value2);

    unsigned int propSize;
    char f23Value[8] = "fixed";
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F23", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(8, propSize);
    char f23Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", &f23Value2, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(f23Value, f23Value2), 0);

    propSize = 0;
    char f24Value[20] = "string";
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F24", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f24Value) + 1);
    char f24Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", &f24Value2, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(f24Value, f24Value2), 0);

    uint8_t f25Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", &f25Value2, sizeof(f25Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value & 0x1f, f25Value2);

    uint16_t f26Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", &f26Value2, sizeof(f26Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value & 0x1ffff, f26Value2);

    uint32_t f27Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", &f27Value2, sizeof(f27Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value & 0x1ffff, f27Value2);

    uint64_t f28Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", &f28Value2, sizeof(f28Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value & 0x1ffffffff, f28Value2);

    bool f29Value = true;
    bool f29Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", &f29Value2, sizeof(f29Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f29Value, f29Value2);

    float f30Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", &f30Value2, sizeof(f30Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f30Value2);

    double f31Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", &f31Value2, sizeof(f31Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(Value, f31Value2);

    uint8_t f32Bits[1] = {0xff};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Bits, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
}

void TestGeneralT1GetUpdateBigObject_Root(GmcNodeT *node, char bigstring)
{
    int ret = 0;
    bool isNull;
    char string13k[1024 * 13] = {0};
    memset(string13k, bigstring, sizeof(string13k));
    string13k[1024 * 13 - 1] = '\0';

    char f23Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", f23Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f23Value2), 0);
    char f24Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", f24Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f24Value2), 0);
    char f25Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", f25Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f25Value2), 0);
    char f26Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", f26Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f26Value2), 0);
    char f27Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", f27Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f27Value2), 0);
    char f28Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", f28Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f28Value2), 0);
    char f29Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", f29Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f29Value2), 0);
    char f30Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", f30Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f30Value2), 0);
    char f31Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", f31Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f31Value2), 0);
    char f32Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f32Value2), 0);
    char f33Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", f33Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f33Value2), 0);
    char f34Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F34", f34Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f34Value2), 0);
    char f35Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F35", f35Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f35Value2), 0);
    char f36Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F36", f36Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f36Value2), 0);
    char f37Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F37", f37Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f37Value2), 0);
    char f38Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F38", f38Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f38Value2), 0);
    char f39Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F39", f39Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f39Value2), 0);
    char f40Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F40", f40Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f40Value2), 0);
    char f41Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F41", f41Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f40Value2), 0);
    char f42Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F42", f42Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f42Value2), 0);
    char f43Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F43", f43Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f43Value2), 0);
    char f44Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F44", f44Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f44Value2), 0);
    char f45Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F45", f45Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f45Value2), 0);
    char f46Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F46", f46Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f46Value2), 0);
    char f47Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F47", f47Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f47Value2), 0);
    char f48Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F48", f48Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f48Value2), 0);
    char f49Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F49", f49Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f49Value2), 0);
    char f50Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F50", f50Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f50Value2), 0);
    char f51Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F51", f51Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f51Value2), 0);
    char f52Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F52", f52Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f52Value2), 0);
    char f53Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F53", f53Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f53Value2), 0);
    char f54Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F54", f54Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f54Value2), 0);
    char f55Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F55", f55Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f55Value2), 0);
    char f56Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F56", f56Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f56Value2), 0);
    char f57Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F57", f57Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f57Value2), 0);
    char f58Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F58", f58Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f58Value2), 0);
    char f59Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F59", f59Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f59Value2), 0);
    char f60Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F60", f60Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f60Value2), 0);
    char f61Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F61", f61Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f61Value2), 0);
    char f62Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F62", f62Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f62Value2), 0);
    char f63Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F63", f63Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f63Value2), 0);
    char f64Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F64", f64Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f64Value2), 0);
    char f65Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F65", f65Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f65Value2), 0);
    char f66Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F66", f66Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f66Value2), 0);
    char f67Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F67", f67Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f67Value2), 0);
    char f68Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F68", f68Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f68Value2), 0);
    char f69Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F69", f69Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f69Value2), 0);
    char f70Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F70", f70Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f70Value2), 0);
    char f71Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F71", f71Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f71Value2), 0);
    char f72Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F72", f72Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f72Value2), 0);
    char f73Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F73", f73Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f73Value2), 0);
    char f74Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F74", f74Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f74Value2), 0);
    char f75Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F75", f75Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f75Value2), 0);
    char f76Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F76", f76Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f76Value2), 0);
    char f77Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F77", f77Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f77Value2), 0);
    char f78Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F78", f78Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f78Value2), 0);
    char f79Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F79", f79Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f79Value2), 0);
    char f80Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F80", f80Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f80Value2), 0);
    char f81Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F81", f81Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f81Value2), 0);
    char f82Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F82", f82Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f82Value2), 0);
    char f83Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F83", f83Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f83Value2), 0);
    char f84Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F84", f84Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f84Value2), 0);
    char f85Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F85", f85Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f85Value2), 0);
    char f86Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F86", f86Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f86Value2), 0);
    char f87Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F87", f87Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f87Value2), 0);
    char f88Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F88", f88Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f88Value2), 0);
    char f89Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F89", f89Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f89Value2), 0);
    char f90Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F90", f90Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f90Value2), 0);
    char f91Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F91", f91Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f91Value2), 0);
    char f92Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F92", f92Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f92Value2), 0);
    char f93Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F93", f93Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f93Value2), 0);
    char f94Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F94", f94Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f94Value2), 0);
    char f95Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F95", f95Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f95Value2), 0);
    char f96Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F96", f96Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f96Value2), 0);
    char f97Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F97", f97Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f97Value2), 0);
    char f98Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F98", f98Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f98Value2), 0);
    char f99Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F99", f99Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f99Value2), 0);
    char f100Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F100", f100Value2, 13312, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string13k, f100Value2), 0);
}
void TestGeneralT1ldVersionGetCommonProperty_T1_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    unsigned int propSize;
    int64_t f0Value = i;
    uint64_t f1Value = i;

    int64_t f0Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0Value2, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f0Value, f0Value2);

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f1Value, f1Value2);

    char f2Value[100] = "string";
    char f2Value2[100] = {0};
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F2", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f2Value) + 1);
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(f2Value, f2Value2), 0);

    char f3Value[9] = "fixed";
    char f3Value2[9] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value2, 9, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(f3Value, f3Value2), 0);

    char f4Value[10] = "bytes";
    char f4Value2[11] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, 10, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(f4Value, f4Value2), 0);
}

void TestGeneralT1ldVersionGetCommonProperty_T2_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    unsigned int propSize;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;

    int64_t f0Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0Value2, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f0Value, f0Value2);

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f1Value, f1Value2);

    int32_t f2Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f2Value, f2Value2);

    uint32_t f3Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f3Value, f3Value2);

    char f4Value[20] = "string";
    char f4Value2[100] = {0};
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F4", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f4Value) + 1);
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(f4Value, f4Value2), 0);

    char f5Value[7] = "fixed";
    char f5Value2[7] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(f5Value, f5Value2), 0);
}

// 订阅接受回调
void SnQuery(GmcStmtT *subStmt, int64_t updateValue = 0)
{
    int ret;
    int64_t f0Value;
    bool isNull;
    GmcNodeT *root, *T1, *T2;
    // 查询根节点
    ret = GmcGetRootNode(subStmt, &root);
    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    // 查询根节点公共部分
    TestGeneralT1GetCommonProperty_Root(root, f0Value + updateValue, (char *)"string", true);
    // 查询根节点LMP6索引
    TestGeneralT1GetLpm6Property(root, f0Value);
    // 查询升级字段属性
    TestGeneralT1GetAllField_Root(root, f0Value + updateValue);
    // 查询vectoryT1
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, f0Value);
    // 查询vectoryT2
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, f0Value);
}

void TestGeneralT1GetAllFieldFail_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    int64_t Value = i;

    int64_t f14Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &f14Value2, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f15Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &f15Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int32_t f16Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &f16Value2, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f17Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int16_t f18Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18Value2, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f19Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int8_t f20Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20Value2, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f21Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value2, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f22Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F22", &f22Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    char f23Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", &f23Value2, 8, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    char f24Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", &f24Value2, 20, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f25Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", &f25Value2, sizeof(f25Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f26Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", &f26Value2, sizeof(f26Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f27Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", &f27Value2, sizeof(f27Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f28Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", &f28Value2, sizeof(f28Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    bool f29Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", &f29Value2, sizeof(f29Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    float f30Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", &f30Value2, sizeof(f30Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    double f31Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", &f31Value2, sizeof(f31Value2), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f32Bits[1] = {0xff};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Bits, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int64_t f33Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", &f33Value2, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void SnQueryOldVersion(GmcStmtT *subStmt, int64_t updateValue = 0)
{
    int ret;
    int64_t f0Value;
    bool isNull;
    GmcNodeT *root, *T1, *T2;
    // 查询根节点
    ret = GmcGetRootNode(subStmt, &root);
    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    // 查询根节点公共部分
    TestGeneralT1GetCommonProperty_Root(root, f0Value + updateValue, (char *)"string", true);
    // 查询根节点LMP6索引
    TestGeneralT1GetLpm6Property(root, f0Value);

    // 查询升级字段属性失败
    TestGeneralT1GetAllFieldFail_Root(root, f0Value + updateValue);

    // 查询vectoryT1
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, f0Value);
    // 查询vectoryT2
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, f0Value);
}

void sn_callback_fullsub(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataT *user_data = (SnUserDataT *)userData;

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;

                printf("[INFO] <---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else {
                printf("[INFO] <---Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }

                case GMC_SUB_EVENT_REPLACE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }

                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                default: {
                    printf("default: invalid eventType %d\r\n", info->eventType);
                    break;
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            default: {
                printf("default: invalid eventType %d\r\n", info->eventType);
                break;
            }
        }
    }
}

void sn_callback_newversion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    bool isNull;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        if (ret != GMERR_OK || eof == true) {
            if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                printf("[info] GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
                user_data->scanEofNum++;
            }
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            // 获取订阅推送顶点的标签名称
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读 new object
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType, info->eventType, index);
                    // 在增量订阅中，指定获取旧顶点或新顶点, true表示获取旧顶点, false表示获取新顶点
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读old
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType, info->eventType, index);

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读old
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType, info->eventType, index);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] %d GMC_SUB_EVENT_REPLACE new_value is %d\r\n", user_data->subIndex, index);
                    break;
                }
                case GMC_SUB_EVENT_KV_SET: {
                    // 读new
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] %d GMC_SUB_EVENT_REPLACE new_value is %d\r\n", user_data->subIndex, index);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读old
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] %d GMC_SUB_EVENT_INITIAL_LOAD new_value is %d\r\n", user_data->subIndex, index);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                break;
            }
        }
    }
}

void sn_callback_oldversion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    bool isNull;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt, 100);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                break;
            }
        }
    }
}

// 查询一般复杂表
void TestGeneralT1PkScan(GmcStmtT *stmt, const char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, bool isParttition = false)
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

// 全表扫描查询
int readTable(GmcStmtT *stmt, const char *tableOut)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "校验表%s", tableOut);

    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, tableOut, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while ((!isFinish) && (ret == GMERR_OK)) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    return cnt;
}

// 特殊复杂表结构化写入
#define SIMPLE_LABEL_FIXED_SIZE 9
#define SIMPLE_LABEL2_FIXED_SIZE 8
#define SIMPLE_LABEL2_BITMAP_SIZE 8
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
#define SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE 13312
#define SIMPLE_LABEL_ADD_FIXED_SIZE 1331

#define STRING_LEN 16
#define BYTES_LEN 256
#define STRING2_LEN (13 * 1024)
#define STRING3_LEN (32 * 1024)
char *g_labelName2 = (char *)"special";

// 结构化参数
typedef struct TagSpeciallabelCfg {
    int32_t startVal;  // 主键或其他非成员索引的起始值
    uint32_t count;    // 主键或其他非成员索引的数量
    int32_t coefficient;  // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    int32_t threadId;       // 线程Id
    uint16_t t1VCount;
    uint16_t t2VCount;
    uint32_t schemaVersion;
    GmcOperationTypeE optType;
    bool fieldIsNull[8];
} GtSpeciallabelCfgT;

#pragma pack(1)
typedef struct TagSpeciallabelT2VVertex {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
} GtSpeciallabelT2VVertexT;

typedef struct TagSpeciallabelT1VVertex {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
    uint16_t t2VCount;
    GtSpeciallabelT2VVertexT *t2V;
} GtSpeciallabelT1VVertexT;

typedef struct TagSpeciallabelVertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
    uint16_t f15Len;  // 升级字段
    uint8_t *f15;
    uint16_t f16Len;  // 升级字段
    uint8_t *f16;
} GtSpeciallabelVertexT;

typedef struct TagSpeciallabelVertexWithPartition {
    int64_t f0;
    uint8_t partition;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
    uint16_t f15Len;  // 升级字段
    uint8_t *f15;
    uint16_t f16Len;  // 升级字段
    uint8_t *f16;
} GtSpeciallabelVertexWithPartitionT;
#pragma pack()

void GtSpeciallabelStructSetT2VProperty(GtSpeciallabelT2VVertexT *vertex, int32_t value, char *stringValue)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    (void)memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
}

void GtSpeciallabelStructSetT1VProperty(
    GtSpeciallabelT1VVertexT *vertex, int32_t value, char *stringValue, uint16_t t2VCount)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
    vertex->t2VCount = t2VCount;
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructSetT2VProperty(&vertex->t2V[i], value, stringValue);
    }
}

void GtSpeciallabel2StructSetNewProperty(GtSpeciallabelVertexT *vertex, char *bytesValue, char *stringValue)
{
    vertex->f15Len = strlen(bytesValue);
    if (!vertex->f15) {
        vertex->f15 = (uint8_t *)malloc(vertex->f15Len + 1);
    }
    if (vertex->f15 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f15 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f15, vertex->f15Len + 1, "%s", bytesValue);
    vertex->f16Len = strlen(stringValue) + 1;
    if (!vertex->f16) {
        vertex->f16 = (uint8_t *)malloc(vertex->f16Len);
    }
    if (vertex->f16 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f16 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f16, vertex->f16Len, "%s", stringValue);
}

void GtSpeciallabelStructSetProperty(GtSpeciallabelVertexT *vertex, int32_t value, uint16_t t1VCount, uint16_t t2VCount,
    char *bytesValue, char *stringValue, bool isDefaultValue = true, int32_t coefficient = 0)
{
    vertex->f0 = value;
    int32_t updateValue = 0;
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
    GtSpeciallabel2StructSetNewProperty(vertex, bytesValue, stringValue);
}

void GtSpeciallabel2StructSetNewPropertyWithPartition(
    GtSpeciallabelVertexWithPartitionT *vertex, char *bytesValue, char *stringValue)
{
    vertex->f15Len = strlen(bytesValue);
    if (!vertex->f15) {
        vertex->f15 = (uint8_t *)malloc(vertex->f15Len + 1);
    }
    if (vertex->f15 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f15 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f15, vertex->f15Len + 1, "%s", bytesValue);
    vertex->f16Len = strlen(stringValue) + 1;
    if (!vertex->f16) {
        vertex->f16 = (uint8_t *)malloc(vertex->f16Len);
    }
    if (vertex->f16 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f16 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f16, vertex->f16Len, "%s", stringValue);
}

void GtSpeciallabelStructSetPropertyWithPartition(GtSpeciallabelVertexWithPartitionT *vertex, int32_t value,
    uint16_t t1VCount, uint16_t t2VCount, char *bytesValue, char *stringValue, bool isDefaultValue = true,
    int32_t coefficient = 0)
{
    vertex->f0 = value;
    int32_t updateValue = 0;
    uint8_t parttion = 1;
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value + updateValue;
    vertex->partition = parttion;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
    GtSpeciallabel2StructSetNewPropertyWithPartition(vertex, bytesValue, stringValue);
}

void GtSpeciallabelStructFreeT2V(GtSpeciallabelT2VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
}

void GtSpeciallabelStructFreeT1V(GtSpeciallabelT1VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructFreeT2V(&(vertex->t2V[i]));
    }
}

void GtSpeciallabel2StructFree(GtSpeciallabelVertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
    if (vertex->f15) {
        free(vertex->f15);
    }
    if (vertex->f16) {
        free(vertex->f16);
    }
}

void GtSpeciallabel2StructFreeWithPartition(GtSpeciallabelVertexWithPartitionT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
    if (vertex->f15) {
        free(vertex->f15);
    }
    if (vertex->f16) {
        free(vertex->f16);
    }
}

// 以结构化的方式 replace Speciallabel 表的数据
int GtSpeciallabelStructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
    bool isDefaultValue = true, int32_t result = GMERR_OK)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabelVertexT *vertex = (GtSpeciallabelVertexT *)malloc(sizeof(GtSpeciallabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        free(vertex);
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
        (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);

    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        free(t1V);
        free(vertex);
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {(char *)"special", schemaVersion, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "special", schemaVersion, optType);
        AW_MACRO_EXPECT_EQ_INT(result, ret);
        if (ret) {
            free(t2V);
            free(t1V);
            free(vertex);
            return ret;
        }

        GtSpeciallabelStructSetProperty(
            vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GtSpeciallabel2StructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

int GtSpeciallabelStructWriteWithPartition(
    GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabelVertexWithPartitionT *vertex =
        (GtSpeciallabelVertexWithPartitionT *)malloc(sizeof(GtSpeciallabelVertexWithPartitionT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexWithPartitionT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
        (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);

    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {(char *)"special", schemaVersion, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "special", schemaVersion, optType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GtSpeciallabelStructSetPropertyWithPartition(
            vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GtSpeciallabel2StructFreeWithPartition(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

void GtSpeciallabel2GetNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t1V)
{
    GmcNodeT *Root, *t1;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1V", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    *root = Root;
    *t1V = t1;
}

void TestSpecialT3UpdateGetOldPropertyByName(GmcNodeT *node, int64_t i, char *bytesValue, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f1Value, f1Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f2Value, f2Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f4Value, f4Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, f9ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f10Value, f10ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f13Value, f13ValueR);
    ret = queryNodePropertyAndCompare(node, (char *)"F14", GMC_DATATYPE_BYTES, bytesValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT3GetLpmProperty(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vrid, vrid2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(maskLen, maskLen2);
}

void TestSpecialT3MidVersionGetNewField(GmcNodeT *node, char *bytesValue, char *stringValue, bool fieldIsNull)
{
    if (!fieldIsNull) {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F15", GMC_DATATYPE_BYTES, bytesValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F16", GMC_DATATYPE_STRING, stringValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F15", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F16", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSpecialT3MidVersionGetNewFieldFailed(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    int32_t ret = 0;
    bool isNull;
    uint32_t sizeValue = 0;
    char bytesV[BYTES_LEN] = {0};
    char stringV[BYTES_LEN] = {0};
    ret = GmcNodeGetPropertySizeByName(node, "F15", &sizeValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F16", &sizeValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F15", bytesV, strlen(bytesValue), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F16", stringV, strlen(stringValue), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void GtSpeciallabel3GeneralComparePropertyVector(GmcNodeT *node, int64_t value, char *stringValue)
{
    uint32_t v1Value = value;
    int32_t ret = queryNodePropertyAndCompare(node, (char *)"V1", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V2", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V4", GMC_DATATYPE_STRING, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 8 - 1;
    v3.bits = v3Bits;
    ret = queryNodePropertyAndCompare(node, (char *)"V3", GMC_DATATYPE_BITMAP, &v3Bits);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabel3GeneralGetVector(
    GmcNodeT *node, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i = 0;
    GmcNodeT *t2V = NULL;
    for (i = 0; i < t1Count; ++i) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabel3GeneralComparePropertyVector(node, index, stringValue);
        ret = GmcNodeGetChild(node, "T2V", &t2V);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t k = 0; k < t2Count; k++) {
            ret = GmcNodeGetElementByIndex(t2V, k, &t2V);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabel3GeneralComparePropertyVector(t2V, index, stringValue);
        }
    }
}
void TestSpecialT3MostNewVersionGetNewFieldFailed(GmcNodeT *node, int64_t value)
{
    int32_t ret = 0;
    bool isNull;
    uint32_t sizeValue = 0;
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }

    ret = GmcNodeGetPropertySizeByName(node, "F17", &sizeValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F18", &sizeValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F21", &sizeValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F17", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F18", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F19", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F20", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F21", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    free(stringTest);
}

void TestSpecialT3MostNewVersionGetNewField(GmcNodeT *node, int64_t value, bool fieldIsNull)
{
    int64_t valueWrite = value;
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)stringTest, STRING3_LEN, "s%032766d", valueWrite);
    if (!fieldIsNull) {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F17", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F18", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F19", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F20", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F21", GMC_DATATYPE_BYTES, stringTest);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F17", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F18", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F19", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F20", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F21", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(stringTest);
}

// 设置主键
void TestSimpleT1PkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// scan 复杂表
int TestSpecialT3NewOldVersionGeneralRead(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
    char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    GmcNodeT *root = NULL, *t1Node = NULL;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    AW_FUN_Log(LOG_INFO, "labelName = %s schemaVersion = %d keyId = %d", g_labelName2, schemaVersion, keyId);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GtSpeciallabel2GetNode(stmt, &root, &t1Node);
        TestSpecialT3UpdateGetOldPropertyByName(root, i + coefficient, bytesValue, isDefaultValue);
        TestSpecialT3GetLpmProperty(root, i);
        GtSpeciallabel3GeneralGetVector(t1Node, i, stringValue, t1VCount, t2VCount);
        if (schemaVersion == 0) {
            TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
            TestSpecialT3MostNewVersionGetNewFieldFailed(root, i + coefficient);
        } else if (schemaVersion == 1) {
            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
            TestSpecialT3MostNewVersionGetNewFieldFailed(root, i + coefficient);
        } else if (schemaVersion == 2) {
            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
            TestSpecialT3MostNewVersionGetNewField(root, i + coefficient, fieldIsNull[2]);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    }
    return 0;
}

// 表降级
char g_dWay[64] = "sync";
int TestDownGradeVertexLabel(const char *labelName, uint32_t schemaVersion, char *expectValue, char *dWay = g_dWay,
    char *nsName = g_testNameSpace)
{
    char cmd[512] = {0};
    int ret = 0;
    (void)snprintf(
        cmd, 512, "%s/gmddl -c alter -t %s -v %d -d %s -ns %s", g_toolPath, labelName, schemaVersion, dWay, nsName);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

// 线程内写数据
void *ThreadPart(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t schemaVersion = 0;
    int32_t index = *(int32_t *)args;
    AW_FUN_Log(LOG_STEP, "index is %d", index);

    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char labelName[128] = "simple";
    (void)sprintf(labelName, "generalpartition%d", index);
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);
    uint8_t par_count = 16;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // update更新100条，replace100条，merge100条，删除100条，插入100条，

    // update更新100条
    TestGeneralT1UpdateOrMerge(
        stmt, labelName, 0, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
    // replace100条
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 100, 200, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);
    // merge100条，
    TestGeneralT1UpdateOrMerge(
        stmt, labelName, 200, 300, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true, true);

    // 删除100条
    TestGeneralDelete(stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    // 插入100条
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(stmt, labelName, partition, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(400, vertexCount);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadNoPart(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t schemaVersion = 0;
    int32_t index = *(int32_t *)args;
    AW_FUN_Log(LOG_STEP, "index is %d", index);

    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char labelName[128] = "simple";
    (void)sprintf(labelName, "generalNopartition%d", index);
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(stmt, labelName, partition);
    EXPECT_EQ(GMERR_OK, ret);

    // update更新100条，replace100条，merge100条，删除100条，插入100条，

    // update更新100条
    TestGeneralT1UpdateOrMerge(stmt, labelName, 0, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
    // replace100条
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 100, 200, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    // merge100条，
    TestGeneralT1UpdateOrMerge(stmt, labelName, 200, 300, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

    // 删除100条
    TestGeneralDelete(stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

    // 插入100条
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    ret = GmcEndCheck(stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(400, vertexCount);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 线程内写数据
void *ThreadPartTrue(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t schemaVersion = 0;
    int32_t index = *(int32_t *)args;
    AW_FUN_Log(LOG_STEP, "index is %d", index);

    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char labelName[128] = "simple";
    (void)sprintf(labelName, "generalpartition%d", index);
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);
    uint8_t par_count = 16;
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // update更新100条，replace100条，merge100条，删除100条，插入100条，

    // update更新100条
    TestGeneralT1UpdateOrMerge(
        stmt, labelName, 0, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
    // replace100条
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 100, 200, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);
    // merge100条，
    TestGeneralT1UpdateOrMerge(
        stmt, labelName, 200, 300, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true, true);

    // 删除100条
    TestGeneralDelete(stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

    // 插入100条
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(stmt, labelName, partition, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadNoPartTrue(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t schemaVersion = 0;
    int32_t index = *(int32_t *)args;
    AW_FUN_Log(LOG_STEP, "index is %d", index);

    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char labelName[128] = "simple";
    (void)sprintf(labelName, "generalNopartition%d", index);
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(stmt, labelName, partition);
    EXPECT_EQ(GMERR_OK, ret);

    // update更新100条，replace100条，merge100条，删除100条，插入100条，

    // update更新100条
    TestGeneralT1UpdateOrMerge(stmt, labelName, 0, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
    // replace100条
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 100, 200, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    // merge100条，
    TestGeneralT1UpdateOrMerge(stmt, labelName, 200, 300, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

    // 删除100条
    TestGeneralDelete(stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

    // 插入100条
    TestGeneralT1InsertOrReplace(
        stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    ret = GmcEndCheck(stmt, labelName, partition, true);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 批量建表
int Createlables(GmcConnT *conn, GmcStmtT *stmt, int32_t labelNums)
{
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    char configTest[] = "{\"max_record_num\":10000, \"isFastReadUncommitted\":1}";
    int ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char lableName[20] = "";
    char schemaName[100] = "";
    for (int i = 0; i < 3; i++) {
        for (int j = 1; j < labelNums + 1; j++) {
            if (i == 1) {
                (void)sprintf(lableName, "simple%d", j);
                (void)sprintf(schemaName, "./one_thread_async/simpleParttion%d.gmjson", j);
            } else if (i == 2) {
                (sprintf)(lableName, "general%d", j);
                (void)sprintf(schemaName, "./one_thread_async/generalNopartition%d.gmjson", j);
            } else {
                (sprintf)(lableName, "special%d", j);
                (void)sprintf(schemaName, "./one_thread_async/specialPartition%d.gmjson", j);
            }
            ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, lableName, NULL, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcBatchExecute(batch, &batchRet);

            char *schemaPath = NULL;
            readJanssonFile(schemaName, &schemaPath);
            EXPECT_NE((void *)NULL, schemaPath);
            ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, lableName, schemaPath, configTest);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            free(schemaPath);
            ret = GmcBatchExecute(batch, &batchRet);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t totalNum = 0;
            uint32_t successNum = 0;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
            AW_MACRO_EXPECT_EQ_INT(successNum, totalNum);
        }
    }
    return GmcBatchDestroy(batch);
}

// 批量删表
// 批量建表
int Droplables(GmcConnT *conn, GmcStmtT *stmt, int32_t labelNums)
{
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    char configTest[] = "{\"max_record_num\":10000, \"isFastReadUncommitted\":0}";
    int ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char lableName1[20] = "";
    char lableName2[20] = "";
    char lableName3[20] = "";
    for (int32_t i = 1; i < labelNums + 1; i++) {
        (void)sprintf(lableName1, "simple%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, lableName1, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        (void)sprintf(lableName2, "general%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, lableName2, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        (void)sprintf(lableName3, "special%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, lableName3, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    AW_MACRO_EXPECT_EQ_INT(successNum, totalNum);

    return GmcBatchDestroy(batch);
}

void TestStartDbWarmReboot(const char *cfgFilePath)
{
    char cmd[1024] = {0};
    (void)sprintf_s(cmd, sizeof(cmd), "gmserver -rb -b -p %s > /dev/null", cfgFilePath);
    system(cmd);
}

#endif
