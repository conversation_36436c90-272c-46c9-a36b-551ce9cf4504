/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 3、保留内存heap模式变长表直连写
 * Author: lushiguang
 * Create: 2024-09-9
 */

#include "rsm_dw.h"


class RsmDw03 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};


void RsmDw03::SetUp()
{
    AW_FUN_Log(LOG_STEP, "[INFO] RsmDw03 Start.");
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = ChangeGmserverCfg((char *)"enableClusterHash", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"directWrite", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = InitRsmCfg();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_insertCount = 5000;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestTryRegisterSignal(TestCrashHandler);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void RsmDw03::TearDown()
{
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "[INFO] RsmDw03 End.");
}

// 001、heap模式变长表，直连写insert，验证数据，重启再次直连写，预期成功恢复
TEST_F(RsmDw03, warmboot_011_001_03_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    int ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonInsertVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WarmReboot();
    ret = CleanAndReConnDw(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重新写
    ret = CommonInsertVarTb(g_stmt, g_varTableName, g_version0, startIndex + insertCount, insertCount,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount * 2,
        insertCount * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、heap模式变长表，直连写update，验证数据，重启再次直连写，预期：数据成功恢复
TEST_F(RsmDw03, warmboot_011_001_03_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    int ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonInsertVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 更新
    ret = CommonUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WarmReboot();
    ret = CleanAndReConnDw(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 更新
    ret = CommonUpdateVarTb2(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb2(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、heap模式变长表，直连写replace，验证数据，重启再次直连写，预期：数据成功恢复
TEST_F(RsmDw03, warmboot_011_001_03_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    int ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WarmReboot();
    ret = CleanAndReConnDw(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex + 0.5 * insertCount, insertCount,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonCheckVarTb(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount * 1.5, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、heap模式变长表，直连写delete，验证数据，重启再次直连写，预期：数据成功恢复
TEST_F(RsmDw03, warmboot_011_001_03_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount * 3;
    int ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonDelete(g_stmt, g_varTableName, g_version0, g_insertCount * 2, g_insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WarmReboot();
    ret = CleanAndReConnDw(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount,
        g_insertCount * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonDelete(g_stmt, g_varTableName, g_version0, g_insertCount, g_insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount, g_insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、heap模式变长表，同表，直连写并发，预期成功恢复（同表并发实际只有一个走直连写）
TEST_F(RsmDw03, warmboot_011_001_03_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    int ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int connCount = 4;
    GmcConnT *conn[connCount];
    GmcStmtT *stmt[connCount];
    for (int i = 0; i < connCount; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = AsyncVarTbOperate(stmt[0], g_varTableName, g_version0, startIndex, insertCount, g_withNewField,
        T_RSM_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncVarTbOperate(stmt[1], g_varTableName, g_version0, startIndex + insertCount, insertCount,
        g_withNewField, T_RSM_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncVarTbOperate(stmt[2], g_varTableName, g_version0, startIndex + insertCount * 2, insertCount,
        g_withNewField, T_RSM_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncVarTbOperate(stmt[3], g_varTableName, g_version0, startIndex + insertCount * 3, insertCount,
        g_withNewField, T_RSM_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount * 4,
        insertCount * 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WarmReboot();
    ret = CleanAndReConnDw(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重新写
    ret = CommonInsertVarTb(g_stmt, g_varTableName, g_version0, startIndex + insertCount * 4, insertCount,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount * 5,
        insertCount * 5);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、heap模式变长表，不同表，直连写并发，预期成功恢复
TEST_F(RsmDw03, warmboot_011_001_03_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    char tableName[30] = { 0 };
    int tableCount = 5;
    int ret;
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret = CommonCreateTable(g_stmt, tableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcConnT *conn[tableCount];
    GmcStmtT *stmt[tableCount];
    for (int i = 0; i < tableCount; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret =
            AsyncVarTbOperate(stmt[i], tableName, g_version0, startIndex, insertCount, g_withNewField, T_RSM_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WarmReboot();
    ret = CleanAndReConnDw(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret = CommonCreateTable(g_stmt, tableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // DFX查询等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重新写
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret = CommonInsertVarTb(g_stmt, tableName, g_version0, startIndex + insertCount, insertCount, g_withNewField);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret =
            CommonRecordCount(g_stmt, tableName, g_version0, startIndex, startIndex + insertCount * 2, insertCount * 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret = GmcDropVertexLabel(g_stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、heap模式变长表，不同表，非直连写、直连写并发，预期成功恢复
TEST_F(RsmDw03, warmboot_011_001_03_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    char tableName[30] = { 0 };
    int dwTableCount = 5;
    int csTableCount = 5;
    int tableCount = dwTableCount + csTableCount;
    int ret;
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        if (i < dwTableCount) {
            ret = CommonCreateTable(g_stmt, tableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
        } else {
            ret = CommonCreateTable(g_stmt, tableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcConnT *conn[tableCount];
    GmcStmtT *stmt[tableCount];
    for (int i = 0; i < tableCount; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret =
            AsyncVarTbOperate(stmt[i], tableName, g_version0, startIndex, insertCount, g_withNewField, T_RSM_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WarmReboot();
    ret = CleanAndReConnDw(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret = CommonCreateTable(g_stmt, tableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // DFX查询等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重新写
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret = CommonInsertVarTb(g_stmt, tableName, g_version0, startIndex + insertCount, insertCount, g_withNewField);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret = CommonRecordCount(g_stmt, tableName, g_version0, startIndex, startIndex + insertCount, insertCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "table_%d", i);
        ret = GmcDropVertexLabel(g_stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、heap模式变长表，添加合并订阅后，写数据触发订阅，重启后，重新下发订阅，直连写再次触发推送，预期成功恢复，正常触发
TEST_F(RsmDw03, warmboot_011_001_03_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    SnUserDataT *newSubData;
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    int ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfigSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonInsertVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建新订阅,订阅modify,update数据时rb重启");
    int chanRingLen = 256;
    g_connNewSub = NULL;
    g_stmtNewSub = NULL;
    const char *newSubConnName = "warmboot_011_conn";
    ret = testSubConnect(&g_connNewSub, &g_stmtNewSub, 1, g_epoll_reg_info, newSubConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcSubConfigT newSubInfo;
    const char *subName = "warmbootfixedsub";
    newSubInfo.subsName = subName;
    newSubInfo.configJson = g_newSubInfoModifyVar;
    ret = testSnMallocUserData(&newSubData, insertCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSubscribe(g_stmt, &newSubInfo, g_connNewSub, NewSnCallBack, newSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = AsyncVarTbOperate(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField,
        T_RSM_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = (SnUserDataT *)newSubData;
    AW_MACRO_EXPECT_NE_INT(userData->insertNum, 0);

    WarmReboot();

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(newSubData);
    (void)testGmcDisconnect(g_conn, g_stmt);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_dwConfigSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "warmboot后,下发相同的订阅关系");
    g_connNewSub = NULL;
    g_stmtNewSub = NULL;
    ret = testSubConnect(&g_connNewSub, &g_stmtNewSub, 1, g_epoll_reg_info, newSubConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    newSubInfo.subsName = subName;
    newSubInfo.configJson = g_newSubInfoModifyVar;
    ret = testSnMallocUserData(&newSubData, insertCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSubscribe(g_stmt, &newSubInfo, g_connNewSub, NewSnCallBack, newSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(newSubData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新
    ret = CommonUpdateVarTb2(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb2(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount,
        g_withNewField);

    ret = testWaitStMgSnRecv(newSubData, GMC_SUB_EVENT_MODIFY, insertCount, insertCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(newSubData);
    ret = testSubDisConnect(g_connNewSub, g_stmtNewSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_EPOLL_OPERATE_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

