/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 连接异常检测基本功能测试（非Yang场景）
 Notes        : 001. 创建namespace，写数据和直连读中途连接异常
                002. 创建订阅连接，推送中途连接长时间无请求
                003. 创建资源池，写数据和读数据中途连接异常
                004. 开启事务，写数据和读数据中途连接异常
                005. 创建特殊复杂表，写数据和读数据中途连接异常
                006. 建表，写数据，哈希扫描中途连接异常
                007. 建表，写数据，连接异常，查连接视图
                008. 建表，导入系统权限，写数据和读数据中途连接异常
                009. 建表，批量写数据中途连接异常
                010. 多次在线修改配置项
                011. 将配置项值设置为：-1；0；100；1000000；10000000
                012. 创建kv表，批量写中途连接异常

 History      :
 Author       : liaoxiang lwx1036939
 Modification : 2022/10/27
*****************************************************************************/

#include "ConnectExceptionDetection.h"

GmcConnT *g_conn_sub = NULL, *g_subChan = NULL;
GmcStmtT *g_stmt_sub = NULL;
char *g_sub_info = NULL;
const char *g_subName = "subVertexLabel", *g_subConnName = "subConnName";

class ConnExcDetFunV5 : public testing::Test {
public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"connectTimeout=1\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=120,200\"");
        system("sh $TEST_HOME/tools/start.sh");

        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    }
};

void ConnExcDetFunV5::SetUp()
{
    // 创建同步连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void ConnExcDetFunV5::TearDown()
{
    // 释放同步连接
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class ConnExcDetFunV5_01 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void ConnExcDetFunV5_01::SetUp()
{}

void ConnExcDetFunV5_01::TearDown()
{}

// 001. 创建namespace，写数据和直连读中途连接异常
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_001)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // create namespace
    ret = GmcCreateNamespace(g_stmt, g_namespace, g_namespaceUserName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create vertexlabel
    readJanssonFile("schemafile/V5/NormalVertexLabel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // insert
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)setVertexPropertyPK(g_stmt, g_endNum);
    (void)setVertexProperty(g_stmt, g_endNum);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcUseNamespace(g_stmt, g_namespace); // 必须切换namespace，不然找不到表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 直连读
    for (int i = g_startNum; i < g_endNum; i++) {
        queryVertexProperty(g_stmt, i, g_labelName, g_pkName);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造读异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t pkValue = g_endNum / 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_pkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);

    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, g_namespace); // 必须切换namespace，不然找不到表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop namespace
    ret = GmcDropNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 002. 创建订阅连接，推送中途连接长时间无请求
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_002)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0, userDataIdx = 0, chanRingLen = 256;

    ret = testSnMallocUserData(&user_data, g_endNum * g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create vertexlabel
    readJanssonFile("schemafile/V5/NormalVertexLabel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemafile/V5/subinfo.gmjson", &g_sub_info);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_sub_info);

    // 订阅结构体    
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;

    // 创建订阅关系
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, snCallback, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // insert
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);

        // 读
        queryVertexProperty(g_stmt, i, g_labelName, g_pkName);
    }

    // replace && query
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i + g_endNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否replace成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, g_affectRows);

        // 读
        queryVertexProperty(g_stmt, i + g_endNum, g_labelName, g_pkName);
    }

    // 等待 insert 事件 推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造连接长时间无请求场景\n");
    sleep(65);

    // 等待 replace 事件 推送完成。连接异常不影响订阅推送
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // free
    free(g_schema);
    free(g_sub_info);
    testSnFreeUserData(user_data);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 003. 创建资源池，写数据和读数据中途连接异常
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_003)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0, g_res_pool_id = 0, g_res_start_id = 0;;

    // 创建资源池和表
    GmcDropVertexLabel(g_stmt, g_resLabelName);
    ret = GmcCreateVertexLabel(g_stmt, g_labelSchemaJson, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateResPool(g_stmt, g_resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt, g_resPoolName, g_resLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // 写入数据
    uint64_t resId;
    uint32_t resCnt = 1;
    ret = TestSetResId(123, resCnt, AUTO_START_IDX, &resId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_startNum; i < g_endNum; i++) {
        GtResVertexT resVertex = {&i, &i, &resId};
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_resLabelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSetAllVertexProperty(g_stmt, resVertex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);

        ret = testGetAndCheckResIdInfo(g_stmt, 1, g_res_pool_id, resCnt, g_res_start_id + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);

    int i = g_endNum;
    GtResVertexT resVertex = {&i, &i, &resId};
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_resLabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSetAllVertexProperty(g_stmt, resVertex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 直连读
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_resLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isFinished;
        ret = GmcFetch(g_stmt, &isFinished);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isNull;
        int32_t f0Val;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0Val, sizeof(f0Val), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(i, f0Val);

        int32_t f1Val;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1Val, sizeof(f1Val), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(i, f1Val);

        uint64_t resVal;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &resVal, sizeof(resVal), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestCheckResIdInfo(resVal, g_res_pool_id, resCnt, g_res_start_id + i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造读异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_resLabelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t pkValue = g_endNum / 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &pkValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 清除资源池和表
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    ret = testDeleteVertexByPk(g_stmt, g_resLabelName, 0, g_endNum + 1);
#else
    ret = testDeleteVertexByPk(g_stmt, g_resLabelName, 0, g_endNum);
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(g_stmt, g_resLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, g_resPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_resLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 004. 开启事务，写数据和读数据中途连接异常
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_004)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // create vertexlabel
    char Label_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    readJanssonFile("schemafile/V5/NormalVertexLabel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, Label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;

    // 开启事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // insert
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)setVertexPropertyPK(g_stmt, g_endNum);
    (void)setVertexProperty(g_stmt, g_endNum);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 直连读
    for (int i = g_startNum; i < g_endNum; i++) {
        queryVertexProperty(g_stmt, i, g_labelName, g_pkName);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造读异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t pkValue = g_endNum / 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_pkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 005. 创建特殊复杂表，写数据和读数据中途连接异常
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_005)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0, g_arrayNum = 3, g_vectorNum = 3;;
    const char *g_labelName2 = "OP_T1";

    // create vertexlabel
    readJanssonFile("schemafile/V5/NormalTreeModel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName2);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // 普通同步插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    testGmcInsertVertex(g_stmt, 1, 0, (char *)"string", g_startNum, g_endNum, g_arrayNum, g_vectorNum, g_labelName2);

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);


    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    (void)testGmcInsertVertex(g_stmt, 1, 0, (char *)"string", g_startNum + g_endNum,
            g_endNum * 2, g_arrayNum, g_vectorNum, g_labelName2, GMERR_OK);
#else
    (void)testGmcInsertVertex(g_stmt, 1, 0, (char *)"string", g_startNum + g_endNum,
            g_endNum * 2, g_arrayNum, g_vectorNum, g_labelName2, GMERR_CONNECTION_RESET_BY_PEER);
#endif
    
    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 直连读
    testCheckProperty(g_stmt, g_labelName2);

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造读异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName2, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t pkValue = g_endNum / 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &pkValue, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "OP_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 006. 建表，写数据，哈希扫描中途连接异常
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_006)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;
    const char *keyName = "hash";

    // create vertexlabel
    readJanssonFile("schemafile/V5/NormalVertexLabel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // insert
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
    }

    // 哈希扫描
    for (int i = g_startNum; i < 1; i++) {
        queryVertexProperty(g_stmt, i, g_labelName, keyName, 1);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t hashValue = g_endNum / 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &hashValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
// euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 007. 建表，写数据，连接异常，查连接视图
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_007)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;
    const char *keyName = "hash";

    // create vertexlabel
    readJanssonFile("schemafile/V5/NormalVertexLabel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // insert
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
    }

    // 哈希扫描
    for (int i = g_startNum; i < g_endNum; i++) {
        queryVertexProperty(g_stmt, i, g_labelName, keyName, 1);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);

    // 查询连接视图
    char const *view_name = "V\\$DRT_CONN_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "g_command = %s\n", g_command);
    ret = executeCommand(g_command, "index = 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 008. 建表，导入系统权限，写数据和读数据中途连接异常
TEST_F(ConnExcDetFunV5_01, Reliablity_003_FuncV5_008)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"connectTimeout=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/start.sh");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入白名单
    const char *allow_list_file = "./schemafile/Policy/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入系统权限 create drop
    const char *sys_policy_file = "./schemafile/Policy/AllPolicy.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, sys_policy_file,
        g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create vertexlabel
    readJanssonFile("schemafile/V5/NormalVertexLabel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // insert
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)setVertexPropertyPK(g_stmt, g_endNum);
    (void)setVertexProperty(g_stmt, g_endNum);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 直连读
    for (int i = g_startNum; i < g_endNum; i++) {
        queryVertexProperty(g_stmt, i, g_labelName, g_pkName);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造读异常场景\n");
    sleep(65);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t pkValue = g_endNum / 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_pkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 009. 建表，批量写数据中途连接异常
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_009)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    // create vertexlabel
    readJanssonFile("schemafile/V5/NormalVertexLabel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // batch insert Vertex
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_endNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(g_endNum, successNum);

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)setVertexPropertyPK(g_stmt, g_endNum);
    (void)setVertexProperty(g_stmt, g_endNum);
    ret = GmcBatchAddDML(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 010. 多次在线修改配置项
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_010)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // create vertexlabel
    char Label_config[] = "{\"max_record_count\":100000}";
    readJanssonFile("schemafile/V5/NormalVertexLabel.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, Label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    AW_FUN_Log(LOG_DEBUG, "等待5s再执行\n");
    sleep(5);

    // insert
    for (int i = g_startNum; i < g_endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setVertexPropertyPK(g_stmt, i);
        setVertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
    }

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待30s后，在线修改配置项connectTimeout\n");
    sleep(30);

    // 将等待时间修改为2分钟-----此处修改失败，不允许在线修改
    system("gmadmin -cfgName connectTimeout -cfgVal 2");

    // 65s内无请求，此时再写数据，预期成功
    AW_FUN_Log(LOG_DEBUG, "再等待35s，再写一条数据\n");
    sleep(35);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    setVertexPropertyPK(g_stmt, g_endNum);
    setVertexProperty(g_stmt, g_endNum);
    ret = GmcExecute(g_stmt);
    // 链接长时间不操作，会超时，此处报错
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待125s后，构造长时间无请求场景\n");
    sleep(125);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)setVertexPropertyPK(g_stmt, g_endNum * 2);
    (void)setVertexProperty(g_stmt, g_endNum * 2);
    ret = GmcExecute(g_stmt);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 011. 将配置项值设置为：-1；0；100；1000000；10000000----不支持在线修改了
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_011)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;
    int modify_dvalue = -1;
    char cfg_name[50] = "connectTimeout";
    char g_command[1024];

    // 在线修改配置项connectTimeout值为：-1
    snprintf(g_command, 1024, "%s/gmadmin -cfgName %s -cfgVal %d -s %s\n", g_toolPath, cfg_name, modify_dvalue, g_connServer);
    ret = executeCommand(g_command, "execute gmadmin corresponding function, ret = 1004000!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 在线修改配置项connectTimeout值为：0
    modify_dvalue = 0;
    snprintf(g_command, 1024, "%s/gmadmin -cfgName %s -cfgVal %d -s %s\n", g_toolPath, cfg_name, modify_dvalue, g_connServer);
    ret = executeCommand(g_command, "execute gmadmin corresponding function, ret = 1004000!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 在线修改配置项connectTimeout值为：100
    modify_dvalue = 100;
    snprintf(g_command, 1024, "%s/gmadmin -cfgName %s -cfgVal %d -s %s\n", g_toolPath, cfg_name, modify_dvalue, g_connServer);
    ret = executeCommand(g_command, "execute gmadmin corresponding function, ret = 1004000!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 在线修改配置项connectTimeout值为：1000000
    modify_dvalue = 1000000;
    snprintf(g_command, 1024, "%s/gmadmin -cfgName %s -cfgVal %d -s %s\n", g_toolPath, cfg_name, modify_dvalue, g_connServer);
    ret = executeCommand(g_command, "execute gmadmin corresponding function, ret = 1004000!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 在线修改配置项connectTimeout值为：10000000
    modify_dvalue = 10000000;
    snprintf(g_command, 1024, "%s/gmadmin -cfgName %s -cfgVal %d -s %s\n", g_toolPath, cfg_name, modify_dvalue, g_connServer);
    ret = executeCommand(g_command, "execute gmadmin corresponding function, ret = 1004000!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 012. 创建kv表，批量写中途连接异常
TEST_F(ConnExcDetFunV5, Reliablity_003_FuncV5_012)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    char g_configJson[128] = "{\"max_record_count\":10000,\"writers\":\"abc\",\"max_record_count_check\":false}";
    int ret = 0;
    char cmd[512];
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    // create kvtable
    ret = GmcKvCreateTable(g_stmt, g_kvName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_DEBUG, "等待30s再执行\n");
    sleep(30);

    // 批量插入数据
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // KV元组结构体
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int i = g_startNum; i < g_endNum; i++) {
        sprintf(key_set, "liqi%d", i);
        int32_t value = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value, sizeof(int32_t));  // 将KV数据插入到stmt中
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(successNum, g_endNum);

    // 构造连接长时间无请求场景
    AW_FUN_Log(LOG_DEBUG, "等待65s，构造写异常场景\n");
    sleep(65);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // KV元组结构体
    GmcKvTupleT kvInfo1 = {0};
    for (int i = g_endNum; i < g_endNum * 2; i++) {
        sprintf(key_set, "liqi%d", i);
        int32_t value = i;
        kvInfo1.key = key_set;
        kvInfo1.keyLen = strlen(key_set);
        kvInfo1.value = &value;
        kvInfo1.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value, sizeof(int32_t));  // 将KV数据插入到stmt中
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    // euler再次写数据报16001错误码，设备和仿真环境写入数据成功
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
#endif

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;

    // 断连再重新建连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

