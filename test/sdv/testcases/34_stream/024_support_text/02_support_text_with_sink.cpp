/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-15 10:25:27
 * @FilePath: \GMDBV5\test\sdv\testcases\34_stream\024_support_text\02_support_text_with_sink.cpp
 * @Description: 
 * @LastEditors: tian<PERSON><PERSON> 
 * @LastEditTime: 2025-03-26 14:23:32
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"

#include "text_util.h"


class SupportTextWithSink : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *SupportTextWithSink::conn = NULL;
GmcStmtT *SupportTextWithSink::stmt = NULL;

void SupportTextWithSink::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportTextWithSink::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void SupportTextWithSink::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportTextWithSink::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建投影列包含text字段的、带with子句的、基于流表的sink结点 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM t1 "
            "where id < 155 INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '5');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 150;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过5s后再检查数据
    sleep(5);
    expectRowsCount = 155;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、不带with子句的、基于流表的sink结点 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM t1 "
            "INTO tsdb(ts1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 200;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 创建投影列包含text字段的、带with子句的、基于视图的sink结点 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where id < 155 INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '5');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 150;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过5s后再检查数据
    sleep(5);
    expectRowsCount = 155;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、不带with子句的、基于视图的sink结点 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "INTO tsdb(ts1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 200;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带where子句且对text字段做比较运算<的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where name < 'name_150' INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 50;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable5(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过一秒再检查时序表数据
    sleep(1);
    expectRowsCount = 59;
    expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable5(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带where子句且对text字段做比较运算>的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where name > 'name_150' INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 140;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable6(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过一秒再检查时序表数据
    sleep(1);
    expectRowsCount = 141;
    expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable6(stmt, selectTsName, expectRowsCount, expectColsCount);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带where子句且对text字段做比较运算<=的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where name <= 'name_150' INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 50;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable7(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过一秒再检查时序表数据
    sleep(1);
    expectRowsCount = 59;
    expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable7(stmt, selectTsName, expectRowsCount, expectColsCount);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带where子句且对text字段做比较运算>=的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where name >= 'name_150' INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 140;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable8(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过一秒再检查时序表数据
    sleep(1);
    expectRowsCount = 141;
    expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable8(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带where子句且对text字段做比较运算!=的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where name != 'name_150' INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 200;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable9(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过一秒再检查时序表数据
    sleep(1);
    expectRowsCount = 200;
    expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable9(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带where子句且对text字段做比较运算=的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where name = 'name_150' INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 0;
    uint32_t expectColsCount = 0;
    RdCheckDataInTSTableOfStreamTable10(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过一秒再检查时序表数据
    sleep(1);
    expectRowsCount = 0;
    expectColsCount = 0;
    RdCheckDataInTSTableOfStreamTable10(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带where子句且对text字段做逻辑运算符(and、or)的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where name != 'name_195' and name > 'name_150' INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 140;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable11(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 过一秒再检查时序表数据
    sleep(1);
    expectRowsCount = 141;
    expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable11(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带where子句且对text字段做模糊查询操作符(like)的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM v1 "
            "where name like 'name%' INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 200;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带包含text字段的format函数、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, "
            "format('My age is %d, name is %s', age, name) FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 200;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTableOfTest13(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带对text字段坐去重算子的、带with子句的、基于视图的sink结点，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50), distin integer) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address, "
            "seq_distinct_count(id, name, time) FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    uint16_t len = 120;
    // 插入200条一样的数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 1, len);

    // 再插入100条不一样的数据
    rowNum = 100;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 8, len);

    sleep(1);
    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1;
    uint32_t expectColsCount = 6;
    RdCheckDataInTSTableOfStreamTableOfTest14(stmt, selectTsName, expectRowsCount, expectColsCount);

    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、同时带对text字段坐去重算子|format函数的、带with子句的、基于视图的sink订阅，写入数据到流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50), distin integer) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, "
            "format('My age is %d, name is %s', age, name), "
            "seq_distinct_count(id, name, time) FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1', timeout = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    uint16_t len = 120;
    // 插入200条一样的数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 1, len);

    // 再插入100条不一样的数据
    rowNum = 100;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 8, len);

    sleep(1);
    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1;
    uint32_t expectColsCount = 6;
    RdCheckDataInTSTableOfStreamTableOfTest15(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 创建select投影重复text列的sink结点，写入数据带流表，并校验数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, name2 text, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, name, age, address FROM t1 "
            "where id < 150 INTO tsdb(ts1) with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    uint16_t len = 120;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 150;
    uint32_t expectColsCount = 6;
    RdCheckDataInTSTableOfStreamTableOfTest16(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建投影列包含text字段的、带over聚合的partition by 包含text字段的、基于视图的sink结点，写入数据到流表，并校验时序表数据 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50), row_num integer) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, address, ROW_NUMBER() over (PARTITION BY window_start, window_end, name) from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, address, row_number FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 18;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age, len);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 9;
    uint32_t expectColsCount = 6;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 21, 23, 25, 22, 23};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 20, 21, 23, 25, 22, 23};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 12, 11, 12, 11, 10};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 2, 2, 2};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                  expectId, expectTime, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对text字段使用sum聚合函数 预期：失败
TEST_F(SupportTextWithSink, STREAM_024_SINK_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    const char *create = "create stream view v1 as select id, name, time, age, address, sum(name) over (PARTITION BY window_start, window_end, name) from "
                         "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对text字段使用ref[][]引用 预期：失败
TEST_F(SupportTextWithSink, STREAM_024_SINK_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50), row_num integer) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict);"
        },
        {"create stream reference aref(integer, integer);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink sink1 as select age, name, REF['aref'][name], id "
                         "from t1 into tsdb (ts1) with (batch_window_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream reference aref;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 定义text字段，形式如text(20) 预期：失败
TEST_F(SupportTextWithSink, STREAM_024_SINK_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    const char *create = "create stream table t1 (id integer, name text(20), time integer, age integer, address char(50), "
                         "watermark for time as time - interval '10' seconds strict);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 大小写混合 预期：成功
TEST_F(SupportTextWithSink, STREAM_024_SINK_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    const char *create = "create stream table t1 (id integer, name TEXT, time integer, age integer, address char(50), "
                         "watermark for time as time - interval '10' seconds strict);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_OK, ret);

    create = "create stream table t2 (id integer, name Text, time integer, age integer, address char(50), "
                         "watermark for time as time - interval '10' seconds strict);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// text类型投影char类型字段 预期：失败
TEST_F(SupportTextWithSink, STREAM_024_SINK_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
           "create table ts2 (id integer, name char(50), time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
           "create table ts3 (id integer, name integer, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream table t2 (id integer, name char(50), time integer, age integer, address char(50));"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // text映射integer
    const char *create = "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM t1 "
                         "where id < 155 INTO tsdb(ts3) with "
                         "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '5');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // text映射char[]
    create = "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM t1 "
                        "where id < 155 INTO tsdb(ts2) with "
                        "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '5');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // char[]映射text
    create = "CREATE STREAM SINK s1 AS select id, name, time, age, address FROM t2 "
                         "where id < 155 INTO tsdb(ts1) with "
                         "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '5');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop table ts1;"},
        {"drop table ts2;"},
        {"drop table ts3;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证通配符_功能
TEST_F(SupportTextWithSink, STREAM_024_SINK_023)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like 'nan_ing';"},
        {"create stream view v1 as select * from viewFrom where name like 'zhan_san' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like 'zhan_san' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like 'nanch_ng' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证通配符%功能
TEST_F(SupportTextWithSink, STREAM_024_SINK_024)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like 'nan%';"},
        {"create stream view v1 as select * from viewFrom where name like 'zhan%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like 'zhan%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like 'z%mg' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证连续出现通配符_功能
TEST_F(SupportTextWithSink, STREAM_024_SINK_025)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like 'nan__ing';"},
        {"create stream view v1 as select * from viewFrom where name like 'zhan__san' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like 'zhan__san' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like 'nanch__ng' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证连续出现通配符%
TEST_F(SupportTextWithSink, STREAM_024_SINK_026)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like 'nan%%';"},
        {"create stream view v1 as select * from viewFrom where name like 'zhan%%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like 'zhan%%%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like 'z%%mg' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证字段中含有多个非连续通配符_功能
TEST_F(SupportTextWithSink, STREAM_024_SINK_027)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like 'nan_ab_ing';"},
        {"create stream view v1 as select * from viewFrom where name like 'zhan_cd_san' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like 'zhan_ed_san' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like 'nanch_uu_ng' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证字段中含有多个非连续通配符%功能
TEST_F(SupportTextWithSink, STREAM_024_SINK_028)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like 'nan%b%';"},
        {"create stream view v1 as select * from viewFrom where name like 'zhan%bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like 'zhan%bfg%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like 'z%bs%mg' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证字段中含有多个匹配规则
TEST_F(SupportTextWithSink, STREAM_024_SINK_029)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like 'nan%b_';"},
        {"create stream view v1 as select * from viewFrom where name like 'zhan_bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like 'zh_anbfg%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like 'z%bs%m_g' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证匹配字段以通配符_开始
TEST_F(SupportTextWithSink, STREAM_024_SINK_030)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like '\_nan%b_';"},
        {"create stream view v1 as select * from viewFrom where name like '\_zhan_bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like '\_zh_anbfg%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like '\_z%bsm_g' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 验证匹配字段以通配符%开始
TEST_F(SupportTextWithSink, STREAM_024_SINK_031)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like '\%nan%b_';"},
        {"create stream view v1 as select * from viewFrom where name like '\%zhan_bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name like '\%zh_anbfg' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address like '\%z%bsm_g' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 模糊匹配LIKE关键字大小写混合
TEST_F(SupportTextWithSink, STREAM_024_SINK_032)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address Like 'nan%b_';"},
        {"create stream view v1 as select * from viewFrom where name LIKE 'zhan_bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_OK},
        {"create stream sink s1 as select * from t1 where name lIKe 'zh_anbfg%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);"},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address LIKe 'z%bs%m_g' into tsdb (ts1);"},
        {"drop stream sink s2;"},
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 模糊匹配like关键字缺失
TEST_F(SupportTextWithSink, STREAM_024_SINK_033)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address 'nan%b_';", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v1 as select * from t1 where name 'zhan_bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s1 as select * from t1 where name 'zh_an%bfg%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and address 'z%bs%m_g' into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 模糊匹配like关键字不正确
TEST_F(SupportTextWithSink, STREAM_024_SINK_034)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address likee 'nan%b_';", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v1 as select * from viewFrom where name alike 'zhan_bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s1 as select * from t1 where name GLOB 'zh_an%bfg%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s2 as select * from v1 where name < 'zhangsan' or id > 23 and address quilc 'z%bs%m_g' into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 模糊匹配指定字段int类型
TEST_F(SupportTextWithSink, STREAM_024_SINK_035)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and id like 'nan%b_';", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream view v1 as select * from t1 where age like 'zhan_bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream sink s1 as select * from t1 where id like 'zh_an%bfg%' and address >= 'nanjing' or age <= 45 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and age like 'z%bs%m_g' into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// LIKE后使用空字符串进行匹配
TEST_F(SupportTextWithSink, STREAM_024_SINK_036)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like '';", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE},
        {"create stream view v1 as select * from t1 where name like '' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE},
        {"create stream sink s1 as select * from t1 where name like '' and address >= 'nanjing' or age <= 45 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and address like '' into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 指定列和匹配表达式位置交换
TEST_F(SupportTextWithSink, STREAM_024_SINK_037)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and 'nan%b_' like address;", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v1 as select * from t1 where 'zhan_bd%' like name and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s1 as select * from t1 where 'zh_an%bfg%' like name and address >= 'nanjing' or age <= 45 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and 'z%bs%m_g' like address into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// LIKE左值缺失
TEST_F(SupportTextWithSink, STREAM_024_SINK_038)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream view viewFrom as select * from t1 where name < 'zhangsan' or id > 23 and address like;", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v1 as select * from t1 where like 'zhan_bd%' and id > 23 or address < 'nanchang';", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s1 as select * from t1 where like 'asdd_' and address >= 'nanjing' or age <= 45 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// LIKE右值缺失
TEST_F(SupportTextWithSink, STREAM_024_SINK_039)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and address like into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and like into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// LIKE两边都是变量
TEST_F(SupportTextWithSink, STREAM_024_SINK_040)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address text) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address text);"},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and address like name into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// LIKE两边都是常量
TEST_F(SupportTextWithSink, STREAM_024_SINK_041)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address char(50));"},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and 'lisi' like 'zhangsan' into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and 12 like 13 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// LIKE右边的整型值
TEST_F(SupportTextWithSink, STREAM_024_SINK_042)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts1 (id integer, name text, age integer, address text) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table t1 (id integer, name text, age integer, address text);"},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and name like 14 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream sink s2 as select * from t1 where name < 'zhangsan' or id > 23 and address like 13 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}




