/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-22 09:32:49
 * @FilePath: \GMDBV5\test\sdv\gmdb_td\testcases\001_stream\015_support_printf_format\02_support_printf_format.cpp
 * @Description: 
 * @LastEditors: tian<PERSON><PERSON> 
 * @LastEditTime: 2024-11-15 11:42:00
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "stream_table_struct.h"



class PrintfFormat : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *PrintfFormat::conn = NULL;
GmcStmtT *PrintfFormat::stmt = NULL;

void PrintfFormat::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void PrintfFormat::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void PrintfFormat::SetUp()
{
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void PrintfFormat::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// format函数使用【.+数字】设置精度小于数据位数，如%.5d、%.5s 预期：成功
TEST_F(PrintfFormat, STREAM_015_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('My age is %.3d, name is %.8s', age, name) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 100000;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"My age is %.3d, name is %.8s", insertVal, "name_100000");
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用【.+数字】设置精度大于数据位数，如%.5d、%.5s 预期：成功
TEST_F(PrintfFormat, STREAM_015_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('My age is %.7d, name is %.13s', age, name) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 100000;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"My age is %.7d, name is %.13s", insertVal, "name_100000");
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用【.*】设置精度，如printf("%.*d\n", 5, 6); 预期：失败
TEST_F(PrintfFormat, STREAM_015_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %.*d', 5, age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数设置正数输出带进制符号%#x 预期：成功
TEST_F(PrintfFormat, STREAM_015_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('My age is %#x', age) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 100000;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"My age is %#x", insertVal);
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用【数字.+数字】设置精度小于数据位数，如%.5d、%.5s 预期：成功
TEST_F(PrintfFormat, STREAM_015_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('My age is %1.3d, name is %4.8s', age, name) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 100000;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"My age is %1.3d, name is %4.8s", insertVal, "name_100000");
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%f，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %f', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %f', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %f, name is %f', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%lf，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %lf', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %lf', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %lf, name is %lf', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%e，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %e', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %e', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %e, name is %e', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%E，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %E', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %E', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %E, name is %E', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%g，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %g', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %g', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %g, name is %g', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%G，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %G', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %G', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %G, name is %G', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%c，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %c', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %c', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %c, name is %c', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%p，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %p', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %p', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %p, name is %p', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%n，且后面参数数量相同，类型为integer/字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %n', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %n', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %n, name is %n', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%12 预期：成功
TEST_F(PrintfFormat, STREAM_015_033_1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('My age is %12') from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 100000;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"My age is %%12");
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%12,后面跟integer参数 预期：报错
TEST_F(PrintfFormat, STREAM_015_033_2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('My age is %12', id) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数参数为字面值，如printf("%d", 56) 预期：失败
TEST_F(PrintfFormat, STREAM_015_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d', 16) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %s', 'name') "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d, name is %s', 16, 'name') "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%d，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %d', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d, name is %d', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%hd，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %hd', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %hd', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %hd, name is %hd', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%ld，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %ld', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %ld', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %ld, name is %ld', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%lld，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %lld', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %lld', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %lld, name is %lld', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%i，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %i', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %i', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %i, name is %i', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%o，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %o', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %o', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %o, name is %o', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%u，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %u', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %u', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %u, name is %u', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}








