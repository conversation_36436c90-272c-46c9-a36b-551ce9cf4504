/*
 * @Author: t<PERSON><PERSON><PERSON>
 * @Date: 2025-02-24 10:16:53
 * @FilePath: \GMDBV5\test\sdv\testcases\34_stream\034_modify_redoFile_config\01_modify_redoFile_config.cpp
 * @Description: 
 * @LastEditors: tian<PERSON><PERSON> 
 * @LastEditTime: 2025-03-10 16:21:19
 */
 
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "t_rd_common.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"

#include "redoFile_config_util.h"


class ModifyRedoFileConfig : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *ModifyRedoFileConfig::conn = NULL;
GmcStmtT *ModifyRedoFileConfig::stmt = NULL;

void ModifyRedoFileConfig::SetUpTestCase(){}

void ModifyRedoFileConfig::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void ModifyRedoFileConfig::SetUp(){}

void ModifyRedoFileConfig::TearDown(){
    // 恢复配置项redoFileSize的值为默认值128
    int32_t ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"128");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 恢复配置项redoPubBufSize的值为默认值4096
    ret = RdGmserverModifyCfg((char *)"redoPubBufSize", (char *)"4096");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 恢复配置项redoFileCount的值为默认值32
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"32");
}

// 配置redoFileSize=1并保证redoFileSize ≥ 2 * pubBufSize +512B，建表并通过结构化插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");
    
    // 修改配置项redoFileSize的值
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoPubBufSize的值保证redoFileSize ≥ 2 * pubBufSize +512B
    ret = RdGmserverModifyCfg((char *)"redoPubBufSize", (char *)"256");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置配置项redoFileCount的值为默认值
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 1000;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除流表schema
    RdStreamFreeTableSchema(vertexLabel);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02 ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置redoFileSize=15并保证redoFileSize ≥ 2 * pubBufSize +512B，建表并通过结构化插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");

    // 修改配置项redoFileSize的值
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"15");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置配置项redoFileCount的值为默认值
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 1000;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除流表schema
    RdStreamFreeTableSchema(vertexLabel);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02 ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置redoFileCount=1，建表并通过结构化插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");

    // 设置配置项redoFileSize的值为默认值
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"128");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoFileCount的值为1
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 1000;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除流表schema
    RdStreamFreeTableSchema(vertexLabel);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02 ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置redoFileSize=1并保证redoFileSize ≥ 2 * pubBufSize +512B、redoFileCount=1，建表并通过结构化插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");

    // 修改配置项redoFileSize的值为1
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoPubBufSize的值保证redoFileSize ≥ 2 * pubBufSize +512B
    ret = RdGmserverModifyCfg((char *)"redoPubBufSize", (char *)"256");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoFileCount的值为1
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 1000;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum, len);

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 删除流表schema
    RdStreamFreeTableSchema(vertexLabel);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02 ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置redoFileSize=1并保证redoFileSize ≥ 2 * pubBufSize +512B，建表并通过异步写插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");

    // 修改配置项redoFileSize的值
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoPubBufSize的值保证redoFileSize ≥ 2 * pubBufSize +512B
    ret = RdGmserverModifyCfg((char *)"redoPubBufSize", (char *)"256");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置配置项redoFileCount的值为默认值
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name char(50), time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);


    // 异步写入数据
    const char *strTblName = "t1";
    uint32_t rowNum = 1000;
    RdAsyncWriteStreamTableByLoop(strTblName, rowNum);


    // 检查时序表数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableByAsyn(stmt, qryCmd, expectRowsCount, expectColsCount);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02_asyn ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置redoFileSize=15并保证redoFileSize ≥ 2 * pubBufSize +512B，建表并通过异步写插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");

    // 修改配置项redoFileSize的值
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"15");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置配置项redoFileCount的值为默认值
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name char(50), time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);


    // 异步写入数据
    const char *strTblName = "t1";
    uint32_t rowNum = 1000;
    RdAsyncWriteStreamTableByLoop(strTblName, rowNum);


    // 检查时序表数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableByAsyn(stmt, qryCmd, expectRowsCount, expectColsCount);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02_asyn ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置redoFileCount=1，建表并通过异步写插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");

    // 设置配置项redoFileSize的值为默认值
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"128");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoFileCount的值为1
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name char(50), time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);


    // 异步写入数据
    const char *strTblName = "t1";
    uint32_t rowNum = 1000;
    RdAsyncWriteStreamTableByLoop(strTblName, rowNum);


    // 检查时序表数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableByAsyn(stmt, qryCmd, expectRowsCount, expectColsCount);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02_asyn ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置redoFileSize=1并保证redoFileSize ≥ 2 * pubBufSize +512B、redoFileCount=1，建表并通过异步写插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");

    // 修改配置项redoFileSize的值为1
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoPubBufSize的值保证redoFileSize ≥ 2 * pubBufSize +512B
    ret = RdGmserverModifyCfg((char *)"redoPubBufSize", (char *)"256");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置配置项redoFileCount的值为默认值32
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name char(50), time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);


    // 异步写入数据
    const char *strTblName = "t1";
    uint32_t rowNum = 1000;
    RdAsyncWriteStreamTableByLoop(strTblName, rowNum);


    // 检查时序表数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableByAsyn(stmt, qryCmd, expectRowsCount, expectColsCount);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02_asyn ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置设置redoPubBufSize的值为511，修改redoFileSize的值为1，使得redoFileSize ≥ 2 * redoPubBufSize + 512B，建表并通过异步写插入数据，检查是否正常，重启客户端，检查时序表数据是否正确   预期：成功
TEST_F(ModifyRedoFileConfig, STREAM_034_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    system("sh $TEST_HOME/tools/stop.sh -f");

    // 修改配置项redoFileSize的值为1
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoPubBufSize的值为255
    ret = RdGmserverModifyCfg((char *)"redoPubBufSize", (char *)"511");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置配置项redoFileCount的值为默认值32
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
        "create table ts1 (id integer, name char(50), time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);


    // 异步写入数据
    const char *strTblName = "t1";
    uint32_t rowNum = 1000;
    RdAsyncWriteStreamTableByLoop(strTblName, rowNum);


    // 检查时序表数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableByAsyn(stmt, qryCmd, expectRowsCount, expectColsCount);

    // 关闭客户端连接
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭服务
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);

    // 重启一个客户端，检查时序表数据
    int status = system("./client_02_asyn ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 配置设置redoPubBufSize的值为512，修改redoFileSize的值为1，使得redoFileSize < 2 * redoPubBufSize + 512B   预期：报错
TEST_F(ModifyRedoFileConfig, STREAM_034_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 关闭服务
    (void)system("sh $TEST_HOME/tools/stop.sh -f");

    // 修改配置项redoFileSize的值为1
    ret = RdGmserverModifyCfg((char *)"redoFileSize", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改配置项redoPubBufSize的值为256
    ret = RdGmserverModifyCfg((char *)"redoPubBufSize", (char *)"512");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置配置项redoFileCount的值为默认值32
    ret = RdGmserverModifyCfg((char *)"redoFileCount", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdGmserverInitCfg();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启服务
    (void)system("${TEST_HOME}/tools/start.sh");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建客户端连接
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}






