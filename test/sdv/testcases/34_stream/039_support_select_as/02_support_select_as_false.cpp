/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-03-24 10:54:24
 * @FilePath: \GMDBV5\test\sdv\testcases\34_stream\039_support_select_as\02_support_select_as_false.cpp
 * @Description: 
 * @LastEditors: t<PERSON><PERSON><PERSON> 
 * @LastEditTime: 2025-03-26 14:41:48
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"

#include "as_util.h"


class SupportSelectAs : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *SupportSelectAs::conn = NULL;
GmcStmtT *SupportSelectAs::stmt = NULL;

void SupportSelectAs::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportSelectAs::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void SupportSelectAs::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportSelectAs::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// as重命名的名字，数字开头，包含字母大小写和_ 预期：报错
TEST_F(SupportSelectAs, STREAM_039_FALSE_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream reference aref(integer, integer) with (mini = 'true');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 134 as 12_idCol, name, time, age, address from v1"
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select 134 as 12_idCol, name, time, age, address from "
                            "v1 into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// as重命名的名字，数字开头，只包含数字  预期：报错
TEST_F(SupportSelectAs, STREAM_039_FALSE_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream reference aref(integer, integer) with (mini = 'true');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 134 as 12345, name, time, age, address from v1"
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select 134 as 12345, name, time, age, address from "
                            "v1 into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// as重命名的名字，包含特殊符号 预期：报错
TEST_F(SupportSelectAs, STREAM_039_FALSE_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream reference aref(integer, integer) with (mini = 'true');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 134 as 12_idCol@, name, time, age, address from v1"
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select 134 as 12_idCol!, name, time, age, address from "
                            "v1 into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}


