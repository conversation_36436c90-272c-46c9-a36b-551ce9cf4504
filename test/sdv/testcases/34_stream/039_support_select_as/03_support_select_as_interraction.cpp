/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-03-24 14:08:22
 * @FilePath: \GMDBV5\test\sdv\testcases\34_stream\039_support_select_as\03_support_select_as_interraction.cpp
 * @Description: 
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-24
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"

#include "as_util.h"


class SupportSelectAs : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *SupportSelectAs::conn = NULL;
GmcStmtT *SupportSelectAs::stmt = NULL;

void SupportSelectAs::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportSelectAs::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void SupportSelectAs::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportSelectAs::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建view，select常量使用as重命名，同一个view中ref引用重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 12 as idCol, name, time, ref['aref'][idCol], address from v1 "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, ref['aref'][idCol], address from "
                            "v1 into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继结点中ref引用重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view if not exists v1 as select 12 as idCol, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select idCol, name, time, ref['aref'][idCol], address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中where条件左值为引用重命名字段ref函数 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 1);"
        },
        {
            "create stream view if not exists v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 12 as idCol, name, time, age, address from v1 "
                            "where ref['aref'][idCol] > 0 with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, age, address from "
                            "v1 where ref['aref'][idCol] > 0 into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继结点中where条件左值为引用重命名字段ref函数 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view if not exists v1 as select id, 12 as idCol, name, time, age, address from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select id, name, time, age, address FROM v1 "
            "where ref['aref'][idcol] = 0 INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectId[i], val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAge[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中去重算子参数包含重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 12 as idCol, name, time, seq_distinct_count(idCol, name), address from v1 "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, seq_distinct_count(idCol, name), address from "
                            "v1 into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继节点中去重算子参数包含重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select 12 as idCol, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select idCol, name, time, seq_distinct_count(idCol, name), address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 1, 1}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 4;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 2, 3, 4};
    int64_t expectTime[expectRowsCount] = {1, 20, 24, 35};
    int64_t expectDistin[expectRowsCount] = {7, 4, 4, 2};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectDistin[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中where条件左值为引用重命名字段distinct函数 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 12 as idCol, name, time, age, address from v1 "
                            "where seq_distinct_count(time, name) > 2 with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, age, address from "
                            "v1 where seq_distinct_count(idCol, name) > 2 into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继结点中where条件左值为引用重命名字段distinct函数 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select id, 12 as idCol, name, time, age, address from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "CREATE STREAM SINK s1 AS select idCol, name, time, age, address FROM v1 "
                            "where seq_distinct_count(idCol, name) > 2 INTO tsdb(ts1) with "
                            "(batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中format函数参数包含重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 12 as idCol, name, time, age, "
                            "format('id is %d', idCol) from v1 "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, age, "
                      "format('id is %d', idCol) from v1 into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继结点中format函数参数包含重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select id, 12 as idCol, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select id, name, time, age, format('id is %d', idCol) FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectId[i], val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAge[i], val);

        // check format
        ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char val[size] = {0};
        char expectVal[size] = {0};
        (void)sprintf(expectVal, "id is %d", 12);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectVal, val));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中sum/max/min/count/avg聚合函数参数包含重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select id, 12 as idCol, name, time, "
                            "avg(idCol) over (PARTITION BY window_start, window_end, age), address from "
                            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, "
                            "avg(idCol) over (PARTITION BY window_start, window_end, age), address from "
                            "table(hop(table v1, time, interval '20' seconds, interval '10' seconds)) "
                            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继节点中sum/max/min/count/avg聚合函数参数包含重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, 12 as idCol, name, time, age, address from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 as select idCol, name, time, avg(idCol) over (PARTITION BY window_start, window_end, age), address "
            "from table(hop(table v1, time, interval '20' seconds, interval '10' seconds)) "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    
    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中over聚合参数包含重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select id, 12 as idCol, name, time, "
                            "avg(id) over (PARTITION BY window_start, window_end, idCol), address from "
                            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, "
                            "avg(id) over (PARTITION BY window_start, window_end, idCol), address from "
                            "table(hop(table v1, time, interval '20' seconds, interval '10' seconds)) "
                            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继节点中over聚合参数包含重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, 12 as idCol, name, time, age, address from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 as select idCol, name, time, avg(id) over (PARTITION BY window_start, window_end, idCol), address "
            "from table(hop(table v1, time, interval '20' seconds, interval '10' seconds)) "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 7, 20, 21, 24, 25, 22, 24};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 7, 20, 21, 24, 25, 22, 24};
    int64_t expectAvg[expectRowsCount] = {4, 4, 4, 4, 22, 22, 22, 22, 22, 22};
    
    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAvg[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中group by聚合参数包含重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select id, 12 as idCol, name, time, "
                            "avg(id), address from "
                            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
                            "group by window_start, window_end, idCol "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, "
                            "avg(id), address from "
                            "table(hop(table v1, time, interval '20' seconds, interval '10' seconds)) "
                            "group by window_start, window_end, idCol "
                            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继节点中group by聚合参数包含重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, 12 as idCol, name, time, age, address from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 as select idCol, name, time, avg(id), address "
            "from table(hop(table v1, time, interval '20' seconds, interval '10' seconds)) "
            "group by window_start, window_end, idCol "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 20};
    int64_t expectTime[expectRowsCount] = {1, 20};
    int64_t expectAvg[expectRowsCount] = {4, 22};
    
    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAvg[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建窗口view，select常量使用as重命名，后继结点投影重命名的字段，且为订阅 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, 12 as idCol, name, time, age, address from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 as select idCol, name, time, avg(id), address "
            "from table(hop(table v1, time, interval '20' seconds, interval '10' seconds)) "
            "group by window_start, window_end, idCol "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        RdExpectData &received = *reinterpret_cast<RdExpectData *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        uint64_t val = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(12, val);
            
            // check time
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectTime[received.cnt], val);
            
            // check avg
            double dval;
            ret = GmcGetVertexPropertySizeById(stmt, 3, &size);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &dval, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(received.expectAvg[received.cnt], dval);
            
            received.cnt++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    RdExpectData expectData = {
        0,
        {1, 20},
        {1, 20},
        {4, 68.0/3},
    };
    
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &expectData);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 检查收到的数据量
    uint32_t expectRowsCount = 2;
    RdTwentyOneWaitSnRecv(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建窗口view，select常量使用as重命名，后继结点投影重命名的字段，且为socket 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select 12 as idCol, name, time, avg(id), address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "group by window_start, window_end, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select first_idCol, first_name, first_time, avg_id, first_address FROM v1 "
            "INTO SERVER_SOCKET('/tmp/stream_unix_sock1.sock') with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 连接server socket
    const char *socketAddr = "/tmp/stream_unix_sock1.sock";
    int32_t socketFd = ConnectSinkSocket(socketAddr);
    sleep(2);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 27, 30, 8, 35, 22, 23, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 27, 30, 8, 35, 22, 23, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 检查数据
    uint32_t expectRowsCount = 6;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 23, 21};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 20, 23, 21};
    double  expectAvg[expectRowsCount] = {4, 3, 5, 70.0/3, 22.5, 23};
    char buf[65535] = {0};
    read(socketFd, buf, 65535);
    
    char expectBuf[7000] = {0};
    for (int i = 0; i < expectRowsCount; i++){
        char sub[100] = {0};

        if(i % 1 == 0){
            (void)sprintf(sub, "12,name%d,%d,%lf,address%d", expectId[i], expectTime[i], expectAvg[i], expectId[i]);
        }else {
            (void)sprintf(sub, "12,name%d,%d,%lf,address%d\n", expectId[i], expectTime[i], expectAvg[i], expectId[i]);
        }
        strcat(expectBuf, sub);
    }
    
    EXPECT_EQ(0, strcmp(buf, expectBuf));

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    // 关闭client socket连接
    close(socketFd);

    (void)system("rm -rf /tmp/stream_unix_sock1.sock");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，且包含where子句 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select 12 as idCol, name, time, age, address from t1 where id < 20 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select idCol, name, time, ref['aref'][idCol], address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 7, 7, 8};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 7, 7, 8};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中包含where条件：比较运算符左值为view重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 12 as idCol, name, time, ref['aref'][idCol], address from v1 "
                            " where idCol < 20 and idCol >= 10 with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, ref['aref'][idCol], address from "
                            "v1 where idCol < 20 or idCol >= 10 into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继结点包含where条件：比较运算符左值为view重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select 12 as idCol, name, time, age, address from t1 where id < 20 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select idCol, name, time, ref['aref'][idCol], address FROM v1 "
            "where idCol = 12 INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 7, 7, 8};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 7, 7, 8};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中包含where条件：in/not in运算符左值为view重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select 12 as idCol, name, time, ref['aref'][idCol], address from v1 "
                            " where idCol in (10, 20) with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "create stream sink s1 as select 12 as idCol, name, time, ref['aref'][idCol], address from "
                            "v1 where idCol in (10, 20) into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继结点包含where条件：in/not in运算符左值为view重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select 12 as idCol, name, time, age, address from t1 where id < 20 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select idCol, name, time, ref['aref'][idCol], address FROM v1 "
            "where idCol in (12, 13, 14) INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 7, 7, 8};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 7, 7, 8};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个view中diapatch by参数包含重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "dispatch by age;"
        },
        {
            "create stream view v1 as select 123 as idCol, name, time, age, address from table(dispatch(table t1, 10)) "
            "dispatch by idCol "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select idCol, name, time, age, address FROM table(dispatch(table v1, 123)) "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 40}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 40};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 10, 20, 7, 30, 8, 24, 40};
    int64_t expectTime[expectRowsCount] = {1, 10, 20, 7, 30, 8, 24, 40};
    int64_t expectAge[expectRowsCount] = {10, 10, 10, 10, 10, 10, 10, 10};
    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(123, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继结点中diapatch by参数包含重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "dispatch by age;"
        },
        {
            "create stream view v1 as select id, 123 as idCol, name, time, age, address from table(dispatch(table t1, 10)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select idCol, name, time, age, address from v1 "
            "dispatch by idCol "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select idCol, name, time, age, address FROM table(dispatch(table v2, 123)) "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 40}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 40};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 10, 20, 7, 30, 8, 24, 40};
    int64_t expectTime[expectRowsCount] = {1, 10, 20, 7, 30, 8, 24, 40};
    int64_t expectAge[expectRowsCount] = {10, 10, 10, 10, 10, 10, 10, 10};
    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(123, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，同一个结点包含where条件，alter where条件包含重命名字段 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select 12 as idCol, name, time, age, address from t1 where id < 20 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select 13 as constCol, name, time, ref['aref'][idCol], address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "alter stream view v1 alter where as idCol < 5;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    create = (char *) "alter stream view v1 alter where as constCol < 5;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建view，select常量使用as重命名，后继结点包含where条件，alter where条件包含重命名字段 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream reference if not exists aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (12, 0);"
        },
        {
            "create stream view v1 as select 12 as idCol, name, time, age, address from t1 where id < 20 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select idCol, name, time, ref['aref'][idCol], address FROM v1 "
            "where time > 10 INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
        {
            "alter stream sink s1 alter where as idCol = 12;"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 7, 7, 8};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 7, 7, 8};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 两个表schema不同，但因as导致schema一致，进行union 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50), weight integer) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select 12 as id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select *, 13 as weight FROM TABLE(UNION(TABLE v1, TABLE v2)) "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id = 12;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, id, len);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 38;
    uint32_t expectColsCount = 6;

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(id, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", id);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(id, val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(id, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", id);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(13, val);
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 两个表schema相同，但因as导致schema不一致，进行union 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select 12 as idCol, id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "CREATE STREAM SINK s1 AS select * FROM TABLE(UNION(TABLE v1, TABLE v2)) "
                            "INTO tsdb(ts1) with "
                            "(batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 两个表schema不相同，但因as导致schema也不一致，进行union 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select 12 as idCol, 13 as id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "CREATE STREAM SINK s1 AS select 12 as id, * FROM TABLE(UNION(TABLE v1, TABLE v2)) "
                            "INTO tsdb(ts1) with "
                            "(batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union一个因as导致schema与原表一致的结点 预期：成功
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50), weight integer) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select 12 as id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select *, 13 as weight FROM TABLE(UNION(TABLE v1, TABLE v2)) "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
        {
            "alter stream sink s1 alter from_union add t1;"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id = 12;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, id, len);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 57;
    uint32_t expectColsCount = 6;

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(id, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", id);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(id, val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(id, val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", id);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(13, val);
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union一个因as导致schema与原表不一致的结点 预期：报错
TEST_F(SupportSelectAs, STREAM_039_INTERACTION_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select 12 as id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v3 as select 12 as idCol, 13 as id, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK if not exists s1 AS select * FROM TABLE(UNION(TABLE v1, TABLE v2)) "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "alter stream sink s1 alter from_union add v3;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}




