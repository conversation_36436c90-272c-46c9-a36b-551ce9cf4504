/*
 * @Author: t<PERSON><PERSON><PERSON>
 * @Date: 2024-11-15 10:25:27
 * @FilePath: \GMDBV5\test\sdv\gmdb_td\testcases\001_stream\021_distribute_data_by_subscribtion\01_distribute_data_by_subscribtion.cpp
 * @Description: 
 * @LastEditors: tianyihui 
 * @LastEditTime: 2024-11-27 15:46:46
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"
#include "sub_util.h"


class DistributeDataBySub : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *DistributeDataBySub::conn = NULL;
GmcStmtT *DistributeDataBySub::stmt = NULL;

void DistributeDataBySub::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DistributeDataBySub::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void DistributeDataBySub::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DistributeDataBySub::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建select只包含普通投影列的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 "
            "where id < 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    uint32_t expectRowsCount = 150;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select投影全部列的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select * FROM v1 "
            "where id < 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    uint32_t expectRowsCount = 150;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select包含普通投影列和去重算子的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, seq_distinct_count(id, name, time) FROM v1 "
            "where id < 150 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, time);

            // check distinct
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(150, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入150条完全相同的数据
    uint32_t rowNum = 150;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 1);
    // 再写入1条不同的数据
    uint32_t rowNum01 = 1;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum01, 8);

    uint32_t expectRowsCount = 1;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select包含普通投影列和format函数的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS SELECT id, name, age, format('My age is %d, name is %s', age, name) FROM v1 "
            "where id < 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, time);

            // check format
            ret = GmcGetVertexPropertySizeById(stmt, 3, &size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            char val[size] = {0};
            char expectVal[size] = {0};
            (void)sprintf(expectVal,"My age is %d, name is %s", id, name);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectVal, val));

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    uint32_t expectRowsCount = 150;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select包含普通投影列和REF函数的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream reference aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (1, 0);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS SELECT id, name, age, REF['aref'][age] FROM v1 "
            "where id < 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, time);

            // check ref
            size = 8;
            int64_t val = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, val);

            received++;

        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 150;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 1);

    
    uint32_t expectRowsCount = 150;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select同时包含普通投影列|format函数|REF函数的|去重算子的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream reference aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (1, 0);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS SELECT id, name, age, "
            "REF['aref'][age], format('My age is %d, name is %s', age, name), "
            "seq_distinct_count(id, name) FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, time);

            // check ref
            size = 8;
            int64_t refVal = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &refVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, refVal);

            // check format
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            char val[size] = {0};
            char expectVal[size] = {0};
            (void)sprintf(expectVal,"My age is %d, name is %s", id, name);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectVal, val));

            // check distin
            size = 8;
            int64_t distinVal = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &distinVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(150, distinVal);

            received++;

        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入150条完全相同的数据
    uint32_t rowNum = 150;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 1);
    // 再写入1条不同的数据
    uint32_t rowNum01 = 1;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum01, 8);

    
    uint32_t expectRowsCount = 1;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select同时包含普通投影列|format函数|去重算子的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_006_1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream reference aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (1, 0);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS SELECT id, name, age, "
            "format('My age is %d, name is %s', age, name), "
            "seq_distinct_count(id, name) FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, time);

            // check format
            ret = GmcGetVertexPropertySizeById(stmt, 3, &size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            char val[size] = {0};
            char expectVal[size] = {0};
            (void)sprintf(expectVal,"My age is %d, name is %s", id, name);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectVal, val));

            // check distin
            size = 8;
            int64_t distinVal = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &distinVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(150, distinVal);

            received++;

        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入150条完全相同的数据
    uint32_t rowNum = 150;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 1);
    // 再写入1条不同的数据
    uint32_t rowNum01 = 1;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum01, 8);

    
    uint32_t expectRowsCount = 1;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select同时包含普通投影列|REF函数的|去重算子的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_006_2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream reference aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (1, 0);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS SELECT id, name, age, "
            "REF['aref'][age], "
            "seq_distinct_count(id, name) FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, time);

            // check ref
            size = 8;
            int64_t refVal = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &refVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, refVal);

            // check distin
            size = 8;
            int64_t distinVal = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &distinVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(150, distinVal);

            received++;

        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入150条完全相同的数据
    uint32_t rowNum = 150;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 1);

    // 再写入1条不同的数据
    uint32_t rowNum01 = 1;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum01, 8);

    
    uint32_t expectRowsCount = 1;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select同时包含普通投影列|format函数|REF函数的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_006_3)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream reference aref(integer, integer);"
        },
        {
            "upsert into streamref aref values (1, 0);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS SELECT id, name, age, "
            "REF['aref'][age], format('My age is %d, name is %s', age, name) FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(1, time);

            // check ref
            size = 8;
            int64_t refVal = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &refVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, refVal);

            // check format
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            char val[size] = {0};
            char expectVal[size] = {0};
            (void)sprintf(expectVal,"My age is %d, name is %s", id, name);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectVal, val));

            received++;

        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入150条完全相同的数据
    uint32_t rowNum = 150;
    RdStructWriteStreamTableWithSameId(stmt, vertexLabel, rowNum, 1);

    
    uint32_t expectRowsCount = 150;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select只包含普通投影列的、where子句为比较运算符(>)的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 "
            "where id > 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(151 + received, id);


            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 151 + received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(151 + received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(151 + received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 201;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 50;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select只包含普通投影列的、where子句为比较运算符(>=)的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 "
            "where id >= 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(150 + received, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 150 + received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(150 + received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(150 + received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 50;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select只包含普通投影列的、where子句为比较运算符(<=)的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 "
            "where id <= 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, id);
            
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 201;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 150;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select只包含普通投影列的、where子句为比较运算符(!=)的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 "
            "where id != 199 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, id);
            
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 190;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select只包含普通投影列的、where子句为逻辑运算符(and)的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 "
            "where id > 120 and age < 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(121 + received, id);
            
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 121 + received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(121 + received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(121 + received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 20;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select只包含普通投影列的、where子句为逻辑运算符(or)的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 "
            "where id > 120 or age < 150 INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, id);
            
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 200;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select只包含普通投影列的、where子句为模糊查询操作符(like)的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 "
            "where name like 'name%' INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, id);
            
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 200;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select包含普通投影列和row_number()聚合函数对应列的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
       {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, row_number FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        RdExpectData &received = *reinterpret_cast<RdExpectData *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        uint64_t val = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectId[received.cnt], val);

            // check name
            size = 50;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld",
                received.expectId[received.cnt] + 0);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            
            // check time
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectTime[received.cnt], val);

            // check age
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectAge[received.cnt], val);
            
            // check row_line
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectOverVal[received.cnt], val);

            received.cnt++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    RdExpectData expectData = {
        0,
        {1, 3, 5, 20, 21, 23, 25, 22, 23},
        {1, 3, 5, 20, 21, 23, 25, 22, 23},
        {10, 11, 12, 10, 12, 11, 12, 11, 10},
        {1, 1, 1, 1, 1, 1, 2, 2, 2},
    };
    
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &expectData);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 18;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 9;
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建select包含普通投影列和max聚合函数对应列的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
       {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, max(id) over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, max_id FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        RdExpectData &received = *reinterpret_cast<RdExpectData *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        uint64_t val = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectId[received.cnt], val);

            // check name
            size = 50;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld",
                received.expectId[received.cnt] + 0);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            
            // check time
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectTime[received.cnt], val);
            
            // check age
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectAge[received.cnt], val);
            
            // check max
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectOverVal[received.cnt], val);
            
            received.cnt++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    RdExpectData expectData = {
        0,
        {5, 3, 0, 9, 1, 16, 13, 11, 10, 15},
        {5, 3, 0, 9, 1, 16, 13, 11, 10, 15},
        {11, 10, 14, 14, 13, 10, 13, 14, 12, 12},
        {5, 3, 9, 9, 1, 16, 13, 11, 15, 15},
    };
    
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &expectData);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 检查收到的数据量
    uint32_t expectRowsCount = 10;
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 创建select包含普通投影列和min聚合函数对应列的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
       {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, min(id) over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, min_id FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        RdExpectData &received = *reinterpret_cast<RdExpectData *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        uint64_t val = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectId[received.cnt], val);

            // check name
            size = 50;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld",
                received.expectId[received.cnt] + 0);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            
            // check time
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectTime[received.cnt], val);
            
            // check age
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectAge[received.cnt], val);
            
            // check min
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectOverVal[received.cnt], val);
            
            received.cnt++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    RdExpectData expectData = {
        0,
        {5, 3, 0, 9, 1, 16, 13, 11, 10, 15},
        {5, 3, 0, 9, 1, 16, 13, 11, 10, 15},
        {11, 10, 14, 14, 13, 10, 13, 14, 12, 12},
        {5, 3, 0, 0, 1, 16, 13, 11, 10, 10},
    };
    
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &expectData);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 10;
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 创建select包含普通投影列和sum聚合函数对应列的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
       {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, sum(id) over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, sum_id FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        RdExpectData &received = *reinterpret_cast<RdExpectData *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        uint64_t val = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectId[received.cnt], val);

            // check name
            size = 50;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld",
                received.expectId[received.cnt] + 0);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            
            // check time
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectTime[received.cnt], val);
            
            // check age
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectAge[received.cnt], val);
            
            // check max
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectOverVal[received.cnt], val);
            
            received.cnt++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    RdExpectData expectData = {
        0,
        {5, 3, 0, 9, 1, 16, 13, 11, 10, 15},
        {5, 3, 0, 9, 1, 16, 13, 11, 10, 15},
        {11, 10, 14, 14, 13, 10, 13, 14, 12, 12},
        {5, 3, 9, 9, 1, 16, 13, 11, 25, 25},
    };
    
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &expectData);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 检查收到的数据量
    uint32_t expectRowsCount = 10;
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 创建select包含普通投影列和count聚合函数对应列的、包含where子句的订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
       {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, count(id) over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, count_id FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        RdExpectData &received = *reinterpret_cast<RdExpectData *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        uint64_t val = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectId[received.cnt], val);

            // check name
            size = 50;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld",
                received.expectId[received.cnt] + 0);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            
            // check time
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectTime[received.cnt], val);
            
            // check age
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectAge[received.cnt], val);
            
            // check max
            size = 8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received.expectOverVal[received.cnt], val);
            
            received.cnt++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    RdExpectData expectData = {
        0,
        {5, 3, 0, 9, 1, 16, 13, 11, 10, 15},
        {5, 3, 0, 9, 1, 16, 13, 11, 10, 15},
        {11, 10, 14, 14, 13, 10, 13, 14, 12, 12},
        {1, 1, 2, 2, 1, 1, 1, 1, 2, 2},
    };
    
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &expectData);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 检查收到的数据量
    
    uint32_t expectRowsCount = 10;
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);
    RdTwentyOneWaitSnRecv1(&expectData, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, expectData.cnt);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 创建带timeout配置项的sink订阅 预期：成功
TEST_F(DistributeDataBySub, STREAM_021_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select * FROM v1 "
            "INTO pubsub_channel with "
            "(batch_window_size = '10', tuple_buffer_size = '1', timeout = '5');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, id);

            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, time);

            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received, age);

            received++;
        }    
    };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 219;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);

    // 直接检查数据量
    uint32_t expectRowsCount = 210;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 过5s再检查数据量
    sleep(5);
    expectRowsCount = 219;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    rowNum = 200;
    RdStructWriteStreamTableByLoop(stmt, vertexLabel, rowNum);


    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

